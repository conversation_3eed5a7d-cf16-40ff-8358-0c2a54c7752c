var a0_0x4a7465=a0_0x1d15;(function(_0x145fd3,_0x1ca2bc){var _0x57f56e=a0_0x1d15,_0x5448d1=_0x145fd3();while(!![]){try{var _0x3435fa=-parseInt(_0x57f56e(0xf9))/0x1+-parseInt(_0x57f56e(0xe5))/0x2+parseInt(_0x57f56e(0x130))/0x3+-parseInt(_0x57f56e(0x10c))/0x4*(parseInt(_0x57f56e(0x100))/0x5)+-parseInt(_0x57f56e(0xec))/0x6*(parseInt(_0x57f56e(0x139))/0x7)+parseInt(_0x57f56e(0x124))/0x8+-parseInt(_0x57f56e(0xeb))/0x9*(-parseInt(_0x57f56e(0x12b))/0xa);if(_0x3435fa===_0x1ca2bc)break;else _0x5448d1['push'](_0x5448d1['shift']());}catch(_0x52f4bd){_0x5448d1['push'](_0x5448d1['shift']());}}}(a0_0x4c0f,0xd0746));async function create_elegant_dual_image(_0x2a6b17,_0xd96a8b,_0x54f747,_0x26aa58,_0x5341af,_0x13c83f,_0x31925e,_0x50f6c5){var _0x1f2079=await urlToImage(_0x2a6b17);_0x1f2079=await resizeImage(_0x1f2079,_0x5341af,_0x13c83f);var _0x2bfde6=await urlToImage(_0x54f747);_0x2bfde6=await resizeImage(_0x2bfde6,_0x13c83f*0.45,_0x5341af*0.45),_0x1f2079=await addLogo(_0x1f2079,_0x2bfde6,0.75);var _0x295d70=await urlToImage(_0xd96a8b);return _0x295d70=await resizeImage(_0x295d70,_0x13c83f*0.45,_0x5341af*0.45),_0x1f2079=await addSideImage(_0x1f2079,_0x295d70,0.95),_0x1f2079;}async function create_single_image(_0x58e9cb,_0x15df14,_0x5bbace,_0x5c2c4d,_0x381ed5,_0x59849b,_0x2805e1){var _0xf2408d=a0_0x1d15,_0x2f9652=await urlToImage(_0x58e9cb);_0x2f9652=await resizeToImperfectSquareImage(_0x2f9652,_0x5c2c4d,_0x381ed5);var _0x479e7d=await urlToImage(_0x15df14);return _0x479e7d=await resizeToImperfectSquareImage(_0x479e7d,_0x381ed5*0.45,_0x5c2c4d*0.45),_0x2f9652=await makeImageWider(_0x2f9652,_0x479e7d,_0xf2408d(0xd6)),_0x479e7d=await resizeToImperfectSquareImage(_0x479e7d,_0x381ed5*0.4,_0x5c2c4d*0.4),_0x2f9652=await addWaterMarkImageToTopRight(_0x2f9652,_0x479e7d,0.7,_0x15df14),_0x2f9652;}async function createSingleImageWithWaterMark(_0x1ad2a8){var _0x214d25=a0_0x1d15,_0x567626=_0x1ad2a8['imageSource'],_0x40b6bb=_0x1ad2a8[_0x214d25(0x132)],_0x307710=await urlToImage(_0x567626);_0x307710=await removeWhiteSpaceFromImage(_0x307710),_0x307710=await makeImageTransparent(_0x307710);var _0x5b1677=await urlToImage(_0x40b6bb);_0x5b1677=await resizeToImperfectSquareImage(_0x5b1677,_0x307710[_0x214d25(0x116)]*0.2,_0x307710[_0x214d25(0x122)]*0.2),_0x5b1677=await removeWhiteSpaceFromImage(_0x5b1677),_0x5b1677=await makeImageTransparent(_0x5b1677),_0x307710=await makeImageWider(_0x307710,_0x5b1677,'right'),_0x307710=await addWaterMarkImageToTopRight(_0x307710,_0x5b1677,0.7);if(_0x307710[_0x214d25(0x122)]>_0x307710[_0x214d25(0x116)])_0x307710=await addPaddingToImage({'imageObject':_0x307710,'padding':_0x307710['width']-_0x307710[_0x214d25(0x116)],'paddingDirection':_0x214d25(0x116)});else{if(_0x307710[_0x214d25(0x122)]<_0x307710[_0x214d25(0x116)])_0x307710=await addPaddingToImage({'imageObject':_0x307710,'padding':_0x307710['height']-_0x307710[_0x214d25(0x122)],'paddingDirection':_0x214d25(0x122)});else _0x307710['width']==_0x307710[_0x214d25(0x116)]&&(_0x307710=await addPaddingToImage({'imageObject':_0x307710,'padding':_0x307710['height']-_0x307710['width'],'paddingDirection':_0x214d25(0x122)}));}return _0x307710;}async function createSingleImageWithWaterMarkV3(_0x3557e3,_0x134878=a0_0x4a7465(0x127),_0x55263f=a0_0x4a7465(0xfd)){var _0x13360f=a0_0x4a7465,_0x53e211=_0x3557e3['imageSource'],_0x177637=_0x3557e3[_0x13360f(0x132)],_0x2cbe68=await urlToImage(_0x53e211);_0x2cbe68=await removeWhiteSpaceFromImage(_0x2cbe68),_0x2cbe68=await makeImageTransparent(_0x2cbe68);var _0x202557=await urlToImage(_0x177637);_0x202557=await resizeToImperfectSquareImage(_0x202557,_0x2cbe68[_0x13360f(0x116)]*0.2,_0x2cbe68[_0x13360f(0x122)]*0.2),_0x202557=await removeWhiteSpaceFromImage(_0x202557),_0x202557=await makeImageTransparent(_0x202557);var _0x4ff942=getProductDescriptionAndFeatures(),_0x3cefd2=getFilteredTitle(),_0x2a47cd=_0x3cefd2+'\x0a\x0a'+_0x4ff942,_0x4a8a4b=await postToServer(_0x13360f(0xe3),{'request_type':'get_buyer_intent','productDescription':_0x2a47cd});console[_0x13360f(0xd1)]('twoWordBuyerIntent',_0x4a8a4b);var _0x443790=_0x4a8a4b[_0x13360f(0x121)]('\x20'),_0x134878=_0x443790[0x0],_0x55263f;for(var _0x4a684c=0x1;_0x4a684c<_0x443790[_0x13360f(0x10a)];_0x4a684c++){_0x55263f+='\x20'+_0x443790[_0x4a684c];}return _0x55263f=_0x55263f[_0x13360f(0xcf)](),_0x134878=_0x134878[_0x13360f(0xcf)](),_0x2cbe68=await addTextToImageFunction2(_0x2cbe68,_0x134878,_0x55263f),_0x2cbe68=await makeImageWider(_0x2cbe68,_0x202557,_0x13360f(0xd6)),_0x2cbe68=await makeImageAspectSame(_0x2cbe68),_0x2cbe68=await addWaterMarkImageToTopRight(_0x2cbe68,_0x202557,0.7),_0x2cbe68;}async function createSingleImageWithWaterMarkV2(_0x496bc1){var _0x53afd2=a0_0x4a7465,_0xbcd43e=_0x496bc1[_0x53afd2(0xd9)],_0x460691=_0x496bc1[_0x53afd2(0x132)],_0x529c5a=await urlToImage(_0xbcd43e);_0x529c5a=await removeWhiteSpaceFromImage(_0x529c5a),_0x529c5a=await makeImageTransparent(_0x529c5a);var _0x28073d=await urlToImage(_0x460691);return _0x28073d=await resizeToImperfectSquareImage(_0x28073d,_0x529c5a[_0x53afd2(0x116)]*0.2,_0x529c5a[_0x53afd2(0x122)]*0.2),_0x28073d=await removeWhiteSpaceFromImage(_0x28073d),_0x28073d=await makeImageTransparent(_0x28073d),_0x529c5a=await makeImageWider(_0x529c5a,_0x28073d,_0x53afd2(0xd6)),_0x529c5a=await makeImageAspectSame(_0x529c5a),_0x529c5a=await addWaterMarkImageToTopRight(_0x529c5a,_0x28073d,0.7),_0x529c5a=await upscaleToMinimumSize(_0x529c5a,0x1f4,0x1f4),_0x529c5a;}function removeImageBlanks(_0x3e8d66){var _0x5d4462=a0_0x4a7465;imgWidth=_0x3e8d66[_0x5d4462(0x122)],imgHeight=_0x3e8d66[_0x5d4462(0x116)];var _0x136d39=document['createElement'](_0x5d4462(0x106));_0x136d39[_0x5d4462(0x12d)](_0x5d4462(0x122),imgWidth),_0x136d39[_0x5d4462(0x12d)]('height',imgHeight);var _0x4c3cc3=_0x136d39[_0x5d4462(0x103)]('2d');_0x4c3cc3['drawImage'](_0x3e8d66,0x0,0x0);var _0x2281d2=_0x4c3cc3['getImageData'](0x0,0x0,imgWidth,imgHeight),_0x1945d9=_0x2281d2['data'],_0x5f48d8=function(_0x24207d,_0x69bc32){var _0x107ab8=imgWidth*_0x69bc32+_0x24207d;return{'red':_0x1945d9[_0x107ab8*0x4],'green':_0x1945d9[_0x107ab8*0x4+0x1],'blue':_0x1945d9[_0x107ab8*0x4+0x2],'opacity':_0x1945d9[_0x107ab8*0x4+0x3]};},_0x3fd5e1=function(_0x83103e){var _0x530e16=_0x5d4462;return _0x83103e[_0x530e16(0x10e)]>0xc8&&_0x83103e[_0x530e16(0xda)]>0xc8&&_0x83103e[_0x530e16(0xf6)]>0xc8;},_0x2e2f75=function(_0xf6b458){var _0x323506=_0x5d4462,_0x22a632=_0xf6b458?0x1:-0x1;for(var _0x3ff7e5=_0xf6b458?0x0:imgHeight-0x1;_0xf6b458?_0x3ff7e5<imgHeight:_0x3ff7e5>-0x1;_0x3ff7e5+=_0x22a632){for(var _0x53e4fb=0x0;_0x53e4fb<imgWidth;_0x53e4fb++){var _0x114ab0=_0x5f48d8(_0x53e4fb,_0x3ff7e5);if(!_0x3fd5e1(_0x114ab0))return _0xf6b458?_0x3ff7e5:Math[_0x323506(0xf0)](_0x3ff7e5+0x1,imgHeight);}}return null;},_0x2a8e6f=function(_0x2fee1c){var _0x1f11bb=_0x5d4462,_0x1200fe=_0x2fee1c?0x1:-0x1;for(var _0x2972a7=_0x2fee1c?0x0:imgWidth-0x1;_0x2fee1c?_0x2972a7<imgWidth:_0x2972a7>-0x1;_0x2972a7+=_0x1200fe){for(var _0x5799b5=0x0;_0x5799b5<imgHeight;_0x5799b5++){var _0x40abdc=_0x5f48d8(_0x2972a7,_0x5799b5);if(!_0x3fd5e1(_0x40abdc))return _0x2fee1c?_0x2972a7:Math[_0x1f11bb(0xf0)](_0x2972a7+0x1,imgWidth);}}return null;},_0x3a0234=_0x2e2f75(!![]),_0x52b0ac=_0x2e2f75(![]),_0x50717c=_0x2a8e6f(!![]),_0xc6c642=_0x2a8e6f(![]),_0x39f14f=_0xc6c642-_0x50717c,_0x5794aa=_0x52b0ac-_0x3a0234;return _0x136d39[_0x5d4462(0x12d)](_0x5d4462(0x122),_0x39f14f),_0x136d39[_0x5d4462(0x12d)](_0x5d4462(0x116),_0x5794aa),_0x136d39[_0x5d4462(0x103)]('2d')[_0x5d4462(0xd4)](_0x3e8d66,_0x50717c,_0x3a0234,_0x39f14f,_0x5794aa,0x0,0x0,_0x39f14f,_0x5794aa),_0x136d39[_0x5d4462(0xf1)]();}async function createDualImageForImageWithLongerWidth(_0x351256){var _0x4d1045=a0_0x4a7465,_0x511619=_0x351256[_0x4d1045(0xd9)],_0x1a06ac=await urlToImage(_0x511619);_0x1a06ac=await addPaddingToImage({'imageObject':_0x1a06ac,'padding':_0x1a06ac[_0x4d1045(0x122)]-_0x1a06ac[_0x4d1045(0x116)],'paddingDirection':_0x4d1045(0x116)}),_0x1a06ac=await addBorderToImage(_0x1a06ac,'black',_0x4d1045(0xd5)),document[_0x4d1045(0x117)]['prepend'](_0x1a06ac);}function addPaddingToImage(_0x339ced){return new Promise((_0x497203,_0x36ca23)=>{var _0x548598=a0_0x1d15,_0x4f6483=_0x339ced[_0x548598(0xcd)],_0x33d153=_0x339ced[_0x548598(0xf2)],_0x164d46=_0x339ced[_0x548598(0x109)]||_0x548598(0x102),_0x564346=document[_0x548598(0xef)]('canvas');_0x564346['id']=_0x548598(0x11a);var _0x310e1b=_0x564346[_0x548598(0x103)]('2d');_0x164d46==_0x548598(0x102)&&(_0x564346['width']=_0x4f6483[_0x548598(0x122)]+_0x33d153,_0x564346[_0x548598(0x116)]=_0x4f6483[_0x548598(0x116)]+_0x33d153,_0x310e1b[_0x548598(0x120)]=_0x548598(0xf3),_0x310e1b[_0x548598(0xe8)](0x0,0x0,_0x564346['width'],_0x564346['height']),_0x310e1b[_0x548598(0xd4)](_0x4f6483,_0x33d153/0x2,_0x33d153/0x2));_0x164d46==_0x548598(0x116)&&(_0x564346[_0x548598(0x122)]=_0x4f6483[_0x548598(0x122)],_0x564346['height']=_0x4f6483[_0x548598(0x116)]+_0x33d153,_0x310e1b[_0x548598(0x120)]=_0x548598(0xf3),_0x310e1b[_0x548598(0xe8)](0x0,0x0,_0x564346['width'],_0x564346[_0x548598(0x116)]),_0x310e1b[_0x548598(0xd4)](_0x4f6483,0x0,_0x33d153/0x2));_0x164d46==_0x548598(0x122)&&(_0x564346[_0x548598(0x122)]=_0x4f6483[_0x548598(0x122)]+_0x33d153,_0x564346['height']=_0x4f6483['height'],_0x310e1b['fillStyle']=_0x548598(0xf3),_0x310e1b[_0x548598(0xe8)](0x0,0x0,_0x564346[_0x548598(0x122)],_0x564346[_0x548598(0x116)]),_0x310e1b[_0x548598(0xd4)](_0x4f6483,_0x33d153/0x2,0x0));var _0x88e3db=new Image();_0x88e3db[_0x548598(0x12a)]=function(){_0x497203(_0x88e3db);},_0x88e3db[_0x548598(0xfe)]=_0x564346['toDataURL']();});}async function creating_dual_image(_0x4fd904,_0x31e796,_0x2b0ae9,_0x18dd81,_0x534f8c,_0x685bd3,_0x43650b,_0x5e75a5){var _0x58131b=a0_0x4a7465,_0x411aa9=await urlToImage(_0x4fd904);_0x411aa9=await resizeToImperfectSquareImage(_0x411aa9,_0x534f8c,_0x685bd3),_0x411aa9=await removeWhiteSpaceFromImage(_0x411aa9);var _0x3bca8c=await urlToImage(_0x2b0ae9);_0x3bca8c=await removeWhiteSpaceFromImage(_0x3bca8c),_0x3bca8c=await resizeToImperfectSquareImage(_0x3bca8c,_0x685bd3*0.45,_0x534f8c*0.45);var _0x1f46a6=await urlToImage(_0x31e796);_0x1f46a6=await resizeToImperfectSquareImage(_0x1f46a6,_0x685bd3*0.45,_0x534f8c*0.45);var _0x373edc=0xc;return _0x411aa9=await makeImageWider(_0x411aa9,_0x1f46a6,_0x58131b(0xd6)),_0x1f46a6=await resizeToImperfectSquareImage(_0x1f46a6,_0x534f8c*0.4,_0x685bd3*0.4),_0x411aa9=await addBottomRightImage(_0x411aa9,_0x1f46a6,0x1,_0x373edc),_0x3bca8c=await resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x3bca8c,_0x1f46a6[_0x58131b(0x122)]*0.65,_0x1f46a6[_0x58131b(0x116)]*0.65),_0x3bca8c=await makeImageTransparent(_0x3bca8c),_0x411aa9=await addWaterMarkImageToTopRight(_0x411aa9,_0x3bca8c,0.7),_0x411aa9=await makeImageAspectSame(_0x411aa9),_0x411aa9=await upscaleToMinimumSize(_0x411aa9,0x1f4,0x1f4),_0x411aa9;}async function creating_dual_image_V2(_0x2a9a94,_0x2661d4,_0xbab3ca,_0x2d562c,_0x1d84ae,_0xb2fe2,_0x41c0a3,_0x42efb9){var _0x2c8c48=a0_0x4a7465,_0x4a2d43=await urlToImage(_0x2a9a94);_0x4a2d43=await resizeToImperfectSquareImage(_0x4a2d43,_0x1d84ae,_0xb2fe2),_0x4a2d43=await removeWhiteSpaceFromImage(_0x4a2d43);var _0x871a33=await urlToImage(_0xbab3ca);_0x871a33=await resizeToImperfectSquareImage(_0x871a33,_0xb2fe2*0.45,_0x1d84ae*0.45),waterMarkImg=await removeWhiteSpaceFromImage(_0x871a33),waterMarkImg=await makeImageTransparent(_0x871a33);var _0x114da2=await urlToImage(_0x2661d4);_0x114da2=await resizeToImperfectSquareImage(_0x114da2,_0xb2fe2*0.45,_0x1d84ae*0.45);var _0x350011=0xc;return _0x4a2d43=await makeImageWider(_0x4a2d43,_0x114da2,_0x2c8c48(0xd6)),_0x871a33=await resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x871a33,_0x114da2[_0x2c8c48(0x122)]*0.65,_0x114da2['height']*0.65),_0x871a33=await makeImageTransparent(_0x871a33),_0x4a2d43=await addWaterMarkImageToTopRight(_0x4a2d43,_0x871a33,0.7),_0x4a2d43=await makeImageAspectSame(_0x4a2d43),_0x114da2=await resizeToImperfectSquareImage(_0x114da2,_0x1d84ae*0.4,_0xb2fe2*0.4),_0x4a2d43=await addBottomRightImage(_0x4a2d43,_0x114da2,0x1,_0x350011),_0x4a2d43=await upscaleToMinimumSize(_0x4a2d43,0x1f4,0x1f4),_0x4a2d43;}async function creating_dual_image_V3(_0xe18e4b,_0x725233,_0x2ce488,_0x70006e,_0x2d53b3,_0x3ad873,_0x337aec,_0xecea30){var _0xe24127=a0_0x4a7465,_0x2fa75b=await urlToImage(_0xe18e4b);_0x2fa75b=await resizeToImperfectSquareImage(_0x2fa75b,_0x2d53b3,_0x3ad873),_0x2fa75b=await removeWhiteSpaceFromImage(_0x2fa75b);var _0x551632=await urlToImage(_0x2ce488);_0x551632=await resizeToImperfectSquareImage(_0x551632,_0x3ad873*0.45,_0x2d53b3*0.45),waterMarkImg=await removeWhiteSpaceFromImage(_0x551632),waterMarkImg=await makeImageTransparent(_0x551632);var _0x174fc6=await urlToImage(_0x725233);_0x174fc6=await resizeToImperfectSquareImage(_0x174fc6,_0x3ad873*0.45,_0x2d53b3*0.45);var _0x14b4dd=0xc;return _0x2fa75b=await makeImageWider(_0x2fa75b,_0x174fc6,'right'),_0x2fa75b=await makeImageAspectSame(_0x2fa75b),_0x551632=await resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x551632,_0x174fc6[_0xe24127(0x122)]*0.65,_0x174fc6[_0xe24127(0x116)]*0.65),_0x551632=await makeImageTransparent(_0x551632),_0x2fa75b=await addWaterMarkImageToTopRight(_0x2fa75b,_0x551632,0.7),_0x174fc6=await resizeToImperfectSquareImage(_0x174fc6,_0x2d53b3*0.4,_0x3ad873*0.4),_0x2fa75b=await addBottomRightImage(_0x2fa75b,_0x174fc6,0x1,_0x14b4dd),_0x2fa75b=await addBadgeToTopLeftOfImage(_0x2fa75b),_0x2fa75b=await addBadgeToBottomRightOfImage(_0x2fa75b),_0x2fa75b=await upscaleToMinimumSize(_0x2fa75b,0x1f4,0x1f4),_0x2fa75b;}function addBackgroundColorToImage(_0xe2beb8,_0x4060a4,_0x2e416b){return new Promise(async(_0xfedf93,_0x57f683)=>{var _0x201cd6=a0_0x1d15;let _0x10856b=document[_0x201cd6(0xef)]('canvas');_0x10856b[_0x201cd6(0x122)]=_0xe2beb8['width'],_0x10856b[_0x201cd6(0x116)]=_0xe2beb8['height'];let _0x4c313e=_0x10856b['getContext']('2d');_0x4c313e[_0x201cd6(0x120)]=_0x2e416b,_0x4c313e[_0x201cd6(0xe8)](0x0,0x0,_0x10856b[_0x201cd6(0x122)],_0x10856b[_0x201cd6(0x116)]),_0x4c313e[_0x201cd6(0xd4)](_0xe2beb8,0x0,0x0,_0xe2beb8['width'],_0xe2beb8[_0x201cd6(0x116)]);var _0x3d4dab=new Image();_0x3d4dab[_0x201cd6(0x12a)]=function(){_0xfedf93(_0x3d4dab);},_0x3d4dab[_0x201cd6(0xfe)]=_0x10856b['toDataURL']();});}async function creating_dual_image_with_transparent_boxes(_0x41c341,_0x2e168e,_0x5f4db0,_0x4c4f2f,_0x458c30,_0x24cea3,_0x105556,_0x29d97e){var _0x549abb=a0_0x4a7465,_0x4d1442=await urlToImage(_0x41c341);_0x4d1442=await resizeToImperfectSquareImage(_0x4d1442,_0x458c30,_0x24cea3),_0x4d1442=await removeWhiteSpaceFromImage(_0x4d1442);var _0x43db2c=await urlToImage(_0x5f4db0);_0x43db2c=await resizeToImperfectSquareImage(_0x43db2c,_0x24cea3*0.45,_0x458c30*0.45);var _0x547bff=await urlToImage(_0x2e168e);_0x547bff=await resizeToImperfectSquareImage(_0x547bff,_0x24cea3*0.45,_0x458c30*0.45);var _0x547bff=await makeImageTransparent(_0x547bff);_0x547bff=await addBackgroundColorToImage(_0x547bff,_0x2e168e,_0x549abb(0x110));var _0x172d5e=0xc;return _0x4d1442=await makeImageWider(_0x4d1442,_0x547bff,'right'),_0x547bff=await resizeToImperfectSquareImage(_0x547bff,_0x458c30*0.4,_0x24cea3*0.4),_0x4d1442=await addBottomRightImage(_0x4d1442,_0x547bff,0x1,_0x172d5e),_0x43db2c=await removeWhiteSpaceFromImage(_0x43db2c),_0x43db2c=await resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x43db2c,_0x547bff['width'],_0x547bff[_0x549abb(0x116)]),_0x43db2c=await makeImageTransparent(_0x43db2c),_0x4d1442=await addWaterMarkImageToTopRight(_0x4d1442,_0x43db2c,0.7,_0x5f4db0),_0x4d1442=await makeImageAspectSame(_0x4d1442),_0x4d1442=await addBadgeToTopLeftOfImage(_0x4d1442),_0x4d1442=await addBadgeToBottomRightOfImage(_0x4d1442),_0x4d1442=await upscaleToMinimumSize(_0x4d1442,0x1f4,0x1f4),_0x4d1442;}async function create_multi_image(_0x1fc12f,_0x5c548d,_0x5c1319,_0x35da4a,_0xe944c5,_0x40e516,_0x303560,_0x165580,_0x5695d2,_0x120b52){var _0x4ec7e5=a0_0x4a7465,_0x8e840d=await urlToImage(_0x1fc12f);_0x8e840d=await resizeImage(_0x8e840d,_0x303560,_0x165580);var _0x3e7fb9=await urlToImage(_0x5c1319);_0x3e7fb9=await resizeToImperfectSquareImage(_0x3e7fb9,_0x165580*0.45,_0x303560*0.45);var _0x5e2f92=await urlToImage(_0x5c548d);_0x5e2f92=await resizeToImperfectSquareImage(_0x5e2f92,_0x165580*0.45,_0x303560*0.45);var _0x554ff5=0xc;_0x8e840d=await makeImageWider(_0x8e840d,_0x5e2f92,_0x4ec7e5(0xd6)),_0x5e2f92=await resizeToImperfectSquareImage(_0x5e2f92,_0x165580*0.4,_0x303560*0.4),_0x3e7fb9=await resizeToImperfectSquareImage(_0x3e7fb9,_0x165580*0.4,_0x303560*0.4),_0x8e840d=await addBottomRightImage(_0x8e840d,_0x5e2f92,0x1,_0x554ff5),_0x3e7fb9=await makeImageTransparent(_0x3e7fb9),_0x8e840d=await addWaterMarkImageToTopRight(_0x8e840d,_0x3e7fb9,0.7);var _0x2b1fbd=await urlToImage(_0x35da4a);_0x2b1fbd=await addBoxToImage(_0x2b1fbd),_0x2b1fbd=await resizeToImperfectSquareImage(_0x2b1fbd,_0x165580*0.45,_0x303560*0.45);var _0x19e11d=await urlToImage(_0xe944c5);_0x19e11d=await addBoxToImage(_0x19e11d),_0x19e11d=await resizeToImperfectSquareImage(_0x19e11d,_0x165580*0.45,_0x303560*0.45);if(_0x19e11d[_0x4ec7e5(0x122)]>=_0x2b1fbd[_0x4ec7e5(0x122)])_0x8e840d=await makeImageWider(_0x8e840d,_0x19e11d,_0x4ec7e5(0x137)),_0x19e11d=await resizeToImperfectSquareImage(_0x19e11d,_0x165580*0.4,_0x303560*0.4),_0x2b1fbd=await resizeToImperfectSquareImage(_0x2b1fbd,_0x165580*0.4,_0x303560*0.4),_0x8e840d=await addBottomLeftImage(_0x8e840d,_0x19e11d,0x1,_0x554ff5),_0x8e840d=await addTopLeftImage(_0x8e840d,_0x2b1fbd,0x1);else _0x2b1fbd[_0x4ec7e5(0x122)]>_0x19e11d[_0x4ec7e5(0x122)]&&(_0x8e840d=await makeImageWider(_0x8e840d,_0x2b1fbd,_0x4ec7e5(0x137)),_0x19e11d=await resizeToImperfectSquareImage(_0x19e11d,_0x165580*0.4,_0x303560*0.4),_0x2b1fbd=await resizeToImperfectSquareImage(_0x2b1fbd,_0x165580*0.4,_0x303560*0.4),_0x8e840d=await addTopLeftImage(_0x8e840d,_0x2b1fbd,0x1),_0x8e840d=await addBottomLeftImage(_0x8e840d,_0x19e11d,0x1,_0x554ff5));return _0x8e840d=await upscaleToMinimumSize(_0x8e840d,0x1f4,0x1f4),_0x8e840d;}function a0_0x4c0f(){var _0x5ef61f=['getContext','html','getImageData','canvas','every','#86B817','paddingDirection','length','closePath','51444YhYZvg','appendChild','red','getElementsByTagName','beige','clip','save','black','beginPath','globalAlpha','height','body','#0074E8','px\x20','myCanvas','max','measureText','getURL','#cccccc','bold\x2024px\x20Arial','fillStyle','split','width','#E53238','7150248JtXMOD','sin','getItem','Look','Hersteller:','cos','onload','16841090LIbZdO','data','setAttribute','scale','quadraticCurveTo','1690878PwtidA','moveTo','waterMarkUrl','putImageData','crossOrigin','strokeRect','#F79148','left','#34B8FF','14csORcd','Look\x20','style','imageObject','translate','trim','strokeStyle','log','Arial','image/png','drawImage','30px','right','font','Image\x20is\x20already\x20a\x20square','imageSource','green','rotate','anonymous','/Image_Badges/Best_Seller.png','runtime','#ffffff','Inside\x20⤸','#000','Making\x20image\x20aspect\x20same','http://********:1102/api/ItemSpecifics/GetBuyerIntent','img','2865750lLTwpR','Done\x20making\x20image\x20aspect\x20same','border:2px\x20solid\x20black;','fillRect','restore','push','18JJUpgZ','3284472DBaeGR','stroke','fillText','createElement','min','toDataURL','padding','white','botttom','lineWidth','blue','EU\x20Verantwortliche\u00a0Person:','https://centreforinquiry.ca/wp-content/uploads/2020/05/68353859-canadian-map-with-canada-flag.jpg','1161154JAUjUq','lineTo','top','#F5AF02','Inside','src','bold\x20','110EUaoZi','bottom','all'];a0_0x4c0f=function(){return _0x5ef61f;};return a0_0x4c0f();}function addBorderToImage(_0x39be0c,_0xd4fea6,_0x22448e){return new Promise((_0x170388,_0x305456)=>{var _0x54b7eb=a0_0x1d15,_0x51f79d=document[_0x54b7eb(0xef)]('canvas');_0x51f79d[_0x54b7eb(0x122)]=_0x39be0c[_0x54b7eb(0x122)],_0x51f79d[_0x54b7eb(0x116)]=_0x39be0c[_0x54b7eb(0x116)];var _0x4146be=_0x51f79d['getContext']('2d');_0x4146be[_0x54b7eb(0xd4)](_0x39be0c,0x0,0x0),_0x4146be['strokeStyle']=_0xd4fea6,_0x4146be[_0x54b7eb(0xf5)]=_0x22448e,_0x4146be[_0x54b7eb(0x135)](0x0,0x0,_0x51f79d[_0x54b7eb(0x122)],_0x51f79d[_0x54b7eb(0x116)]);var _0x421b31=new Image();_0x421b31[_0x54b7eb(0x12a)]=function(){_0x170388(_0x421b31);},_0x421b31[_0x54b7eb(0xfe)]=_0x51f79d[_0x54b7eb(0xf1)]();});}async function createMultiImage(_0x48dc60,_0x3f88c8,_0xcd85ce,_0x356d1e,_0xcb0e01,_0x3217aa,_0xd4ac3a){var _0x4f10d3=a0_0x4a7465,_0x12d9ab=_0x4f10d3(0xf8),_0x55ed12=await urlToImage(_0x48dc60);_0x55ed12=await resizeImage(_0x55ed12,_0x356d1e,_0xcb0e01);var _0x4d7909=await urlToImage(_0x12d9ab);_0x4d7909=await resizeImage(_0x4d7909,_0xcb0e01*0.45,_0x356d1e*0.45),_0x55ed12=await addLogo(_0x55ed12,_0x4d7909,0.75);var _0x1728b3=await urlToImage(_0x3f88c8);return _0x1728b3=await addBoxToImage(_0x1728b3),_0x1728b3=await flipImage(_0x1728b3),_0x1728b3=await rotateImage(_0x1728b3,0xa),_0x1728b3=await resizeImage(_0x1728b3,_0xcb0e01*0.45,_0x356d1e*0.45),_0x55ed12=await addSideImage(_0x55ed12,_0x1728b3,0.95),_0x55ed12=await addTextToImage(_0x55ed12,_0xcd85ce,_0x3217aa,_0xd4ac3a),_0x55ed12=await addBoxToImage(_0x55ed12),_0x55ed12;}function addBoxToImage(_0x17151c){return new Promise(function(_0x47b846,_0x50a4cd){var _0x30accd=a0_0x1d15;let _0x2740a0=document[_0x30accd(0xef)](_0x30accd(0x106));_0x2740a0[_0x30accd(0x122)]=_0x17151c['width'],_0x2740a0[_0x30accd(0x116)]=_0x17151c['height'];let _0x49bcaa=_0x2740a0[_0x30accd(0x103)]('2d');_0x49bcaa['fillStyle']=_0x30accd(0xf3),_0x49bcaa[_0x30accd(0xe8)](0x0,0x0,_0x2740a0[_0x30accd(0x122)],_0x2740a0[_0x30accd(0x116)]),_0x49bcaa[_0x30accd(0xd4)](_0x17151c,0x0,0x0,_0x2740a0[_0x30accd(0x122)],_0x2740a0[_0x30accd(0x116)]),_0x49bcaa[_0x30accd(0xd0)]=_0x30accd(0x113),_0x49bcaa[_0x30accd(0xf5)]=0x1e,_0x49bcaa[_0x30accd(0x135)](0x0,0x0,_0x2740a0[_0x30accd(0x122)],_0x2740a0[_0x30accd(0x116)]);var _0x355a96=new Image();_0x355a96[_0x30accd(0x12a)]=function(){_0x47b846(_0x355a96);},_0x355a96[_0x30accd(0xfe)]=_0x2740a0[_0x30accd(0xf1)]();});}function addSideImage(_0xc40276,_0x56b2ab,_0x3a8c09){return new Promise(function(_0xff3f1e,_0x5baed3){var _0x2ee113=a0_0x1d15;let _0x2c7c58=document[_0x2ee113(0xef)](_0x2ee113(0x106));_0x2c7c58[_0x2ee113(0x122)]=_0xc40276[_0x2ee113(0x122)],_0x2c7c58[_0x2ee113(0x116)]=_0xc40276['height'];let _0x4cefb0=_0x2c7c58[_0x2ee113(0x103)]('2d');_0x4cefb0['fillStyle']=_0x2ee113(0xf3),_0x4cefb0['fillRect'](0x0,0x0,_0x2c7c58['width'],_0x2c7c58['height']),_0x4cefb0[_0x2ee113(0xd4)](_0xc40276,0x0,0x0),_0x4cefb0[_0x2ee113(0x115)]=_0x3a8c09;var _0x2f104c=_0x2c7c58['width']-_0x56b2ab[_0x2ee113(0x122)]-_0x2c7c58[_0x2ee113(0x122)]/0x32,_0x21d5d6=_0x2c7c58[_0x2ee113(0x116)]-_0x56b2ab['height']-_0x2c7c58[_0x2ee113(0x116)]/0xc;_0x4cefb0[_0x2ee113(0xd4)](_0x56b2ab,_0x2f104c,_0x21d5d6);var _0x298b01=new Image();_0x298b01['onload']=function(){_0xff3f1e(_0x298b01);},_0x298b01[_0x2ee113(0xfe)]=_0x2c7c58[_0x2ee113(0xf1)]();});}async function uploadEditedImage(_0x5d693c,_0x354cef,_0x289212,_0x26b0a3,_0x35e6d7){var _0x48160d=a0_0x4a7465,_0x223035=localStorage[_0x48160d(0x126)](_0x48160d(0x132)),_0x3e2b9a=await urlToImage(_0x5d693c);_0x3e2b9a=await resizeImage(_0x3e2b9a,_0x26b0a3,_0x35e6d7);var _0x4b0c5d=await urlToImage(_0x223035);_0x4b0c5d=await resizeImage(_0x4b0c5d,_0x35e6d7*0.4,_0x26b0a3*0.4),_0x3e2b9a=await addLogo(_0x3e2b9a,_0x4b0c5d,0.75),_0x3e2b9a=await addTextToImage(_0x3e2b9a,_0x354cef),upload(_0x3e2b9a[_0x48160d(0xfe)],_0x354cef,_0x289212);}async function uploadNormalImage(_0x2e6029,_0x3979d6,_0x1cbc8f,_0x37db71,_0x57c4ea){var _0x4739f1=a0_0x4a7465,_0x2273c1=localStorage[_0x4739f1(0x126)](_0x4739f1(0x132)),_0xe67f6b=await urlToImage(_0x2e6029);_0xe67f6b=await rotateImage(_0xe67f6b,0x0),_0xe67f6b=await resizeImage(_0xe67f6b,_0x37db71,_0x57c4ea),upload(_0xe67f6b[_0x4739f1(0xfe)],_0x3979d6,_0x1cbc8f);}function addImgToDom(_0x39f61b){var _0x59952b=a0_0x4a7465,_0x1a9440=document[_0x59952b(0xef)](_0x59952b(0xe4));_0x1a9440[_0x59952b(0xfe)]=_0x39f61b;var _0x40e6a6=document[_0x59952b(0x10f)](_0x59952b(0x104))[0x0];_0x40e6a6[_0x59952b(0x10d)](_0x1a9440);}function base64ToImage(_0x1c9639){return new Promise(function(_0xe20f2f,_0x317f76){var _0x2dbb52=a0_0x1d15,_0x55efcf=new Image();_0x55efcf[_0x2dbb52(0xfe)]=_0x1c9639,_0x55efcf[_0x2dbb52(0x134)]=_0x2dbb52(0xdc),_0x55efcf[_0x2dbb52(0x12a)]=function(){_0xe20f2f(_0x55efcf);};});}async function addTextToImage(_0x2fb6bf,_0x1e1719,_0xeae183,_0x14b57c){var _0x4a960b=await getTotalLines(_0x2fb6bf,_0x1e1719,_0xeae183,_0x14b57c),_0x2fb6bf=await addTextToImageFunction(_0x2fb6bf,_0x1e1719,_0x4a960b,_0xeae183,_0x14b57c);return _0x2fb6bf;}function addTextToImageFunction(_0x4a8477,_0x4b9bd7,_0x3616f1,_0x15eb90,_0x2a8a6d){return new Promise(function(_0x40a38b,_0x63f300){var _0xe4b281=a0_0x1d15,_0x3b2e99=document[_0xe4b281(0xef)](_0xe4b281(0x106));_0x3b2e99['id']=_0xe4b281(0x11a);var _0x349f94=_0x4a8477['height']/0xa*0.45,_0x3bb608=_0x349f94+_0x349f94/0x2,_0x464165=0x0,_0x4b1b89=_0x349f94;_0x3b2e99[_0xe4b281(0x122)]=_0x4a8477[_0xe4b281(0x122)],_0x3b2e99[_0xe4b281(0x116)]=_0x4a8477[_0xe4b281(0x116)]+_0x3bb608*_0x3616f1;var _0x2730cd=_0x3b2e99[_0xe4b281(0x103)]('2d');_0x2730cd['fillStyle']=_0xe4b281(0xf3),_0x2730cd[_0xe4b281(0xe8)](0x0,0x0,_0x3b2e99[_0xe4b281(0x122)],_0x3b2e99[_0xe4b281(0x116)]);var _0x126fb6=_0x3b2e99[_0xe4b281(0x122)],_0x45f983=_0x3b2e99[_0xe4b281(0x116)]*(0x46/0x5dc);_0x2730cd[_0xe4b281(0x120)]=_0x15eb90,_0x2730cd[_0xe4b281(0xd7)]=_0x349f94+'px\x20'+_0x2a8a6d,wrapText(_0x2730cd,_0x4b9bd7,_0x464165,_0x4b1b89,_0x126fb6,_0x45f983),_0x2730cd['drawImage'](_0x4a8477,0x0,_0x3bb608*_0x3616f1);let _0x1c869c=new Image();_0x1c869c['crossOrigin']='anonymous',_0x1c869c['src']=_0x3b2e99[_0xe4b281(0xf1)](),_0x1c869c['onload']=function(){_0x40a38b(_0x1c869c);};});}function addLookInsideTextToImage(_0x16d413){var _0x236a24=a0_0x4a7465,_0x396e47=_0x236a24(0xcb),_0xdd85eb=_0x236a24(0xe0),_0x74d8a=0x1,_0x352452=_0x236a24(0xd2),_0x11220b=_0x16d413['height']/0xa*1.5,_0x85bb=_0x11220b+_0x11220b/0x2,_0x29a030=0x0,_0x4c6c3a=_0x11220b,_0x483d52='#34B8FF',_0x3b5150=_0x236a24(0x136);return new Promise(function(_0x543a5f,_0x18804b){var _0x24d0d2=_0x236a24,_0x384868=document[_0x24d0d2(0xef)]('canvas');_0x384868['id']=_0x24d0d2(0x11a),_0x384868['width']=_0x16d413['width'],_0x384868[_0x24d0d2(0x116)]=_0x16d413[_0x24d0d2(0x116)]+_0x85bb*_0x74d8a;var _0xa6294c=_0x384868[_0x24d0d2(0x103)]('2d');_0xa6294c['fillStyle']=_0x24d0d2(0xf3),_0xa6294c[_0x24d0d2(0xe8)](0x0,0x0,_0x384868[_0x24d0d2(0x122)],_0x384868[_0x24d0d2(0x116)]);var _0x41c965=_0x384868['width'];_0xa6294c['fillStyle']=_0x3b5150,_0xa6294c['font']=_0x24d0d2(0xff)+_0x11220b+_0x24d0d2(0x119)+_0x352452,_0xa6294c['fillText'](_0x396e47,_0x29a030,_0x4c6c3a);var _0x3027e6=_0xa6294c['measureText'](_0x396e47)[_0x24d0d2(0x122)];_0xa6294c[_0x24d0d2(0x120)]=_0x483d52,_0xa6294c[_0x24d0d2(0xd7)]=_0x24d0d2(0xff)+_0x11220b+_0x24d0d2(0x119)+_0x352452,_0xa6294c[_0x24d0d2(0xee)](_0xdd85eb,_0x29a030+_0x3027e6,_0x4c6c3a),_0xa6294c[_0x24d0d2(0xd4)](_0x16d413,0x0,_0x85bb*_0x74d8a);var _0x3bf8e6=new Image();_0x3bf8e6[_0x24d0d2(0x134)]=_0x24d0d2(0xdc),_0x3bf8e6[_0x24d0d2(0xfe)]=_0x384868['toDataURL'](),_0x3bf8e6[_0x24d0d2(0x12a)]=function(){_0x543a5f(_0x3bf8e6);};});}function a0_0x1d15(_0x32e2d1,_0x297b61){var _0x4c0fb8=a0_0x4c0f();return a0_0x1d15=function(_0x1d15e2,_0x223bc1){_0x1d15e2=_0x1d15e2-0xcb;var _0x5aa39a=_0x4c0fb8[_0x1d15e2];return _0x5aa39a;},a0_0x1d15(_0x32e2d1,_0x297b61);}function addTextToImageFunction2(_0x30de2b,_0x5442a2,_0x27085a){var _0x2f22ad=a0_0x4a7465;_0x27085a=_0x27085a+'\x20⤸';var _0x4ffebe=0x1,_0x144eab=_0x2f22ad(0xd2),_0x31465a=_0x30de2b[_0x2f22ad(0x122)],_0x327fbf=_0x30de2b[_0x2f22ad(0x122)]/0xa,_0x5508cd=_0x327fbf+_0x327fbf/0x2,_0x3074a7=0x0,_0x2f6265=_0x327fbf,_0x4e9548=_0x2f22ad(0x138),_0xf4edfa=_0x2f22ad(0x136);return new Promise(function(_0x454947,_0x4888a9){var _0xf8b911=_0x2f22ad,_0x2a8114=document[_0xf8b911(0xef)](_0xf8b911(0x106));_0x2a8114['id']=_0xf8b911(0x11a),_0x2a8114[_0xf8b911(0x122)]=_0x30de2b[_0xf8b911(0x122)],_0x2a8114[_0xf8b911(0x116)]=_0x30de2b[_0xf8b911(0x116)]+_0x5508cd*_0x4ffebe;var _0x40c947=_0x2a8114['getContext']('2d');_0x40c947[_0xf8b911(0x120)]='white',_0x40c947['fillRect'](0x0,0x0,_0x2a8114['width'],_0x2a8114['height']),_0x40c947[_0xf8b911(0x120)]=_0x4e9548,_0x40c947[_0xf8b911(0xd7)]='bold\x20'+_0x327fbf+_0xf8b911(0x119)+_0x144eab,_0x40c947['fillText'](_0x5442a2,_0x3074a7,_0x2f6265);var _0x21c1b9=_0x40c947[_0xf8b911(0x11c)](_0x5442a2)[_0xf8b911(0x122)],_0x1a6861=_0x31465a-_0x21c1b9,_0x44936c=_0x1a6861/_0x27085a[_0xf8b911(0x10a)]*0x2;_0x40c947[_0xf8b911(0x120)]=_0xf4edfa,_0x40c947[_0xf8b911(0xd7)]=_0xf8b911(0xff)+_0x44936c+_0xf8b911(0x119)+_0x144eab,_0x40c947['fillText'](_0x27085a,_0x3074a7+_0x21c1b9,_0x2f6265),_0x40c947[_0xf8b911(0xd4)](_0x30de2b,0x0,_0x5508cd*_0x4ffebe);var _0x2502af=new Image();_0x2502af[_0xf8b911(0x134)]=_0xf8b911(0xdc),_0x2502af['src']=_0x2a8114['toDataURL'](),_0x2502af[_0xf8b911(0x12a)]=function(){_0x454947(_0x2502af);};});}function getTotalLines(_0x46aeca,_0x148acd,_0xe4f280,_0x11929e){return new Promise(_0x5bae29=>{var _0x595a3c=a0_0x1d15,_0x2b9c1c=document['createElement'](_0x595a3c(0x106));_0x2b9c1c['id']='myCanvas';var _0x2ef4cd=_0x46aeca['height']/0xa*0.45,_0x2736f2=_0x2ef4cd+_0x2ef4cd/0x2,_0x1022d2=0x0,_0x316e13=_0x2ef4cd;_0x2b9c1c[_0x595a3c(0x122)]=_0x46aeca[_0x595a3c(0x122)],_0x2b9c1c[_0x595a3c(0x116)]=_0x46aeca[_0x595a3c(0x116)];var _0x1351aa=_0x2b9c1c[_0x595a3c(0x103)]('2d');_0x1351aa[_0x595a3c(0x120)]=_0x595a3c(0xf3),_0x1351aa[_0x595a3c(0xe8)](0x0,0x0,_0x2b9c1c[_0x595a3c(0x122)],_0x2b9c1c[_0x595a3c(0x116)]);var _0x36a11a=_0x2b9c1c['width'],_0x2d2080=_0x2b9c1c['height']*(0x46/0x5dc);_0x1351aa[_0x595a3c(0x120)]=_0xe4f280,_0x1351aa['font']=_0x2ef4cd+_0x595a3c(0x119)+_0x11929e;var _0x2e4bcb=0x1,_0x17134b=_0x148acd[_0x595a3c(0x121)]('\x20'),_0x253051='';for(var _0x92c2fb=0x0;_0x92c2fb<_0x17134b[_0x595a3c(0x10a)];_0x92c2fb++){var _0x210468=_0x253051+_0x17134b[_0x92c2fb]+'\x20',_0x250c3e=_0x1351aa[_0x595a3c(0x11c)](_0x210468),_0x41f3e6=_0x250c3e[_0x595a3c(0x122)];_0x41f3e6>_0x36a11a&&_0x92c2fb>0x0?(_0x2e4bcb++,_0x1351aa[_0x595a3c(0xee)](_0x253051,_0x1022d2,_0x316e13),_0x253051=_0x17134b[_0x92c2fb]+'\x20',_0x316e13+=_0x2d2080):_0x253051=_0x210468;}_0x5bae29(_0x2e4bcb);});}function wrapText(_0x1dbdfb,_0x5d7ec9,_0x15404b,_0x184edf,_0x4f6416,_0x3d58e4){var _0x10e02e=a0_0x4a7465,_0x48d075=_0x5d7ec9[_0x10e02e(0x121)]('\x20'),_0x4e01fa='';for(var _0x205f36=0x0;_0x205f36<_0x48d075[_0x10e02e(0x10a)];_0x205f36++){var _0x359083=_0x4e01fa+_0x48d075[_0x205f36]+'\x20',_0x4ff2d7=_0x1dbdfb['measureText'](_0x359083),_0x58052c=_0x4ff2d7['width'];_0x58052c>_0x4f6416&&_0x205f36>0x0?(_0x1dbdfb['fillText'](_0x4e01fa,_0x15404b,_0x184edf),_0x4e01fa=_0x48d075[_0x205f36]+'\x20',_0x184edf+=_0x3d58e4):_0x4e01fa=_0x359083;}_0x1dbdfb[_0x10e02e(0xee)](_0x4e01fa,_0x15404b,_0x184edf);}function flipImage(_0x4b065c){return new Promise(function(_0x4fb14b,_0x5ad1e8){var _0x523dad=a0_0x1d15,_0xe31186=document[_0x523dad(0xef)](_0x523dad(0x106));_0xe31186['id']='myCanvas',_0xe31186[_0x523dad(0x122)]=_0x4b065c[_0x523dad(0x122)],_0xe31186[_0x523dad(0x116)]=_0x4b065c[_0x523dad(0x116)];var _0x215ece=_0xe31186[_0x523dad(0x103)]('2d');_0x215ece[_0x523dad(0x120)]=_0x523dad(0xf3),_0x215ece[_0x523dad(0xe8)](0x0,0x0,_0xe31186['width'],_0xe31186[_0x523dad(0x116)]),_0x215ece['translate'](_0xe31186[_0x523dad(0x122)],0x0),_0x215ece[_0x523dad(0x12e)](-0x1,0x1),_0x215ece['drawImage'](_0x4b065c,0x0,0x0);let _0x2c6f66=new Image();_0x2c6f66['crossOrigin']='anonymous',_0x2c6f66[_0x523dad(0xfe)]=_0xe31186[_0x523dad(0xf1)](),_0x2c6f66[_0x523dad(0x12a)]=function(){_0x4fb14b(_0x2c6f66);};});}function resizeImage(_0x173bc0,_0x6b6181,_0x43e630){return new Promise(function(_0x6c6929,_0x3379bd){var _0x22182a=a0_0x1d15,_0x283b98=document[_0x22182a(0xef)]('canvas');_0x283b98['id']=_0x22182a(0x11a),_0x283b98[_0x22182a(0x122)]=_0x6b6181,_0x283b98[_0x22182a(0x116)]=_0x43e630;var _0x144000=_0x283b98[_0x22182a(0x103)]('2d');_0x144000[_0x22182a(0x120)]=_0x22182a(0xf3),_0x144000[_0x22182a(0xe8)](0x0,0x0,_0x283b98[_0x22182a(0x122)],_0x283b98['height']);var _0x5201a6=_0x6b6181/0x2,_0x436185=_0x43e630/0x2,_0xae3294=0x1;_0x173bc0['width']>_0x173bc0['height']&&(_0xae3294=_0x6b6181/_0x173bc0[_0x22182a(0x122)]);_0x173bc0[_0x22182a(0x122)]<_0x173bc0[_0x22182a(0x116)]&&(_0xae3294=_0x6b6181/_0x173bc0[_0x22182a(0x116)]);_0x173bc0[_0x22182a(0x122)]===_0x173bc0['height']&&(_0xae3294=_0x6b6181/_0x173bc0[_0x22182a(0x116)]);var _0x5017ad=_0x173bc0[_0x22182a(0x122)]*_0xae3294,_0x5a0480=_0x173bc0[_0x22182a(0x116)]*_0xae3294;_0x144000[_0x22182a(0x115)]=0x1,_0x144000[_0x22182a(0xd4)](_0x173bc0,_0x5201a6-_0x5017ad/0x2,_0x436185-_0x5a0480/0x2,_0x5017ad,_0x5a0480);let _0x43a7e5=new Image();_0x43a7e5['crossOrigin']=_0x22182a(0xdc),_0x43a7e5['src']=_0x283b98[_0x22182a(0xf1)](),_0x43a7e5[_0x22182a(0x12a)]=function(){_0x6c6929(_0x43a7e5);};});}function resizeToImperfectSquareImage(_0x40c7d5,_0xaee7fc,_0x5a7b11){return new Promise(function(_0x47e80d,_0x314a6a){var _0x233510=a0_0x1d15,_0xaace75=0x1;_0x40c7d5[_0x233510(0x122)]>_0x40c7d5[_0x233510(0x116)]&&(_0xaace75=_0xaee7fc/_0x40c7d5[_0x233510(0x122)]);_0x40c7d5['width']<_0x40c7d5['height']&&(_0xaace75=_0xaee7fc/_0x40c7d5['height']);_0x40c7d5['width']===_0x40c7d5['height']&&(_0xaace75=_0xaee7fc/_0x40c7d5['height']);var _0x2df5a2=_0x40c7d5[_0x233510(0x122)]*_0xaace75,_0x821bc=_0x40c7d5[_0x233510(0x116)]*_0xaace75,_0x3c730a=document[_0x233510(0xef)](_0x233510(0x106));_0x3c730a['id']=_0x233510(0x11a),_0x3c730a[_0x233510(0x122)]=_0x2df5a2,_0x3c730a['height']=_0x821bc,_0x3c730a[_0x233510(0xcc)]=_0x233510(0xe7);var _0x1ad24a=_0x3c730a['getContext']('2d');_0x1ad24a['fillStyle']=_0x233510(0xf3),_0x1ad24a['fillRect'](0x0,0x0,_0x3c730a[_0x233510(0x122)],_0x3c730a[_0x233510(0x116)]);var _0x3bad1c=_0xaee7fc/0x2,_0x3d6fe5=_0x5a7b11/0x2;_0x1ad24a[_0x233510(0x115)]=0x1,_0x1ad24a[_0x233510(0xd4)](_0x40c7d5,0x0,0x0,_0x2df5a2,_0x821bc);let _0x283c0a=new Image();_0x283c0a['crossOrigin']='anonymous',_0x283c0a[_0x233510(0xfe)]=_0x3c730a['toDataURL'](),_0x283c0a[_0x233510(0x12a)]=function(){_0x47e80d(_0x283c0a);};});}function resizeToPerfectSquareImage(_0x574d98){return new Promise(function(_0x5e8740,_0x5d1afd){var _0x414c47=a0_0x1d15;const _0x5f49fb=Math[_0x414c47(0x11b)](_0x574d98[_0x414c47(0x122)],_0x574d98['height']),_0x2fd1f8=document['createElement'](_0x414c47(0x106));_0x2fd1f8[_0x414c47(0x122)]=_0x5f49fb,_0x2fd1f8['height']=_0x5f49fb;const _0x10d476=_0x2fd1f8[_0x414c47(0x103)]('2d');_0x10d476[_0x414c47(0x120)]=_0x414c47(0xf3),_0x10d476[_0x414c47(0xe8)](0x0,0x0,_0x2fd1f8[_0x414c47(0x122)],_0x2fd1f8['height']);const _0x5c7ae0=(_0x5f49fb-_0x574d98[_0x414c47(0x122)])/0x2,_0x2d52a1=(_0x5f49fb-_0x574d98[_0x414c47(0x116)])/0x2;_0x10d476[_0x414c47(0xd4)](_0x574d98,_0x5c7ae0,_0x2d52a1,_0x574d98[_0x414c47(0x122)],_0x574d98[_0x414c47(0x116)]);const _0x5c121f=new Image();_0x5c121f[_0x414c47(0x12a)]=function(){_0x5e8740(_0x5c121f);},_0x5c121f[_0x414c47(0xfe)]=_0x2fd1f8['toDataURL']();});}function upscaleToMinimumSize(_0x1333e6,_0x17c3cf,_0x29d50a){return new Promise(function(_0x2d3360,_0x2083ed){var _0x3220ff=a0_0x1d15,_0x415b5c=_0x1333e6[_0x3220ff(0x122)],_0x652829=_0x1333e6['height'];if(_0x415b5c<_0x17c3cf||_0x652829<_0x29d50a){var _0x2bf4ab=document[_0x3220ff(0xef)]('canvas'),_0x30ae72=_0x2bf4ab['getContext']('2d'),_0x1ba7d7=Math['max'](_0x17c3cf/_0x415b5c,_0x29d50a/_0x652829);_0x415b5c*=_0x1ba7d7,_0x652829*=_0x1ba7d7,_0x2bf4ab[_0x3220ff(0x122)]=_0x415b5c,_0x2bf4ab[_0x3220ff(0x116)]=_0x652829,_0x30ae72[_0x3220ff(0xd4)](_0x1333e6,0x0,0x0,_0x415b5c,_0x652829);var _0x3e3596=new Image();_0x3e3596[_0x3220ff(0xfe)]=_0x2bf4ab[_0x3220ff(0xf1)](),_0x3e3596[_0x3220ff(0x12a)]=function(){_0x2d3360(_0x3e3596);};}else _0x2d3360(_0x1333e6);});}function resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x475bfb,_0xc9f1b9,_0xb90a8a){return new Promise(function(_0x3b6264,_0x54856c){var _0xbdca8f=a0_0x1d15,_0x4b1923=0x1;_0x475bfb['width']>_0x475bfb[_0xbdca8f(0x116)]&&(_0x4b1923=_0xc9f1b9/_0x475bfb[_0xbdca8f(0x122)]);_0x475bfb['width']<_0x475bfb[_0xbdca8f(0x116)]&&(_0x4b1923=_0xc9f1b9/_0x475bfb['height']);_0x475bfb[_0xbdca8f(0x122)]===_0x475bfb[_0xbdca8f(0x116)]&&(_0x4b1923=_0xc9f1b9/_0x475bfb['height']);var _0x4d293d=_0x475bfb[_0xbdca8f(0x122)]*_0x4b1923,_0x31e1fa=_0x475bfb[_0xbdca8f(0x116)]*_0x4b1923,_0x5e840a=0x1;while(_0x4d293d>_0xc9f1b9){_0x4d293d=_0x475bfb[_0xbdca8f(0x122)]*_0x5e840a,_0x31e1fa=_0x475bfb[_0xbdca8f(0x116)]*_0x5e840a,_0x5e840a=_0x5e840a-0.01;}var _0x14fd23=document[_0xbdca8f(0xef)](_0xbdca8f(0x106));_0x14fd23['id']=_0xbdca8f(0x11a),_0x14fd23['width']=_0x4d293d,_0x14fd23['height']=_0x31e1fa,_0x14fd23[_0xbdca8f(0xcc)]='border:2px\x20solid\x20black;';var _0x5cbef=_0x14fd23[_0xbdca8f(0x103)]('2d');_0x5cbef[_0xbdca8f(0x120)]=_0xbdca8f(0xf3),_0x5cbef[_0xbdca8f(0xe8)](0x0,0x0,_0x14fd23[_0xbdca8f(0x122)],_0x14fd23['height']);var _0x44a7be=_0xc9f1b9/0x2,_0x5b8c35=_0xb90a8a/0x2;_0x5cbef[_0xbdca8f(0x115)]=0x1,_0x5cbef[_0xbdca8f(0xd4)](_0x475bfb,0x0,0x0,_0x4d293d,_0x31e1fa);let _0x231ae1=new Image();_0x231ae1['crossOrigin']=_0xbdca8f(0xdc),_0x231ae1['src']=_0x14fd23['toDataURL'](),_0x231ae1[_0xbdca8f(0x12a)]=function(){_0x3b6264(_0x231ae1);};});}function b64ToCanvas(_0x417834){return new Promise((_0x165910,_0xabf45c)=>{var _0x2acd8d=a0_0x1d15,_0x3f9ee2=document[_0x2acd8d(0xef)]('canvas'),_0x3d0514=_0x3f9ee2[_0x2acd8d(0x103)]('2d'),_0x429b34=new Image();_0x429b34[_0x2acd8d(0x12a)]=function(){var _0x2a501a=_0x2acd8d;_0x3d0514[_0x2a501a(0xd4)](_0x429b34,0x0,0x0),_0x165910(_0x429b34);},_0x429b34[_0x2acd8d(0xfe)]=_0x417834;});}function addLogo(_0x29ad7d,_0x147d58,_0x109ba5){return new Promise(function(_0xd751cb,_0x38eacd){var _0x22f24a=a0_0x1d15;let _0x327d30=document[_0x22f24a(0xef)](_0x22f24a(0x106));_0x327d30[_0x22f24a(0x122)]=_0x29ad7d['width']+_0x147d58['width'],_0x327d30[_0x22f24a(0x116)]=_0x29ad7d[_0x22f24a(0x116)];let _0x175df1=_0x327d30['getContext']('2d');_0x175df1[_0x22f24a(0x120)]=_0x22f24a(0xf3),_0x175df1[_0x22f24a(0xe8)](0x0,0x0,_0x327d30[_0x22f24a(0x122)],_0x327d30[_0x22f24a(0x116)]),_0x175df1[_0x22f24a(0xd4)](_0x29ad7d,0x0,0x0),_0x175df1['globalAlpha']=_0x109ba5;var _0x26046d=_0x147d58[_0x22f24a(0x122)]*0.9,_0x39ecb5=_0x147d58[_0x22f24a(0x116)]*0.9;_0x175df1[_0x22f24a(0xd4)](_0x147d58,_0x327d30[_0x22f24a(0x122)]-_0x26046d,0x0,_0x26046d,_0x39ecb5);var _0x57fc8f=new Image();_0x57fc8f[_0x22f24a(0x12a)]=function(){_0xd751cb(_0x57fc8f);},_0x57fc8f[_0x22f24a(0xfe)]=_0x327d30[_0x22f24a(0xf1)]();});}function rotateImage(_0x4aadc2,_0x3a8032){return new Promise(function(_0x32a724,_0x2fccc6){var _0x3cdb3b=a0_0x1d15,_0x29712c=document[_0x3cdb3b(0xef)]('canvas'),_0xa88816=_0x29712c[_0x3cdb3b(0x103)]('2d');_0xa88816[_0x3cdb3b(0x120)]=_0x3cdb3b(0xf3),_0xa88816[_0x3cdb3b(0xe8)](0x0,0x0,_0x29712c['width'],_0x29712c[_0x3cdb3b(0x116)]);var _0x3e8013=_0x3a8032*Math['PI']/0xb4,_0x16690d=Math[_0x3cdb3b(0x129)](_0x3e8013),_0x30f89d=Math[_0x3cdb3b(0x125)](_0x3e8013);_0x30f89d<0x0&&(_0x30f89d=-_0x30f89d);_0x16690d<0x0&&(_0x16690d=-_0x16690d);_0x29712c[_0x3cdb3b(0x122)]=_0x4aadc2[_0x3cdb3b(0x116)]*_0x30f89d+_0x4aadc2[_0x3cdb3b(0x122)]*_0x16690d,_0x29712c['height']=_0x4aadc2[_0x3cdb3b(0x116)]*_0x16690d+_0x4aadc2['width']*_0x30f89d;var _0x4c3bdf=_0x4aadc2[_0x3cdb3b(0x122)],_0x574e3f=_0x4aadc2[_0x3cdb3b(0x116)],_0x342384=_0x29712c[_0x3cdb3b(0x122)]/0x2,_0xdd7dbf=_0x29712c[_0x3cdb3b(0x116)]/0x2,_0x4109c7=Math['PI']/0xb4;_0xa88816[_0x3cdb3b(0xce)](_0x342384,_0xdd7dbf),_0xa88816[_0x3cdb3b(0xdb)](_0x3a8032*_0x4109c7),_0xa88816[_0x3cdb3b(0xd4)](_0x4aadc2,-_0x4c3bdf/0x2,-_0x574e3f/0x2,_0x4c3bdf,_0x574e3f);var _0x4a6432=new Image();_0x4a6432['onload']=function(){_0x32a724(_0x4a6432);},_0x4a6432[_0x3cdb3b(0xfe)]=_0x29712c[_0x3cdb3b(0xf1)]();});}function makeImageWider(_0x2af3c7,_0x2d1aba,_0x259f67){return new Promise(function(_0x2d969f,_0xa2f29a){var _0x464526=a0_0x1d15;let _0x2c6154=document[_0x464526(0xef)](_0x464526(0x106));_0x2c6154[_0x464526(0x122)]=_0x2af3c7[_0x464526(0x122)]+_0x2d1aba[_0x464526(0x122)],_0x2c6154[_0x464526(0x116)]=_0x2af3c7[_0x464526(0x116)];let _0x23cd1b=_0x2c6154[_0x464526(0x103)]('2d');_0x23cd1b[_0x464526(0x120)]=_0x464526(0xf3),_0x23cd1b[_0x464526(0xe8)](0x0,0x0,_0x2c6154['width'],_0x2c6154['height']);var _0x5ed18d=_0x2d1aba[_0x464526(0x122)],_0x2b932f=_0x2d1aba[_0x464526(0x116)];if(_0x259f67===_0x464526(0x137))_0x23cd1b[_0x464526(0xd4)](_0x2af3c7,_0x5ed18d,0x0);else _0x259f67==='right'&&_0x23cd1b[_0x464526(0xd4)](_0x2af3c7,0x0,0x0);var _0x51d9f1=new Image();_0x51d9f1[_0x464526(0x12a)]=function(){_0x2d969f(_0x51d9f1);},_0x51d9f1[_0x464526(0xfe)]=_0x2c6154['toDataURL']();});}function makeImageLonger(_0x4b8930,_0x9d395d,_0x3297ee){return new Promise(function(_0x3d18bb,_0xf79606){var _0x224014=a0_0x1d15;let _0x5c5db8=document[_0x224014(0xef)](_0x224014(0x106));_0x5c5db8['width']=_0x4b8930['width']+_0x9d395d,_0x5c5db8[_0x224014(0x116)]=_0x4b8930[_0x224014(0x116)];let _0x1c27aa=_0x5c5db8[_0x224014(0x103)]('2d');_0x1c27aa[_0x224014(0x120)]=_0x224014(0xf3),_0x1c27aa[_0x224014(0xe8)](0x0,0x0,_0x5c5db8[_0x224014(0x122)],_0x5c5db8[_0x224014(0x116)]);var _0x1b8efd=_0x9d395d;if(_0x3297ee==='top')_0x1c27aa[_0x224014(0xd4)](_0x4b8930,0x0,_0x1b8efd);else _0x3297ee===_0x224014(0xf4)&&_0x1c27aa[_0x224014(0xd4)](_0x4b8930,0x0,0x0);var _0x43d3b7=new Image();_0x43d3b7[_0x224014(0x12a)]=function(){_0x3d18bb(_0x43d3b7);},_0x43d3b7[_0x224014(0xfe)]=_0x5c5db8['toDataURL']();});}function makeImageAspectSame(_0x2821e0){return new Promise(function(_0x21d37c,_0x3d5950){var _0x5cd93c=a0_0x1d15;let _0x46238e=document[_0x5cd93c(0xef)]('canvas');console['log'](_0x5cd93c(0xe2));if(_0x2821e0['width']>_0x2821e0[_0x5cd93c(0x116)])_0x46238e[_0x5cd93c(0x122)]=_0x2821e0[_0x5cd93c(0x122)],_0x46238e[_0x5cd93c(0x116)]=_0x2821e0[_0x5cd93c(0x116)]+(_0x2821e0[_0x5cd93c(0x122)]-_0x2821e0[_0x5cd93c(0x116)]);else _0x2821e0[_0x5cd93c(0x122)]<_0x2821e0[_0x5cd93c(0x116)]?(_0x46238e[_0x5cd93c(0x116)]=_0x2821e0[_0x5cd93c(0x116)],_0x46238e[_0x5cd93c(0x122)]=_0x2821e0['width']+(_0x2821e0[_0x5cd93c(0x116)]-_0x2821e0[_0x5cd93c(0x122)])):(console[_0x5cd93c(0xd1)](_0x5cd93c(0xd8)),_0x21d37c(_0x2821e0));console[_0x5cd93c(0xd1)](_0x5cd93c(0xe6));let _0x12c525=_0x46238e['getContext']('2d');_0x12c525['fillStyle']=_0x5cd93c(0xf3),_0x12c525['fillRect'](0x0,0x0,_0x46238e[_0x5cd93c(0x122)],_0x46238e[_0x5cd93c(0x116)]),_0x12c525['drawImage'](_0x2821e0,(_0x46238e['width']-_0x2821e0[_0x5cd93c(0x122)])/0x2,(_0x46238e['height']-_0x2821e0[_0x5cd93c(0x116)])/0x2);var _0x9f12=new Image();_0x9f12[_0x5cd93c(0x12a)]=function(){_0x21d37c(_0x9f12);},_0x9f12[_0x5cd93c(0xfe)]=_0x46238e['toDataURL']();});}function addTopLeftImage(_0x30d160,_0x4bf603,_0x313231){return new Promise(function(_0x403dc5,_0x1abb45){var _0x3a86cd=a0_0x1d15;let _0x1385bc=document['createElement'](_0x3a86cd(0x106));_0x1385bc['width']=_0x30d160[_0x3a86cd(0x122)],_0x1385bc['height']=_0x30d160['height'];let _0x25e495=_0x1385bc[_0x3a86cd(0x103)]('2d');_0x25e495['fillStyle']=_0x3a86cd(0xf3),_0x25e495['fillRect'](0x0,0x0,_0x1385bc['width'],_0x1385bc[_0x3a86cd(0x116)]);var _0x2ef5ce=_0x4bf603['width'],_0xc03b65=_0x4bf603[_0x3a86cd(0x116)];_0x25e495[_0x3a86cd(0xd4)](_0x30d160,0x0,0x0),_0x25e495[_0x3a86cd(0x115)]=_0x313231;var _0x541625=0x0+_0x1385bc[_0x3a86cd(0x122)]/0x32;_0x25e495[_0x3a86cd(0xd4)](_0x4bf603,_0x541625,0x0,_0x2ef5ce,_0xc03b65);var _0xeadee=new Image();_0xeadee[_0x3a86cd(0x12a)]=function(){_0x403dc5(_0xeadee);},_0xeadee[_0x3a86cd(0xfe)]=_0x1385bc['toDataURL']();});}function addWaterMarkImageToTopRight(_0x3a7d47,_0x1c02a3,_0x13b0dc){return new Promise(async function(_0x31deff,_0x5a7884){var _0x1c194a=a0_0x1d15;let _0x53114e=document[_0x1c194a(0xef)](_0x1c194a(0x106));_0x53114e[_0x1c194a(0x122)]=_0x3a7d47['width'],_0x53114e[_0x1c194a(0x116)]=_0x3a7d47[_0x1c194a(0x116)];let _0x1e7b6c=_0x53114e[_0x1c194a(0x103)]('2d');_0x1e7b6c[_0x1c194a(0x120)]=_0x1c194a(0xf3),_0x1e7b6c[_0x1c194a(0xe8)](0x0,0x0,_0x53114e[_0x1c194a(0x122)],_0x53114e['height']),_0x1e7b6c[_0x1c194a(0xd4)](_0x3a7d47,0x0,0x0),_0x1e7b6c[_0x1c194a(0x115)]=_0x13b0dc,_0x1e7b6c[_0x1c194a(0xd4)](_0x1c02a3,_0x53114e['width']-_0x1c02a3[_0x1c194a(0x122)],0x0,_0x1c02a3['width'],_0x1c02a3[_0x1c194a(0x116)]);var _0x112ef8=new Image();_0x112ef8[_0x1c194a(0x12a)]=function(){_0x31deff(_0x112ef8);},_0x112ef8[_0x1c194a(0xfe)]=_0x53114e[_0x1c194a(0xf1)]();});}function addTopRightImage(_0x1cbced,_0x3eee97,_0x5f4083){return new Promise(function(_0x154613,_0xf28253){var _0x4ce358=a0_0x1d15;let _0x1fb7d5=document[_0x4ce358(0xef)]('canvas');_0x1fb7d5[_0x4ce358(0x122)]=_0x1cbced[_0x4ce358(0x122)],_0x1fb7d5[_0x4ce358(0x116)]=_0x1cbced[_0x4ce358(0x116)];let _0x47e710=_0x1fb7d5[_0x4ce358(0x103)]('2d');_0x47e710[_0x4ce358(0x120)]=_0x4ce358(0xf3),_0x47e710['fillRect'](0x0,0x0,_0x1fb7d5[_0x4ce358(0x122)],_0x1fb7d5[_0x4ce358(0x116)]),_0x47e710[_0x4ce358(0xd4)](_0x1cbced,0x0,0x0),_0x47e710[_0x4ce358(0x115)]=_0x5f4083;var _0x4722bd=_0x3eee97['width'],_0xf9c7c6=_0x3eee97['height'];_0x47e710['drawImage'](_0x3eee97,_0x1fb7d5[_0x4ce358(0x122)]-_0x4722bd,0x0,_0x4722bd,_0xf9c7c6);var _0x203e00=new Image();_0x203e00[_0x4ce358(0x12a)]=function(){_0x154613(_0x203e00);},_0x203e00[_0x4ce358(0xfe)]=_0x1fb7d5[_0x4ce358(0xf1)]();});}function roundedImage(_0x27d42e,_0x2f16f2,_0x2de4f3,_0x415533,_0x11e89c,_0x1efc3b){var _0x3eeb38=a0_0x4a7465;_0x27d42e[_0x3eeb38(0x114)](),_0x27d42e[_0x3eeb38(0x131)](_0x2f16f2+_0x1efc3b,_0x2de4f3),_0x27d42e[_0x3eeb38(0xfa)](_0x2f16f2+_0x415533-_0x1efc3b,_0x2de4f3),_0x27d42e['quadraticCurveTo'](_0x2f16f2+_0x415533,_0x2de4f3,_0x2f16f2+_0x415533,_0x2de4f3+_0x1efc3b),_0x27d42e['lineTo'](_0x2f16f2+_0x415533,_0x2de4f3+_0x11e89c-_0x1efc3b),_0x27d42e[_0x3eeb38(0x12f)](_0x2f16f2+_0x415533,_0x2de4f3+_0x11e89c,_0x2f16f2+_0x415533-_0x1efc3b,_0x2de4f3+_0x11e89c),_0x27d42e[_0x3eeb38(0xfa)](_0x2f16f2+_0x1efc3b,_0x2de4f3+_0x11e89c),_0x27d42e[_0x3eeb38(0x12f)](_0x2f16f2,_0x2de4f3+_0x11e89c,_0x2f16f2,_0x2de4f3+_0x11e89c-_0x1efc3b),_0x27d42e['lineTo'](_0x2f16f2,_0x2de4f3+_0x1efc3b),_0x27d42e['quadraticCurveTo'](_0x2f16f2,_0x2de4f3,_0x2f16f2+_0x1efc3b,_0x2de4f3),_0x27d42e[_0x3eeb38(0x10b)]();}function addBottomRightImage(_0x4b8c79,_0x315b87,_0x48a0cd,_0x301561){return new Promise(function(_0x48b5ae,_0x675f44){var _0x223a0d=a0_0x1d15;let _0x56e015=document[_0x223a0d(0xef)](_0x223a0d(0x106));_0x56e015[_0x223a0d(0x122)]=_0x4b8c79[_0x223a0d(0x122)],_0x56e015[_0x223a0d(0x116)]=_0x4b8c79[_0x223a0d(0x116)];let _0x162eee=_0x56e015[_0x223a0d(0x103)]('2d');_0x162eee[_0x223a0d(0x120)]='white',_0x162eee[_0x223a0d(0xe8)](0x0,0x0,_0x56e015['width'],_0x56e015[_0x223a0d(0x116)]),_0x162eee['drawImage'](_0x4b8c79,0x0,0x0),_0x162eee[_0x223a0d(0x115)]=_0x48a0cd;var _0x59bb17=_0x56e015[_0x223a0d(0x122)]-_0x315b87[_0x223a0d(0x122)]-_0x56e015[_0x223a0d(0x122)]/0x32,_0x2859ec=_0x56e015[_0x223a0d(0x116)]-_0x315b87['height']-_0x56e015[_0x223a0d(0x116)]/_0x301561;roundedImage(_0x162eee,_0x59bb17,_0x2859ec,_0x315b87['width'],_0x315b87[_0x223a0d(0x116)],0xa),_0x162eee[_0x223a0d(0x111)](),_0x162eee[_0x223a0d(0xd4)](_0x315b87,_0x59bb17,_0x2859ec);var _0x57e84d=new Image();_0x57e84d[_0x223a0d(0x12a)]=function(){_0x48b5ae(_0x57e84d);},_0x57e84d[_0x223a0d(0xfe)]=_0x56e015[_0x223a0d(0xf1)]();});}function addBottomLeftImage(_0x16de00,_0x387398,_0x428fef,_0xff7b92){return new Promise(function(_0x5411f,_0xd7d492){var _0x2e7cb2=a0_0x1d15;let _0x5bd860=document[_0x2e7cb2(0xef)](_0x2e7cb2(0x106));_0x5bd860[_0x2e7cb2(0x122)]=_0x16de00[_0x2e7cb2(0x122)],_0x5bd860['height']=_0x16de00[_0x2e7cb2(0x116)];let _0x2c1681=_0x5bd860['getContext']('2d');_0x2c1681[_0x2e7cb2(0x120)]=_0x2e7cb2(0xf3),_0x2c1681['fillRect'](0x0,0x0,_0x5bd860[_0x2e7cb2(0x122)],_0x5bd860[_0x2e7cb2(0x116)]),_0x2c1681[_0x2e7cb2(0xd4)](_0x16de00,0x0,0x0),_0x2c1681['globalAlpha']=_0x428fef;var _0x6c4fc7=0x0+_0x5bd860[_0x2e7cb2(0x122)]/0x32,_0x5cfecb=_0x5bd860['height']-_0x387398['height']-_0x5bd860[_0x2e7cb2(0x116)]/_0xff7b92;_0x2c1681[_0x2e7cb2(0xd4)](_0x387398,_0x6c4fc7,_0x5cfecb);var _0xae10e=new Image();_0xae10e[_0x2e7cb2(0x12a)]=function(){_0x5411f(_0xae10e);},_0xae10e['src']=_0x5bd860[_0x2e7cb2(0xf1)]();});}function addImageToBottom(_0x27ef2b,_0x4bf0ae,_0x2bf98e){return new Promise(function(_0x3c78e3,_0x48a642){var _0x54f6f6=a0_0x1d15;let _0x1dd2cb=document[_0x54f6f6(0xef)](_0x54f6f6(0x106));_0x1dd2cb[_0x54f6f6(0x122)]=_0x27ef2b['width'],_0x1dd2cb[_0x54f6f6(0x116)]=_0x27ef2b[_0x54f6f6(0x116)];let _0x4030da=_0x1dd2cb['getContext']('2d');_0x4030da[_0x54f6f6(0x120)]=_0x54f6f6(0xf3),_0x4030da['fillRect'](0x0,0x0,_0x1dd2cb['width'],_0x1dd2cb[_0x54f6f6(0x116)]),_0x4030da[_0x54f6f6(0xd4)](_0x27ef2b,0x0,0x0),_0x4030da['drawImage'](_0x4bf0ae,_0x2bf98e,_0x1dd2cb[_0x54f6f6(0x116)]-_0x4bf0ae[_0x54f6f6(0x116)]);var _0x615b76=new Image();_0x615b76[_0x54f6f6(0x12a)]=function(){_0x3c78e3(_0x615b76);},_0x615b76[_0x54f6f6(0xfe)]=_0x1dd2cb[_0x54f6f6(0xf1)]();});}function changeOpacityFix(_0x3d0b10,_0x36ccc7){return new Promise(function(_0x59acaf,_0xefe60a){var _0x1ae62d=a0_0x1d15;let _0x25f6b5=document[_0x1ae62d(0xef)](_0x1ae62d(0x106));_0x25f6b5['width']=_0x3d0b10[_0x1ae62d(0x122)],_0x25f6b5[_0x1ae62d(0x116)]=_0x3d0b10[_0x1ae62d(0x116)];let _0x31a7a8=_0x25f6b5[_0x1ae62d(0x103)]('2d');_0x31a7a8['fillStyle']=_0x1ae62d(0xf3),_0x31a7a8[_0x1ae62d(0xe8)](0x0,0x0,_0x25f6b5[_0x1ae62d(0x122)],_0x25f6b5[_0x1ae62d(0x116)]),_0x31a7a8[_0x1ae62d(0x115)]=_0x36ccc7,_0x31a7a8[_0x1ae62d(0xd4)](_0x3d0b10,0x0,0x0);var _0x190cc8=new Image();_0x190cc8[_0x1ae62d(0x12a)]=function(){_0x59acaf(_0x190cc8);},_0x190cc8[_0x1ae62d(0xfe)]=_0x25f6b5[_0x1ae62d(0xf1)]();});}function changeOpacity(_0x71ce72,_0x3b7a49){return new Promise(function(_0x2010cb,_0x51ee05){var _0x4ca0c3=a0_0x1d15;let _0x2997ef=document[_0x4ca0c3(0xef)](_0x4ca0c3(0x106));_0x2997ef[_0x4ca0c3(0x122)]=_0x71ce72[_0x4ca0c3(0x122)]+imgWatermark[_0x4ca0c3(0x122)],_0x2997ef[_0x4ca0c3(0x116)]=_0x71ce72[_0x4ca0c3(0x116)];let _0x168a5c=_0x2997ef[_0x4ca0c3(0x103)]('2d');_0x168a5c[_0x4ca0c3(0x120)]=_0x4ca0c3(0xf3),_0x168a5c[_0x4ca0c3(0xe8)](0x0,0x0,_0x2997ef[_0x4ca0c3(0x122)],_0x2997ef['height']),_0x168a5c[_0x4ca0c3(0x115)]=_0x3b7a49,_0x168a5c['drawImage'](_0x71ce72,0x0,0x0);var _0x5d67a9=imgWatermark[_0x4ca0c3(0x122)]*0.9,_0x165b4f=imgWatermark[_0x4ca0c3(0x116)]*0.9;_0x168a5c[_0x4ca0c3(0xd4)](imgWatermark,_0x2997ef[_0x4ca0c3(0x122)]-_0x5d67a9,0x0,_0x5d67a9,_0x165b4f);var _0x5ce228=new Image();_0x5ce228[_0x4ca0c3(0x12a)]=function(){_0x2010cb(_0x5ce228);},_0x5ce228['src']=_0x2997ef[_0x4ca0c3(0xf1)]();});}function imgToCanvas(_0x1df41f){return new Promise((_0xfda63,_0x51f2c3)=>{var _0xb5046e=a0_0x1d15,_0x19ca8f=document[_0xb5046e(0xef)](_0xb5046e(0x106)),_0x33005c=_0x19ca8f['getContext']('2d');_0x19ca8f[_0xb5046e(0x122)]=_0x1df41f[_0xb5046e(0x122)],_0x19ca8f['height']=_0x1df41f['height'];var _0x411aff=new Image();_0x411aff[_0xb5046e(0x12a)]=function(){var _0x523bc6=_0xb5046e;_0x33005c[_0x523bc6(0xd4)](_0x1df41f,0x0,0x0),_0xfda63(_0x19ca8f);},_0x411aff['src']=_0x1df41f[_0xb5046e(0xfe)];});}function canvasToImg(_0x315d49){return new Promise((_0x1bf74b,_0x101a83)=>{var _0x4b0eb0=a0_0x1d15,_0x39ca5c=new Image();_0x39ca5c['onload']=function(){_0x1bf74b(_0x39ca5c);},_0x39ca5c[_0x4b0eb0(0xfe)]=_0x315d49[_0x4b0eb0(0xf1)]();});}function removeWhiteSpaceFromImage(_0x2657fc){return new Promise((_0x2a1ded,_0x36d76b)=>{var _0x33bc23=a0_0x1d15,_0x4a1583=new Image(),_0x21654e=document[_0x33bc23(0xef)](_0x33bc23(0x106)),_0x5cc5f2=_0x21654e['getContext']('2d'),_0x3c0818={};_0x4a1583[_0x33bc23(0x12a)]=function(){var _0x4b166d=_0x33bc23;_0x21654e[_0x4b166d(0x122)]=_0x4a1583['width'],_0x21654e['height']=_0x4a1583[_0x4b166d(0x116)],_0x5cc5f2[_0x4b166d(0xd4)](_0x4a1583,0x0,0x0,_0x4a1583['width'],_0x4a1583[_0x4b166d(0x116)]),_0x3c0818=_0x5cc5f2[_0x4b166d(0x105)](0x0,0x0,_0x4a1583[_0x4b166d(0x122)],_0x4a1583['height'])[_0x4b166d(0x12c)];var _0x5a6a15=scanY(!![],_0x4a1583,_0x3c0818),_0x48d8ef=scanY(![],_0x4a1583,_0x3c0818),_0x48b9b4=scanX(!![],_0x4a1583,_0x3c0818),_0x4736d4=scanX(![],_0x4a1583,_0x3c0818),_0x2c070e=_0x4736d4-_0x48b9b4,_0x504643=_0x48d8ef-_0x5a6a15;_0x21654e[_0x4b166d(0x122)]=_0x2c070e,_0x21654e['height']=_0x504643,_0x5cc5f2['drawImage'](_0x4a1583,_0x48b9b4,_0x5a6a15,_0x2c070e,_0x504643,0x0,0x0,_0x2c070e,_0x504643);let _0x8c7704=new Image();_0x8c7704[_0x4b166d(0x134)]=_0x4b166d(0xdc),_0x8c7704['onload']=function(){_0x2a1ded(_0x8c7704);},_0x8c7704[_0x4b166d(0xfe)]=_0x21654e[_0x4b166d(0xf1)]();},_0x4a1583['src']=_0x2657fc['src'];});}function scanX(_0x21bab6,_0x3cb8b9,_0x189187){var _0x56c54f=a0_0x4a7465,_0x234825=_0x21bab6?0x1:-0x1;for(var _0x5e62ee=_0x21bab6?0x0:_0x3cb8b9[_0x56c54f(0x122)]-0x1;_0x21bab6?_0x5e62ee<_0x3cb8b9[_0x56c54f(0x122)]:_0x5e62ee>-0x1;_0x5e62ee+=_0x234825){for(var _0x2cde95=0x0;_0x2cde95<_0x3cb8b9[_0x56c54f(0x116)];_0x2cde95++){if(!isEmpty(getRGB(_0x5e62ee,_0x2cde95,_0x189187,_0x3cb8b9)))return _0x5e62ee;}}return null;}function scanY(_0x837e4c,_0x4729cd,_0xcc7df2){var _0x140d1b=a0_0x4a7465,_0x4be90e=_0x837e4c?0x1:-0x1;for(var _0x590a58=_0x837e4c?0x0:_0x4729cd[_0x140d1b(0x116)]-0x1;_0x837e4c?_0x590a58<_0x4729cd[_0x140d1b(0x116)]:_0x590a58>-0x1;_0x590a58+=_0x4be90e){for(var _0x2e5f31=0x0;_0x2e5f31<_0x4729cd['width'];_0x2e5f31++){if(!isEmpty(getRGB(_0x2e5f31,_0x590a58,_0xcc7df2,_0x4729cd)))return _0x590a58;}}return null;}function isEmpty(_0x387b4e){var _0x3ae4dd=a0_0x4a7465;return _0x387b4e[_0x3ae4dd(0x10e)]==0xff&&_0x387b4e[_0x3ae4dd(0xda)]==0xff&&_0x387b4e['blue']==0xff;}function getRGB(_0x2d317f,_0x2b45ca,_0x2dc454,_0x3b32a1){var _0x4ca570=a0_0x4a7465;return{'red':_0x2dc454[(_0x3b32a1[_0x4ca570(0x122)]*_0x2b45ca+_0x2d317f)*0x4],'green':_0x2dc454[(_0x3b32a1[_0x4ca570(0x122)]*_0x2b45ca+_0x2d317f)*0x4+0x1],'blue':_0x2dc454[(_0x3b32a1[_0x4ca570(0x122)]*_0x2b45ca+_0x2d317f)*0x4+0x2]};}function makeImageTransparent(_0x469ead){return new Promise((_0x24cfea,_0x2fa799)=>{var _0x48bd50=a0_0x1d15,_0x4a171d=document[_0x48bd50(0xef)](_0x48bd50(0x106)),_0x21d5c4=_0x4a171d[_0x48bd50(0x103)]('2d');_0x4a171d['width']=_0x469ead[_0x48bd50(0x122)],_0x4a171d[_0x48bd50(0x116)]=_0x469ead[_0x48bd50(0x116)],_0x21d5c4[_0x48bd50(0xd4)](_0x469ead,0x0,0x0,_0x469ead[_0x48bd50(0x122)],_0x469ead[_0x48bd50(0x116)]);let _0x178108=_0x21d5c4['getImageData'](0x0,0x0,_0x469ead[_0x48bd50(0x122)],_0x469ead[_0x48bd50(0x116)]);var _0x50f71e=_0x178108[_0x48bd50(0x12c)];for(let _0x3b5544=0x0;_0x3b5544<_0x50f71e[_0x48bd50(0x10a)];_0x3b5544+=0x4){let _0x4c2ac7=_0x50f71e[_0x3b5544],_0x2cf4ed=_0x50f71e[_0x3b5544+0x1],_0xb9e726=_0x50f71e[_0x3b5544+0x2];if([_0x4c2ac7,_0x2cf4ed,_0xb9e726][_0x48bd50(0x107)](_0x560241=>_0x560241<0x100&&_0x560241>0xf5))_0x50f71e[_0x3b5544+0x3]=0x0;}_0x21d5c4[_0x48bd50(0x133)](_0x178108,0x0,0x0);var _0x43ff85=new Image();_0x43ff85[_0x48bd50(0x12a)]=function(){_0x24cfea(_0x43ff85);},_0x43ff85[_0x48bd50(0xfe)]=_0x4a171d['toDataURL']();});}async function addBadgeToTopLeftOfImage(_0x50f002,_0x521c1a=a0_0x4a7465(0xdd)){var _0x42635a=a0_0x4a7465,_0x173f5f=chrome['runtime'][_0x42635a(0x11d)](_0x521c1a),_0x3ffa2b=await urlToImage(_0x173f5f),_0x1c7a33=document[_0x42635a(0xef)](_0x42635a(0x106)),_0x25a7c0=_0x1c7a33[_0x42635a(0x103)]('2d');_0x1c7a33[_0x42635a(0x122)]=_0x50f002[_0x42635a(0x122)],_0x1c7a33[_0x42635a(0x116)]=_0x50f002[_0x42635a(0x116)]+_0x3ffa2b[_0x42635a(0x116)],_0x25a7c0['fillStyle']=_0x42635a(0xf3),_0x25a7c0[_0x42635a(0xe8)](0x0,0x0,_0x1c7a33['width'],_0x1c7a33[_0x42635a(0x116)]),_0x25a7c0[_0x42635a(0xd4)](_0x50f002,0x0,_0x3ffa2b[_0x42635a(0x116)]),_0x25a7c0[_0x42635a(0xd4)](_0x3ffa2b,0x0,0x0,_0x3ffa2b[_0x42635a(0x122)],_0x3ffa2b[_0x42635a(0x116)]);var _0x16e9b6=new Image();return _0x16e9b6[_0x42635a(0xfe)]=_0x1c7a33['toDataURL'](),_0x16e9b6;}async function addBadgeToBottomRightOfImage(_0x345d00,_0x278791='/Image_Badges/Limited-Time-Deal-Badge.jpg'){var _0x2c72b7=a0_0x4a7465,_0x197059=chrome[_0x2c72b7(0xde)][_0x2c72b7(0x11d)](_0x278791),_0x4b8984=await urlToImage(_0x197059),_0x2cd23c=document['createElement']('canvas'),_0x4d7f97=_0x2cd23c[_0x2c72b7(0x103)]('2d');_0x2cd23c[_0x2c72b7(0x122)]=_0x345d00[_0x2c72b7(0x122)],_0x2cd23c[_0x2c72b7(0x116)]=_0x345d00[_0x2c72b7(0x116)],_0x4d7f97[_0x2c72b7(0x120)]=_0x2c72b7(0xf3),_0x4d7f97[_0x2c72b7(0xe8)](0x0,0x0,_0x2cd23c['width'],_0x2cd23c[_0x2c72b7(0x116)]),_0x4d7f97['drawImage'](_0x345d00,0x0,0x0);var _0x38d793=_0x345d00[_0x2c72b7(0x122)]/2.5,_0x4c28f7=_0x4b8984[_0x2c72b7(0x116)]*(_0x38d793/_0x4b8984[_0x2c72b7(0x122)]);_0x4d7f97[_0x2c72b7(0xd4)](_0x4b8984,_0x345d00[_0x2c72b7(0x122)]-_0x38d793,_0x345d00[_0x2c72b7(0x116)]-_0x4c28f7,_0x38d793,_0x4c28f7);var _0x37c9a3=new Image();return _0x37c9a3['src']=_0x2cd23c['toDataURL'](),_0x37c9a3;}async function create_multi_image_V2(_0x7e8759,_0x3af306,_0x50ca5c,_0x3e00ae,_0x37c5d9,_0x50fdfc,_0x216dd7,_0x4f06cd,_0x3e1d73,_0x3dc4ae,_0x2a7a53=![]){var _0x3353fe=a0_0x4a7465;console['log']('Creating\x20multi\x20image\x20V2'),console['log'](_0x3353fe(0xd9),_0x7e8759);var _0x5f13e0=await urlToImage(_0x7e8759);_0x5f13e0=await resizeToImperfectSquareImage(_0x5f13e0,_0x216dd7,_0x4f06cd),_0x5f13e0=await removeWhiteSpaceFromImage(_0x5f13e0);var _0x2bb3d7=await urlToImage(_0x3af306),_0x48d5b0=await urlToImage(_0x3e00ae),_0x439742=await urlToImage(_0x37c5d9);_0x2bb3d7=await resizeToImperfectSquareImage(_0x2bb3d7,_0x216dd7*0.45,_0x4f06cd*0.45),_0x48d5b0=await resizeToImperfectSquareImage(_0x48d5b0,_0x216dd7*0.45,_0x4f06cd*0.45),_0x439742=await resizeToImperfectSquareImage(_0x439742,_0x216dd7*0.45,_0x4f06cd*0.45);var _0x40bf51=Math[_0x3353fe(0x11b)](_0x2bb3d7[_0x3353fe(0x122)],_0x48d5b0[_0x3353fe(0x122)],_0x439742[_0x3353fe(0x122)]);_0x5f13e0=await makeImageWiderWithPixels(_0x5f13e0,_0x40bf51,_0x3353fe(0xd6)),_0x5f13e0=await makeImageAspectSame(_0x5f13e0),_0x2bb3d7=await resizeToImperfectSquareImage(_0x2bb3d7,_0x216dd7*0.4,_0x4f06cd*0.4),_0x48d5b0=await resizeToImperfectSquareImage(_0x48d5b0,_0x216dd7*0.4,_0x4f06cd*0.4),_0x439742=await resizeToImperfectSquareImage(_0x439742,_0x216dd7*0.4,_0x4f06cd*0.4);var _0x13bf77=0x0;_0x5f13e0=await addImageToRight(_0x5f13e0,_0x2bb3d7,_0x13bf77),_0x13bf77+=_0x2bb3d7[_0x3353fe(0x116)]+_0x5f13e0['height']/0x19,_0x5f13e0=await addImageToRight(_0x5f13e0,_0x48d5b0,_0x13bf77),_0x13bf77+=_0x48d5b0[_0x3353fe(0x116)]+_0x5f13e0[_0x3353fe(0x116)]/0x19,_0x5f13e0=await addImageToRight(_0x5f13e0,_0x439742,_0x13bf77);var _0x34f512=await urlToImage(_0x50ca5c);_0x34f512=await resizeToImperfectSquareImage(_0x34f512,_0x5f13e0[_0x3353fe(0x116)]*0.2,_0x5f13e0[_0x3353fe(0x122)]*0.2);var _0x547650=_0x5f13e0['height'],_0x3046b4=chrome['runtime'][_0x3353fe(0x11d)](_0x3353fe(0xdd)),_0x469cf3=await urlToImage(_0x3046b4),_0x1fbdef=_0x5f13e0[_0x3353fe(0x116)]/0x5,_0x4caa51=_0x469cf3['width']*(_0x1fbdef/_0x469cf3['height']);_0x469cf3=await resizeToImperfectSquareImage(_0x469cf3,_0x4caa51,_0x1fbdef);if(_0x2a7a53){_0x5f13e0=await addBadgeToTopLeftOfImage(_0x5f13e0),_0x5f13e0=await addBadgeToBottomRightOfImage(_0x5f13e0);var _0x4565f=_0x5f13e0[_0x3353fe(0x116)],_0x5a84ca=_0x4565f-_0x547650;_0x34f512=await resizeToImperfectSquareImage(_0x34f512,_0x5a84ca,_0x5f13e0['width']*0.2),_0x5f13e0=await addImageToTopRight(_0x5f13e0,_0x34f512,0.7);}return _0x5f13e0=await upscaleToMinimumSize(_0x5f13e0,0x1f4,0x1f4),_0x5f13e0;}function addImageToLeft(_0x3361cc,_0x15116d,_0x70b49b,_0x443b7c=0x1){return new Promise(function(_0x282fa9,_0x1aa092){var _0xfb366e=a0_0x1d15;let _0x569e0d=document[_0xfb366e(0xef)](_0xfb366e(0x106));_0x569e0d[_0xfb366e(0x122)]=_0x3361cc[_0xfb366e(0x122)],_0x569e0d['height']=_0x3361cc['height'];let _0x3122ef=_0x569e0d[_0xfb366e(0x103)]('2d');_0x3122ef['fillStyle']=_0xfb366e(0xf3),_0x3122ef[_0xfb366e(0xe8)](0x0,0x0,_0x569e0d[_0xfb366e(0x122)],_0x569e0d[_0xfb366e(0x116)]),_0x3122ef['drawImage'](_0x3361cc,0x0,0x0);var _0x5ba61e=0x0,_0x1d129e=_0x70b49b;roundedImage(_0x3122ef,_0x5ba61e,_0x1d129e,_0x15116d[_0xfb366e(0x122)],_0x15116d[_0xfb366e(0x116)],0xa),_0x3122ef['globalAlpha']=_0x443b7c,_0x3122ef[_0xfb366e(0xd4)](_0x15116d,_0x5ba61e,_0x1d129e);let _0x579cf4=new Image();_0x579cf4[_0xfb366e(0xfe)]=_0x569e0d[_0xfb366e(0xf1)](),_0x282fa9(_0x579cf4);});}function addImageToRight(_0x51a54b,_0x245697,_0x17a13b){return new Promise(function(_0x446f98,_0x2425e8){var _0x1475ca=a0_0x1d15;let _0x419c70=document['createElement'](_0x1475ca(0x106));_0x419c70[_0x1475ca(0x122)]=_0x51a54b['width'],_0x419c70['height']=_0x51a54b[_0x1475ca(0x116)];let _0xfd1437=_0x419c70['getContext']('2d');_0xfd1437[_0x1475ca(0x120)]=_0x1475ca(0xf3),_0xfd1437[_0x1475ca(0xe8)](0x0,0x0,_0x419c70[_0x1475ca(0x122)],_0x419c70[_0x1475ca(0x116)]),_0xfd1437[_0x1475ca(0xd4)](_0x51a54b,0x0,0x0),_0xfd1437[_0x1475ca(0x115)]=0.9;var _0x2d7ae2=_0x419c70[_0x1475ca(0x122)]-_0x245697['width']-_0x419c70[_0x1475ca(0x122)]/0x32,_0x438281=_0x17a13b;roundedImage(_0xfd1437,_0x2d7ae2,_0x438281,_0x245697[_0x1475ca(0x122)],_0x245697['height'],0xa),_0xfd1437[_0x1475ca(0x111)](),_0xfd1437['drawImage'](_0x245697,_0x2d7ae2,_0x438281);var _0xe6d12=new Image();_0xe6d12[_0x1475ca(0x12a)]=function(){_0x446f98(_0xe6d12);},_0xe6d12['src']=_0x419c70['toDataURL']();});}function makeImageWiderWithPixels(_0x153bd7,_0x1e7cd7,_0x13e707){return new Promise(function(_0x67cd4d,_0xe4d426){var _0x285ef8=a0_0x1d15;let _0x34742d=document[_0x285ef8(0xef)]('canvas');_0x34742d['width']=_0x153bd7[_0x285ef8(0x122)]+_0x1e7cd7,_0x34742d[_0x285ef8(0x116)]=_0x153bd7[_0x285ef8(0x116)];let _0x56b178=_0x34742d[_0x285ef8(0x103)]('2d');_0x56b178['fillStyle']='white',_0x56b178[_0x285ef8(0xe8)](0x0,0x0,_0x34742d['width'],_0x34742d[_0x285ef8(0x116)]);var _0x258aa8=_0x1e7cd7;if(_0x13e707===_0x285ef8(0x137))_0x56b178[_0x285ef8(0xd4)](_0x153bd7,_0x258aa8,0x0);else _0x13e707==='right'&&_0x56b178[_0x285ef8(0xd4)](_0x153bd7,0x0,0x0);var _0x5d1599=new Image();_0x5d1599[_0x285ef8(0x12a)]=function(){_0x67cd4d(_0x5d1599);},_0x5d1599[_0x285ef8(0xfe)]=_0x34742d[_0x285ef8(0xf1)]();});}function makeImageLongerWithPixels(_0x1ea352,_0x5c7523,_0xbf74ba){return new Promise(function(_0x18bbf8,_0x5135ed){var _0xd976a5=a0_0x1d15;let _0x5570be=document[_0xd976a5(0xef)](_0xd976a5(0x106));_0x5570be[_0xd976a5(0x122)]=_0x1ea352[_0xd976a5(0x122)],_0x5570be['height']=_0x1ea352[_0xd976a5(0x116)]+_0x5c7523;let _0x70718a=_0x5570be[_0xd976a5(0x103)]('2d');_0x70718a[_0xd976a5(0x120)]=_0xd976a5(0xf3),_0x70718a['fillRect'](0x0,0x0,_0x5570be[_0xd976a5(0x122)],_0x5570be[_0xd976a5(0x116)]);var _0x518a66=_0x5c7523;if(_0xbf74ba===_0xd976a5(0xfb))_0x70718a[_0xd976a5(0xd4)](_0x1ea352,0x0,_0x518a66);else _0xbf74ba===_0xd976a5(0x101)&&_0x70718a[_0xd976a5(0xd4)](_0x1ea352,0x0,0x0);var _0x2a8a60=new Image();_0x2a8a60[_0xd976a5(0x12a)]=function(){_0x18bbf8(_0x2a8a60);},_0x2a8a60[_0xd976a5(0xfe)]=_0x5570be[_0xd976a5(0xf1)]();});}function addImageToTopRight(_0x58ea52,_0x208acb,_0x3ceba4){return new Promise(async function(_0x279ce4,_0x51c234){var _0x52f97e=a0_0x1d15;let _0x51a566=document[_0x52f97e(0xef)](_0x52f97e(0x106));_0x51a566[_0x52f97e(0x122)]=_0x58ea52[_0x52f97e(0x122)],_0x51a566[_0x52f97e(0x116)]=_0x58ea52[_0x52f97e(0x116)];let _0xc63ffe=_0x51a566[_0x52f97e(0x103)]('2d');_0xc63ffe[_0x52f97e(0x120)]=_0x52f97e(0xf3),_0xc63ffe[_0x52f97e(0xe8)](0x0,0x0,_0x51a566['width'],_0x51a566[_0x52f97e(0x116)]),_0xc63ffe[_0x52f97e(0xd4)](_0x58ea52,0x0,0x0),_0xc63ffe[_0x52f97e(0x115)]=_0x3ceba4,_0xc63ffe[_0x52f97e(0xd4)](_0x208acb,_0x51a566['width']-_0x208acb[_0x52f97e(0x122)],0x0,_0x208acb[_0x52f97e(0x122)],_0x208acb[_0x52f97e(0x116)]);var _0x476e6c=new Image();_0x476e6c['onload']=function(){_0x279ce4(_0x476e6c);},_0x476e6c['src']=_0x51a566[_0x52f97e(0xf1)]();});}function makeImageAspectSameAndMoveToRight(_0x1c9494){return new Promise(function(_0x5061aa,_0xbf182e){var _0x33bbe9=a0_0x1d15;let _0x552142=document[_0x33bbe9(0xef)](_0x33bbe9(0x106));var _0x20b14b=_0x1c9494['width'],_0x11bf81=_0x1c9494['height'];if(_0x1c9494[_0x33bbe9(0x122)]>_0x1c9494['height'])_0x552142[_0x33bbe9(0x122)]=_0x1c9494[_0x33bbe9(0x122)],_0x552142[_0x33bbe9(0x116)]=_0x1c9494[_0x33bbe9(0x116)]+(_0x1c9494[_0x33bbe9(0x122)]-_0x1c9494[_0x33bbe9(0x116)]);else{if(_0x1c9494[_0x33bbe9(0x122)]<_0x1c9494[_0x33bbe9(0x116)])_0x552142[_0x33bbe9(0x116)]=_0x1c9494[_0x33bbe9(0x116)],_0x552142['width']=_0x1c9494[_0x33bbe9(0x122)]+(_0x1c9494[_0x33bbe9(0x116)]-_0x1c9494[_0x33bbe9(0x122)]);else return _0x1c9494;}let _0x5131f5=_0x552142[_0x33bbe9(0x103)]('2d');_0x5131f5[_0x33bbe9(0x120)]=_0x33bbe9(0xf3),_0x5131f5['fillRect'](0x0,0x0,_0x552142[_0x33bbe9(0x122)],_0x552142[_0x33bbe9(0x116)]),_0x5131f5[_0x33bbe9(0xd4)](_0x1c9494,_0x552142[_0x33bbe9(0x122)]-_0x1c9494[_0x33bbe9(0x122)],_0x552142[_0x33bbe9(0x116)]-_0x1c9494[_0x33bbe9(0x116)]);var _0x2b54a9=new Image();_0x2b54a9['onload']=function(){_0x5061aa(_0x2b54a9);},_0x2b54a9[_0x33bbe9(0xfe)]=_0x552142[_0x33bbe9(0xf1)]();});}async function createLCGHeroImage(_0x5453ab,_0x130da3){var _0x1871e7=a0_0x4a7465,_0x32c274=await urlToImage(_0x5453ab);_0x32c274=await resizeToImperfectSquareImage(_0x32c274,0x5dc,0x5dc);var _0xf9471b=[];for(var _0x57c75c=0x0;_0x57c75c<_0x130da3[_0x1871e7(0x10a)];_0x57c75c++){var _0x2355c0=await urlToImage(_0x130da3[_0x57c75c]);_0x2355c0=await resizeToImperfectSquareImage(_0x2355c0,_0x32c274[_0x1871e7(0x122)]/_0x130da3[_0x1871e7(0x10a)],_0x32c274[_0x1871e7(0x116)]/_0x130da3[_0x1871e7(0x10a)]),_0xf9471b[_0x1871e7(0xea)](_0x2355c0);}var _0x109c24=_0xf9471b[0x0];for(var _0x57c75c=0x0;_0x57c75c<_0xf9471b[_0x1871e7(0x10a)];_0x57c75c++){_0xf9471b[_0x57c75c][_0x1871e7(0x122)]>_0x109c24[_0x1871e7(0x122)]&&(_0x109c24=_0xf9471b[_0x57c75c]);}_0x32c274=await makeImageLongerWithPixels(_0x32c274,_0x109c24[_0x1871e7(0x116)],'bottom');for(var _0x57c75c=0x0;_0x57c75c<_0xf9471b[_0x1871e7(0x10a)];_0x57c75c++){_0x32c274=await addImageToBottom(_0x32c274,_0xf9471b[_0x57c75c],_0x57c75c*(_0x32c274[_0x1871e7(0x122)]/_0xf9471b[_0x1871e7(0x10a)]));}return _0x32c274;}async function creating_LCG_duel_image(_0x1ddc82,_0x243810,_0x411851){var _0x33a177=a0_0x4a7465,_0x1d8189=0x5dc,_0xacb583=0x5dc;_0x1ddc82=await resizeToImperfectSquareImage(_0x1ddc82,_0x1d8189,_0xacb583),_0x1ddc82=await removeWhiteSpaceFromImage(_0x1ddc82);var _0x2d5109=await urlToImage(_0x411851);_0x2d5109=await removeWhiteSpaceFromImage(_0x2d5109),_0x2d5109=await resizeToImperfectSquareImage(_0x2d5109,_0xacb583*0.45,_0x1d8189*0.45);var _0x5dd85b=await urlToImage(_0x243810);_0x5dd85b=await resizeToImperfectSquareImage(_0x5dd85b,_0xacb583*0.45,_0x1d8189*0.45);var _0x47cf82=0xc;return _0x1ddc82=await makeImageWider(_0x1ddc82,_0x5dd85b,_0x33a177(0xd6)),_0x5dd85b=await resizeToImperfectSquareImage(_0x5dd85b,_0x1d8189*0.4,_0xacb583*0.4),_0x1ddc82=await addBottomRightImage(_0x1ddc82,_0x5dd85b,0x1,_0x47cf82),_0x2d5109=await resizeToImperfectSquareImageBelowTargetWidthAndHeight(_0x2d5109,_0x5dd85b[_0x33a177(0x122)]*0.65,_0x5dd85b['height']*0.65),_0x2d5109=await makeImageTransparent(_0x2d5109),_0x1ddc82=await addWaterMarkImageToTopRight(_0x1ddc82,_0x2d5109,0.7),_0x1ddc82=await makeImageAspectSame(_0x1ddc82),_0x1ddc82=await upscaleToMinimumSize(_0x1ddc82,0x1f4,0x1f4),_0x1ddc82;}function getRoundedImage(_0xf75b11,_0x2b7f48){return new Promise((_0x261d50,_0x9f234e)=>{var _0x12da1a=a0_0x1d15;const _0x20a901=_0xf75b11,_0x2376df=document[_0x12da1a(0xef)]('canvas');_0x2376df['width']=_0x20a901[_0x12da1a(0x122)],_0x2376df[_0x12da1a(0x116)]=_0x20a901[_0x12da1a(0x116)];const _0x385867=_0x2376df[_0x12da1a(0x103)]('2d');_0x385867['clearRect'](0x0,0x0,_0x2376df[_0x12da1a(0x122)],_0x2376df[_0x12da1a(0x116)]),_0x385867[_0x12da1a(0x112)](),_0x385867['beginPath'](),_0x385867[_0x12da1a(0x131)](_0x2b7f48,0x0),_0x385867['lineTo'](_0x2376df[_0x12da1a(0x122)]-_0x2b7f48,0x0),_0x385867[_0x12da1a(0x12f)](_0x2376df[_0x12da1a(0x122)],0x0,_0x2376df[_0x12da1a(0x122)],_0x2b7f48),_0x385867[_0x12da1a(0xfa)](_0x2376df[_0x12da1a(0x122)],_0x2376df[_0x12da1a(0x116)]-_0x2b7f48),_0x385867[_0x12da1a(0x12f)](_0x2376df['width'],_0x2376df['height'],_0x2376df['width']-_0x2b7f48,_0x2376df[_0x12da1a(0x116)]),_0x385867[_0x12da1a(0xfa)](_0x2b7f48,_0x2376df[_0x12da1a(0x116)]),_0x385867[_0x12da1a(0x12f)](0x0,_0x2376df[_0x12da1a(0x116)],0x0,_0x2376df[_0x12da1a(0x116)]-_0x2b7f48),_0x385867[_0x12da1a(0xfa)](0x0,_0x2b7f48),_0x385867[_0x12da1a(0x12f)](0x0,0x0,_0x2b7f48,0x0),_0x385867['clip'](),_0x385867[_0x12da1a(0xd4)](_0x20a901,0x0,0x0,_0x2376df['width'],_0x2376df[_0x12da1a(0x116)]),_0x385867[_0x12da1a(0xe9)]();const _0x5bf313=new Image();_0x5bf313[_0x12da1a(0x12a)]=()=>{_0x261d50(_0x5bf313);},_0x5bf313[_0x12da1a(0xfe)]=_0x2376df[_0x12da1a(0xf1)]();});}async function generateGPSRPicture(_0x1fb5ca,_0x2cef76){var _0x212e45=a0_0x4a7465,_0x5a00ac=document[_0x212e45(0xef)]('canvas');_0x5a00ac[_0x212e45(0x122)]=0x1f4,_0x5a00ac[_0x212e45(0x116)]=0x1f4;var _0x649fe5=_0x5a00ac[_0x212e45(0x103)]('2d'),_0x5e8d61=0x14;_0x649fe5[_0x212e45(0x120)]=_0x212e45(0x123),_0x649fe5[_0x212e45(0xe8)](0x0,0x0,_0x5a00ac[_0x212e45(0x122)],_0x5e8d61),_0x649fe5['fillStyle']=_0x212e45(0x118),_0x649fe5[_0x212e45(0xe8)](_0x5a00ac[_0x212e45(0x122)]-_0x5e8d61,0x0,_0x5e8d61,_0x5a00ac['height']),_0x649fe5['fillStyle']=_0x212e45(0xfc),_0x649fe5['fillRect'](0x0,_0x5a00ac[_0x212e45(0x116)]-_0x5e8d61,_0x5a00ac[_0x212e45(0x122)],_0x5e8d61),_0x649fe5[_0x212e45(0x120)]=_0x212e45(0x108),_0x649fe5[_0x212e45(0xe8)](0x0,0x0,_0x5e8d61,_0x5a00ac[_0x212e45(0x116)]);var _0x5cab09=_0x5e8d61,_0x35c280=_0x5e8d61,_0xf71e33=_0x5a00ac[_0x212e45(0x122)]-0x2*_0x5e8d61,_0x229ab2=_0x5a00ac['height']-0x2*_0x5e8d61;_0x649fe5[_0x212e45(0x120)]=_0x212e45(0xdf),_0x649fe5['fillRect'](_0x5cab09,_0x35c280,_0xf71e33,_0x229ab2);var _0x980357='Informationen\x20zur\x20Produktsicherheit';_0x649fe5[_0x212e45(0xd7)]=_0x212e45(0x11f),_0x649fe5[_0x212e45(0x120)]=_0x212e45(0xe1);var _0x3fc46b=_0x35c280+0x14,_0x35b752=_0x649fe5[_0x212e45(0x11c)](_0x980357)[_0x212e45(0x122)],_0x1de556=_0x5cab09+(_0xf71e33-_0x35b752)/0x2;_0x649fe5[_0x212e45(0xee)](_0x980357,_0x1de556,_0x3fc46b);var _0x4949f8=_0x3fc46b+0x23;_0x649fe5[_0x212e45(0xd0)]=_0x212e45(0x11e),_0x649fe5[_0x212e45(0xf5)]=0x1,_0x649fe5[_0x212e45(0x114)](),_0x649fe5[_0x212e45(0x131)](_0x5cab09+0xa,_0x4949f8),_0x649fe5[_0x212e45(0xfa)](_0x5cab09+_0xf71e33-0xa,_0x4949f8),_0x649fe5[_0x212e45(0xed)]();var _0x379ebb=_0x4949f8+0xa,_0x195f3b=_0x35c280+_0x229ab2-_0x379ebb-0xa,_0x171a65=_0x379ebb+_0x195f3b/0x2,_0x59c6aa=_0x35c280+_0x229ab2-0xa;function _0x4827ac(_0x34ab3e,_0x97d38a,_0x46ba88){var _0x474d3c=_0x212e45,_0x214140=_0x97d38a['split']('\x0a'),_0x1d4c89=[];for(var _0x2421b4=0x0;_0x2421b4<_0x214140[_0x474d3c(0x10a)];_0x2421b4++){var _0x19b434=_0x214140[_0x2421b4][_0x474d3c(0x121)]('\x20'),_0x5e4102='';for(var _0x4b3fc5=0x0;_0x4b3fc5<_0x19b434[_0x474d3c(0x10a)];_0x4b3fc5++){var _0x40e8b8=_0x5e4102+_0x19b434[_0x4b3fc5]+'\x20';_0x34ab3e[_0x474d3c(0x11c)](_0x40e8b8)[_0x474d3c(0x122)]>_0x46ba88&&_0x4b3fc5>0x0?(_0x1d4c89[_0x474d3c(0xea)](_0x5e4102[_0x474d3c(0xcf)]()),_0x5e4102=_0x19b434[_0x4b3fc5]+'\x20'):_0x5e4102=_0x40e8b8;}_0x1d4c89['push'](_0x5e4102[_0x474d3c(0xcf)]());}return _0x1d4c89;}function _0x3e5da4(_0x12cb8e,_0x195aa5,_0x5e5d05,_0x1784ca,_0x3cba87,_0x31999d,_0x1a3c15){var _0x421d86=_0x212e45,_0x5ad259=0x8;_0x12cb8e[_0x421d86(0x120)]=_0x421d86(0xe1),_0x12cb8e[_0x421d86(0xd7)]='bold\x2016px\x20Arial',_0x12cb8e['textBaseline']='top',_0x12cb8e[_0x421d86(0xee)](_0x3cba87,_0x195aa5+_0x5ad259,_0x5e5d05),_0x5e5d05+=0x14,_0x12cb8e['font']='14px\x20Arial';var _0x52523=_0x4827ac(_0x12cb8e,_0x31999d,_0x1784ca-0x2*_0x5ad259),_0x1ff361=0x10;for(var _0x4034ce=0x0;_0x4034ce<_0x52523['length'];_0x4034ce++){if(_0x5e5d05+_0x1ff361>_0x1a3c15)break;_0x12cb8e[_0x421d86(0xee)](_0x52523[_0x4034ce],_0x195aa5+_0x5ad259,_0x5e5d05),_0x5e5d05+=_0x1ff361;}return _0x5e5d05;}var _0x48eba6=_0x3e5da4(_0x649fe5,_0x5cab09,_0x379ebb,_0xf71e33,_0x212e45(0x128),_0x1fb5ca,_0x171a65);return _0x48eba6+=0xa,_0x3e5da4(_0x649fe5,_0x5cab09,_0x48eba6,_0xf71e33,_0x212e45(0xf7),_0x2cef76,_0x59c6aa),_0x649fe5[_0x212e45(0xd0)]='#eeeeee',_0x649fe5[_0x212e45(0xf5)]=0x2,_0x649fe5[_0x212e45(0x135)](_0x5cab09,_0x35c280,_0xf71e33,_0x229ab2),new Promise(function(_0x5ec8f0){var _0x368e84=_0x212e45,_0x242e8f=new Image();_0x242e8f[_0x368e84(0xfe)]=_0x5a00ac[_0x368e84(0xf1)](_0x368e84(0xd3)),_0x242e8f['onload']=function(){_0x5ec8f0(_0x242e8f);};});}