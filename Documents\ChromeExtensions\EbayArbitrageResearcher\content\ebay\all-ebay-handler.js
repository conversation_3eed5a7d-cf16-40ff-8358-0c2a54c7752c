/**
 * ✅ ALL EBAY HANDLER - Universal eBay page handler
 * Based on working EbayLister4 reference extension pattern
 * Loads on ALL eBay pages at document_start for maximum compatibility
 */

(function() {
  'use strict';
  
  console.log('🌐 All eBay Handler loaded - Universal eBay page support');
  
  // ✅ CRITICAL: Early message listener registration (like reference extension)
  // This MUST be at document_start to catch all messages
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 All eBay Handler received message:', message);
    
    try {
      // Handle ping requests immediately
      if (message.action === 'ping') {
        console.log('🏓 All eBay Handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'all-ebay-handler',
          url: window.location.href,
          readyState: document.readyState
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ All eBay Handler confirming content script ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'all-ebay-handler',
          dependencies: {
            eventBus: typeof window.ArbitrageEventBus !== 'undefined',
            errorHandler: typeof window.ArbitrageErrorHandler !== 'undefined'
          }
        });
        return true;
      }
      
      // For other messages, check if enhanced integration should handle them
      if (window.enhancedIntegration && typeof window.enhancedIntegration.handleMessage === 'function') {
        console.log('🔄 Forwarding message to enhanced integration');
        return window.enhancedIntegration.handleMessage(message, sender, sendResponse);
      }

      // ✅ CRITICAL FIX: Wait for enhanced integration if this is a search page
      if (isSearchPage() && (message.action === 'startScraping' || message.action === 'testSelectors' || message.action === 'minimalTest')) {
        console.log('⏳ Waiting for enhanced integration to load for search page message:', message.action);
        waitForEnhancedIntegration(message, sender, sendResponse);
        return true; // Keep message channel open
      }

      // ✅ NEW: For any other message that might need enhanced integration, wait for it
      if (isSearchPage() && !window.enhancedIntegration) {
        console.log('⏳ Search page detected but enhanced integration not loaded yet, waiting...', message.action);
        waitForEnhancedIntegration(message, sender, sendResponse);
        return true; // Keep message channel open
      }

      // Default response for unhandled messages
      console.log('❓ All eBay Handler - message not handled:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by all-ebay-handler',
        action: message.action,
        url: window.location.href,
        enhancedIntegrationAvailable: !!window.enhancedIntegration,
        isSearchPage: isSearchPage()
      });
      
    } catch (error) {
      console.error('❌ All eBay Handler message error:', error);
      sendResponse({
        success: false,
        error: 'All eBay Handler crashed: ' + error.message
      });
    }
    
    return true; // Keep message channel open
  });
  
  // ✅ CRITICAL: Page load stability detection (like reference extension)
  let pageStable = false;
  let stabilityTimer = null;
  
  function checkPageStability() {
    if (stabilityTimer) {
      clearTimeout(stabilityTimer);
    }
    
    stabilityTimer = setTimeout(() => {
      if (!pageStable) {
        pageStable = true;
        console.log('✅ All eBay Handler - Page stable and ready');
        
        // Notify that page is stable
        if (window.ArbitrageEventBus) {
          window.ArbitrageEventBus.emit('pageStable', {
            url: window.location.href,
            timestamp: Date.now()
          });
        }
      }
    }, 1000);
  }
  
  // Monitor page changes
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkPageStability);
  } else {
    checkPageStability();
  }
  
  window.addEventListener('load', checkPageStability);
  
  // ✅ CRITICAL: Mutation observer for dynamic content (like reference extension)
  const observer = new MutationObserver((mutations) => {
    checkPageStability();
  });
  
  // Start observing when DOM is ready
  if (document.body) {
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    });
  }
  
  // ✅ CRITICAL: Global status tracking
  window.allEbayHandlerReady = true;
  window.allEbayHandlerTimestamp = Date.now();
  
  console.log('✅ All eBay Handler initialization complete');

  // Send ready notification to service worker
  chrome.runtime.sendMessage({
    action: 'contentScriptReady',
    handler: 'all-ebay-handler',
    url: window.location.href,
    timestamp: Date.now()
  }).catch(error => {
    console.log('📡 Service worker not ready yet, will retry later');
  });

  // ✅ CRITICAL: Helper functions for message handling

  /**
   * Check if current page is an eBay search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }

  /**
   * Wait for enhanced integration to load and then forward message
   */
  function waitForEnhancedIntegration(message, sender, sendResponse, maxWaitMs = 15000) {
    const startTime = Date.now();
    let attempts = 0;
    const maxAttempts = 150; // 15 seconds / 100ms = 150 attempts

    const checkIntegration = () => {
      attempts++;

      // Check if enhanced integration is available and initialized
      if (window.enhancedIntegration &&
          typeof window.enhancedIntegration.handleMessage === 'function' &&
          window.enhancedIntegration.isInitialized) {
        console.log(`✅ Enhanced integration ready after ${attempts} attempts, forwarding message:`, message.action);
        return window.enhancedIntegration.handleMessage(message, sender, sendResponse);
      }

      // Check if enhanced integration exists but isn't initialized yet
      if (window.enhancedIntegration && !window.enhancedIntegration.isInitialized) {
        console.log(`⏳ Enhanced integration exists but not initialized yet (attempt ${attempts}/${maxAttempts})`);
      }

      if (Date.now() - startTime > maxWaitMs || attempts >= maxAttempts) {
        console.error(`❌ Timeout waiting for enhanced integration after ${attempts} attempts`);
        sendResponse({
          success: false,
          error: 'Enhanced integration failed to load within timeout',
          action: message.action,
          waitedMs: Date.now() - startTime,
          attempts: attempts,
          enhancedIntegrationExists: !!window.enhancedIntegration,
          enhancedIntegrationInitialized: window.enhancedIntegration?.isInitialized || false
        });
        return;
      }

      // Check again in 100ms
      setTimeout(checkIntegration, 100);
    };

    checkIntegration();
  }

})();
