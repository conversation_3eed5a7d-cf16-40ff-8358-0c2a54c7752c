<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" />
  <style type="text/css">
    img {
      max-width: 100%;
    }

    #brdr {
      background: #fff;
    }

    .description {
      color: #111 !important;
      font-size: 14px !important;
    }

    .total_body {
      background: #fff;
      border: 3px solid #ccc;
      display: block;
      margin: 0 auto;
      overflow: hidden;
      width: 950px;
      color: #111;
      border-radius: 15px;
    }

    .descriptions,
    .tabs {
      width: 100%;
      display: block;
      overflow: hidden;
    }

    .descriptions {
      font-family: Arial;
      font-weight: 400;
      margin-top: 25px;
    }

    .title {
      background-color: #fff !important;
      color: #058cd3 !important;
      font-size: 35px !important;
      font-weight: 700 !important;
      padding-bottom: 20px;
      padding-top: 20px;
      text-align: center !important;
    }

    .tabs span {
      padding: 0 33px;
    }

    .footerss {
      font-size: xx-large;
    }

    .images,
    .images img {
      max-width: 100%;
    }

    .description li {
      padding-bottom: 10px;
    }

    .title h1 {
      font-weight: bold !important;
    }

    .tabs {
      line-height: 25px;
    }

    #delivery_right>img {
      width: 100%;
    }

    button.accordion {
      background-color: #058cd3;
      border: medium none;
      color: #fff;
      cursor: pointer;
      font-size: 26px;
      font-weight: bold;
      outline: medium none;
      padding: 3px 21px;
      text-align: center;
      transition: all 0.4s ease 0s;
      width: 100%;
    }

    .panel {
      box-shadow: none;
      margin-bottom: 0;
    }

    ul li {
      padding-bottom: 5px;
    }

    ul {
      padding-top: 15px;
    }

    button.accordion.active,
    button.accordion1:hover {
      background-color: #ddd;
      color: #058cd3;
    }

    div.panel {
      padding: 0 18px;
      background-color: white;
      overflow: hidden;
      transition: max-height 0.2s ease-out;
    }

    @media only screen and (max-width: 767px) {
      .total_body {
        width: 320px;
      }

      .tabs span {
        font-size: 14px;
        line-height: 20px;
        padding: 5px 2px;
      }

      .title {
        font-size: 19px;
        margin: 0;
      }

      .footerss {
        font-size: 15px;
      }

      ul.slides {
        height: auto;
      }


    }


    ul.slides {
      display: block;
      position: relative;
      height: 600px;
      margin: 0;
      padding: 0;
      overflow: hidden;
      list-style: none;
    }

    .slides * {
      user-select: none;
      -ms-user-select: none;
      -moz-user-select: none;
      -khtml-user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
    }

    ul.slides input {
      display: none;
    }


    .slide-container {
      display: block;
    }

    .slide-image {
      display: block;
      position: absolute;

      top: 0;
      opacity: 0;
      transition: all .7s ease-in-out;
    }

    .slide-image img {
      width: 100%;
      height: auto;
    }

    .carousel-controls {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 999;
      font-size: 100px;
      line-height: 600px;
      color: #333;
    }

    .carousel-controls label {
      display: none;
      position: absolute;
      padding: 0 20px;
      opacity: 0;
      transition: opacity .2s;
      cursor: pointer;
    }

    .slide-image:hover+.carousel-controls label {
      opacity: 0.5;
    }

    .carousel-controls label:hover {
      opacity: 0.8;
    }

    .carousel-controls .prev-slide {
      width: auto;
      height: auto;
      text-align: left;
      left: 0;
    }

    .carousel-controls .next-slide {
      width: auto;
      height: auto;
      text-align: right;
      right: 0;
    }



    input:checked+.slide-container .slide-image {
      opacity: 1;
      transform: scale(1);
      transition: opacity 1s ease-in-out;
    }

    input:checked+.slide-container .carousel-controls label {
      display: block;
      height: auto;
      width: auto;
    }

    .images, #carouselContainer {
  min-height: 600px;
  }

  </style>
</head>

<body>
  <div class="total_body">
    <div class="col-lg-12 title" id="brdr">
      <h1>{{Product_Title}}</h1>
    </div>
    <div class="col-lg-12 descriptions">
      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col_left" id="bo">
        <div class="images">
          <div id="carouselContainer">{{Product_Carousel}}</div>
        </div>
      </div>
      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6" id="rght">
        <div class="description">{{Product_Description}}</div>
      </div>
    </div>
    <button class="accordion">{{Shipping_Header}}</button>
    <div class="panel" style="max-height: 478px">
      <div class="col-xs-12 col-sm-9 col-md-9 col-lg-9">
        <ul>
          {{Shipping_Info}}
        </ul>
      </div>
      <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
        <div id="delivery_right">
          <br />
          <img src="{{Shipping_Image_URL}}" alt="" />
        </div>
      </div>
    </div>
    <button class="accordion">{{Return_Header}}</button>
    <div class="panel">
      <div class="col-xs-12 col-sm-9 col-md-9 col-lg-9">
        <ul>
          {{Return_Info}}
        </ul>
      </div>
      <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
        <div id="delivery_right">
          <img src="{{Return_Image_URL}}" alt="" />
        </div>
      </div>
    </div>
    <button class="accordion">{{Payment_Header}}</button>
    <div class="panel">
      <div class="col-xs-12 col-sm-9 col-md-9 col-lg-9">
        <p>{{Payment_Info}}</p>
      </div>
      <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
        <div id="delivery_right" style="text-align: center">
          <img style="width: 80%; text-align: center" src="{{Payment_Image_URL}}" alt="" />
        </div>
      </div>
    </div>
    <button class="accordion">{{Feedback_Header}}</button>
    <div class="panel">
      <div class="col-xs-12 col-sm-9 col-md-9 col-lg-9">
        <p>{{Feedback_Info}}</p>
      </div>
      <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
        <div id="delivery_right">
          <br />
          <br />
          <img src="{{Feedback_Image_URL}}" alt="" />
        </div>
      </div>
    </div>
    <button class="accordion">{{Contact_Us_Header}}</button>
    <div class="panel">
      <div class="col-xs-12 col-sm-9 col-md-9 col-lg-9">
        <br />
        <p>{{Contact_Info}}</p>
        <br />
        <br />
      </div>
      <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3"></div>
    </div>
    <div class="description" style="text-align: center"></div>
    <div class="col-lg-12 title" id="brdr">
      <p><span class="footerss" data-label="{{SKU}}">{{Store_Thank_You_Message}}</span></p>
    </div>
  </div>
</body>