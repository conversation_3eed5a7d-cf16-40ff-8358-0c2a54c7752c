@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(4px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* --- Order container --- */
  .order-details {
    margin-top: 4px;
    font-family: 'Segoe UI', Tahoma, sans-serif;
    font-size: 12.5px;
    line-height: 1.45;
    color: #00acf0;
    animation: fadeInUp 0.3s ease-out;
  }
  
  /* --- Status icon --- */
  .order-status-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 6px;
    position: relative;
  }
  
  /* --- Loading Spinner --- */
  .order-status-icon.loading::after {
    content: '';
    box-sizing: border-box;
    border: 2px solid #ccc;
    border-top: 2px solid #00acf0;
    border-radius: 50%;
    width: 13px;
    height: 13px;
    display: inline-block;
    animation: spin 1s linear infinite;
  }
  
  /* --- Success Checkmark --- */
  .order-status-icon.success::after {
    content: '✔';
    color: #2ecc71;
    font-weight: 600;
    font-size: 13px;
  }
  
  /* --- Error Cross --- */
  .order-status-icon.error::after {
    content: '✘';
    color: #e74c3c;
    font-weight: 600;
    font-size: 13px;
  }
  
  /* --- Each address line --- */
  .order-line {
    margin-left: 22px;
    margin-bottom: 1px;
  }