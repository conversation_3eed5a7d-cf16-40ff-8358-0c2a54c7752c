<!DOCTYPE html>
<html lang="en">

<head>
  <title>Ebay Inserting Extension</title>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="popup.css">
  <style>
    #logo {
      max-width: 128px;
      max-height: 128px;
      width: auto;
      height: auto;
    }
  </style>
</head>

<body>
  <div id="login_status" style="display: block;">
    <div id="header">
      <img id="logo" src="logo.png" alt="Your Company Logo">
    </div>
    <div id="status_container">
      <span id="status_text">Not logged in</span>
      <span id="logout_icon" style="display: none;"><i class="fas fa-cog"></i></span>
      <div id="user_info">
        <span id="email_text"></span>
        <div id="user_details">
          <span id="subscription_text" style="display: none;">Free tier</span>
          <span id="credits_text" style="display: none;">Credits: 10</span>
        </div>
      </div>
    </div>
    <div id="login_container">
      <h2>Login</h2>
      <form>
        <label for="username">Username:</label>
        <input type="text" id="username" name="username"><br><br>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password"><br><br>
        <button type="submit" id="login_button">Login</button>
      </form>
    </div>
  </div>



  <div id="user_features_containers" style="display: none;">
    <h2>User Features</h2>



    <div class="save_features">
      <input id="watermark_url" type="text" placeholder="Enter Image URL">
      <button id="save_image_to_local_storage">Save</button>
    </div>

    <br>
    <div class="watermark-container">
      <div class="watermark">
        <img id="image_from_local_storage" src="" width="200" height="200">
        <div class="text">Watermark</div>
      </div>
    </div>

    <div class="price-container">
      <label for="markup-price">Markup Percentage:</label>
      <input type="number" id="markup-price" min="0" step="0.01">
    </div>

    <div class="price-container">
      <label for="end-price">End Price:</label>
      <input type="number" id="end-price" min="0" max="0.99" step="0.01" value="0.99">
    </div>

    <br>
    <br>

    <label class="switch" style="display: none;">
      Auto Enable AI Title Creations:
      <input type="checkbox" id="auto_ai_title_checkbox" checked>
      <span class="slider round"></span>
    </label>

    <label for="domain-select">Select Domain:</label>
    <select id="domain-select">
      <option value="">None</option>
      <option value="com">USA</option>
      <option value="ca">CA</option>
      <option value="co.uk">UK</option>
      <option value="com.au">AU</option>
      <option value="de">DE</option>
      <option value="fr">FR</option>
      <option value="it">IT</option>
      <option value="es">ES</option>
    </select>


    <div>
      <div>
        <span>Enable Vero Protection</span>
        <label class="switch">
          <input type="checkbox" id="vero_protection_switch" checked>
          <span class="slider round"></span>
        </label>
      </div>

      <div>
        <span>Auto Submit Listing</span>
        <label class="switch">
          <input type="checkbox" id="auto_submit_switch" checked>
          <span class="slider round"></span>
        </label>
      </div>

      <br>


      <div id="veroBrandContainer" class="container">
        <button id="getVeroBrandsButton" class="tasteful-button">Get Vero Brands</button>
        <div id="spinner" class="hidden loader-container"></div>
        <span id="veroBrandStatus" class="status"></span>
        <div id="statusIcon" class="hidden icon"></div>
        <span id="fetchedCount" class="counter">Total Brands: 0</span>
      </div>


    </div>





    <div id="bulk-list" style="display:none;">
      <h2>Bulk List Feature</h2>
      <p>Enter Amazon product links below, separated by new lines:</p>
      <textarea id="amazon-links"></textarea>
      <button id="list-btn">List on eBay</button>
      <div id="pause_resume_buttons">
        <button id="pause_button">Pause</button>
        <button id="resume_button" disabled>Resume</button>
      </div>
      <div id="position-container">
        <span id="position-label">Position:</span>
        <input id="position-input" type="number" min="0" value="0">
        /<span id="total-links-label"></span>
        <button id="update-position-btn">Update</button>
        <button id="status-report">Status Report</button>
      </div>
      <div>
        <label for="thread-count">Thread Count:</label>
        <select id="thread-count" name="thread-count">
          <option value="1" selected>1</option>
          <option value="2">2</option>
          <option value="3">3</option>
          <option value="4">4</option>
          <option value="5">5</option>
          <option value="6">6</option>
          <option value="7">7</option>
          <option value="8">8</option>
          <option value="9">9</option>
          <option value="10">10</option>
          <option value="11">11</option>
          <option value="12">12</option>
          <option value="13">13</option>
          <option value="14">14</option>
          <option value="15">15</option>
          <option value="16">16</option>
          <option value="17">17</option>
          <option value="18">18</option>
          <option value="19">19</option>
          <option value="20">20</option>
          <option value="21">21</option>
          <option value="22">22</option>
          <option value="23">23</option>
          <option value="24">24</option>
          <option value="25">25</option>
          <option value="26">26</option>
          <option value="27">27</option>
          <option value="28">28</option>
          <option value="29">29</option>
          <option value="30">30</option>

        </select>
      </div>

      <br>

      <!-- <div>
        <label for="max-price">Max Price (AutoList):</label>
        <input type="number" id="max-price" min="0" step="1" value="50">
      </div>

      <div>
        <label for="min-price">Min Price (AutoList):</label>
        <input type="number" id="min-price" min="0" step="1" value="0">
      </div> -->





      <br>


      <div>
        <p>Enter Amazon search querys below, separated by new lines:</p>
        <textarea id="amazon-search-querys"></textarea>
        <button id="search-amazon-querys-btn">Search Titles</button>
        <button id="search-amazon-keywords-btn">Search Keywords</button>
        <div id="amazon-search-position-container">
          <label for="amazon-search-position-input">Position:</label><br>
          <input id="amazon-search-position-input" type="number" min="0" value="0">
          /<span id="amazon-search-total-querys-label"></span>
        </div>

        <div>
          <label for="total-asins-to-fetch-from-search">Total Asins to Grab per Search:</label>
          <input type="number" id="total-asins-to-fetch-from-search" min="1" step="1" value="2">
        </div>


      </div>
    </div>


    <div id="ultimate-features" style="display: none;">
      <h2>Ultimate Features</h2>

      <div id="ultimate-buttons">

        <button id="open_duplicate_checker">Open Duplicate Checker</button>
        <button id="research_seo" style="display: none;">Research SEO</button>
        <button id="open_product_finder_button">Open Product Hunter</button>
        <button id="open_bulk_lister">Open Bulk Lister</button>

        <button id="open_image_template">Open Image Template</button>
        <button id="open_advanced_title_builder" style="display: none;">Open Advanced Title Builder</button>
        <button id="open_tracker" style="display: block;">Open Tracker</button>
        <button id="open_competitor_research">Open Competitor Research</button>
        <button id="open_quick_chat_settings" class="popup-button" style="display: none;">Quick Chat Settings</button>
        <button id="open_boost_my_listings" class="popup-button" style="display: block;">Boost My Listings</button>

        <button id="open_sniper_list" class="popup-button" style="display: none;">Sniper List</button>

        <button id="open_stock_monitor" class="popup-button" style="display: none;">Stock Monitor/Price Tracker</button>

        <!-- <button id="open_description_builder">Open Description Builder</button> -->
      </div>
      <br>

      <div id="image_template_container_options">

        <!-- <span>Use Default Image Template</span>
        <label class="switch">
          <input type="checkbox" id="use_default_image_template_switch">
          <span class="slider round"></span>
        </label>
        <br> -->


        <span>Use Image Template</span>
        <label class="switch">
          <input type="checkbox" id="enable_image_template_switch">
          <span class="slider round"></span>
        </label>

        <br>

        <span>Use Review Images</span>
        <label class="switch">
          <input type="checkbox" id="use_review_images_switch">
          <span class="slider round"></span>
        </label>

      </div>


      <br>

      <br>
      <span>Activate Snipe Mode</span>
      <label class="switch">
        <input type="checkbox" id="snipe_mode_switch">
        <span class="slider round"></span>
      </label>

      <br><br>

      <!-- //use simple description  -->
      <span>Use Simple Description</span>
      <label class="switch">
        <input type="checkbox" id="use_simple_description_switch">
        <span class="slider round"></span>
      </label>

      <br><br>


      <div>
        <label for="min-price">Min Price (AutoList):</label>
        <input type="number" id="min-price" min="0" step="1" value="0">
      </div>


      <div>
        <label for="max-price">Max Price (AutoList):</label>
        <input type="number" id="max-price" min="0" step="1" value="50">
      </div>

      <div>
        <label for="sniper-price-markdown">Sniper Price Markdown (cents):</label>
        <input type="number" id="sniper-price-markdown" min="0" step="0.01" value="0">
      </div>

      <div>
        <label for="promoted_listing_ad_rate">Promoted Listing Ad Rate:</label>
        <input type="number" id="promoted_listing_ad_rate" min="2" max="99" step="0.1" value="2.1">
      </div>

      <div>
        <label for="schedule_listing_time">Schedule Listing Time:</label>
        <select id="schedule_listing_time">
          <option value="false">Disabled</option>
          <option value="0">12 AM</option>
          <option value="1">1 AM</option>
          <option value="2">2 AM</option>
          <option value="3">3 AM</option>
          <option value="4">4 AM</option>
          <option value="5">5 AM</option>
          <option value="6">6 AM</option>
          <option value="7">7 AM</option>
          <option value="8">8 AM</option>
          <option value="9">9 AM</option>
          <option value="10">10 AM</option>
          <option value="11">11 AM</option>
          <option value="12">12 PM</option>
          <option value="13">1 PM</option>
          <option value="14">2 PM</option>
          <option value="15">3 PM</option>
          <option value="16">4 PM</option>
          <option value="17">5 PM</option>
          <option value="18">6 PM</option>
          <option value="19">7 PM</option>
          <option value="20">8 PM</option>
          <option value="21">9 PM</option>
          <option value="22">10 PM</option>
          <option value="23">11 PM</option>
        </select>
      </div>
      
      




      <div>
        <h1>Listing Settings (Disabling item specifics, make the lister Faster)</h1>
        <div>Fill Required Item Specifics with AI</div>
        <label class="switch">
          <input type="checkbox" id="fill_required_item_specifics_switch" checked>
          <span class="slider round"></span>
        </label>


        <div>Fill Optional Item Specifics with AI</div>
        <label class="switch">
          <input type="checkbox" id="fill_optional_item_specifics_switch" checked>
          <span class="slider round"></span>
        </label>
      </div>

      <div>
        <span>Hide Personal Information</span>
        <label class="switch">
          <input type="checkbox" id="hide_personal_information_switch">
          <span class="slider round"></span>
        </label>
      </div>



      <div id="agent_name_container">
        <label for="agent_name">Agent Working:</label>
        <input type="text" id="agent_name" value="Me">
      </div>


      <div>
        <h2>Virtual Clipboard</h2>
        <div class="form-group">
          <label for="virtual_clipboard_1_key">Enter Virtual Clipboard #1 Key:</label>
          <textarea id="virtual_clipboard_1_key" class="textarea" placeholder="Paste your key here"></textarea>
        </div>

        <div class="form-group">
          <label for="virtual_clipboard_2_key">Enter Virtual Clipboard #2 Key:</label>
          <textarea id="virtual_clipboard_2_key" class="textarea" placeholder="Paste your key here"></textarea>
        </div>

      </div>





    </div>

    <div id="dev_tools" style="display: none;">
      //dev dev_tools
      <h2>Dev Tools</h2>
      //button ask chatgpt
      <button id="ask_chatgpt">Ask ChatGPT</button>
      //text
      <input id="chatgpt_input" type="text" placeholder="Enter Text" value="say hello">

      //button for test mode Ebay
      <button id="toggle_test_mode_ebay">Test Mode Ebay</button>

      //toggle chatGpt Test Mode
      <button id="toggle_chatgpt_test_mode">ChatGPT Test Mode</button>



      <h2>Duplicate Checker</h2>



      <div id="duplicate-skus-container">
        <form id="main_form">
          <label>
            SKU SAVED:
            <h4><span id="skuFound">SkuFound: 0</span></h4>

            <h6>Enter page to begin grabbing skus</h6>
            <input type="text" id="page_number" value="1" placeholder="Page Number">
          </label>
        </form>
        <br>
        <button id="main_button">Get Active SKU List</button>
        <button id="save_sku_on_page">Save SKU on Page</button>
        <button id="delete_skus">Delete SKUs</button>

        <br>
        <br>

        <form id="import_form">
          <label>
            Import SKUs from file:
            <input type="file" id="import_file" accept=".txt">
          </label>
          <button type="submit" id="import_button">Import</button>
        </form>
        <br>
        <form id="export_form">
          <button type="submit" id="export_button">Export SKU List</button>
        </form>
      </div>


      <div id="api_key_container" style="display: none;">
        <input id="api_key" type="text" placeholder="Enter API Key">
        <button id="save_api_key">Save API Key</button>
        <br>
        <br>
        API Key: <span id="api_key_display"></span>
        <br>
        <br>
      </div>



      //apply amazon affilaite tag check box
      <div>
        <span>Apply Amazon Affiliate Tag</span>
        <label class="switch">
          <input type="checkbox" id="apply_amazon_affiliate_tag_switch">
          <span class="slider round"></span>
        </label>



      </div>

      <div>

        //test identify Listing
        <button id="test_identify_listing">Test Identify Listing</button>


      </div>



      <script src="/libraries/firebase-app.js"></script>
      <script src="/libraries/firebase-auth.js"></script>
      <script src="/libraries/firebase-main.js"></script>
      <script src="/libraries/firebase-database.js"></script>

      <script src="/content/amazon/amazon_check_duplicate_functions.js"></script>

      <script src="/libraries/amazon_lib.js"></script>
      <script src="/libraries/chrome_storage_utils.js"></script>
      <script src="/libraries/csv_utils.js"></script>
      <script src="/libraries/chrome_storage_utils.js"></script>
      <script src="/libraries/chat_gpt_web.js"></script>



      <script src="login.js"></script>
      <script src="popup.js"></script>


</body>

</html>