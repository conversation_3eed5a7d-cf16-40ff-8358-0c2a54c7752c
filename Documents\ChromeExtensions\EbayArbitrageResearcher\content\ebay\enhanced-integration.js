/**
 * Enhanced eBay Integration - Connects all Phase 1 components
 * Provides a unified interface for the enhanced arbitrage scanning
 */
class EnhancedEbayIntegration {
  constructor() {
    console.log('🏗️ Enhanced Integration constructor called');
    // Don't access dependencies immediately - they may not be loaded yet
    this.eventBus = null;
    this.scanner = null;
    this.profitCalculator = null;
    this.errorHandler = null;

    this.isInitialized = false;
    this.initializationAttempts = 0;
    this.maxInitAttempts = 100; // Reduced since we have better dependency loading
    this.initDelay = 200; // Increased delay for defensive loading
    this.initializationGaveUp = false;
    this.currentScan = null;
    this.results = [];

    this.debug = true;

    // ✅ CRITICAL FIX: Bind all methods to maintain 'this' context
    this.initialize = this.initialize.bind(this);
    this.scanCurrentPage = this.scanCurrentPage.bind(this);
    this.initializeDependencies = this.initializeDependencies.bind(this);
    this.validateDependencies = this.validateDependencies.bind(this);
    this.startInitializationSequence = this.startInitializationSequence.bind(this);

    // Don't call init() immediately - use dependency checking
    this.startInitializationSequence();
  }

  /**
   * Start the initialization sequence with dependency checking
   */
  async startInitializationSequence() {
    console.log('🔄 Starting enhanced integration initialization sequence...');
    await this.attemptInitialization();
  }

  /**
   * Check if all required dependencies are loaded
   */
  checkDependencies() {
    const dependencies = {
      eventBus: 'ArbitrageEventBus',
      errorHandler: 'ArbitrageErrorHandler',
      rateLimiter: 'ArbitrageRateLimiter',
      profitCalculator: 'ArbitrageProfitCalculator',
      scanner: 'EbaySearchScanner'
    };

    const missing = [];
    const available = {};

    for (const [key, globalName] of Object.entries(dependencies)) {
      if (typeof window[globalName] === 'undefined') {
        missing.push(globalName);
      } else {
        available[key] = window[globalName];
      }
    }

    // Check DOM readiness
    if (document.readyState !== 'complete' && document.readyState !== 'interactive') {
      missing.push('DOM_READY');
    }

    if (missing.length > 0) {
      console.log(`⏳ Waiting for dependencies: ${missing.join(', ')}`);
      return { ready: false, missing, available };
    }

    console.log('✅ All dependencies available:', Object.keys(available));
    return { ready: true, missing: [], available };
  }

  /**
   * Attempt initialization with dependency checking
   */
  async attemptInitialization() {
    this.initializationAttempts++;

    if (this.debug) {
      console.log(`🔍 Initialization attempt ${this.initializationAttempts}/${this.maxInitAttempts}`);
    }

    const depCheck = this.checkDependencies();

    if (depCheck.ready) {
      try {
        // ✅ CRITICAL FIX: Instantiate ALL classes with 'new' keyword
        this.eventBus = new depCheck.available.eventBus();
        this.scanner = new depCheck.available.scanner();
        this.profitCalculator = new depCheck.available.profitCalculator();
        this.errorHandler = new depCheck.available.errorHandler();

        // ✅ CRITICAL FIX: Validate all dependency methods
        this.validateDependencies();

        console.log('✅ All dependencies instantiated and verified successfully');

        console.log('🚀 Enhanced integration initializing with all dependencies ready...');

        // Perform actual initialization
        await this.performInitialization();

        console.log('✅ Enhanced integration initialized successfully');
        return true;

      } catch (error) {
        console.error('❌ Enhanced integration initialization failed:', error);

        // Try again if we haven't exceeded max attempts
        if (this.initializationAttempts < this.maxInitAttempts) {
          setTimeout(async () => await this.attemptInitialization(), this.initDelay * 2);
        } else {
          console.error('❌ Enhanced integration initialization failed after maximum attempts');
        }
        return false;
      }
    }

    // Dependencies not ready, try again
    if (this.initializationAttempts < this.maxInitAttempts) {
      setTimeout(async () => await this.attemptInitialization(), this.initDelay);
    } else {
      console.error(`❌ Enhanced integration initialization timeout after ${this.maxInitAttempts} attempts`);
      console.error('Missing dependencies:', depCheck.missing);
      this.initializationGaveUp = true;

      // Set up a periodic check to restart initialization if dependencies become available
      this.setupDependencyWatcher();
    }

    return false;
  }

  /**
   * Set up a watcher to restart initialization when dependencies become available
   */
  setupDependencyWatcher() {
    if (this.dependencyWatcher) return; // Already set up

    console.log('🔍 Setting up dependency watcher for late-loading scripts...');

    this.dependencyWatcher = setInterval(() => {
      if (this.isInitialized) {
        clearInterval(this.dependencyWatcher);
        return;
      }

      const depCheck = this.checkDependencies();
      if (depCheck.ready) {
        console.log('🎉 Dependencies now available! Restarting initialization...');
        clearInterval(this.dependencyWatcher);

        // Reset state and restart initialization
        this.initializationAttempts = 0;
        this.initializationGaveUp = false;
        this.attemptInitialization();
      }
    }, 2000); // Check every 2 seconds
  }

  /**
   * ✅ CRITICAL FIX: Validate that all dependencies have required methods
   */
  validateDependencies() {
    const validations = [
      ['errorHandler.handleError', typeof this.errorHandler?.handleError === 'function'],
      ['scanner.scanCurrentPage', typeof this.scanner?.scanCurrentPage === 'function'],
      ['eventBus.emit', typeof this.eventBus?.emit === 'function'],
      ['profitCalculator.calculateProfit', typeof this.profitCalculator?.calculateProfit === 'function']
    ];

    for (const [name, isValid] of validations) {
      if (!isValid) {
        throw new Error(`${name} is not available or not a function`);
      }
    }

    console.log('✅ All dependency methods validated successfully');
    return true;
  }

  /**
   * ✅ CRITICAL FIX: Public method to initialize dependencies (called by service worker)
   */
  async initializeDependencies() {
    try {
      // Force a fresh initialization attempt
      this.isInitialized = false;
      this.initializationGaveUp = false;
      this.initializationAttempts = 0;

      // Start the initialization sequence
      return await new Promise((resolve, reject) => {
        const checkInit = () => {
          if (this.isInitialized) {
            resolve(true);
          } else if (this.initializationGaveUp || this.initializationAttempts >= this.maxInitAttempts) {
            reject(new Error('Enhanced integration failed to initialize after restart attempts'));
          } else {
            setTimeout(checkInit, 100);
          }
        };

        // Start initialization
        this.attemptInitialization();
        checkInit();
      });

    } catch (error) {
      console.error('❌ initializeDependencies failed:', error);
      throw error;
    }
  }

  /**
   * Perform the actual initialization once dependencies are ready
   */
  async performInitialization() {
    if (this.isInitialized) return;

    // Clear dependency watcher if it exists
    if (this.dependencyWatcher) {
      clearInterval(this.dependencyWatcher);
      this.dependencyWatcher = null;
    }

    // Set up event listeners
    this.setupEventListeners();

    // DON'T create UI overlay - use popup instead
    // this.createEnhancedUI(); // REMOVED - causing UI confusion

    this.isInitialized = true;

    // Emit ready event
    if (this.eventBus) {
      this.eventBus.emit('integration:ready', {
        timestamp: Date.now(),
        components: ['scanner', 'profitCalculator', 'errorHandler'],
        initializationAttempts: this.initializationAttempts
      });
    }
  }

  /**
   * Wait for all required components to be available
   */
  async waitForComponents() {
    const maxWait = 10000; // 10 seconds
    const checkInterval = 100; // 100ms
    let waited = 0;
    
    while (waited < maxWait) {
      if (window.ArbitrageEventBus && 
          window.EbaySearchScanner && 
          window.ArbitrageProfitCalculator && 
          window.ArbitrageErrorHandler) {
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waited += checkInterval;
    }
    
    throw new Error('Required components not available after timeout');
  }

  /**
   * Set up event listeners for component communication
   */
  setupEventListeners() {
    // Scanner events
    this.eventBus.on('scan:started', (data) => {
      this.updateUI('scanning', data);
    });
    
    this.eventBus.on('scan:progress', (data) => {
      this.updateProgress(data.progress);
      this.updateStats(data);
    });
    
    this.eventBus.on('scan:completed', (data) => {
      this.handleScanComplete(data);
    });
    
    this.eventBus.on('scan:error', (data) => {
      this.handleScanError(data);
    });
    
    // Profit calculation events
    this.eventBus.on('profit:calculated', (data) => {
      this.handleProfitCalculated(data);
    });
    
    // Error events
    this.eventBus.on('error:final', (data) => {
      this.handleFinalError(data);
    });
  }

  /**
   * Create enhanced UI panel
   */
  createEnhancedUI() {
    // Remove existing panel
    const existing = document.getElementById('enhanced-arbitrage-panel');
    if (existing) existing.remove();

    const panel = document.createElement('div');
    panel.id = 'enhanced-arbitrage-panel';
    panel.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        width: 380px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        backdrop-filter: blur(10px);
      ">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
          <div style="font-size: 20px; margin-right: 10px;">🔍</div>
          <div>
            <div style="font-weight: bold; font-size: 16px;">eBay Arbitrage Pro</div>
            <div style="opacity: 0.8; font-size: 12px;">Enhanced Scanner v2.0</div>
          </div>
          <button id="close-panel" style="
            margin-left: auto;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
          ">×</button>
        </div>
        
        <div id="scan-status" style="
          background: rgba(255,255,255,0.1);
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 15px;
        ">
          <div style="font-weight: bold; margin-bottom: 5px;">Status: Ready</div>
          <div id="status-details" style="opacity: 0.8; font-size: 12px;">
            Click "Start Enhanced Scan" to begin analysis
          </div>
        </div>
        
        <div id="progress-section" style="margin-bottom: 15px; display: none;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <span>Progress</span>
            <span id="progress-text">0%</span>
          </div>
          <div style="
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
          ">
            <div id="progress-fill" style="
              height: 100%;
              background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
              width: 0%;
              transition: width 0.3s ease;
            "></div>
          </div>
        </div>
        
        <div id="stats-grid" style="
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          margin-bottom: 15px;
        ">
          <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 6px; text-align: center;">
            <div style="font-size: 18px; font-weight: bold;" id="items-found">0</div>
            <div style="font-size: 11px; opacity: 0.8;">Items Found</div>
          </div>
          <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 6px; text-align: center;">
            <div style="font-size: 18px; font-weight: bold;" id="avg-margin">0%</div>
            <div style="font-size: 11px; opacity: 0.8;">Avg Margin</div>
          </div>
        </div>
        
        <div style="display: flex; gap: 8px; margin-bottom: 15px;">
          <button id="start-enhanced-scan" style="
            flex: 1;
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
          ">Start Enhanced Scan</button>
          <button id="stop-scan" style="
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            cursor: pointer;
            display: none;
          ">Stop</button>
        </div>
        
        <div id="results-preview" style="
          max-height: 200px;
          overflow-y: auto;
          background: rgba(0,0,0,0.2);
          border-radius: 6px;
          padding: 10px;
        ">
          <div style="text-align: center; opacity: 0.6; padding: 20px;">
            No results yet. Start a scan to see opportunities!
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(panel);

    // Add event listeners
    document.getElementById('start-enhanced-scan').addEventListener('click', () => this.startEnhancedScan());
    document.getElementById('stop-scan').addEventListener('click', () => this.stopScan());
    document.getElementById('close-panel').addEventListener('click', () => panel.remove());
    
    // Add hover effects
    const buttons = panel.querySelectorAll('button');
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        button.style.transform = 'scale(1.05)';
      });
      button.addEventListener('mouseleave', () => {
        button.style.transform = 'scale(1)';
      });
    });
  }

  /**
   * Start enhanced scanning with profit analysis
   */
  async startEnhancedScan() {
    try {
      if (this.currentScan) {
        console.warn('⚠️ Scan already in progress');
        return;
      }
      
      if (this.debug) {
        console.log('🚀 EnhancedEbayIntegration: Starting enhanced scan...');
      }
      
      this.results = [];
      this.updateUI('scanning');
      
      // Start the enhanced scan
      this.currentScan = await this.scanner.scanCurrentPage({
        maxItems: 50,
        includeSponsored: false,
        onProgress: (progress) => {
          this.updateProgress(progress.progress);
        }
      });
      
      if (this.debug) {
        console.log(`✅ EnhancedEbayIntegration: Scan complete - ${this.currentScan.length} items found`);
      }
      
      // Process results with profit analysis
      await this.processResults(this.currentScan);
      
      this.updateUI('complete');
      
    } catch (error) {
      console.error('❌ EnhancedEbayIntegration: Enhanced scan failed:', error);
      this.updateUI('error', error.message);
    } finally {
      this.currentScan = null;
    }
  }

  /**
   * Process scan results with profit analysis
   */
  async processResults(scanResults) {
    if (this.debug) {
      console.log('💰 EnhancedEbayIntegration: Processing results with profit analysis...');
    }
    
    for (const item of scanResults) {
      try {
        // Mock Amazon data for now (Phase 2 will have real Amazon integration)
        const mockAmazonData = {
          price: item.price * 1.3, // Assume 30% higher on Amazon
          available: true,
          title: item.title
        };
        
        // Calculate profit
        const profitAnalysis = this.profitCalculator.calculateProfit(item, mockAmazonData, {
          category: 'default',
          condition: item.condition?.toLowerCase() || 'used',
          includeShipping: true,
          includeTaxes: true
        });
        
        // Add to results if profitable
        if (profitAnalysis.netProfit > 5) {
          this.results.push({
            ...item,
            amazonData: mockAmazonData,
            profitAnalysis
          });
        }
        
      } catch (error) {
        console.warn('⚠️ Failed to process item:', item.title, error);
      }
    }
    
    // Sort by profit margin
    this.results.sort((a, b) => 
      parseFloat(b.profitAnalysis.profitMargin) - parseFloat(a.profitAnalysis.profitMargin)
    );
    
    this.updateResultsPreview();
  }

  /**
   * Update UI state
   */
  updateUI(state, data = null) {
    const statusEl = document.getElementById('scan-status');
    const startBtn = document.getElementById('start-enhanced-scan');
    const stopBtn = document.getElementById('stop-scan');
    const progressSection = document.getElementById('progress-section');
    const statusDetails = document.getElementById('status-details');

    switch (state) {
      case 'scanning':
        statusEl.querySelector('div').textContent = 'Status: Scanning...';
        statusDetails.textContent = 'Analyzing eBay listings with enhanced extraction';
        startBtn.style.display = 'none';
        stopBtn.style.display = 'block';
        progressSection.style.display = 'block';
        break;

      case 'complete':
        statusEl.querySelector('div').textContent = 'Status: Complete';
        statusDetails.textContent = `Found ${this.results.length} profitable opportunities`;
        startBtn.style.display = 'block';
        stopBtn.style.display = 'none';
        progressSection.style.display = 'none';
        break;

      case 'error':
        statusEl.querySelector('div').textContent = 'Status: Error';
        statusDetails.textContent = data || 'An error occurred during scanning';
        startBtn.style.display = 'block';
        stopBtn.style.display = 'none';
        progressSection.style.display = 'none';
        break;

      default:
        statusEl.querySelector('div').textContent = 'Status: Ready';
        statusDetails.textContent = 'Click "Start Enhanced Scan" to begin analysis';
        startBtn.style.display = 'block';
        stopBtn.style.display = 'none';
        progressSection.style.display = 'none';
    }
  }

  /**
   * Update progress bar
   */
  updateProgress(percent) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    if (progressFill && progressText) {
      progressFill.style.width = percent + '%';
      progressText.textContent = Math.round(percent) + '%';
    }
  }

  /**
   * Update statistics display
   */
  updateStats(data = null) {
    const itemsFound = document.getElementById('items-found');
    const avgMargin = document.getElementById('avg-margin');

    if (itemsFound) {
      itemsFound.textContent = this.results.length;
    }

    if (avgMargin && this.results.length > 0) {
      const totalMargin = this.results.reduce((sum, item) =>
        sum + parseFloat(item.profitAnalysis?.profitMargin || 0), 0
      );
      const average = (totalMargin / this.results.length).toFixed(1);
      avgMargin.textContent = average + '%';
    }
  }

  /**
   * Update results preview
   */
  updateResultsPreview() {
    const resultsContainer = document.getElementById('results-preview');
    if (!resultsContainer) return;

    if (this.results.length === 0) {
      resultsContainer.innerHTML = `
        <div style="text-align: center; opacity: 0.6; padding: 20px;">
          No profitable opportunities found. Try a different search.
        </div>
      `;
      return;
    }

    const topResults = this.results.slice(0, 5); // Show top 5

    resultsContainer.innerHTML = topResults.map(item => {
      const profit = item.profitAnalysis;
      const gradeColor = this.getGradeColor(profit.grade);

      return `
        <div style="
          background: rgba(255,255,255,0.1);
          border-radius: 6px;
          padding: 10px;
          margin-bottom: 8px;
          border-left: 3px solid ${gradeColor};
        ">
          <div style="font-weight: bold; margin-bottom: 5px; font-size: 12px;">
            ${item.title.substring(0, 60)}${item.title.length > 60 ? '...' : ''}
          </div>
          <div style="display: flex; justify-content: space-between; font-size: 11px;">
            <span>eBay: $${item.price}</span>
            <span>Amazon: $${item.amazonData.price}</span>
            <span style="color: ${gradeColor}; font-weight: bold;">
              ${profit.grade} (${profit.profitMargin.toFixed(1)}%)
            </span>
          </div>
          <div style="font-size: 10px; opacity: 0.8; margin-top: 3px;">
            Profit: $${profit.netProfit.toFixed(2)} | Risk: ${profit.riskScore}/100
          </div>
        </div>
      `;
    }).join('');

    // Add "View All" button if more results
    if (this.results.length > 5) {
      resultsContainer.innerHTML += `
        <div style="text-align: center; margin-top: 10px;">
          <button onclick="window.enhancedIntegration.exportResults()" style="
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
          ">Export All ${this.results.length} Results</button>
        </div>
      `;
    }
  }

  /**
   * Get color for grade
   */
  getGradeColor(grade) {
    const colors = {
      'A+': '#4ade80',
      'A': '#22c55e',
      'B+': '#84cc16',
      'B': '#eab308',
      'C+': '#f59e0b',
      'C': '#f97316',
      'D': '#ef4444',
      'F': '#dc2626'
    };
    return colors[grade] || '#6b7280';
  }

  /**
   * Export results to CSV
   */
  exportResults() {
    if (this.results.length === 0) {
      alert('No results to export');
      return;
    }

    const headers = [
      'Title', 'eBay Price', 'Amazon Price', 'Net Profit', 'Profit Margin',
      'Grade', 'Risk Score', 'eBay URL', 'Condition'
    ];

    const csvData = [
      headers.join(','),
      ...this.results.map(item => [
        `"${item.title.replace(/"/g, '""')}"`,
        item.price,
        item.amazonData.price,
        item.profitAnalysis.netProfit.toFixed(2),
        item.profitAnalysis.profitMargin.toFixed(1) + '%',
        item.profitAnalysis.grade,
        item.profitAnalysis.riskScore,
        `"${item.url}"`,
        item.condition || 'Unknown'
      ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ebay_arbitrage_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (this.debug) {
      console.log(`📊 Exported ${this.results.length} results to CSV`);
    }
  }

  /**
   * Stop current scan
   */
  stopScan() {
    if (this.scanner) {
      this.scanner.stopScan();
    }
    this.currentScan = null;
    this.updateUI('ready');
  }

  /**
   * Handle scan completion
   */
  handleScanComplete(data) {
    if (this.debug) {
      console.log('✅ Scan completed:', data);
    }
  }

  /**
   * Handle scan error
   */
  handleScanError(data) {
    console.error('❌ Scan error:', data);
    this.updateUI('error', data.error);
  }

  /**
   * Handle profit calculation
   */
  handleProfitCalculated(data) {
    if (this.debug) {
      console.log('💰 Profit calculated:', data);
    }
  }

  /**
   * Handle final errors
   */
  handleFinalError(data) {
    console.error('💥 Final error:', data);
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      currentScan: !!this.currentScan,
      resultsCount: this.results.length,
      components: {
        scanner: !!this.scanner,
        profitCalculator: !!this.profitCalculator,
        errorHandler: !!this.errorHandler,
        eventBus: !!this.eventBus
      }
    };
  }

  /**
   * Manually restart initialization (can be called externally)
   */
  restartInitialization() {
    if (this.isInitialized) {
      console.log('✅ Enhanced integration already initialized');
      return true;
    }

    console.log('🔄 Manually restarting enhanced integration initialization...');

    // Clear any existing watcher
    if (this.dependencyWatcher) {
      clearInterval(this.dependencyWatcher);
      this.dependencyWatcher = null;
    }

    // Reset state
    this.initializationAttempts = 0;
    this.initializationGaveUp = false;

    // Start fresh
    this.attemptInitialization();

    return false; // Will be true once initialization completes
  }
}

// ⚡ CRITICAL FIX: Robust Chrome Runtime Message Listeners
// These MUST be registered at top level, never inside guards or conditions
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('📨 Content script received message:', message);

  // ✅ CRITICAL FIX: Wrap all async operations to prevent port closure
  handleMessageSafely(message, sender, sendResponse);
  return true; // Always keep message channel open
});

/**
 * ✅ CRITICAL FIX: Safe message handler that prevents port closure
 */
async function handleMessageSafely(message, sender, sendResponse) {
  try {

    // Handle ping requests for connection verification
    if (message.action === 'ping') {
      console.log('🏓 Content script responding to ping');

      // Include detailed initialization status in ping response
      const integration = window.enhancedIntegration;
      const depCheck = integration ? integration.checkDependencies() : { ready: false, missing: ['EnhancedIntegration'], available: {} };

      sendResponse({
        success: true,
        message: 'pong',
        initialization: {
          integrationAvailable: !!integration,
          integrationInitialized: integration?.isInitialized || false,
          initializationAttempts: integration?.initializationAttempts || 0,
          dependencies: depCheck
        }
      });
      return;
    }

    // Handle scraping requests
    if (message.action === 'startScraping') {
      console.log('🔍 Content script starting scraping with config:', message.config);
      await handleScrapingRequestSafely(message.config, sendResponse);
      return;
    }

    // Handle selector testing
    if (message.action === 'testSelectors') {
      console.log('🧪 Testing eBay selectors');
      testEbaySelectors(sendResponse);
      return;
    }

    // Handle minimal testing
    if (message.action === 'minimalTest') {
      console.log('🔬 Running minimal scraper test');
      runMinimalTest(sendResponse);
      return;
    }

    console.warn('❓ Unknown message action:', message.action);
    sendResponse({ success: false, error: 'Unknown action' });

  } catch (error) {
    console.error('❌ Message handler error:', error);
    sendResponse({
      success: false,
      error: 'Message handler crashed: ' + error.message
    });
  }

  /**
   * ✅ CRITICAL: Handle messages forwarded from all-ebay-handler
   */
  handleMessage(message, sender, sendResponse) {
    console.log('📨 Enhanced integration handling forwarded message:', message);

    // Use the same safe message handling
    handleMessageSafely(message, sender, sendResponse);
    return true; // Keep message channel open
  }
}

/**
 * ✅ CRITICAL FIX: Safe scraping handler that prevents message port closure
 */
async function handleScrapingRequestSafely(config, sendResponse) {
  try {
    // Check if integration is ready
    if (!window.enhancedIntegration || !window.enhancedIntegration.isInitialized) {
      console.warn('⚠️ Enhanced integration not ready, attempting restart...');

      // Try to initialize/restart with timeout
      const initResult = await initializeWithTimeout();

      if (!initResult) {
        sendResponse({
          success: false,
          error: 'Enhanced integration failed to initialize after restart attempts'
        });
        return;
      }
    }

    // Handle scraping with timeout protection
    await Promise.race([
      handleScrapingRequest(config, sendResponse),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Scraping timeout after 30 seconds')), 30000)
      )
    ]);

  } catch (error) {
    console.error('❌ Safe scraping handler error:', error);
    sendResponse({
      success: false,
      error: 'Scraping failed: ' + error.message
    });
  }
}

/**
 * ✅ CRITICAL FIX: Initialize with timeout to prevent hanging
 */
async function initializeWithTimeout(timeoutMs = 10000) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    // Set timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.error('❌ Initialization timeout after', timeoutMs, 'ms');
      resolve(false);
    }, timeoutMs);

    const checkInit = () => {
      if (window.enhancedIntegration?.isInitialized) {
        clearTimeout(timeout);
        resolve(true);
      } else if (Date.now() - startTime > timeoutMs) {
        clearTimeout(timeout);
        resolve(false);
      } else {
        // Try to restart if needed
        if (!window.enhancedIntegration) {
          window.enhancedIntegration = new EnhancedEbayIntegration();
        } else if (window.enhancedIntegration.initializationGaveUp) {
          window.enhancedIntegration.restartInitialization();
        }

        setTimeout(checkInit, 200);
      }
    };

    checkInit();
  });
}
});

// Handle scraping requests with enhanced initialization checking
async function handleScrapingRequest(config, sendResponse) {
  try {
    console.log('🚀 Starting eBay scraping with config:', config);

    // Check if enhanced integration is ready
    const integration = window.enhancedIntegration;
    if (!integration) {
      throw new Error('Enhanced integration not available');
    }

    if (!integration.isInitialized) {
      console.warn('⚠️ Enhanced integration not initialized, waiting...');

      // Wait up to 10 seconds for initialization
      const maxWait = 10000;
      const checkInterval = 500;
      let waited = 0;

      while (!integration.isInitialized && waited < maxWait) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waited += checkInterval;
      }

      if (!integration.isInitialized) {
        throw new Error(`Enhanced integration failed to initialize after ${maxWait}ms. Dependencies missing: ${integration.checkDependencies().missing.join(', ')}`);
      }
    }

    const scanner = integration.scanner;
    if (!scanner) {
      throw new Error('eBay scanner not available after initialization');
    }

    // Start the scan
    const results = await scanner.scanCurrentPage(config);

    console.log('✅ Scraping completed, found products:', results.length);
    sendResponse({
      success: true,
      products: results,
      summary: {
        totalFound: results.length,
        config: config,
        initializationAttempts: integration.initializationAttempts
      }
    });

  } catch (error) {
    console.error('❌ Scraping failed:', error);
    sendResponse({
      success: false,
      error: error.message,
      details: error.stack,
      diagnostics: {
        integrationAvailable: !!window.enhancedIntegration,
        integrationInitialized: window.enhancedIntegration?.isInitialized || false,
        dependencies: window.enhancedIntegration?.checkDependencies() || null
      }
    });
  }
}

// Test eBay selectors
function testEbaySelectors(sendResponse) {
  try {
    const selectors = {
      listings: '.s-item',
      title: '.s-item__title',
      price: '.s-item__price',
      shipping: '.s-item__shipping',
      condition: '.s-item__condition-text'
    };

    const results = {};
    for (const [name, selector] of Object.entries(selectors)) {
      const elements = document.querySelectorAll(selector);
      results[name] = {
        selector: selector,
        found: elements.length,
        sample: elements.length > 0 ? elements[0].textContent?.trim().substring(0, 100) : null
      };
    }

    sendResponse({ success: true, results: results });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Run minimal test
function runMinimalTest(sendResponse) {
  try {
    const testResults = {
      url: window.location.href,
      title: document.title,
      listingsFound: document.querySelectorAll('.s-item').length,
      isEbayPage: window.location.href.includes('ebay.com'),
      isSoldListings: window.location.href.includes('LH_Sold=1'),
      timestamp: new Date().toISOString()
    };

    sendResponse({ success: true, results: testResults });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Handle bfcache events for connection recovery
window.addEventListener('pageshow', (event) => {
  if (event.persisted) {
    console.log('📄 Page restored from bfcache, re-establishing connection');
    // Re-initialize if needed
    if (!window.enhancedIntegration || !window.enhancedIntegration.isInitialized) {
      console.log('🔄 Re-initializing enhanced integration after bfcache restore');
      window.enhancedIntegration = new EnhancedEbayIntegration();
    }
  }
});

window.addEventListener('pagehide', (event) => {
  if (event.persisted) {
    console.log('📄 Page going into bfcache');
  }
});

// Initialize when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Creating enhanced integration on DOMContentLoaded');
    window.enhancedIntegration = new EnhancedEbayIntegration();
  });
} else {
  console.log('🚀 Creating enhanced integration immediately (DOM already ready)');
  window.enhancedIntegration = new EnhancedEbayIntegration();
}

// Signal readiness to background script
function signalReadiness() {
  try {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      url: window.location.href,
      timestamp: Date.now()
    }).catch(error => {
      console.log('Failed to signal readiness (normal if background not ready):', error.message);
    });
  } catch (error) {
    console.log('Failed to signal readiness:', error.message);
  }
}

// Signal readiness after initialization
setTimeout(signalReadiness, 1000);

// Add diagnostic function for troubleshooting
window.diagnoseDependencies = function() {
  console.log('🔍 DEPENDENCY DIAGNOSTIC REPORT');
  console.log('================================');

  const dependencies = {
    'ArbitrageEventBus': window.ArbitrageEventBus,
    'ArbitrageErrorHandler': window.ArbitrageErrorHandler,
    'ArbitrageRateLimiter': window.ArbitrageRateLimiter,
    'ArbitrageProfitCalculator': window.ArbitrageProfitCalculator,
    'EbaySearchScanner': window.EbaySearchScanner
  };

  console.log('📊 Dependency Status:');
  for (const [name, obj] of Object.entries(dependencies)) {
    console.log(`  ${obj ? '✅' : '❌'} ${name}: ${typeof obj} ${obj?.constructor?.name || ''}`);
  }

  console.log('📄 Document State:', document.readyState);
  console.log('🌐 URL:', window.location.href);

  const integration = window.enhancedIntegration;
  if (integration) {
    console.log('🔧 Enhanced Integration:');
    console.log(`  Initialized: ${integration.isInitialized}`);
    console.log(`  Attempts: ${integration.initializationAttempts}/${integration.maxInitAttempts}`);

    const depCheck = integration.checkDependencies();
    console.log(`  Dependencies Ready: ${depCheck.ready}`);
    if (!depCheck.ready) {
      console.log(`  Missing: ${depCheck.missing.join(', ')}`);
    }
  } else {
    console.log('❌ Enhanced Integration: Not available');
  }

  console.log('================================');
};

// Add manual restart function for external use
window.restartEnhancedIntegration = function() {
  if (window.enhancedIntegration) {
    return window.enhancedIntegration.restartInitialization();
  } else {
    console.log('🔄 Creating new enhanced integration instance...');
    window.enhancedIntegration = new EnhancedEbayIntegration();
    return false;
  }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedEbayIntegration;
}
