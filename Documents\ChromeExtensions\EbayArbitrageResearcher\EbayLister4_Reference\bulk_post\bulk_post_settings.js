var a0_0x72a039=a0_0x1a4e;(function(_0x5abe19,_0x411d65){var _0x19d668=a0_0x1a4e,_0x2b5e68=_0x5abe19();while(!![]){try{var _0xdb21c8=parseInt(_0x19d668(0x82))/0x1+parseInt(_0x19d668(0x6e))/0x2*(-parseInt(_0x19d668(0x99))/0x3)+parseInt(_0x19d668(0xcd))/0x4*(parseInt(_0x19d668(0x70))/0x5)+-parseInt(_0x19d668(0xca))/0x6*(-parseInt(_0x19d668(0x8c))/0x7)+-parseInt(_0x19d668(0xcf))/0x8+parseInt(_0x19d668(0x8e))/0x9*(parseInt(_0x19d668(0x8a))/0xa)+parseInt(_0x19d668(0x7b))/0xb;if(_0xdb21c8===_0x411d65)break;else _0x2b5e68['push'](_0x2b5e68['shift']());}catch(_0x33000e){_0x2b5e68['push'](_0x2b5e68['shift']());}}}(a0_0x1c7c,0x73cf4),document[a0_0x72a039(0x69)]('DOMContentLoaded',async()=>{var _0x2de6d1=a0_0x72a039,{amazonLinks:_0x50cf1f,minPrice:_0x11d373,maxPrice:_0x352677,fbaOnly:_0x5ffdac,threadCount:_0x48ac10,position:_0xe74e1,run_status_bulk_lister:_0x119ea1,closeErrors:_0x1ff9f8,shouldOnlyListChineseSellers:_0x1317b9,shouldGetGspr:_0x475b2c,shouldOptimizeForSlowComputers:_0x381ff4}=await chrome['storage'][_0x2de6d1(0x64)][_0x2de6d1(0xa8)]([_0x2de6d1(0xbc),_0x2de6d1(0xc1),'maxPrice','fbaOnly',_0x2de6d1(0x9b),_0x2de6d1(0x7f),_0x2de6d1(0xb1),_0x2de6d1(0x97),_0x2de6d1(0x9c),_0x2de6d1(0xac),_0x2de6d1(0x7e)]);console['log'](_0x2de6d1(0xbc),_0x50cf1f),console['log']('minPrice',_0x11d373),console[_0x2de6d1(0x93)](_0x2de6d1(0xb3),_0x352677),console['log'](_0x2de6d1(0xae),_0x5ffdac),console[_0x2de6d1(0x93)](_0x2de6d1(0x9b),_0x48ac10),console['log'](_0x2de6d1(0x7f),_0xe74e1),console[_0x2de6d1(0x93)](_0x2de6d1(0xb1),_0x119ea1),console['log'](_0x2de6d1(0x97),_0x1ff9f8),console[_0x2de6d1(0x93)](_0x2de6d1(0x9c),_0x1317b9),console[_0x2de6d1(0x93)](_0x2de6d1(0xac),_0x475b2c),console['log'](_0x2de6d1(0x7e),_0x381ff4);_0x50cf1f&&(document[_0x2de6d1(0xb0)](_0x2de6d1(0xb7))[_0x2de6d1(0x71)]=_0x50cf1f[_0x2de6d1(0x94)]('\x0a'),document[_0x2de6d1(0xb0)]('total-links-label')[_0x2de6d1(0x9d)]=_0x50cf1f[_0x2de6d1(0x75)]);_0x11d373==undefined?chrome[_0x2de6d1(0xa9)][_0x2de6d1(0x64)][_0x2de6d1(0xa3)]({'minPrice':0x0}):document[_0x2de6d1(0xb0)]('min-price')[_0x2de6d1(0x71)]=_0x11d373;_0x352677==undefined?chrome[_0x2de6d1(0xa9)][_0x2de6d1(0x64)][_0x2de6d1(0xa3)]({'maxPrice':0x64}):document[_0x2de6d1(0xb0)]('max-price')['value']=_0x352677;_0x5ffdac==undefined?chrome[_0x2de6d1(0xa9)]['local']['set']({'fbaOnly':!![]}):document[_0x2de6d1(0xb0)]('fbaOnly')[_0x2de6d1(0x91)]=_0x5ffdac;_0x1ff9f8==undefined?chrome[_0x2de6d1(0xa9)][_0x2de6d1(0x64)][_0x2de6d1(0xa3)]({'closeErrors':!![]}):document[_0x2de6d1(0xb0)](_0x2de6d1(0x97))[_0x2de6d1(0x91)]=_0x1ff9f8;_0x1317b9==undefined?chrome[_0x2de6d1(0xa9)][_0x2de6d1(0x64)][_0x2de6d1(0xa3)]({'shouldOnlyListChineseSellers':![]}):document[_0x2de6d1(0xb0)]('shouldOnlyListChineseSellers')[_0x2de6d1(0x91)]=_0x1317b9;_0x475b2c==undefined?chrome[_0x2de6d1(0xa9)]['local']['set']({'shouldGetGspr':![]}):document[_0x2de6d1(0xb0)]('shouldGetGspr')[_0x2de6d1(0x91)]=_0x475b2c;_0x381ff4==undefined?chrome[_0x2de6d1(0xa9)]['local']['set']({'shouldOptimizeForSlowComputers':![]}):document[_0x2de6d1(0xb0)](_0x2de6d1(0x7e))['checked']=_0x381ff4;_0x48ac10==undefined?chrome['storage']['local']['set']({'threadCount':0x1}):document[_0x2de6d1(0xb0)]('thread-count')[_0x2de6d1(0x71)]=_0x48ac10;_0xe74e1==undefined?chrome['storage'][_0x2de6d1(0x64)]['set']({'position':0x0}):document[_0x2de6d1(0xb0)]('position-input')['value']=_0xe74e1;if(_0x119ea1==undefined||_0x119ea1==![])chrome['storage'][_0x2de6d1(0x64)]['set']({'run_status_bulk_lister':![]}),document['getElementById'](_0x2de6d1(0x83))[_0x2de6d1(0x7a)]=!![];else _0x119ea1==!![]&&(document[_0x2de6d1(0xb0)]('resume_button')[_0x2de6d1(0x7a)]=!![],document[_0x2de6d1(0xb0)](_0x2de6d1(0xba))['disabled']=!![],document[_0x2de6d1(0xb0)](_0x2de6d1(0xc7))[_0x2de6d1(0x7a)]=!![],blockInput(!![]));var _0x4fd0ac=_0xe74e1/_0x50cf1f[_0x2de6d1(0x75)]*0x64;console[_0x2de6d1(0x93)](_0x2de6d1(0xab),_0x4fd0ac),animateProgressBar(_0x4fd0ac);}),document[a0_0x72a039(0xb0)](a0_0x72a039(0xb7))[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x3fed84=>{var _0x17e50f=a0_0x72a039,_0xb02248=_0x3fed84[_0x17e50f(0x79)][_0x17e50f(0x71)][_0x17e50f(0x8d)]('\x0a');_0xb02248=_0xb02248[_0x17e50f(0x66)](_0x17c59a=>_0x17c59a!=''),await chrome['storage'][_0x17e50f(0x64)][_0x17e50f(0xa3)]({'amazonLinks':_0xb02248}),document[_0x17e50f(0xb0)]('total-links-label')[_0x17e50f(0x9d)]=_0xb02248[_0x17e50f(0x75)];}),document[a0_0x72a039(0xb0)](a0_0x72a039(0xc6))[a0_0x72a039(0x69)]('change',async _0x127213=>{var _0x55bca2=a0_0x72a039,_0x271f73=_0x127213[_0x55bca2(0x79)]['value'];await chrome[_0x55bca2(0xa9)][_0x55bca2(0x64)][_0x55bca2(0xa3)]({'minPrice':_0x271f73});}),document[a0_0x72a039(0xb0)](a0_0x72a039(0xa1))[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x46b700=>{var _0x324afa=a0_0x72a039,_0x2fe6a9=_0x46b700['target'][_0x324afa(0x71)];await chrome['storage']['local'][_0x324afa(0xa3)]({'maxPrice':_0x2fe6a9});}),document[a0_0x72a039(0xb0)]('fbaOnly')[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x284149=>{var _0x8799b4=a0_0x72a039,_0xa52032=_0x284149[_0x8799b4(0x79)][_0x8799b4(0x91)];await chrome[_0x8799b4(0xa9)][_0x8799b4(0x64)]['set']({'fbaOnly':_0xa52032});}),document[a0_0x72a039(0xb0)]('closeErrors')[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x131cea=>{var _0x2bbdcb=a0_0x72a039,_0x5bf787=_0x131cea[_0x2bbdcb(0x79)][_0x2bbdcb(0x91)];await chrome[_0x2bbdcb(0xa9)]['local'][_0x2bbdcb(0xa3)]({'closeErrors':_0x5bf787});}),document[a0_0x72a039(0xb0)](a0_0x72a039(0x9c))[a0_0x72a039(0x69)]('change',async _0x12257b=>{var _0x5a1aa6=a0_0x72a039,_0x2e611f=_0x12257b[_0x5a1aa6(0x79)][_0x5a1aa6(0x91)];await chrome[_0x5a1aa6(0xa9)][_0x5a1aa6(0x64)][_0x5a1aa6(0xa3)]({'shouldOnlyListChineseSellers':_0x2e611f});}),document[a0_0x72a039(0xb0)]('shouldGetGspr')['addEventListener']('change',async _0xe9625e=>{var _0x1e6f58=a0_0x72a039,_0x1bc9e9=_0xe9625e[_0x1e6f58(0x79)][_0x1e6f58(0x91)];await chrome[_0x1e6f58(0xa9)]['local'][_0x1e6f58(0xa3)]({'shouldGetGspr':_0x1bc9e9});}),document['getElementById'](a0_0x72a039(0x7e))[a0_0x72a039(0x69)]('change',async _0x1c5840=>{var _0x4d7694=a0_0x72a039;console['log'](_0x4d7694(0x6f),_0x1c5840[_0x4d7694(0x79)][_0x4d7694(0x91)]);var _0x31575e=_0x1c5840[_0x4d7694(0x79)][_0x4d7694(0x91)];await chrome['storage']['local'][_0x4d7694(0xa3)]({'shouldOptimizeForSlowComputers':_0x31575e});}),document['getElementById'](a0_0x72a039(0x65))[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x5597e0=>{var _0x32e9b6=a0_0x72a039,_0x14a81c=_0x5597e0[_0x32e9b6(0x79)][_0x32e9b6(0x71)];await chrome['storage'][_0x32e9b6(0x64)][_0x32e9b6(0xa3)]({'threadCount':_0x14a81c});}),document['getElementById'](a0_0x72a039(0xc0))[a0_0x72a039(0x69)](a0_0x72a039(0xc9),async _0x13ec0e=>{var _0x292eaf=a0_0x72a039,_0x26572d=_0x13ec0e[_0x292eaf(0x79)][_0x292eaf(0x71)];await chrome[_0x292eaf(0xa9)][_0x292eaf(0x64)][_0x292eaf(0xa3)]({'position':_0x26572d});}),document[a0_0x72a039(0xb0)](a0_0x72a039(0x83))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x4bbef3){var _0x164065=a0_0x72a039;_0x4bbef3[_0x164065(0x78)](),chrome[_0x164065(0xa9)][_0x164065(0x64)][_0x164065(0xa3)]({'run_status_bulk_lister':![]}),blockInput(![]),document[_0x164065(0xb0)](_0x164065(0x83))['disabled']=!![],document[_0x164065(0xb0)]('resume_button')['disabled']=![];}),document[a0_0x72a039(0xb0)]('resume_button')[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x213419){var _0x3f2100=a0_0x72a039;_0x213419[_0x3f2100(0x78)](),chrome[_0x3f2100(0xa9)][_0x3f2100(0x64)][_0x3f2100(0xa3)]({'run_status_bulk_lister':!![]}),blockInput(!![]),document[_0x3f2100(0xb0)](_0x3f2100(0x83))[_0x3f2100(0x7a)]=![],document[_0x3f2100(0xb0)](_0x3f2100(0xb8))[_0x3f2100(0x7a)]=!![];}),document['getElementById'](a0_0x72a039(0xba))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),function(_0x19aeaa){var _0x5a5060=a0_0x72a039;initiateProductListing(_0x19aeaa,_0x5a5060(0xa5));}),document[a0_0x72a039(0xb0)](a0_0x72a039(0xc7))['addEventListener'](a0_0x72a039(0xb2),function(_0x44792c){initiateProductListing(_0x44792c,'standard');}),document['getElementById'](a0_0x72a039(0xce))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x28bfe4){var _0x2a3314=a0_0x72a039;console[_0x2a3314(0x93)](_0x2a3314(0x87));var _0x491a08=await new Promise(_0xda8551=>{var _0x3ca8ab=_0x2a3314;chrome[_0x3ca8ab(0xb9)][_0x3ca8ab(0xcb)]({'type':'getChatGptWebStatus'},function(_0x4c205c){var _0x54b1cf=_0x3ca8ab;_0xda8551(_0x4c205c[_0x54b1cf(0x6a)]);});});console[_0x2a3314(0x93)]('chatGptWebTabId:\x20',_0x491a08),initiateProductListing(_0x28bfe4,_0x2a3314(0xa7));}),document[a0_0x72a039(0xb0)](a0_0x72a039(0x6c))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x1c1c28){var _0x352888=a0_0x72a039;console[_0x352888(0x93)](_0x352888(0x85));var _0x4da52c=await new Promise(_0x232c79=>{var _0x3667fb=_0x352888;chrome[_0x3667fb(0xb9)][_0x3667fb(0xcb)]({'type':_0x3667fb(0x8b)},function(_0x575288){_0x232c79(_0x575288['response']);});});console[_0x352888(0x93)]('chatGptWebTabId:\x20',_0x4da52c),initiateProductListing(_0x1c1c28,_0x352888(0x6b));}));async function initiateProductListing(_0x48e66f,_0x76713f){var _0x2ddb07=a0_0x72a039,_0x41aa01=await checkMembership();console['log'](_0x2ddb07(0x8f),_0x41aa01);if(_0x41aa01!=_0x2ddb07(0x73)){alert(_0x2ddb07(0xa2));return;}var _0x1dc1d7=await checkIfCreditsAreAvailable();if(_0x1dc1d7==![]){alert(_0x2ddb07(0x95));return;}var _0x4c8164=await new Promise(_0x18dce8=>{var _0x38078c=_0x2ddb07;chrome['runtime'][_0x38078c(0xcb)]({'type':_0x38078c(0xc3)},function(_0x49f00f){var _0x37b9bd=_0x38078c;_0x18dce8(_0x49f00f[_0x37b9bd(0x6a)]);});}),_0x4c8164=await new Promise(_0x321150=>{var _0x579088=_0x2ddb07;chrome[_0x579088(0xb9)][_0x579088(0xcb)]({'type':'terminateSearch'},function(_0x15e5c3){var _0x2d5092=_0x579088;_0x321150(_0x15e5c3[_0x2d5092(0x6a)]);});});chrome[_0x2ddb07(0xa9)][_0x2ddb07(0x64)][_0x2ddb07(0xa3)]({'run_status_bulk_lister':!![],'bulkListType':_0x76713f}),blockInput(!![]),document[_0x2ddb07(0xb0)]('resume_button')[_0x2ddb07(0x7a)]=!![],document[_0x2ddb07(0xb0)](_0x2ddb07(0x83))['disabled']=![],document[_0x2ddb07(0xb0)]('list-btn')[_0x2ddb07(0x7a)]=!![],document[_0x2ddb07(0xb0)](_0x2ddb07(0xc7))[_0x2ddb07(0x7a)]=!![],document[_0x2ddb07(0xb0)]('amazon-links')[_0x2ddb07(0xc8)](new Event(_0x2ddb07(0xc9)));var {position:_0x28cdfc}=await chrome[_0x2ddb07(0xa9)][_0x2ddb07(0x64)][_0x2ddb07(0xa8)](_0x2ddb07(0x7f)),{amazonLinks:_0x359f38}=await chrome[_0x2ddb07(0xa9)][_0x2ddb07(0x64)][_0x2ddb07(0xa8)](_0x2ddb07(0xbc)),_0x5afedc=_0x28cdfc/_0x359f38[_0x2ddb07(0x75)]*0x64;animateProgressBar(_0x5afedc),chrome[_0x2ddb07(0xb9)][_0x2ddb07(0xcb)]({'action':_0x2ddb07(0x92)});}document[a0_0x72a039(0xb0)](a0_0x72a039(0xaf))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x5ad227){var _0x126188=a0_0x72a039,_0x4ee9a0=await new Promise(_0x510abb=>{var _0x5bc0a8=a0_0x1a4e;chrome[_0x5bc0a8(0xb9)]['sendMessage']({'type':_0x5bc0a8(0xc3)},function(_0x13ec6d){var _0x45e0df=_0x5bc0a8;_0x510abb(_0x13ec6d[_0x45e0df(0x6a)]);});});_0x4ee9a0==!![]&&(chrome[_0x126188(0xa9)][_0x126188(0x64)][_0x126188(0xa3)]({'run_status_bulk_lister':![]}),blockInput(![]),document[_0x126188(0xb0)]('resume_button')['disabled']=!![],document[_0x126188(0xb0)]('list-btn')[_0x126188(0x7a)]=![],document['getElementById']('standard-listing-button')[_0x126188(0x7a)]=![],document['getElementById'](_0x126188(0x83))[_0x126188(0x7a)]=![]);});function animateProgressBar(_0x1549c7){var _0x52e6ca=a0_0x72a039;const _0x2e2205=document[_0x52e6ca(0xb0)](_0x52e6ca(0xc2)),_0x2ce106=document[_0x52e6ca(0x90)](_0x52e6ca(0xa4));if(_0x1549c7<0x0)_0x1549c7=0x0;else _0x1549c7>0x64&&(_0x1549c7=0x64);_0x2e2205[_0x52e6ca(0x7c)][_0x52e6ca(0x68)]=_0x1549c7+'%',_0x2ce106[_0x52e6ca(0x7d)](function(_0x1a29da){var _0x5f3292=_0x52e6ca;_0x1a29da[_0x5f3292(0xcc)]=_0x1549c7+'%';}),_0x1549c7>0x0&&(_0x2ce106[_0x52e6ca(0x7d)](function(_0x42f95c){var _0x17c6b9=_0x52e6ca;_0x42f95c[_0x17c6b9(0x7c)][_0x17c6b9(0xa0)]=_0x17c6b9(0x72);}),_0x2e2205[_0x52e6ca(0x7c)][_0x52e6ca(0x89)]=_0x52e6ca(0x88));}chrome['runtime'][a0_0x72a039(0xb4)][a0_0x72a039(0xa6)](async function(_0x7a0850,_0x4c7dce,_0x3162a5){var _0x1fe50c=a0_0x72a039;if(_0x7a0850[_0x1fe50c(0xbb)]==_0x1fe50c(0xb6)){var {position:_0x2810aa}=await chrome[_0x1fe50c(0xa9)][_0x1fe50c(0x64)][_0x1fe50c(0xa8)](_0x1fe50c(0x7f));document[_0x1fe50c(0xb0)](_0x1fe50c(0xc0))[_0x1fe50c(0x71)]=_0x2810aa;var {amazonLinks:_0x5acc29}=await chrome['storage']['local'][_0x1fe50c(0xa8)](_0x1fe50c(0xbc)),_0x4d02d9=_0x2810aa/_0x5acc29[_0x1fe50c(0x75)]*0x64;console[_0x1fe50c(0x93)](_0x1fe50c(0xab),_0x4d02d9),animateProgressBar(_0x4d02d9);}}),document['getElementById'](a0_0x72a039(0x6d))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x18baa5){var _0x2352ed=a0_0x72a039;_0x18baa5[_0x2352ed(0x78)](),chrome[_0x2352ed(0xbe)][_0x2352ed(0xb5)]({'url':chrome[_0x2352ed(0xb9)]['getURL'](_0x2352ed(0x76))});}),document['getElementById'](a0_0x72a039(0x9a))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x999b1){var _0x177d5f=a0_0x72a039,_0x19bd57=document[_0x177d5f(0xb0)]('position-input');_0x19bd57[_0x177d5f(0x71)]=0x0,_0x19bd57[_0x177d5f(0xc8)](new Event('change'));var _0xe780d1=document[_0x177d5f(0xb0)](_0x177d5f(0xb7));_0xe780d1[_0x177d5f(0x71)]='',_0xe780d1[_0x177d5f(0xc8)](new Event(_0x177d5f(0xc9))),animateProgressBar(0x0);});function a0_0x1a4e(_0x50f44a,_0xdd2366){var _0x1c7ce5=a0_0x1c7c();return a0_0x1a4e=function(_0x1a4e96,_0x2eb2ec){_0x1a4e96=_0x1a4e96-0x64;var _0x3cd5fa=_0x1c7ce5[_0x1a4e96];return _0x3cd5fa;},a0_0x1a4e(_0x50f44a,_0xdd2366);}function blockInput(_0x45158b){var _0x54f745=a0_0x72a039,_0x334d67=document[_0x54f745(0xb0)](_0x54f745(0xb7));_0x45158b?_0x334d67['classList']['add'](_0x54f745(0xbf)):_0x334d67[_0x54f745(0x80)][_0x54f745(0x81)](_0x54f745(0xbf));}function a0_0x1c7c(){var _0x3c9d76=['ultimate','poshmark','length','bulk_post/table.html','querySelector','preventDefault','target','disabled','10931107cnzXnU','style','forEach','shouldOptimizeForSlowComputers','position','classList','remove','57932qDoGzr','pause_button','#standard-listing-button','seo-listing-button\x20clicked','readText','chat-listing-button\x20clicked','#90ee90','backgroundColor','653990XNqzrX','getChatGptWebStatus','67011AGXwct','split','9TNOnou','membership','querySelectorAll','checked','listAmazonProducts','log','join','You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20more\x20credits.','contextmenu','closeErrors','import-links-btn','2445204sMsWIm','clear-btn','threadCount','shouldOnlyListChineseSellers','innerText','add-links-btn','clipboard','visibility','max-price','You\x20are\x20not\x20a\x20member\x20of\x20the\x20sniper\x20list.\x20Please\x20contact\x20support.','set','.progress-percentage','opti','addListener','chatGpt','get','storage','addMenuItem','updateProgressBar:\x20','shouldGetGspr','terminateSearch','fbaOnly','reset-btn','getElementById','run_status_bulk_lister','click','maxPrice','onMessage','create','updateProgressBar','amazon-links','resume_button','runtime','list-btn','type','amazonLinks','showMenu','tabs','blocked','position-input','minPrice','progress-bar','terminateBulkLister','concat','add-links','min-price','standard-listing-button','dispatchEvent','change','354tgYYjO','sendMessage','textContent','412tPfeFX','chat-listing-button','5713784LRLwzm','local','thread-count','filter','standard','width','addEventListener','response','seo','seo-listing-button','status-report','2aqXEtX','shouldOptimizeForSlowComputers:\x20','15620ffrerK','value','visible'];a0_0x1c7c=function(){return _0x3c9d76;};return a0_0x1c7c();}document['getElementById'](a0_0x72a039(0x98))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x1c55c8){var _0x25184c=a0_0x72a039;_0x1c55c8[_0x25184c(0x78)]();var _0x6b0656=await navigator[_0x25184c(0x9f)][_0x25184c(0x86)]();document[_0x25184c(0xb0)](_0x25184c(0xc5))[_0x25184c(0x71)]=_0x6b0656;}),document[a0_0x72a039(0xb0)](a0_0x72a039(0x9e))[a0_0x72a039(0x69)](a0_0x72a039(0xb2),async function(_0x562e00){var _0x1b532a=a0_0x72a039;_0x562e00[_0x1b532a(0x78)]();var _0x21547d=document[_0x1b532a(0xb0)](_0x1b532a(0xc5))['value'],_0x2a8b01=document[_0x1b532a(0xb0)](_0x1b532a(0xb7))['value'],_0x51e00c=_0x2a8b01[_0x1b532a(0x8d)]('\x0a'),_0x3b8735=_0x21547d[_0x1b532a(0x8d)]('\x0a'),_0x33b454=_0x51e00c[_0x1b532a(0xc4)](_0x3b8735),_0x5edbaf=[...new Set(_0x33b454)];_0x21547d=_0x5edbaf['join']('\x0a'),document[_0x1b532a(0xb0)]('amazon-links')[_0x1b532a(0x71)]=_0x21547d,document[_0x1b532a(0xb0)](_0x1b532a(0xb7))['dispatchEvent'](new Event(_0x1b532a(0xc9))),document[_0x1b532a(0xb0)]('add-links')[_0x1b532a(0x71)]='';});var bulkBasicContextMenu=createContextMenu('my-custom-context-menu');bulkBasicContextMenu[a0_0x72a039(0xaa)]('List\x20To\x20Poshmark',function(_0x283841){var _0x465bb1=a0_0x72a039;initiateBulkProductListing(_0x283841,_0x465bb1(0x67),[_0x465bb1(0x74)]);}),document[a0_0x72a039(0x77)](a0_0x72a039(0x84))[a0_0x72a039(0x69)](a0_0x72a039(0x96),function(_0x50eb7f){var _0xf8b4be=a0_0x72a039;_0x50eb7f[_0xf8b4be(0x78)](),bulkBasicContextMenu[_0xf8b4be(0xbd)](_0x50eb7f);});async function initiateBulkProductListing(_0x389b1b,_0x595ac4,_0x53d303){var _0x207727=a0_0x72a039,_0x5b45c3=await checkMembership();console['log'](_0x207727(0x8f),_0x5b45c3);if(_0x5b45c3!=_0x207727(0x73)){alert(_0x207727(0xa2));return;}var _0x11fb08=await checkIfCreditsAreAvailable();if(_0x11fb08==![]){alert('You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20more\x20credits.');return;}var _0x395fa8=await new Promise(_0x5f5193=>{var _0x2411cd=_0x207727;chrome[_0x2411cd(0xb9)][_0x2411cd(0xcb)]({'type':_0x2411cd(0xc3)},function(_0x5c3dc4){var _0x553e47=_0x2411cd;_0x5f5193(_0x5c3dc4[_0x553e47(0x6a)]);});}),_0x395fa8=await new Promise(_0x56fbb0=>{var _0x4a76c8=_0x207727;chrome[_0x4a76c8(0xb9)][_0x4a76c8(0xcb)]({'type':_0x4a76c8(0xad)},function(_0x2600df){var _0x3ffd37=_0x4a76c8;_0x56fbb0(_0x2600df[_0x3ffd37(0x6a)]);});});chrome['storage'][_0x207727(0x64)][_0x207727(0xa3)]({'run_status_bulk_lister':!![],'bulkListType':_0x595ac4}),blockInput(!![]),document[_0x207727(0xb0)](_0x207727(0xb8))[_0x207727(0x7a)]=!![],document[_0x207727(0xb0)](_0x207727(0x83))[_0x207727(0x7a)]=![],document[_0x207727(0xb0)]('list-btn')['disabled']=!![],document[_0x207727(0xb0)](_0x207727(0xc7))[_0x207727(0x7a)]=!![],document['getElementById'](_0x207727(0xb7))[_0x207727(0xc8)](new Event(_0x207727(0xc9)));var {position:_0x497d18}=await chrome[_0x207727(0xa9)][_0x207727(0x64)][_0x207727(0xa8)](_0x207727(0x7f)),{amazonLinks:_0x4228a5}=await chrome['storage'][_0x207727(0x64)]['get'](_0x207727(0xbc)),_0x271a61=_0x497d18/_0x4228a5[_0x207727(0x75)]*0x64;animateProgressBar(_0x271a61),chrome['runtime'][_0x207727(0xcb)]({'type':'start_bulk_listing_to_marketplaces','targetMarketplaces':_0x53d303});}