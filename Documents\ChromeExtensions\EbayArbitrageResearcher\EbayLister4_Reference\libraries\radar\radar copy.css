/* Radar Overlay */
.radar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* Darker background */
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 1));
    overflow: hidden;
    display: flex;
    flex-direction: column; /* Stack items vertically */
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's on top */
  }
  
  
  /* Radar Text */
  .radar-text {
    color: #15fcd8; /* Matching radar color */
    font-size: 2rem;
    margin-bottom: 20px; /* Space between text and radar */
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-shadow: 0 0 10px rgba(21, 252, 216, 0.7); /* Glow effect */
  }
  
  /* Radar */
  .radar {
    --size: 450px;
    --color: #15fcd8;
  
    width: var(--size);
    height: var(--size);
    border: 3px solid var(--color);
    border-radius: 50%;
    background:
      /* 4) Guides */
      linear-gradient(transparent 0 49.75%, var(--color) 49.75% 50%, transparent 50% 100%),
      linear-gradient(to right, transparent 0 49.75%, var(--color) 49.75% 50%, transparent 50% 100%),
      /* 3) Radar */
      radial-gradient(
        transparent 0% 25%,
        var(--color) 25.5% 25.75%, transparent 26% 50%,
        var(--color) 50.5% 50.75%, transparent 51% 75%,
        var(--color) 75.5% 75.75%, transparent 76% 100%),
      /* 2.5) Background Overlay */
      radial-gradient(#00767211, #002421 65%),
      /* 2) Grid */
      repeating-linear-gradient(to right, #0e817a77 0 1px, transparent 1.5px 25px),
      repeating-linear-gradient(#0e817a77 0 1px, transparent 1.5px 25px),
      /* 1) Background */
      radial-gradient(#007672 0, #002421 65%);
    position: relative;
  }
  
  /* Radar Dots */
  .dot {
    width: var(--size);
    height: var(--size);
    background: var(--color);
    border-radius: 50%;
    position: absolute;
    filter: blur(1px);
    opacity: 0.5;
    left: var(--x);
    top: var(--y);
    animation: disappear 1s ease-in-out forwards;
  }
  
  /* Radar Pulsing Effect */
  .radar::before {
    content: "";
    display: block;
    background: rgba(21, 252, 216, 0.15);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    filter: blur(1px);
    animation: pulse 1.25s infinite 0.25s;
  }
  
  /* Radar Sweep Effect */
  .radar::after {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
      transparent 0,
      rgba(21, 252, 216, 0.5) 36deg 54deg,
      transparent 90deg
    );
    mix-blend-mode: plus-lighter;
    animation: move-radar 5s linear infinite;
  }
  
  /* Screen Particles */
  .screen-particle {
    position: absolute;
    background-color: #15fcd8;
    border-radius: 50%;
    animation: particleFade 4s ease-in-out forwards;
    mix-blend-mode: screen;
  }
  
  /* Keyframes */
  @keyframes move-radar {
    from {
      transform: rotate(-45deg);
    }
    to {
      transform: rotate(315deg);
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.15);
      opacity: 0;
    }
  }
  
  @keyframes disappear {
    to {
      opacity: 0;
    }
  }
  
  @keyframes particleFade {
    0% {
      opacity: 0;
      transform: scale(0.5);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(1.5);
    }
  }
  

/* Mouse Sparkles */
.mouse-sparkle {
    position: absolute;
    background-color: #1effd0; /* Matching the radar color */
    border-radius: 50%;
    pointer-events: none; /* So sparkles don't interfere with mouse events */
    mix-blend-mode: screen;
    opacity: 1;
    animation: sparkleFade 1s ease-out forwards;
  }
  
  @keyframes sparkleFade {
    0% {
      opacity: 1;
      transform: scale(1);
      filter: blur(0);
    }
    100% {
      opacity: 0;
      transform: scale(2);
      filter: blur(5px);
    }
  }
  