#keyword_modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

#keyword_modal_content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */

    text-align: center;  /* Center the content */
}

#keyword_modal_close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

#keyword_modal_close:hover,
#keyword_modal_close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}


#keyword_modal_body textarea,
#keyword_modal_footer button {
    display: block;
    margin: 0 auto;  /* Center the elements */
}
