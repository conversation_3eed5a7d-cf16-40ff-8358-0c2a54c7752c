

.amazon-copy-link-button, .amazon-update-quantity-button {
    flex: 1 1 auto; /* Buttons will grow and shrink */
    min-width: 120px; /* Minimum button width */
    padding: 5px 10px; /* Adjust padding */
    font-size: 12px; /* Adjust font size */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
    margin: 5px; /* Margin around buttons */
}

/* Add media queries for different screen sizes */
@media (max-width: 600px) {
    .amazon-copy-link-button, .amazon-update-quantity-button {
        padding: 5px; /* Less padding on smaller screens */
        font-size: 10px; /* Smaller font size on smaller screens */
    }
    .utility-buttons {
        gap: 5px; /* Smaller gap on smaller screens */
    }
}





.amazon-copy-link-button {
    background-color: #FF9900;  /* Amazon's signature orange */
    color: white;              /* White text for contrast */
    padding: 5px 10px;         /* Reduced padding for smaller size */
    border: none;              /* No border */
    border-radius: 3px;        /* Rounded corners */
    font-size: 12px;           /* Smaller font size */
    font-weight: bold;         /* Bold text */
    cursor: pointer;           /* Hand icon on hover */
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    transition: background-color 0.3s, box-shadow 0.3s; /* Smooth transition */
    position: relative;        /* Positioning context for tooltip */
}

.amazon-copy-link-button:hover {
    background-color: #E68A00; /* Darker orange on hover */
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15); /* Larger shadow on hover */
}

.amazon-copy-link-button::before {
    content: "📋";            /* Clipboard icon */
    margin-right: 5px;        /* Space between icon and text */
}

.amazon-copy-link-button::after {
    content: "Copy to clipboard"; /* Tooltip text */
    position: absolute;       /* Absolute position for tooltip */
    top: -30px;               /* Position above the button */
    left: 50%;                /* Center the tooltip */
    transform: translateX(-50%); /* Adjust for centering */
    background-color: #333;   /* Tooltip background */
    color: white;             /* Tooltip text color */
    padding: 2px 5px;         /* Padding for tooltip */
    border-radius: 3px;       /* Rounded corners for tooltip */
    font-size: 10px;          /* Smaller font for tooltip */
    white-space: nowrap;      /* Prevent wrapping */
    opacity: 0;               /* Start invisible */
    transition: opacity 0.3s; /* Transition for tooltip */
    pointer-events: none;     /* Prevents tooltip from being clickable */
}

.amazon-copy-link-button:hover::after {
    opacity: 1;               /* Show tooltip on hover */
}




.amazon-copy-link-button-clicked {
    background-color: #4CAF50;  /* Green background to indicate success */
    color: white;
}

.amazon-copy-link-button-clicked::before {
    content: "✓ "; /* A checkmark icon before the text */
}

.amazon-copy-link-button-clicked::after {
    content: "Copied!"; /* Update tooltip text */
}







.amazon-update-quantity-button {
    background-color: #3B5998; /* Facebook's signature blue for a different look */
    color: white; /* White text for contrast */
    padding: 5px 10px; /* Padding */
    border: none; /* No border */
    border-radius: 3px; /* Rounded corners */
    font-size: 12px; /* Font size */
    font-weight: bold; /* Bold text */
    cursor: pointer; /* Pointer on hover */
    transition: background-color 0.3s; /* Smooth background transition */
    position: relative; /* Needed for the loading animation positioning */
    overflow: hidden; /* Ensures the pseudo-elements do not overflow the button */
}

.amazon-update-quantity-button:hover {
    background-color: #334D77; /* Darker shade of blue for hover effect */
}

.amazon-update-quantity-button:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, 0.2);
    animation: loadingAnimation 2s linear infinite;
    opacity: 0; /* Start hidden */
    transition: opacity 0.3s; /* Fade effect */
}

.amazon-update-quantity-button-working:before {
    opacity: 1; /* Show animation when working */
}

@keyframes loadingAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.amazon-update-quantity-button-success {
    background-color: #4CAF50; /* Green background for success state */
    color: white; /* White text for contrast */
}

.amazon-update-quantity-button-error {
    background-color: #F44336; /* Red background for error state */
    color: white; /* White text for contrast */
}


#ETA-div {
    display: flex; /* Use flexbox for alignment */
    align-items: center; /* Align items vertically in the center */
    gap: 10px; /* Space between the input field and button */
    padding: 10px; /* Padding around the entire container */
    background-color: #f0f0f0; /* Light grey background for the container */
    border-radius: 5px; /* Rounded corners for the container */
    margin: 10px 0; /* Margin above and below the container */
}

#feedback-div{
    display: flex; /* Use flexbox for alignment */
    align-items: center; /* Align items vertically in the center */
    gap: 10px; /* Space between the input field and button */
    padding: 10px; /* Padding around the entire container */
    background-color: #f0f0f0; /* Light grey background for the container */
    border-radius: 5px; /* Rounded corners for the container */
    margin: 10px 0; /* Margin above and below the container */

}

.amazon-eta-field {
    padding: 5px 10px; /* Padding inside the input field */
    border: 1px solid #ccc; /* Light grey border for the input */
    border-radius: 4px; /* Slightly rounded corners for the input */
    font-size: 16px; /* Larger font size for better readability */
}

.amazon-eta-label {
    font-size: 14px; /* Font size for the label */
    color: #333; /* Dark grey color for the text */
    margin-right: 5px; /* Space between label and input field */
}

.amazon-copy-eta-button {
    background-color: #4CAF50; /* Green background */
    color: white; /* White text */
    padding: 5px 15px; /* Padding around the text */
    border: none; /* No border */
    border-radius: 4px; /* Rounded corners */
    cursor: pointer; /* Pointer cursor on hover */
    transition: background-color 0.3s; /* Smooth transition for background color */
}

.amazon-copy-eta-button:hover {
    background-color: #45a049; /* Slightly darker green on hover */
}

.amazon-copy-eta-button-clicked {
    background-color: #008CBA; /* Change to a different color when clicked */
}

/* .action-buttons {
    display: flex; 
    gap: 10px; 
    margin-top: 10px; 
} */


.fulfillmentDetails {
    font-family: 'Arial', sans-serif;
    color: #333;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    max-width: 600px;
    margin: 20px auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    text-align: left;
}

.fulfillmentDetails h2 {
    color: #007bff;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.fulfillmentDetails p {
    margin-bottom: 10px;
}

.fulfillmentDetails strong {
    color: #555;
    font-weight: normal;
    margin-right: 5px;
}

.fulfillmentDetails span {
    font-weight: bold;
}

/* Responsive design adjustments */
@media (max-width: 768px) {
    .fulfillmentDetails {
        padding: 15px;
        margin: 10px;
    }
}







.action-buttons-container {
    background-color: #f0f0f0; /* Light grey background */
    padding: 10px;
    margin-bottom: 20px;
}

.action-buttons-row, .clipboard-buttons-row {
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 10px; /* Space between buttons */
    margin-bottom: 10px; /* Space between rows */
}

.clipboard-label {
    font-size: 14px; /* Adjust as needed */
    color: #666; /* Dark grey for the main text */
    margin-right: 15px; /* Space between label and buttons */
    line-height: 1.4; /* Adjust line height for better readability */
    display: block; /* Ensure label takes the full width */
}

.clipboard-key {
    display: block; /* Ensure key is on a new line */
    font-size: 12px; /* Smaller font size for the key */
    color: #888; /* Lighter grey for less emphasis */
}


.action-buttons-container button, .action-buttons-row button, .clipboard-buttons-row button {
    background-color: #e0e0e0;
    border: none;
    padding: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.action-buttons-container button:hover, .action-buttons-row button:hover, .clipboard-buttons-row button:hover {
    background-color: #d0d0d0;
}

.action-buttons-container img, .action-buttons-row img, .clipboard-buttons-row img {
    width: 24px; /* Adjust based on your preference */
    height: 24px; /* Adjust based on your preference */
    margin-right: 8px; /* Space between icon and text */
}




/* auto order container  */

/* Container around the "Auto Order" button + gear icon */
.auto-order-container {
    display: inline-flex;
    align-items: center;
    position: relative;
    font-family: sans-serif;
  }
  
  /* The main button (styled as a div) */
  .auto-order-button {
    background-color: #007bff;
    color: #fff;
    padding: 6px 12px;
    margin-right: 8px;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    text-align: center;
  }
  
  /* The gear icon */
  .settings-icon {
    cursor: pointer;
    font-size: 18px;
    user-select: none;
  }
  
  /* The overlay for the modal */
  .settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
  }
  
  /* The modal content area */
  .settings-modal-content {
    background: #fff;
    width: 300px;
    margin: 15% auto;
    padding: 20px;
    position: relative;
    border-radius: 4px;
  }
  
  /* The close (x) button in the modal */
  .settings-modal-close {
    position: absolute;
    top: 8px;
    right: 8px;
    cursor: pointer;
    font-weight: bold;
    user-select: none;
  }
  


  .automation-div {
    display: flex; /* Use flexbox for alignment */
    white-space: nowrap; /* Prevent text from wrapping */


}

.auto-order-button:hover {
    background-color: #0056b3; /* Darker blue on hover */
}

.auto-order-button:active {
    background-color: #003366; /* Darker blue on click */
}

.auto-order-button:disabled {
    background-color: #ccc; /* Light grey for disabled state */
    cursor: not-allowed; /* Disable cursor */
}