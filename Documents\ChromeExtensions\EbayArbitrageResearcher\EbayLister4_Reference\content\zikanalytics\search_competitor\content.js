var a0_0x53d46c=a0_0x1467;(function(_0x291f9e,_0x1e6c25){var _0x3d533a=a0_0x1467,_0x445b44=_0x291f9e();while(!![]){try{var _0x2a5045=parseInt(_0x3d533a(0x133))/0x1+parseInt(_0x3d533a(0x138))/0x2+parseInt(_0x3d533a(0x139))/0x3+-parseInt(_0x3d533a(0x130))/0x4*(-parseInt(_0x3d533a(0x124))/0x5)+parseInt(_0x3d533a(0x12e))/0x6+-parseInt(_0x3d533a(0x126))/0x7+-parseInt(_0x3d533a(0x12b))/0x8;if(_0x2a5045===_0x1e6c25)break;else _0x445b44['push'](_0x445b44['shift']());}catch(_0x149aff){_0x445b44['push'](_0x445b44['shift']());}}}(a0_0x2f4e,0xe036e),console[a0_0x53d46c(0x128)]('content.js\x20loaded'));try{var searchSellerButton=searchSellerFromClipboardButton(),csSearchWrap=document[a0_0x53d46c(0x12a)](a0_0x53d46c(0x132));csSearchWrap[a0_0x53d46c(0x121)](searchSellerButton,csSearchWrap[a0_0x53d46c(0x135)][0x1]);}catch(a0_0x9b558){console[a0_0x53d46c(0x128)](a0_0x9b558);}function addSearchButtons(){var _0x246d11=a0_0x53d46c,_0x5b4e56=document[_0x246d11(0x12a)](_0x246d11(0x12c)),_0x4782b9=_0x5b4e56[_0x246d11(0x137)]('tr');for(var _0xf2b2d9=0x0;_0xf2b2d9<_0x4782b9[_0x246d11(0x125)];_0xf2b2d9++){var _0x46f9e7=_0x4782b9[_0xf2b2d9],_0x18e0c7=_0x46f9e7[_0x246d11(0x12a)](_0x246d11(0x122));if(!_0x18e0c7){var _0x13a8dc=createSearchAmazonButton(),_0x517ef9=_0x46f9e7[_0x246d11(0x12a)]('td');_0x517ef9&&_0x517ef9[_0x246d11(0x127)](_0x13a8dc);}}}function a0_0x2f4e(){var _0x2a1c14=['changeTimeout','9098598hnrkDm','childList','28YOIfxc','type','.compSearchWrap\x20.scWrap\x20.csSearchWrap','1737602sAJABi','tbody','children','remove','querySelectorAll','2036640FRWQkf','2298105UTkSiJ','insertBefore','.search-amazon-button','Changes\x20stopped','201430LirzLE','length','10457433YAvVIW','appendChild','log','observe','querySelector','23264728YAWVmh','#datatable-responsive\x20tbody'];a0_0x2f4e=function(){return _0x2a1c14;};return a0_0x2f4e();}function removeSearchButtons(){var _0x308dbe=a0_0x53d46c,_0x1e4307=document[_0x308dbe(0x12a)](_0x308dbe(0x12c)),_0x42d237=_0x1e4307[_0x308dbe(0x137)]('tr');for(var _0x1b7b42=0x0;_0x1b7b42<_0x42d237[_0x308dbe(0x125)];_0x1b7b42++){var _0x5b1c0e=_0x42d237[_0x1b7b42],_0x37a8e2=_0x5b1c0e['querySelector'](_0x308dbe(0x122));_0x37a8e2&&_0x37a8e2[_0x308dbe(0x136)]();}}async function observeDatatable(){return new Promise((_0x55223a,_0xe52385)=>{var _0xb4c05d=a0_0x1467;const _0x33a92f=document[_0xb4c05d(0x12a)]('#datatable-responsive'),_0x39abb1={'attributes':![],'childList':!![],'subtree':!![]},_0x22b6d2=new MutationObserver(_0x5af8cd=>{var _0x3d0822=_0xb4c05d;for(let _0x227501 of _0x5af8cd){if(_0x227501[_0x3d0822(0x131)]===_0x3d0822(0x12f)){const _0x44ea1b=_0x33a92f[_0x3d0822(0x12a)](_0x3d0822(0x134));_0x44ea1b&&(_0x22b6d2['disconnect'](),_0x55223a(_0x44ea1b));}}});_0x22b6d2[_0xb4c05d(0x129)](_0x33a92f,_0x39abb1);});}function observeTbody(_0x3addde){var _0x11dbba=a0_0x53d46c;const _0x49953b={'attributes':![],'childList':!![],'subtree':![]},_0x3de535=new MutationObserver(_0x555db5=>{var _0x544eaf=a0_0x1467;for(let _0xd35fa7 of _0x555db5){_0xd35fa7[_0x544eaf(0x131)]==='childList'&&(clearTimeout(window[_0x544eaf(0x12d)]),window[_0x544eaf(0x12d)]=setTimeout(()=>{myFunction();},0x1f4));}});_0x3de535[_0x11dbba(0x129)](_0x3addde,_0x49953b);}function a0_0x1467(_0x12afad,_0x3e6ed7){var _0x2f4e88=a0_0x2f4e();return a0_0x1467=function(_0x14675f,_0x1d3324){_0x14675f=_0x14675f-0x121;var _0x558117=_0x2f4e88[_0x14675f];return _0x558117;},a0_0x1467(_0x12afad,_0x3e6ed7);}function myFunction(){var _0x18fa91=a0_0x53d46c;console[_0x18fa91(0x128)](_0x18fa91(0x123)),addSearchButtons();}observeDatatable()['then'](_0x5770cf=>observeTbody(_0x5770cf)),observeSearchBtn();