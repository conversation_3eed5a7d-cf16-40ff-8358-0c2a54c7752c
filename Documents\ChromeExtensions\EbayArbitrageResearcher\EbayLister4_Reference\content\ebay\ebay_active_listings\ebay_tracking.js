var a0_0x41c80f=a0_0x2b08;function a0_0x2bdf(){var _0x45fcee=['local','continue_tracking','get','itemNode','isPageOpenedCorrectly','log','timeout\x20sending\x20message\x20to\x20background.js\x20to\x20continue\x20tracking','run_status','1673uDHaCq','tab','453wLgDEq','Page\x20did\x20not\x20open\x20correctly','From\x20the\x20extension\x20request.type\x20ebay.js','set','From\x20a\x20content\x20script:','itemNumber','tracking_completed','✅\x20Tracking\x20completed\x20for\x20all\x20pages','Page\x20opened\x20correctly:\x20','addListener','track_single_item','2133392pXRJxA','tracking_timeout','storage','21636QAUOwQ','startTrackingPage()\x20called','onMessage','doesNextPageExist:\x20','Tracking\x20','\x20-\x20Tracking','sendMessage','start_tracking','⏳\x20Waiting\x20for\x20','1548FGbaEX','makeTabActive','isActive','204477BAFytW','length','ebay_tracking.js\x20loaded','24aqjasB','13357060LUMPHX','2588679gKGUhC','type','title','6397310ECXPHX','tracker_run_status','\x20seconds\x20to\x20start\x20tracking\x20again','enable_continuous_tracking','timeout\x20run_status:\x20','then','runtime'];a0_0x2bdf=function(){return _0x45fcee;};return a0_0x2bdf();}(function(_0x2e7d67,_0x42e302){var _0x735002=a0_0x2b08,_0xb093af=_0x2e7d67();while(!![]){try{var _0x3af7f7=-parseInt(_0x735002(0x98))/0x1*(-parseInt(_0x735002(0xaf))/0x2)+parseInt(_0x735002(0xb2))/0x3*(-parseInt(_0x735002(0xb5))/0x4)+parseInt(_0x735002(0x87))/0x5+-parseInt(_0x735002(0xa6))/0x6*(-parseInt(_0x735002(0x96))/0x7)+parseInt(_0x735002(0xa3))/0x8+-parseInt(_0x735002(0xb7))/0x9+-parseInt(_0x735002(0xb6))/0xa;if(_0x3af7f7===_0x42e302)break;else _0xb093af['push'](_0xb093af['shift']());}catch(_0x1fea20){_0xb093af['push'](_0xb093af['shift']());}}}(a0_0x2bdf,0xb151d));var timeoutId;function a0_0x2b08(_0x3a61a1,_0x58aba6){var _0x2bdfe5=a0_0x2bdf();return a0_0x2b08=function(_0x2b0841,_0x5acc0c){_0x2b0841=_0x2b0841-0x87;var _0x40a2ef=_0x2bdfe5[_0x2b0841];return _0x40a2ef;},a0_0x2b08(_0x3a61a1,_0x58aba6);}console[a0_0x41c80f(0x93)](a0_0x41c80f(0xb4)),chrome[a0_0x41c80f(0x8d)][a0_0x41c80f(0xa8)][a0_0x41c80f(0xa1)]((_0x40f6da,_0x3b3f81,_0xbb155c)=>{var _0x3cde44=a0_0x41c80f;console[_0x3cde44(0x93)](_0x3b3f81[_0x3cde44(0x97)]?_0x3cde44(0x9c)+_0x3b3f81[_0x3cde44(0x97)]['url']:_0x3cde44(0x9a)+_0x40f6da[_0x3cde44(0xb8)]);if(_0x40f6da[_0x3cde44(0xb8)]==='start_tracking_page'){console[_0x3cde44(0x93)](_0x3cde44(0xad)),timeoutId=setTimeout(()=>{var _0x4fcd8c=_0x3cde44;chrome[_0x4fcd8c(0xa5)][_0x4fcd8c(0x8e)][_0x4fcd8c(0x90)](_0x4fcd8c(0x95),_0x3c5a3b=>{var _0x473dfe=_0x4fcd8c;console[_0x473dfe(0x93)](_0x473dfe(0x8b)+_0x3c5a3b[_0x473dfe(0x95)]),_0x3c5a3b[_0x473dfe(0x95)]&&(console['log'](_0x473dfe(0x94)),chrome[_0x473dfe(0x8d)][_0x473dfe(0xac)]({'type':_0x473dfe(0x8f)}));});},0x1*0x3c*0x3e8);var _0x211ba0=checkIfPageLoadedProperly();return _0xbb155c({'type':_0x3cde44(0x92),'isPageOpenedCorrectly':_0x211ba0}),console[_0x3cde44(0x93)](_0x3cde44(0x92),_0x211ba0),document[_0x3cde44(0xb9)]=_0x3cde44(0xa0)+_0x211ba0,_0x211ba0?startTrackingPage():console[_0x3cde44(0x93)](_0x3cde44(0x99)),!![];}if(_0x40f6da[_0x3cde44(0xb8)]===_0x3cde44(0xa2)){console[_0x3cde44(0x93)](_0x3cde44(0xa2));var _0x1c53d4=_0x40f6da[_0x3cde44(0x9d)],_0x55c5aa=getItemNodeByItemNumber(_0x1c53d4);trackItem(_0x55c5aa)[_0x3cde44(0x8c)](()=>{var _0x9576c3=_0x3cde44;_0xbb155c({'type':_0x9576c3(0x9e)});});}});async function startTrackingPage(){var _0x2c9409=a0_0x41c80f;console[_0x2c9409(0x93)](_0x2c9409(0xa7)),changeFaviconToSniper(),clearTimeout(timeoutId);var {stay_on_ebay_page:_0x369bcb}=await chrome[_0x2c9409(0xa5)]['local'][_0x2c9409(0x90)]('stay_on_ebay_page');_0x369bcb&&(intervalToMakeTabActive=setInterval(function(){var _0x336b6c=_0x2c9409;chrome['runtime'][_0x336b6c(0xac)]({'type':_0x336b6c(0xb0)});},0x1388));var _0x32ea9=getCurrentPgNumber(),_0x41c488=getTotalPgNumber();await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)][_0x2c9409(0x9b)]({'totalPageNumber':_0x41c488});var {enable_continuous_tracking:_0x2467dc}=await chrome['storage'][_0x2c9409(0x8e)][_0x2c9409(0x90)](_0x2c9409(0x8a)),{tracking_timeout:_0x3d8ca7}=await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)][_0x2c9409(0x90)](_0x2c9409(0xa4));_0x3d8ca7=Number(_0x3d8ca7);if(_0x32ea9>_0x41c488){await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)]['set']({'page_number':0x0});_0x2467dc&&(await new Promise(_0x90b2c3=>setTimeout(_0x90b2c3,_0x3d8ca7*0x3e8)),chrome[_0x2c9409(0x8d)][_0x2c9409(0xac)]({'type':'continue_tracking'}));return;}var _0x45cc55=getItemNodes();if(_0x45cc55[_0x2c9409(0xb3)]==0x0){console[_0x2c9409(0x93)]('Page\x20failed\x20to\x20load,\x20refreshing\x20page'),await new Promise(_0x150293=>setTimeout(_0x150293,0xa*0x3e8)),chrome[_0x2c9409(0x8d)]['sendMessage']({'type':'continue_tracking'});return;}var {current_tracker_position:_0x295114}=await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)][_0x2c9409(0x90)]('current_tracker_position');!_0x295114&&(_0x295114=0x1);for(let _0x4c01ed=_0x295114-0x1;_0x4c01ed<_0x45cc55[_0x2c9409(0xb3)];_0x4c01ed++){var {tracker_run_status:_0x9463d0}=await chrome['storage'][_0x2c9409(0x8e)][_0x2c9409(0x90)](_0x2c9409(0x88));if(!_0x9463d0){console[_0x2c9409(0x93)]('tracker_run_status\x20is\x20false,\x20stopping\x20tracking'),document[_0x2c9409(0xb9)]='\x20🚫\x20Tracker\x20Aborted';return;}var _0x3e71ad=_0x45cc55[_0x4c01ed];document[_0x2c9409(0xb9)]=_0x4c01ed+0x1+'/'+_0x45cc55[_0x2c9409(0xb3)]+_0x2c9409(0xab),console['log'](_0x2c9409(0x91),_0x3e71ad),showRadar[_0x2c9409(0xb1)]&&showRadar['updateText'](_0x2c9409(0xaa)+(_0x4c01ed+0x1)+'/'+_0x45cc55['length']),await trackItem(_0x3e71ad),await chrome['storage']['local'][_0x2c9409(0x9b)]({'current_tracker_position':_0x4c01ed+0x2}),chrome[_0x2c9409(0x8d)][_0x2c9409(0xac)]({'type':'update_tracker_position','page_number':_0x32ea9,'position_in_page':_0x4c01ed+0x1,'total_pages':_0x41c488,'total_items_in_page':_0x45cc55[_0x2c9409(0xb3)]});}var _0x4d8f97=await doesNextPageExistFunc();console[_0x2c9409(0x93)](_0x2c9409(0xa9)+_0x4d8f97),_0x4d8f97?(await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)]['set']({'page_number':_0x32ea9+0x1}),await chrome['storage'][_0x2c9409(0x8e)][_0x2c9409(0x9b)]({'current_tracker_position':0x1}),chrome[_0x2c9409(0x8d)]['sendMessage']({'type':_0x2c9409(0x8f)})):(await chrome[_0x2c9409(0xa5)]['local'][_0x2c9409(0x9b)]({'page_number':0x0}),await chrome[_0x2c9409(0xa5)][_0x2c9409(0x8e)][_0x2c9409(0x9b)]({'current_tracker_position':0x1}),document['title']=_0x2c9409(0x9f),_0x2467dc&&(document[_0x2c9409(0xb9)]=_0x2c9409(0xae)+_0x3d8ca7+_0x2c9409(0x89),await new Promise(_0x3c9f4c=>setTimeout(_0x3c9f4c,_0x3d8ca7*0x3e8)),chrome['runtime']['sendMessage']({'type':'continue_tracking'})));};