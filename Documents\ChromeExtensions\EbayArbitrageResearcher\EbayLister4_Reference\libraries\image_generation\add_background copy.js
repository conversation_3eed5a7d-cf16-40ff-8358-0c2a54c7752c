function a0_0x4f14(_0x2caa85,_0x1eada8){var _0x398f87=a0_0x398f();return a0_0x4f14=function(_0x4f14eb,_0x4960a2){_0x4f14eb=_0x4f14eb-0x129;var _0x179156=_0x398f87[_0x4f14eb];return _0x179156;},a0_0x4f14(_0x2caa85,_0x1eada8);}(function(_0x14eb96,_0x25e753){var _0x299cf4=a0_0x4f14,_0x51b3fd=_0x14eb96();while(!![]){try{var _0x137164=parseInt(_0x299cf4(0x159))/0x1+parseInt(_0x299cf4(0x130))/0x2+parseInt(_0x299cf4(0x136))/0x3*(-parseInt(_0x299cf4(0x14d))/0x4)+-parseInt(_0x299cf4(0x12c))/0x5+parseInt(_0x299cf4(0x138))/0x6+parseInt(_0x299cf4(0x142))/0x7+parseInt(_0x299cf4(0x144))/0x8*(-parseInt(_0x299cf4(0x14c))/0x9);if(_0x137164===_0x25e753)break;else _0x51b3fd['push'](_0x51b3fd['shift']());}catch(_0x331882){_0x51b3fd['push'](_0x51b3fd['shift']());}}}(a0_0x398f,0x6396d));function createGenerateImageDiv(){var _0x1b2ece=a0_0x4f14,_0x7e399=document[_0x1b2ece(0x14e)]('span');_0x7e399['id']='generate-image-div';var _0x5936b7=document[_0x1b2ece(0x14e)](_0x1b2ece(0x13f));_0x5936b7['id']=_0x1b2ece(0x149),_0x5936b7[_0x1b2ece(0x157)]['add'](_0x1b2ece(0x149)),_0x5936b7[_0x1b2ece(0x139)]=_0x1b2ece(0x152),_0x5936b7[_0x1b2ece(0x145)]=async function(){var _0x37c2c3=_0x1b2ece;_0x5936b7[_0x37c2c3(0x139)]=_0x37c2c3(0x14f),_0x5936b7[_0x37c2c3(0x157)][_0x37c2c3(0x12e)](_0x37c2c3(0x156));var _0x333216=document[_0x37c2c3(0x154)](_0x37c2c3(0x151))[_0x37c2c3(0x135)];console[_0x37c2c3(0x13c)](_0x37c2c3(0x133),_0x333216);var _0x4d6582=product_data[_0x37c2c3(0x13b)][0x0],_0x253b2c=await urlToImage(_0x4d6582);_0x253b2c=await compressImageToUnder50kb(_0x253b2c);var _0x25370d=_0x253b2c[_0x37c2c3(0x146)],_0x13ca90=getCategories(),_0x12ad52=_0x13ca90[_0x13ca90[_0x37c2c3(0x140)]-0x1];console[_0x37c2c3(0x13c)](_0x37c2c3(0x14b),_0x25370d),_0x25370d=_0x25370d[_0x37c2c3(0x12a)]('data:image/jpeg;base64,','');var _0x20c023=await postToServer('http://localhost:3333/generate-images',{'prompt':_0x333216,'image':_0x25370d,'imageMaskPrompt':_0x12ad52});console[_0x37c2c3(0x13c)](_0x37c2c3(0x148),_0x20c023);if(_0x20c023[_0x37c2c3(0x147)]){if(_0x20c023[_0x37c2c3(0x147)]['includes'](_0x37c2c3(0x129))){var _0xf91c6c=_0x13ca90[_0x13ca90['length']-0x2];_0x20c023=await postToServer('http://localhost:3333/generate-images',{'prompt':_0x333216,'image':_0x25370d,'imageMaskPrompt':_0xf91c6c});}}var _0x19345d=_0x20c023[_0x37c2c3(0x12f)];_0x5936b7['classList'][_0x37c2c3(0x12d)]('generate-image-button-working'),_0x5936b7[_0x37c2c3(0x157)][_0x37c2c3(0x12e)]('generate-image-button-success'),_0x5936b7[_0x37c2c3(0x139)]=_0x37c2c3(0x131),setTimeout(()=>{var _0x5d7df6=_0x37c2c3;_0x5936b7['classList'][_0x5d7df6(0x12d)](_0x5d7df6(0x158)),_0x5936b7[_0x5d7df6(0x139)]='Generate\x20Image';},0xbb8);for(var _0x4e1b0b=0x0;_0x4e1b0b<_0x19345d[_0x37c2c3(0x140)];_0x4e1b0b++){var _0xd95973='data:image/jpeg;base64,'+_0x19345d[_0x4e1b0b];createImageAndAppendBase64(_0xd95973);}console[_0x37c2c3(0x13c)]('clicked\x20button');},_0x7e399[_0x1b2ece(0x14a)](_0x5936b7);var _0x59a86c=document['createElement'](_0x1b2ece(0x132));return _0x59a86c['id']=_0x1b2ece(0x151),_0x59a86c[_0x1b2ece(0x150)]='5',_0x59a86c[_0x1b2ece(0x153)]='3',_0x59a86c[_0x1b2ece(0x135)]=_0x1b2ece(0x141),_0x7e399[_0x1b2ece(0x14a)](_0x59a86c),_0x7e399;}function compressImageToUnder50kb(_0x58370d){return new Promise((_0x1a30bc,_0x1a4ca2)=>{var _0x1c4988=a0_0x4f14;const _0x3391ab=0x32*0x400;let _0x1d12bf=document[_0x1c4988(0x14e)]('canvas'),_0x2651be=_0x1d12bf[_0x1c4988(0x137)]('2d'),_0x54e103=0.8;_0x1d12bf[_0x1c4988(0x134)]=_0x58370d[_0x1c4988(0x134)],_0x1d12bf[_0x1c4988(0x13a)]=_0x58370d[_0x1c4988(0x13a)];let _0x4a94da=0.95,_0x5eda78=null;function _0x43736c(){var _0x1968bd=_0x1c4988;_0x2651be[_0x1968bd(0x143)](0x0,0x0,_0x1d12bf[_0x1968bd(0x134)],_0x1d12bf[_0x1968bd(0x13a)]),_0x1d12bf[_0x1968bd(0x134)]=_0x58370d[_0x1968bd(0x134)]*_0x4a94da,_0x1d12bf[_0x1968bd(0x13a)]=_0x58370d['height']*_0x4a94da,_0x2651be[_0x1968bd(0x12b)](_0x58370d,0x0,0x0,_0x1d12bf[_0x1968bd(0x134)],_0x1d12bf['height']);var _0x5a6f9c=_0x1d12bf[_0x1968bd(0x155)](_0x1968bd(0x13e),_0x54e103);if(_0x5a6f9c[_0x1968bd(0x140)]<_0x3391ab){let _0x33d393=new Image();_0x33d393['onload']=()=>_0x1a30bc(_0x33d393),_0x33d393['src']=_0x5a6f9c;return;}else{if(_0x54e103>0.1)_0x54e103-=0.05;else{if(_0x4a94da>0.5)_0x4a94da-=0.05,_0x54e103=0.8;else{if(_0x5eda78){let _0x1794a2=new Image();_0x1794a2['onload']=()=>_0x1a30bc(_0x1794a2),_0x1794a2['src']=_0x5eda78;return;}else{_0x1a4ca2(new Error(_0x1968bd(0x13d)));return;}}}_0x5eda78=_0x5a6f9c,_0x43736c();}}_0x43736c();});}function a0_0x398f(){var _0x3fa486=['generate-image-button-success','324601vBfQVW','Unable\x20to\x20auto-generate\x20mask\x20image','replace','drawImage','3662625rhlyYF','remove','add','images','663936TITPnZ','Image\x20Generated!','textarea','backgroundDescription:\x20','width','value','3RIQvjb','getContext','2934552sHtaUq','textContent','height','main_hd_images','log','Could\x20not\x20compress\x20image\x20to\x20less\x20than\x2050KB.','image/jpeg','button','length','luxurious\x20spa\x20bathroom\x20setting\x20with\x20clean\x20and\x20serene\x20atmosphere','1586025GMKKCa','clearRect','112ODrObX','onclick','src','error','response:\x20','generate-image-button','appendChild','base64Image:\x20','138042ZLTFfe','68248JNCyjc','createElement','Generating\x20Image...','cols','background-description','Generate\x20Image','rows','getElementById','toDataURL','generate-image-button-working','classList'];a0_0x398f=function(){return _0x3fa486;};return a0_0x398f();}