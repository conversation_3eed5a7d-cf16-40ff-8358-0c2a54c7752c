const a0_0x55d78c=a0_0x483e;(function(_0x525bfc,_0x5b3e14){const _0x5d1eb1=a0_0x483e,_0x2e8a00=_0x525bfc();while(!![]){try{const _0x1d2033=parseInt(_0x5d1eb1(0xc0))/0x1*(-parseInt(_0x5d1eb1(0xa1))/0x2)+-parseInt(_0x5d1eb1(0xb6))/0x3*(parseInt(_0x5d1eb1(0xdd))/0x4)+-parseInt(_0x5d1eb1(0xae))/0x5+-parseInt(_0x5d1eb1(0xa4))/0x6+parseInt(_0x5d1eb1(0xd2))/0x7*(parseInt(_0x5d1eb1(0xd1))/0x8)+parseInt(_0x5d1eb1(0xd5))/0x9*(-parseInt(_0x5d1eb1(0xcd))/0xa)+parseInt(_0x5d1eb1(0xe8))/0xb;if(_0x1d2033===_0x5b3e14)break;else _0x2e8a00['push'](_0x2e8a00['shift']());}catch(_0x245439){_0x2e8a00['push'](_0x2e8a00['shift']());}}}(a0_0x501f,0xb6964),console[a0_0x55d78c(0xad)](a0_0x55d78c(0xbc)));function a0_0x483e(_0x20bbcb,_0x3afb4e){const _0x501fbc=a0_0x501f();return a0_0x483e=function(_0x483ec1,_0x2f72d1){_0x483ec1=_0x483ec1-0x9a;let _0x1ddb02=_0x501fbc[_0x483ec1];return _0x1ddb02;},a0_0x483e(_0x20bbcb,_0x3afb4e);}var EbayApi={async 'printTest'(){const _0x223cc7=a0_0x55d78c;console['log'](_0x223cc7(0xe2));},async 'getCategoryRecommendations'(_0x1a59a1){const _0x4c4b73=a0_0x55d78c,_0x53ed23=getCurrentEbaySuffix(),_0x4fc49c=_0x4c4b73(0xb8)+_0x53ed23+_0x4c4b73(0xca)+encodeURIComponent(_0x1a59a1),_0x19728e=await fetch(_0x4fc49c,{'method':'GET','credentials':'include'});if(!_0x19728e['ok'])throw new Error('Failed\x20to\x20get\x20category\x20recommendations.\x20Status:\x20'+_0x19728e['status']);return await _0x19728e[_0x4c4b73(0xd9)]();},async 'createListing'(_0x56d358,_0x10a3fd){const _0x26df41=a0_0x55d78c,_0x4c4609=getCurrentEbaySuffix(),_0x5c31bc=new URLSearchParams();_0x5c31bc[_0x26df41(0xb1)](_0x26df41(0xb4),_0x26df41(0xc6)),_0x5c31bc[_0x26df41(0xb1)]('categoryId',_0x10a3fd[_0x26df41(0xdf)]()),_0x5c31bc[_0x26df41(0xb1)]('title',_0x56d358),_0x5c31bc[_0x26df41(0xb1)](_0x26df41(0xd6),'1000');const _0x233d90='https://www.ebay.'+_0x4c4609+_0x26df41(0xe1)+_0x5c31bc['toString'](),_0x314c83=await fetch(_0x233d90,{'method':_0x26df41(0xea),'credentials':_0x26df41(0xb9)});if(!_0x314c83['ok'])throw new Error(_0x26df41(0xce)+_0x314c83[_0x26df41(0xde)]);console[_0x26df41(0xad)]('response\x20createListing',_0x314c83);var _0x272ceb=await _0x314c83['text']();return parseSessionData(_0x272ceb);},async 'updateListing'(_0x2ec10a,_0x25dac6,_0x38e1a9,_0x3b2b21,_0x1fc141){const _0x1694bd=a0_0x55d78c,_0x23cdce=getCurrentEbaySuffix(),_0x217baf='https://www.ebay.'+_0x23cdce+_0x1694bd(0xc2)+_0x2ec10a+_0x1694bd(0xbb),_0x3c887c=await fetch(_0x217baf,{'method':_0x1694bd(0xa9),'credentials':_0x1694bd(0xb9),'headers':{'Content-Type':_0x1694bd(0xc3),'srt':_0x25dac6},'body':JSON[_0x1694bd(0xe9)](_0x1fc141)});if(!_0x3c887c['ok'])throw new Error(_0x1694bd(0xd0)+_0x3c887c[_0x1694bd(0xde)]);const _0x55d51f=await _0x3c887c[_0x1694bd(0xd9)]();if(_0x55d51f&&_0x55d51f[_0x1694bd(0x9a)])throw new Error(_0x1694bd(0xa0)+_0x55d51f[_0x1694bd(0x9a)]);return _0x55d51f;},async 'addVariations'(_0x503814,_0x2a1f0f,_0x4bd55c,_0x12b83a){const _0x16d57d=a0_0x55d78c,_0x167a2f=getCurrentEbaySuffix(),_0x3fd617=_0x4bd55c[_0x16d57d(0xe6)][_0x16d57d(0xaa)]((_0x38b242,_0x173cbf)=>{const _0x11204a=_0x16d57d;let _0x294542={};return Object[_0x11204a(0xb3)](_0x38b242[_0x11204a(0xa5)])[_0x11204a(0xed)](_0x3ef6d9=>{const _0xe57766=_0x11204a;_0x294542[_0x3ef6d9]=_0x38b242[_0xe57766(0xa5)][_0x3ef6d9][_0xe57766(0xd4)];}),{'index':_0x173cbf,'variationSpecific':_0x294542,'listingVariation':{'price':_0x38b242[_0x11204a(0xba)][_0x11204a(0xc8)](0x2),'quantity':_0x38b242[_0x11204a(0xa2)]||0x1},'state':_0x11204a(0xb5)};}),_0x1d15f6={'action':_0x16d57d(0xa3),'draftId':_0x503814,'listingMode':_0x16d57d(0xc6),'variationItem':_0x3fd617},_0x1f23fa='https://bulkedit.ebay.'+_0x167a2f+_0x16d57d(0xee),_0x58cb61=await fetch(_0x1f23fa,{'method':_0x16d57d(0x9d),'credentials':'include','body':JSON[_0x16d57d(0xe9)](_0x1d15f6)});if(!_0x58cb61['ok'])throw new Error(_0x16d57d(0xbd)+_0x58cb61[_0x16d57d(0xde)]);const _0x554316=await _0x58cb61['json']();if(_0x554316&&_0x554316[_0x16d57d(0x9a)])throw new Error('Error\x20from\x20the\x20msku-update\x20endpoint:\x20'+_0x554316[_0x16d57d(0x9a)]);},async 'uploadPhotoToEbay'(_0x53ff55,_0x5b4a2c){const _0x1bdf04=a0_0x55d78c,_0x17543a=new FormData();console[_0x1bdf04(0xad)](_0x1bdf04(0xda),_0x5b4a2c),console[_0x1bdf04(0xad)]('imageUrl',_0x53ff55),_0x17543a[_0x1bdf04(0xb1)]('s','SuperSize'),_0x17543a['append']('n','i'),_0x17543a[_0x1bdf04(0xb1)]('v','2'),_0x17543a[_0x1bdf04(0xb1)]('uaek',_0x5b4a2c[_0x1bdf04(0xb2)]),_0x17543a['append'](_0x1bdf04(0xab),_0x5b4a2c[_0x1bdf04(0xab)]),_0x17543a['append'](_0x1bdf04(0x9c),'2'),_0x17543a[_0x1bdf04(0xb1)]('wm',''),_0x17543a[_0x1bdf04(0xb1)]('w',_0x53ff55);const _0x3ceb3d=await fetch(_0x1bdf04(0xb7),{'method':_0x1bdf04(0x9d),'body':_0x17543a});console['log'](_0x1bdf04(0xe7),_0x3ceb3d);const _0xa225b8=(await _0x3ceb3d[_0x1bdf04(0xcb)]())['split'](';');console[_0x1bdf04(0xad)](_0x1bdf04(0xcb),_0xa225b8);if(_0xa225b8['length']<0x2)throw new Error(_0x1bdf04(0xcf));if(_0xa225b8[0x1]===_0x1bdf04(0xd8))throw new Error(_0x1bdf04(0xdc));return _0xa225b8[0x1];},async 'uploadImageFileToEbay'(_0xd70c9e,_0x3d84be){const _0x5d2386=a0_0x55d78c,_0x2c1978=new FormData();_0x2c1978['append']('s',_0x5d2386(0xec)),_0x2c1978[_0x5d2386(0xb1)]('n','i'),_0x2c1978['append']('v','2'),_0x2c1978[_0x5d2386(0xb1)](_0x5d2386(0xb2),_0x3d84be[_0x5d2386(0xb2)]),_0x2c1978[_0x5d2386(0xb1)](_0x5d2386(0xab),_0x3d84be['uaes']),_0x2c1978[_0x5d2386(0xb1)](_0x5d2386(0x9c),'2'),_0x2c1978[_0x5d2386(0xb1)]('wm',''),_0x2c1978[_0x5d2386(0xb1)]('i',_0xd70c9e);var _0x3d6aa7=await fetch(_0x5d2386(0xb7),{'method':_0x5d2386(0x9d),'body':_0x2c1978});console[_0x5d2386(0xad)](_0x5d2386(0xe7),_0x3d6aa7);const _0x2dfce7=(await _0x3d6aa7['text']())['split'](';');console[_0x5d2386(0xad)](_0x5d2386(0xcb),_0x2dfce7);if(_0x2dfce7['length']<0x2)throw new Error(_0x5d2386(0xcf));if(_0x2dfce7[0x1]===_0x5d2386(0xd8))throw new Error(_0x5d2386(0xdc));return _0x2dfce7[0x1];}};function getCurrentEbaySuffix(){const _0xb521f6=a0_0x55d78c;if(window[_0xb521f6(0xe5)][_0xb521f6(0xac)][_0xb521f6(0x9e)](_0xb521f6(0xa6)))return _0xb521f6(0xe4);if(window[_0xb521f6(0xe5)][_0xb521f6(0xac)][_0xb521f6(0x9e)]('ebay.com.au'))return _0xb521f6(0xaf);return _0xb521f6(0x9b);}function extractMatches(_0x3e39fb,_0xc17a59){const _0x36d53e=a0_0x55d78c;let _0x2f1b59,_0x16b0a6=[];while((_0x2f1b59=_0x3e39fb[_0x36d53e(0xb0)](_0xc17a59))!==null){_0x2f1b59[_0x36d53e(0xd7)]===_0x3e39fb['lastIndex']&&_0x3e39fb[_0x36d53e(0xc4)]++,_0x2f1b59[_0x36d53e(0xed)]((_0x529020,_0x166096)=>{const _0x4fef6a=_0x36d53e;_0x16b0a6[_0x4fef6a(0xa7)](_0x529020);});}return _0x16b0a6;}function a0_0x501f(){const _0x15f151=['log','5474145IHJKum','com.au','exec','append','uaek','keys','mode','enabled','171ZawkqY','https://msa-b1.ebay.com/ws/eBayISAPI.dll?EpsBasic','https://www.ebay.','include','price','?mode=AddItem','api.js\x20loaded','Failed\x20to\x20add\x20variations.\x20Status:\x20','draftId:','findIndex','6298cuVtmJ','replace','/lstng/api/listing_draft/','application/json;charset=UTF-8','lastIndex','Could\x20not\x20locate\x20the\x20portion\x20containing\x20draftId\x20or\x20csrf','AddItem','widgetConfig','toFixed','model','/sl/prelist/api/category/search?keyword=','text','parsed','2904410FCBGVV','Failed\x20to\x20create\x20listing.\x20Status:\x20','Photo\x20upload\x20failed:\x20no\x20valid\x20response\x20from\x20eBay','Failed\x20to\x20update\x20the\x20listing.\x20Status:\x20','8800096QYofKy','7SExWxV','draftId','productName','27iRMuUV','condition','index','ERROR:WC002','json','sessionData','csrf','Photo\x20upload\x20error:\x20eBay\x20returned\x20WC002','59980IkEvOS','status','toString','epsData','/lstng?','printTest\x20function','parse','co.uk','location','prod_variations','response','36096533dbhPBg','stringify','GET','Draft\x20ID:','SuperSize','forEach','/msku-update','error','com','aXRequest','POST','includes','results','eBay\x20responded\x20with\x20an\x20error:\x20','138krBOyR','quantity','save','2269260LPtdHp','attrs','ebay.co.uk','push','APPSTATUS','PUT','map','uaes','host'];a0_0x501f=function(){return _0x15f151;};return a0_0x501f();}function parseSessionData(_0x4fbd02){const _0x310f9a=a0_0x55d78c;var _0x30ad21=extractMatches(/(?<=\.concat\()[\s\S]*?(?=<\/script>)/gi,_0x4fbd02);console[_0x310f9a(0xad)](_0x310f9a(0x9f),_0x30ad21);var _0x2e2ccb=_0x30ad21[_0x310f9a(0xbf)](_0x3f34c9=>_0x3f34c9[_0x310f9a(0x9e)](_0x310f9a(0xa8))&&_0x3f34c9[_0x310f9a(0x9e)](_0x310f9a(0xc7)));if(_0x2e2ccb<0x0)throw new Error(_0x310f9a(0xc5));var _0x240655=_0x30ad21[_0x2e2ccb][_0x310f9a(0xc1)](/\)(?=[^\)]*$)/,''),_0x964d8b=JSON['parse'](_0x240655);console[_0x310f9a(0xad)](_0x310f9a(0xcc),_0x964d8b);var _0x3b0314=_0x964d8b['o']['w'][0x0][0x2]['model']['APPSTATUS'][_0x310f9a(0xc7)][_0x310f9a(0xdb)]['/lstng/api/listing_draft/:draftId(\x5cd+)'];console[_0x310f9a(0xad)]('CSRF\x20token:',_0x3b0314);var _0x1e3dae=_0x964d8b['o']['w'][0x0][0x2][_0x310f9a(0xc9)][_0x310f9a(0xa8)]['widgetConfig']['epsData'][_0x310f9a(0xab)],_0x259ca1=_0x964d8b['o']['w'][0x0][0x2][_0x310f9a(0xc9)][_0x310f9a(0xa8)]['widgetConfig'][_0x310f9a(0xe0)][_0x310f9a(0xb2)],_0x2e2ccb=_0x30ad21[_0x310f9a(0xbf)](_0x152a4c=>_0x152a4c[_0x310f9a(0x9e)](_0x310f9a(0xbe)));if(_0x2e2ccb<0x0)throw new Error(_0x310f9a(0xc5));var _0x1c42ab=_0x30ad21[_0x2e2ccb][_0x310f9a(0xc1)](/\)(?=[^\)]*$)/,''),_0x7c80c6=JSON[_0x310f9a(0xe3)](_0x1c42ab);console[_0x310f9a(0xad)]('parsedDraftId',_0x7c80c6);var _0x5b77ec;try{_0x5b77ec=_0x7c80c6['w'][0x0][0x2][_0x310f9a(0xc9)]['meta'][_0x310f9a(0xd3)];}catch(_0x1bb3e2){console[_0x310f9a(0xad)](_0x310f9a(0x9a),_0x1bb3e2);}if(!_0x5b77ec)try{_0x5b77ec=_0x7c80c6['o']['w'][0x0][0x2][_0x310f9a(0xc9)]['meta'][_0x310f9a(0xd3)];}catch(_0xd14666){console[_0x310f9a(0xad)]('error',_0xd14666);}return console[_0x310f9a(0xad)](_0x310f9a(0xeb),_0x5b77ec),{'draftId':_0x5b77ec,'csrf':_0x3b0314,'uaes':_0x1e3dae,'uaek':_0x259ca1};}