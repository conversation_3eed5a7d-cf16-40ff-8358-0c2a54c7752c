body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1 {
    text-align: center;
}

#controls {
    text-align: center;
    margin-bottom: 20px;
}

#clear_logs {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
}

#logs_table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

#logs_table th, #logs_table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    vertical-align: middle;
}

#logs_table th {
    background-color: #f2f2f2;
}

#logs_table tr:nth-child(even) {
    background-color: #f9f9f9;
}

#logs_table tr:hover {
    background-color: #e9e9e9;
}

button {
    padding: 5px 10px;
    cursor: pointer;
}

#logs_table img {
    max-width: 50px;
    height: auto;
}

#logs_table a {
    color: #1a0dab;
    text-decoration: none;
}

#logs_table a:hover {
    text-decoration: underline;
}












/* Add this CSS to your stylesheet or within a <style> tag in your HTML */

.tracking-message {
    font-family: Arial, sans-serif;
    line-height: 1.5;
  }
  
  .status-error {
    color: #B22222; /* Firebrick Red */
    font-size: 1.2em;
    margin-bottom: 8px;
  }
  
  .status-reasons {
    color: #1E90FF; /* Dodger Blue */
    margin-bottom: 4px;
  }
  
  .status-availability {
    color: #228B22; /* Forest Green */
  }
  
  .status-error strong {
    font-weight: bold;
  }
  
  .status-reasons em,
  .status-availability em {
    font-style: italic;
    font-weight: bold;
  }
  