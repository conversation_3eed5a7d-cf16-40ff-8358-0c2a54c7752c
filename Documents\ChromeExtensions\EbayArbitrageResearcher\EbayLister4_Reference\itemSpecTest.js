var a0_0x4e5648=a0_0xecad;(function(_0x3e1c6d,_0x18290f){var _0x5b8938=a0_0xecad,_0x52b037=_0x3e1c6d();while(!![]){try{var _0x3f9bad=-parseInt(_0x5b8938(0x19c))/0x1*(parseInt(_0x5b8938(0x19d))/0x2)+parseInt(_0x5b8938(0x186))/0x3*(parseInt(_0x5b8938(0x15a))/0x4)+parseInt(_0x5b8938(0x124))/0x5+parseInt(_0x5b8938(0xe2))/0x6*(parseInt(_0x5b8938(0xd6))/0x7)+parseInt(_0x5b8938(0x150))/0x8+-parseInt(_0x5b8938(0x129))/0x9*(-parseInt(_0x5b8938(0xdd))/0xa)+-parseInt(_0x5b8938(0xef))/0xb;if(_0x3f9bad===_0x18290f)break;else _0x52b037['push'](_0x52b037['shift']());}catch(_0x3923be){_0x52b037['push'](_0x52b037['shift']());}}}(a0_0x3171,0xea09d));var waitTime=0x12c;function getItemSpecificFieldsWithProperties(_0x1378ba=a0_0x4e5648(0x115)){var _0x563562=getItemSpecificFields(_0x1378ba),_0x566a52=getSingleSelectItemSpecificFields(_0x563562),_0x4223d9=getInputRequiredItemSpecificFields(_0x563562),_0x3bd76e=getMultiSelectItemSpecificFields(_0x563562),_0x3ef4d9=getSingleSelectWithLimitedOptionsItemSpecificFields(_0x563562),_0x22ce82=getSingleSelectWithNoSearchItemSpecificFields(_0x563562),_0x156264=getMultiSelectWithNoSearchItemSpecificFields(_0x563562),_0x7f976={'singleSelectItemSpecificFields':_0x566a52,'inputRequiredItemSpecificFields':_0x4223d9,'multiSelectItemSpecificFields':_0x3bd76e,'singleSelectWithLimitedOptionsItemSpecificFields':_0x3ef4d9,'singleSelectWithNoSearchItemSpecificFields':_0x22ce82,'multiSelectWithNoSearchItemSpecificFields':_0x156264};return _0x7f976;}function checkValueExistsInItemSpecificField(_0x501e7d,_0x518c9d){var _0xbc8e8c=a0_0x4e5648,_0x71261b=_0x501e7d['querySelector'](_0xbc8e8c(0xd3));if(_0x71261b&&_0x71261b[_0xbc8e8c(0x18c)])return!![];var _0x414b64=_0x501e7d[_0xbc8e8c(0x136)]('.summary__attributes--value\x20input');if(_0x414b64&&_0x414b64[_0xbc8e8c(0x16a)])return!![];return![];}function getTotalFilledItemSpecificFields(){var _0x29c81c=a0_0x4e5648,_0x48525a=getItemSpecificFields(_0x29c81c(0x191)),_0x265cd0=0x0;for(var _0x5d34c0=0x0;_0x5d34c0<_0x48525a[_0x29c81c(0xfb)];_0x5d34c0++){checkValueExistsInItemSpecificField(_0x48525a[_0x5d34c0])?_0x265cd0++:console[_0x29c81c(0xdc)]('value\x20does\x20not\x20exist\x20in\x20itemSpecificField',_0x48525a[_0x5d34c0],_0x5d34c0);}return _0x265cd0;}function getItemSpecificFields(_0x13a8d9){var _0x3fe368=a0_0x4e5648,_0x222e88=0x0;_0x13a8d9===_0x3fe368(0x115)&&(_0x222e88=0x0);_0x13a8d9===_0x3fe368(0x12b)&&(_0x222e88=0x1);if(_0x13a8d9==='all'){var _0x12c0ef=getItemSpecificFields(_0x3fe368(0x12b)),_0x2817f4=getItemSpecificFields(_0x3fe368(0x115)),_0x446b85=_0x2817f4[_0x3fe368(0x193)](_0x12c0ef);return _0x446b85;}var _0x42a681=document[_0x3fe368(0x100)](_0x3fe368(0x105));if(_0x42a681['length']===0x0){var _0x362313=document[_0x3fe368(0x136)](_0x3fe368(0xc9));if(_0x362313&&_0x362313[_0x3fe368(0x125)][_0x3fe368(0xfb)]>0x0){var _0x68c25f=_0x362313[_0x3fe368(0x125)],_0x151d81=[];for(var _0xfed5f0=0x0;_0xfed5f0<_0x68c25f[_0x3fe368(0xfb)];_0xfed5f0++){_0x68c25f[_0xfed5f0][_0x3fe368(0xf0)][_0x3fe368(0xe1)](_0x3fe368(0xe8))&&_0x151d81[_0x3fe368(0x17e)](_0x68c25f[_0xfed5f0]);}_0x42a681=_0x151d81;}}if(_0x42a681[_0x3fe368(0xfb)]===0x0)return[];if(_0x42a681[_0x3fe368(0xfb)]===0x1){var _0x760eac=_0x42a681[0x0],_0x35199e=_0x760eac[_0x3fe368(0x136)]('h3');if(_0x35199e){if(_0x35199e['innerText'][_0x3fe368(0x11e)]()[_0x3fe368(0xf5)](_0x3fe368(0x12b))||_0x35199e[_0x3fe368(0x106)][_0x3fe368(0x11e)]()[_0x3fe368(0xf5)](_0x3fe368(0x13c))){if(_0x13a8d9===_0x3fe368(0x115))return[];_0x222e88=0x0;}else _0x222e88=0x0;}}var _0x83d982=_0x42a681[_0x222e88],_0xa80197=[];for(var _0xfed5f0=0x0;_0xfed5f0<_0x83d982[_0x3fe368(0x125)][_0x3fe368(0xfb)];_0xfed5f0++){_0x83d982[_0x3fe368(0x125)][_0xfed5f0]['tagName']===_0x3fe368(0x140)&&_0xa80197['push'](_0x83d982['children'][_0xfed5f0]);}return _0xa80197;}function getItemSpecificField(_0x466c2c){var _0x4d3546=a0_0x4e5648,_0x5c4c7c=getItemSpecificFields('all');for(var _0x17e1c3=0x0;_0x17e1c3<_0x5c4c7c[_0x4d3546(0xfb)];_0x17e1c3++){var _0x4be252=getLabel(_0x5c4c7c[_0x17e1c3]);if(_0x4be252[_0x4d3546(0x11e)]()===_0x466c2c[_0x4d3546(0x11e)]())return _0x5c4c7c[_0x17e1c3];}}function test(){var _0x2fe4e5=a0_0x4e5648,_0x2307f0=document[_0x2fe4e5(0x100)]('fieldset'),_0x264fd5=_0x2307f0[0x6],_0x873c24=_0x264fd5[_0x2fe4e5(0x136)]('.filter-menu\x20input');console[_0x2fe4e5(0xdc)]('input:',_0x873c24),_0x873c24['value']=_0x2fe4e5(0x18e),_0x873c24[_0x2fe4e5(0xf7)](),keydownEvent=new KeyboardEvent(_0x2fe4e5(0xd2),{'bubbles':!![],'key':_0x2fe4e5(0x113)}),_0x873c24[_0x2fe4e5(0x12d)](keydownEvent);var _0x353e82=new Event(_0x2fe4e5(0x117),{'bubbles':!![]});_0x873c24[_0x2fe4e5(0x12d)](_0x353e82),changeEvent=new Event(_0x2fe4e5(0x128),{'bubbles':!![]}),_0x873c24['dispatchEvent'](changeEvent);}async function fillSingleOptionItemSpecifics(){var _0x17d89e=a0_0x4e5648,_0x32d864=getItemSpecificFieldsWithProperties('required'),_0x32a3a8=_0x32d864[_0x17d89e(0x153)];for(var _0x50a621=0x0;_0x50a621<_0x32a3a8[_0x17d89e(0xfb)];_0x50a621++){_0x32a3a8[_0x50a621][_0x17d89e(0x107)][_0x17d89e(0xfb)]===0x1&&await fillCustomItemSpecific(_0x32a3a8[_0x50a621][_0x17d89e(0xea)],_0x32a3a8[_0x50a621]['recommendedValues'][0x0]);}}function getInputRequiredItemSpecificFields(_0x4b7492){var _0x3d96e4=a0_0x4e5648,_0x352530=[];for(var _0x3ff5b6=0x0;_0x3ff5b6<_0x4b7492[_0x3d96e4(0xfb)];_0x3ff5b6++){var _0x58e604=_0x4b7492[_0x3ff5b6],_0x132968=getTypeOfItemSpecific(_0x58e604);if(_0x132968!==_0x3d96e4(0x16d))continue;var _0x17f34a=getLabelDefinition(_0x58e604),_0x234f80=getLabel(_0x58e604),_0x1215f8=_0x132968,_0x15875e=getCurrentValue(_0x58e604,_0x1215f8),_0x54596c={'label':_0x234f80,'type':_0x1215f8,'currentValue':_0x15875e,'labelDefinition':_0x17f34a,'recommendedValues':[]};_0x352530['push'](_0x54596c);}return _0x352530;}function getTextContentExcludeClipped(_0x186c46){var _0x18446d=a0_0x4e5648,_0x3608cf=_0x186c46['textContent'];return _0x186c46[_0x18446d(0x100)]('.textual-display.clipped')[_0x18446d(0xfb)]>0x0&&(clippedText=_0x186c46['querySelector'](_0x18446d(0x178))[_0x18446d(0x18c)],_0x3608cf=_0x3608cf[_0x18446d(0xf6)](clippedText,'')),_0x3608cf;}function getSingleSelectItemSpecificFields(_0x54ecd8){var _0x1cb9d1=a0_0x4e5648,_0x8b7ddd=[];for(var _0x30ee6c=0x0;_0x30ee6c<_0x54ecd8[_0x1cb9d1(0xfb)];_0x30ee6c++){var _0x5ac936=_0x54ecd8[_0x30ee6c],_0x4f8929=getTypeOfItemSpecific(_0x5ac936);if(_0x4f8929!==_0x1cb9d1(0x195))continue;var _0x5bae60=getLabelDefinition(_0x5ac936),_0x491457=getLabel(_0x5ac936),_0x2566ac=_0x4f8929,_0xe1f340=getCurrentValue(_0x5ac936,_0x2566ac),_0x1394f7=getRecommendedValues(_0x5ac936,_0x2566ac),_0x4384f2={'label':_0x491457,'type':_0x2566ac,'currentValue':_0xe1f340,'recommendedValues':_0x1394f7,'labelDefinition':_0x5bae60};_0x8b7ddd['push'](_0x4384f2);}return _0x8b7ddd;}function getMultiSelectItemSpecificFields(_0x152dfc){var _0x45dcf8=a0_0x4e5648,_0x2c6dcc=[];for(var _0x5b8dcc=0x0;_0x5b8dcc<_0x152dfc[_0x45dcf8(0xfb)];_0x5b8dcc++){var _0x492e39=_0x152dfc[_0x5b8dcc],_0x4650da=getTypeOfItemSpecific(_0x492e39);if(_0x4650da!==_0x45dcf8(0x146))continue;var _0x2beed8=getLabelDefinition(_0x492e39),_0x346594=getLabel(_0x492e39),_0x91ccd=_0x4650da,_0x22fb7e=getCurrentValue(_0x492e39,_0x91ccd),_0x3203c0=getRecommendedValues(_0x492e39,_0x91ccd),_0x443aa8={'label':_0x346594,'type':_0x91ccd,'recommendedValues':_0x3203c0,'currentValue':_0x22fb7e,'labelDefinition':_0x2beed8};_0x2c6dcc['push'](_0x443aa8);}return _0x2c6dcc;}function getSingleSelectWithLimitedOptionsItemSpecificFields(_0x3657e0){var _0x35e8ec=a0_0x4e5648,_0x52e5ac=[];for(var _0x1574c3=0x0;_0x1574c3<_0x3657e0[_0x35e8ec(0xfb)];_0x1574c3++){var _0x547459=_0x3657e0[_0x1574c3],_0xf3c835=getTypeOfItemSpecific(_0x547459);if(_0xf3c835!==_0x35e8ec(0x111))continue;var _0x2c69f9=getLabelDefinition(_0x547459),_0x1318ea=getLabel(_0x547459),_0x5b9184=_0xf3c835,_0xa2284e=getCurrentValue(_0x547459,_0x5b9184),_0x15785b=getRecommendedValues(_0x547459,_0x5b9184),_0xa286f5={'label':_0x1318ea,'type':_0x5b9184,'currentValue':_0xa2284e,'recommendedValues':_0x15785b,'labelDefinition':_0x2c69f9};_0x52e5ac[_0x35e8ec(0x17e)](_0xa286f5);}return _0x52e5ac;}function getSingleSelectWithNoSearchItemSpecificFields(_0x19819e){var _0x1875df=a0_0x4e5648,_0x3ef76c=[];for(var _0x3c01fc=0x0;_0x3c01fc<_0x19819e[_0x1875df(0xfb)];_0x3c01fc++){var _0x2481c0=_0x19819e[_0x3c01fc],_0x147cd4=getTypeOfItemSpecific(_0x2481c0);if(_0x147cd4!==_0x1875df(0xce))continue;var _0x5b6cf0=getLabelDefinition(_0x2481c0),_0x4dc2e6=getLabel(_0x2481c0),_0x343b92=_0x147cd4,_0x4fbda8=getCurrentValue(_0x2481c0,_0x343b92),_0xbb1bd=getRecommendedValues(_0x2481c0,_0x343b92),_0xf3921a={'label':_0x4dc2e6,'type':_0x343b92,'currentValue':_0x4fbda8,'recommendedValues':_0xbb1bd,'labelDefinition':_0x5b6cf0};_0x3ef76c[_0x1875df(0x17e)](_0xf3921a);}return _0x3ef76c;}function getMultiSelectWithNoSearchItemSpecificFields(_0x6e70b6){var _0x5473de=a0_0x4e5648,_0x3a8793=[];for(var _0x2889ea=0x0;_0x2889ea<_0x6e70b6[_0x5473de(0xfb)];_0x2889ea++){var _0x2c3434=_0x6e70b6[_0x2889ea],_0x31c962=getTypeOfItemSpecific(_0x2c3434);if(_0x31c962!==_0x5473de(0x127))continue;var _0x4f3cb2=getLabelDefinition(_0x2c3434),_0x5c4340=getLabel(_0x2c3434),_0x16c4f2=_0x31c962,_0x2280f9=getCurrentValue(_0x2c3434,_0x16c4f2),_0x51af7b=getRecommendedValues(_0x2c3434,_0x16c4f2),_0x15d2a1={'label':_0x5c4340,'type':_0x16c4f2,'currentValue':_0x2280f9,'recommendedValues':_0x51af7b,'labelDefinition':_0x4f3cb2};_0x3a8793[_0x5473de(0x17e)](_0x15d2a1);}return _0x3a8793;}function removedFilledItemSpecificFields(_0x437843){var _0x5d3041=a0_0x4e5648,_0x30d236=[];for(var _0x106816=0x0;_0x106816<_0x437843[_0x5d3041(0xfb)];_0x106816++){var _0x15195f=_0x437843[_0x106816],_0x107781=_0x15195f['type'];if(_0x107781!==_0x5d3041(0x195)&&_0x107781!==_0x5d3041(0x16d)){_0x30d236['push'](_0x15195f);continue;}(_0x15195f['currentValue']===''||_0x15195f[_0x5d3041(0x18d)]===undefined||_0x15195f['currentValue']===null)&&_0x30d236[_0x5d3041(0x17e)](_0x15195f);}return _0x30d236;}function getTypeOfItemSpecific(_0xfc0aae){var _0x14891b=a0_0x4e5648,_0x3bb192='';if(_0xfc0aae[_0x14891b(0x100)]('[class*=\x27filter-menu\x27]\x20input[aria-label*=\x27nter\x20your\x20own\x27]')[_0x14891b(0xfb)]>0x0){var _0x449b7b=_0xfc0aae[_0x14891b(0x100)]('[class*=\x27checkbox\x27]');return _0x449b7b[_0x14891b(0xfb)]>0x0?'multiSelect':'singleSelect';}if(_0xfc0aae[_0x14891b(0x100)]('[class*=\x27filter-menu\x27]\x20input[aria-label=\x27Search\x27]')['length']>0x0){var _0x449b7b=_0xfc0aae[_0x14891b(0x100)](_0x14891b(0x13e));return _0x449b7b['length']>0x0?'multiSelectWithNoSearch':_0x14891b(0xce);}if(_0xfc0aae['querySelectorAll'](_0x14891b(0xf3))[_0x14891b(0xfb)]>0x0)return'inputRequired';if(_0xfc0aae[_0x14891b(0x100)]('.btn--tertiary')[_0x14891b(0xfb)]>0x0)return _0x14891b(0x111);}function backup_getTypeOfItemSpecific(_0x37953a){var _0x550721=a0_0x4e5648,_0x491784='';if(_0x37953a[_0x550721(0x100)](_0x550721(0x156))[_0x550721(0xfb)]>0x0&&_0x37953a[_0x550721(0x100)](_0x550721(0x17c))['length']>0x0)return _0x491784=_0x550721(0x127),_0x491784;if(_0x37953a[_0x550721(0x100)](_0x550721(0x156))['length']>0x0)return _0x491784='singleSelectWithNoSearch',_0x491784;if(_0x37953a['querySelectorAll']('.summary__attributes--pill')[_0x550721(0xfb)]>0x0)return _0x491784=_0x550721(0x111),_0x491784;if(_0x37953a['querySelectorAll'](_0x550721(0x17c))[_0x550721(0xfb)]>0x0)return _0x491784='multiSelect',_0x491784;if(_0x37953a[_0x550721(0x100)](_0x550721(0xe0))[_0x550721(0xfb)]>0x0)return _0x491784='inputRequired',_0x491784;if(_0x37953a[_0x550721(0x100)](_0x550721(0xed))['length']>0x0)return _0x491784=_0x550721(0x195),_0x491784;if(_0x37953a[_0x550721(0x100)](_0x550721(0xd9))[_0x550721(0xfb)]==0x0)return _0x491784=_0x550721(0x195),_0x491784;}function checkIfItemSpecificIsFilled(){}async function fillInputItemSpecific(_0x26bc1b,_0x4df112){var _0x443043=a0_0x4e5648;documentTitle=_0x443043(0x122)+_0x26bc1b+_0x443043(0x13d)+_0x4df112+_0x443043(0xd1),console['log'](_0x443043(0x16f),_0x26bc1b),console[_0x443043(0xdc)](_0x443043(0x10f),_0x4df112);var _0x5ba68a=getItemSpecificField(_0x26bc1b);console[_0x443043(0xdc)](_0x443043(0xde),_0x5ba68a),scrollIntoView(_0x5ba68a),highlightElement(_0x5ba68a,_0x443043(0x152)),await waitSomeTime(0x64);var _0x281a43=checkIfItemSpecificIsAdded(_0x26bc1b,_0x4df112);if(_0x281a43)return console[_0x443043(0xdc)](_0x443043(0x143)),highlightElement(_0x5ba68a,_0x443043(0x187)),await waitSomeTime(0x64),console[_0x443043(0xdc)](_0x443043(0x114)),!![];var _0x51e33c=_0x5ba68a[_0x443043(0x136)](_0x443043(0x117));return console[_0x443043(0xdc)](_0x443043(0x117),_0x51e33c),_0x51e33c[_0x443043(0xf7)](),_0x51e33c[_0x443043(0x16a)]=_0x4df112,dispatchKeyBoardAndChangeEvent(_0x51e33c),await waitForNetworkResponseCountIncrease(),_0x281a43=checkIfItemSpecificIsAdded(_0x26bc1b,_0x4df112),_0x281a43&&(highlightElement(_0x5ba68a,_0x443043(0x187)),documentTitle=_0x443043(0x190)+_0x26bc1b+'\x20with\x20'+_0x4df112+_0x443043(0xd1)),!_0x281a43&&(highlightElement(_0x5ba68a,_0x443043(0x171)),documentTitle=_0x443043(0x14a)+_0x26bc1b+_0x443043(0x13d)+_0x4df112+_0x443043(0xd1)),await waitSomeTime(waitTime),await closeDialogs(),!![];}async function fillSingleSelectItemSpecific(_0x5ba24e,_0x4a2be6,_0x35f6ca=0x1,_0x4c83d9=0x1){var _0x48186c=a0_0x4e5648;console[_0x48186c(0xdc)]('1\x20fillSingleSelectItemSpecific\x20itemSpecificValue',_0x4a2be6);if(Array[_0x48186c(0xe3)](_0x4a2be6)){for(let _0x132e6e=0x0;_0x132e6e<_0x4a2be6[_0x48186c(0xfb)];_0x132e6e++){const _0x907ff3=_0x4a2be6[_0x132e6e];if(_0x907ff3[_0x48186c(0xfb)]<0x19){_0x4a2be6=_0x907ff3;break;}}Array[_0x48186c(0xe3)](_0x4a2be6)&&(_0x4a2be6=_0x4a2be6[0x0]);}if(_0x4a2be6['length']>0x19)return;var _0x242bd2=getItemSpecificField(_0x5ba24e);if(!_0x242bd2){console[_0x48186c(0xdc)](_0x48186c(0x123));return;}documentTitle=_0x48186c(0x122)+_0x5ba24e+'\x20with\x20'+_0x4a2be6+'\x20('+_0x4c83d9+'/'+_0x35f6ca+')',console[_0x48186c(0xdc)](_0x48186c(0xfe),_0x5ba24e),console[_0x48186c(0xdc)](_0x48186c(0x139),_0x4a2be6),scrollIntoView(_0x242bd2),highlightElement(_0x242bd2,_0x48186c(0x152)),await waitSomeTime(0x64);var _0x47d725=checkIfItemSpecificIsAdded(_0x5ba24e,_0x4a2be6);if(_0x47d725){console['log']('itemSpecific\x20is\x20already\x20added'),highlightElement(_0x242bd2,'success'),await waitSomeTime(0x64);return;}var _0x3dd273=_0x242bd2[_0x48186c(0x136)](_0x48186c(0x117));_0x3dd273[_0x48186c(0xf7)](),_0x3dd273[_0x48186c(0x16a)]=_0x4a2be6,dispatchKeyBoardAndChangeEvent(_0x3dd273),await waitForNetworkResponseCountIncrease(),_0x47d725=checkIfItemSpecificIsAdded(_0x5ba24e,_0x4a2be6),_0x47d725&&(documentTitle=_0x48186c(0x190)+_0x5ba24e+_0x48186c(0x13d)+_0x4a2be6+'\x20('+_0x4c83d9+'/'+_0x35f6ca+')',highlightElement(_0x242bd2,_0x48186c(0x187)),await waitSomeTime(0x64)),!_0x47d725&&(documentTitle=_0x48186c(0x14a)+_0x5ba24e+_0x48186c(0x13d)+_0x4a2be6+'\x20('+_0x4c83d9+'/'+_0x35f6ca+')',highlightElement(_0x242bd2,_0x48186c(0x171)),await waitSomeTime(0x64)),await waitSomeTime(waitTime),await closeDialogs();}function a0_0xecad(_0x240601,_0x50aa57){var _0x3171b0=a0_0x3171();return a0_0xecad=function(_0xecadc8,_0xf0c245){_0xecadc8=_0xecadc8-0xc7;var _0x537796=_0x3171b0[_0xecadc8];return _0x537796;},a0_0xecad(_0x240601,_0x50aa57);}async function fillMultiSelectItemSpecific(_0x15cd04,_0x5e4bc7){var _0x49b2b2=a0_0x4e5648;!Array[_0x49b2b2(0xe3)](_0x5e4bc7)&&(_0x5e4bc7=[_0x5e4bc7]);documentTitle=_0x49b2b2(0x122)+_0x15cd04+'\x20with\x20'+_0x5e4bc7[_0x49b2b2(0xfb)]+'\x20values';var _0x3a94f8=_0x5e4bc7[_0x49b2b2(0x19f)](0x0,0x19);for(var _0x382d13=0x0;_0x382d13<_0x3a94f8[_0x49b2b2(0xfb)];_0x382d13++){var _0x28dd66=_0x3a94f8[_0x382d13];_0x28dd66[_0x49b2b2(0xfb)]>0x1e&&_0x3a94f8[_0x49b2b2(0xda)](_0x382d13,0x1);}if(_0x3a94f8[_0x49b2b2(0xfb)]>0x0){var _0x3aa848={[_0x15cd04]:_0x3a94f8};await postViaNetworkRequest(_0x49b2b2(0x103),_0x3aa848),await waitForNetworkResponseCountIncrease();}}async function fillSingleSelectWithLimitedOptionsItemSpecific(_0x3bbae1,_0x45bf73){var _0x4ba5a0=a0_0x4e5648;Array['isArray'](_0x45bf73)&&(_0x45bf73=_0x45bf73[0x0]);documentTitle=_0x4ba5a0(0x122)+_0x3bbae1+_0x4ba5a0(0x13d)+_0x45bf73+_0x4ba5a0(0xd1);var _0x1e42d2=getItemSpecificField(_0x3bbae1);console[_0x4ba5a0(0xdc)]('itemSpecificField',_0x1e42d2),scrollIntoView(_0x1e42d2),highlightElement(_0x1e42d2,'loading'),await waitSomeTime(0x64);var _0x4caf76=getButtonOptionsForSingleSelectWithLimitedOptions(_0x1e42d2),_0x3da043=null;for(var _0x153466=0x0;_0x153466<_0x4caf76[_0x4ba5a0(0xfb)];_0x153466++){var _0x369e80=_0x4caf76[_0x153466],_0x528942=getTextContentExcludeClipped(_0x369e80);if(_0x528942==_0x45bf73){_0x3da043=_0x369e80;break;}}if(_0x3da043==null){console['log'](_0x4ba5a0(0x17b)),highlightElement(_0x1e42d2,_0x4ba5a0(0x171));return;}_0x3da043['click'](),highlightElement(_0x1e42d2,_0x4ba5a0(0x187)),await waitForNetworkResponseCountIncrease(),await waitSomeTime(0x64),documentTitle=_0x4ba5a0(0x190)+_0x3bbae1+_0x4ba5a0(0x13d)+_0x45bf73+_0x4ba5a0(0xd1),await waitSomeTime(waitTime),await closeDialogs();}async function closeDialogs(){var _0x5b1349=a0_0x4e5648,_0x2a9934=document['querySelectorAll']('button[aria-label=\x27Close\x20dialog\x27]');_0x2a9934[_0x5b1349(0xfb)]>0x0&&(documentTitle=_0x5b1349(0x16b),await makeTabActive(),_0x2a9934[0x0][_0x5b1349(0xf7)](),_0x2a9934[0x0][_0x5b1349(0xe7)](),await waitSomeTime(0x12c),await tellChromeToSwitchToPreviousTab(),_0x2a9934=document[_0x5b1349(0x100)](_0x5b1349(0x163)),_0x2a9934[_0x5b1349(0xfb)]>0x0?(documentTitle=_0x5b1349(0x144),await waitSomeTime(0x12c),_0x2a9934[0x0]['focus'](),_0x2a9934[0x0][_0x5b1349(0xe7)]()):(console[_0x5b1349(0xdc)]('dialog\x20closed'),documentTitle=_0x5b1349(0x141)));}async function postViaNetworkRequest(_0x4c5794,_0x34d9dc){var _0x487b1d=a0_0x4e5648;console[_0x487b1d(0xdc)]('postViaNetworkRequest',_0x4c5794,_0x34d9dc);var _0x4ba529=new Headers();for(var _0x22cb64=0x0;_0x22cb64<requestHeaders[_0x487b1d(0xfb)];_0x22cb64++){var _0x5c8d1c=requestHeaders[_0x22cb64];_0x4ba529[_0x487b1d(0x1a0)](_0x5c8d1c[_0x487b1d(0x18b)],_0x5c8d1c[_0x487b1d(0x16a)]);}console[_0x487b1d(0xdc)](_0x487b1d(0x11a),_0x4ba529);var _0x429841=new URLSearchParams(new URL(window['location'][_0x487b1d(0xf2)])[_0x487b1d(0x132)]),_0x454a87=_0x429841[_0x487b1d(0xfd)](_0x487b1d(0x159)),_0x40a6a2=window[_0x487b1d(0x11c)][_0x487b1d(0xf2)][_0x487b1d(0x11f)]('/')[0x2],_0x4b4281='https://'+_0x40a6a2+_0x487b1d(0xec)+_0x454a87+_0x487b1d(0x116),_0x4f1f26=_0x487b1d(0x134),_0x374481={[_0x4c5794]:_0x34d9dc};console['log'](_0x487b1d(0x120),_0x374481);var _0x56e160=new TextEncoder()[_0x487b1d(0x177)](JSON[_0x487b1d(0x101)](_0x374481));try{const _0x12f443=await fetch(_0x4b4281,{'method':_0x4f1f26,'headers':_0x4ba529,'body':_0x56e160});console[_0x487b1d(0xdc)](_0x487b1d(0x10a),_0x12f443);}catch(_0x22ce66){console[_0x487b1d(0x171)](_0x487b1d(0xf1),_0x22ce66);}}async function fillCustomItemSpecific(_0x14d4fd,_0x39fe05){var _0x52bbd9=a0_0x4e5648;console[_0x52bbd9(0xdc)]('fillCustomItemSpecific',_0x14d4fd,_0x39fe05);var _0x5a2110={[_0x14d4fd]:[_0x39fe05]};console[_0x52bbd9(0xdc)](_0x52bbd9(0x15f),_0x5a2110),await postViaNetworkRequest(_0x52bbd9(0x103),_0x5a2110),console['log'](_0x52bbd9(0xfa));}async function fillItemSpecifics(_0x3fce9b){var _0x26b01d=a0_0x4e5648;for(var _0x382db2=0x0;_0x382db2<_0x3fce9b['length'];_0x382db2++){await fillItemSpecific(_0x3fce9b[_0x382db2][_0x26b01d(0xea)],_0x3fce9b[_0x382db2]['value']);}}function getCombinedItemSpecificFieldProperties(_0x4dd8e5=a0_0x4e5648(0x191)){var _0x1ac8ff=a0_0x4e5648,_0x466183=getItemSpecificFieldsWithProperties(_0x4dd8e5),_0x25b65e=_0x466183[_0x1ac8ff(0x104)],_0x3b92c0=_0x466183['multiSelectItemSpecificFields'],_0x4d79d8=_0x466183['singleSelectItemSpecificFields'],_0x4c8607=_0x466183['singleSelectWithLimitedOptionsItemSpecificFields'],_0x2795da=_0x466183[_0x1ac8ff(0xeb)],_0x3b5890=_0x466183['multiSelectWithNoSearchItemSpecificFields'],_0x139d62=_0x25b65e['concat'](_0x3b92c0,_0x4d79d8,_0x4c8607,_0x2795da,_0x3b5890);return console[_0x1ac8ff(0xdc)](_0x1ac8ff(0x149),_0x139d62),_0x139d62;}function getItemSpecificFieldProperties(_0x333d79){var _0x40fe5a=a0_0x4e5648,_0xed2c3e=getCombinedItemSpecificFieldProperties(_0x40fe5a(0x191));console[_0x40fe5a(0xdc)](_0x40fe5a(0x189),_0xed2c3e);for(var _0xf787db=0x0;_0xf787db<_0xed2c3e['length'];_0xf787db++){if(_0xed2c3e[_0xf787db]['label']==_0x333d79)return _0xed2c3e[_0xf787db];}}async function fillItemSpecific(_0x516ca2,_0x39997c){var _0x46c1bc=a0_0x4e5648;console[_0x46c1bc(0xdc)](_0x46c1bc(0xfc),_0x516ca2,_0x39997c);var _0x334a9d=![],_0x4dcf44=getCombinedItemSpecificFieldProperties(_0x46c1bc(0x191));console[_0x46c1bc(0xdc)]('itemSpecificFields:',_0x4dcf44);for(var _0x561f70=0x0;_0x561f70<_0x4dcf44[_0x46c1bc(0xfb)];_0x561f70++){console[_0x46c1bc(0xdc)]('itemSpecificFields[i]:',_0x4dcf44[_0x561f70]);if(_0x4dcf44[_0x561f70][_0x46c1bc(0xea)][_0x46c1bc(0x11e)]()==_0x516ca2['toLowerCase']()){if(_0x4dcf44[_0x561f70][_0x46c1bc(0x12c)]=='multiSelect'){console[_0x46c1bc(0xdc)](_0x46c1bc(0x198)+_0x516ca2+_0x46c1bc(0x13d)+_0x39997c),await fillMultiSelectItemSpecific(_0x516ca2,_0x39997c),_0x334a9d=!![];break;}else{if(_0x4dcf44[_0x561f70][_0x46c1bc(0x12c)]==_0x46c1bc(0x195)){console[_0x46c1bc(0xdc)](_0x46c1bc(0x175)+_0x516ca2+'\x20with\x20'+_0x39997c),await fillSingleSelectItemSpecific(_0x516ca2,_0x39997c),_0x334a9d=!![];break;}else{if(_0x4dcf44[_0x561f70]['type']==_0x46c1bc(0x111)){console[_0x46c1bc(0xdc)](_0x46c1bc(0x12f)+_0x516ca2+_0x46c1bc(0x13d)+_0x39997c),await fillSingleSelectWithLimitedOptionsItemSpecific(_0x516ca2,_0x39997c),_0x334a9d=!![];break;}}}}}!_0x334a9d&&(console['log']('creating\x20custom\x20item\x20specific\x20'+_0x516ca2+_0x46c1bc(0x13d)+_0x39997c),await fillCustomItemSpecific(_0x516ca2,_0x39997c));}async function fillEmptyItemSpecifics(_0x1b3b3b){var _0x512607=a0_0x4e5648,_0x198cbd=getTotalFilledItemSpecificFields();console[_0x512607(0xdc)](_0x512607(0x13f),_0x198cbd);if(_0x198cbd>=0x2d){console[_0x512607(0xdc)](_0x512607(0x138));return;}for(var _0x4b7c04=0x0;_0x4b7c04<_0x1b3b3b[_0x512607(0xfb)];_0x4b7c04++){await fillEmptyItemSpecific(_0x1b3b3b[_0x4b7c04][_0x512607(0xea)],_0x1b3b3b[_0x4b7c04][_0x512607(0x16a)]);if(_0x198cbd+_0x4b7c04>=0x2d){console['log'](_0x512607(0x148));return;}}}async function fillEmptyItemSpecific(_0xc41e9,_0x259275){var _0x353182=a0_0x4e5648,_0x59335e=![];if(_0x259275[_0x353182(0xfb)]>0x32)return;var _0x2b4f36=getCombinedItemSpecificFieldProperties(_0x353182(0x191));for(var _0x52af29=0x0;_0x52af29<_0x2b4f36[_0x353182(0xfb)];_0x52af29++){if(_0x2b4f36[_0x52af29][_0x353182(0xea)]==_0xc41e9){_0x59335e=!![];if(_0x2b4f36[_0x52af29][_0x353182(0x18d)]==null||_0x2b4f36[_0x52af29][_0x353182(0x18d)]=='')await fillItemSpecific(_0xc41e9,_0x259275);else continue;}}if(!_0x59335e)try{await fillCustomItemSpecific(_0xc41e9,_0x259275);}catch(_0x5e8c00){console[_0x353182(0xdc)](_0x353182(0x121),_0xc41e9,_0x259275);try{await fillCustomItemSpecific(_0xc41e9,_0x259275);}catch(_0x118516){console[_0x353182(0xdc)](_0x353182(0x121),_0xc41e9,_0x259275);return;}}}async function fillEmptyRequiredItemSpecificsV2(_0x570709){var _0x4b9470=a0_0x4e5648,_0x2a3e71=getCombinedItemSpecificFieldProperties(_0x4b9470(0x115));for(var _0x3e8ec6=0x0;_0x3e8ec6<_0x2a3e71['length'];_0x3e8ec6++){var _0x28b3c2=_0x2a3e71[_0x3e8ec6];if(_0x28b3c2['currentValue']==null||_0x28b3c2[_0x4b9470(0x18d)]==''){console[_0x4b9470(0xdc)](_0x4b9470(0xde),_0x28b3c2);var _0x5f336c=_0x570709;_0x28b3c2[_0x4b9470(0xea)][_0x4b9470(0x11e)]()=='mpn'&&(_0x5f336c=_0x570709[_0x4b9470(0xf6)](/\s/g,'_'));console[_0x4b9470(0xdc)](_0x4b9470(0x192),_0x28b3c2[_0x4b9470(0xea)],_0x5f336c);try{await fillCustomItemSpecific(_0x28b3c2['label'],_0x5f336c);}catch(_0x39da1d){console['log'](_0x4b9470(0x121),_0x28b3c2[_0x4b9470(0xea)],_0x5f336c);}}else console[_0x4b9470(0xdc)](_0x4b9470(0x119),_0x28b3c2[_0x4b9470(0xea)],_0x28b3c2[_0x4b9470(0x18d)]);}}async function fillEmptyOptionalItemSpecificsV2(_0x1767cc){var _0x2c73ce=a0_0x4e5648,_0x36d3b7=getCombinedItemSpecificFieldProperties('optional');console[_0x2c73ce(0xdc)](_0x2c73ce(0x189),_0x36d3b7);for(var _0x3d4953=0x0;_0x3d4953<_0x36d3b7[_0x2c73ce(0xfb)];_0x3d4953++){var _0x23855f=getTotalFilledItemSpecificFields(_0x36d3b7);if(_0x23855f>=0x2d){console['log'](_0x2c73ce(0x161));return;}if(_0x36d3b7[_0x3d4953]['label']['toLowerCase']()==_0x2c73ce(0xfb)){console[_0x2c73ce(0xdc)](_0x2c73ce(0x10d));return;}var _0x2464e8=_0x36d3b7[_0x3d4953];console[_0x2c73ce(0xdc)](_0x2c73ce(0xde),_0x2464e8);if(_0x2464e8['currentValue']==null||_0x2464e8[_0x2c73ce(0x18d)]==''){var _0x222b21=_0x1767cc;_0x2464e8[_0x2c73ce(0xea)]['toLowerCase']()==_0x2c73ce(0xf4)&&(_0x222b21=_0x1767cc[_0x2c73ce(0xf6)](/\s/g,'_'));try{await fillCustomItemSpecific(_0x2464e8[_0x2c73ce(0xea)],_0x222b21);}catch(_0xf7b228){console[_0x2c73ce(0xdc)]('error\x20creating\x20custom\x20item\x20specific',_0x2464e8[_0x2c73ce(0xea)],_0x222b21);}}if(_0x3d4953==0x4){console[_0x2c73ce(0xdc)](_0x2c73ce(0x108));return;}}}async function fillEmptyRequiredItemSpecifics(_0x3d9c88){var _0x302ad6=a0_0x4e5648,_0x4e6bf5=getItemSpecificFieldsWithProperties(_0x302ad6(0x115));for(var _0x3bd201=0x0;_0x3bd201<_0x4e6bf5[_0x302ad6(0x104)][_0x302ad6(0xfb)];_0x3bd201++){var _0x1f2e3c=_0x4e6bf5[_0x302ad6(0x104)][_0x3bd201];console[_0x302ad6(0xdc)](_0x302ad6(0x19a),_0x1f2e3c),(_0x1f2e3c[_0x302ad6(0x18d)]==null||_0x1f2e3c['currentValue']=='')&&await fillItemSpecific(_0x1f2e3c[_0x302ad6(0xea)],_0x3d9c88);}for(var _0x3bd201=0x0;_0x3bd201<_0x4e6bf5[_0x302ad6(0x147)][_0x302ad6(0xfb)];_0x3bd201++){var _0x214f3a=_0x4e6bf5['multiSelectItemSpecificFields'][_0x3bd201];console[_0x302ad6(0xdc)]('multiSelectItemSpecificField',_0x214f3a),(_0x214f3a[_0x302ad6(0x18d)]==null||_0x214f3a[_0x302ad6(0x18d)]=='')&&await fillItemSpecific(_0x214f3a[_0x302ad6(0xea)],_0x3d9c88);}for(var _0x3bd201=0x0;_0x3bd201<_0x4e6bf5[_0x302ad6(0x185)]['length'];_0x3bd201++){var _0x273575=_0x4e6bf5[_0x302ad6(0x185)][_0x3bd201];console['log'](_0x302ad6(0x158),_0x273575),(_0x273575[_0x302ad6(0x18d)]==null||_0x273575[_0x302ad6(0x18d)]=='')&&await fillItemSpecific(_0x273575[_0x302ad6(0xea)],_0x3d9c88);}for(var _0x3bd201=0x0;_0x3bd201<_0x4e6bf5[_0x302ad6(0x153)][_0x302ad6(0xfb)];_0x3bd201++){var _0x286ac2=_0x4e6bf5['singleSelectWithLimitedOptionsItemSpecificFields'][_0x3bd201];console[_0x302ad6(0xdc)]('singleSelectWithLimitedOptionsItemSpecificField',_0x286ac2),(_0x286ac2['value']==null||_0x286ac2[_0x302ad6(0x16a)]=='')&&await fillItemSpecific(_0x286ac2[_0x302ad6(0xea)],_0x3d9c88);}}async function fillItemSpecificValuesWithAiV2(_0x22385b){var _0x359d2a=a0_0x4e5648,_0x20208f=combineProductData(_0x22385b);console[_0x359d2a(0xdc)](_0x359d2a(0xe9),_0x20208f);var _0x10f683=getCombinedItemSpecificFieldProperties(_0x359d2a(0x191)),_0x1f5880=await getFilteredItemSpecificFields(_0x10f683);console[_0x359d2a(0xdc)](_0x359d2a(0x109),_0x1f5880);var _0x4db45d=_0x359d2a(0xe5),_0x5b7616=await fetchPrompt(_0x4db45d),_0x7a5cdc='';for(var _0x2e11f3=0x0;_0x2e11f3<_0x1f5880[_0x359d2a(0xfb)];_0x2e11f3++){var _0x20d528=_0x1f5880[_0x2e11f3],_0x14fbc5=_0x20d528['label'],_0x408084='';if(_0x20d528[_0x359d2a(0x107)]!=null)for(var _0x577054=0x0;_0x577054<_0x20d528[_0x359d2a(0x107)]['length'];_0x577054++){var _0x486b3a=_0x20d528['recommendedValues'][_0x577054];_0x577054==0x0?_0x408084+=_0x486b3a:_0x408084+=',\x20'+_0x486b3a;}var _0x3ee28d='';_0x3ee28d=_0x5b7616['replace'](_0x359d2a(0xff),_0x14fbc5),_0x3ee28d=_0x3ee28d[_0x359d2a(0xf6)](_0x359d2a(0x173),_0x408084),_0x7a5cdc+=_0x3ee28d+'\x0a\x0a';}console[_0x359d2a(0xdc)]('combinedProductInformation',_0x7a5cdc);var _0x19e76a=getAllProductIdentifiers(_0x1f5880);console['log']('productIdentifierLabels',_0x19e76a);var _0x4db45d='text_prompts/itemSpecPromptV3.txt',_0x46f0da=await fetchPrompt(_0x4db45d);_0x46f0da=_0x46f0da[_0x359d2a(0xf6)](_0x359d2a(0x145),_0x19e76a),_0x46f0da=_0x46f0da[_0x359d2a(0xf6)]('{{Product\x20Identifiers}}',_0x7a5cdc),_0x46f0da=_0x46f0da[_0x359d2a(0xf6)](_0x359d2a(0x133),_0x20208f),console['log']('itemSpecPromptV3',_0x46f0da);var _0x57f039=await receiveOpenAiResponseFromBackgroundScript(_0x46f0da,0x0);console[_0x359d2a(0xdc)](_0x359d2a(0xe6),_0x57f039);var _0x1e88db=_0x57f039[_0x359d2a(0x194)][0x0]['text'];console[_0x359d2a(0xdc)](_0x359d2a(0x130),_0x1e88db),_0x1e88db='{'+_0x1e88db,console[_0x359d2a(0xdc)]('textFromAi',_0x1e88db);var _0x39984c=JSON['parse'](_0x1e88db);console[_0x359d2a(0xdc)]('itemSpecificValuesFromAi',_0x39984c);for(var _0x4c1b66 in _0x39984c){_0x39984c[_0x4c1b66]!=null&&_0x39984c[_0x4c1b66]!=''&&((_0x39984c[_0x4c1b66]!=_0x359d2a(0xd4)||_0x39984c[_0x4c1b66]!=_0x359d2a(0x18f)||_0x39984c[_0x4c1b66][_0x359d2a(0x11e)]()!=_0x359d2a(0x197))&&(console[_0x359d2a(0xdc)]('key',_0x4c1b66),console[_0x359d2a(0xdc)]('itemSpecificValuesFromAi[key]',_0x39984c[_0x4c1b66]),await fillItemSpecific(_0x4c1b66,_0x39984c[_0x4c1b66])));}}function getAllProductIdentifiers(_0x474b40){var _0x537c8f=a0_0x4e5648,_0x3d2ac4='';for(var _0xb5e63e=0x0;_0xb5e63e<_0x474b40[_0x537c8f(0xfb)];_0xb5e63e++){var _0xab1b52=_0x474b40[_0xb5e63e],_0x31bef0=_0xab1b52[_0x537c8f(0xea)];_0x31bef0!=null&&(_0x3d2ac4==''?_0x3d2ac4+=_0x31bef0:_0x3d2ac4+=',\x20'+_0x31bef0);}return _0x3d2ac4='['+_0x3d2ac4+']',_0x3d2ac4;}async function fillItemSpecificValuesWithAi(_0x17d860){var _0x17a85e=a0_0x4e5648,_0x515c92=combineProductData(_0x17d860),_0x53cdd2=getCombinedItemSpecificFieldProperties(_0x17a85e(0x191)),_0x29283e=await getFilteredItemSpecificFields(_0x53cdd2);console[_0x17a85e(0xdc)](_0x17a85e(0x109),_0x29283e);var _0x416789=await fillProductInformationTemplate(_0x29283e);console[_0x17a85e(0xdc)]('filledProductInformationTemplate',_0x416789);var _0x2c8e31='text_prompts/product_specifics_prompt_2.txt',_0x802abb=await fetchPrompt(_0x2c8e31),_0x588f97=_0x802abb[_0x17a85e(0xf6)](_0x17a85e(0x182),_0x515c92),_0x588f97=_0x588f97[_0x17a85e(0xf6)](_0x17a85e(0x157),_0x416789);console['log'](_0x17a85e(0x142),_0x588f97);var _0x917548=await receiveOpenAiResponseFromBackgroundScript(_0x588f97,0x0);console['log']('response',_0x917548);var _0x1a48e9=_0x917548[_0x17a85e(0x194)][0x0]['text'];console[_0x17a85e(0xdc)](_0x17a85e(0x130),_0x1a48e9);_0x1a48e9[_0x17a85e(0x11b)](0x0)=='\x22'&&(_0x1a48e9=_0x1a48e9[_0x17a85e(0x167)](0x1));_0x1a48e9[_0x17a85e(0x11b)](_0x1a48e9[_0x17a85e(0xfb)]-0x1)=='\x22'&&(_0x1a48e9=_0x1a48e9['substring'](0x0,_0x1a48e9[_0x17a85e(0xfb)]-0x1));console[_0x17a85e(0xdc)](_0x17a85e(0x130),_0x1a48e9);var _0x1a4e82=JSON[_0x17a85e(0x15c)](_0x1a48e9);console['log'](_0x17a85e(0xcf),_0x1a4e82);for(var _0x8b029b in _0x1a4e82){_0x1a4e82[_0x8b029b]!=null&&_0x1a4e82[_0x8b029b]!=''&&(_0x1a4e82[_0x8b029b]!=_0x17a85e(0xd4)&&_0x1a4e82[_0x8b029b]!=_0x17a85e(0x18f)&&(console[_0x17a85e(0xdc)](_0x17a85e(0x164),_0x8b029b),console['log'](_0x17a85e(0xdb),_0x1a4e82[_0x8b029b]),await fillItemSpecific(_0x8b029b,_0x1a4e82[_0x8b029b])));}}function removeQuotes(_0x52a709){var _0x3b993b=a0_0x4e5648;_0x52a709[_0x3b993b(0x11b)](0x0)=='\x22'&&(_0x52a709=_0x52a709['substring'](0x1)),_0x52a709['charAt'](_0x52a709[_0x3b993b(0xfb)]-0x1)=='\x22'&&(_0x52a709=_0x52a709['substring'](0x0,_0x52a709['length']-0x1));}async function getFilteredItemSpecificWords(){var _0x346cb6=a0_0x4e5648,_0x393d1d=[],_0x648023=_0x346cb6(0x14b),_0x4f282b=await fetchPrompt(_0x648023);return _0x393d1d=_0x4f282b['split'](',')[_0x346cb6(0x137)](function(_0x5175f6){var _0x19d7ea=_0x346cb6;return _0x5175f6[_0x19d7ea(0x168)]();}),console[_0x346cb6(0xdc)](_0x346cb6(0x172),_0x393d1d),_0x393d1d;}async function getWhiteListedItemSpecifics(){var _0x48b884=a0_0x4e5648,_0x437fc7=[],_0x520adb='text_prompts/white_listed_item_specifics.txt',_0x15903a=await fetchPrompt(_0x520adb);return _0x437fc7=_0x15903a['split'](',')[_0x48b884(0x137)](function(_0x2562cb){var _0x43ee76=_0x48b884;return _0x2562cb[_0x43ee76(0x168)]();}),console['log'](_0x48b884(0x112),_0x437fc7),_0x437fc7;}function a0_0x3171(){var _0x4d024d=['text_prompts/filtered_words.txt','currentValue\x20is\x20null\x20or\x20undefined','div','https://ebaysniperitemspecificsapi.ngrok.io/api/ItemSpecifics/GetItemSpecificValues','50%','8109672sEiWtU','6px\x20solid\x20#3498db','loading','singleSelectWithLimitedOptionsItemSpecificFields','itemSpecificsArray','button','.se-search-box__field\x20input[aria-label=\x27Search\x27]','{{data-template}}','singleSelectItemSpecificField','draftId','71908iAodEw','borderTop','parse','text_prompts/item_specific_prompt.txt','text_prompts/ProductInformationTemplate2.txt','attributeObject','[class$=\x27n__cell\x27]','totalFilledItemSpecificFields\x20is\x20greater\x20than\x2045,\x20skipping','parentNode','button[aria-label=\x27Close\x20dialog\x27]','key','.se-checkbox-group__option','.se-expand-button__button','substring','trim','bullet_points','value','⏳\x20Closing\x20Dialogs','labelDefinition','inputRequired','text_prompts/item_specific_prompt_no_recommended_values.txt','itemSpecificLabel','generate-answer-button','error','filteredWords','{{recommendedValues}}','style','filling\x20single\x20select\x20','.filter-menu__item[aria-checked=\x22true\x22]','encode','.textual-display.clipped','currentValue\x20is\x20array','parentElement','valueButtonOption\x20is\x20null','.filter-menu__checkbox','target','push','[checked]','addEventListener','join','{{product-data}}','{{label}}','offsetWidth','singleSelectItemSpecificFields','231sYTOeo','success','webkitAnimation','itemSpecificFields','Lame','name','textContent','currentValue','Test','Not\x20Specified','✔️\x20Filling\x20','all','filling\x20item\x20specific','concat','choices','singleSelect','Generate\x20Answer','not\x20applicable','filling\x20multi\x20select\x20','stopPropagation','inputRequiredItemSpecificField','title','663931EHXvxq','2pRyVmq','checkIfItemSpecificIsAdded','slice','append','height','.filter-menu__text','.smry.summary__attributes\x20.summary__attributes-gridview','notLoading','.btn--tertiary','3px\x20solid\x20#FF0000','.loader','singleSelectWithNoSearch','itemSpecificValuesFromAi','add','\x20(1/1)','keydown','.summary__attributes--value\x20.se-expand-button__button-text','Does\x20Not\x20Apply','generateAnswer:','4407655ugcxiE','removeChild','.filter-menu-button__menu-container\x20input','.filter-menu-button__list-menu','splice','itemSpecificValuesFromAi[key]','log','490UnanPF','itemSpecificField','animation','.se-textbox--input\x20input[name^=\x27attributes.\x27]','contains','6InHBfd','isArray','3px\x20solid\x20#FFA500','text_prompts/ProductIdentifierTemplate.txt','response','click','se-panel-section','product_data','label','singleSelectWithNoSearchItemSpecificFields','/lstng/api/listing_draft/','.menu__item','recommendedValuesString','33084711Tgaarx','classList','Error:','href','.se-textbox--input\x20input','mpn','includes','replace','focus','filteredItemSpecifics','{{type}}','postViaNetworkRequest\x20done','length','fillItemSpecific','get','fillSingleSelectItemSpecific\x20itemSpecificLabel','{{Label}}','querySelectorAll','stringify','filter','attributes','inputRequiredItemSpecificFields','.summary__attributes--section-container','innerText','recommendedValues','stopping\x20after\x204','filteredItemSpecificFields','Response:','Show\x20all','fieldset','skipping\x20length','.tooltip__content','itemSpecificValue','itemSpecificFieldProperties','singleSelectWithLimitedOptions','whiteListedItemSpecifics','Enter','itemSpecific\x20is\x20already\x20added,\x20skipping','required','?mode=AddItem&forceValidation=true','input','appendChild','skipping\x20item\x20specific','headers','charAt','location','descriptionText','toLowerCase','split','requestBody','error\x20creating\x20custom\x20item\x20specific','⏳\x20Filling\x20','itemSpecificField\x20not\x20found','5268270kPxYFy','children','Unavailable','multiSelectWithNoSearch','change','100836oDMoUq','border','optional','type','dispatchEvent','6px\x20solid\x20#f3f3f3','filling\x20single\x20select\x20with\x20limited\x20options\x20','textFromAi','createElement','search','{{productData}}','PUT','3px\x20solid\x20#FFFF00','querySelector','map','Total\x20is\x20greater\x20than\x2045,\x20skipping','2\x20fillSingleSelectItemSpecific\x20itemSpecificValue','indexOf','3px\x20solid\x20#00FF00','additional','\x20with\x20','[class*=\x27checkbox\x27]','Total:\x20','FIELDSET','✔️\x20Dialogs\x20Closed','completed_prompt','itemSpecific\x20is\x20already\x20added','⏳\x20Dialogs\x20Still\x20Open','{{Product\x20Identifier\x20Labels}}','multiSelect','multiSelectItemSpecificFields','Total\x20is\x20greater\x20than\x2045,\x20stopping','getCombinedItemSpecificFieldProperties','❌\x20Filling\x20'];a0_0x3171=function(){return _0x4d024d;};return a0_0x3171();}async function getFilteredItemSpecificFields(_0x1b0be0){var _0x3e93dd=a0_0x4e5648,_0x41f122=await getFilteredItemSpecificWords(),_0xf3a213=await getWhiteListedItemSpecifics(),_0x4ed563=[];for(var _0x29c634=0x0;_0x29c634<_0x1b0be0[_0x3e93dd(0xfb)];_0x29c634++){var _0x1a38d4=_0x1b0be0[_0x29c634],_0x958df8=_0x1a38d4['label'],_0x2ecb8f=![];for(var _0x34cf6d=0x0;_0x34cf6d<_0xf3a213[_0x3e93dd(0xfb)];_0x34cf6d++){var _0x552c8b=_0xf3a213[_0x34cf6d];if(_0x958df8[_0x3e93dd(0x11e)]()[_0x3e93dd(0xf5)](_0x552c8b[_0x3e93dd(0x11e)]())){_0x4ed563[_0x3e93dd(0x17e)](_0x1a38d4);break;}}for(var _0x34cf6d=0x0;_0x34cf6d<_0x41f122[_0x3e93dd(0xfb)];_0x34cf6d++){var _0x2752fa=_0x41f122[_0x34cf6d];if(_0x958df8[_0x3e93dd(0x11e)]()[_0x3e93dd(0xf5)](_0x2752fa['toLowerCase']())){_0x2ecb8f=!![];break;}}!_0x2ecb8f&&_0x4ed563[_0x3e93dd(0x17e)](_0x1a38d4);}return _0x4ed563;}async function fillProductInformationTemplate(_0x11fb49){var _0x420b43=a0_0x4e5648,_0x3dac11=_0x420b43(0x15e),_0x17831f=await fetchPrompt(_0x3dac11),_0x428fe2='';for(var _0xe2d86a=0x0;_0xe2d86a<_0x11fb49[_0x420b43(0xfb)];_0xe2d86a++){var _0x32e5d4=_0x11fb49[_0xe2d86a],_0x39236b=_0x32e5d4['label'],_0x15a013='';if(_0x32e5d4[_0x420b43(0x107)]!=null)for(var _0x417cdf=0x0;_0x417cdf<_0x32e5d4[_0x420b43(0x107)][_0x420b43(0xfb)];_0x417cdf++){var _0x38cfde=_0x32e5d4[_0x420b43(0x107)][_0x417cdf];_0x417cdf==0x0?_0x15a013+=_0x38cfde:_0x15a013+=',\x20'+_0x38cfde;}var _0xcc1eb5=_0x32e5d4['type'],_0x349d0d='';_0x349d0d=_0x17831f[_0x420b43(0xf6)]('{{Label}}',_0x39236b),_0x349d0d=_0x349d0d[_0x420b43(0xf6)](_0x420b43(0x173),_0x15a013),_0xcc1eb5==_0x420b43(0x146)?_0x349d0d=_0x349d0d[_0x420b43(0xf6)]('{{type}}','Yes'):_0x349d0d=_0x349d0d['replace'](_0x420b43(0xf9),'No'),_0x428fe2+=_0x349d0d+'\x0a\x0a';}return _0x428fe2;}function combineProductData(_0x3140ff){var _0x2aaeb3=a0_0x4e5648,_0x32b748=_0x3140ff[_0x2aaeb3(0x169)][_0x2aaeb3(0x181)]('\x0a'),_0xcd4c56=_0x3140ff[_0x2aaeb3(0x11d)],_0x16fcf8=_0x3140ff[_0x2aaeb3(0x19b)],_0x1200ec='';for(var _0x4f3d53=0x0;_0x4f3d53<_0x3140ff[_0x2aaeb3(0xf8)][_0x2aaeb3(0xfb)];_0x4f3d53++){var _0x30e5b7=_0x3140ff[_0x2aaeb3(0xf8)][_0x4f3d53],_0x209ddd=_0x30e5b7['label'],_0x14d5b7=_0x30e5b7[_0x2aaeb3(0x16a)];_0x1200ec+=_0x209ddd+':\x20'+_0x14d5b7+'\x0a';}var _0x29cc9b=_0x16fcf8+'\x0a\x0a'+_0x32b748+'\x0a\x0a'+_0xcd4c56+'\x0a\x0a'+_0x1200ec;return _0x29cc9b;}async function getItemSpecificValueFromAi(_0x5d4ea2,_0x25b638){var _0x45d878=a0_0x4e5648,_0x533673=getItemSpecificFieldProperties(_0x5d4ea2),_0x2f836c=_0x25b638['bullet_points']['join']('\x0a'),_0x182b1a=_0x25b638[_0x45d878(0x11d)],_0x4e5794=_0x25b638[_0x45d878(0x19b)],_0x51d355='';for(var _0x2c0d14=0x0;_0x2c0d14<_0x25b638[_0x45d878(0xf8)][_0x45d878(0xfb)];_0x2c0d14++){var _0x112ac2=_0x25b638[_0x45d878(0xf8)][_0x2c0d14],_0x585007=_0x112ac2[_0x45d878(0xea)],_0x4f132c=_0x112ac2['value'];_0x51d355+=_0x585007+':\x20'+_0x4f132c+'\x0a';}var _0x509e57=_0x4e5794+'\x0a\x0a'+_0x2f836c+'\x0a\x0a'+_0x182b1a+'\x0a\x0a'+_0x51d355,_0x4e4a4c=_0x533673[_0x45d878(0x107)],_0x5ebaa5='';if(_0x4e4a4c!=null)for(var _0x2c0d14=0x0;_0x2c0d14<_0x4e4a4c[_0x45d878(0xfb)];_0x2c0d14++){var _0x267a77=_0x4e4a4c[_0x2c0d14];_0x5ebaa5+=_0x267a77+',\x20';}console[_0x45d878(0xdc)](_0x45d878(0xee),_0x5ebaa5);if(_0x5ebaa5!=''){var _0x47ed41=_0x45d878(0x15d),_0x41078c=await fetchPrompt(_0x47ed41);_0x41078c=_0x41078c[_0x45d878(0xf6)](_0x45d878(0x133),_0x509e57),_0x41078c=_0x41078c[_0x45d878(0xf6)](_0x45d878(0x183),_0x5d4ea2),_0x41078c=_0x41078c[_0x45d878(0xf6)](_0x45d878(0x173),_0x5ebaa5);}if(_0x5ebaa5==='')var _0x47ed41=_0x45d878(0x16e);}function clickMoreButtonToShowAllItemSpecifics(){}function filterItemSpecifics(){}function capitalizeFirstLetterOfEachWord(_0x5cfe78){var _0xbbe7fc=a0_0x4e5648,_0x2fdfb4=_0x5cfe78[_0xbbe7fc(0x11f)]('\x20'),_0xb45f4c=[];for(var _0x4ac439=0x0;_0x4ac439<_0x2fdfb4['length'];_0x4ac439++){var _0x2029e7=_0x2fdfb4[_0x4ac439],_0x59aaef=_0x2029e7[_0xbbe7fc(0x11b)](0x0)['toUpperCase']()+_0x2029e7[_0xbbe7fc(0x19f)](0x1);_0xb45f4c[_0xbbe7fc(0x17e)](_0x59aaef);}var _0x2c12a4=_0xb45f4c[_0xbbe7fc(0x181)]('\x20');return _0x2c12a4;}function capitalizeFirstLetterOfEachWordInItemSpecifics(_0x8648a7){var _0x4a3574=a0_0x4e5648,_0x1d5cb5=[];for(var _0x1910c4=0x0;_0x1910c4<_0x8648a7['length'];_0x1910c4++){var _0x57ff07=_0x8648a7[_0x1910c4],_0x3f358b=capitalizeFirstLetterOfEachWord(_0x57ff07[_0x4a3574(0xea)]),_0x46984c=capitalizeFirstLetterOfEachWord(_0x57ff07['value']),_0x3688f2={'label':_0x3f358b,'value':_0x46984c};_0x1d5cb5[_0x4a3574(0x17e)](_0x3688f2);}return _0x1d5cb5;}function getLabel(_0x1d62d9){var _0x279f9d=a0_0x4e5648,_0x3e06bd=_0x1d62d9[_0x279f9d(0x136)]('legend')['textContent'];return _0x3e06bd;}function getLabelDefinition(_0x1d0424){var _0x402904=a0_0x4e5648,_0x3023cd=_0x402904(0x126),_0x4f96a6=_0x1d0424[_0x402904(0x136)](_0x402904(0x10e));return _0x4f96a6&&(_0x3023cd=_0x4f96a6[_0x402904(0x18c)],_0x3023cd===''&&(_0x3023cd='Unavailable')),_0x3023cd;}function getRecommendedValues(_0x198e54,_0x3423c2){var _0x4daac7=a0_0x4e5648,_0x364f36=[];if(_0x3423c2==_0x4daac7(0x146))return getMultiSelectSearchElementValues(_0x198e54);if(_0x3423c2==_0x4daac7(0x195))return getSearchElementValues(_0x198e54);if(_0x3423c2==_0x4daac7(0x111)){var _0x39916b=getButtonOptionsForSingleSelectWithLimitedOptions(_0x198e54);for(var _0x4fc6ff=0x0;_0x4fc6ff<_0x39916b[_0x4daac7(0xfb)];_0x4fc6ff++){var _0x1d8f3e=_0x39916b[_0x4fc6ff],_0x56e553=getTextContentExcludeClipped(_0x1d8f3e);_0x364f36[_0x4daac7(0x17e)](_0x56e553);}return _0x364f36=[...new Set(_0x364f36)],_0x364f36;}if(_0x3423c2==_0x4daac7(0x16d))return'';if(_0x3423c2==_0x4daac7(0xce))return getSearchElementValues(_0x198e54);if(_0x3423c2==_0x4daac7(0x127))return getMultiSelectSearchElementValues(_0x198e54);}function getMultiSelectSearchElementValues(_0x5bd454){var _0x30207f=a0_0x4e5648,_0x2ae42e=[],_0x5d57f2=_0x5bd454[_0x30207f(0x100)](_0x30207f(0xc8));for(var _0x14ed52=0x0;_0x14ed52<_0x5d57f2['length'];_0x14ed52++){var _0x1eade6=_0x5d57f2[_0x14ed52],_0x4b3ae6=_0x1eade6[_0x30207f(0x18c)];_0x4b3ae6=getTextContentExcludeClipped(_0x1eade6),_0x2ae42e[_0x30207f(0x17e)](_0x4b3ae6);}var _0x5d57f2=_0x5bd454['querySelectorAll'](_0x30207f(0xea));for(var _0x14ed52=0x0;_0x14ed52<_0x5d57f2[_0x30207f(0xfb)];_0x14ed52++){var _0x1eade6=_0x5d57f2[_0x14ed52],_0x4b3ae6=_0x1eade6[_0x30207f(0x18c)];_0x4b3ae6=getTextContentExcludeClipped(_0x1eade6),_0x2ae42e[_0x30207f(0x17e)](_0x4b3ae6);}return _0x2ae42e=[...new Set(_0x2ae42e)],_0x2ae42e;}function getSearchElementValues(_0x8102ba){var _0xd99459=a0_0x4e5648,_0x1aabc5=[],_0x2c8666=_0x8102ba[_0xd99459(0x100)](_0xd99459(0xed));for(var _0x3e15ed=0x0;_0x3e15ed<_0x2c8666[_0xd99459(0xfb)];_0x3e15ed++){var _0x49192a=_0x2c8666[_0x3e15ed],_0x28c6ce=getTextContentExcludeClipped(_0x49192a);_0x1aabc5[_0xd99459(0x17e)](_0x28c6ce);}return _0x1aabc5=[...new Set(_0x1aabc5)],_0x1aabc5;}function getButtonOptionsForSingleSelectWithLimitedOptions(_0x40d422){var _0x5373c7=a0_0x4e5648,_0x361abb=_0x40d422[_0x5373c7(0x100)](_0x5373c7(0xcb));return _0x361abb;}function getCurrentValue(_0xe9636,_0xe8be61){var _0x103319=a0_0x4e5648,_0x5d515a;if(_0xe8be61==_0x103319(0x146))return getMultiSelectCurrentValue(_0xe9636);if(_0xe8be61==_0x103319(0x195))return getSingleSelectCurrentValue(_0xe9636);if(_0xe8be61==_0x103319(0x111))return null;if(_0xe8be61==_0x103319(0x16d))return _0x5d515a=_0xe9636[_0x103319(0x136)](_0x103319(0x117))[_0x103319(0x16a)],_0x5d515a===''&&(_0x5d515a=null),_0x5d515a;if(_0xe8be61==_0x103319(0xce))return getSingleSelectCurrentValue(_0xe9636);if(_0xe8be61=='multiSelectWithNoSearch')return getMultiSelectCurrentValue(_0xe9636);}function getMultiSelectCurrentValue(_0x52be06){var _0x57bcd3=a0_0x4e5648,_0x42c2ab=[],_0x176d5f=_0x52be06[_0x57bcd3(0x100)](_0x57bcd3(0x176));for(var _0x53f77f=0x0;_0x53f77f<_0x176d5f[_0x57bcd3(0xfb)];_0x53f77f++){var _0x3024ae=_0x176d5f[_0x53f77f],_0x397229=_0x3024ae['querySelector'](_0x57bcd3(0xc8)),_0x42c1c7=getTextContentExcludeClipped(_0x397229);_0x42c2ab['push'](_0x42c1c7);}var _0x49739a=_0x52be06[_0x57bcd3(0x100)](_0x57bcd3(0x165));for(var _0x53f77f=0x0;_0x53f77f<_0x49739a[_0x57bcd3(0xfb)];_0x53f77f++){var _0x2af561=_0x49739a[_0x53f77f];if(_0x2af561[_0x57bcd3(0x136)](_0x57bcd3(0x17f))){var _0x397229=_0x2af561[_0x57bcd3(0x136)](_0x57bcd3(0xea)),_0x42c1c7=getTextContentExcludeClipped(_0x397229);_0x42c2ab[_0x57bcd3(0x17e)](_0x42c1c7);}}return _0x42c2ab=_0x42c2ab[_0x57bcd3(0x102)](function(_0xdfb44c,_0x46e7ad){var _0x40a424=_0x57bcd3;return _0x42c2ab[_0x40a424(0x13a)](_0xdfb44c)==_0x46e7ad;}),_0x42c2ab[_0x57bcd3(0xfb)]===0x0&&(_0x42c2ab=null),_0x42c2ab;}function getSingleSelectCurrentValue(_0x3b95db){var _0x4b231d=a0_0x4e5648,_0x57e82d=null,_0x3e4903;return _0x3e4903=_0x3b95db[_0x4b231d(0x136)](_0x4b231d(0x160)),_0x3e4903&&(_0x57e82d=getTextContentExcludeClipped(_0x3e4903)),_0x57e82d===_0x4b231d(0x10b)&&(_0x57e82d=null),_0x57e82d===''&&(_0x57e82d=null),_0x57e82d;}function dispatchKeyBoardAndChangeEvent(_0x1bdee3){var _0xa3e565=a0_0x4e5648,_0x15c5eb=new Event('input',{'bubbles':!![]});_0x1bdee3[_0xa3e565(0x12d)](_0x15c5eb);var _0xd3b37c=new KeyboardEvent('keydown',{'bubbles':!![],'key':_0xa3e565(0x113)});_0x1bdee3['dispatchEvent'](_0xd3b37c);var _0xc32b28=new Event(_0xa3e565(0x128),{'bubbles':!![]});_0x1bdee3[_0xa3e565(0x12d)](_0xc32b28);}function testInputInput(){var _0x33e31d=a0_0x4e5648,_0x16b764=document['querySelectorAll'](_0x33e31d(0x10c)),_0x2171bd=_0x16b764[0x0],_0x1b62da=_0x2171bd[_0x33e31d(0x136)](_0x33e31d(0xd8));_0x1b62da[_0x33e31d(0x16a)]=_0x33e31d(0x18a);var _0x4b9dbe=new Event(_0x33e31d(0x117),{'bubbles':!![]});_0x1b62da[_0x33e31d(0x12d)](_0x4b9dbe);var _0x5b9581=new KeyboardEvent(_0x33e31d(0xd2),{'bubbles':!![],'key':_0x33e31d(0x113)});_0x1b62da[_0x33e31d(0x12d)](_0x5b9581);var _0xe5ed1d=new Event(_0x33e31d(0x128),{'bubbles':!![]});_0x1b62da['dispatchEvent'](_0xe5ed1d);}function highlightElement(_0x180acd,_0x54e445){var _0x2e1d1b=a0_0x4e5648,_0x5a0323=_0x180acd['querySelectorAll'](_0x2e1d1b(0x166)),_0x230710=_0x180acd[_0x2e1d1b(0x100)]('.filter-button'),_0x4dedcf=_0x180acd[_0x2e1d1b(0x100)](_0x2e1d1b(0x117));if(_0x5a0323[_0x2e1d1b(0xfb)]>0x0)_0x180acd=_0x5a0323[0x0];else{if(_0x230710[_0x2e1d1b(0xfb)]>0x0)_0x180acd=_0x230710[0x0];else _0x4dedcf[_0x2e1d1b(0xfb)]>0x0&&(_0x180acd=_0x4dedcf[0x0]);}if(_0x54e445==_0x2e1d1b(0x187)){_0x180acd[_0x2e1d1b(0x174)][_0x2e1d1b(0x12a)]=_0x2e1d1b(0x13b);return;}if(_0x54e445=='error'){_0x180acd['style'][_0x2e1d1b(0x12a)]=_0x2e1d1b(0xcc);return;}if(_0x54e445=='loading'){_0x180acd[_0x2e1d1b(0x174)]['border']=_0x2e1d1b(0x135);return;}if(_0x54e445=='notLoading'){_0x180acd['style'][_0x2e1d1b(0x12a)]=_0x2e1d1b(0xe4);return;}}function checkIfItemSpecificIsAdded(_0x32cdb1,_0x33fb20){var _0x2992f3=a0_0x4e5648;console[_0x2992f3(0xdc)](_0x2992f3(0x19e),_0x32cdb1,_0x33fb20);var _0x219ce1=getItemSpecificFieldProperties(_0x32cdb1);console[_0x2992f3(0xdc)](_0x2992f3(0x110),_0x219ce1);var _0x2eceb6=_0x219ce1['currentValue'];console[_0x2992f3(0xdc)](_0x2992f3(0x18d),_0x2eceb6);if(_0x2eceb6==null||_0x2eceb6==undefined)return console[_0x2992f3(0xdc)](_0x2992f3(0x14c)),![];if(Array[_0x2992f3(0xe3)](_0x2eceb6)){console['log'](_0x2992f3(0x179));for(var _0x1689c7=0x0;_0x1689c7<_0x2eceb6[_0x2992f3(0xfb)];_0x1689c7++){console['log']('currentValue[i]',_0x2eceb6[_0x1689c7]);if(_0x2eceb6[_0x1689c7][_0x2992f3(0x11e)]()[_0x2992f3(0x168)]()==_0x33fb20[_0x2992f3(0x11e)]()['trim']())return!![];}}else{if(_0x2eceb6['toLowerCase']()[_0x2992f3(0x168)]()==_0x33fb20[_0x2992f3(0x11e)]()[_0x2992f3(0x168)]())return!![];}return![];}function addButtonToItemSpecificFieldsToGetGenerateAnswer(_0x5ef25a){var _0x58be9c=a0_0x4e5648,_0xf05695=getItemSpecificFields(_0x58be9c(0x191));for(var _0x49ccdf=0x0;_0x49ccdf<_0xf05695['length'];_0x49ccdf++){var _0x5438b7=_0xf05695[_0x49ccdf],_0x39be4b=document[_0x58be9c(0x131)](_0x58be9c(0x155));_0x39be4b[_0x58be9c(0xf0)][_0x58be9c(0xd0)](_0x58be9c(0x170)),_0x39be4b[_0x58be9c(0x18c)]=_0x58be9c(0x196),_0x39be4b[_0x58be9c(0x180)](_0x58be9c(0xe7),async function(_0x49dd63){var _0x4d325a=_0x58be9c;_0x49dd63[_0x4d325a(0x199)]();var _0x4ad188=getLabel(_0x49dd63[_0x4d325a(0x17d)][_0x4d325a(0x17a)]);await generateAnswer(_0x4ad188,_0x5ef25a);}),_0x5438b7[_0x58be9c(0x118)](_0x39be4b);}}async function generateAnswer(_0x3a0a53,_0x16b339){var _0x37dbca=a0_0x4e5648;console[_0x37dbca(0xdc)](_0x37dbca(0xd5),_0x3a0a53);var _0x114a3b=getItemSpecificFieldProperties(_0x3a0a53);console[_0x37dbca(0xdc)](_0x37dbca(0x110),_0x114a3b);var _0x4a8e7f=combineProductDataAttributes(_0x16b339),_0x350463=_0x114a3b[_0x37dbca(0x107)],_0x503147=_0x114a3b[_0x37dbca(0xea)],_0x287f29=_0x114a3b[_0x37dbca(0x16c)],_0x1be3a8=_0x114a3b[_0x37dbca(0x12c)],_0x22b418=_0x37dbca(0x14e);if(_0x114a3b['type']==_0x37dbca(0x16d)){startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)](_0x37dbca(0x154),_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147);if(_0x37b720[_0x37dbca(0xfb)]>0x0){var _0x30e473=_0x37b720[0x0];await fillInputItemSpecific(_0x503147,_0x30e473);}}if(_0x114a3b[_0x37dbca(0x12c)]=='singleSelect'){startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)](_0x37dbca(0x154),_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147);if(_0x37b720[_0x37dbca(0xfb)]>0x0){var _0x30e473=_0x37b720[0x0];await fillSingleSelectItemSpecific(_0x503147,_0x30e473);}}if(_0x114a3b[_0x37dbca(0x12c)]==_0x37dbca(0x111)){startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)](_0x37dbca(0x154),_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147);if(_0x37b720['length']>0x0){var _0x30e473=_0x37b720[0x0];await fillSingleSelectWithLimitedOptionsItemSpecific(_0x503147,_0x30e473);}}if(_0x114a3b[_0x37dbca(0x12c)]=='singleSelectWithNoSearch'){startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)](_0x37dbca(0x154),_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147);if(_0x37b720[_0x37dbca(0xfb)]>0x0){var _0x30e473=_0x37b720[0x0];await fillSingleSelectItemSpecific(_0x503147,_0x30e473);}}if(_0x114a3b[_0x37dbca(0x12c)]==_0x37dbca(0x146)){console['log']('multiSelect'),startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)]('itemSpecificsArray',_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147),_0x37b720[_0x37dbca(0xfb)]>0x0&&await fillMultiSelectItemSpecific(_0x503147,_0x37b720);}if(_0x114a3b[_0x37dbca(0x12c)]=='multiSelectWithNoSearch'){console[_0x37dbca(0xdc)](_0x37dbca(0x127)),startVisualInProgressForItemSpecificField(_0x503147);var _0x37b720=await postToServer(_0x22b418,{'productDescription':_0x4a8e7f,'recommendedValues':_0x350463,'itemSpecific':_0x503147,'labelDefinition':_0x287f29,'itemSpecificFieldType':_0x1be3a8,'forceResponse':!![]});console[_0x37dbca(0xdc)](_0x37dbca(0x154),_0x37b720),stopVisualInProgressForItemSpecificField(_0x503147),_0x37b720[_0x37dbca(0xfb)]>0x0&&await fillMultiSelectItemSpecific(_0x503147,_0x37b720);}}function startVisualInProgressForItemSpecificField(_0x1d6461){var _0x33455a=a0_0x4e5648,_0x2b157d=getItemSpecificFields('all');for(var _0xd09a69=0x0;_0xd09a69<_0x2b157d[_0x33455a(0xfb)];_0xd09a69++){var _0x2ca888=_0x2b157d[_0xd09a69],_0x620d17=getLabel(_0x2ca888);_0x620d17==_0x1d6461&&(highlightElement(_0x2ca888,_0x33455a(0x152)),addLoaderToElement(_0x2ca888));}}function stopVisualInProgressForItemSpecificField(_0x40cc01){var _0x360b4a=a0_0x4e5648,_0x512cbe=getItemSpecificFields(_0x360b4a(0x191));for(var _0x4e89a2=0x0;_0x4e89a2<_0x512cbe[_0x360b4a(0xfb)];_0x4e89a2++){var _0x168dc1=_0x512cbe[_0x4e89a2],_0x5f1013=getLabel(_0x168dc1);_0x5f1013==_0x40cc01&&(highlightElement(_0x168dc1,_0x360b4a(0xca)),removeLoaderFromElement(_0x168dc1));}}function addLoaderToElement(_0x4657b9){var _0x2683e1=a0_0x4e5648,_0x3aa24d=document[_0x2683e1(0x131)](_0x2683e1(0x14d));_0x3aa24d[_0x2683e1(0xf0)]['add']('loader'),_0x3aa24d[_0x2683e1(0x174)][_0x2683e1(0x12a)]=_0x2683e1(0x12e),_0x3aa24d['style']['borderRadius']=_0x2683e1(0x14f),_0x3aa24d[_0x2683e1(0x174)][_0x2683e1(0x15b)]=_0x2683e1(0x151),_0x3aa24d['style']['width']=_0x4657b9[_0x2683e1(0x184)]/0x23+'px',_0x3aa24d[_0x2683e1(0x174)][_0x2683e1(0xc7)]=_0x4657b9[_0x2683e1(0x184)]/0x23+'px',_0x3aa24d[_0x2683e1(0x174)][_0x2683e1(0x188)]='spin\x202s\x20linear\x20infinite',_0x3aa24d[_0x2683e1(0x174)][_0x2683e1(0xdf)]='spin\x202s\x20linear\x20infinite',_0x4657b9['appendChild'](_0x3aa24d);}function removeLoaderFromElement(_0x466393){var _0x122b9d=a0_0x4e5648,_0x1919be=_0x466393[_0x122b9d(0x136)](_0x122b9d(0xcd));_0x1919be&&_0x1919be[_0x122b9d(0x162)][_0x122b9d(0xd7)](_0x1919be);}await fillEmptyRequiredItemSpecificsV2('Details\x20In\x20Description');