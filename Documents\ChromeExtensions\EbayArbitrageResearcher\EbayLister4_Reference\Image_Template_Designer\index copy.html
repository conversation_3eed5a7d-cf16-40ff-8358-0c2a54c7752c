<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collage Creator</title>
    <script src="../libraries/fabric.min.js"></script>
    <script src="../libraries/image_utils.js"></script>
    <script src="../libraries/image_transform_utils.js"></script>

    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div id="editor" class="editor-container">
        <div id="canvasWrapper">
            <div id="canvasContainer">
                <canvas id="c" width="1500" height="1500"></canvas>
            </div>
        </div>


        <div id="controls">
            <button id="addRect">Add Placeholder</button>
            <input type="file" id="uploadBadge" multiple />

            <button id="fetchBadgeFromUrl">Add Image from Url</button>
            <input type="text" id="badgeUrl" placeholder="Image Url" />

            <br>
            <br>

            <label for="bannerText">Banner Text:</label>
            <input type="text" id="bannerText" placeholder="Enter banner text" />

            <label for="bannerColor">Banner Color:</label>
            <input type="color" id="bannerColor" value="#FF0000" />
            <label for="bannerFont">Font Style:</label>
            <select id="bannerFont">
                <option value="Arial">Arial</option>
                <option value="'Amazon Ember', sans-serif">Amazon Ember</option>
                <!-- Add more fonts as needed -->
            </select>



            <button id="addBanner">Add Banner</button>


            <button id="saveTemplate">Save Template</button>
            <button id="loadTemplate">Load Template</button>
            <button id="testTemplate">Test Template</button>
            <button id="clearCanvas">Clear Canvas</button>

            <button id="delete">Delete</button>


            <button id="exportTemplate">Export Template</button>
            <input type="file" id="importTemplate" style="display: none;" />
            <button id="btnImportTemplate">Import Template</button>


        </div>
        <div id="tester" hidden>
            <div id="testCanvasContainer">
                <canvas id="testCanvas" width="1500" height="1500"></canvas>
            </div>
            <button id="backToEditor">Back to Editor</button>
            <button id="downloadImage">Download Image</button>
        </div>
        <script src="index.js"></script>
</body>

</html>