function a0_0x135b(_0x47af3d,_0x35ee47){var _0x104138=a0_0x1041();return a0_0x135b=function(_0x135bc9,_0x7bd24){_0x135bc9=_0x135bc9-0x107;var _0x3b3466=_0x104138[_0x135bc9];return _0x3b3466;},a0_0x135b(_0x47af3d,_0x35ee47);}var a0_0x17520=a0_0x135b;(function(_0x5174cc,_0x3bd41f){var _0x45acd1=a0_0x135b,_0x161882=_0x5174cc();while(!![]){try{var _0x3b79c0=parseInt(_0x45acd1(0x111))/0x1+-parseInt(_0x45acd1(0x107))/0x2+parseInt(_0x45acd1(0x10e))/0x3+-parseInt(_0x45acd1(0x108))/0x4*(-parseInt(_0x45acd1(0x112))/0x5)+parseInt(_0x45acd1(0x10c))/0x6+-parseInt(_0x45acd1(0x116))/0x7+-parseInt(_0x45acd1(0x109))/0x8;if(_0x3b79c0===_0x3bd41f)break;else _0x161882['push'](_0x161882['shift']());}catch(_0x407e17){_0x161882['push'](_0x161882['shift']());}}}(a0_0x1041,0x4c2c5),console[a0_0x17520(0x115)]('Terapeak\x20script\x20loaded'));async function main(){var _0x3ab3fa=a0_0x17520;await onPageLoadAndIdle(),console[_0x3ab3fa(0x115)](_0x3ab3fa(0x10f));var _0x476354=await attemptToAddButtonsWithTimeout(0x2710);if(!_0x476354)return;await initAmazonSortTypeContextMenu();}function a0_0x1041(){var _0x24a52c=['1686475pgQwwA','93548GaXWwW','82292odWYJm','5485112zsvYSw','message','Window.addEvent','545046rtxsWy','removeEventListener','1640064yXMPPD','terapeak\x20page\x20loaded','#search-div','298073HPAHDH','85UBLwWn','addEventListener','querySelector','log'];a0_0x1041=function(){return _0x24a52c;};return a0_0x1041();}main();async function attemptToAddButtonsWithTimeout(_0x441b61){return new Promise(_0x3a0591=>{var _0x345c61=a0_0x135b;const _0x554214=_0x51238a=>{var _0x59b978=a0_0x135b;console[_0x59b978(0x115)](_0x59b978(0x10b),_0x51238a),!document[_0x59b978(0x114)](_0x59b978(0x110))&&addQuickActionButtons(),document[_0x59b978(0x114)](_0x59b978(0x110))&&(window['removeEventListener']('message',_0x554214),_0x3a0591(!![]));};window[_0x345c61(0x113)](_0x345c61(0x10a),_0x554214,![]),setTimeout(()=>{var _0xbd1c7d=_0x345c61;window[_0xbd1c7d(0x10d)](_0xbd1c7d(0x10a),_0x554214),_0x3a0591(![]);},_0x441b61);});}