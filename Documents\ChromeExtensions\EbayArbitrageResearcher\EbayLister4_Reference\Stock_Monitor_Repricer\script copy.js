var a0_0x4778f8=a0_0x5f07;(function(_0x47b919,_0x3df6fb){var _0x542ee0=a0_0x5f07,_0x423b33=_0x47b919();while(!![]){try{var _0x7087c4=-parseInt(_0x542ee0(0xe6))/0x1+-parseInt(_0x542ee0(0xe7))/0x2+parseInt(_0x542ee0(0x10c))/0x3*(-parseInt(_0x542ee0(0x10d))/0x4)+parseInt(_0x542ee0(0x108))/0x5*(parseInt(_0x542ee0(0xfa))/0x6)+parseInt(_0x542ee0(0xfb))/0x7+-parseInt(_0x542ee0(0x110))/0x8*(parseInt(_0x542ee0(0xfc))/0x9)+parseInt(_0x542ee0(0xee))/0xa;if(_0x7087c4===_0x3df6fb)break;else _0x423b33['push'](_0x423b33['shift']());}catch(_0x59115f){_0x423b33['push'](_0x423b33['shift']());}}}(a0_0x30e9,0x60d7d));function a0_0x30e9(){var _0x2acf81=['forEach','runtime','elementId','10XghgTN','change','enable_delete_items_with_no_sales','addEventListener','3egzHav','2768520tPxowK','enable_price_monitor','tracker_filters','2935568zCPTPg','tracker_run_status','enable_continuous_tracking','getElementById','log','storageKey','Sending\x20message\x20to\x20background.js\x20to\x20test\x20item','delete','121435EQrhNf','757404JFYNhq','amazonData\x202','tracking_timeout','type','Sending\x20message\x20to\x20background.js\x20to\x20start\x20tracking','enable_delete_items_with_no_sku','start_tracking','12848260YwZCGr','checked','set','querySelectorAll','checkbox','Please\x20select\x20at\x20least\x20one\x20filter\x20to\x20track','page_number','storage','input','value','get','click','1740246IEzadJ','636818RfrUjd','9nkOFne','https://www.amazon.ca/dpdsfsfsdf','test_end_item','enable_delete_policy_violation_items','sendMessage','enable_stock_monitor','local','defaultValue','length'];a0_0x30e9=function(){return _0x2acf81;};return a0_0x30e9();}const settings=[{'elementId':a0_0x4778f8(0xdf),'storageKey':'tracker_run_status','defaultValue':![],'type':a0_0x4778f8(0xf2),'event':a0_0x4778f8(0x109)},{'elementId':a0_0x4778f8(0xff),'storageKey':a0_0x4778f8(0xff),'defaultValue':![],'type':a0_0x4778f8(0xf2),'event':a0_0x4778f8(0xf9)},{'elementId':a0_0x4778f8(0xe0),'storageKey':'enable_continuous_tracking','defaultValue':![],'type':a0_0x4778f8(0xf2),'event':'click'},{'elementId':a0_0x4778f8(0xec),'storageKey':'enable_delete_items_with_no_sku','defaultValue':![],'type':a0_0x4778f8(0xf2),'event':a0_0x4778f8(0xf9)},{'elementId':'enable_stock_monitor','storageKey':a0_0x4778f8(0x101),'defaultValue':![],'type':'checkbox','event':a0_0x4778f8(0xf9)},{'elementId':a0_0x4778f8(0x10e),'storageKey':'enable_price_monitor','defaultValue':![],'type':'checkbox','event':a0_0x4778f8(0xf9)},{'elementId':a0_0x4778f8(0xf4),'storageKey':a0_0x4778f8(0xf4),'defaultValue':0x1,'type':a0_0x4778f8(0xf6),'event':a0_0x4778f8(0xf6)},{'elementId':a0_0x4778f8(0xe9),'storageKey':a0_0x4778f8(0xe9),'defaultValue':0x258,'type':a0_0x4778f8(0xf6),'event':'input'}],ruleBasedSettings=[{'elementId':'enable_delete_items_with_no_sales','storageKey':a0_0x4778f8(0x10a),'defaultValue':{'enabled':![],'item-action-select':a0_0x4778f8(0xe5),'sales-threshold-input':0x0,'days-threshold-input':0x0}}];function a0_0x5f07(_0x3ee6b1,_0x4ce05e){var _0x30e9a7=a0_0x30e9();return a0_0x5f07=function(_0x5f070d,_0x438961){_0x5f070d=_0x5f070d-0xdf;var _0xc3d07b=_0x30e9a7[_0x5f070d];return _0xc3d07b;},a0_0x5f07(_0x3ee6b1,_0x4ce05e);}ruleBasedSettings[a0_0x4778f8(0x105)](function(_0x1991cf){var _0x394ff9=a0_0x4778f8;const _0x50df11=document[_0x394ff9(0xe1)](_0x1991cf[_0x394ff9(0x107)]);_0x50df11[_0x394ff9(0x10b)](_0x394ff9(0xf9),function(){var _0x5f576a=_0x394ff9;let _0x17aab8=_0x50df11['checked'];chrome[_0x5f576a(0xf5)][_0x5f576a(0x102)]['set']({[_0x1991cf[_0x5f576a(0xe3)]]:_0x17aab8}),console[_0x5f576a(0xe2)](_0x1991cf['storageKey']+':',_0x17aab8);}),chrome[_0x394ff9(0xf5)][_0x394ff9(0x102)][_0x394ff9(0xf8)](_0x1991cf[_0x394ff9(0xe3)],function(_0x1cd19c){var _0x2c034d=_0x394ff9;let _0x4e8631=_0x1cd19c[_0x1991cf['storageKey']];_0x4e8631===undefined&&(_0x4e8631=_0x1991cf[_0x2c034d(0x103)],chrome[_0x2c034d(0xf5)][_0x2c034d(0x102)]['set']({[_0x1991cf[_0x2c034d(0xe3)]]:_0x4e8631})),_0x50df11[_0x2c034d(0xef)]=_0x4e8631,console['log'](_0x1991cf[_0x2c034d(0xe3)]+':',_0x4e8631);});}),settings[a0_0x4778f8(0x105)](function(_0x251ea3){var _0x3d87e8=a0_0x4778f8;const _0x2f4af0=document['getElementById'](_0x251ea3[_0x3d87e8(0x107)]);_0x2f4af0[_0x3d87e8(0x10b)](_0x251ea3['event'],function(){var _0x2cff1f=_0x3d87e8;let _0x1874f0;if(_0x251ea3['type']==='checkbox')_0x1874f0=_0x2f4af0[_0x2cff1f(0xef)];else _0x251ea3[_0x2cff1f(0xea)]===_0x2cff1f(0xf6)&&(_0x1874f0=_0x2f4af0[_0x2cff1f(0xf7)]);chrome['storage'][_0x2cff1f(0x102)]['set']({[_0x251ea3[_0x2cff1f(0xe3)]]:_0x1874f0}),console['log'](_0x251ea3[_0x2cff1f(0xe3)]+':',_0x1874f0);}),chrome['storage'][_0x3d87e8(0x102)][_0x3d87e8(0xf8)](_0x251ea3['storageKey'],function(_0x3ee66e){var _0x26909c=_0x3d87e8;let _0x1202f9=_0x3ee66e[_0x251ea3['storageKey']];_0x1202f9===undefined&&(_0x1202f9=_0x251ea3[_0x26909c(0x103)],chrome[_0x26909c(0xf5)][_0x26909c(0x102)]['set']({[_0x251ea3['storageKey']]:_0x1202f9}));if(_0x251ea3[_0x26909c(0xea)]===_0x26909c(0xf2))_0x2f4af0[_0x26909c(0xef)]=_0x1202f9;else _0x251ea3[_0x26909c(0xea)]===_0x26909c(0xf6)&&(_0x2f4af0[_0x26909c(0xf7)]=_0x1202f9);console[_0x26909c(0xe2)](_0x251ea3[_0x26909c(0xe3)]+':',_0x1202f9);});}),document['getElementById'](a0_0x4778f8(0xed))[a0_0x4778f8(0x10b)](a0_0x4778f8(0xf9),function(){var _0x21340e=a0_0x4778f8;console[_0x21340e(0xe2)](_0x21340e(0xeb)),document[_0x21340e(0xe1)](_0x21340e(0xdf))[_0x21340e(0xef)]=!![];var _0x59c05a=document[_0x21340e(0xe1)](_0x21340e(0x10f))[_0x21340e(0xf1)]('input[type=\x22checkbox\x22]'),_0x397a89=![];for(var _0x9eaecd=0x0;_0x9eaecd<_0x59c05a[_0x21340e(0x104)];_0x9eaecd++){if(_0x59c05a[_0x9eaecd][_0x21340e(0xef)]){_0x397a89=!![];break;}}if(!_0x397a89){alert(_0x21340e(0xf3)),document[_0x21340e(0xe1)](_0x21340e(0xdf))[_0x21340e(0xef)]=![],chrome[_0x21340e(0xf5)][_0x21340e(0x102)][_0x21340e(0xf0)]({'tracker_run_status':![]});return;}chrome[_0x21340e(0xf5)]['local'][_0x21340e(0xf0)]({'tracker_run_status':!![]}),chrome[_0x21340e(0x106)][_0x21340e(0x100)]({'type':'start_tracking'});}),document[a0_0x4778f8(0xe1)](a0_0x4778f8(0xfe))[a0_0x4778f8(0x10b)](a0_0x4778f8(0xf9),async function(){var _0x3dffc5=a0_0x4778f8;console[_0x3dffc5(0xe2)](_0x3dffc5(0xe4));var _0x11f9d3=_0x3dffc5(0xfd),{amazonData:_0x2fd7ec}=await new Promise(_0x2d9d45=>{var _0x1e1737=_0x3dffc5;chrome[_0x1e1737(0x106)][_0x1e1737(0x100)]({'type':'fetch_amazon_data','url':_0x11f9d3},function(_0x3c706a){_0x2d9d45(_0x3c706a);});});console[_0x3dffc5(0xe2)](_0x3dffc5(0xe8),_0x2fd7ec);});