<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>eBay Arbitrage Researcher Pro - UI Test</title>
    <link rel="stylesheet" href="popup/popup.css">
    <style>
        body {
            margin: 20px;
            background: #f0f0f0;
        }
        .demo-container {
            max-width: 420px;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="container">
            <header class="header">
                <h1>🎯 Arbitrage Researcher Pro</h1>
                <div class="version">v1.0.0</div>
            </header>

            <!-- Quick Test Section -->
            <section class="quick-test-section">
                <h3>⚡ Quick Test</h3>
                <div class="quick-test-controls">
                    <div class="test-item">
                        <label for="maxPages">📄 Pages to scan:</label>
                        <input type="number" id="maxPages" value="1" min="1" max="50" class="test-input">
                    </div>
                    <div class="test-item">
                        <label for="minSales">📊 Min sales:</label>
                        <input type="number" id="minSales" value="3" min="1" max="50" class="test-input">
                    </div>
                </div>
            </section>

            <!-- Advanced Configuration (Collapsible) -->
            <section class="config-section">
                <div class="config-header" id="configToggle">
                    <h3>⚙️ Advanced Settings</h3>
                    <span class="toggle-icon">▼</span>
                </div>
                
                <div class="config-content" id="configContent" style="display: none;">
                    <div class="config-grid">
                        <div class="config-item">
                            <label for="daysBack">Days to analyze:</label>
                            <input type="number" id="daysBack" value="30" min="7" max="90">
                        </div>
                        
                        <div class="config-item">
                            <label for="minProfit">Min profit ($):</label>
                            <input type="number" id="minProfit" value="10" min="1" max="1000">
                        </div>
                        
                        <div class="config-item">
                            <label for="minMargin">Min margin (%):</label>
                            <input type="number" id="minMargin" value="15" min="5" max="100">
                        </div>
                        
                        <div class="config-item">
                            <label for="maxRisk">Max risk score:</label>
                            <input type="number" id="maxRisk" value="70" min="0" max="100">
                        </div>
                    </div>

                    <div class="price-range">
                        <h4>💰 Price Range</h4>
                        <div class="range-inputs">
                            <input type="number" id="minPrice" value="10" min="1" placeholder="Min $">
                            <span>to</span>
                            <input type="number" id="maxPrice" value="500" min="1" placeholder="Max $">
                        </div>
                    </div>
                </div>
            </section>

            <!-- Control Section -->
            <section class="control-section">
                <div class="main-controls">
                    <button id="startAnalysis" class="btn-primary">
                        🚀 Start Analysis
                    </button>
                    <button id="stopAnalysis" class="btn-stop" style="display: none;">
                        ⏹️ Stop
                    </button>
                </div>
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-text">Ready to start</span>
                    <span class="status-dot ready"></span>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-header">
                    <h3>📈 Analysis Progress</h3>
                    <span id="progressPercent">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-status" id="progressStatus">Ready to start...</div>
                <div class="progress-stats" id="progressStats"></div>
            </section>

            <!-- Results Section -->
            <section class="results-section">
                <div class="results-header">
                    <h3>💎 Opportunities Found</h3>
                    <div class="results-controls">
                        <span id="opportunityCount" class="count-badge">0</span>
                        <button id="exportResults" class="btn-export" disabled>📊 Export CSV</button>
                        <button id="refreshResults" class="btn-refresh">🔄 Refresh</button>
                    </div>
                </div>

                <!-- Results List -->
                <div class="results-list" id="resultsList">
                    <div class="no-results">
                        <p>🔍 No opportunities found yet.</p>
                        <p>Click "Start Analysis" to begin scanning for profitable products.</p>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Simple demo functionality
        document.getElementById('configToggle').addEventListener('click', function() {
            const content = document.getElementById('configContent');
            const icon = document.querySelector('.toggle-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.classList.add('expanded');
            } else {
                content.style.display = 'none';
                icon.classList.remove('expanded');
            }
        });

        document.getElementById('startAnalysis').addEventListener('click', function() {
            const statusText = document.querySelector('.status-text');
            const statusDot = document.querySelector('.status-dot');
            const progressSection = document.getElementById('progressSection');
            const stopBtn = document.getElementById('stopAnalysis');

            // Hide start button, show stop button
            this.style.display = 'none';
            stopBtn.style.display = 'block';

            statusText.textContent = 'Analysis in progress...';
            statusDot.classList.remove('ready');
            statusDot.classList.add('processing');
            progressSection.style.display = 'block';
        });

        document.getElementById('stopAnalysis').addEventListener('click', function() {
            const statusText = document.querySelector('.status-text');
            const statusDot = document.querySelector('.status-dot');
            const progressSection = document.getElementById('progressSection');
            const startBtn = document.getElementById('startAnalysis');

            // Hide stop button, show start button
            this.style.display = 'none';
            startBtn.style.display = 'block';

            statusText.textContent = 'Analysis stopped';
            statusDot.classList.remove('processing');
            statusDot.classList.add('ready');
            progressSection.style.display = 'none';
        });
    </script>
</body>
</html>
