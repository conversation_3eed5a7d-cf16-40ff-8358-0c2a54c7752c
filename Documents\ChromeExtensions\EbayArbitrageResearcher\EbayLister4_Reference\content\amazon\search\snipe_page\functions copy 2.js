const a0_0x19986d=a0_0x208e;function a0_0x539b(){const _0x3523ac=['forEach','sniper-animation-container','Sniped!','split','snipedItemData:\x20','false','contenteditable','12330675pTCyfD','word-bank-label','input','opacity','data-last-valid-adRate','transform','sniper-animation-fade-out','body','createRange','4893693iNOjKu','word-bank-container','addRange','price','snipeMode-competitor-price','top\x201s\x20ease-out\x20','custom-title-container','sniper-overlay-fade-out','preventDefault','char-counter','/80','innerHTML','1525682ATIFQn','src','fade-out','left\x201s\x20ease-in-out,\x20top\x201s\x20ease-in-out','.s-product-image-container','test','relative','paste','customFeaturesLeftContainer:\x20','log','Price\x20Updated!','span','.new-letter','letters-wrapped','snipeMode-content','.custom-features-right-container','normal','spellcheck','undefined','getElementById','map','Click\x20words\x20to\x20add\x20to\x20your\x20custom\x20title:','price-update-confirmation','querySelector','alt','#007600','.custom-price-element','custom-features-left-container','custom-title-element','left\x201.5s\x20ease-in-out\x20','text','insertBefore','getSelection','<span\x20class=\x22new-letter\x22\x20data-index=\x22','.char-counter','absolute','floating-loop','Click\x20here\x20to\x20edit\x20custom\x20title\x20(max\x2080\x20characters)','replaceChild','pointer','random','adRate-update-confirmation','textbox','dialog','Parent\x20container\x20not\x20found\x20in\x20the\x20card:','currentTop','0px','sniper-message','sniper-animation-image','card:\x20','not-allowed','hidden','from','Cleaning\x20icon\x20already\x20exists\x20in\x20this\x20card:','createElement','custom-price-element','color','snipeMode-close-button','addEventListener','snipeMode-banner-container','collapse','clipboardData','Enter\x20your\x20price','194755QuvYdm','13120605DIJkCV','trim','className','.letter','cleaning-icon','10GFjRRJ','warn','parentNode','setAttribute','.custom-features-left-container','custom-adRate-element','contentEditable','image','Clean\x20Custom\x20Title','assignedOldLetter','aria-label','button','add','\x22>&nbsp;</span>','top\x201s\x20ease,\x20opacity\x201s\x20ease','webkitLineClamp','left','visible','1868976DTyXvK','img','Product\x20image\x20container\x20not\x20found\x20in\x20the\x20card:','remove','$0.00','asin','</span>','cursor','2300500sloVYv','runtime','.a-button-text','target','div','tabindex','parentElement','style','keyup','insertText','Custom\x20title\x20element\x20not\x20found:','76xRlytm','appendChild','.custom-title-element','2.%','middleContainer\x20not\x20found','textContent','transition','data-last-valid-price','contains','.s-result-item','text/plain','getAttribute','length','sniper-message-fade-out','getData','snipe-button','.puis-atcb-add-container\x20.a-button-primary','title','8heBWMf','getURL','join','Custom\x20Ad\x20Rate','floor','invalid','High\x20Quality\x20Cat6\x20Cable','auto','.puis-card-container','.cleaning-icon','char','clip','Sniped','top','Snipe\x20List','Sniper\x20Mark','stopPropagation','data-snipe-title','push','s-line-clamp-4','amazon_search_page_functions_snipe.js','aria-modal','a-button-primary','disabled','Price:\x20$','$29.99','getBoundingClientRect','&times;','blur','data-placeholder','Custom\x20features\x20left\x20container\x20not\x20found\x20in\x20the\x20card:','data-custom-title','custom-features-right-container','role','data-snipe-item-number','sniper-mark-single','word-button','removeChild','snipeMode-competitor-image','visibility','.s-title-instructions-style','Price\x20element\x20not\x20found\x20in\x20the\x20card:','querySelectorAll','selectNodeContents','<span\x20class=\x22letter\x22\x20style=\x22--animation-order:','transitionend','position','none','assigned','currentLeft','exceeds-limit','Refresh\x20Custom\x20Titles','custom-title-features-container','closest','Custom\x20Listing\x20Price','classList','true','focus','whiteSpace','click','wow-animation'];a0_0x539b=function(){return _0x3523ac;};return a0_0x539b();}(function(_0x562eb3,_0x25fd61){const _0x36c89d=a0_0x208e,_0xcf0ee8=_0x562eb3();while(!![]){try{const _0x2dc3fa=parseInt(_0x36c89d(0x257))/0x1+-parseInt(_0x36c89d(0x2b6))/0x2+-parseInt(_0x36c89d(0x24b))/0x3+-parseInt(_0x36c89d(0x1ec))/0x4*(parseInt(_0x36c89d(0x296))/0x5)+-parseInt(_0x36c89d(0x2ae))/0x6+-parseInt(_0x36c89d(0x242))/0x7*(-parseInt(_0x36c89d(0x1fe))/0x8)+-parseInt(_0x36c89d(0x297))/0x9*(-parseInt(_0x36c89d(0x29c))/0xa);if(_0x2dc3fa===_0x25fd61)break;else _0xcf0ee8['push'](_0xcf0ee8['shift']());}catch(_0x5cab03){_0xcf0ee8['push'](_0xcf0ee8['shift']());}}}(a0_0x539b,0xdea86),console[a0_0x19986d(0x260)](a0_0x19986d(0x212)));async function activateSnipeMode(_0xbd5de4){const _0x477adc=a0_0x19986d;console['log'](_0x477adc(0x23f),_0xbd5de4),addBanner(_0xbd5de4[_0x477adc(0x1fd)],_0xbd5de4[_0x477adc(0x24e)],_0xbd5de4[_0x477adc(0x2b3)],_0xbd5de4[_0x477adc(0x2a3)]);var _0xa8ecb=document[_0x477adc(0x228)](_0x477adc(0x1f5));for(let _0x3aca0d=0x0;_0x3aca0d<_0xa8ecb['length'];_0x3aca0d++){let _0x8ebfc3=_0xa8ecb[_0x3aca0d];addCustomTitleTextBox(_0x8ebfc3),movePriceToTop(_0x8ebfc3),addCustomEditablePrice(_0x8ebfc3,_0x477adc(0x217)),addCleaningIcon(_0x8ebfc3),addSnipeButton(_0x8ebfc3),addCustomEditableAdRateToContainer(_0x8ebfc3);}}function a0_0x208e(_0x182aae,_0x5c8ab4){const _0x539b1b=a0_0x539b();return a0_0x208e=function(_0x208e40,_0x46db7f){_0x208e40=_0x208e40-0x1e3;let _0x19eaec=_0x539b1b[_0x208e40];return _0x19eaec;},a0_0x208e(_0x182aae,_0x5c8ab4);}function wrapLetters(_0x1c3297){const _0x103d39=a0_0x19986d,_0x171a8c=_0x1c3297[_0x103d39(0x1f1)],_0x3b413d=_0x171a8c[_0x103d39(0x23e)](''),_0x596a70=_0x3b413d[_0x103d39(0x26b)]((_0x48ee4c,_0x754b51)=>{const _0x53f81d=_0x103d39;if(_0x48ee4c==='\x20')return _0x53f81d(0x22a)+_0x754b51+_0x53f81d(0x2a9);return _0x53f81d(0x22a)+_0x754b51+'\x22>'+_0x48ee4c+_0x53f81d(0x2b4);})[_0x103d39(0x200)]('');_0x1c3297['innerHTML']=_0x596a70;}async function getCleanTitle2(){const _0x1139fe=a0_0x19986d,_0x3157ce=Math[_0x1139fe(0x202)](Math[_0x1139fe(0x27f)]()*0xbb8)+0x7d0;return new Promise(_0x473438=>{setTimeout(()=>{const _0x4e7d42=a0_0x208e;_0x473438(_0x4e7d42(0x204));},_0x3157ce);});}async function addCleaningIcon(_0x41a24a){const _0x5f0abd=a0_0x19986d,_0x5e39da=_0x41a24a[_0x5f0abd(0x26e)]('.custom-title-container');if(!_0x5e39da){console[_0x5f0abd(0x29d)]('Custom\x20title\x20container\x20not\x20found\x20in\x20the\x20card:',_0x41a24a);return;}if(_0x5e39da[_0x5f0abd(0x26e)](_0x5f0abd(0x207))){console[_0x5f0abd(0x29d)](_0x5f0abd(0x28c),_0x41a24a);return;}const _0x4ac27e=document[_0x5f0abd(0x28d)](_0x5f0abd(0x1e5));_0x4ac27e[_0x5f0abd(0x299)]=_0x5f0abd(0x29b),_0x4ac27e['setAttribute'](_0x5f0abd(0x1fd),_0x5f0abd(0x2a4)),_0x4ac27e[_0x5f0abd(0x29f)](_0x5f0abd(0x21f),_0x5f0abd(0x2a7)),_0x4ac27e[_0x5f0abd(0x29f)]('aria-label','Clean\x20Custom\x20Title');var _0x420470=await createSpinner(_0x5f0abd(0x221));console[_0x5f0abd(0x260)]('spinner:\x20',_0x420470),_0x4ac27e[_0x5f0abd(0x1ed)](_0x420470);const _0x5b992e=_0x5e39da[_0x5f0abd(0x26e)](_0x5f0abd(0x279));if(_0x5b992e){const _0x1c03dc=_0x5e39da[_0x5f0abd(0x26e)](_0x5f0abd(0x266));_0x1c03dc?_0x1c03dc['insertBefore'](_0x4ac27e,_0x5b992e):_0x5e39da[_0x5f0abd(0x276)](_0x4ac27e,_0x5b992e);}else _0x5e39da[_0x5f0abd(0x1ed)](_0x4ac27e);_0x4ac27e[_0x5f0abd(0x291)]('click',async function(_0x34e47f){const _0xfd35e3=_0x5f0abd;var _0x1f4276=_0x34e47f[_0xfd35e3(0x1e4)]['parentElement'],_0x528f98=_0x1f4276[_0xfd35e3(0x26e)]('.sniper-mark');startSpinSingleElement(_0x528f98),_0x4ac27e[_0xfd35e3(0x235)][_0xfd35e3(0x2a8)](_0xfd35e3(0x215)),_0x4ac27e[_0xfd35e3(0x1e8)][_0xfd35e3(0x2b5)]=_0xfd35e3(0x289);const _0xf196b5=_0x5e39da[_0xfd35e3(0x26e)]('.custom-title-element');if(!_0xf196b5){console[_0xfd35e3(0x29d)](_0xfd35e3(0x1eb),_0x41a24a);return;}!_0xf196b5[_0xfd35e3(0x235)][_0xfd35e3(0x1f4)](_0xfd35e3(0x264))&&(wrapLetters(_0xf196b5),_0xf196b5[_0xfd35e3(0x235)][_0xfd35e3(0x2a8)](_0xfd35e3(0x264)));_0xf196b5[_0xfd35e3(0x235)][_0xfd35e3(0x2a8)](_0xfd35e3(0x27b));var _0x261ef5=extractTitleFromCard(_0x41a24a),_0x4ef29c=_0xf196b5[_0xfd35e3(0x1f1)],_0x59dd5c=await getCleanTitle2();startMagicalAnimation(_0xf196b5,_0x59dd5c,_0x5e39da,_0x4ac27e),stopSpinSingleElement(_0x528f98),startFlashSingleElement(_0x528f98),await new Promise(_0x4c50dc=>setTimeout(_0x4c50dc,0x7d0)),stopFlashSingleElement(_0x528f98);});}function startMagicalAnimation(_0x23a046,_0x4c508d,_0x534e19,_0x3efd4c){const _0x26f59e=a0_0x19986d;_0x23a046[_0x26f59e(0x235)]['remove'](_0x26f59e(0x27b));const _0xddbaae=Array[_0x26f59e(0x28b)](_0x23a046[_0x26f59e(0x228)](_0x26f59e(0x29a)));_0x23a046['style'][_0x26f59e(0x22c)]=_0x26f59e(0x25d),_0xddbaae['forEach'](_0x4c0cf7=>{const _0x572932=_0x26f59e,_0x3a1b3d=_0x4c0cf7[_0x572932(0x218)](),_0x1476d5=_0x23a046[_0x572932(0x218)](),_0x313405=_0x3a1b3d[_0x572932(0x2ac)]-_0x1476d5[_0x572932(0x2ac)],_0x6fda71=_0x3a1b3d[_0x572932(0x20b)]-_0x1476d5['top'];_0x4c0cf7[_0x572932(0x1e8)]['position']=_0x572932(0x27a),_0x4c0cf7[_0x572932(0x1e8)][_0x572932(0x2ac)]=_0x313405+'px',_0x4c0cf7[_0x572932(0x1e8)][_0x572932(0x20b)]=_0x6fda71+'px',_0x4c0cf7[_0x572932(0x1e8)]['margin']='0',_0x4c0cf7[_0x572932(0x22f)]=_0x313405,_0x4c0cf7[_0x572932(0x284)]=_0x6fda71,_0x4c0cf7[_0x572932(0x22e)]=![];}),_0xddbaae['forEach'](_0x26a0be=>{const _0x1030ef=_0x26f59e,_0x1ecddb=(Math[_0x1030ef(0x27f)]()-0.5)*0xc8,_0x2b9b79=(Math['random']()-0.5)*0xc8,_0x46d88a=_0x26a0be[_0x1030ef(0x22f)]+_0x1ecddb,_0x50c90e=_0x26a0be[_0x1030ef(0x284)]+_0x2b9b79;_0x26a0be[_0x1030ef(0x1e8)]['transition']=_0x1030ef(0x25a),_0x26a0be['style'][_0x1030ef(0x2ac)]=_0x46d88a+'px',_0x26a0be[_0x1030ef(0x1e8)]['top']=_0x50c90e+'px',_0x26a0be['currentLeft']=_0x46d88a,_0x26a0be['currentTop']=_0x50c90e;}),setTimeout(()=>{rearrangeLetters(_0x23a046,_0x4c508d,_0x534e19,_0x3efd4c);},0x3e8);}function rearrangeLetters(_0x16660c,_0x4a0f99,_0x8c26f5,_0x20735b){const _0x15214f=a0_0x19986d,_0x517e9d=Array[_0x15214f(0x28b)](_0x16660c[_0x15214f(0x228)](_0x15214f(0x29a))),_0x10769d=_0x4a0f99[_0x15214f(0x23e)](''),_0x47c333=[];_0x10769d[_0x15214f(0x23b)](_0x5157cd=>{const _0x108f02=_0x15214f;let _0x4bd6e9=null;for(const _0x23ffae of _0x517e9d){if(!_0x23ffae['assigned']&&_0x23ffae[_0x108f02(0x1f1)]['trim']()===_0x5157cd){_0x4bd6e9=_0x23ffae,_0x23ffae[_0x108f02(0x22e)]=!![];break;}}_0x47c333[_0x108f02(0x210)]({'char':_0x5157cd,'assignedOldLetter':_0x4bd6e9,'position':null});});const _0x4a411d=document[_0x15214f(0x28d)](_0x15214f(0x1e5));_0x4a411d[_0x15214f(0x1e8)][_0x15214f(0x225)]=_0x15214f(0x28a),_0x4a411d['style'][_0x15214f(0x22c)]=_0x15214f(0x27a),_0x4a411d[_0x15214f(0x1e8)]['top']=_0x15214f(0x285),_0x4a411d[_0x15214f(0x1e8)][_0x15214f(0x2ac)]=_0x15214f(0x285),_0x4a411d[_0x15214f(0x1e8)][_0x15214f(0x238)]='nowrap',_0x16660c[_0x15214f(0x1ed)](_0x4a411d);const _0x48fe64=_0x10769d[_0x15214f(0x26b)]((_0x14fd31,_0x2216c7)=>{const _0x471813=_0x15214f;if(_0x14fd31==='\x20')return _0x471813(0x278)+_0x2216c7+'\x22>&nbsp;</span>';return _0x471813(0x278)+_0x2216c7+'\x22>'+_0x14fd31+_0x471813(0x2b4);})['join']('');_0x4a411d['innerHTML']=_0x48fe64;const _0x5a2628=Array[_0x15214f(0x28b)](_0x4a411d[_0x15214f(0x228)](_0x15214f(0x263)));_0x5a2628[_0x15214f(0x23b)]((_0x28bf6d,_0x1309c4)=>{const _0x187af1=_0x15214f,_0x820e50=_0x28bf6d[_0x187af1(0x218)](),_0x4c2719=_0x16660c[_0x187af1(0x218)](),_0xa89437=_0x820e50[_0x187af1(0x2ac)]-_0x4c2719[_0x187af1(0x2ac)],_0x497728=_0x820e50[_0x187af1(0x20b)]-_0x4c2719['top'];_0x47c333[_0x1309c4][_0x187af1(0x22c)]={'left':_0xa89437,'top':_0x497728};}),_0x16660c[_0x15214f(0x223)](_0x4a411d),_0x47c333[_0x15214f(0x23b)]((_0x5f3f4e,_0x3de0f7)=>{const _0x31940a=_0x15214f;if(_0x5f3f4e[_0x31940a(0x2a5)]){const _0x360efe=_0x5f3f4e['assignedOldLetter'];_0x360efe[_0x31940a(0x1e8)][_0x31940a(0x1f2)]=_0x31940a(0x274)+_0x3de0f7*0.05+'s,\x20top\x201.5s\x20ease-in-out\x20'+_0x3de0f7*0.05+'s',_0x360efe['style'][_0x31940a(0x2ac)]=_0x5f3f4e['position'][_0x31940a(0x2ac)]+'px',_0x360efe[_0x31940a(0x1e8)][_0x31940a(0x20b)]=_0x5f3f4e['position'][_0x31940a(0x20b)]+'px';}}),_0x517e9d[_0x15214f(0x23b)](_0x5dc0c8=>{const _0x5d2c87=_0x15214f;!_0x5dc0c8[_0x5d2c87(0x22e)]&&(setTimeout(()=>{const _0x12fd21=_0x5d2c87;_0x5dc0c8['style']['transition']=_0x12fd21(0x2aa),_0x5dc0c8[_0x12fd21(0x1e8)][_0x12fd21(0x20b)]=parseFloat(_0x5dc0c8['style'][_0x12fd21(0x20b)])+0x64+'px',_0x5dc0c8['style'][_0x12fd21(0x245)]='0';},0x1f4),setTimeout(()=>{const _0x3497b4=_0x5d2c87;_0x5dc0c8[_0x3497b4(0x2b1)]();},0x5dc));}),_0x47c333[_0x15214f(0x23b)]((_0x12f392,_0x21fa08)=>{const _0x3886aa=_0x15214f;if(!_0x12f392[_0x3886aa(0x2a5)]){const _0x335dd6=document['createElement'](_0x3886aa(0x262));_0x335dd6[_0x3886aa(0x299)]='letter',_0x335dd6[_0x3886aa(0x1e8)]['position']=_0x3886aa(0x27a),_0x335dd6[_0x3886aa(0x1e8)][_0x3886aa(0x2ac)]=_0x12f392[_0x3886aa(0x22c)][_0x3886aa(0x2ac)]+'px',_0x335dd6[_0x3886aa(0x1e8)][_0x3886aa(0x20b)]='-'+(Math[_0x3886aa(0x27f)]()*0x32+0x32)+'px',_0x335dd6['style'][_0x3886aa(0x245)]='0',_0x12f392['char']==='\x20'?_0x335dd6[_0x3886aa(0x256)]='&nbsp;':_0x335dd6[_0x3886aa(0x1f1)]=_0x12f392[_0x3886aa(0x208)],_0x16660c[_0x3886aa(0x1ed)](_0x335dd6),setTimeout(()=>{const _0x58037d=_0x3886aa;_0x335dd6[_0x58037d(0x1e8)][_0x58037d(0x1f2)]=_0x58037d(0x250)+_0x21fa08*0.05+'s,\x20opacity\x201s\x20ease\x20'+_0x21fa08*0.05+'s',_0x335dd6['style']['top']=_0x12f392[_0x58037d(0x22c)][_0x58037d(0x20b)]+'px',_0x335dd6[_0x58037d(0x1e8)][_0x58037d(0x245)]='1';},0x64);}}),setTimeout(()=>{const _0x4b7592=_0x15214f,_0xcb8ad1=_0x8c26f5['querySelector'](_0x4b7592(0x279));_0xcb8ad1&&(_0xcb8ad1['textContent']=_0x4a0f99[_0x4b7592(0x1f8)]+_0x4b7592(0x255)),_0x16660c[_0x4b7592(0x235)][_0x4b7592(0x2a8)]('wow-animation'),setTimeout(()=>{const _0x1245db=_0x4b7592;_0x16660c['classList'][_0x1245db(0x2b1)](_0x1245db(0x23a));const _0xbabd6d=_0x16660c[_0x1245db(0x228)](_0x1245db(0x29a));_0xbabd6d[_0x1245db(0x23b)](_0x1f0887=>{const _0x8b21d2=_0x1245db;_0x1f0887[_0x8b21d2(0x1e8)][_0x8b21d2(0x22c)]='',_0x1f0887[_0x8b21d2(0x1e8)]['left']='',_0x1f0887[_0x8b21d2(0x1e8)][_0x8b21d2(0x20b)]='',_0x1f0887[_0x8b21d2(0x1e8)][_0x8b21d2(0x1f2)]='',_0x1f0887[_0x8b21d2(0x1e8)][_0x8b21d2(0x247)]='',_0x1f0887['style'][_0x8b21d2(0x245)]='';}),_0x16660c['textContent']=_0x4a0f99,wrapLetters(_0x16660c);},0x7d0),_0x20735b[_0x4b7592(0x235)]['remove'](_0x4b7592(0x215)),_0x20735b[_0x4b7592(0x1e8)][_0x4b7592(0x2b5)]=_0x4b7592(0x27e);},0x9c4);}function addCustomEditablePrice(_0x426190,_0x3b486a='$0.00'){const _0x35f6aa=a0_0x19986d;if(!_0x426190){console['warn']('Invalid\x20card\x20element\x20provided.');return;}if(_0x426190[_0x35f6aa(0x26e)](_0x35f6aa(0x271))){console[_0x35f6aa(0x29d)]('Custom\x20price\x20element\x20already\x20exists\x20in\x20this\x20card:',_0x426190);return;}const _0x35b475=_0x426190['querySelector'](_0x35f6aa(0x206));if(!_0x35b475){console[_0x35f6aa(0x29d)](_0x35f6aa(0x283),_0x426190);return;}const _0x1b20f3=window['getComputedStyle'](_0x35b475);_0x1b20f3[_0x35f6aa(0x22c)]==='static'&&(_0x35b475[_0x35f6aa(0x1e8)][_0x35f6aa(0x22c)]='relative');const _0x3c7204=document[_0x35f6aa(0x28d)]('div');_0x3c7204[_0x35f6aa(0x299)]=_0x35f6aa(0x28e),_0x3c7204[_0x35f6aa(0x29f)](_0x35f6aa(0x241),_0x35f6aa(0x236)),_0x3c7204[_0x35f6aa(0x29f)](_0x35f6aa(0x21b),_0x35f6aa(0x295)),_0x3c7204[_0x35f6aa(0x29f)](_0x35f6aa(0x268),_0x35f6aa(0x240)),_0x3c7204[_0x35f6aa(0x29f)](_0x35f6aa(0x21f),'textbox'),_0x3c7204['setAttribute'](_0x35f6aa(0x2a6),_0x35f6aa(0x234)),_0x3c7204['setAttribute'](_0x35f6aa(0x1e6),'0'),_0x3c7204[_0x35f6aa(0x1f1)]=_0x3b486a,_0x3c7204['setAttribute']('data-last-valid-price',_0x3b486a),_0x3c7204['addEventListener'](_0x35f6aa(0x237),function(){const _0x5b9bf8=_0x35f6aa;this['textContent'][_0x5b9bf8(0x298)]()===_0x5b9bf8(0x2b2)&&(this[_0x5b9bf8(0x1f1)]='');}),_0x3c7204[_0x35f6aa(0x291)](_0x35f6aa(0x21a),function(){const _0x17e3b9=_0x35f6aa;this['textContent'][_0x17e3b9(0x298)]()===''?this[_0x17e3b9(0x1f1)]=_0x17e3b9(0x2b2):_0x559034(this);});function _0x559034(_0x442f84){const _0x18a3ec=_0x35f6aa;let _0x398e64=_0x442f84[_0x18a3ec(0x1f1)][_0x18a3ec(0x298)]();const _0x2f5346=/^\$\d{1,3}(,\d{3})*(\.\d{2})?$/;!_0x2f5346[_0x18a3ec(0x25c)](_0x398e64)?(_0x442f84['classList']['add'](_0x18a3ec(0x203)),setTimeout(()=>{const _0x668e5a=_0x18a3ec;_0x442f84[_0x668e5a(0x1f1)]=_0x442f84[_0x668e5a(0x1f7)](_0x668e5a(0x1f3))||_0x668e5a(0x2b2),_0x442f84[_0x668e5a(0x235)]['remove'](_0x668e5a(0x203));},0x5dc)):(_0x442f84[_0x18a3ec(0x29f)]('data-last-valid-price',_0x398e64),_0x442f84[_0x18a3ec(0x235)]['remove'](_0x18a3ec(0x203)),_0x7616fb(_0x442f84));}function _0x7616fb(_0x1c5cb3){const _0x188e81=_0x35f6aa,_0x1810af=document[_0x188e81(0x28d)](_0x188e81(0x1e5));_0x1810af[_0x188e81(0x299)]=_0x188e81(0x26d),_0x1810af['textContent']=_0x188e81(0x261),_0x1c5cb3[_0x188e81(0x1ed)](_0x1810af),setTimeout(()=>{const _0x51aa55=_0x188e81;_0x1810af[_0x51aa55(0x235)][_0x51aa55(0x2a8)]('fade-out'),_0x1810af[_0x51aa55(0x291)](_0x51aa55(0x22b),()=>{const _0x200088=_0x51aa55;_0x1810af[_0x200088(0x29e)]&&_0x1810af['parentNode'][_0x200088(0x223)](_0x1810af);});},0x3e8);}const _0x3ea083=_0x426190[_0x35f6aa(0x26e)](_0x35f6aa(0x2a0));_0x3ea083[_0x35f6aa(0x1ed)](_0x3c7204);}function movePriceToTop(_0x5324a1){const _0x59f007=a0_0x19986d,_0x46c765=_0x5324a1[_0x59f007(0x26e)]('.s-price-instructions-style');if(!_0x46c765){console[_0x59f007(0x29d)](_0x59f007(0x227),_0x5324a1);return;}const _0x29d5f1=_0x5324a1[_0x59f007(0x26e)](_0x59f007(0x25b));if(!_0x29d5f1){console[_0x59f007(0x29d)](_0x59f007(0x2b0),_0x5324a1);return;}const _0x86ef76=_0x29d5f1['parentNode'];_0x86ef76[_0x59f007(0x276)](_0x46c765,_0x29d5f1),_0x46c765['classList'][_0x59f007(0x2a8)]('price-at-top');}function modifyAddToCartButton(_0x57867e){const _0x3f62e9=a0_0x19986d;let _0x50069c=_0x57867e[_0x3f62e9(0x26e)](_0x3f62e9(0x1fc));if(_0x50069c){let _0xc66315=_0x50069c[_0x3f62e9(0x26e)](_0x3f62e9(0x1e3));_0xc66315&&(_0xc66315[_0x3f62e9(0x1f1)]='Snipe'),_0x50069c[_0x3f62e9(0x235)][_0x3f62e9(0x2b1)](_0x3f62e9(0x214)),_0x50069c['classList'][_0x3f62e9(0x2a8)](_0x3f62e9(0x1fb)),_0x50069c[_0x3f62e9(0x291)](_0x3f62e9(0x239),function(_0x352e3d){const _0xb15036=_0x3f62e9;_0x352e3d[_0xb15036(0x253)](),_0x352e3d[_0xb15036(0x20e)](),_0x346ddd(),_0xc66315[_0xb15036(0x215)]=!![],_0xc66315[_0xb15036(0x1f1)]=_0xb15036(0x20a);});}function _0x346ddd(){const _0x3b8719=_0x3f62e9;if(document['querySelector']('.sniper-animation-container'))return;let _0x3826cf=document[_0x3b8719(0x28d)](_0x3b8719(0x1e5));_0x3826cf['className']='sniper-overlay',_0x3826cf['setAttribute'](_0x3b8719(0x21f),_0x3b8719(0x282)),_0x3826cf['setAttribute'](_0x3b8719(0x213),'true');let _0x51f1e7=document[_0x3b8719(0x28d)](_0x3b8719(0x1e5));_0x51f1e7[_0x3b8719(0x299)]=_0x3b8719(0x23c);let _0x540724=document[_0x3b8719(0x28d)](_0x3b8719(0x2af));_0x540724['className']=_0x3b8719(0x287),_0x540724[_0x3b8719(0x258)]=chrome[_0x3b8719(0x2b7)][_0x3b8719(0x1ff)]('libraries/target-spin/target.png'),_0x540724[_0x3b8719(0x26f)]=_0x3b8719(0x20d),_0x51f1e7[_0x3b8719(0x1ed)](_0x540724),document[_0x3b8719(0x249)][_0x3b8719(0x1ed)](_0x3826cf),document[_0x3b8719(0x249)][_0x3b8719(0x1ed)](_0x51f1e7);let _0x4bcbbd=document[_0x3b8719(0x28d)]('div');_0x4bcbbd[_0x3b8719(0x299)]='sniper-message',_0x4bcbbd['textContent']=_0x3b8719(0x23d),document[_0x3b8719(0x249)][_0x3b8719(0x1ed)](_0x4bcbbd);function _0x48addb(){const _0x59d5d2=_0x3b8719;_0x51f1e7[_0x59d5d2(0x235)][_0x59d5d2(0x2a8)](_0x59d5d2(0x248)),_0x3826cf['classList'][_0x59d5d2(0x2a8)](_0x59d5d2(0x252)),_0x4bcbbd[_0x59d5d2(0x235)]['add'](_0x59d5d2(0x1f9)),setTimeout(function(){const _0x6d801=_0x59d5d2;_0x51f1e7[_0x6d801(0x29e)]&&_0x51f1e7[_0x6d801(0x29e)][_0x6d801(0x223)](_0x51f1e7),_0x3826cf[_0x6d801(0x29e)]&&_0x3826cf['parentNode'][_0x6d801(0x223)](_0x3826cf),_0x4bcbbd[_0x6d801(0x29e)]&&_0x4bcbbd[_0x6d801(0x29e)][_0x6d801(0x223)](_0x4bcbbd);},0x1f4);}_0x3826cf[_0x3b8719(0x291)](_0x3b8719(0x239),function(_0x5575de){_0x48addb();}),_0x51f1e7[_0x3b8719(0x291)](_0x3b8719(0x239),function(_0x312af0){_0x312af0['stopPropagation']();}),setTimeout(function(){_0x48addb();},0x7d0);}}function addSnipeButton(_0x417219){const _0x272439=a0_0x19986d;var _0x508909=document[_0x272439(0x28d)](_0x272439(0x1e5));_0x508909[_0x272439(0x1f1)]=_0x272439(0x20c),_0x508909[_0x272439(0x235)]['remove'](_0x272439(0x214)),_0x508909['classList'][_0x272439(0x2a8)]('snipe-button'),_0x508909['addEventListener'](_0x272439(0x239),function(_0xcccfbe){const _0x78e962=_0x272439;_0xcccfbe['preventDefault'](),_0xcccfbe[_0x78e962(0x20e)](),_0x5e7a62(),buttonTextElement[_0x78e962(0x215)]=!![],buttonTextElement[_0x78e962(0x1f1)]=_0x78e962(0x20a);});function _0x5e7a62(){const _0x2b3a38=_0x272439;if(document[_0x2b3a38(0x26e)]('.sniper-animation-container'))return;let _0x275ef7=document['createElement'](_0x2b3a38(0x1e5));_0x275ef7[_0x2b3a38(0x299)]='sniper-overlay',_0x275ef7['setAttribute'](_0x2b3a38(0x21f),'dialog'),_0x275ef7[_0x2b3a38(0x29f)](_0x2b3a38(0x213),_0x2b3a38(0x236));let _0x396ff2=document['createElement'](_0x2b3a38(0x1e5));_0x396ff2[_0x2b3a38(0x299)]='sniper-animation-container';let _0x35eea1=document[_0x2b3a38(0x28d)]('img');_0x35eea1['className']=_0x2b3a38(0x287),_0x35eea1[_0x2b3a38(0x258)]=chrome[_0x2b3a38(0x2b7)][_0x2b3a38(0x1ff)]('libraries/target-spin/target.png'),_0x35eea1[_0x2b3a38(0x26f)]='Sniper\x20Mark',_0x396ff2[_0x2b3a38(0x1ed)](_0x35eea1),document[_0x2b3a38(0x249)][_0x2b3a38(0x1ed)](_0x275ef7),document[_0x2b3a38(0x249)]['appendChild'](_0x396ff2);let _0x1e731c=document['createElement'](_0x2b3a38(0x1e5));_0x1e731c[_0x2b3a38(0x299)]=_0x2b3a38(0x286),_0x1e731c[_0x2b3a38(0x1f1)]=_0x2b3a38(0x23d),document[_0x2b3a38(0x249)][_0x2b3a38(0x1ed)](_0x1e731c);function _0x55411e(){const _0x96afc2=_0x2b3a38;_0x396ff2[_0x96afc2(0x235)][_0x96afc2(0x2a8)](_0x96afc2(0x248)),_0x275ef7[_0x96afc2(0x235)]['add'](_0x96afc2(0x252)),_0x1e731c[_0x96afc2(0x235)][_0x96afc2(0x2a8)]('sniper-message-fade-out'),setTimeout(function(){const _0x3cb552=_0x96afc2;_0x396ff2[_0x3cb552(0x29e)]&&_0x396ff2['parentNode'][_0x3cb552(0x223)](_0x396ff2),_0x275ef7[_0x3cb552(0x29e)]&&_0x275ef7[_0x3cb552(0x29e)][_0x3cb552(0x223)](_0x275ef7),_0x1e731c['parentNode']&&_0x1e731c[_0x3cb552(0x29e)][_0x3cb552(0x223)](_0x1e731c);},0x1f4);}_0x275ef7['addEventListener']('click',function(_0xbcc970){_0x55411e();}),_0x396ff2['addEventListener']('click',function(_0x1f4936){const _0x1e216f=_0x2b3a38;_0x1f4936[_0x1e216f(0x20e)]();}),setTimeout(function(){_0x55411e();},0x7d0);}var _0x286892=_0x417219['querySelector']('.custom-features-middle-container');console[_0x272439(0x260)]('middleContainer:\x20',_0x286892),console[_0x272439(0x260)](_0x272439(0x288),_0x417219);if(!_0x286892){console['log'](_0x272439(0x1f0));return;}_0x286892[_0x272439(0x1ed)](_0x508909);}async function addCustomTitleTextBox(_0x3e0867){const _0xf36db1=a0_0x19986d;var _0x1da9fe=document[_0xf36db1(0x26a)](_0xf36db1(0x292)),_0x551d67='';_0x1da9fe&&(_0x551d67=_0x1da9fe['getAttribute'](_0xf36db1(0x20f))||'');let _0x4416e0=_0x3e0867[_0xf36db1(0x26e)](_0xf36db1(0x226));if(!_0x4416e0)return;let _0x2b2449=_0x4416e0['querySelectorAll']('h2');_0x2b2449['forEach'](function(_0x3b2f24){const _0x25c4cb=_0xf36db1;_0x3b2f24['classList']['remove'](_0x25c4cb(0x211),'s-line-clamp-1'),_0x3b2f24[_0x25c4cb(0x1e8)][_0x25c4cb(0x238)]=_0x25c4cb(0x267),_0x3b2f24[_0x25c4cb(0x1e8)]['overflow']=_0x25c4cb(0x2ad),_0x3b2f24[_0x25c4cb(0x1e8)]['textOverflow']=_0x25c4cb(0x209),_0x3b2f24[_0x25c4cb(0x1e8)][_0x25c4cb(0x2ab)]='unset',_0x3b2f24[_0x25c4cb(0x1e8)]['maxHeight']=_0x25c4cb(0x22d),_0x3b2f24['style']['height']=_0x25c4cb(0x205),_0x3b2f24[_0x25c4cb(0x1e8)][_0x25c4cb(0x28f)]=_0x25c4cb(0x270);let _0x3018bb=_0x3b2f24[_0x25c4cb(0x228)]('a');_0x3018bb['forEach'](function(_0x265a96){const _0x5abf6e=_0x25c4cb;let _0x5365a1=_0x265a96[_0x5abf6e(0x256)],_0x340c3a=document['createElement'](_0x5abf6e(0x262));_0x340c3a[_0x5abf6e(0x256)]=_0x5365a1,_0x340c3a[_0x5abf6e(0x1e8)][_0x5abf6e(0x2b5)]=_0x5abf6e(0x275),_0x265a96[_0x5abf6e(0x29e)][_0x5abf6e(0x27d)](_0x340c3a,_0x265a96);});});let _0x502cab=_0x2b2449[_0x2b2449[_0xf36db1(0x1f8)]-0x1][_0xf36db1(0x1f1)][_0xf36db1(0x298)](),_0x24a72e=Array['from'](new Set(_0x502cab['split'](/\s+/))),_0x6faabf=document[_0xf36db1(0x28d)](_0xf36db1(0x1e5));_0x6faabf[_0xf36db1(0x299)]=_0xf36db1(0x24c);let _0x4a5076=document['createElement']('div');_0x4a5076[_0xf36db1(0x299)]=_0xf36db1(0x243),_0x4a5076['textContent']=_0xf36db1(0x26c),_0x6faabf[_0xf36db1(0x1ed)](_0x4a5076),_0x24a72e['forEach'](function(_0x2fe1a5){const _0x5a3b8b=_0xf36db1;let _0x2b368a=document[_0x5a3b8b(0x28d)](_0x5a3b8b(0x2a7));_0x2b368a[_0x5a3b8b(0x299)]=_0x5a3b8b(0x222),_0x2b368a[_0x5a3b8b(0x1f1)]=_0x2fe1a5,_0x2b368a['addEventListener'](_0x5a3b8b(0x239),function(){const _0x24991b=_0x5a3b8b;let _0x19a194=_0x45e79c[_0x24991b(0x1f1)][_0x24991b(0x298)](),_0x301c84=_0x19a194?_0x19a194+'\x20'+_0x2fe1a5:_0x2fe1a5;_0x45e79c['textContent']=_0x301c84,_0x1a3d17(_0x45e79c),_0x467599();}),_0x6faabf[_0x5a3b8b(0x1ed)](_0x2b368a);});let _0x2015fe=document[_0xf36db1(0x28d)](_0xf36db1(0x1e5));_0x2015fe[_0xf36db1(0x299)]=_0xf36db1(0x251);var _0xd57025=document[_0xf36db1(0x28d)](_0xf36db1(0x1e5));_0xd57025[_0xf36db1(0x299)]=_0xf36db1(0x254),_0xd57025['textContent']=_0x551d67[_0xf36db1(0x1f8)]+_0xf36db1(0x255);let _0x45e79c=document[_0xf36db1(0x28d)](_0xf36db1(0x1e5));_0x45e79c[_0xf36db1(0x299)]=_0xf36db1(0x273),_0x45e79c['contentEditable']=_0xf36db1(0x236),_0x45e79c[_0xf36db1(0x1f1)]=_0x551d67,_0x45e79c[_0xf36db1(0x29f)](_0xf36db1(0x21b),_0xf36db1(0x27c)),_0x45e79c[_0xf36db1(0x29f)](_0xf36db1(0x268),_0xf36db1(0x240));function _0x467599(){const _0x3cff29=_0xf36db1;let _0x54a2c4=_0x45e79c[_0x3cff29(0x1f1)]||'',_0x1041cd=_0x54a2c4[_0x3cff29(0x298)]()[_0x3cff29(0x1f8)];_0xd57025[_0x3cff29(0x1f1)]=_0x1041cd+_0x3cff29(0x255),_0x1041cd>0x50?(_0x45e79c[_0x3cff29(0x235)][_0x3cff29(0x2a8)](_0x3cff29(0x230)),_0xd57025[_0x3cff29(0x235)][_0x3cff29(0x2a8)](_0x3cff29(0x230))):(_0x45e79c[_0x3cff29(0x235)]['remove'](_0x3cff29(0x230)),_0xd57025['classList'][_0x3cff29(0x2b1)](_0x3cff29(0x230))),_0x3e0867[_0x3cff29(0x29f)](_0x3cff29(0x21d),_0x54a2c4[_0x3cff29(0x298)]());}_0x45e79c[_0xf36db1(0x291)](_0xf36db1(0x244),_0x467599),_0x45e79c[_0xf36db1(0x291)](_0xf36db1(0x1e9),_0x467599),_0x45e79c[_0xf36db1(0x291)](_0xf36db1(0x25e),function(_0x1101a0){const _0x5b1ef6=_0xf36db1;_0x1101a0[_0x5b1ef6(0x253)]();let _0x209c3b=(_0x1101a0[_0x5b1ef6(0x294)]||window['clipboardData'])[_0x5b1ef6(0x1fa)](_0x5b1ef6(0x1f6));document['execCommand'](_0x5b1ef6(0x1ea),![],_0x209c3b),_0x467599();});function _0x1a3d17(_0x24d6f0){const _0x52d92b=_0xf36db1;_0x24d6f0['focus']();if(typeof window[_0x52d92b(0x277)]!=_0x52d92b(0x269)&&typeof document[_0x52d92b(0x24a)]!='undefined'){let _0x3326a0=document[_0x52d92b(0x24a)]();_0x3326a0[_0x52d92b(0x229)](_0x24d6f0),_0x3326a0[_0x52d92b(0x293)](![]);let _0x3ac6a5=window[_0x52d92b(0x277)]();_0x3ac6a5['removeAllRanges'](),_0x3ac6a5[_0x52d92b(0x24d)](_0x3326a0);}}var _0x39ed5a=document[_0xf36db1(0x28d)]('div');_0x39ed5a[_0xf36db1(0x299)]=_0xf36db1(0x232);var _0x1a3401=document[_0xf36db1(0x28d)](_0xf36db1(0x1e5));_0x1a3401['className']=_0xf36db1(0x272);var _0x4764a1=document['createElement'](_0xf36db1(0x1e5));_0x4764a1[_0xf36db1(0x299)]='custom-features-middle-container';var _0x1f894c=document[_0xf36db1(0x28d)]('div');_0x1f894c[_0xf36db1(0x299)]=_0xf36db1(0x21e),_0x39ed5a['appendChild'](_0x1a3401),_0x39ed5a[_0xf36db1(0x1ed)](_0x4764a1),_0x39ed5a[_0xf36db1(0x1ed)](_0x1f894c),_0x1f894c['appendChild'](_0xd57025),_0x2015fe[_0xf36db1(0x1ed)](_0x45e79c),_0x2015fe[_0xf36db1(0x1ed)](_0x39ed5a),_0x4416e0['appendChild'](_0x2015fe),_0x4416e0[_0xf36db1(0x1ed)](_0x6faabf);}function addBanner(_0x445859,_0x3a9adb,_0x53d248,_0x177f2f){const _0x63bf11=a0_0x19986d;var _0x4c61f7=document[_0x63bf11(0x26a)](_0x63bf11(0x292));_0x4c61f7&&_0x4c61f7[_0x63bf11(0x2b1)]();var _0x543e2b=document['createElement'](_0x63bf11(0x1e5));_0x543e2b['id']=_0x63bf11(0x292),_0x543e2b[_0x63bf11(0x299)]=_0x63bf11(0x292),document[_0x63bf11(0x249)]['appendChild'](_0x543e2b);var _0x3f42d1=document[_0x63bf11(0x28d)](_0x63bf11(0x2af));_0x3f42d1[_0x63bf11(0x299)]=_0x63bf11(0x224),_0x3f42d1[_0x63bf11(0x258)]=_0x177f2f,_0x543e2b[_0x63bf11(0x1ed)](_0x3f42d1);var _0x140027=document[_0x63bf11(0x28d)](_0x63bf11(0x1e5));_0x140027[_0x63bf11(0x299)]=_0x63bf11(0x265),_0x543e2b[_0x63bf11(0x1ed)](_0x140027);var _0xa5f733=document['createElement']('div');_0xa5f733[_0x63bf11(0x299)]='snipeMode-title-container',_0x140027[_0x63bf11(0x1ed)](_0xa5f733);var _0x314f00=document['createElement']('div');_0x314f00[_0x63bf11(0x299)]='snipeMode-competitor-title',_0x314f00[_0x63bf11(0x2a2)]='true',_0x314f00[_0x63bf11(0x256)]=_0x445859,_0xa5f733[_0x63bf11(0x1ed)](_0x314f00);var _0x44ae55=document[_0x63bf11(0x28d)](_0x63bf11(0x2a7));_0x44ae55[_0x63bf11(0x299)]='snipeMode-refresh-button',_0x44ae55['setAttribute'](_0x63bf11(0x1fd),_0x63bf11(0x231)),_0x44ae55[_0x63bf11(0x256)]='&#x21bb;',_0xa5f733[_0x63bf11(0x1ed)](_0x44ae55),_0x44ae55[_0x63bf11(0x291)](_0x63bf11(0x239),function(){const _0x2f346e=_0x63bf11;var _0x5c4108=_0x314f00[_0x2f346e(0x1f1)][_0x2f346e(0x298)]();_0x543e2b[_0x2f346e(0x29f)](_0x2f346e(0x20f),_0x5c4108),updateAllCustomTitles(_0x5c4108);});var _0x2d03a8=document[_0x63bf11(0x28d)](_0x63bf11(0x1e5));_0x2d03a8['className']=_0x63bf11(0x24f),_0x2d03a8[_0x63bf11(0x256)]=_0x63bf11(0x216)+_0x3a9adb,_0x140027[_0x63bf11(0x1ed)](_0x2d03a8);var _0x41784e=document['createElement'](_0x63bf11(0x2a7));_0x41784e['id']=_0x63bf11(0x290),_0x41784e[_0x63bf11(0x299)]=_0x63bf11(0x290),_0x41784e[_0x63bf11(0x256)]=_0x63bf11(0x219),_0x543e2b[_0x63bf11(0x1ed)](_0x41784e),_0x41784e['addEventListener'](_0x63bf11(0x239),function(){const _0x13f33f=_0x63bf11;_0x543e2b[_0x13f33f(0x2b1)]();}),_0x543e2b[_0x63bf11(0x29f)]('data-snipe-title',_0x445859),_0x543e2b['setAttribute']('data-snipe-price',_0x3a9adb),_0x543e2b[_0x63bf11(0x29f)](_0x63bf11(0x220),_0x53d248);}function updateAllCustomTitles(_0x56d8c9){const _0x28fcd4=a0_0x19986d;var _0x181a12=document[_0x28fcd4(0x228)](_0x28fcd4(0x1ee));_0x181a12[_0x28fcd4(0x23b)](function(_0xbd8b3a){const _0x1d6b1a=_0x28fcd4;_0xbd8b3a[_0x1d6b1a(0x1f1)]=_0x56d8c9;var _0x12721d=_0xbd8b3a[_0x1d6b1a(0x233)](_0x1d6b1a(0x1f5));_0x12721d&&_0x12721d['setAttribute'](_0x1d6b1a(0x21d),_0x56d8c9);var _0x3bfb55=_0xbd8b3a[_0x1d6b1a(0x1e7)][_0x1d6b1a(0x26e)](_0x1d6b1a(0x279));_0x3bfb55&&(_0x3bfb55['textContent']=_0x56d8c9[_0x1d6b1a(0x1f8)]+_0x1d6b1a(0x255));if(_0x56d8c9['length']>0x50){_0xbd8b3a[_0x1d6b1a(0x235)][_0x1d6b1a(0x2a8)](_0x1d6b1a(0x230));if(_0x3bfb55)_0x3bfb55[_0x1d6b1a(0x235)][_0x1d6b1a(0x2a8)]('exceeds-limit');}else{_0xbd8b3a['classList']['remove'](_0x1d6b1a(0x230));if(_0x3bfb55)_0x3bfb55[_0x1d6b1a(0x235)][_0x1d6b1a(0x2b1)](_0x1d6b1a(0x230));}});}function addCustomEditableAdRateToContainer(_0x5f2a4a,_0x14f9a1='2.%'){const _0x44e30b=a0_0x19986d,_0x36e991=document[_0x44e30b(0x28d)]('div');_0x36e991[_0x44e30b(0x299)]=_0x44e30b(0x2a1),_0x36e991[_0x44e30b(0x29f)](_0x44e30b(0x241),_0x44e30b(0x236)),_0x36e991[_0x44e30b(0x29f)](_0x44e30b(0x21b),'Ad\x20Rate\x20(%)'),_0x36e991[_0x44e30b(0x29f)](_0x44e30b(0x268),'false'),_0x36e991[_0x44e30b(0x29f)](_0x44e30b(0x21f),_0x44e30b(0x281)),_0x36e991['setAttribute']('aria-label',_0x44e30b(0x201)),_0x36e991[_0x44e30b(0x29f)]('tabindex','0'),_0x36e991[_0x44e30b(0x1f1)]=_0x14f9a1,_0x36e991[_0x44e30b(0x29f)](_0x44e30b(0x246),_0x14f9a1),_0x36e991[_0x44e30b(0x291)](_0x44e30b(0x237),function(){const _0x24f01a=_0x44e30b;this[_0x24f01a(0x1f1)][_0x24f01a(0x298)]()===_0x24f01a(0x1ef)&&(this[_0x24f01a(0x1f1)]='');}),_0x36e991[_0x44e30b(0x291)](_0x44e30b(0x21a),function(){const _0x4217e9=_0x44e30b;this[_0x4217e9(0x1f1)]['trim']()===''?this[_0x4217e9(0x1f1)]=_0x4217e9(0x1ef):_0x59aa7c(this);});function _0x59aa7c(_0x3cd00e){const _0x306a15=_0x44e30b;let _0x3c5a10=_0x3cd00e['textContent'][_0x306a15(0x298)]();const _0x5bfff4=/^\d+(\.\d+)?%$/;!_0x5bfff4[_0x306a15(0x25c)](_0x3c5a10)?(_0x3cd00e[_0x306a15(0x235)][_0x306a15(0x2a8)](_0x306a15(0x203)),setTimeout(()=>{const _0x37b0d9=_0x306a15;_0x3cd00e[_0x37b0d9(0x1f1)]=_0x3cd00e['getAttribute'](_0x37b0d9(0x246))||_0x37b0d9(0x1ef),_0x3cd00e[_0x37b0d9(0x235)]['remove']('invalid');},0x5dc)):(_0x3cd00e['setAttribute'](_0x306a15(0x246),_0x3c5a10),_0x3cd00e[_0x306a15(0x235)][_0x306a15(0x2b1)](_0x306a15(0x203)),_0x30823c(_0x3cd00e));}function _0x30823c(_0x1d07dd){const _0x11cea3=_0x44e30b,_0x5cb985=document[_0x11cea3(0x28d)]('div');_0x5cb985[_0x11cea3(0x299)]=_0x11cea3(0x280),_0x5cb985[_0x11cea3(0x1f1)]='Ad\x20Rate\x20Updated!',_0x1d07dd[_0x11cea3(0x1ed)](_0x5cb985),setTimeout(()=>{const _0x48ec17=_0x11cea3;_0x5cb985[_0x48ec17(0x235)]['add'](_0x48ec17(0x259)),_0x5cb985[_0x48ec17(0x291)](_0x48ec17(0x22b),()=>{const _0x23e1c6=_0x48ec17;_0x5cb985[_0x23e1c6(0x29e)]&&_0x5cb985['parentNode'][_0x23e1c6(0x223)](_0x5cb985);});},0x3e8);}var _0x1c8880=_0x5f2a4a['querySelector'](_0x44e30b(0x2a0));if(!_0x1c8880){console[_0x44e30b(0x29d)](_0x44e30b(0x21c),_0x5f2a4a);return;}console[_0x44e30b(0x260)](_0x44e30b(0x25f),_0x1c8880),_0x1c8880[_0x44e30b(0x1ed)](_0x36e991);}