.icon-button.with-number {
    padding-top: 10px; /* Adjust as needed for number space */
    padding-right: 10px; /* Adjust as needed for number space */
}

/* Keep other .icon-button styles unchanged */
.icon-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    /* Remove padding-top and padding-right from here if they were previously set */
}

.icon-button:hover {
    transform: scale(2.0); /* Slightly increase size on hover */
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.6); /* Optional: Add a glow effect on hover */
}

.icon-button:active, .icon-button.clicked {
    /* Add rotation and scaling */
    transform: scale(3.0) rotateZ(360deg);
    transition: transform 0.2s ease-out;
    /* Change background or border if needed */
}

.icon-button-img {
    width: 30px;
    height: 30px;
    vertical-align: middle;
    margin-right: 5px;
    transition: transform 0.3s ease; /* Smooth transition for the image as well */
}

/* Magic sparkles or pulse effect */
.icon-button::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.0) 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

.icon-button:active::after, .icon-button.clicked::after {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 1;
    transition: transform 0.5s ease, opacity 0.3s ease;
}

.icon-button-number {
    position: absolute;
    top: 0; /* Adjust if the number is too high or too low */
    right: 0; /* Adjust if the number is too far right or left */
    background-color: #fff; /* Change as needed */
    color: #000; /* Change as needed */
    width: 12px; /* Make the badge larger if necessary */
    height: 12px; /* Make the badge larger if necessary */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px; /* Increase if the number is too small */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); Optional: adds a subtle shadow */
    border: 2px solid #ffffff; /* Adds a white border to make the badge stand out */
    z-index: 2; /* Ensure the number is above all other elements */
}


.icon-button-status {
    position: absolute;
    top: 0; /* Adjust if necessary */
    right: 0; /* Adjust if necessary */
    color: #000; /* This will be the color of your emojis */
    font-size: 12px; /* Adjust based on your design */
    z-index: 9999; /* Ensures it's on top of other elements */
    background-color: transparent; /* Removes background */
    border: none; /* Removes border */
    width: auto; /* Adjust as needed */
    height: auto; /* Adjust as needed */
    align-items: center;
    justify-content: center;
    display: flex;
}


@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotating {
    animation: rotate 2s linear infinite;
}
