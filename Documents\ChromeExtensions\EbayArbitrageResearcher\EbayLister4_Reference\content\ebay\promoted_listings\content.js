var a0_0x417e3d=a0_0x68d0;(function(_0x458e5b,_0x193a28){var _0x6b2c1e=a0_0x68d0,_0x3848be=_0x458e5b();while(!![]){try{var _0xa057b5=parseInt(_0x6b2c1e(0x194))/0x1+parseInt(_0x6b2c1e(0x19f))/0x2*(-parseInt(_0x6b2c1e(0x198))/0x3)+-parseInt(_0x6b2c1e(0x19c))/0x4+parseInt(_0x6b2c1e(0x199))/0x5+-parseInt(_0x6b2c1e(0x190))/0x6+parseInt(_0x6b2c1e(0x19a))/0x7*(-parseInt(_0x6b2c1e(0x19b))/0x8)+-parseInt(_0x6b2c1e(0x19e))/0x9;if(_0xa057b5===_0x193a28)break;else _0x3848be['push'](_0x3848be['shift']());}catch(_0x1c9ec9){_0x3848be['push'](_0x3848be['shift']());}}}(a0_0x297e,0x5bc55),console[a0_0x417e3d(0x197)](a0_0x417e3d(0x19d)));function a0_0x68d0(_0x280da7,_0x4576e1){var _0x297ec3=a0_0x297e();return a0_0x68d0=function(_0x68d073,_0x1fa6a5){_0x68d073=_0x68d073-0x18c;var _0x4b3449=_0x297ec3[_0x68d073];return _0x4b3449;},a0_0x68d0(_0x280da7,_0x4576e1);}var itemSummarySelector=a0_0x417e3d(0x18c);async function onElementAdded(){var _0x249761=a0_0x417e3d;console[_0x249761(0x197)](_0x249761(0x1a0)),setInterval(addNextButton,0x3e8);}async function addNextButton(){var _0x333ac7=a0_0x417e3d,_0x20f6a1=document['querySelector'](_0x333ac7(0x18d));if(!_0x20f6a1){var _0x30e44=document['querySelector'](itemSummarySelector),_0x20f6a1=await createNextButton();_0x30e44['parentElement'][_0x333ac7(0x195)][_0x333ac7(0x18e)](_0x20f6a1);var _0x32cd03=document[_0x333ac7(0x1a4)](_0x333ac7(0x191));_0x32cd03['click']();}counter>0x0&&counter<0xc&&(console[_0x333ac7(0x197)](_0x333ac7(0x192),counter),_0x20f6a1['click']());}function a0_0x297e(){var _0x505a28=['Element\x20found','body','type','querySelector','.item-count-summary','.next-button','appendChild','observe','49416Clxvlh','input[aria-label=\x27Select\x20all\x20listings\x20for\x20performing\x20bulk\x20action\x27]','next\x20page\x20counter\x20','childList','537619YYfUND','parentElement','disconnect','log','3rXfUms','2844035vquprS','91SwAcgV','83368rrYNws','3464TdsaIf','ebay/promoted_listings/content.js','850761uvHwEE','982858EbGsKD','Element\x20added!'];a0_0x297e=function(){return _0x505a28;};return a0_0x297e();}const observer=new MutationObserver((_0x161fd5,_0x28a2d3)=>{var _0x8fe455=a0_0x417e3d;for(let _0x46794e of _0x161fd5){if(_0x46794e[_0x8fe455(0x1a3)]===_0x8fe455(0x193)){const _0x59079d=document['querySelector'](itemSummarySelector);if(_0x59079d){console[_0x8fe455(0x197)](_0x8fe455(0x1a1)),onElementAdded(),_0x28a2d3[_0x8fe455(0x196)]();break;}}}});observer[a0_0x417e3d(0x18f)](document[a0_0x417e3d(0x1a2)],{'childList':!![],'subtree':!![]});