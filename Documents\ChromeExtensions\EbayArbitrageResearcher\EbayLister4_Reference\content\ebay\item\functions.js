var a0_0x7d7124=a0_0x5441;function a0_0x5441(_0x2531fe,_0x3d4626){var _0x17f06d=a0_0x17f0();return a0_0x5441=function(_0x54418b,_0x2ceb34){_0x54418b=_0x54418b-0x1d2;var _0x43f4ef=_0x17f06d[_0x54418b];return _0x43f4ef;},a0_0x5441(_0x2531fe,_0x3d4626);}(function(_0x517be5,_0x17aed7){var _0x2446a2=a0_0x5441,_0xf48a2b=_0x517be5();while(!![]){try{var _0x1bbeef=parseInt(_0x2446a2(0x234))/0x1+parseInt(_0x2446a2(0x246))/0x2+-parseInt(_0x2446a2(0x1e7))/0x3+parseInt(_0x2446a2(0x1ed))/0x4*(parseInt(_0x2446a2(0x210))/0x5)+parseInt(_0x2446a2(0x1fc))/0x6+-parseInt(_0x2446a2(0x25c))/0x7*(parseInt(_0x2446a2(0x24e))/0x8)+-parseInt(_0x2446a2(0x259))/0x9;if(_0x1bbeef===_0x17aed7)break;else _0xf48a2b['push'](_0xf48a2b['shift']());}catch(_0x2015a9){_0xf48a2b['push'](_0xf48a2b['shift']());}}}(a0_0x17f0,0x98bf4),console[a0_0x7d7124(0x206)](a0_0x7d7124(0x23c)));var itemSpecificLabelsToRemove=['Condition'],backgroundResponse;function getSellerName(){var _0x5ed341=a0_0x7d7124,_0x108b76=getSellerUsernameFromLink();if(_0x108b76)return _0x108b76;var _0x36e2bf=document[_0x5ed341(0x1ef)]('.ux-seller-section__item--seller\x20span'),_0x108b76;if(_0x36e2bf){var _0x108b76=_0x36e2bf[_0x5ed341(0x1f0)][_0x5ed341(0x23d)]();return _0x108b76;}var _0x36e2bf=document[_0x5ed341(0x1ef)](_0x5ed341(0x232));if(_0x36e2bf){var _0x1fbfc1=_0x36e2bf[_0x5ed341(0x1db)],_0x2562a6=_0x1fbfc1['match'](/ssn=([^&]+)/);if(_0x2562a6&&_0x2562a6[0x1])return console[_0x5ed341(0x206)]('Seller\x20Name:',_0x2562a6[0x1]),_0x108b76=_0x2562a6[0x1],_0x108b76;var _0x36e2bf=document[_0x5ed341(0x1ef)](_0x5ed341(0x24d));if(_0x36e2bf){var _0x51d71c=_0x36e2bf[_0x5ed341(0x227)](_0x5ed341(0x22c));if(_0x51d71c)return console['log']('Seller\x20Name:',_0x51d71c),_0x108b76=_0x51d71c,_0x108b76;}}try{var _0x36e2bf=document['querySelectorAll']('[data-testid=\x27x-sellercard-atf__data-item\x27]')[0x2][_0x5ed341(0x1ef)]('a'),_0x1fbfc1=_0x36e2bf[_0x5ed341(0x1db)],_0x2562a6=_0x1fbfc1[_0x5ed341(0x20c)](/requested=([^&]+)/);if(_0x2562a6&&_0x2562a6[0x1])return console[_0x5ed341(0x206)](_0x5ed341(0x1e0),_0x2562a6[0x1]),_0x108b76=_0x2562a6[0x1],_0x108b76;}catch(_0x500583){console[_0x5ed341(0x1fa)](_0x5ed341(0x25b));}var _0x36e2bf=document[_0x5ed341(0x1ef)](_0x5ed341(0x1d4))[_0x5ed341(0x22b)]('li')[0x1][_0x5ed341(0x1ef)]('a');if(_0x36e2bf){var _0x1fbfc1=_0x36e2bf[_0x5ed341(0x1db)],_0x2562a6=_0x1fbfc1['match'](/ssn=([^&]+)/);if(_0x2562a6&&_0x2562a6[0x1])return console['log'](_0x5ed341(0x1e0),_0x2562a6[0x1]),_0x108b76=_0x2562a6[0x1],_0x108b76;}return _0x108b76;}function getSellerUsernameFromLink(){var _0x38e9b6=a0_0x7d7124,_0x36481d=document['querySelectorAll'](_0x38e9b6(0x22f)),_0x290d12=Array[_0x38e9b6(0x220)](_0x36481d)['find'](_0x5212b0=>_0x5212b0[_0x38e9b6(0x1f0)]['includes'](_0x38e9b6(0x1d3)));if(_0x290d12){var _0x5d9929=_0x290d12[_0x38e9b6(0x1f2)];if(_0x5d9929){var _0x7cc4f3=_0x5d9929[_0x38e9b6(0x1db)],_0x324c0e=new URL(_0x7cc4f3),_0x4aba2a=_0x324c0e[_0x38e9b6(0x255)][_0x38e9b6(0x257)](_0x38e9b6(0x252));if(_0x4aba2a)return _0x4aba2a;return null;}else return null;}else return null;}function createButtonToSearchOnZikanalytics(){var _0x2a7035=a0_0x7d7124;let _0x1ce24f=document[_0x2a7035(0x20d)](_0x2a7035(0x1d6));return _0x1ce24f[_0x2a7035(0x1e6)]='generate-search-button',_0x1ce24f['textContent']=_0x2a7035(0x241),_0x1ce24f[_0x2a7035(0x22e)]('click',searchZikanalytics),_0x1ce24f;}async function searchZikanalytics(){var _0x550698=a0_0x7d7124,_0xe1b508=getSellerName();await navigator[_0x550698(0x222)][_0x550698(0x201)](_0xe1b508);var _0x566a93=_0x550698(0x211);window[_0x550698(0x20a)][_0x550698(0x1db)]=_0x566a93;}function getTitle(){var _0x1d3aae=a0_0x7d7124,_0x2d8211=document[_0x1d3aae(0x1ef)](_0x1d3aae(0x215)),_0x1312d7=_0x2d8211[_0x1d3aae(0x1f0)][_0x1d3aae(0x23d)]();return _0x1312d7;}function getPrice(){var _0x281005=a0_0x7d7124,_0xb8c346=document[_0x281005(0x1ef)](_0x281005(0x229)),_0x514a9f=_0xb8c346[_0x281005(0x1f0)][_0x281005(0x23d)]();return _0x514a9f=globalizedParsePrice(_0x514a9f),_0x514a9f;}function getImages(){var _0x3f9dad=a0_0x7d7124,_0x382984=document[_0x3f9dad(0x1ef)](_0x3f9dad(0x235)),_0x407721=_0x382984['querySelectorAll'](_0x3f9dad(0x217)),_0x34eb7d=[];return _0x407721[_0x3f9dad(0x228)](_0x493497=>{var _0x38dfd9=_0x3f9dad,_0x4d3f08=_0x493497[_0x38dfd9(0x1e1)];_0x4d3f08==''&&(_0x4d3f08=_0x493497[_0x38dfd9(0x225)][_0x38dfd9(0x1e1)]),_0x34eb7d[_0x38dfd9(0x209)](_0x4d3f08);}),_0x34eb7d;}function getItemSpecifics(){var _0x19c060=a0_0x7d7124;const _0x2a19b7=[];try{const _0x15d53c=document[_0x19c060(0x22b)](_0x19c060(0x248));if(!_0x15d53c||_0x15d53c[_0x19c060(0x20e)]===0x0){console[_0x19c060(0x1fa)]('Item\x20specifics\x20containers\x20not\x20found');return;}_0x15d53c[_0x19c060(0x228)]((_0x5e920c,_0x63641)=>{var _0x3753e8=_0x19c060;const _0x344147=_0x5e920c['querySelector'](_0x3753e8(0x1d5)),_0x4109bd=_0x5e920c['querySelector'](_0x3753e8(0x20b));if(!_0x344147||!_0x4109bd){console[_0x3753e8(0x250)]('Label\x20or\x20value\x20element\x20missing\x20in\x20container\x20'+(_0x63641+0x1)+'.\x20Skipping.');return;}var _0x11e49e=_0x344147[_0x3753e8(0x1f0)]['trim'](),_0x9772c1=_0x4109bd[_0x3753e8(0x1f0)][_0x3753e8(0x23d)]();_0x9772c1[_0x3753e8(0x1ec)](',')&&(_0x9772c1=_0x9772c1[_0x3753e8(0x1d8)](',')[_0x3753e8(0x237)](_0x3c11f5=>_0x3c11f5[_0x3753e8(0x23d)]())),_0x2a19b7[_0x3753e8(0x209)]({'label':_0x11e49e,'value':_0x9772c1});});}catch(_0x1fb35c){console[_0x19c060(0x1fa)]('An\x20error\x20occurred\x20while\x20retrieving\x20item\x20specifics:',_0x1fb35c);}return _0x2a19b7;}function filterItemSpecifics(_0x30297a,_0x24ab8f){var _0xbc14a1=a0_0x7d7124,_0x359030=[];return _0x30297a[_0xbc14a1(0x228)](_0x1d0ebe=>{var _0x1b57b=_0xbc14a1;!_0x24ab8f[_0x1b57b(0x1ec)](_0x1d0ebe[_0x1b57b(0x224)])&&_0x359030[_0x1b57b(0x209)](_0x1d0ebe);}),_0x359030;}console[a0_0x7d7124(0x206)](a0_0x7d7124(0x244));function waitForMessage(){return new Promise(_0x273129=>{var _0x2bcd45=a0_0x5441;window[_0x2bcd45(0x22e)](_0x2bcd45(0x1f3),_0x41762c=>{var _0x31d615=_0x2bcd45;_0x41762c[_0x31d615(0x1e4)]&&_0x41762c['data'][_0x31d615(0x233)]===_0x31d615(0x21f)&&_0x273129(_0x41762c['data'][_0x31d615(0x1fd)]);});});}async function getDescriptionFromIframe(){var _0x197875=a0_0x7d7124;const _0x4ecc14=document[_0x197875(0x1ef)](_0x197875(0x208));if(_0x4ecc14){_0x4ecc14['contentWindow'][_0x197875(0x1e5)]({'action':_0x197875(0x1d9)},'*');const _0x3cb2e7=await waitForMessage();return _0x3cb2e7;}else return console[_0x197875(0x1fa)]('Iframe\x20not\x20found'),null;}function getCategory(){var _0x590e69=a0_0x7d7124,_0x5db5a3=document[_0x590e69(0x1ef)](_0x590e69(0x238)),_0x271530=_0x5db5a3[_0x590e69(0x22b)]('a'),_0x422677=_0x271530[_0x271530[_0x590e69(0x20e)]-0x1]['innerText'][_0x590e69(0x23d)]();return _0x422677;}function getCategoryID(){var _0x22583e=a0_0x7d7124,_0x26f30c=document['querySelector'](_0x22583e(0x238));if(!_0x26f30c){console[_0x22583e(0x1fa)]('Breadcrumb\x20wrapper\x20not\x20found');return;}var _0x1b3d7e=_0x26f30c[_0x22583e(0x22b)]('a');if(_0x1b3d7e[_0x22583e(0x20e)]===0x0){console['error'](_0x22583e(0x24a));return;}for(var _0x29b562=_0x1b3d7e[_0x22583e(0x20e)]-0x1;_0x29b562>=0x0;_0x29b562--){var _0x474eb5=_0x1b3d7e[_0x29b562][_0x22583e(0x1db)][_0x22583e(0x23d)](),_0x99d01b=_0x474eb5[_0x22583e(0x20c)](/\/b\/[^/]+\/(\d+)/);if(_0x99d01b&&_0x99d01b[0x1])return console[_0x22583e(0x206)](_0x22583e(0x24f),_0x99d01b[0x1]),_0x99d01b[0x1];}console[_0x22583e(0x1fa)]('Category\x20ID\x20not\x20found');}function createButtonListToEbay(){var _0x2b63e6=a0_0x7d7124,_0xeab73f=document['createElement'](_0x2b63e6(0x1d2)),_0x20d769=document[_0x2b63e6(0x20d)]('a');_0x20d769[_0x2b63e6(0x1e6)]=_0x2b63e6(0x226),_0x20d769[_0x2b63e6(0x1db)]='#',_0x20d769['classList'][_0x2b63e6(0x1f5)](_0x2b63e6(0x1e2));var _0x1630dc=document[_0x2b63e6(0x20d)](_0x2b63e6(0x22f));return _0x1630dc[_0x2b63e6(0x1e6)]='ux-call-to-action__cell',_0x1630dc[_0x2b63e6(0x242)]=_0x2b63e6(0x200),_0x20d769['appendChild'](_0x1630dc),_0xeab73f[_0x2b63e6(0x249)](_0x20d769),_0xeab73f[_0x2b63e6(0x1e6)]=_0x2b63e6(0x258),_0x20d769[_0x2b63e6(0x22e)](_0x2b63e6(0x21d),async function(_0x2a28c4){var _0x24d02a=_0x2b63e6;_0x2a28c4['preventDefault']();if(_0x20d769[_0x24d02a(0x23e)][_0x24d02a(0x1f6)](_0x24d02a(0x213)))return;console[_0x24d02a(0x206)](_0x24d02a(0x1d7)),_0x20d769[_0x24d02a(0x23e)]['add'](_0x24d02a(0x213)),_0x1630dc[_0x24d02a(0x1ef)](_0x24d02a(0x1fb))[_0x24d02a(0x1f0)]='Listing...';var _0x13b7b3=await getEbayItemData();console[_0x24d02a(0x206)](_0x24d02a(0x1eb),_0x13b7b3);var _0x52a7b0=await checkMembership();console[_0x24d02a(0x206)]('membership',_0x52a7b0);if(_0x52a7b0!=_0x24d02a(0x1ff)){var _0x5c4759=createDisplayElement({'type':'error','message':_0x24d02a(0x204)});_0xeab73f[_0x24d02a(0x249)](_0x5c4759),_0x1630dc[_0x24d02a(0x1ef)](_0x24d02a(0x1fb))[_0x24d02a(0x1f0)]=_0x24d02a(0x21c);return;}var _0x28f373=await checkIfCreditsAreAvailable();if(_0x28f373==![]){alert(_0x24d02a(0x243));return;}chrome['runtime'][_0x24d02a(0x218)]({'type':_0x24d02a(0x256),'amount':0x1});var _0x50de01=await new Promise(_0x427b46=>{var _0x3f004d=_0x24d02a;chrome['runtime']['sendMessage']({'type':_0x3f004d(0x254),'ebayData':_0x13b7b3},function(_0x9248c1){var _0x39d977=_0x3f004d;_0x427b46(_0x9248c1[_0x39d977(0x239)]);});});backgroundResponse=_0x50de01;var _0x5c4759=createDisplayElement(_0x50de01['message']);_0xeab73f[_0x24d02a(0x249)](_0x5c4759),_0x1630dc[_0x24d02a(0x1ef)](_0x24d02a(0x1fb))[_0x24d02a(0x1f0)]=_0x50de01[_0x24d02a(0x1f3)][_0x24d02a(0x216)]==_0x24d02a(0x253)?_0x24d02a(0x203):_0x24d02a(0x21c);}),_0xeab73f;}function createDisplayElement(_0x1aa984){var _0x1f5a7c=a0_0x7d7124,_0x1b3cd3=document[_0x1f5a7c(0x20d)](_0x1f5a7c(0x1d2));_0x1b3cd3[_0x1f5a7c(0x1e6)]=_0x1f5a7c(0x205);var _0x57035d=_0x1aa984[_0x1f5a7c(0x216)]==_0x1f5a7c(0x253)?_0x1f5a7c(0x23a):_0x1f5a7c(0x1fa),_0xc4a70d=_0x1aa984[_0x1f5a7c(0x230)]?'<a\x20href=\x22'+_0x1aa984[_0x1f5a7c(0x230)]+_0x1f5a7c(0x1fe):_0x1f5a7c(0x1ee);return _0x1b3cd3['innerHTML']=_0x1f5a7c(0x219)+_0x57035d+_0x1f5a7c(0x21e)+_0x1aa984['message']+_0x1f5a7c(0x1f4)+_0xc4a70d+'</p>',_0x1b3cd3;}async function handleResponse(_0x15767d,_0xa1228b,_0x1f7144,_0x5714ad){var _0x338101=a0_0x7d7124;console['log'](_0x338101(0x239),_0x15767d);var _0x40d9d2=_0x15767d['message']['message'],_0x1089b6=_0x15767d[_0x338101(0x1f3)][_0x338101(0x216)];_0x1f7144['querySelector'](_0x338101(0x1fb))[_0x338101(0x1f0)]=_0x1089b6=='itemListed'?_0x338101(0x203):_0x338101(0x21c);var _0x3ddb4a=document['createElement'](_0x338101(0x1d2));_0x3ddb4a[_0x338101(0x1e6)]=_0x338101(0x205);var _0x3babe7=_0x1089b6=='itemListed'?_0x338101(0x23a):_0x338101(0x1fa),_0x133674=_0x15767d['message'][_0x338101(0x230)]?_0x338101(0x1f7)+_0x15767d[_0x338101(0x1f3)]['ebayItemLink']+_0x338101(0x1fe):_0x338101(0x1ee);return _0x3ddb4a[_0x338101(0x242)]=_0x338101(0x1e9)+_0x3babe7+_0x338101(0x1f8)+_0x40d9d2+_0x338101(0x25d)+_0x133674+_0x338101(0x1dc),_0x5714ad[_0x338101(0x249)](_0x3ddb4a),_0x15767d;}async function getEbayItemData(){var _0x4e1c2f=a0_0x7d7124,_0x35b3bb=await getDescriptionFromIframe(),_0x430196=getItemSpecifics(),_0x330637=filterItemSpecifics(_0x430196,itemSpecificLabelsToRemove),_0x81686=getTitle(),_0x1d4cc6=getPrice(),_0x526d93=getCategory(),_0x5c0507=getCategoryID(),_0x2d9e6d=getImages(),_0x2ccccd=getCustomLabel(),{sniper_price_markdown:_0x1ae470}=await chrome[_0x4e1c2f(0x23b)]['local'][_0x4e1c2f(0x257)](['sniper_price_markdown']);_0x1d4cc6=_0x1d4cc6-_0x1ae470;var _0x4f73f2={'title':_0x81686,'price':_0x1d4cc6,'description':_0x35b3bb,'itemSpecifics':_0x330637,'category':_0x526d93,'categoryId':_0x5c0507,'images':_0x2d9e6d,'listingType':_0x4e1c2f(0x1e8),'sku':_0x2ccccd};return _0x4f73f2;}function getItemNumber(){var _0x38a3fb=a0_0x7d7124,_0x515b17=window[_0x38a3fb(0x20a)][_0x38a3fb(0x1db)],_0x285fab=_0x515b17['match'](/itm\/([^?]+)/),_0x3d8ff=_0x285fab[0x1];return _0x3d8ff;}async function getSkuFromEcomSniperDescription(){var _0x3c2629=a0_0x7d7124;console[_0x3c2629(0x206)]('getEcomSniperItemSku');var _0x408b70=null,_0x5e2346=await getDescriptionFromIframe();console[_0x3c2629(0x206)](_0x3c2629(0x1f9),_0x5e2346);if(!_0x5e2346)return null;var _0xa1ec15=new DOMParser(),_0x472edc=_0xa1ec15[_0x3c2629(0x1dd)](_0x5e2346,_0x3c2629(0x240));console[_0x3c2629(0x206)](_0x3c2629(0x25a),_0x472edc);var _0xab5ef3=_0x472edc[_0x3c2629(0x1ef)](_0x3c2629(0x245));return _0xab5ef3&&(_0x408b70=_0xab5ef3[_0x3c2629(0x227)](_0x3c2629(0x247))),_0x408b70;}function getSkuFromLayout(){var _0x58ff79=a0_0x7d7124,_0x4d5b4b=null,_0x50e38c=document[_0x58ff79(0x22b)]('.ux-labels-values__values-content');for(var _0x2d416f of _0x50e38c){var _0x53566a=_0x2d416f[_0x58ff79(0x1f0)]['trim']();if(_0x53566a[_0x58ff79(0x1e3)]('=='))return _0x4d5b4b=_0x53566a,_0x4d5b4b;}var _0x1ea0b3=document[_0x58ff79(0x22b)]('.ux-layout-section__row'),_0x1eda34=null;_0x1ea0b3['forEach'](_0x105433=>{var _0x1dc8db=_0x58ff79;console[_0x1dc8db(0x206)](_0x105433[_0x1dc8db(0x1f0)]),_0x105433['innerText'][_0x1dc8db(0x236)]()[_0x1dc8db(0x1ec)](_0x1dc8db(0x24c))&&(_0x1eda34=_0x105433);});if(_0x1eda34){var _0x5c193c=_0x1eda34[_0x58ff79(0x1ef)]('.ux-labels-values__values-content');_0x4d5b4b=_0x5c193c[_0x58ff79(0x1f0)][_0x58ff79(0x23d)]();}return _0x4d5b4b;}async function getCustomLabel(){var _0x1bfe5e=null;_0x1bfe5e=getSkuFromLayout();if(_0x1bfe5e)return _0x1bfe5e;_0x1bfe5e=await getSkuFromEcomSniperDescription();if(_0x1bfe5e)return _0x1bfe5e;return null;}async function checkIfItemIsRestricted(){var _0x27c9b4=a0_0x7d7124,_0x3efe5b=getTitle(),_0x7107ce=await getDescriptionFromIframe();try{const _0x2404f4=new DOMParser(),_0x472069=_0x2404f4['parseFromString'](_0x7107ce,_0x27c9b4(0x240));_0x472069['querySelectorAll'](_0x27c9b4(0x1de))[_0x27c9b4(0x228)](_0x43d29a=>_0x43d29a[_0x27c9b4(0x1df)]()),_0x472069['querySelectorAll'](_0x27c9b4(0x22d))[_0x27c9b4(0x228)](_0x251a28=>{var _0x4e36a5=_0x27c9b4;const _0x2ea594=Array[_0x4e36a5(0x220)](_0x251a28[_0x4e36a5(0x22b)]('tr'))[_0x4e36a5(0x237)](_0x56d8d9=>{var _0x3fda86=_0x4e36a5;const _0x393e54=Array[_0x3fda86(0x220)](_0x56d8d9[_0x3fda86(0x22b)](_0x3fda86(0x24b)))[_0x3fda86(0x237)](_0x42ace9=>_0x42ace9[_0x3fda86(0x1f0)]['trim']());return _0x393e54['join']('\x20');}),_0x6ebc1c=_0x2ea594[_0x4e36a5(0x223)]('\x0a'),_0x585726=_0x472069[_0x4e36a5(0x20d)](_0x4e36a5(0x1d2));_0x585726[_0x4e36a5(0x221)]=_0x6ebc1c,_0x251a28['parentNode'][_0x4e36a5(0x1ea)](_0x585726,_0x251a28);}),_0x7107ce=_0x472069[_0x27c9b4(0x22a)]['innerText'];}catch(_0x464086){console['error'](_0x27c9b4(0x21a),_0x464086);}_0x7107ce=_0x7107ce['replace'](/its original condition/g,''),_0x7107ce=_0x7107ce[_0x27c9b4(0x202)](/feel free to reach out/g,''),_0x7107ce=_0x7107ce[_0x27c9b4(0x202)](/Herstellergarantie: Not Set/g,''),_0x7107ce=_0x7107ce[_0x27c9b4(0x202)](/Garantierte Software-Updates bis: unbekannt/g,''),_0x7107ce=_0x7107ce['replace'](/Zertifizierung: Not Set/g,''),_0x7107ce=_0x7107ce['replace'](/Garantierte Software-Updates bis: Not Set/g,'');var _0x28d2db=getItemSpecifics();_0x28d2db=_0x28d2db[_0x27c9b4(0x214)](_0xccee0e=>_0xccee0e[_0x27c9b4(0x224)]!=_0x27c9b4(0x231)),console[_0x27c9b4(0x206)](_0x27c9b4(0x22c),_0x3efe5b),console['log'](_0x27c9b4(0x1f9),_0x7107ce),console[_0x27c9b4(0x206)](_0x27c9b4(0x212),_0x28d2db);var _0x949f5b=await detectRestrictedWords(_0x3efe5b);console[_0x27c9b4(0x206)](_0x27c9b4(0x20f),_0x949f5b);var _0x378177=await detectRestrictedWords(_0x7107ce);console[_0x27c9b4(0x206)](_0x27c9b4(0x1f1),_0x378177);var _0x564f83=![];for(var _0x29a058=0x0;_0x29a058<_0x28d2db[_0x27c9b4(0x20e)];_0x29a058++){var _0x51995a=_0x28d2db[_0x29a058];if(Array[_0x27c9b4(0x21b)](_0x51995a[_0x27c9b4(0x207)]))for(var _0x212e72=0x0;_0x212e72<_0x51995a[_0x27c9b4(0x207)][_0x27c9b4(0x20e)];_0x212e72++){var _0x1fde55=_0x51995a['value'][_0x212e72],_0x1fdb03=await detectRestrictedWords(_0x1fde55);if(_0x1fdb03){_0x564f83=!![];break;}}}return console['log'](_0x27c9b4(0x251),_0x564f83),_0x949f5b||_0x378177||_0x564f83;}function a0_0x17f0(){var _0x4f0131=['src','custom-button','endsWith','data','postMessage','className','749991IGGUiZ','EbayRelist','<p>Status:\x20','replaceChild','itemData','includes','16LLDlxO','Not\x20Available','querySelector','innerText','doesDescriptionContainRestrictedWords','parentElement','message','</p>\x0a\x20\x20\x20\x20<p>','add','contains','<a\x20href=\x22','</p><p>Message:\x20','description','error','.ux-call-to-action__text','488022xSRMpb','descriptionHTML','\x22\x20target=\x22_blank\x22>View\x20Item</a>','ultimate','<span\x20class=\x22ux-call-to-action__text\x22>List\x20To\x20Ebay</span>','writeText','replace','Listed!','You\x20are\x20not\x20a\x20member\x20of\x20the\x20sniper\x20list.\x20Please\x20contact\x20support.','info-box','log','value','#desc_ifr','push','location','.ux-labels-values__values-content','match','createElement','length','doesTitleContainRestrictedWords','1137550TAfVqK','https://www.zikanalytics.com/SearchCompetitor/Index','itemSpecifics','disabled','filter','#mainContent\x20[class*=\x27item-title\x27]\x20span','type','img','sendMessage','\x0a\x20\x20\x20\x20<p>Status:\x20','Error\x20parsing\x20description','isArray','Failed!','click','</p>\x0a\x20\x20\x20\x20<p>Message:\x20','sendDescription','from','textContent','clipboard','join','label','dataset','vim\x20fake-btn\x20fake-btn--primary\x20ux-call-to-action','getAttribute','forEach','#mainContent\x20[class*=\x27price-primary\x27]\x20span','body','querySelectorAll','title','table','addEventListener','span','ebayItemLink','Condition','.vim.x-sellercard-atf\x20a','action','509540dMfMkh','.ux-image-carousel-container\x20.ux-image-carousel','toLowerCase','map','.x-breadcrumb__wrapper','response','success','storage','functions.js\x20loaded','trim','classList','Search\x20SKU\x20on\x20Ecom\x20Sniper','text/html','Search\x20Zikanalytics','innerHTML','You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20more\x20credits.','Main\x20content\x20script\x20is\x20running','.footerss','893586NgJZqd','data-label','.ux-layout-section-evo__col','appendChild','Breadcrumb\x20elements\x20not\x20found','td,\x20th','custom\x20label','.x-sellercard-atf__info__about-seller','106664cahuxw','Category\x20ID:','warn','doesItemSpecificsContainRestrictedWords','_ssn','itemListed','listEbayItem','searchParams','deductCredits','get','button-list\x20vim','5688648rNRGwF','descriptionElement','Seller\x20Name\x20not\x20found','231sAdrHX','</p><p>','div','Seller\x27s\x20other\x20items','.x-sellercard-atf__data-item-wrapper','.ux-labels-values__labels-content','button','buttonList\x20clicked','split','getDescription','generate-search-button','href','</p>','parseFromString','style,\x20script','remove','Seller\x20Name:'];a0_0x17f0=function(){return _0x4f0131;};return a0_0x17f0();}function createSearchSkuButton(_0x154c16){var _0x2084fa=a0_0x7d7124,_0x5b6375=document[_0x2084fa(0x20d)](_0x2084fa(0x1d6));return _0x5b6375[_0x2084fa(0x1e6)]=_0x2084fa(0x1da),_0x5b6375[_0x2084fa(0x221)]=_0x2084fa(0x23f),_0x5b6375['addEventListener'](_0x2084fa(0x21d),function(){var _0x133248=_0x2084fa;console[_0x133248(0x206)]('Search\x20SKU\x20on\x20Ecom\x20Sniper\x20clicked');}),_0x5b6375;}