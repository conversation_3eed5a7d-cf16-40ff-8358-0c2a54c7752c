var a0_0xcef963=a0_0x2c80;(function(_0x5c373a,_0xdcbdd2){var _0x291f57=a0_0x2c80,_0x57f6fe=_0x5c373a();while(!![]){try{var _0x10b6fa=parseInt(_0x291f57(0xf8))/0x1*(-parseInt(_0x291f57(0xe7))/0x2)+parseInt(_0x291f57(0xeb))/0x3+parseInt(_0x291f57(0x121))/0x4*(-parseInt(_0x291f57(0x125))/0x5)+-parseInt(_0x291f57(0x12d))/0x6*(parseInt(_0x291f57(0x12b))/0x7)+parseInt(_0x291f57(0x129))/0x8*(parseInt(_0x291f57(0xe8))/0x9)+-parseInt(_0x291f57(0x122))/0xa+parseInt(_0x291f57(0x124))/0xb;if(_0x10b6fa===_0xdcbdd2)break;else _0x57f6fe['push'](_0x57f6fe['shift']());}catch(_0x2c834d){_0x57f6fe['push'](_0x57f6fe['shift']());}}}(a0_0x38d7,0x91b3a),console[a0_0xcef963(0x112)](a0_0xcef963(0x11c)));async function clickVerifyYourAHumanButton(){var _0x13b482=a0_0xcef963,_0x99816b=document[_0x13b482(0x11a)](_0x13b482(0xf4));console[_0x13b482(0x112)]('verifyButton',_0x99816b);while(!_0x99816b){await new Promise(_0x22d2eb=>setTimeout(_0x22d2eb,0x3e8)),_0x99816b=document[_0x13b482(0x11a)](_0x13b482(0xf4));}return _0x99816b[_0x13b482(0x10c)](),!![];}async function waitForChallengeExample(_0x4bebbc=0x0){var _0x559e3f=a0_0xcef963,_0x26dc6d=document['querySelector']('.challenge-example');console['log']('captchaChallenge',_0x26dc6d);if(_0x26dc6d)return!![];await new Promise(_0xce1de9=>setTimeout(_0xce1de9,0x3e8));if(_0x4bebbc%0xa===0x0)try{clickVerifyYourAHumanButton();}catch(_0x2a8e11){console['log'](_0x559e3f(0xe3),_0x2a8e11);}return waitForChallengeExample(_0x4bebbc+0x1);}function getCaptchaChallengeImage(){var _0x32924c=a0_0xcef963,_0x183b66=document[_0x32924c(0x11a)](_0x32924c(0x12c));console[_0x32924c(0x112)](_0x32924c(0x134),_0x183b66);var _0x2d8067=_0x183b66[_0x32924c(0x10a)](_0x32924c(0xf1))[0x1];console[_0x32924c(0x112)](_0x32924c(0x137),_0x2d8067);var _0x41cf4c=_0x2d8067['getAttribute']('style');console[_0x32924c(0x112)]('style',_0x41cf4c);var _0x77264a=_0x41cf4c['match'](/url\(([^)]+)\)/)[0x1];return console[_0x32924c(0x112)](_0x32924c(0x110),_0x77264a),_0x77264a=_0x77264a['replace'](/['"]+/g,''),_0x77264a=_0x77264a[_0x32924c(0xf9)](/&quot;/g,''),_0x77264a;}function a0_0x2c80(_0x3e9629,_0x4ea649){var _0x38d76a=a0_0x38d7();return a0_0x2c80=function(_0x2c801a,_0x37994c){_0x2c801a=_0x2c801a-0xe3;var _0x4ee65b=_0x38d76a[_0x2c801a];return _0x4ee65b;},a0_0x2c80(_0x3e9629,_0x4ea649);}function getAllPossibleImages(){var _0x2c7241=a0_0xcef963,_0x327d62=document[_0x2c7241(0x11a)](_0x2c7241(0x114)),_0x57278c=[];return _0x327d62['querySelectorAll'](_0x2c7241(0x103))[_0x2c7241(0xff)](_0x591729=>{var _0x5bec51=_0x2c7241,_0x3c9d4d=_0x591729[_0x5bec51(0x11a)](_0x5bec51(0xe6)),_0x3a258d=_0x3c9d4d[_0x5bec51(0x10a)](_0x5bec51(0xf1))[0x0],_0x4d3a56=_0x3a258d[_0x5bec51(0x107)]('style'),_0x1ac14b=_0x4d3a56[_0x5bec51(0x11e)](/url\(([^)]+)\)/)[0x1];_0x1ac14b=_0x1ac14b[_0x5bec51(0xf9)](/['"]+/g,''),_0x57278c[_0x5bec51(0x123)](_0x1ac14b);}),_0x57278c;}function getCheckedImages(){var _0x71c652=a0_0xcef963,_0x43330f=document[_0x71c652(0x11a)](_0x71c652(0x114)),_0x487a80=[];_0x43330f[_0x71c652(0x10a)](_0x71c652(0x103))[_0x71c652(0xff)](_0x337821=>{var _0x1cfc29=_0x71c652;_0x337821[_0x1cfc29(0x107)](_0x1cfc29(0x128))==='true'&&_0x487a80[_0x1cfc29(0x123)](_0x337821);});var _0x3aa808=[];return _0x487a80['forEach'](_0x42f6e0=>{var _0x13cb69=_0x71c652;console['log']('task',_0x42f6e0);var _0x41ba54=_0x42f6e0[_0x13cb69(0x11a)](_0x13cb69(0xe6));console[_0x13cb69(0x112)](_0x13cb69(0x130),_0x41ba54);var _0x592a34=_0x41ba54[_0x13cb69(0x10a)](_0x13cb69(0xf1))[0x0];console['log'](_0x13cb69(0x137),_0x592a34);var _0xa4bb83=_0x592a34[_0x13cb69(0x107)](_0x13cb69(0xfd));console['log'](_0x13cb69(0xfd),_0xa4bb83);var _0x54c951=_0xa4bb83[_0x13cb69(0x11e)](/url\(([^)]+)\)/)[0x1];console[_0x13cb69(0x112)](_0x13cb69(0x110),_0x54c951),_0x54c951=_0x54c951['replace'](/['"]+/g,''),_0x3aa808['push'](_0x54c951);}),_0x3aa808;}function a0_0x38d7(){var _0x1aaef6=['hcaptcha_base64','querySelectorAll','message','click','sendMessage','Captcha\x20solution\x20updated\x20in\x20storage.','storage','url','b64Image','log','apikey','.task-grid','apiKey','get','.button-submit.button','url-to-b64','task','querySelector','textContent','hcaptcha/content.js\x20loaded','absolute','match','https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/getApiKey/1724740250583?email=','.prompt-text','8WTSkbb','6574170XJHIKq','push','28626092FOfHXA','1148990MwpySj','membership','base64_json','aria-pressed','1328qHzGsw','Store\x20Solution','3185798hLQMfW','.challenge-example','6XNJvvP','runtime','email','taskImage','ultimate','data','POST','captchaChallenge','stringify','button','image','local','error','parentNode','addEventListener','.task-image','178hqLPcN','13302oFJSDC','You\x20are\x20not\x20a\x20member\x20of\x20the\x20sniper\x20list.\x20Please\x20contact\x20support.','1000','1483464UpKkwi','substring','application/json','user','includes','refresh_tab','.image','appendChild','skip','#checkbox','grid','map','set','13183egCgsx','replace','position','status','json','style','left','forEach','createElement','https://pro.nocaptchaai.com/solve','zIndex','.task','top','innerText','solution','getAttribute','solved'];a0_0x38d7=function(){return _0x1aaef6;};return a0_0x38d7();}async function storeCaptchaSolution(){var _0x3a7361=a0_0xcef963,_0x35e62a=document[_0x3a7361(0x11a)](_0x3a7361(0x117)),_0x42b504=document[_0x3a7361(0x100)](_0x3a7361(0x136));_0x42b504[_0x3a7361(0x11b)]=_0x3a7361(0x12a),_0x42b504[_0x3a7361(0xfd)][_0x3a7361(0xfa)]=_0x3a7361(0x11d),_0x42b504[_0x3a7361(0xfd)][_0x3a7361(0x104)]='0',_0x42b504['style'][_0x3a7361(0xfe)]='0',_0x42b504['style'][_0x3a7361(0x102)]=_0x3a7361(0xea),_0x35e62a[_0x3a7361(0xe4)][_0x3a7361(0xf2)](_0x42b504),_0x42b504[_0x3a7361(0xe5)](_0x3a7361(0x10c),async _0x214cc6=>{var _0x5c66c9=_0x3a7361;_0x214cc6['preventDefault']();var _0x53a361=getCheckedImages(),_0x3a524a=getCaptchaChallengeImage(),_0x2a350b=await new Promise(_0x36d1c1=>{var _0x277737=a0_0x2c80;chrome[_0x277737(0x12e)][_0x277737(0x10d)]({'type':_0x277737(0x118),'url':_0x3a524a},_0x158a72=>{_0x36d1c1(_0x158a72['b64Image']);});}),_0x59079a=[];for(var _0x1a52bc of _0x53a361){var _0x2fdea4=await new Promise(_0x405bdb=>{var _0x43c347=a0_0x2c80;chrome[_0x43c347(0x12e)][_0x43c347(0x10d)]({'type':_0x43c347(0x118),'url':_0x1a52bc},_0x1d933e=>{var _0x147c9d=_0x43c347;_0x405bdb(_0x1d933e[_0x147c9d(0x111)]);});});_0x59079a[_0x5c66c9(0x123)](_0x2fdea4);}var _0x53df30=await chrome[_0x5c66c9(0x10f)]['local']['get']('solvedCaptchas'),_0x2f6575=_0x53df30['solvedCaptchas']||{},_0x4adb18=_0x2a350b[_0x5c66c9(0xec)](0x0,0xfa0),_0x493963=![];for(let _0x574917 in _0x2f6575){if(_0x574917[_0x5c66c9(0xec)](0x0,0xfa0)===_0x4adb18){_0x493963=_0x574917;break;}}!_0x493963&&(_0x2f6575[_0x2a350b]=[]);const _0x1e1d83=_0x493963?_0x2f6575[_0x493963][_0x5c66c9(0xf6)](_0x376d97=>_0x376d97[_0x5c66c9(0xec)](0x0,0xfa0)):[];_0x59079a['forEach'](_0x117b6d=>{var _0x3efb07=_0x5c66c9,_0x5e019b=_0x117b6d[_0x3efb07(0xec)](0x0,0xfa0);!_0x1e1d83[_0x3efb07(0xef)](_0x5e019b)&&_0x2f6575[_0x493963?_0x493963:_0x2a350b][_0x3efb07(0x123)](_0x117b6d);}),await chrome[_0x5c66c9(0x10f)][_0x5c66c9(0x138)][_0x5c66c9(0xf7)]({'solvedCaptchas':_0x2f6575}),console['log'](_0x5c66c9(0x10e)),chrome['runtime']['sendMessage']({'type':_0x5c66c9(0xf0)});});}async function solveHCaptcha(){var _0x82797f=a0_0xcef963,_0x469d59;try{_0x469d59=await getSolutionFromNoCaptcha();}catch(_0x12bd20){if(_0x12bd20[_0x82797f(0x10b)]==='skip'){}}if(_0x469d59!==null)for(var _0x13e07e of _0x469d59){selectImage(_0x13e07e),await new Promise(_0x212f50=>setTimeout(_0x212f50,0x3e8));}var _0x21e2e4=document['querySelector'](_0x82797f(0x117));_0x21e2e4['click']();}async function selectImage(_0x24d9c8){var _0x3f84a1=a0_0xcef963,_0x2530b9=document['querySelector'](_0x3f84a1(0x114)),_0x11249b=_0x2530b9[_0x3f84a1(0x10a)](_0x3f84a1(0x103))[_0x24d9c8];console[_0x3f84a1(0x112)](_0x3f84a1(0x119),_0x11249b),_0x11249b[_0x3f84a1(0x107)]('aria-pressed')==='false'&&_0x11249b['click']();}function getTargetText(){var _0x20eeb5=a0_0xcef963,_0x516106=document['querySelector'](_0x20eeb5(0x120));return _0x516106[_0x20eeb5(0x105)];}async function getBase64CaptchaImages(){var _0x388fc1=a0_0xcef963,_0x1e602c=getAllPossibleImages(),_0x1bc143=[];for(var _0x24ef5d of _0x1e602c){var _0xb11e5b=await new Promise(_0x3a1dfb=>{var _0x135ecc=a0_0x2c80;chrome[_0x135ecc(0x12e)][_0x135ecc(0x10d)]({'type':'url-to-b64','url':_0x24ef5d},_0x465b9d=>{var _0x2c566a=_0x135ecc;_0x3a1dfb(_0x465b9d[_0x2c566a(0x111)]);});});_0x1bc143[_0x388fc1(0x123)](_0xb11e5b);}return _0x1bc143;}async function getBase64ChallengeImage(){var _0x58da67=getCaptchaChallengeImage(),_0x176e1d=await new Promise(_0x54fb04=>{var _0x4b2cb0=a0_0x2c80;chrome[_0x4b2cb0(0x12e)][_0x4b2cb0(0x10d)]({'type':_0x4b2cb0(0x118),'url':_0x58da67},_0x1f335c=>{var _0x2adf02=_0x4b2cb0;_0x54fb04(_0x1f335c[_0x2adf02(0x111)]);});});return _0x176e1d;}async function getSolutionFromNoCaptcha(){var _0x492b3c=a0_0xcef963,_0xf2bc2d=await getBase64CaptchaImages();_0xf2bc2d=_0xf2bc2d[_0x492b3c(0xf6)](_0x7bdd16=>_0x7bdd16[_0x492b3c(0xec)](0x17));var _0x24050d=await getBase64ChallengeImage();_0x24050d=_0x24050d[_0x492b3c(0xec)](0x17);var _0x568647='Click\x20on\x20the\x20images\x20similar\x20to\x20the\x20sample\x20image\x20(ignore\x20the\x20text).',_0x5e3a8e={'images':{'0':_0xf2bc2d[0x0],'1':_0xf2bc2d[0x1],'2':_0xf2bc2d[0x2],'3':_0xf2bc2d[0x3],'4':_0xf2bc2d[0x4],'5':_0xf2bc2d[0x5],'6':_0xf2bc2d[0x6],'7':_0xf2bc2d[0x7],'8':_0xf2bc2d[0x8]},'target':_0x568647,'type':_0x492b3c(0xf5),'method':_0x492b3c(0x109),'examples':[_0x24050d]},_0x3059d2=await getNoCaptchaApiKey();console[_0x492b3c(0x112)](_0x492b3c(0x113),_0x3059d2);const _0x131206=_0x492b3c(0x101);console['log'](_0x492b3c(0x127),_0x5e3a8e);var _0x28a811=await checkMembership();console['log'](_0x492b3c(0x126),_0x28a811);if(_0x28a811!=_0x492b3c(0x131)){console[_0x492b3c(0x112)](_0x492b3c(0xe9));return;}var _0x1522bc=await fetch(_0x131206,{'method':_0x492b3c(0x133),'headers':{'Content-Type':_0x492b3c(0xed),'apikey':_0x3059d2},'body':JSON[_0x492b3c(0x135)](_0x5e3a8e)}),_0x454e9b=await _0x1522bc['json']();console[_0x492b3c(0x112)]('result\x20from\x20captcha\x20solver',_0x454e9b);if(_0x454e9b[_0x492b3c(0xfb)]===_0x492b3c(0x108))return _0x454e9b[_0x492b3c(0x106)];else{if(_0x454e9b['status']===_0x492b3c(0xf3)){console['log']('error',_0x454e9b['status']);throw new Error(_0x454e9b[_0x492b3c(0xfb)]);}else return null;}}async function getNoCaptchaApiKey(){var _0x2e5d33=a0_0xcef963,{nocaptcha:_0x5f2a50}=await chrome[_0x2e5d33(0x10f)][_0x2e5d33(0x138)][_0x2e5d33(0x116)]('nocaptcha');if(_0x5f2a50&&_0x5f2a50[_0x2e5d33(0x115)])return _0x5f2a50[_0x2e5d33(0x115)];var {user:_0x52e62e}=await chrome[_0x2e5d33(0x10f)][_0x2e5d33(0x138)]['get'](_0x2e5d33(0xee)),_0x1986a2=_0x52e62e[_0x2e5d33(0x12f)],_0x15f5b0=_0x2e5d33(0x11f)+_0x1986a2,_0x4be52d=await checkMembership();console[_0x2e5d33(0x112)]('membership',_0x4be52d);if(_0x4be52d!=_0x2e5d33(0x131)){alert('You\x20are\x20not\x20a\x20member\x20of\x20the\x20sniper\x20list.\x20Please\x20contact\x20support.');return;}var _0x28d3c0=await fetch(_0x15f5b0),_0x1c6b7e=await _0x28d3c0[_0x2e5d33(0xfc)]();console[_0x2e5d33(0x112)](_0x2e5d33(0x132),_0x1c6b7e);var _0x354de3=_0x1c6b7e[_0x2e5d33(0x115)];return chrome['storage'][_0x2e5d33(0x138)]['set']({'nocaptcha':{'apiKey':_0x354de3}}),_0x354de3;}