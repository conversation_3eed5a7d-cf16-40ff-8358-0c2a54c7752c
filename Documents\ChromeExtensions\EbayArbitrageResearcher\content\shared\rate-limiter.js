/**
 * Smart Rate Limiter for API calls and DOM operations
 * Prevents overwhelming eBay/Amazon with requests
 */
class RateLimiter {
  constructor(requestsPerSecond = 2, burstLimit = 5) {
    this.requestsPerSecond = requestsPerSecond;
    this.interval = 1000 / requestsPerSecond;
    this.burstLimit = burstLimit;
    
    this.lastRequest = 0;
    this.requestQueue = [];
    this.burstCount = 0;
    this.burstResetTime = 0;
    
    this.debug = true;
  }

  /**
   * Throttle a single request
   * @returns {Promise} Resolves when it's safe to make the request
   */
  async throttle() {
    const now = Date.now();
    
    // Reset burst count if enough time has passed
    if (now - this.burstResetTime > 60000) { // 1 minute
      this.burstCount = 0;
      this.burstResetTime = now;
    }

    // Check burst limit
    if (this.burstCount >= this.burstLimit) {
      const waitTime = 60000 - (now - this.burstResetTime);
      if (waitTime > 0) {
        if (this.debug) {
          console.log(`⏱️ RateLimiter: Burst limit reached, waiting ${waitTime}ms`);
        }
        await this.delay(waitTime);
        this.burstCount = 0;
        this.burstResetTime = Date.now();
      }
    }

    // Check regular interval
    const timeSinceLastRequest = now - this.lastRequest;
    if (timeSinceLastRequest < this.interval) {
      const waitTime = this.interval - timeSinceLastRequest;
      if (this.debug) {
        console.log(`⏱️ RateLimiter: Throttling for ${waitTime}ms`);
      }
      await this.delay(waitTime);
    }

    this.lastRequest = Date.now();
    this.burstCount++;
    
    if (this.debug) {
      console.log(`⏱️ RateLimiter: Request allowed (burst: ${this.burstCount}/${this.burstLimit})`);
    }
  }

  /**
   * Execute a function with rate limiting
   * @param {function} fn - Function to execute
   * @returns {Promise} Result of the function
   */
  async execute(fn) {
    await this.throttle();
    return await fn();
  }

  /**
   * Queue multiple requests for sequential execution
   * @param {Array<function>} requests - Array of functions to execute
   * @param {function} onProgress - Progress callback (optional)
   * @returns {Promise<Array>} Array of results
   */
  async executeQueue(requests, onProgress = null) {
    const results = [];
    
    for (let i = 0; i < requests.length; i++) {
      try {
        const result = await this.execute(requests[i]);
        results.push({ success: true, data: result, index: i });
        
        if (onProgress) {
          onProgress({
            completed: i + 1,
            total: requests.length,
            progress: ((i + 1) / requests.length) * 100,
            currentResult: result
          });
        }
      } catch (error) {
        console.error(`⏱️ RateLimiter: Request ${i} failed:`, error);
        results.push({ success: false, error: error.message, index: i });
        
        if (onProgress) {
          onProgress({
            completed: i + 1,
            total: requests.length,
            progress: ((i + 1) / requests.length) * 100,
            error: error.message
          });
        }
      }
    }
    
    return results;
  }

  /**
   * Delay execution for specified milliseconds
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update rate limiting parameters
   * @param {number} requestsPerSecond - New requests per second limit
   * @param {number} burstLimit - New burst limit
   */
  updateLimits(requestsPerSecond, burstLimit) {
    this.requestsPerSecond = requestsPerSecond;
    this.interval = 1000 / requestsPerSecond;
    this.burstLimit = burstLimit;
    
    if (this.debug) {
      console.log(`⏱️ RateLimiter: Updated limits - ${requestsPerSecond} req/sec, burst: ${burstLimit}`);
    }
  }

  /**
   * Get current rate limiter status
   * @returns {object} Status information
   */
  getStatus() {
    const now = Date.now();
    return {
      requestsPerSecond: this.requestsPerSecond,
      burstLimit: this.burstLimit,
      burstCount: this.burstCount,
      timeSinceLastRequest: now - this.lastRequest,
      timeUntilBurstReset: Math.max(0, 60000 - (now - this.burstResetTime))
    };
  }

  /**
   * Enable/disable debug logging
   * @param {boolean} enabled - Debug enabled
   */
  setDebug(enabled) {
    this.debug = enabled;
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.RateLimiter = RateLimiter;
window.ArbitrageRateLimiter = window.ArbitrageRateLimiter || new RateLimiter();
window.globalContext.RateLimiter = RateLimiter;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RateLimiter;
}

console.log('✅ ArbitrageRateLimiter loaded and exported to window.ArbitrageRateLimiter');
console.log('🕐 RateLimiter initialization timing:', {
  timestamp: performance.now(),
  readyState: document.readyState,
  dependencies: {
    eventBus: !!window.ArbitrageEventBus,
    errorHandler: !!window.ArbitrageErrorHandler
  }
});
