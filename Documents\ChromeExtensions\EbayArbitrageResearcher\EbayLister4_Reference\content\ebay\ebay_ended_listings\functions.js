var a0_0xb45ad5=a0_0xdd08;(function(_0x32b0b5,_0x110982){var _0x5612b3=a0_0xdd08,_0x594211=_0x32b0b5();while(!![]){try{var _0x5f19dd=parseInt(_0x5612b3(0x84))/0x1*(parseInt(_0x5612b3(0x7e))/0x2)+parseInt(_0x5612b3(0x85))/0x3*(-parseInt(_0x5612b3(0x79))/0x4)+-parseInt(_0x5612b3(0x86))/0x5*(parseInt(_0x5612b3(0x7d))/0x6)+-parseInt(_0x5612b3(0x8a))/0x7+parseInt(_0x5612b3(0x6e))/0x8*(-parseInt(_0x5612b3(0x70))/0x9)+-parseInt(_0x5612b3(0x88))/0xa*(-parseInt(_0x5612b3(0x95))/0xb)+-parseInt(_0x5612b3(0x92))/0xc*(-parseInt(_0x5612b3(0x83))/0xd);if(_0x5f19dd===_0x110982)break;else _0x594211['push'](_0x594211['shift']());}catch(_0x5d3b6d){_0x594211['push'](_0x594211['shift']());}}}(a0_0x52de,0x8d112),console[a0_0xb45ad5(0x8c)](a0_0xb45ad5(0x8b)));async function sellSimilarEndedItems(){var _0x445d8d=a0_0xb45ad5;console[_0x445d8d(0x8c)](_0x445d8d(0x81)),await onPageLoadAndStable();var {lowPerformingItemData:_0x5c09d9}=await chrome[_0x445d8d(0x80)]['local'][_0x445d8d(0x93)](_0x445d8d(0x7b));console['log'](_0x445d8d(0x7b),_0x5c09d9);var _0x3808ad=_0x5c09d9[_0x445d8d(0x78)](_0xbf7cf4=>_0xbf7cf4[_0x445d8d(0x90)]);console[_0x445d8d(0x8c)](_0x445d8d(0x94),_0x3808ad),console[_0x445d8d(0x8c)](_0x445d8d(0x6d),_0x3808ad['length']);var _0x161b92=await getItemNodesByItemNumbers(_0x3808ad);console[_0x445d8d(0x8c)](_0x445d8d(0x74),_0x161b92);if(_0x161b92[_0x445d8d(0x73)]==0x0)return;await selectLowPerformingItems(_0x161b92),await new Promise(_0x4b7d98=>setTimeout(_0x4b7d98,0x7d0)),clickSellSimilarButton(),await clickContinueButton();}async function clickContinueButton(){var _0x5a80ef=a0_0xb45ad5,_0x196f3f=null;while(!_0x196f3f){var _0x45de0f=document[_0x5a80ef(0x6f)](_0x5a80ef(0x7c));_0x196f3f=Array[_0x5a80ef(0x8f)](_0x45de0f)[_0x5a80ef(0x87)](_0x53777f=>_0x53777f[_0x5a80ef(0x7a)][_0x5a80ef(0x75)]()[_0x5a80ef(0x77)]()==='continue'),!_0x196f3f&&(console[_0x5a80ef(0x8c)](_0x5a80ef(0x76)),await new Promise(_0x2c5fd3=>setTimeout(_0x2c5fd3,0x3e8)));}console[_0x5a80ef(0x8c)](_0x5a80ef(0x89)),_0x196f3f[_0x5a80ef(0x71)]();}async function clickSellSimilarButton(){var _0x154f49=a0_0xb45ad5;console[_0x154f49(0x8c)](_0x154f49(0x7f));var _0x14c7ac=document[_0x154f49(0x6f)](_0x154f49(0x7c)),_0x3f8c87=Array['from'](_0x14c7ac)[_0x154f49(0x87)](_0x377f88=>_0x377f88['textContent'][_0x154f49(0x75)]()[_0x154f49(0x77)]()===_0x154f49(0x91)||_0x377f88[_0x154f49(0x7a)]['trim']()[_0x154f49(0x77)]()===_0x154f49(0x8d));_0x3f8c87['click']();}function a0_0x52de(){var _0x56e05f=['length','lowPerformingItemNodes','trim','waiting\x20for\x20submit\x20button\x20to\x20appear','toLowerCase','map','3774092qSdikd','textContent','lowPerformingItemData','button','479190vLyBWY','466Ficmbf','clickSellSimilarButton','storage','sellSimilarEndedItems','th.shui-dt-column__actualEndDate\x20button','13ogSGEe','3089GXQJTm','3pOZHqF','20iySXLB','find','1018870CuiAyE','submit\x20button\x20found','602028bwuJLM','ebay\x20ended\x20listings\x20functions.js\x20loaded','log','ähnlichen\x20artikel\x20verkaufen','local','from','itemNumber','sell\x20similar','11317164AuHKjk','get','itemNumbers','121rSvfJO','itemNumbers.length','3427176gfnSyW','querySelectorAll','18upmKrv','click','querySelector'];a0_0x52de=function(){return _0x56e05f;};return a0_0x52de();}function a0_0xdd08(_0x2ac83e,_0x17e85d){var _0x52deb7=a0_0x52de();return a0_0xdd08=function(_0xdd08ad,_0x5835fa){_0xdd08ad=_0xdd08ad-0x6d;var _0x77107f=_0x52deb7[_0xdd08ad];return _0x77107f;},a0_0xdd08(_0x2ac83e,_0x17e85d);}async function wipeLowPerformingItems(){var _0x9b91f8=a0_0xb45ad5;await chrome['storage'][_0x9b91f8(0x8e)]['remove']('lowPerformingItemData');}async function sortByEndDate(){var _0x1b0373=a0_0xb45ad5;console[_0x1b0373(0x8c)]('sortByEndDate');var _0x1b9784=document[_0x1b0373(0x72)](_0x1b0373(0x82));_0x1b9784['click'](),await onPageLoadAndStable(),_0x1b9784[_0x1b0373(0x71)](),await onPageLoadAndStable();}