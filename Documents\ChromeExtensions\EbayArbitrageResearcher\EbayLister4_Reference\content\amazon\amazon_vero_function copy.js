var a0_0x29e090=a0_0x2dbc;(function(_0x6709f3,_0x4b155a){var _0xc0b974=a0_0x2dbc,_0x419223=_0x6709f3();while(!![]){try{var _0xe12b1b=parseInt(_0xc0b974(0xe2))/0x1+parseInt(_0xc0b974(0xd1))/0x2*(parseInt(_0xc0b974(0xd0))/0x3)+parseInt(_0xc0b974(0xce))/0x4+-parseInt(_0xc0b974(0xbf))/0x5+parseInt(_0xc0b974(0xbd))/0x6*(-parseInt(_0xc0b974(0xe1))/0x7)+-parseInt(_0xc0b974(0xc3))/0x8*(-parseInt(_0xc0b974(0xba))/0x9)+-parseInt(_0xc0b974(0xb6))/0xa;if(_0xe12b1b===_0x4b155a)break;else _0x419223['push'](_0x419223['shift']());}catch(_0x2d33f0){_0x419223['push'](_0x419223['shift']());}}}(a0_0x4059,0x8d374));var amazonBrandToCheck=getProductBrand();amazonBrandToCheck=amazonBrandToCheck['toLowerCase']();var veroBrandDetectedList=[],amazonTitle=getProductTitle();function a0_0x2dbc(_0x2c1bf5,_0x321042){var _0x40590e=a0_0x4059();return a0_0x2dbc=function(_0x2dbc66,_0x2be23e){_0x2dbc66=_0x2dbc66-0xb5;var _0x119e94=_0x40590e[_0x2dbc66];return _0x119e94;},a0_0x2dbc(_0x2c1bf5,_0x321042);}amazonTitle=amazonTitle[a0_0x29e090(0xcd)]();window[a0_0x29e090(0xd8)][a0_0x29e090(0xcf)][a0_0x29e090(0xc2)]('/dp/')>-0x1&&(console[a0_0x29e090(0xdd)](a0_0x29e090(0xb8)),scanForVero());async function getVeroListText(){var _0x31c23e=a0_0x29e090,_0x14df35=[],_0x4f09e2=await fetch(chrome[_0x31c23e(0xc8)][_0x31c23e(0xd9)](_0x31c23e(0xe5)));_0x4f09e2=await _0x4f09e2[_0x31c23e(0xbc)](),_0x14df35=_0x4f09e2[_0x31c23e(0xd5)]('\x0a'),_0x14df35=_0x14df35[_0x31c23e(0xe6)](function(_0x44e690){return _0x44e690!='';});for(let _0x29ccb5=0x0;_0x29ccb5<_0x14df35[_0x31c23e(0xe3)];_0x29ccb5++){var _0x28e147=_0x14df35[_0x29ccb5];_0x28e147=_0x28e147[_0x31c23e(0xb5)](),_0x14df35[_0x29ccb5]=_0x28e147;}return _0x14df35;}function a0_0x4059(){var _0x40227a=['811879wnwmwE','length','getElementById','/VeroList.txt','filter','trim','16106710QecgSn','veroBrandDetectedList','scanning\x20for\x20vero','push','64296YcyTuP','\x0averoBrand:\x20\x0a','text','426uyLbgl','\x0aTitle\x20Contains\x20','157390sPzSHk','width','VeroBrands:\x20\x0a\x0a','indexOf','400kLfnGK','veroBrandWithoutInc','includes','img','checkIfBrandIsVero','runtime','src',',\x20inc','appendChild','replace','toLowerCase','2464372XdUgVf','href','69idqPzs','43590xFktKw','toString','height','productTitle','split','createElement','140','location','getURL','veroBrandWithOutWhiteSpace','inc','https://cdn1.iconfinder.com/data/icons/color-bold-style/21/30-512.png','log','\x20:\x0a','\x0averoBrandWithOutWhiteSpace:\x20\x0a','\x0averoBrandWithoutInc:\x20\x0a','6496XfpyQU'];a0_0x4059=function(){return _0x40227a;};return a0_0x4059();}async function scanForVero(){var _0x3f490e=await getVeroListText();await checkVeroBrandWithList(_0x3f490e);try{await isBrandVero();}catch(_0x471384){}}function checkIfBrandIsVero(){var _0x1b7436=a0_0x29e090;return console[_0x1b7436(0xdd)](_0x1b7436(0xc7)),new Promise(_0x2a57f7=>{var _0x10c244=_0x1b7436;console[_0x10c244(0xdd)](_0x10c244(0xb7),veroBrandDetectedList),veroBrandDetectedList[_0x10c244(0xe3)]>0x0?_0x2a57f7(!![]):_0x2a57f7(![]);});}function isBrandVero(){return new Promise(_0x5baa1a=>{var _0x8e734a=a0_0x2dbc;if(veroBrandDetectedList['length']>0x0){var _0x137d43=_0x8e734a(0xc1)+veroBrandDetectedList[_0x8e734a(0xd2)]()[_0x8e734a(0xcc)](/,/g,'\x0a'),_0x451775='https://upload.wikimedia.org/wikipedia/en/thumb/0/05/Vero_Insurance_logo.svg/1280px-Vero_Insurance_logo.svg.png',_0x569a62=document[_0x8e734a(0xd6)](_0x8e734a(0xc6));_0x569a62[_0x8e734a(0xc9)]=_0x451775,_0x569a62[_0x8e734a(0xc0)]=_0x8e734a(0xd7),_0x569a62[_0x8e734a(0xd3)]='60',_0x569a62['title']=_0x137d43;var _0x3927ad=document['getElementById'](_0x8e734a(0xd4));_0x3927ad[_0x8e734a(0xcb)](_0x569a62),_0x5baa1a();}else{var _0x63f197=_0x8e734a(0xdc),_0x569a62=document[_0x8e734a(0xd6)](_0x8e734a(0xc6));_0x569a62['src']=_0x63f197,_0x569a62[_0x8e734a(0xc0)]='50',_0x569a62['height']='30';var _0x3927ad=document[_0x8e734a(0xe4)](_0x8e734a(0xd4));_0x3927ad['appendChild'](_0x569a62),_0x5baa1a();}});}function checkVeroInAmazonTitle(_0x2c8e99,_0x13e76d){var _0x35b952=a0_0x29e090,_0xd5708b=amazonTitle['split']('\x20');for(let _0x29ef76=0x0;_0x29ef76<_0xd5708b[_0x35b952(0xe3)];_0x29ef76++){var _0x7f38e1=_0xd5708b[_0x29ef76];if(_0x7f38e1===_0x2c8e99){var _0x5fba2e='\x0aTitle\x20Contains\x20'+_0x13e76d+_0x35b952(0xde)+_0x2c8e99;veroBrandDetectedList[_0x35b952(0xb9)](_0x5fba2e);}}if(_0x2c8e99[_0x35b952(0xc5)]('\x20')){if(amazonTitle[_0x35b952(0xc5)](_0x2c8e99)){var _0x5fba2e=_0x35b952(0xbe)+_0x13e76d+_0x35b952(0xde)+_0x2c8e99;veroBrandDetectedList[_0x35b952(0xb9)](_0x5fba2e);}}}function checkVeroBrandWithList(_0xd18302){return new Promise(_0x46b7d8=>{var _0x4be1ff=a0_0x2dbc;for(var _0x3697fb=0x0;_0x3697fb<_0xd18302[_0x4be1ff(0xe3)];++_0x3697fb){var _0x2fcaff=_0xd18302[_0x3697fb]['toLowerCase']();_0x2fcaff['includes'](amazonBrandToCheck)&&veroBrandDetectedList['push']('\x0averoBrand:\x20\x0a'+_0x2fcaff);amazonBrandToCheck[_0x4be1ff(0xc5)](_0x2fcaff)&&veroBrandDetectedList['push'](_0x4be1ff(0xbb)+_0x2fcaff);checkVeroInAmazonTitle(_0x2fcaff,'veroBrand');var _0x4d5590=_0x2fcaff['replace'](/\s/g,'');_0x4d5590[_0x4be1ff(0xc5)](amazonBrandToCheck)&&veroBrandDetectedList[_0x4be1ff(0xb9)](_0x4be1ff(0xdf)+_0x4d5590);amazonBrandToCheck[_0x4be1ff(0xc5)](_0x4d5590)&&veroBrandDetectedList['push'](_0x4be1ff(0xdf)+_0x4d5590);checkVeroInAmazonTitle(_0x4d5590,_0x4be1ff(0xda));var _0x1e5aa8=_0x2fcaff;_0x1e5aa8=_0x1e5aa8[_0x4be1ff(0xcc)](_0x4be1ff(0xca),'');var _0x1e5aa8=_0x1e5aa8[_0x4be1ff(0xcc)](_0x4be1ff(0xdb),''),_0x1e5aa8=_0x1e5aa8[_0x4be1ff(0xcc)](',','');checkVeroInAmazonTitle(_0x1e5aa8,_0x4be1ff(0xc4)),_0x1e5aa8['includes'](amazonBrandToCheck)&&veroBrandDetectedList[_0x4be1ff(0xb9)](_0x4be1ff(0xe0)+_0x1e5aa8),amazonBrandToCheck[_0x4be1ff(0xc5)](_0x1e5aa8)&&veroBrandDetectedList[_0x4be1ff(0xb9)](_0x4be1ff(0xe0)+_0x1e5aa8),_0x46b7d8();}});}