{"response_format": "structured_json", "task": "Keyword relevance check with critical thinking", "instructions": "Please provide a structured JSON response with the relevance assessment of each keyword to the item. Consider the context and logical relevance beyond the title and description. Exclude generic words like 'for,' 'in,' and 'with.' Example: { 'heater': 'relevant', 'space': 'relevant', ... }", "item_description": "GAIATOP Space Heater, 1000W Energy Efficient Space Heaters for Indoor Use, 3 Mode PTC Fast Heating Ceramic Electric Heater, Overheating & 45°Tip-Over Protection Portable Heater for Office Home White", "keywords": ["heater", "space", "electric", "portable", "wall", "remote", "1500w", "with", "fan", "plug", "thermostat", "room", "500w", "timer", "for", "in", "fast", "heating", "ceramic", "control", "mini", "oscillating"]}