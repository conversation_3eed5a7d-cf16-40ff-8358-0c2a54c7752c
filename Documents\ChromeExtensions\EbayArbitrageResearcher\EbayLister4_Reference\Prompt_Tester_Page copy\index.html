<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>EcomSniper - Teach AI to Make Product Titles</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1><span class="red">Ecom</span><span class="blue">Sniper</span>: Teach AI Product Titles</h1>

    <div class="step">
      <h2>Step 1: Explain clearly what you want the AI to do:</h2>
      <textarea id="systemMessage" placeholder="Example: Make 5 short, simple product titles for my product."></textarea>
    </div>

    <div class="step">
        <h2>Step 2: Copy and Paste a Product Description:</h2>
        <textarea id="productDescription" placeholder="Example: Small USB fan, quiet, 3 speeds for desk."></textarea>
      </div>
      
      <div class="step">
        <h2>Step 3: Write titles you like for the product above:</h2>
        <div id="titlesContainer">
          <div class="title-row">
            <input type="text" class="example-title" placeholder="Example: Quiet USB Desk Fan">
            <button class="delete-btn">✖️</button>
          </div>
        </div>
        <button id="addTitleBtn">➕ Add another title</button>
      </div>
      

    <div class="step">
      <h2>Step 4: Check if the AI understands you:</h2>
      <textarea id="testProduct" placeholder="Write another product to see the titles AI will make."></textarea>
    </div>

    <div class="buttons">
      <button id="generateBtn" disabled>✅ Check AI Titles</button>
      <button id="exampleBtn">💡 Show Me Example</button>
      <button id="clearBtn">🗑️ Start Over</button>
      <button id="downloadBtn">⬇️ Save Instructions (JSON)</button>
    </div>

    <div id="results">
      <h2>📋 AI Made These Titles:</h2>
      <ul id="titlesList"></ul>
    </div>
  </div>

  <script src="script.js"></script>
</body>
</html>
