function a0_0xe8f6(){var _0x5d9021=['DOMContentLoaded','313117OWZPBT','local','onMessage','length','DOM\x20loaded','1128rkKkZe','snipedItemData','75GicHAE','92MOmZxr','type','then','storage','20tFqsMx','tab','717540NjJCzW','addEventListener','url','49992IqhEYx','params','log','amazon_search_page.js\x20loaded','get-top-items','228BTFdFV','17001bajcwI','runtime','862bEMEYJ','get','219725rXAzZz','140964hjaiZh'];a0_0xe8f6=function(){return _0x5d9021;};return a0_0xe8f6();}var a0_0x1dea9a=a0_0x351a;(function(_0x3ebbc6,_0x4c20af){var _0x159c9=a0_0x351a,_0x2b8182=_0x3ebbc6();while(!![]){try{var _0x3a45f4=parseInt(_0x159c9(0x162))/0x1*(parseInt(_0x159c9(0x16f))/0x2)+parseInt(_0x159c9(0x165))/0x3*(parseInt(_0x159c9(0x173))/0x4)+parseInt(_0x159c9(0x16e))/0x5*(-parseInt(_0x159c9(0x178))/0x6)+-parseInt(_0x159c9(0x167))/0x7+parseInt(_0x159c9(0x16c))/0x8*(-parseInt(_0x159c9(0x160))/0x9)+-parseInt(_0x159c9(0x175))/0xa+parseInt(_0x159c9(0x164))/0xb*(parseInt(_0x159c9(0x15f))/0xc);if(_0x3a45f4===_0x4c20af)break;else _0x2b8182['push'](_0x2b8182['shift']());}catch(_0x17b73c){_0x2b8182['push'](_0x2b8182['shift']());}}}(a0_0xe8f6,0x23b7f),console['log'](a0_0x1dea9a(0x15d)));var skuList=[];function a0_0x351a(_0x48fd36,_0x2df9b9){var _0xe8f6de=a0_0xe8f6();return a0_0x351a=function(_0x351a09,_0x1cd0c5){_0x351a09=_0x351a09-0x15d;var _0x7d720d=_0xe8f6de[_0x351a09];return _0x7d720d;},a0_0x351a(_0x48fd36,_0x2df9b9);}chrome[a0_0x1dea9a(0x161)][a0_0x1dea9a(0x169)]['addListener'](function(_0x55d55e,_0x1918cc,_0x453814){var _0x4b62f2=a0_0x1dea9a;console[_0x4b62f2(0x17a)](_0x1918cc[_0x4b62f2(0x174)]?'from\x20a\x20content\x20script:'+_0x1918cc['tab'][_0x4b62f2(0x177)]:'from\x20the\x20extension'),console[_0x4b62f2(0x17a)](_0x55d55e);if(_0x55d55e[_0x4b62f2(0x170)]=='get-top-items'){console[_0x4b62f2(0x17a)](_0x4b62f2(0x15e));var _0x47f1b2={};return _0x55d55e[_0x4b62f2(0x179)]!=null&&(_0x47f1b2=_0x55d55e[_0x4b62f2(0x179)]),retrieveFilteredCards(_0x47f1b2)[_0x4b62f2(0x171)](function(_0x168bca){var _0x54e32f=_0x4b62f2,_0xcd462a=[];for(var _0x5cff96=0x0;_0x5cff96<_0x168bca[_0x54e32f(0x16a)];_0x5cff96++){var _0x63fc8e=_0x168bca[_0x5cff96],_0x1bbea6=extractAsinFromCard(_0x63fc8e),_0x44a00a=extractTitleFromCard(_0x63fc8e),_0x5d8fb1=extractPriceFromElement(_0x63fc8e),_0x15e687=extractImageUrlFromCard(_0x63fc8e),_0x4ee456=extractReviewsFromCard(_0x63fc8e),_0x3e26a9={'asin':_0x1bbea6,'title':_0x44a00a,'price':_0x5d8fb1,'image':_0x15e687,'reviewCount':_0x4ee456};_0xcd462a['push'](_0x3e26a9);}_0x453814({'products':_0xcd462a});}),!![];}}),document[a0_0x1dea9a(0x176)](a0_0x1dea9a(0x166),async()=>{var _0x468017=a0_0x1dea9a;skuList=await getSkuListFromLocalStorage(),console[_0x468017(0x17a)](_0x468017(0x16b));var {snipeModeEnabled:_0x24de0f}=await chrome[_0x468017(0x172)]['local'][_0x468017(0x163)]('snipeModeEnabled');if(_0x24de0f){var {snipedItemData:_0x196b04}=await chrome['storage'][_0x468017(0x168)][_0x468017(0x163)](_0x468017(0x16d));activateSnipeMode(_0x196b04);}else addAsinButtons(),addCollectAsinsButton();});