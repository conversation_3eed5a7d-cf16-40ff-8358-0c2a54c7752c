(function(_0xa69125,_0x283e88){var _0x2351c5=a0_0x32dd,_0x5eae40=_0xa69125();while(!![]){try{var _0xfd28b7=-parseInt(_0x2351c5(0x1bc))/0x1*(parseInt(_0x2351c5(0x1e8))/0x2)+parseInt(_0x2351c5(0x1d0))/0x3*(parseInt(_0x2351c5(0x1d8))/0x4)+-parseInt(_0x2351c5(0x1f8))/0x5+-parseInt(_0x2351c5(0x1f7))/0x6*(-parseInt(_0x2351c5(0x1f0))/0x7)+-parseInt(_0x2351c5(0x1db))/0x8+-parseInt(_0x2351c5(0x1dd))/0x9*(-parseInt(_0x2351c5(0x1bf))/0xa)+parseInt(_0x2351c5(0x204))/0xb;if(_0xfd28b7===_0x283e88)break;else _0x5eae40['push'](_0x5eae40['shift']());}catch(_0x595a11){_0x5eae40['push'](_0x5eae40['shift']());}}}(a0_0x3ec3,0xce0cb),console['log']('functions.js\x20loaded'));function addBanner(_0x5c6156){var _0x24a146=a0_0x32dd,_0x4b4be3;!_0x5c6156[_0x24a146(0x1cb)]?_0x4b4be3=canvas[_0x24a146(0x1f6)]()||0x5dc:_0x4b4be3=_0x5c6156[_0x24a146(0x1cb)][_0x24a146(0x1f6)]()||0x5dc;var _0x4f0b8f=_0x4b4be3-0x12c,_0x19a45a=_0x5c6156[_0x24a146(0x1e5)]||document[_0x24a146(0x1cd)](_0x24a146(0x1e5))[_0x24a146(0x200)]||_0x24a146(0x1d3),_0x628360=_0x5c6156[_0x24a146(0x1de)]||document[_0x24a146(0x1cd)]('bannerFont')['value']||_0x24a146(0x1bb),_0x3248a9=_0x5c6156['fontSize']||0x64,_0x1936a2=new fabric['Text'](_0x19a45a,{'fontSize':_0x3248a9,'fontFamily':_0x628360,'fontWeight':'bold'}),_0x10f2b1;_0x1936a2[_0x24a146(0x1d9)]<=_0x4f0b8f?_0x10f2b1=new fabric['Text'](_0x19a45a,{'fontSize':_0x3248a9,'fill':_0x5c6156['bannerFontColor']||document[_0x24a146(0x1cd)](_0x24a146(0x1d7))[_0x24a146(0x200)]||_0x24a146(0x1c1),'fontFamily':_0x628360,'fontWeight':_0x24a146(0x1ec),'textAlign':_0x24a146(0x1ee),'left':_0x5c6156[_0x24a146(0x1dc)]||0x32,'top':_0x5c6156[_0x24a146(0x1cf)]||0x32}):_0x10f2b1=new fabric[(_0x24a146(0x1d1))](_0x19a45a,{'fontSize':_0x3248a9/0x2,'fill':_0x5c6156[_0x24a146(0x1d7)]||document[_0x24a146(0x1cd)](_0x24a146(0x1d7))[_0x24a146(0x200)]||'white','fontFamily':_0x628360,'fontWeight':_0x24a146(0x1ec),'width':_0x4f0b8f-0x14,'textAlign':_0x24a146(0x1ee),'splitByGrapheme':![],'left':_0x5c6156['left']||0x32,'top':_0x5c6156['top']||0x32});_0x10f2b1[_0x24a146(0x1d5)]();var _0x2a243f=_0x5c6156[_0x24a146(0x1f5)]||parseInt(document[_0x24a146(0x1cd)](_0x24a146(0x1f5))[_0x24a146(0x200)])||0x14,_0x2c8f4a=_0x10f2b1 instanceof fabric[_0x24a146(0x1d1)]?_0x10f2b1[_0x24a146(0x1d9)]:_0x1936a2[_0x24a146(0x1d9)],_0x4dd391=_0x10f2b1 instanceof fabric[_0x24a146(0x1d1)]?_0x10f2b1['height']:_0x1936a2[_0x24a146(0x1be)],_0x3d67a9=new fabric[(_0x24a146(0x1e2))]({'rx':0xa,'ry':0xa,'fill':_0x5c6156[_0x24a146(0x1c9)]||document[_0x24a146(0x1cd)](_0x24a146(0x1c9))['value']||'#FF0000','width':_0x2c8f4a+_0x2a243f,'height':_0x4dd391+_0x2a243f,'originX':_0x24a146(0x1ee),'originY':_0x24a146(0x1ee),'left':_0x10f2b1[_0x24a146(0x1dc)]+_0x2c8f4a/0x2,'top':_0x10f2b1[_0x24a146(0x1cf)]+_0x4dd391/0x2}),_0x577340=new fabric[(_0x24a146(0x1f2))]([_0x3d67a9,_0x10f2b1],{'left':_0x10f2b1[_0x24a146(0x1dc)],'top':_0x10f2b1[_0x24a146(0x1cf)],'originX':_0x24a146(0x1dc),'originY':_0x24a146(0x1cf)});!_0x5c6156[_0x24a146(0x1cb)]?(canvas['add'](_0x577340),canvas[_0x24a146(0x1d4)](_0x577340),canvas[_0x24a146(0x1c6)]()):(_0x5c6156[_0x24a146(0x1cb)][_0x24a146(0x1ea)](_0x577340),_0x5c6156['canvas'][_0x24a146(0x1d4)](_0x577340),_0x5c6156[_0x24a146(0x1cb)][_0x24a146(0x1c6)]());}async function changeBannerText(_0x2c23f2,_0x3a9139){var _0x2e1b2c=a0_0x32dd;console[_0x2e1b2c(0x1cc)](_0x2e1b2c(0x1fb));const _0x1c145b=_0x2c23f2[_0x2e1b2c(0x1da)]()[_0x2e1b2c(0x1fa)](async function(_0x41c5b9){var _0xb3aece=_0x2e1b2c;if(_0x41c5b9[_0xb3aece(0x1c4)]===_0xb3aece(0x1e9)&&(_0x41c5b9[_0xb3aece(0x206)](0x1)['text']===_0xb3aece(0x1d2)||_0x41c5b9['item'](0x1)['text']==='{{mainBenefit}}')){var _0x498c64;_0x41c5b9[_0xb3aece(0x206)](0x1)[_0xb3aece(0x1eb)]===_0xb3aece(0x1d2)&&(_0x498c64=_0x3a9139[_0xb3aece(0x1fc)]);if(_0x41c5b9['item'](0x1)[_0xb3aece(0x1eb)]===_0xb3aece(0x1e0)){console['log'](_0xb3aece(0x1ca));var _0xd71b12=await askchatGptForMainBenefit(_0x3a9139[_0xb3aece(0x1fc)],_0x3a9139['descriptionAndFeatures']);console[_0xb3aece(0x1cc)](_0xb3aece(0x1c5)+_0xd71b12),_0x498c64=_0xd71b12;}console['log'](_0xb3aece(0x1ed));var _0x4c2bf0=_0x41c5b9[_0xb3aece(0x206)](0x1),_0x19b376=_0x41c5b9['item'](0x0);if(_0x4c2bf0&&_0x4c2bf0[_0xb3aece(0x1c4)]===_0xb3aece(0x1eb)&&_0x19b376&&_0x19b376[_0xb3aece(0x1c4)]===_0xb3aece(0x1ef)){var _0xbeda89=_0x19b376[_0xb3aece(0x1e4)],_0xf0df8=_0x4c2bf0['fontFamily'],_0x2dea28=_0x4c2bf0[_0xb3aece(0x1e4)],_0x382792=0x14,_0x1ad173=_0x498c64,_0x57a44d=_0x41c5b9[_0xb3aece(0x1cf)],_0x39f45c=_0x41c5b9[_0xb3aece(0x1dc)],_0x1dffc2=_0x4c2bf0[_0xb3aece(0x1e1)],_0x529375={'bannerText':_0x1ad173,'bannerColor':_0xbeda89,'bannerFont':_0xf0df8,'bannerFontColor':_0x2dea28,'bannerPadding':_0x382792,'canvas':_0x2c23f2,'left':_0x39f45c,'top':_0x57a44d,'fontSize':_0x1dffc2};addBanner(_0x529375),_0x2c23f2[_0xb3aece(0x1bd)](_0x41c5b9);}}});await Promise[_0x2e1b2c(0x1e7)](_0x1c145b),_0x2c23f2[_0x2e1b2c(0x1c6)]();}function a0_0x32dd(_0x1f4d3b,_0x52306c){var _0x3ec301=a0_0x3ec3();return a0_0x32dd=function(_0x32ddf,_0x3c4f48){_0x32ddf=_0x32ddf-0x1bb;var _0x15324d=_0x3ec301[_0x32ddf];return _0x15324d;},a0_0x32dd(_0x1f4d3b,_0x52306c);}function a0_0x3ec3(){var _0x348e6b=['17250574KhJAly','clear','item','Arial','66INfgDe','remove','height','12115910ATIabo','scaleToWidth','white','scaleX','scaleToHeight','type','mainBenefit:\x20','renderAll','Image','loadFromJSON','bannerColor','mainBenefit','canvas','log','getElementById','template\x20set\x20up\x20complete','top','222141JBRazo','Textbox','{{productTitle}}','Best\x20Seller','setActiveObject','setCoords','Image\x20processed','bannerFontColor','4NPzOTT','width','getObjects','5954080xOYoCZ','left','9hrMqor','bannerFont','template','{{mainBenefit}}','fontSize','Rect','Images\x20populated','fill','bannerText','length','all','19550hNckPj','group','add','text','bold','Found\x20custom\x20banner\x20text\x20group','center','rect','1582ZDBvuU','scaleY','Group','set','isType','bannerPadding','getWidth','8796dUOvkz','4758995sNTnvo','local','map','changeBannerText','title','get','storage','Populating\x20images','value','opacity','sendToBack','bind'];a0_0x3ec3=function(){return _0x348e6b;};return a0_0x3ec3();}async function populateTemplate(_0xb35c76,_0x3729fa,_0x413002){var _0x28f016=a0_0x32dd;_0xb35c76[_0x28f016(0x205)](),_0xb35c76['backgroundColor']=_0x28f016(0x1c1),console[_0x28f016(0x1cc)]('Populating\x20template');var {template:_0x4d4270}=await chrome[_0x28f016(0x1fe)][_0x28f016(0x1f9)][_0x28f016(0x1fd)](_0x28f016(0x1df));await new Promise(_0x3c7d77=>{var _0x2a8919=_0x28f016;_0xb35c76[_0x2a8919(0x1c8)](_0x4d4270,_0x3c7d77);}),console[_0x28f016(0x1cc)]('Template\x20loaded'),_0xb35c76['setBackgroundColor'](_0x28f016(0x1c1),_0xb35c76[_0x28f016(0x1c6)][_0x28f016(0x203)](_0xb35c76)),await populateImages(_0xb35c76,_0x3729fa),console[_0x28f016(0x1cc)](_0x28f016(0x1e3)),await changeBannerText(_0xb35c76,_0x413002),_0xb35c76[_0x28f016(0x1c6)]();}async function populateImages(_0x5b221d,_0x333e29){var _0x156a16=a0_0x32dd;let _0x564706=0x0;var _0x5821ad=_0x5b221d[_0x156a16(0x1da)]();let _0x3c8d5a=[];console[_0x156a16(0x1cc)](_0x156a16(0x1ff),_0x5b221d,_0x333e29);for(let _0x53d4a8 of _0x5821ad){if(_0x53d4a8['isType'](_0x156a16(0x1e9))&&_0x564706<_0x333e29[_0x156a16(0x1e6)]&&!isNaN(_0x53d4a8[_0x156a16(0x206)](0x1)[_0x156a16(0x1eb)])){const _0x5ed942=_0x53d4a8[_0x156a16(0x1d9)]*_0x53d4a8[_0x156a16(0x1c2)],_0x4c2a59=_0x53d4a8[_0x156a16(0x1be)]*_0x53d4a8[_0x156a16(0x1f1)],_0x572716=_0x53d4a8[_0x156a16(0x206)](0x0)[_0x156a16(0x201)];console[_0x156a16(0x1cc)]('Processing\x20image',_0x564706);const _0x38317a=await loadImage(_0x333e29[_0x564706]);console[_0x156a16(0x1cc)]('Image\x20loaded');const _0x41ed57=await processImage(_0x38317a,_0x5ed942,_0x4c2a59);console['log'](_0x156a16(0x1d6));const _0x18c4af=new fabric[(_0x156a16(0x1c7))](_0x41ed57,{'left':_0x53d4a8[_0x156a16(0x1dc)],'top':_0x53d4a8[_0x156a16(0x1cf)],'opacity':_0x572716});_0x18c4af[_0x156a16(0x1c0)](_0x5ed942),_0x18c4af[_0x156a16(0x1c3)](_0x4c2a59),_0x18c4af[_0x156a16(0x201)]<0x1?_0x3c8d5a['push'](_0x18c4af):_0x5b221d['add'](_0x18c4af),_0x5b221d['remove'](_0x53d4a8),centerImage(_0x18c4af,_0x5ed942,_0x4c2a59,_0x53d4a8),_0x564706++;}else _0x53d4a8[_0x156a16(0x1f4)](_0x156a16(0x1e9))&&!isNaN(_0x53d4a8[_0x156a16(0x206)](0x1)[_0x156a16(0x1eb)])&&_0x5b221d['remove'](_0x53d4a8);}console[_0x156a16(0x1cc)](_0x156a16(0x1ce));for(const _0xab004b of _0x3c8d5a){_0x5b221d[_0x156a16(0x1ea)](_0xab004b),_0x5b221d[_0x156a16(0x202)](_0xab004b);}}function centerImage(_0x22b101,_0xf891d8,_0x1957d9,_0x3e7ef5){var _0x9ecff7=a0_0x32dd;const _0x3c9649=_0x22b101['getScaledWidth'](),_0x83c2d6=_0x22b101['getScaledHeight']();_0x3c9649<_0xf891d8&&_0x22b101['set']({'left':_0x3e7ef5[_0x9ecff7(0x1dc)]+(_0xf891d8-_0x3c9649)/0x2}),_0x83c2d6<_0x1957d9&&_0x22b101[_0x9ecff7(0x1f3)]({'top':_0x3e7ef5['top']+(_0x1957d9-_0x83c2d6)/0x2}),_0x22b101[_0x9ecff7(0x1d5)]();}