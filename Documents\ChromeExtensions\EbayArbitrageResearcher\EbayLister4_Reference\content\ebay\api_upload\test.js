function a0_0x39a2(){const _0x3007f6=['save','push','2485611KrJgMR','enabled','505617scLSiX','UploadPhoto','add','4NAdxuR','POST','toFixed','getCurrentSuffix','get','length','prod_variations','from','log','uploaded://','/msku-update','5757680rmulAL','productName','application/json','set','Sending\x20variations\x20payload\x20to\x20eBay\x20Bulk\x20Editor:','imgProp','467192AaSOFh','com','variationSpecific','4586070LKEOOT','has','error','AddItem','6477485phRcij','Bulk\x20Editor\x20response:','259942ZVloqv'];a0_0x39a2=function(){return _0x3007f6;};return a0_0x39a2();}(function(_0xb5fb23,_0x380e05){const _0x4785f1=a0_0x5d6a,_0x59ff50=_0xb5fb23();while(!![]){try{const _0x51e352=parseInt(_0x4785f1(0xc2))/0x1*(parseInt(_0x4785f1(0xca))/0x2)+-parseInt(_0x4785f1(0xc5))/0x3+parseInt(_0x4785f1(0xb3))/0x4+-parseInt(_0x4785f1(0xc0))/0x5+parseInt(_0x4785f1(0xbc))/0x6+parseInt(_0x4785f1(0xc7))/0x7+parseInt(_0x4785f1(0xb9))/0x8;if(_0x51e352===_0x380e05)break;else _0x59ff50['push'](_0x59ff50['shift']());}catch(_0x543465){_0x59ff50['push'](_0x59ff50['shift']());}}}(a0_0x39a2,0xb2485));async function uploadVariationImages(_0x2dac04,_0x26a29a,_0x17c653,_0x295157){const _0x4de445=a0_0x5d6a;if(!_0x17c653[_0x4de445(0xae)])return;const _0x11bcc3=[],_0xfeeddf={},_0x4e0517=new Map();let _0x3000ca=_0x17c653[_0x4de445(0xae)][0x0]?.[_0x4de445(0xb8)]||'';for(let _0x3f6a51=0x0;_0x3f6a51<_0x17c653[_0x4de445(0xae)][_0x4de445(0xad)];_0x3f6a51++){const _0x54937c=_0x17c653[_0x4de445(0xae)][_0x3f6a51],{attrs:_0x4e60f1,img:_0x4515ea,imgProp:_0xe21dfa,price:_0x413419,currency:_0x3cf5d8,quantity:quantity=0x1}=_0x54937c;let _0x3c7105='';if(_0x4515ea){if(!_0x4e0517[_0x4de445(0xbd)](_0x4515ea))try{const _0x49b29=await ApiPhotoUploader[_0x4de445(0xc8)](_0x4515ea,_0x26a29a);_0x4e0517[_0x4de445(0xb6)](_0x4515ea,_0x49b29),_0x3c7105=_0x49b29;}catch(_0x1d33c1){console[_0x4de445(0xbe)]('Image\x20upload\x20failed\x20for\x20Variation\x20#'+_0x3f6a51,_0x1d33c1);}else _0x3c7105=_0x4e0517[_0x4de445(0xce)](_0x4515ea);}if(_0x3000ca&&_0x4e60f1[_0x3000ca]?.[_0x4de445(0xb4)]&&_0x3c7105){const _0xd09dc2=_0x3000ca,_0x3e0086=_0x4e60f1[_0x3000ca][_0x4de445(0xb4)];!_0xfeeddf[_0xd09dc2]&&(_0xfeeddf[_0xd09dc2]={}),!_0xfeeddf[_0xd09dc2][_0x3e0086]&&(_0xfeeddf[_0xd09dc2][_0x3e0086]=[]),_0xfeeddf[_0xd09dc2][_0x3e0086][_0x4de445(0xc4)](_0x3c7105);}const _0x21306c={};for(const _0x3f6505 in _0x4e60f1){_0x21306c[_0x3f6505]=_0x4e60f1[_0x3f6505][_0x4de445(0xb4)];}const _0x42ad0e={'price':(_0x413419||0x1)[_0x4de445(0xcc)](0x2),'quantity':quantity,'state':_0x4de445(0xc6)};_0x11bcc3[_0x4de445(0xc4)]({'variationSpecific':_0x21306c,'listingVariation':_0x42ad0e,'index':_0x3f6a51});}const _0x530d09={'action':_0x4de445(0xc3),'draftId':_0x2dac04,'listingMode':_0x4de445(0xbf),'restricted':![],'upiFieldName':'upc','variationItem':_0x11bcc3,'variationSpecificPictureSet':_0xfeeddf,'variationSpecificsMetaData':gatherAllAttributes(_0x11bcc3)},_0x25e6bb=Utils[_0x4de445(0xcd)](),_0x470c51='https://bulkedit.ebay.'+_0x25e6bb+_0x4de445(0xb2);console[_0x4de445(0xb0)](_0x4de445(0xb7),_0x530d09);const _0x2ed0eb=await fetch(_0x470c51,{'method':_0x4de445(0xcb),'credentials':'include','body':JSON['stringify'](_0x530d09),'headers':{'Content-Type':_0x4de445(0xb5)}}),_0x3e4b52=await _0x2ed0eb['json']();console[_0x4de445(0xb0)](_0x4de445(0xc1),_0x3e4b52);}function gatherAllAttributes(_0x32e434){const _0x3a4964=a0_0x5d6a,_0x1510f5={};for(const _0x1fe2be of _0x32e434){for(const _0x57b1fe in _0x1fe2be[_0x3a4964(0xbb)]){const _0x55c60e=_0x1fe2be[_0x3a4964(0xbb)][_0x57b1fe];!_0x1510f5[_0x57b1fe]&&(_0x1510f5[_0x57b1fe]=new Set()),_0x1510f5[_0x57b1fe][_0x3a4964(0xc9)](_0x55c60e);}}const _0x943732=[];for(const _0x2f00ad in _0x1510f5){_0x943732[_0x3a4964(0xc4)]({'name':_0x2f00ad,'value':Array[_0x3a4964(0xaf)](_0x1510f5[_0x2f00ad])});}return _0x943732;}function a0_0x5d6a(_0x1137b9,_0x54529f){const _0x39a244=a0_0x39a2();return a0_0x5d6a=function(_0x5d6a2b,_0x1f0665){_0x5d6a2b=_0x5d6a2b-0xad;let _0x28e5c7=_0x39a244[_0x5d6a2b];return _0x28e5c7;},a0_0x5d6a(_0x1137b9,_0x54529f);}const ApiPhotoUploader={async 'UploadPhoto'(_0x12f4bc,_0x1eb575){const _0x7406fa=a0_0x5d6a;return _0x7406fa(0xb1)+_0x12f4bc;}},Utils={'getCurrentSuffix'(){const _0x4c8fc5=a0_0x5d6a;return _0x4c8fc5(0xba);}};