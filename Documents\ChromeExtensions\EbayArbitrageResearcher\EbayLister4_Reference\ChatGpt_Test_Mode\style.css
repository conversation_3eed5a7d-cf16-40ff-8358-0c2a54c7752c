body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

.chat-container {
    width: 500px;
    margin: 50px auto;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#messageInput {
    width: calc(100% - 90px);
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
}

#sendMessage {
    width: 70px;
}

#chatHistory {
    margin-top: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.message {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
}

.request {
    background-color: #d0e0f0;
    color: #003366;
}

.response {
    margin-left: 20px;
    background-color: #d0f0d0;
    color: #004d33;
    position: relative;
}

.request-label, .response-label {
    color: #0056b3; /* Different shade of blue for labels */
    font-weight: bold;
}

.time-tracker-container {
    text-align: right;
    margin-top: 5px;
}


.time-tracker {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 0.8em;
    background-color: #f0e68c; /* Active timer in yellow */
    padding: 2px 5px;
    border-radius: 4px;
    color: #555;
}

.time-tracker-final {
    background-color: #d3d3d3; /* Final time in a neutral color */
}


#logContainer {
    margin-top: 30px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    max-height: 600px;
    overflow-y: auto;
}

.log-message {
    font-family: monospace;
    color: #333;
    margin-bottom: 5px;
}

.timestamp {
    background-color: #ffeb3b; /* Light yellow background for visibility */
    color: #333; /* Dark text for contrast */
    padding: 2px 6px;
    margin-right: 5px;
    border-radius: 4px;
    font-weight: bold;
    font-family: monospace;
}

.log-text {
    color: #444; /* Slightly softer text color for the log message */
}


#clearLog {
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#clearLog:hover {
    background-color: #d32f2f;
}