


.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    position: relative;
    padding: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    max-height: 80%;
    overflow: auto;
}

.modal-canvas {
    border: 1px solid #ddd;
    cursor: crosshair;
}

.modal-button {
    margin-top: 20px;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    outline: none;
}

.modal-button:hover {
    background-color: #0056b3;
}


.mask-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}



#generate-image-div {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-start;
    padding: 20px;
    background-color: #f4f4f4;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 800px;
    gap: 10px;
}


.checkbox-div {
    display: flex;
    flex-direction: column;
  }
  
  .checkbox-div div {
    display: flex;
    align-items: center; /* Aligns items vertically in the center */
    margin-bottom: 10px; /* Adds space between each checkbox */
  }
  
  .checkbox-div label {
    margin-right: 10px; /* Adds space between the label and the checkbox */
  }
  
  /* Ensures the checkboxes align even if labels are of different lengths */
  .checkbox-div input[type="checkbox"] {
    margin-left: auto; 
    order: 2; /* Puts the checkbox after the label in the layout */
  }
  
  .checkbox-div label {
    flex-grow: 1; /* Allows the label to take up the available space */
    text-align: right; /* Aligns the label text to the right */
  }
  

  input.error {
    border: 2px solid red !important; /* Ensure this border style takes precedence */
    background-color: #FFD6D6 !important; /* Ensure this background color takes precedence */
    color: black !important; /* Ensure this text color takes precedence */
    animation: shake 0.5s !important; /* Ensure this animation takes precedence */
    outline: 2px solid red !important; /* Ensure this outline takes precedence */
}

@keyframes shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

input.error:focus {
    outline: 2px solid red !important; /* Ensure this focus outline style takes precedence */
}

#generate-image-title {
    font-size: 20px; /* Smaller, more elegant font size */
    font-family: 'Arial', sans-serif; /* Professional font */
    color: #333333; /* Dark grey, subtle and professional */
    padding: 5px 10px; /* Reduced padding for a more compact look */
    margin-top: 5px; /* Maintain a little space above the title */
    text-align: center; /* Center align text */
    background-color: transparent; /* No background */
    border: none; /* No border */
    position: relative; /* Required for pseudo-elements */
    overflow: hidden; /* Ensures the pseudo-elements are contained within the title's bounds */
}

#generate-image-title::before, #generate-image-title::after {
    content: attr(data-text); /* Use the text of the title for the effect */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #0064D2; /* eBay's blue color */
    overflow: hidden;
}

#generate-image-title::before {
    animation: electric-trace 3s linear infinite;
    clip-path: polygon(0 0, 15% 0, 15% 100%, 0% 100%);
}

#generate-image-title::after {
    animation: electric-trace 3s linear 1.5s infinite;
    clip-path: polygon(100% 0, 85% 0, 85% 100%, 100% 100%);
}

@keyframes electric-trace {
    0%, 100% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(100%);
    }
}



#generate-image-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start; 
    width: 100%; /* Full width */
    max-width: 800px; /* Match generate-image-div's max width */
}






.generate-image-button {
    background-color: #3B5998; /* Facebook's signature blue for a different look */
    color: white; /* White text for contrast */
    padding: 5px 10px; /* Padding */
    border: none; /* No border */
    border-radius: 3px; /* Rounded corners */
    font-size: 12px; /* Font size */
    font-weight: bold; /* Bold text */
    cursor: pointer; /* Pointer on hover */
    transition: background-color 0.3s, opacity 0.3s; /* Smooth background transition */
    position: relative; /* Needed for the loading animation positioning */
    overflow: hidden; /* Ensures the pseudo-elements do not overflow the button */
    margin-left: 5px; /* Spacing between buttons */
    display: inline-block; /* Necessary for proper spacing and alignment */
}

.generate-image-button:hover {
    background-color: #334D77; /* Darker shade of blue for hover effect */
}

.generate-image-button:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, 0.2);
    animation: loadingAnimation 2s linear infinite;
    opacity: 0; /* Start hidden */
}

.generate-image-button-working:before {
    opacity: 1; /* Show animation when working */
}

.generate-image-button-success {
    background-color: #4CAF50; /* Green background for success state */
    color: white; /* White text for contrast */
}

.generate-image-button-error {
    background-color: #F44336; /* Red background for error state */
    color: white; /* White text for contrast */
}

@keyframes loadingAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}


#generate-image-button{
    margin-top: 20px;
}


#background-description-textarea{
    width: 100%;
    height: 100px;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    font-size: 14px;
    resize: both; /* Allows both horizontal and vertical resizing */
}