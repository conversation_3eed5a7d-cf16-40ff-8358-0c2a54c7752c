<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tracker System</title>
    <link rel="stylesheet" href="styles.css" />
</head>
<body>

    <!-- Main Controls -->
    <div id="main_controls">
        
        <!-- <label class="pill-toggle" title="Click to Start or Stop tracking">
            <input type="checkbox" id="tracker_run_status">
            <span class="pill-toggle__label"
                  data-off="Start Tracking"
                  data-on="Stop Tracking">
            </span>
          </label> -->

          <button id="start_tracking">Start Tracking</button>
        
          <label class="switch">
              <input type="checkbox" id="tracker_run_status">
              <span class="slider round"></span>
          </label>
          
          


    </div>

    <!-- Filter Options -->
    <div id="tracker_filters">
        <h2 id="tracking_filters_title">Tracker Filters</h2>

        <!-- Rule-Based Settings -->
        <!-- <div id="enable_stock_monitor" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>Enable Stock Monitor</label>
        </div> -->

        <!-- Stock Monitor with Prime Option and Restock Quantity -->
        <div id="enable_stock_monitor" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                Enable Stock Monitor
            </label>
            <label>
                <select class="item_action_select" name="prime_option" style="margin-left: 10px;">
                    <option value="all">All Items</option>
                    <option value="prime_only">Prime Only</option>
                </select>
            </label>
            <label style="margin-left: 10px;">
                Restock Quantity:
                <input type="number" name="restock_quantity" min="1" value="1" style="width:50px; margin-left:5px;">
            </label>
        </div>



    <!-- Price Monitor with Options -->
    <div id="enable_price_monitor" class="rule_based_setting">
        <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
        <label>
            Enable Price Monitor
        </label>

        <!-- Pricing Option Dropdown -->
        <label style="margin-left: 10px;">
            Pricing Option:
            <select class="item_action_select" name="pricing_option" style="margin-left: 5px;">
                <option value="markup_pricing">Markup Pricing</option>
                <option value="variable_pricing">Variable Pricing</option>
            </select>
        </label>

        <!-- Markup Percentage (default 100) -->
        <label style="margin-left: 10px;">
            Markup Percentage:
            <input type="number" name="markup_percentage" min="0" value="100" style="width:50px; margin-left:5px;">%
        </label>

        <!-- Price Trigger Threshold -->
        <label style="margin-left: 10px;">
            Price Trigger Threshold (±$):
            <input type="number" name="price_trigger_threshold" min="0" value="2" style="width:50px; margin-left:5px;">
        </label>
    </div>

    <!-- Ending Price Filter (Separate Setting) -->
    <div id="enable_price_ending_filter" class="rule_based_setting">
        <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
        <label>
            Only Price Monitor Items with Ending Price:
        </label>
        <!-- Input with placeholder to guide the user -->
        <input type="text" name="price_endings" placeholder="99,97,95" title="Enter price endings separated by commas, e.g., 99,97,95" style="width:100px; margin-left:5px;">
    </div>





        <h2>Pruning Options</h2>

        <div id="enable_delete_policy_violation_items" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>Delete Policy Violation Items</label>
        </div>

        <h3>Rule-Based Settings</h3>

        <!-- Rule-Based Setting for Items with No SKU -->
        <div id="enable_delete_items_with_no_sku" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                </select>
                items with no SKU
            </label>
        </div>

        <!-- //enable_delete_items_with_broken_sku -->
        <div id="enable_delete_items_with_broken_sku" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                </select>
                items with broken SKU
            </label>
        </div>

        <!-- Rule-Based Setting for Items Not Found on Amazon -->
        <div id="enable_delete_items_not_found_on_amazon" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                </select>
                items that are not found on Amazon
            </label>
        </div>

        <!-- Rule-Based Setting for SKU Changed on Amazon -->
        <div id="enable_delete_items_sku_changed_on_amazon" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                </select>
                items where the SKU has been changed on Amazon
            </label>
        </div>

        <!-- Rule-Based Setting for Items with No Sales -->
        <div id="enable_delete_items_with_no_sales" class="rule_based_setting">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                </select>
                items with
                <input type="number" class="sales_threshold_input" name="sales_threshold" min="0" value="0" style="width:50px; margin: 0 5px;">
                or fewer sales that are older than
                <input type="number" class="days_threshold_input" name="days_threshold" min="0" value="30" style="width:50px; margin: 0 5px;">
                days
            </label>
        </div>

        <!-- enable_delete_items_by_non_chinese_sellers -->
        <div id="enable_delete_items_by_non_chinese_sellers" class="rule_based_setting hidden_menu" style="display: none;">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
                <select class="item_action_select" name="action" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out_of_stock">Make Out of Stock</option>
                    <option value="save_item_id">Save Item ID</option>
                </select>
                items by non-Chinese sellers
            </label>
        </div>


        <div id="enable_set_gspr" class="rule_based_setting hidden_menu" style="display: none;">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
               Set GSPR
            </label>
        </div>

        <div id="enable_set_skus_to_items" class="rule_based_setting hidden_menu" style="display: none;">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
               Set SKUs to Items
            </label>
        </div>
        
        <div id="enable_scan_for_restricted_words" class="rule_based_setting hidden_menu" style="display: none;">
            <input type="checkbox" class="enable_setting_checkbox" name="checkbox">
            <label>
               Scan for Restricted Words
            </label>
        </div>

    </div>

    <div id="additional_gspr_controls_container" class="hidden_menu" style="display: none;">
        <button id="print_failed_gspr_items" >Print Item Numbers</button>
        <button id="clear_failed_gspr_items" >Clear Item Numbers</button>
    </div>
    <div>
        <textarea id="gspr_item_numbers" class="hidden_menu" style="display: none;" rows="4" cols="50" placeholder="Item numbers of items will be here"></textarea>
        total items: <span id="total_failed_gspr_items">0</span>
    </div>


    <!-- Additional Settings -->
    <div id="additional_settings">
        <h2>Additional Settings</h2>

        <div>
            <input type="checkbox" id="enable_continuous_tracking">
            <label for="enable_continuous_tracking">Enable Continuous Tracking</label>
        </div>

        <div>
            <label for="tracking_timeout">Tracking Timeout (in seconds)</label>
            <input type="text" id="tracking_timeout" value="60">
        </div>

 

        <div>
            <input type="checkbox" id="should_track_log">
            <label for="should_track_log">Log Data</label>

            <button id="open_logs">Open Logs</button>
        </div>  

        <div>
            <input type="checkbox" id="should_pin_tabs">
            <label for="should_pin_tabs">Pin Tabs</label>
        </div>

        <div>
            <input type="checkbox" id="stay_on_ebay_page">
            <label for="stay_on_ebay_page">Keep eBay Page Open</label>
            <p style="font-size: 0.9em; color: gray; margin-top: 4px;">
                Stops your computer from switching away from the eBay page. This helps the tracker keep working, especially on slower computers.
            </p>
        </div>
        
        <!-- Multi‑Country Tracking -->
        <!-- <div id="multi_country_section" style="margin-top:16px;">
            <div>
            <input type="checkbox" id="enable_multi_country_tracking">
            <label for="enable_multi_country_tracking">Track Multiple Countries</label>
            </div>
        
            <div id="multi_country_grid"
                style="display:none;
                        margin-top:8px;
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                        gap: 4px;">
            <label><input type="checkbox" name="country" value="US"> US</label>
            <label><input type="checkbox" name="country" value="UK"> UK</label>
            <label><input type="checkbox" name="country" value="CA"> CA</label>
            <label><input type="checkbox" name="country" value="AU"> AU</label>
            <label><input type="checkbox" name="country" value="DE"> DE</label>
            <label><input type="checkbox" name="country" value="FR"> FR</label>
            <label><input type="checkbox" name="country" value="IT"> IT</label>
            <label><input type="checkbox" name="country" value="ES"> ES</label>
            <label><input type="checkbox" name="country" value="NL"> NL</label>
            <label><input type="checkbox" name="country" value="BE"> BE</label>
            <label><input type="checkbox" name="country" value="CH"> CH</label>
            </div>
        </div> -->
        
                



        <!-- <div style="display: none;">
            <label for="page_number">Page Number</label>
            <input type="text" id="page_number" value="1">
        </div> -->

        <div class="tracking-container">

            <button id="reset_tracking">Reset</button>
            <div class="tracking-info">
              <label for="page_number">Page:</label>
              <div class="numbers">
                <input type="number" id="page_number" class="page_number" value="1" min="1">
                <span class="slash">/</span>
                <span id="totalPageNumber" class="total-pages">1</span>
              </div>
            </div>
          
            <div class="tracking-info">
              <label for="current_tracker_position">Position:</label>
              <div class="numbers">
                <input type="number" id="current_tracker_position" class="current_tracker_position" value="1" min="1" max="200">
                <span class="slash">/</span>
                <span class="max_position">200</span>
              </div>
            </div>
          </div>
          

    </div>

    <!-- TWO-BAR PROGRESS CONTAINER (POSITION: FIXED TOP-RIGHT) -->
    <div id="progress_bars_container">
        <!-- ITEM (TOP) -->
        <div class="progress-section">
        <div class="progress-title" id="item_progress_title">
            Item <span id="current_item_label">1</span> of <span id="total_item_label">200</span>
            (<span id="item_percentage_label">0%</span>)
        </div>
        <div class="progress-bar-bg">
            <div class="progress-bar-fill item-bar-fill" id="item_progress_fill"></div>
        </div>
        </div>
    
        <!-- PAGE (BOTTOM) -->
        <div class="progress-section">
        <div class="progress-title" id="page_progress_title">
            Page <span id="current_page_label">1</span> of <span id="total_page_label">1</span>
            (<span id="page_percentage_label">0%</span>)
        </div>
        <div class="progress-bar-bg">
            <div class="progress-bar-fill page-bar-fill" id="page_progress_fill"></div>
        </div>
        </div>
    </div>
  
  

    <div id="additional_controls_chinese_sellers_container" class="hidden_menu" style="display: none;">
        <button id="print_non_chinese_sellers_item_ids" >Print Non-Chinese Sellers Item IDs</button>
        <button id="clear_non_chinese_sellers_item_ids" >Clear Non-Chinese Sellers Item IDs</button>
    </div>
    


    <!-- Test Button (for development, ignore in production) -->
     <div id="test_button_div" class="hidden_menu" style="display: none;">
         <div id="test_input_container">
             <label for="test_button_value">Item Number</label>
             <input type="text" id="test_button_value" style="display: block;" >
         </div>
         <button id="test_button" style="display: block;">Test</button>
     </div>


    <script src="script.js"></script>
</body>
</html>
