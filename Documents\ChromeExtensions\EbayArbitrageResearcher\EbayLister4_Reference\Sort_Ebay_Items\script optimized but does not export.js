function a0_0x21c6(){const _0x2a0ad7=['div','364QuBjDS','storage','4417350yCQVXG','totalCompetitors','filter','16082jXRAbz','7UDpPEz','15111SVrDJm','addEventListener','item','N/A','1600368obLGHW','getTime','createDocumentFragment','click','domain','\x20results','soldInLastDays','local','innerHTML','37285DmwnPf','forEach','total_results','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20','title','appendChild','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Item\x20Number:</strong>\x20','\x22\x20target=\x22_blank\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<img\x20src=\x22','3920YbYGGu','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Competitors:</strong>\x20','className','get','21587115rorGIc','447OhOvqk','dateSold','savedEbaySearchItems','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Sold:</strong>\x20','clear_items','length','\x22\x20alt=\x22','price','7078200OqReyr','/itm/','remove','value','createElement','totalSold','image','itemNumber','getElementById'];a0_0x21c6=function(){return _0x2a0ad7;};return a0_0x21c6();}const a0_0x4b604d=a0_0x23c6;function a0_0x23c6(_0x2692fc,_0x3fde7e){const _0x21c675=a0_0x21c6();return a0_0x23c6=function(_0x23c64d,_0x1c84a0){_0x23c64d=_0x23c64d-0x18c;let _0x357757=_0x21c675[_0x23c64d];return _0x357757;},a0_0x23c6(_0x2692fc,_0x3fde7e);}(function(_0x270fd5,_0x57d4e5){const _0x3bbee9=a0_0x23c6,_0x46223=_0x270fd5();while(!![]){try{const _0x4e8bfd=-parseInt(_0x3bbee9(0x195))/0x1+-parseInt(_0x3bbee9(0x18f))/0x2*(-parseInt(_0x3bbee9(0x1ab))/0x3)+-parseInt(_0x3bbee9(0x1bd))/0x4*(parseInt(_0x3bbee9(0x19e))/0x5)+parseInt(_0x3bbee9(0x1b3))/0x6*(-parseInt(_0x3bbee9(0x190))/0x7)+parseInt(_0x3bbee9(0x1a6))/0x8*(parseInt(_0x3bbee9(0x191))/0x9)+parseInt(_0x3bbee9(0x18c))/0xa+parseInt(_0x3bbee9(0x1aa))/0xb;if(_0x4e8bfd===_0x57d4e5)break;else _0x46223['push'](_0x46223['shift']());}catch(_0x109e4c){_0x46223['push'](_0x46223['shift']());}}}(a0_0x21c6,0xebedc),document[a0_0x4b604d(0x192)]('DOMContentLoaded',function(){const _0x52eb1b=a0_0x4b604d;document[_0x52eb1b(0x1bb)]('apply_filters')['addEventListener'](_0x52eb1b(0x198),applyFilters);}));function applyAllFilters(_0xd2a982,_0x467497,_0xbbc655,_0xd5c951){const _0x4d42d3=a0_0x4b604d;let _0x5383ca=new Date(),_0x43c9c1=new Date(_0x5383ca[_0x4d42d3(0x196)]()-_0x467497*0x18*0x3c*0x3c*0x3e8);return _0xd2a982[_0x4d42d3(0x18e)](_0x42203c=>{const _0x79d62=_0x4d42d3;let _0x50baee=new Date(_0x42203c[_0x79d62(0x1ac)]),_0x26b529=parseInt(_0x42203c[_0x79d62(0x18d)],0xa),_0x16d2e8=parseInt(_0x42203c['totalSold'],0xa);return(!_0x467497||_0x50baee>=_0x43c9c1&&_0x50baee<=_0x5383ca)&&(!_0xbbc655||_0x26b529>=_0xbbc655)&&(!_0xd5c951||_0x16d2e8>=_0xd5c951);});}displayItems();async function applyFilters(){const _0x59e714=a0_0x4b604d;let {savedEbaySearchItems:_0x1ef275}=await chrome[_0x59e714(0x1be)][_0x59e714(0x19c)]['get'](_0x59e714(0x1ad));const _0x4a5e94=parseInt(document[_0x59e714(0x1bb)](_0x59e714(0x19b))[_0x59e714(0x1b6)],0xa)||0x0,_0x1dc974=parseInt(document[_0x59e714(0x1bb)]('totalCompetitors')[_0x59e714(0x1b6)],0xa)||0x0,_0x34d23a=parseInt(document[_0x59e714(0x1bb)](_0x59e714(0x1b8))[_0x59e714(0x1b6)],0xa)||0x0;let _0x307bcc=applyAllFilters(_0x1ef275,_0x4a5e94,_0x1dc974,_0x34d23a);displayItems(_0x307bcc);}async function displayItems(_0x1bcea2=[]){const _0x212048=a0_0x4b604d;if(!_0x1bcea2[_0x212048(0x1b0)]){let {savedEbaySearchItems:_0x138098}=await chrome[_0x212048(0x1be)]['local'][_0x212048(0x1a9)](_0x212048(0x1ad));_0x1bcea2=_0x138098||[];}const {domain:_0x5a5bba}=await chrome[_0x212048(0x1be)][_0x212048(0x19c)]['get'](_0x212048(0x199)),_0xeafc9e=document['getElementById']('items-list');_0xeafc9e[_0x212048(0x19d)]='';let _0x1f87ba=document[_0x212048(0x197)]();_0x1bcea2[_0x212048(0x19f)](_0x423f3d=>{const _0x5db190=_0x212048,_0xb31c17=document[_0x5db190(0x1b7)](_0x5db190(0x1bc));_0xb31c17[_0x5db190(0x1a8)]=_0x5db190(0x193),_0xb31c17[_0x5db190(0x19d)]='\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<a\x20href=\x22https://www.ebay.'+_0x5a5bba+_0x5db190(0x1b4)+_0x423f3d['itemNumber']+_0x5db190(0x1a5)+_0x423f3d[_0x5db190(0x1b9)]+_0x5db190(0x1b1)+_0x423f3d['title']+'\x22\x20class=\x22item-image\x22/>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</a>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22item-info\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<h3\x20class=\x22displayed-titles\x22>'+_0x423f3d[_0x5db190(0x1a2)]+'</h3>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Price:</strong>\x20'+_0x423f3d[_0x5db190(0x1b2)]+_0x5db190(0x1ae)+(_0x423f3d['totalSold']===_0x5db190(0x194)?'Not\x20Available':_0x423f3d[_0x5db190(0x1b8)])+_0x5db190(0x1a7)+_0x423f3d[_0x5db190(0x18d)]+'</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Date\x20Sold:</strong>\x20'+_0x423f3d[_0x5db190(0x1ac)]+_0x5db190(0x1a4)+_0x423f3d['itemNumber']+_0x5db190(0x1a1);var _0x246036=createEcommerceSearchButtonsPanel2(_0x423f3d[_0x5db190(0x1a2)],_0x423f3d['username'],_0x423f3d[_0x5db190(0x1b9)],![],_0x423f3d[_0x5db190(0x1ba)],_0x423f3d[_0x5db190(0x1b2)]);_0xb31c17[_0x5db190(0x1a3)](_0x246036),_0x1f87ba[_0x5db190(0x1a3)](_0xb31c17);}),_0xeafc9e['appendChild'](_0x1f87ba),document[_0x212048(0x1bb)](_0x212048(0x1a0))[_0x212048(0x19d)]=_0x1bcea2[_0x212048(0x1b0)]+_0x212048(0x19a);}document[a0_0x4b604d(0x1bb)](a0_0x4b604d(0x1af))[a0_0x4b604d(0x192)](a0_0x4b604d(0x198),async()=>{const _0x5f8e27=a0_0x4b604d;await chrome[_0x5f8e27(0x1be)]['local'][_0x5f8e27(0x1b5)](_0x5f8e27(0x1ad)),displayItems([]);});