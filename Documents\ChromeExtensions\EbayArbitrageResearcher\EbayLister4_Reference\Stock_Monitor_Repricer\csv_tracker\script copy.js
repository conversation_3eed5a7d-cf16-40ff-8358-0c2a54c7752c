const a0_0x34456e=a0_0x3eff;(function(_0x342259,_0x56b999){const _0x2ac023=a0_0x3eff,_0x4485aa=_0x342259();while(!![]){try{const _0x3d8eac=-parseInt(_0x2ac023(0x24a))/0x1*(parseInt(_0x2ac023(0x218))/0x2)+parseInt(_0x2ac023(0x1f9))/0x3+-parseInt(_0x2ac023(0x1c7))/0x4*(parseInt(_0x2ac023(0x20b))/0x5)+parseInt(_0x2ac023(0x214))/0x6*(parseInt(_0x2ac023(0x23f))/0x7)+parseInt(_0x2ac023(0x259))/0x8+-parseInt(_0x2ac023(0x249))/0x9*(-parseInt(_0x2ac023(0x1ba))/0xa)+-parseInt(_0x2ac023(0x1e1))/0xb;if(_0x3d8eac===_0x56b999)break;else _0x4485aa['push'](_0x4485aa['shift']());}catch(_0x5a8419){_0x4485aa['push'](_0x4485aa['shift']());}}}(a0_0x3385,0xb5cbc));let showOnlyUnprocessed=![];const showUnprocessedOnlyCheckbox=document[a0_0x34456e(0x248)]('showUnprocessedOnly');showUnprocessedOnlyCheckbox[a0_0x34456e(0x1cd)](a0_0x34456e(0x1b4),_0x3966d9=>{const _0x4a15af=a0_0x34456e;showOnlyUnprocessed=_0x3966d9[_0x4a15af(0x1f3)][_0x4a15af(0x22e)],renderTable();});const actionsConfig={'gspr':{'label':'Get\x20GSPR','dataColumns':[a0_0x34456e(0x245),a0_0x34456e(0x1be),a0_0x34456e(0x244),a0_0x34456e(0x246),'Manufacturer\x20Country',a0_0x34456e(0x220),a0_0x34456e(0x1b8),'Manufacturer\x20Phone',a0_0x34456e(0x225),a0_0x34456e(0x258),a0_0x34456e(0x201),a0_0x34456e(0x1c6),a0_0x34456e(0x1f2),a0_0x34456e(0x22b),a0_0x34456e(0x1ed),a0_0x34456e(0x234),a0_0x34456e(0x217),a0_0x34456e(0x1ea),a0_0x34456e(0x1c3),a0_0x34456e(0x238),'Responsible\x20Person\x20EU\x20Text'],'scannedColumn':a0_0x34456e(0x20d)},'chineseSeller':{'label':a0_0x34456e(0x221),'dataColumns':[a0_0x34456e(0x205)],'scannedColumn':a0_0x34456e(0x1b5)}};let debugMode=![],originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,concurrency=0x1,isProcessing=![],currentHeaders=[];const showRowNumber=!![];let currentLineIndex=0x0;const csvUpload=document['getElementById'](a0_0x34456e(0x1d9)),headerSelectionContainer=document[a0_0x34456e(0x248)](a0_0x34456e(0x251)),headerCheckboxes=document[a0_0x34456e(0x248)]('headerCheckboxes'),confirmHeadersBtn=document[a0_0x34456e(0x248)](a0_0x34456e(0x1eb)),mainSection=document[a0_0x34456e(0x248)](a0_0x34456e(0x1f0)),dataTable=document[a0_0x34456e(0x248)]('dataTable'),downloadCsvBtn=document['getElementById'](a0_0x34456e(0x243)),concurrencyInput=document[a0_0x34456e(0x248)](a0_0x34456e(0x235)),processedCountEl=document[a0_0x34456e(0x248)](a0_0x34456e(0x1fc)),totalCountEl=document[a0_0x34456e(0x248)](a0_0x34456e(0x228)),statusMessageEl=document['getElementById'](a0_0x34456e(0x21b)),restoreFromStorageBtn=document[a0_0x34456e(0x248)](a0_0x34456e(0x1e6)),clearDataBtn=document[a0_0x34456e(0x248)](a0_0x34456e(0x224)),runActionsBtn=document[a0_0x34456e(0x248)](a0_0x34456e(0x223)),actionCheckboxes=document['getElementById'](a0_0x34456e(0x1ef)),startLineInput=document[a0_0x34456e(0x248)](a0_0x34456e(0x1ca));document[a0_0x34456e(0x1cd)]('DOMContentLoaded',onDOMContentLoaded),csvUpload[a0_0x34456e(0x1cd)]('change',handleFileUpload),confirmHeadersBtn[a0_0x34456e(0x1cd)](a0_0x34456e(0x21e),handleConfirmHeaders),downloadCsvBtn[a0_0x34456e(0x1cd)](a0_0x34456e(0x21e),handleDownloadCSV),restoreFromStorageBtn[a0_0x34456e(0x1cd)]('click',handleRestoreFromStorage),clearDataBtn[a0_0x34456e(0x1cd)]('click',handleClearData),runActionsBtn[a0_0x34456e(0x1cd)](a0_0x34456e(0x21e),handleRunActions),concurrencyInput['addEventListener']('change',_0x2e3c60=>{const _0x41240f=a0_0x34456e;concurrency=parseInt(_0x2e3c60['target'][_0x41240f(0x203)],0xa)||0x1;});function a0_0x3eff(_0x141bbd,_0x234b0c){const _0x338538=a0_0x3385();return a0_0x3eff=function(_0x3eff1a,_0x5bc187){_0x3eff1a=_0x3eff1a-0x1b3;let _0x537064=_0x338538[_0x3eff1a];return _0x537064;},a0_0x3eff(_0x141bbd,_0x234b0c);}async function onDOMContentLoaded(){const _0x1d9043=a0_0x34456e;await restoreState();originalData[_0x1d9043(0x229)]>0x0&&selectedHeaders[_0x1d9043(0x229)]>0x0&&(initActionsUI(),renderTable(),mainSection['style'][_0x1d9043(0x1f4)]=_0x1d9043(0x24c),startLineInput&&(startLineInput[_0x1d9043(0x203)]=currentLineIndex));const _0x1f1cea=document[_0x1d9043(0x248)](_0x1d9043(0x1d0));_0x1f1cea['addEventListener'](_0x1d9043(0x1b4),_0x202997=>{const _0x4c8d6e=_0x1d9043;debugMode=_0x202997[_0x4c8d6e(0x1f3)][_0x4c8d6e(0x22e)],console['log'](_0x4c8d6e(0x1c8),debugMode?'ON':_0x4c8d6e(0x1f7));});}async function handleFileUpload(_0x51fe4d){const _0x345c58=a0_0x34456e,_0x343ab0=_0x51fe4d[_0x345c58(0x1f3)][_0x345c58(0x202)][0x0];if(!_0x343ab0)return;Papa[_0x345c58(0x250)](_0x343ab0,{'header':!![],'skipEmptyLines':!![],'dynamicTyping':![],'complete':async _0x48d171=>{const _0x1c2ca6=_0x345c58,_0x699ca5=_0x48d171[_0x1c2ca6(0x1e5)];if(_0x699ca5[_0x1c2ca6(0x229)]===0x0){alert(_0x1c2ca6(0x1ee));return;}originalData=_0x699ca5,originalData[_0x1c2ca6(0x1f5)](_0x22c7be=>{const _0x4e44db=_0x1c2ca6;for(const _0x3d10ba in _0x22c7be){if(_0x3d10ba[_0x4e44db(0x257)](_0x4e44db(0x23c))){const _0xd8485e=(_0x22c7be[_0x3d10ba]||'')[_0x4e44db(0x1dd)]()[_0x4e44db(0x216)]();_0x22c7be[_0x3d10ba]=_0xd8485e===_0x4e44db(0x22c);}}});const _0xbf6737=Object[_0x1c2ca6(0x204)](originalData[0x0]);appliedActions={};for(const [_0x2197a0,_0x3dda8b]of Object[_0x1c2ca6(0x21c)](actionsConfig)){appliedActions[_0x2197a0]=_0xbf6737[_0x1c2ca6(0x211)](_0x3dda8b[_0x1c2ca6(0x254)]);}const _0x4b8293=Object['values'](actionsConfig)[_0x1c2ca6(0x231)](_0x123ff2=>[_0x123ff2[_0x1c2ca6(0x254)],..._0x123ff2['dataColumns']]),_0x34beb6=_0xbf6737[_0x1c2ca6(0x1d6)](_0x3cf871=>!_0x4b8293['includes'](_0x3cf871));selectedHeaders=_0x34beb6,selectedHeaders['length']===0x0||mainSection[_0x1c2ca6(0x237)]['display']===_0x1c2ca6(0x1d2)?showHeaderSelection():(mainSection[_0x1c2ca6(0x237)][_0x1c2ca6(0x1f4)]=_0x1c2ca6(0x24c),initActionsUI(),renderTable(),await saveState());},'error':function(_0x3a476b){const _0x388b3d=_0x345c58;console[_0x388b3d(0x212)](_0x388b3d(0x207),_0x3a476b),alert(_0x388b3d(0x21a));}});}function showHeaderSelection(){const _0x3c29a1=a0_0x34456e;headerSelectionContainer[_0x3c29a1(0x237)][_0x3c29a1(0x1f4)]=_0x3c29a1(0x24c),headerCheckboxes['innerHTML']='';const _0x2b4ca0=Object[_0x3c29a1(0x204)](originalData[0x0]);_0x2b4ca0['forEach'](_0x3da160=>{const _0x292d89=_0x3c29a1,_0x4bfd33=document[_0x292d89(0x1de)](_0x292d89(0x1fe));_0x4bfd33[_0x292d89(0x256)]='<label><input\x20type=\x22checkbox\x22\x20value=\x22'+_0x3da160+_0x292d89(0x236)+_0x3da160+'</label>',headerCheckboxes[_0x292d89(0x23a)](_0x4bfd33);});}async function handleConfirmHeaders(){const _0x2750a4=a0_0x34456e,_0x459f04=headerCheckboxes[_0x2750a4(0x1d8)]('input[type=\x22checkbox\x22]');selectedHeaders=Array[_0x2750a4(0x23b)](_0x459f04)[_0x2750a4(0x1d6)](_0x2a291a=>_0x2a291a['checked'])['map'](_0x1928c4=>_0x1928c4[_0x2750a4(0x203)]);if(selectedHeaders[_0x2750a4(0x229)]===0x0){alert(_0x2750a4(0x23d));return;}originalData=originalData[_0x2750a4(0x1df)](_0x417913=>{const _0x438219=_0x2750a4,_0x39e415={};for(const _0x31b4f5 of selectedHeaders){_0x417913[_0x438219(0x1c5)](_0x31b4f5)&&(_0x39e415[_0x31b4f5]=_0x417913[_0x31b4f5]);}return _0x39e415;}),appliedActions={},headerSelectionContainer[_0x2750a4(0x237)][_0x2750a4(0x1f4)]=_0x2750a4(0x1d2),mainSection[_0x2750a4(0x237)][_0x2750a4(0x1f4)]=_0x2750a4(0x24c),initActionsUI(),renderTable(),await saveState();}function initActionsUI(){const _0x10f68b=a0_0x34456e;actionCheckboxes['innerHTML']='';for(const [_0x217076,_0x2936d4]of Object[_0x10f68b(0x21c)](actionsConfig)){const _0x1c685c=appliedActions[_0x217076]?_0x10f68b(0x22e):'',_0x3ff429=document[_0x10f68b(0x1de)]('div');_0x3ff429[_0x10f68b(0x256)]=_0x10f68b(0x241)+_0x217076+'\x22\x20'+_0x1c685c+'>\x20'+_0x2936d4[_0x10f68b(0x215)]+_0x10f68b(0x1b9),actionCheckboxes[_0x10f68b(0x23a)](_0x3ff429);}}function isRowProcessed(_0x3fc357){const _0x22d0a9=a0_0x34456e,_0x26a37b=Object[_0x22d0a9(0x204)](appliedActions)[_0x22d0a9(0x1d6)](_0x233686=>appliedActions[_0x233686]);if(_0x26a37b[_0x22d0a9(0x229)]===0x0)return![];return _0x26a37b[_0x22d0a9(0x219)](_0x2d5881=>{const _0x5423c1=_0x22d0a9,_0x5c4649=actionsConfig[_0x2d5881][_0x5423c1(0x254)];if(_0x3fc357[_0x5c4649]===undefined)return![];return _0x3fc357[_0x5c4649]===!![];});}function renderTable(){const _0x582ef2=a0_0x34456e;dataTable[_0x582ef2(0x256)]='';let _0x5db334=[];showRowNumber&&_0x5db334[_0x582ef2(0x1c9)]('Row');_0x5db334[_0x582ef2(0x1c9)](...selectedHeaders);for(const [_0x37fa4d,_0x52f828]of Object['entries'](appliedActions)){if(_0x52f828){const _0x3206c1=actionsConfig[_0x37fa4d];_0x3206c1[_0x582ef2(0x1cf)][_0x582ef2(0x1f5)](_0x457703=>{const _0x2a8a55=_0x582ef2;if(!_0x5db334[_0x2a8a55(0x211)](_0x457703))_0x5db334['push'](_0x457703);}),!_0x5db334[_0x582ef2(0x211)](_0x3206c1['scannedColumn'])&&_0x5db334[_0x582ef2(0x1c9)](_0x3206c1[_0x582ef2(0x254)]);}}currentHeaders=_0x5db334;const _0xc04152=document[_0x582ef2(0x1de)](_0x582ef2(0x1d3)),_0x5df779=document[_0x582ef2(0x1de)]('tr');_0x5db334[_0x582ef2(0x1f5)](_0x42a32c=>{const _0x1ce6e9=_0x582ef2,_0x1a40dc=document[_0x1ce6e9(0x1de)]('th');_0x1a40dc[_0x1ce6e9(0x1bc)]=_0x42a32c,_0x42a32c['startsWith']('scanned')&&_0x1a40dc[_0x1ce6e9(0x1b7)]['add'](_0x1ce6e9(0x1c0)),_0x5df779[_0x1ce6e9(0x23a)](_0x1a40dc);}),_0xc04152[_0x582ef2(0x23a)](_0x5df779),dataTable[_0x582ef2(0x23a)](_0xc04152);const _0x342219=document[_0x582ef2(0x1de)](_0x582ef2(0x20f));let _0x2256fe=originalData;showOnlyUnprocessed&&(_0x2256fe=_0x2256fe[_0x582ef2(0x1d6)](isRowUnprocessed)),_0x2256fe[_0x582ef2(0x1f5)]((_0x2a5604,_0x54da30)=>{const _0x27abf9=_0x582ef2,_0x41c8b6=document[_0x27abf9(0x1de)]('tr');currentHeaders[_0x27abf9(0x1f5)](_0x15bd02=>{const _0x4df968=_0x27abf9,_0x5a5a94=document[_0x4df968(0x1de)]('td');let _0x2bc6d4='';_0x15bd02===_0x4df968(0x242)?_0x2bc6d4=(_0x54da30+0x1)[_0x4df968(0x1d1)]():(_0x2bc6d4=_0x2a5604[_0x15bd02]??'',_0x15bd02['startsWith'](_0x4df968(0x23c))&&(_0x2bc6d4=_0x2a5604[_0x15bd02]===!![]?'✓':'')),_0x5a5a94['textContent']=_0x2bc6d4,_0x41c8b6['appendChild'](_0x5a5a94);}),isRowProcessed(_0x2a5604)&&_0x41c8b6[_0x27abf9(0x1b7)][_0x27abf9(0x1e0)](_0x27abf9(0x1db)),_0x342219['appendChild'](_0x41c8b6);}),dataTable[_0x582ef2(0x23a)](_0x342219),updateProgressUI();}function isRowUnprocessed(_0x271eb7){const _0x23cfa3=a0_0x34456e,_0x22fd04=Object['keys'](appliedActions)[_0x23cfa3(0x1d6)](_0xefd6dc=>appliedActions[_0xefd6dc]);if(_0x22fd04[_0x23cfa3(0x229)]===0x0)return![];return!_0x22fd04[_0x23cfa3(0x219)](_0x5e9fec=>{const _0x30454c=_0x23cfa3,_0x4b8fca=actionsConfig[_0x5e9fec][_0x30454c(0x254)];return _0x271eb7[_0x4b8fca]===!![];});}async function handleRestoreFromStorage(){const _0x5f3be2=a0_0x34456e;await restoreState();if(originalData[_0x5f3be2(0x229)]===0x0){alert(_0x5f3be2(0x252));return;}selectedHeaders['length']===0x0?showHeaderSelection():(initActionsUI(),renderTable(),mainSection[_0x5f3be2(0x237)][_0x5f3be2(0x1f4)]=_0x5f3be2(0x24c));}async function handleClearData(){const _0xad27ad=a0_0x34456e;originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,isProcessing=![],dataTable[_0xad27ad(0x256)]='',mainSection[_0xad27ad(0x237)]['display']=_0xad27ad(0x1d2),headerSelectionContainer[_0xad27ad(0x237)][_0xad27ad(0x1f4)]='none',await chrome[_0xad27ad(0x22a)][_0xad27ad(0x25a)]['remove']([_0xad27ad(0x24d),_0xad27ad(0x1c2),_0xad27ad(0x1e2),_0xad27ad(0x1c1)]),csvUpload[_0xad27ad(0x203)]='',statusMessageEl[_0xad27ad(0x1bc)]='',processedCountEl[_0xad27ad(0x1bc)]='0',totalCountEl[_0xad27ad(0x1bc)]='0',alert(_0xad27ad(0x239));}function handleDownloadCSV(){const _0x537678=a0_0x34456e,_0x4edad4=generateCSV(),_0x4589dd=new Blob([_0x4edad4],{'type':'text/csv;charset=utf-8;'}),_0x337cfd=URL[_0x537678(0x1fb)](_0x4589dd),_0x37b786=document[_0x537678(0x1de)]('a');_0x37b786['href']=_0x337cfd,_0x37b786[_0x537678(0x1da)]='exported_data.csv',_0x37b786[_0x537678(0x21e)](),URL[_0x537678(0x1b6)](_0x337cfd);}function generateCSV(){const _0x1de6a2=a0_0x34456e;let _0x25bc3e=[...selectedHeaders];for(const [_0x168d83,_0x1515d2]of Object[_0x1de6a2(0x21c)](appliedActions)){if(_0x1515d2){const _0x29c2c3=actionsConfig[_0x168d83];_0x29c2c3['dataColumns'][_0x1de6a2(0x1f5)](_0x39f65e=>{const _0x4d6f13=_0x1de6a2;if(!_0x25bc3e[_0x4d6f13(0x211)](_0x39f65e))_0x25bc3e[_0x4d6f13(0x1c9)](_0x39f65e);}),!_0x25bc3e[_0x1de6a2(0x211)](_0x29c2c3['scannedColumn'])&&_0x25bc3e[_0x1de6a2(0x1c9)](_0x29c2c3[_0x1de6a2(0x254)]);}}const _0x2941cc=originalData[_0x1de6a2(0x1df)](_0x42a836=>{const _0x3fa33d=_0x1de6a2,_0x476030={..._0x42a836};return _0x25bc3e[_0x3fa33d(0x1f5)](_0x4f1ec4=>{const _0x334a77=_0x3fa33d;_0x4f1ec4[_0x334a77(0x257)]('scanned')&&(_0x476030[_0x4f1ec4]=_0x476030[_0x4f1ec4]===!![]?'true':_0x334a77(0x1c4));}),_0x476030;});return Papa[_0x1de6a2(0x233)]({'fields':_0x25bc3e,'data':_0x2941cc},{'quotes':!![],'delimiter':',','newline':'\x0a'});}async function saveState(){const _0x26137d=a0_0x34456e;await chrome[_0x26137d(0x22a)][_0x26137d(0x25a)][_0x26137d(0x1d7)]({'originalData':originalData,'selectedHeaders':selectedHeaders,'appliedActions':appliedActions,'currentLineIndex':currentLineIndex});}async function restoreState(){const _0x1956c2=a0_0x34456e,_0x5b93f4=await chrome['storage'][_0x1956c2(0x25a)][_0x1956c2(0x24f)](['originalData',_0x1956c2(0x1c2),'appliedActions',_0x1956c2(0x1c1)]);_0x5b93f4[_0x1956c2(0x24d)]&&_0x5b93f4[_0x1956c2(0x1c2)]&&(originalData=_0x5b93f4[_0x1956c2(0x24d)],selectedHeaders=_0x5b93f4['selectedHeaders'],appliedActions=_0x5b93f4[_0x1956c2(0x1e2)]||{}),typeof _0x5b93f4[_0x1956c2(0x1c1)]===_0x1956c2(0x1bb)&&(currentLineIndex=_0x5b93f4['currentLineIndex']);}function updateProgressUI(){const _0x50bc3a=a0_0x34456e;processedCount=countProcessedRows();const _0x4195ef=originalData['length'],_0x21149f=_0x4195ef-processedCount;processedCountEl[_0x50bc3a(0x1bc)]=processedCount,totalCountEl[_0x50bc3a(0x1bc)]=_0x4195ef;const _0x467dff=document[_0x50bc3a(0x248)](_0x50bc3a(0x1e8));_0x467dff&&(_0x467dff[_0x50bc3a(0x1bc)]=_0x21149f),updateTitleWithProgress();}function updateTitleWithProgress(){const _0x4ce363=a0_0x34456e,_0x4a9953=countProcessedRows(),_0x4852ae=originalData[_0x4ce363(0x229)];document[_0x4ce363(0x20c)]=_0x4ce363(0x1e3)+_0x4a9953+_0x4ce363(0x247)+_0x4852ae+_0x4ce363(0x227)+currentLineIndex+')';}function a0_0x3385(){const _0x1709f6=['Processing...','Responsible\x20Person\x201\x20AddressLine2','target','display','forEach','min','OFF','message','1120122CYlJaV','name','createObjectURL','processedCount','CustomLabel','div','max','No\x20actions\x20selected.','Responsible\x20Person\x201\x20Type','files','value','keys','Chinese\x20Seller','responsiblePersonEUText','Error\x20parsing\x20CSV:','Skipping\x20due\x20to\x20error:','gspr',',\x20Overall:\x20','5cjpNpO','title','scannedGSPR','domain','tbody','postalCode','includes','error','emailAddress','6hvXYaq','label','toLowerCase','Responsible\x20Person\x201\x20StateOrProvince','2ZnFALE','every','Error\x20parsing\x20CSV.\x20Check\x20the\x20browser\x20console\x20for\x20details.','statusMessage','entries','warn','click','address2','Manufacturer\x20PostalCode','Check\x20Chinese\x20Seller','/?th=1&psc=1','runActionsBtn','clearDataBtn','Manufacturer\x20Email','Domain\x20not\x20found\x20in\x20storage','\x20(Line:\x20','totalCount','length','storage','Responsible\x20Person\x201\x20City','true','Failed\x20to\x20get\x20GSPR\x20data\x20from\x20background:\x20','checked','log','Error\x20processing\x20action:','flatMap','Run:\x20','unparse','Responsible\x20Person\x201\x20PostalCode','concurrencyInput','\x22\x20checked>\x20','style','Manufacturer\x20Info\x20Text','CSV\x20data\x20cleared.','appendChild','from','scanned','Please\x20select\x20at\x20least\x20one\x20header.','manufacturerInfoText','3503367oFQsyL','random','<label><input\x20type=\x22checkbox\x22\x20class=\x22actionCheck\x22\x20value=\x22','Row','downloadCsvBtn','Manufacturer\x20AddressLine2','Manufacturer\x20Name','Manufacturer\x20City','\x20/\x20','getElementById','9DpHhPl','774302AhBSku','/dp/','block','originalData','Dummy\x20Data','get','parse','headerSelectionContainer','No\x20saved\x20data\x20found\x20in\x20storage.','get_gspr','scannedColumn','then','innerHTML','startsWith','Responsible\x20Person\x201','6678912rddsNK','local','manufacturerInfo','change','scannedChinese','revokeObjectURL','classList','Manufacturer\x20StateOrProvince','</label>','2770570tekQUw','number','textContent','runtime','Manufacturer\x20AddressLine1','responsiblePersonEU','scanned-header','currentLineIndex','selectedHeaders','Responsible\x20Person\x201\x20Email','false','hasOwnProperty','Responsible\x20Person\x201\x20AddressLine1','1247164zImiwb','Debug\x20mode\x20is\x20now','push','startLineInput','sendMessage','city','addEventListener','country','dataColumns','debugModeSwitch','toString','none','thead','EUResponsiblePerson',',\x20Current\x20line:\x20','filter','set','querySelectorAll','csvUpload','download','row-done','Failed\x20to\x20decode\x20customLabel:\x20','trim','createElement','map','add','1705517ACwHiZ','appliedActions','Processed:\x20','address1','data','restoreFromStorageBtn','Responsible\x20Person\x20EU\x20Text','unprocessedCount','phoneNumber','Responsible\x20Person\x201\x20Phone','confirmHeadersBtn','chineseSeller','Responsible\x20Person\x201\x20Country','CSV\x20appears\x20to\x20be\x20empty\x20or\x20invalid.','actionCheckboxes','mainSection'];a0_0x3385=function(){return _0x1709f6;};return a0_0x3385();}function updateRunTitle(_0x14f777,_0x523213){const _0xe0d2c3=a0_0x34456e,_0x2d6974=countProcessedRows(),_0x45b06d=originalData['length'];document[_0xe0d2c3(0x20c)]=_0xe0d2c3(0x232)+_0x14f777+'/'+_0x523213+_0xe0d2c3(0x20a)+_0x2d6974+'/'+_0x45b06d+_0xe0d2c3(0x1d5)+currentLineIndex;}function countProcessedRows(){const _0x6df97b=a0_0x34456e,_0x1df975=Object[_0x6df97b(0x21c)](actionsConfig)[_0x6df97b(0x1d6)](([_0x4896a9])=>appliedActions[_0x4896a9])['map'](([_0x36d259])=>_0x36d259);if(_0x1df975['length']===0x0)return 0x0;return originalData[_0x6df97b(0x1d6)](_0x4134f3=>{const _0x141c58=_0x6df97b;return _0x1df975[_0x141c58(0x219)](_0x54621c=>{const _0x447d88=_0x141c58,_0x49e669=actionsConfig[_0x54621c][_0x447d88(0x254)];return _0x4134f3[_0x49e669]===!![];});})[_0x6df97b(0x229)];}async function handleRunActions(){const _0x1520c8=a0_0x34456e;if(isProcessing)return;concurrency=parseInt(concurrencyInput[_0x1520c8(0x203)],0xa)||0x1;const _0x58ceca=Array[_0x1520c8(0x23b)](actionCheckboxes['querySelectorAll']('.actionCheck:checked'))[_0x1520c8(0x1df)](_0x1a1f4c=>_0x1a1f4c[_0x1520c8(0x203)]);if(_0x58ceca[_0x1520c8(0x229)]===0x0){alert(_0x1520c8(0x200));return;}let _0x5b9a88=0x0;startLineInput&&(_0x5b9a88=parseInt(startLineInput['value'],0xa)||0x0,_0x5b9a88=Math[_0x1520c8(0x1ff)](0x0,Math[_0x1520c8(0x1f6)](_0x5b9a88,originalData[_0x1520c8(0x229)]-0x1)),currentLineIndex=_0x5b9a88,await saveState());isProcessing=!![],statusMessageEl[_0x1520c8(0x1bc)]=_0x1520c8(0x1f1);for(const _0x2200ed of _0x58ceca){if(!appliedActions[_0x2200ed]){appliedActions[_0x2200ed]=!![];const _0xa10492=actionsConfig[_0x2200ed];for(const _0xb2f436 of originalData){_0xb2f436[_0xa10492['scannedColumn']]=_0xb2f436[_0xa10492[_0x1520c8(0x254)]]||![];}renderTable();}}const _0x73ea8b=[];for(let _0x5deba6=currentLineIndex;_0x5deba6<originalData['length'];_0x5deba6++){const _0x14d040=originalData[_0x5deba6];!isRowProcessed(_0x14d040)&&_0x73ea8b[_0x1520c8(0x1c9)]({'row':_0x14d040,'idx':_0x5deba6});}const _0x15e5f4=_0x73ea8b[_0x1520c8(0x229)];let _0x2dfffe=0x0;updateRunTitle(_0x2dfffe,_0x15e5f4),await runWithConcurrency(_0x73ea8b,async _0x2d1eea=>{const _0x3855cd=_0x1520c8,{row:_0x244597,idx:_0x56762f}=_0x2d1eea;await processAllActionsForRow(_0x244597,_0x58ceca),_0x2dfffe++,currentLineIndex=_0x56762f,await saveState(),startLineInput&&(startLineInput[_0x3855cd(0x203)]=currentLineIndex),updateRunTitle(_0x2dfffe,_0x15e5f4);}),statusMessageEl['textContent']='Processing\x20complete.',isProcessing=![],await saveState(),updateTitleWithProgress();}async function processAllActionsForRow(_0x520878,_0x412eef){for(const _0x2dd764 of _0x412eef){!isRowActionScanned(_0x520878,_0x2dd764)&&await processRowAction(_0x520878,_0x2dd764);}}function isRowActionScanned(_0xfdcc1f,_0x148bf5){const _0x3824b1=actionsConfig[_0x148bf5]['scannedColumn'];return _0xfdcc1f[_0x3824b1]===!![];}async function runWithConcurrency(_0x4ae45c,_0x3c2dca){let _0x343fb3=0x0,_0x1ad5ec=0x0;return new Promise(_0x272a68=>{const _0x51a8ba=async()=>{const _0x4fc9ec=a0_0x3eff;while(_0x1ad5ec<concurrency&&_0x343fb3<_0x4ae45c['length']){const _0x2bc535=_0x4ae45c[_0x343fb3++];_0x1ad5ec++,_0x3c2dca(_0x2bc535)[_0x4fc9ec(0x255)](async()=>{const _0x16ca8e=_0x4fc9ec;_0x1ad5ec--,updateProgressUI(),await saveState(),_0x343fb3===_0x4ae45c[_0x16ca8e(0x229)]&&_0x1ad5ec===0x0?_0x272a68():_0x51a8ba();});}};_0x51a8ba();});}function parseManufacturerText(_0x3cb404){const _0x2e2e41=a0_0x34456e;if(!_0x3cb404||_0x3cb404['trim']()==='')return{};const _0x2d9518=parseFirstAddress(_0x3cb404);return{'Manufacturer\x20Name':_0x2d9518['name']??null,'Manufacturer\x20AddressLine1':_0x2d9518[_0x2e2e41(0x1e4)]??null,'Manufacturer\x20AddressLine2':_0x2d9518['address2']??null,'Manufacturer\x20City':_0x2d9518[_0x2e2e41(0x1cc)]??null,'Manufacturer\x20Country':_0x2d9518['country']??null,'Manufacturer\x20PostalCode':_0x2d9518[_0x2e2e41(0x210)]??null,'Manufacturer\x20StateOrProvince':_0x2d9518['stateOrProvince']??null,'Manufacturer\x20Phone':_0x2d9518[_0x2e2e41(0x1e9)]??null,'Manufacturer\x20Email':_0x2d9518[_0x2e2e41(0x213)]??null};}function parseEUResponsibleText(_0xa3a961){const _0x29290f=a0_0x34456e;if(!_0xa3a961||_0xa3a961[_0x29290f(0x1dd)]()==='')return{};const _0x23ad02=parseFirstAddress(_0xa3a961);return{'Responsible\x20Person\x201':_0x23ad02[_0x29290f(0x1fa)]??null,'Responsible\x20Person\x201\x20Type':'EUResponsiblePerson','Responsible\x20Person\x201\x20AddressLine1':_0x23ad02[_0x29290f(0x1e4)]??null,'Responsible\x20Person\x201\x20AddressLine2':_0x23ad02[_0x29290f(0x21f)]??null,'Responsible\x20Person\x201\x20City':_0x23ad02['city']??null,'Responsible\x20Person\x201\x20Country':_0x23ad02[_0x29290f(0x1ce)]??null,'Responsible\x20Person\x201\x20PostalCode':_0x23ad02[_0x29290f(0x210)]??null,'Responsible\x20Person\x201\x20StateOrProvince':_0x23ad02['stateOrProvince']??null,'Responsible\x20Person\x201\x20Phone':_0x23ad02['phoneNumber']??null,'Responsible\x20Person\x201\x20Email':_0x23ad02['emailAddress']??null};}async function processRowAction(_0x324ddc,_0x1f2824){const _0x140664=a0_0x34456e;if(isRowActionScanned(_0x324ddc,_0x1f2824))return;const _0x2f09ef=actionsConfig[_0x1f2824],_0x192855=_0x324ddc[_0x140664(0x1fd)]||'';try{if(_0x1f2824==='gspr'){const _0x4d36f8=_0x324ddc['Manufacturer\x20Info\x20Text']??null,_0x295647=_0x324ddc[_0x140664(0x1e7)]??null;let _0x3afb4a={};if(_0x4d36f8||_0x295647){const _0xc6ea9b=parseManufacturerText(_0x4d36f8),_0x34c7b2=parseEUResponsibleText(_0x295647);_0x3afb4a={..._0xc6ea9b,..._0x34c7b2};let _0xb0032f=!![];_0x4d36f8&&(_0xc6ea9b[_0x140664(0x245)]===null||_0xc6ea9b['Manufacturer\x20Country']===null)&&(_0xb0032f=![]),_0x295647&&(_0x34c7b2['Responsible\x20Person\x201']===null||_0x34c7b2[_0x140664(0x1ed)]===null)&&(_0xb0032f=![]),_0x2f09ef[_0x140664(0x1cf)][_0x140664(0x1f5)](_0x5b91b6=>{_0x324ddc[_0x5b91b6]=_0x3afb4a[_0x5b91b6]??_0x324ddc[_0x5b91b6]??null;}),_0x324ddc[_0x2f09ef[_0x140664(0x254)]]=_0xb0032f;}else{const _0x14fee2=await getGSPR(_0x192855);let _0x3e329f=!![];(_0x14fee2[_0x140664(0x245)]===null||_0x14fee2['Manufacturer\x20Country']===null)&&(_0x3e329f=![]),_0x14fee2[_0x140664(0x258)]!==null&&(_0x14fee2[_0x140664(0x258)]===null||_0x14fee2[_0x140664(0x1ed)]===null)&&(_0x3e329f=![]),_0x2f09ef[_0x140664(0x1cf)][_0x140664(0x1f5)](_0x37274a=>{_0x324ddc[_0x37274a]=_0x14fee2[_0x37274a]??null;}),_0x324ddc[_0x2f09ef['scannedColumn']]=_0x3e329f;}}else{if(_0x1f2824===_0x140664(0x1ec)){const _0x4f0fae=await checkIfSellerChinese(_0x192855);_0x2f09ef[_0x140664(0x1cf)]['forEach'](_0x214f32=>{_0x324ddc[_0x214f32]=_0x4f0fae[_0x214f32]??'';}),_0x324ddc[_0x2f09ef[_0x140664(0x254)]]=!![];}else _0x2f09ef['dataColumns'][_0x140664(0x1f5)](_0x43a8f8=>{const _0x4192b5=_0x140664;_0x324ddc[_0x43a8f8]=_0x4192b5(0x24e);}),_0x324ddc[_0x2f09ef[_0x140664(0x254)]]=!![];}}catch(_0x5746dc){console['error'](_0x140664(0x230),_0x1f2824,_0x5746dc);if(debugMode)throw _0x5746dc;else console[_0x140664(0x21d)](_0x140664(0x208),_0x5746dc[_0x140664(0x1f8)]);}await saveState();}async function getGSPR(_0x3f41d2){const _0x11c0ef=a0_0x34456e;let _0x208bdf;try{_0x208bdf=atob(_0x3f41d2);}catch(_0x2b46c7){throw new Error(_0x11c0ef(0x1dc)+_0x3f41d2);}const {domain:_0x2d1f37}=await chrome[_0x11c0ef(0x22a)][_0x11c0ef(0x25a)][_0x11c0ef(0x24f)](_0x11c0ef(0x20e))||{};if(!_0x2d1f37)throw new Error(_0x11c0ef(0x226));const _0x11c79a='https://www.amazon.'+_0x2d1f37+_0x11c0ef(0x24b)+_0x208bdf+_0x11c0ef(0x222);console[_0x11c0ef(0x22f)]('Fetching\x20GSPR\x20data\x20for\x20URL:\x20'+_0x11c79a);let _0x515e46;try{_0x515e46=await new Promise(_0x559770=>{const _0x4bc6f2=_0x11c0ef;chrome[_0x4bc6f2(0x1bd)][_0x4bc6f2(0x1cb)]({'type':_0x4bc6f2(0x253),'url':_0x11c79a},function(_0x19e499){_0x559770(_0x19e499);});});}catch(_0x3e78b7){throw new Error(_0x11c0ef(0x22d)+_0x3e78b7[_0x11c0ef(0x1f8)]);}if(!_0x515e46||!_0x515e46[_0x11c0ef(0x209)])throw new Error('GSPR\x20data\x20not\x20found\x20for\x20SKU:\x20'+_0x3f41d2);const _0x2c32fa=_0x515e46[_0x11c0ef(0x209)],_0x27dfb2=_0x2c32fa[_0x11c0ef(0x1b3)]||{},_0x3bc5d6=_0x2c32fa[_0x11c0ef(0x1bf)]||{};var _0x25779e=_0x2c32fa[_0x11c0ef(0x23e)],_0x89837a=_0x2c32fa[_0x11c0ef(0x206)];return{'Manufacturer\x20Name':_0x27dfb2[_0x11c0ef(0x1fa)]??null,'Manufacturer\x20AddressLine1':_0x27dfb2[_0x11c0ef(0x1e4)]??null,'Manufacturer\x20AddressLine2':_0x27dfb2['address2']??null,'Manufacturer\x20City':_0x27dfb2[_0x11c0ef(0x1cc)]??null,'Manufacturer\x20Country':_0x27dfb2[_0x11c0ef(0x1ce)]??null,'Manufacturer\x20PostalCode':_0x27dfb2['postalCode']??null,'Manufacturer\x20StateOrProvince':_0x27dfb2['stateOrProvince']??null,'Manufacturer\x20Phone':_0x27dfb2[_0x11c0ef(0x1e9)]??null,'Manufacturer\x20Email':_0x27dfb2[_0x11c0ef(0x213)]??null,'Responsible\x20Person\x201':_0x3bc5d6['name']??null,'Responsible\x20Person\x201\x20Type':_0x11c0ef(0x1d4),'Responsible\x20Person\x201\x20AddressLine1':_0x3bc5d6[_0x11c0ef(0x1e4)]??null,'Responsible\x20Person\x201\x20AddressLine2':_0x3bc5d6[_0x11c0ef(0x21f)]??null,'Responsible\x20Person\x201\x20City':_0x3bc5d6[_0x11c0ef(0x1cc)]??null,'Responsible\x20Person\x201\x20Country':_0x3bc5d6[_0x11c0ef(0x1ce)]??null,'Responsible\x20Person\x201\x20PostalCode':_0x3bc5d6[_0x11c0ef(0x210)]??null,'Responsible\x20Person\x201\x20StateOrProvince':_0x3bc5d6['stateOrProvince']??null,'Responsible\x20Person\x201\x20Phone':_0x3bc5d6['phoneNumber']??null,'Responsible\x20Person\x201\x20Email':_0x3bc5d6[_0x11c0ef(0x213)]??null,'Manufacturer\x20Info\x20Text':_0x25779e??null,'Responsible\x20Person\x20EU\x20Text':_0x89837a??null};}async function checkIfSellerChinese(_0xd84143){const _0x28f285=a0_0x34456e;await new Promise(_0x573172=>setTimeout(_0x573172,0x32));const _0x4ea26b=Math[_0x28f285(0x240)]()>0.5?_0x28f285(0x22c):_0x28f285(0x1c4);return{'Chinese\x20Seller':_0x4ea26b};}