{"model": "gpt-4o-mini", "system_message": "You are a product categorization system. Given a product title and a main category, determine the subcategory from the provided list that best fits the item.\n\nMain Category: Women.\nSubcategories: Accessories, Bags, Dresses, Intimates & Sleepwear, Jackets & Coats, Jeans, Jewelry, Makeup, Pants & Jumpsuits, Shoes, Shorts, Skirts, Sweaters, Swim, Tops, Skincare, Hair, Bath & Body, Global & Traditional Wear, Other.", "function_schema": {"name": "get_subcategory", "description": "The input is a product title. The output is the subcategory from the provided list that best describes the item.", "parameters": {"type": "object", "properties": {"output": {"type": "string", "enum": ["Accessories", "Bags", "Dresses", "Intimates & Sleepwear", "Jackets & Coats", "<PERSON><PERSON>", "Jewelry", "Makeup", "Pants & Jumpsuits", "Shoes", "Shorts", "Skirts", "Sweaters", "Swim", "Tops", "Skincare", "Hair", "Bath & Body", "Global & Traditional Wear", "Other"]}}, "required": ["output"]}}, "example_user_input": "Vintage Denim Jacket", "example_assistant_output_arguments": {"output": "Jackets & Coats"}, "user_input": "Vintage Denim Jacket"}