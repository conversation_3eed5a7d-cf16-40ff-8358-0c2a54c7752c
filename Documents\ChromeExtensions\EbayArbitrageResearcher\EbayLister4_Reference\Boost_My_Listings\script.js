var a0_0x4c6a09=a0_0x3293;(function(_0x5ac591,_0x18fbeb){var _0x43f37a=a0_0x3293,_0x384d98=_0x5ac591();while(!![]){try{var _0xf76836=-parseInt(_0x43f37a(0xf9))/0x1+-parseInt(_0x43f37a(0xfa))/0x2*(-parseInt(_0x43f37a(0x146))/0x3)+-parseInt(_0x43f37a(0x10b))/0x4+-parseInt(_0x43f37a(0x135))/0x5*(-parseInt(_0x43f37a(0x10a))/0x6)+-parseInt(_0x43f37a(0x123))/0x7+parseInt(_0x43f37a(0x112))/0x8*(parseInt(_0x43f37a(0x127))/0x9)+parseInt(_0x43f37a(0x120))/0xa;if(_0xf76836===_0x18fbeb)break;else _0x384d98['push'](_0x384d98['shift']());}catch(_0x27df27){_0x384d98['push'](_0x384d98['shift']());}}}(a0_0x4ed7,0x2d0a2),console[a0_0x4c6a09(0xd9)](a0_0x4c6a09(0x12e)),chrome[a0_0x4c6a09(0xde)][a0_0x4c6a09(0xe2)][a0_0x4c6a09(0x11b)](function(_0x24d1c8,_0x10f9ce,_0x4f38dc){var _0x40017e=a0_0x4c6a09;return console[_0x40017e(0xd9)]('message\x20received\x20in\x20script.js',_0x24d1c8),_0x24d1c8[_0x40017e(0xd6)]==_0x40017e(0x126)&&(console['log'](_0x40017e(0xf4)),updateProgress(_0x24d1c8[_0x40017e(0xdd)],_0x24d1c8['message'])),_0x24d1c8[_0x40017e(0xd6)]==_0x40017e(0x116)&&(console['log'](_0x40017e(0x14a)),restartSellSimilarTab()),_0x24d1c8[_0x40017e(0xd6)]==_0x40017e(0xe5)&&(console[_0x40017e(0xd9)](_0x40017e(0xe6)),startBoostMyListings()),!![];}));function a0_0x3293(_0x4bf7cf,_0x13e569){var _0x4ed747=a0_0x4ed7();return a0_0x3293=function(_0x3293de,_0x541090){_0x3293de=_0x3293de-0xd3;var _0x3d6547=_0x4ed747[_0x3293de];return _0x3d6547;},a0_0x3293(_0x4bf7cf,_0x13e569);}async function startBoostMyListings(){var _0x3827b3=a0_0x4c6a09,_0x535658=await checkMembership();console[_0x3827b3(0xd9)](_0x3827b3(0x151),_0x535658);if(_0x535658!='ultimate'){alert(_0x3827b3(0x148)),endAndSellSimilarButton[_0x3827b3(0xf0)]=![];return;}var {sellSimilarScheduleAutomation:_0x2d8f0b}=await chrome['storage'][_0x3827b3(0xd8)][_0x3827b3(0x104)](_0x3827b3(0x110)),{reviseListingScheduleAutomation:_0x4ea9f6}=await chrome['storage'][_0x3827b3(0xd8)][_0x3827b3(0x104)](_0x3827b3(0x12a));_0x2d8f0b&&(document[_0x3827b3(0x134)](_0x3827b3(0xd5))[_0x3827b3(0xf0)]=!![],await endAndSellSimilar(),document['getElementById']('end_and_sell_similar_button')[_0x3827b3(0xf0)]=![]),_0x4ea9f6&&(document['getElementById']('bulk_revise_listing_button')[_0x3827b3(0xf0)]=!![],await bulkReviseListings(),document[_0x3827b3(0x134)]('bulk_revise_listing_button')['disabled']=![]);}async function restartSellSimilarTab(){var _0x38dfbb=a0_0x4c6a09;console[_0x38dfbb(0xd9)](_0x38dfbb(0x11e)),await new Promise(_0x459d51=>setTimeout(_0x459d51,0x7d0)),updateProgress(0x0,_0x38dfbb(0xff)),await new Promise(_0xf6b53e=>setTimeout(_0xf6b53e,0x7d0)),document[_0x38dfbb(0x134)](_0x38dfbb(0xd5))[_0x38dfbb(0xfb)]();}var endAndSellSimilarButton=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0xd5));endAndSellSimilarButton[a0_0x4c6a09(0x14b)]('click',async function(){var _0x1fff18=a0_0x4c6a09;endAndSellSimilarButton[_0x1fff18(0xf0)]=!![];var _0x3d2cb2=await checkMembership();console[_0x1fff18(0xd9)](_0x1fff18(0x151),_0x3d2cb2);if(_0x3d2cb2!='ultimate'){alert(_0x1fff18(0x148)),endAndSellSimilarButton[_0x1fff18(0xf0)]=![];return;}console['log'](_0x1fff18(0xed));;await endAndSellSimilar(),endAndSellSimilarButton[_0x1fff18(0xf0)]=![];});async function endAndSellSimilar(){var _0x54b0fb=a0_0x4c6a09,_0x5c8d0b=document[_0x54b0fb(0x134)]('minSold')[_0x54b0fb(0x152)],_0x13b05f=document[_0x54b0fb(0x134)](_0x54b0fb(0xf8))[_0x54b0fb(0x152)],_0x2e83e5=document['getElementById'](_0x54b0fb(0x140))[_0x54b0fb(0x152)];updateProgress(0x0,_0x54b0fb(0xe1));var {response:_0x328a88}=await new Promise(_0x585c14=>{var _0x4b96bc=_0x54b0fb;chrome['runtime'][_0x4b96bc(0x121)]({'type':_0x4b96bc(0x101),'minSold':_0x5c8d0b,'minViews':_0x13b05f,'filterByHours':_0x2e83e5},function(_0x2de440){var _0xd406a7=_0x4b96bc;console[_0xd406a7(0xd9)](_0x2de440),_0x585c14(_0x2de440);});});updateProgress(0x64,_0x54b0fb(0x100)+_0x328a88[_0x54b0fb(0xe7)]);var {autoRepeatSellSimilarTab:_0x5d4101}=await chrome['storage'][_0x54b0fb(0xd8)]['get'](_0x54b0fb(0x132));_0x328a88['status']['includes'](_0x54b0fb(0x113))&&_0x5d4101&&(await new Promise(_0x4cc21b=>setTimeout(_0x4cc21b,0x7d0)),updateProgress(0x0,'0%\x20Restarting\x20until\x20no\x20more\x20low\x20performing\x20items'),await new Promise(_0x210532=>setTimeout(_0x210532,0x7d0)),await endAndSellSimilar());}function updateProgress(_0x2dc6cf,_0x3f216c){var _0xf0958e=a0_0x4c6a09;const _0x47222c=document['getElementById'](_0xf0958e(0x14c)),_0x5f2ecc=document['getElementById'](_0xf0958e(0x122));_0x47222c[_0xf0958e(0x138)][_0xf0958e(0x103)]=_0x2dc6cf+'%',_0x5f2ecc['textContent']=_0x3f216c,addLogEntry(_0x2dc6cf,_0x3f216c),_0x2dc6cf>=0x64&&(endAndSellSimilarButton[_0xf0958e(0xf0)]=![]);}var minSoldInput=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0xe0));minSoldInput[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x1e38e2=a0_0x4c6a09;await chrome[_0x1e38e2(0x143)][_0x1e38e2(0xd8)][_0x1e38e2(0x145)]({'minSoldQuantity':minSoldInput[_0x1e38e2(0x152)]});});var minViewInput=document['getElementById']('minViews');minViewInput[a0_0x4c6a09(0x14b)]('change',async function(){var _0x51b8b2=a0_0x4c6a09;await chrome[_0x51b8b2(0x143)]['local'][_0x51b8b2(0x145)]({'minViewCount':minViewInput['value']});});var timeLeftInput=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0x140));timeLeftInput[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x1f9337=a0_0x4c6a09;await chrome[_0x1f9337(0x143)][_0x1f9337(0xd8)]['set']({'filterByHours':timeLeftInput[_0x1f9337(0x152)]});}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)](['minSoldQuantity',a0_0x4c6a09(0xf2),'filterByHours'],function(_0x4650e0){var _0x2029e0=a0_0x4c6a09;console[_0x2029e0(0xd9)]('Value\x20currently\x20is\x20'+_0x4650e0[_0x2029e0(0x125)]),_0x4650e0[_0x2029e0(0x125)]?minSoldInput[_0x2029e0(0x152)]=_0x4650e0[_0x2029e0(0x125)]:chrome['storage'][_0x2029e0(0xd8)]['set']({'minSoldQuantity':minSoldInput[_0x2029e0(0x152)]}),_0x4650e0['minViewCount']?minViewInput['value']=_0x4650e0['minViewCount']:chrome['storage'][_0x2029e0(0xd8)]['set']({'minViewCount':minViewInput[_0x2029e0(0x152)]}),_0x4650e0['filterByHours']?timeLeftInput['value']=_0x4650e0[_0x2029e0(0x14f)]:chrome[_0x2029e0(0x143)][_0x2029e0(0xd8)]['set']({'filterByHours':timeLeftInput[_0x2029e0(0x152)]});});var autoCloseToggle=document[a0_0x4c6a09(0x134)]('autoCloseToggle');autoCloseToggle[a0_0x4c6a09(0x14b)]('change',async function(){var _0x3cab4b=a0_0x4c6a09;console['log']('autoCloseToggle',autoCloseToggle[_0x3cab4b(0xd7)]),await chrome[_0x3cab4b(0x143)][_0x3cab4b(0xd8)][_0x3cab4b(0x145)]({'autoCloseSellSimilarTab':autoCloseToggle[_0x3cab4b(0xd7)]});}),chrome['storage'][a0_0x4c6a09(0xd8)]['get']([a0_0x4c6a09(0xfe)],function(_0x3019ce){var _0x197fa6=a0_0x4c6a09;console[_0x197fa6(0xd9)](_0x197fa6(0x137)+_0x3019ce[_0x197fa6(0xfe)]),_0x3019ce['autoCloseSellSimilarTab']==undefined?(console[_0x197fa6(0xd9)](_0x197fa6(0xf3)),chrome['storage'][_0x197fa6(0xd8)][_0x197fa6(0x145)]({'autoCloseSellSimilarTab':autoCloseToggle['checked']})):(console[_0x197fa6(0xd9)](_0x197fa6(0x111)),autoCloseToggle['checked']=_0x3019ce[_0x197fa6(0xfe)]);});var autoRepeatToggle=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0xe4));autoRepeatToggle[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x497969=a0_0x4c6a09;await chrome[_0x497969(0x143)][_0x497969(0xd8)]['set']({'autoRepeatSellSimilarTab':autoRepeatToggle[_0x497969(0xd7)]});}),chrome[a0_0x4c6a09(0x143)]['local'][a0_0x4c6a09(0x104)](['autoRepeatSellSimilarTab'],function(_0x33f662){var _0x46e50f=a0_0x4c6a09;console[_0x46e50f(0xd9)](_0x46e50f(0x136)+_0x33f662[_0x46e50f(0x132)]),_0x33f662[_0x46e50f(0x132)]==undefined?chrome[_0x46e50f(0x143)][_0x46e50f(0xd8)][_0x46e50f(0x145)]({'autoRepeatSellSimilarTab':autoRepeatToggle[_0x46e50f(0xd7)]}):autoRepeatToggle[_0x46e50f(0xd7)]=_0x33f662[_0x46e50f(0x132)];});var timeInput=document['getElementById'](a0_0x4c6a09(0x149)),countdownDisplay=document[a0_0x4c6a09(0x134)]('countdown'),countdownInterval=null;function updateCountdown(){var _0x29bf74=a0_0x4c6a09;chrome[_0x29bf74(0x143)][_0x29bf74(0xd8)][_0x29bf74(0x104)]([_0x29bf74(0x12f)],function(_0x212b5d){var _0xc7e8bb=_0x29bf74;const _0x315196=_0x212b5d[_0xc7e8bb(0x12f)]?parseInt(_0x212b5d['scheduledInterval']):0x18;if(!document[_0xc7e8bb(0x134)](_0xc7e8bb(0x118))[_0xc7e8bb(0xd7)]){document[_0xc7e8bb(0x134)](_0xc7e8bb(0xf5))[_0xc7e8bb(0x109)]=_0xc7e8bb(0x141),clearInterval(countdownInterval);return;}const _0xc2b15e=new Date(),_0x1e3f5a=document[_0xc7e8bb(0x134)](_0xc7e8bb(0x149))[_0xc7e8bb(0x152)],_0x2524cd=_0x1e3f5a[_0xc7e8bb(0x129)](':'),_0x195315=parseInt(_0x2524cd[0x0],0xa),_0xcf77e5=parseInt(_0x2524cd[0x1],0xa);let _0x1fae88=new Date(_0xc2b15e[_0xc7e8bb(0xfd)](),_0xc2b15e[_0xc7e8bb(0x108)](),_0xc2b15e['getDate'](),_0x195315,_0xcf77e5);while(_0x1fae88<=_0xc2b15e){_0x1fae88=new Date(_0x1fae88[_0xc7e8bb(0x11f)]()+_0x315196*0x36ee80);}const _0x488d62=_0x1fae88-_0xc2b15e,_0x4cc784=Math[_0xc7e8bb(0xef)](_0x488d62/0x36ee80),_0x3d9618=Math[_0xc7e8bb(0xef)](_0x488d62%0x36ee80/0xea60),_0xcfdd44=Math[_0xc7e8bb(0xef)](_0x488d62%0xea60/0x3e8);document['getElementById']('countdown')[_0xc7e8bb(0x109)]=_0x4cc784+'h\x20'+_0x3d9618+'m\x20'+_0xcfdd44+'s';});}document[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x139),function(){function _0x31f3c2(){var _0x3fc374=a0_0x3293;const _0x592a8d=new Date();document[_0x3fc374(0x134)]('current_time')[_0x3fc374(0x109)]=_0x592a8d[_0x3fc374(0x13b)]();}setInterval(_0x31f3c2,0x3e8),updateCountdown(),countdownInterval=setInterval(updateCountdown,0x3e8);});var scheduleToggle=document['getElementById'](a0_0x4c6a09(0x118));scheduleToggle[a0_0x4c6a09(0x14b)]('change',function(){var _0x38a813=a0_0x4c6a09;chrome[_0x38a813(0x143)][_0x38a813(0xd8)][_0x38a813(0x145)]({'scheduleSellSimilar':scheduleToggle[_0x38a813(0xd7)]});var _0x366adf=document[_0x38a813(0x134)](_0x38a813(0xd4));scheduleToggle['checked']?(_0x366adf[_0x38a813(0x138)][_0x38a813(0x14e)]='block',countdownInterval=setInterval(updateCountdown,0x3e8),scheduleAlarm(scheduledTimeInput[_0x38a813(0x152)],_0x38a813(0x114),scheduledIntervalInput[_0x38a813(0x152)])):(_0x366adf[_0x38a813(0x138)][_0x38a813(0x14e)]=_0x38a813(0x10d),clearInterval(countdownInterval),removePeriodicAlarm(_0x38a813(0x114)));}),chrome[a0_0x4c6a09(0x143)]['local'][a0_0x4c6a09(0x104)]([a0_0x4c6a09(0xec)],function(_0x416ac7){var _0x2c5178=a0_0x4c6a09;console[_0x2c5178(0xd9)]('Value\x20currently\x20is\x20'+_0x416ac7[_0x2c5178(0xec)]),scheduleToggle['checked']=_0x416ac7[_0x2c5178(0xec)]||![];var _0x4a2877=document[_0x2c5178(0x134)](_0x2c5178(0xd4));_0x416ac7['scheduleSellSimilar']?_0x4a2877[_0x2c5178(0x138)]['display']=_0x2c5178(0x13a):_0x4a2877['style'][_0x2c5178(0x14e)]='none';});var scheduledTimeInput=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0x149));scheduledTimeInput['addEventListener'](a0_0x4c6a09(0x12b),function(){var _0x32ac33=a0_0x4c6a09;chrome[_0x32ac33(0x143)][_0x32ac33(0xd8)][_0x32ac33(0x145)]({'scheduledTimeSellSimilar':scheduledTimeInput[_0x32ac33(0x152)]}),scheduleToggle[_0x32ac33(0xd7)]&&scheduleAlarm(scheduledTimeInput[_0x32ac33(0x152)],_0x32ac33(0x114),scheduledIntervalInput['value']);}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)]([a0_0x4c6a09(0x13d)],function(_0xb312d9){var _0x9dd05f=a0_0x4c6a09;console['log'](_0x9dd05f(0x136)+_0xb312d9[_0x9dd05f(0x13d)]),scheduledTimeInput['value']=_0xb312d9[_0x9dd05f(0x13d)]||_0x9dd05f(0x124);});function removePeriodicAlarm(_0x508d7e){var _0x54090c=a0_0x4c6a09;chrome[_0x54090c(0xde)]['sendMessage']({'type':'removePeriodicAlarm','alarmName':_0x508d7e});}function scheduleAlarm(_0x1c1f1e,_0x2fad35,_0x35c5ae=0x18){var _0x2d1fad=a0_0x4c6a09,_0x54dbf4=_0x35c5ae*0x3c;const _0x274700=_0x1c1f1e[_0x2d1fad(0x129)](':'),_0x1b4d4e=parseInt(_0x274700[0x0],0xa),_0xc12b18=parseInt(_0x274700[0x1],0xa),_0x43394e=new Date();let _0x5956f5=new Date(_0x43394e[_0x2d1fad(0xfd)](),_0x43394e[_0x2d1fad(0x108)](),_0x43394e['getDate'](),_0x1b4d4e,_0xc12b18);_0x5956f5<=_0x43394e&&_0x5956f5['setDate'](_0x5956f5[_0x2d1fad(0x10f)]()+0x1);const _0x4a6279=(_0x5956f5-_0x43394e)/0xea60;chrome[_0x2d1fad(0xde)][_0x2d1fad(0x121)]({'type':_0x2d1fad(0x105),'alarmName':_0x2fad35,'when':_0x5956f5['getTime'](),'periodInMinutes':_0x54dbf4});}document[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x139),function(){var _0x42c249=a0_0x4c6a09;const _0x198ae0=document[_0x42c249(0xdb)](_0x42c249(0xda)),_0x9a6b32=document[_0x42c249(0xdb)](_0x42c249(0xe8)),_0x2a07a5=document[_0x42c249(0xdb)](_0x42c249(0x142));_0x198ae0['forEach'](_0x261bb3=>{var _0x37a905=_0x42c249;_0x261bb3[_0x37a905(0x14b)](_0x37a905(0xfb),()=>{var _0x4e17dc=_0x37a905;const _0x3ef720=document[_0x4e17dc(0x11a)](_0x261bb3[_0x4e17dc(0xf1)](_0x4e17dc(0x107)));_0x3ef720['style'][_0x4e17dc(0x14e)]=_0x4e17dc(0x13a);});}),_0x9a6b32['forEach'](_0x50734d=>{var _0x1997c5=_0x42c249;_0x50734d[_0x1997c5(0x14b)](_0x1997c5(0xfb),()=>{var _0x29083b=_0x1997c5;const _0x27d97c=_0x50734d[_0x29083b(0x131)](_0x29083b(0x142));_0x27d97c[_0x29083b(0x138)][_0x29083b(0x14e)]='none';});}),window[_0x42c249(0x14b)]('click',_0x1fdd60=>{var _0x471f46=_0x42c249;_0x1fdd60[_0x471f46(0x130)][_0x471f46(0xdf)]===_0x471f46(0x10e)&&(_0x1fdd60[_0x471f46(0x130)][_0x471f46(0x138)][_0x471f46(0x14e)]=_0x471f46(0x10d));});}),document[a0_0x4c6a09(0x14b)]('DOMContentLoaded',async function(){var _0x285085=a0_0x4c6a09,{domain:_0x137305}=await chrome['storage'][_0x285085(0xd8)][_0x285085(0x104)]([_0x285085(0xee)]),_0xb66335=_0x285085(0xeb)+_0x137305+_0x285085(0xf6);document[_0x285085(0x134)](_0x285085(0x147))[_0x285085(0x150)]=_0xb66335;var _0x185f3c=_0x285085(0xeb)+_0x137305+_0x285085(0x106);document[_0x285085(0x134)](_0x285085(0x117))['href']=_0x185f3c;});var bulkReviseListingButton=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0xf7));bulkReviseListingButton['addEventListener'](a0_0x4c6a09(0xfb),async function(){var _0x59b61e=a0_0x4c6a09,_0xd2deb1=await checkMembership();console['log'](_0x59b61e(0x151),_0xd2deb1);if(_0xd2deb1!='ultimate'){alert(_0x59b61e(0x148));return;}bulkReviseListingButton[_0x59b61e(0xf0)]=!![],await bulkReviseListings(),bulkReviseListingButton[_0x59b61e(0xf0)]=![];});async function changeOffersDropdown(){var _0x84f225=a0_0x4c6a09,_0xd5b22a=document[_0x84f225(0x134)](_0x84f225(0x13c)),_0x1de5c6=_0xd5b22a[_0x84f225(0x152)];_0x1de5c6==0x0?_0xd5b22a[_0x84f225(0x152)]=0x1:_0xd5b22a[_0x84f225(0x152)]=0x0,_0xd5b22a['dispatchEvent'](new Event(_0x84f225(0x12b)));}async function bulkReviseListings(){var _0x48a7b6=a0_0x4c6a09;updateProgress(0x0,_0x48a7b6(0x133));var {response:_0x541ecc}=await new Promise(_0x968021=>{var _0x2c3fa6=_0x48a7b6;chrome[_0x2c3fa6(0xde)][_0x2c3fa6(0x121)]({'type':_0x2c3fa6(0xea)},function(_0x1da3b7){var _0x52e185=_0x2c3fa6;console[_0x52e185(0xd9)](_0x1da3b7),_0x968021(_0x1da3b7);});});console[_0x48a7b6(0xd9)]('response',_0x541ecc);_0x541ecc[_0x48a7b6(0x119)](_0x48a7b6(0x12d))?updateProgress(0x64,_0x48a7b6(0x144)):updateProgress(0x64,'100%\x20'+_0x541ecc);var {switchOffer:_0x107b50}=await chrome[_0x48a7b6(0x143)][_0x48a7b6(0xd8)]['get'](_0x48a7b6(0x12c));_0x107b50&&await changeOffersDropdown();}var offersDropdown=document['getElementById'](a0_0x4c6a09(0x13c));offersDropdown['addEventListener']('change',async function(){var _0x2901d9=a0_0x4c6a09;await chrome['storage'][_0x2901d9(0xd8)][_0x2901d9(0x145)]({'offersDropdownOption':offersDropdown[_0x2901d9(0x152)]});}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)]([a0_0x4c6a09(0x11c)],function(_0x1e8155){var _0x191f3f=a0_0x4c6a09;console[_0x191f3f(0xd9)]('Value\x20currently\x20is\x20'+_0x1e8155[_0x191f3f(0x11c)]),offersDropdown[_0x191f3f(0x152)]=_0x1e8155[_0x191f3f(0x11c)]||0x0;});var logs=[];function addLogEntry(_0x1d0721,_0x2a9e2f){var _0x924d4f=a0_0x4c6a09;const _0x54dcab=new Date(),_0x369703=_0x54dcab[_0x924d4f(0x13b)]();logs['push'](_0x369703+':\x20'+_0x1d0721+'%\x20-\x20'+_0x2a9e2f),displayLogs();}function displayLogs(){var _0xde0fe9=a0_0x4c6a09;const _0x427b19=document[_0xde0fe9(0x134)](_0xde0fe9(0xe3));_0x427b19['innerHTML']=logs[_0xde0fe9(0xdc)](_0x4dd969=>_0xde0fe9(0x13f)+_0x4dd969+_0xde0fe9(0xe9))[_0xde0fe9(0x115)]('');}document['addEventListener']('DOMContentLoaded',function(){var _0x40f5ba=a0_0x4c6a09;const _0x52b1dd=document[_0x40f5ba(0x11a)](_0x40f5ba(0x14d)),_0x112a33=document[_0x40f5ba(0x11a)]('#log_modal'),_0x21f1f1=_0x112a33[_0x40f5ba(0x11a)]('.close');_0x52b1dd['addEventListener'](_0x40f5ba(0xfb),()=>{var _0xb4207=_0x40f5ba;_0x112a33['style'][_0xb4207(0x14e)]='block';}),_0x21f1f1[_0x40f5ba(0x14b)](_0x40f5ba(0xfb),()=>{var _0x3a7543=_0x40f5ba;_0x112a33[_0x3a7543(0x138)][_0x3a7543(0x14e)]=_0x3a7543(0x10d);}),window[_0x40f5ba(0x14b)](_0x40f5ba(0xfb),_0x50f15c=>{var _0x396da5=_0x40f5ba;_0x50f15c[_0x396da5(0x130)]===_0x112a33&&(_0x112a33[_0x396da5(0x138)]['display']=_0x396da5(0x10d));});});var switchOfferToggle=document[a0_0x4c6a09(0x134)]('switchOfferToggle');switchOfferToggle[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x25edd5=a0_0x4c6a09;console[_0x25edd5(0xd9)](_0x25edd5(0x128),switchOfferToggle[_0x25edd5(0xd7)]),await chrome[_0x25edd5(0x143)][_0x25edd5(0xd8)]['set']({'switchOffer':switchOfferToggle['checked']});}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)](['switchOffer'],function(_0x3f3467){var _0x35da6e=a0_0x4c6a09;console['log']('switchOffer\x20Value\x20currently\x20is\x20'+_0x3f3467[_0x35da6e(0x12c)]),switchOfferToggle[_0x35da6e(0xd7)]=_0x3f3467[_0x35da6e(0x12c)],!_0x3f3467[_0x35da6e(0x12c)]&&chrome[_0x35da6e(0x143)]['local'][_0x35da6e(0x145)]({'switchOffer':switchOfferToggle[_0x35da6e(0xd7)]});}),document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0x11d))[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x17f574=a0_0x4c6a09;const _0x41aafc=this[_0x17f574(0xd7)];await chrome[_0x17f574(0x143)][_0x17f574(0xd8)]['set']({'reviseListingScheduleAutomation':_0x41aafc});}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)](a0_0x4c6a09(0x12a),function(_0x5bcae0){var _0x356438=a0_0x4c6a09;const _0x10d781=_0x5bcae0[_0x356438(0x12a)];_0x10d781!==undefined&&(document[_0x356438(0x134)](_0x356438(0x11d))[_0x356438(0xd7)]=_0x10d781),_0x10d781===undefined&&chrome['storage'][_0x356438(0xd8)][_0x356438(0x145)]({'reviseListingScheduleAutomation':![]});}),document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0x13e))[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),async function(){var _0x42605a=a0_0x4c6a09;const _0x116221=this[_0x42605a(0xd7)];await chrome['storage'][_0x42605a(0xd8)][_0x42605a(0x145)]({'sellSimilarScheduleAutomation':_0x116221});}),chrome[a0_0x4c6a09(0x143)][a0_0x4c6a09(0xd8)][a0_0x4c6a09(0x104)]('sellSimilarScheduleAutomation',function(_0x28749b){var _0x231915=a0_0x4c6a09;const _0x2dd134=_0x28749b['sellSimilarScheduleAutomation'];_0x2dd134!==undefined&&(document[_0x231915(0x134)](_0x231915(0x13e))['checked']=_0x2dd134),_0x2dd134===undefined&&chrome[_0x231915(0x143)][_0x231915(0xd8)][_0x231915(0x145)]({'sellSimilarScheduleAutomation':!![]});});var scheduledIntervalInput=document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0x10c));scheduledIntervalInput[a0_0x4c6a09(0x14b)](a0_0x4c6a09(0x12b),function(){var _0xa9fef3=a0_0x4c6a09;chrome[_0xa9fef3(0x143)]['local']['set']({'scheduledInterval':scheduledIntervalInput[_0xa9fef3(0x152)]});}),chrome[a0_0x4c6a09(0x143)]['local'][a0_0x4c6a09(0x104)]([a0_0x4c6a09(0x12f)],function(_0xc2dedb){var _0x41a3b7=a0_0x4c6a09;console[_0x41a3b7(0xd9)](_0x41a3b7(0x136)+_0xc2dedb[_0x41a3b7(0x12f)]),_0xc2dedb['scheduledInterval']&&(scheduledIntervalInput['value']=_0xc2dedb['scheduledInterval']),!_0xc2dedb[_0x41a3b7(0x12f)]&&chrome[_0x41a3b7(0x143)][_0x41a3b7(0xd8)][_0x41a3b7(0x145)]({'scheduledInterval':scheduledIntervalInput[_0x41a3b7(0x152)]});}),document[a0_0x4c6a09(0x134)](a0_0x4c6a09(0xfc))['addEventListener'](a0_0x4c6a09(0xfb),async function(){var _0x5b6ece=a0_0x4c6a09;chrome[_0x5b6ece(0xde)][_0x5b6ece(0x121)]({'type':_0x5b6ece(0x102)},function(_0x262e21){var _0x3ae897=_0x5b6ece;console[_0x3ae897(0xd9)](_0x3ae897(0xd3),_0x262e21);});});function a0_0x4ed7(){var _0x2d5c2a=['autoRepeatToggle','start-boost-my-listings','start-boost-my-listings\x20message\x20received\x20in\x20script.js','status','.modal\x20.close','</li>','bulk-revise-listing-button-clicked','https://www.ebay.','scheduleSellSimilar','end_and_sell_similar_button\x20clicked','domain','floor','disabled','getAttribute','minViewCount','autoCloseSellSimilarTab\x20not\x20found','progress-update\x20message\x20received\x20in\x20script.js','countdown','/sh/lst/active?action=pagination&sort=timeRemaining&limit=200','bulk_revise_listing_button','minViews','236145fAGfwl','2LcvLJf','click','send_offers_button','getFullYear','autoCloseSellSimilarTab','Restarting\x20until\x20no\x20more\x20low\x20performing\x20items','100%\x20','end-and-sell-similar-button-clicked','send-offers-button-clicked','width','get','createOrUpdateAlarm','/sh/lst/ended?status=UNSOLD_NOT_RELISTED&limit=200&sort=-actualEndDate','data-modal-target','getMonth','textContent','1398KNvuJl','566256TKJJAU','scheduled_interval','none','modal','getDate','sellSimilarScheduleAutomation','autoCloseSellSimilarTab\x20found','3008XRjZDW','listings\x20are\x20now\x20live','scheduleBoostAlarm','join','restart-sell-similar-tab','endedListingLink','schedule_toggle','includes','querySelector','addListener','offersDropdownOption','revise_listing_toggle_schedule_automation','restartSellSimilarTab','getTime','4065260KrmIDt','sendMessage','status_message','2569952cTgNvj','00:00','minSoldQuantity','progress-update','3087FblRJC','switchOfferToggle','split','reviseListingScheduleAutomation','change','switchOffer','Error\x20occurred','script.js\x20loaded','scheduledInterval','target','closest','autoRepeatSellSimilarTab','0%\x20Starting\x20to\x20revise\x20listings...','getElementById','5215PXtAoG','Value\x20currently\x20is\x20','autoCloseSellSimilarTab\x20Value\x20currently\x20is\x20','style','DOMContentLoaded','block','toLocaleTimeString','offers_dropdown','scheduledTimeSellSimilar','sell_similar_toggle_schedule_automation','<li>','timeLeft','Not\x20scheduled','.modal','storage','100%\x20Error\x20occurred.\x20Please\x20try\x20again\x20later.','set','452442uAofeh','activeListingLink','You\x20are\x20not\x20a\x20member\x20of\x20the\x20sniper\x20list.\x20Please\x20contact\x20support.','scheduled_time','restart-sell-similar-tab\x20message\x20received\x20in\x20script.js','addEventListener','loading_progress','#log_button','display','filterByHours','href','membership','value','Response\x20from\x20background\x20script:','time_until_schedule','end_and_sell_similar_button','type','checked','local','log','[data-modal-target]','querySelectorAll','map','progress','runtime','className','minSold','0%\x20Attempting\x20to\x20end\x20low\x20performing\x20items...\x20Please\x20wait.','onMessage','log_list'];a0_0x4ed7=function(){return _0x2d5c2a;};return a0_0x4ed7();}