

@keyframes jumpAndBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  20% {
    transform: translateY(-20px) scale(1.2);
  }
  40% {
    transform: translateY(0) scale(1);
  }
  60% {
    transform: translateY(-10px) scale(1.1);
  }
  80% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

.icon {
  transition: transform 0.2s ease;
  display: inline-block;
  cursor: pointer; /* Suggests that the icon is clickable */
}

.icon:hover {
  transform: scale(1.25);
}

.icon.clicked {
  animation: jumpAndBounce 0.8s ease;
}

.a-link-text {
  padding: 3px;

}


/* Always spin while .spinner is active */
.icon.spinner {
  animation: spin 1s linear infinite;
}

/* If you also want the element to scale on hover,
   you must apply scale + rotate together in the keyframes.  */
.icon.spinner:hover {
  animation: spinAndScale 1s linear infinite;
}

/* Keyframes that do both rotation and scale: */
@keyframes spinAndScale {
  0% {
    transform: rotate(0deg) scale(1.25);
  }
  100% {
    transform: rotate(360deg) scale(1.25);
  }
}



.product-links-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* semi-transparent background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it appears on top */
}

.product-links-modal {
  background-color: #fff;
  width: 600px;
  max-width: 90%;
  padding: 20px;
  border-radius: 4px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.modal-button-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  gap: 10px;
}

/* Close and Copy buttons (feel free to customize) */
.close-button,
.copy-button {
  background-color: #ccc;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: bold;
}

.close-button:hover,
.copy-button:hover {
  background-color: #aaa;
}

/* Modal title */
.product-links-modal h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.25rem;
}
