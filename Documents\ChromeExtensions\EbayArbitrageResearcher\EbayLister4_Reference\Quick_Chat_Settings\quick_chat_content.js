var a0_0x26976c=a0_0x1f25;(function(_0x2e70e0,_0x3c38e){var _0x5c244d=a0_0x1f25,_0x4041c0=_0x2e70e0();while(!![]){try{var _0x5b8e5c=-parseInt(_0x5c244d(0x1ff))/0x1*(parseInt(_0x5c244d(0x1fa))/0x2)+-parseInt(_0x5c244d(0x1fe))/0x3*(parseInt(_0x5c244d(0x204))/0x4)+-parseInt(_0x5c244d(0x1e3))/0x5*(parseInt(_0x5c244d(0x1f5))/0x6)+parseInt(_0x5c244d(0x1ed))/0x7*(parseInt(_0x5c244d(0x1fb))/0x8)+-parseInt(_0x5c244d(0x1f7))/0x9*(parseInt(_0x5c244d(0x1e9))/0xa)+parseInt(_0x5c244d(0x20c))/0xb*(parseInt(_0x5c244d(0x203))/0xc)+parseInt(_0x5c244d(0x207))/0xd;if(_0x5b8e5c===_0x3c38e)break;else _0x4041c0['push'](_0x4041c0['shift']());}catch(_0x146b9d){_0x4041c0['push'](_0x4041c0['shift']());}}}(a0_0x4383,0x921fa),console[a0_0x26976c(0x20f)](a0_0x26976c(0x200)));var chatBoxTextArea,currentPage=0x0,buttonsPerPage=0x9,quick_chat_buttons;function updateButtonVisibility(){var _0xb86a0a=a0_0x26976c;const _0x3cfda5=document[_0xb86a0a(0x1f0)]('.quick_chat_button');_0x3cfda5['forEach']((_0x4fc4ed,_0x121318)=>{var _0x5da825=_0xb86a0a;_0x4fc4ed[_0x5da825(0x1f6)][_0x5da825(0x1ec)]=_0x121318>=currentPage*buttonsPerPage&&_0x121318<(currentPage+0x1)*buttonsPerPage?_0x5da825(0x1e2):_0x5da825(0x206);});}function createNavigationButton(_0x336fad){var _0x2cca1d=a0_0x26976c,_0x4609a3=document['createElement'](_0x2cca1d(0x1ee));return _0x4609a3[_0x2cca1d(0x201)]=_0x336fad===_0x2cca1d(0x1e4)?'<':'>',_0x4609a3[_0x2cca1d(0x208)]=_0x2cca1d(0x1d9),_0x4609a3['onclick']=function(_0x274ffc){var _0xa39db6=_0x2cca1d;_0x274ffc[_0xa39db6(0x1da)](),_0x336fad===_0xa39db6(0x1e4)?currentPage=Math['max'](0x0,currentPage-0x1):currentPage=Math[_0xa39db6(0x1d8)](currentPage+0x1,Math[_0xa39db6(0x1e0)](quick_chat_buttons[_0xa39db6(0x205)]/buttonsPerPage)-0x1),updateButtonVisibility();},_0x4609a3;}async function initQuickChatContentButtons(_0x2c46c5,_0x29b369){var _0x2b1eff=a0_0x26976c;chatBoxTextArea=_0x2c46c5;var _0x544ab0=await chrome[_0x2b1eff(0x1e7)]['local']['get'](_0x2b1eff(0x202));quick_chat_buttons=_0x544ab0['quick_chat_buttons'];var _0x3bca1b=document[_0x2b1eff(0x1ea)]('div');_0x3bca1b[_0x2b1eff(0x208)]='main-container';var _0x264566=document[_0x2b1eff(0x1ea)]('div');_0x264566[_0x2b1eff(0x208)]=_0x2b1eff(0x1f4);for(var _0x1e3ef7=0x0;_0x1e3ef7<quick_chat_buttons[_0x2b1eff(0x205)];_0x1e3ef7++){var _0x3c810b=createQuickChatContentButton(quick_chat_buttons[_0x1e3ef7][_0x2b1eff(0x20e)],quick_chat_buttons[_0x1e3ef7][_0x2b1eff(0x1e8)],quick_chat_buttons[_0x1e3ef7][_0x2b1eff(0x20d)]);_0x264566[_0x2b1eff(0x1f2)](_0x3c810b);}var _0xbc549=document['createElement']('div');_0xbc549[_0x2b1eff(0x208)]=_0x2b1eff(0x1fc);var _0x3b1d7f=createNavigationButton(_0x2b1eff(0x1e4)),_0x3cb5e2=createNavigationButton(_0x2b1eff(0x20a));_0xbc549['append'](_0x3b1d7f),_0xbc549['append'](_0x3cb5e2),_0x3bca1b['append'](_0x264566),_0x3bca1b[_0x2b1eff(0x1f2)](_0xbc549),_0x29b369[_0x2b1eff(0x1f2)](_0x3bca1b),updateButtonVisibility();}function createQuickChatContentButton(_0x1fe171,_0x4d77c1,_0x1acc4b){var _0x85ced9=a0_0x26976c,_0x5a2b3f=document[_0x85ced9(0x1ea)]('button');_0x5a2b3f['innerHTML']=_0x1fe171,_0x5a2b3f['style']['backgroundColor']=_0x1acc4b,_0x5a2b3f[_0x85ced9(0x208)]='quick_chat_button',_0x5a2b3f['classList'][_0x85ced9(0x20b)](_0x85ced9(0x1ef));for(var _0x312948=0x0;_0x312948<_0x4d77c1[_0x85ced9(0x205)];_0x312948++){var _0x2d5b77=document[_0x85ced9(0x209)](_0x85ced9(0x1dd)+_0x312948);_0x2d5b77[_0x85ced9(0x1fd)]=_0x4d77c1[_0x312948],_0x5a2b3f['setAttributeNode'](_0x2d5b77),_0x312948===0x0&&_0x5a2b3f['setAttribute'](_0x85ced9(0x1e6),'0');}return _0x5a2b3f[_0x85ced9(0x1f3)]=function(_0xc7f06f){var _0x2393f8=_0x85ced9;_0xc7f06f[_0x2393f8(0x1da)]();var _0x105e01=parseInt(this['getAttribute'](_0x2393f8(0x1e6))),_0xc86f99=(_0x105e01+0x1)%_0x4d77c1[_0x2393f8(0x205)];writeInChat(this[_0x2393f8(0x1f1)](_0x2393f8(0x1dd)+_0x105e01),chatBoxTextArea),this['setAttribute'](_0x2393f8(0x1e6),_0xc86f99[_0x2393f8(0x1e1)]());},_0x5a2b3f;}function getChatBoxTextArea(_0x53af12){return document['querySelector'](_0x53af12);}function a0_0x1f25(_0x23bf86,_0x4b1945){var _0x4383ae=a0_0x4383();return a0_0x1f25=function(_0x1f257e,_0x3c5155){_0x1f257e=_0x1f257e-0x1d8;var _0x5d3240=_0x4383ae[_0x1f257e];return _0x5d3240;},a0_0x1f25(_0x23bf86,_0x4b1945);}function a0_0x4383(){var _0x5b3a5b=['button','draggable','querySelectorAll','getAttribute','append','onclick','button-grid','2097594VqxjXR','style','9VPJJoM','collapse','setSelectionRange','338uxVvnR','3235736nJrPGL','nav-container','value','110562pTzcJs','4247nHTVtf','ebay\x20contact\x20functions.js\x20loaded','textContent','quick_chat_buttons','12eaaOBp','36TZPqyM','length','none','27816477GAdtCS','className','createAttribute','right','add','1245541COhgHw','color','name','log','min','navigation-button','preventDefault','character','moveStart','data-message-','dispatchEvent','createTextRange','ceil','toString','block','5tpqMsh','left','input','data-checked','storage','messages','6598770JfJEOu','createElement','select','display','7vVCqbk'];a0_0x4383=function(){return _0x5b3a5b;};return a0_0x4383();}function writeInChat(_0x56e93d,_0x18043a){var _0x570f59=_0x18043a;setTimeout(()=>{var _0x47f4a2=a0_0x1f25;_0x570f59[_0x47f4a2(0x1fd)]=_0x56e93d,_0x570f59[_0x47f4a2(0x1de)](new Event(_0x47f4a2(0x1e5),{'bubbles':!![]})),focusOnTextBoxAtEndOfTheLine(_0x18043a);},0x64);}function focusOnTextBoxAtEndOfTheLine(_0xca3ae6){var _0x1d5cd4=a0_0x26976c,_0x41b154=_0xca3ae6[_0x1d5cd4(0x1fd)]['length'];if(_0xca3ae6[_0x1d5cd4(0x1f9)])_0xca3ae6['focus'](),_0xca3ae6[_0x1d5cd4(0x1f9)](_0x41b154,_0x41b154);else{if(_0xca3ae6[_0x1d5cd4(0x1df)]){var _0x5f54fa=_0xca3ae6['createTextRange']();_0x5f54fa[_0x1d5cd4(0x1f8)](!![]),_0x5f54fa['moveEnd'](_0x1d5cd4(0x1db),_0x41b154),_0x5f54fa[_0x1d5cd4(0x1dc)]('character',_0x41b154),_0x5f54fa[_0x1d5cd4(0x1eb)]();}}}