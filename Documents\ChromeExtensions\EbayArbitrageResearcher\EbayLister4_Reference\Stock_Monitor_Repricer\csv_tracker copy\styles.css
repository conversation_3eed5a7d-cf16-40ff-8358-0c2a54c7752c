body {
    font-family: Arial, sans-serif;
    margin: 20px;
    font-size:14px;
}

.table-container {
    max-height: 300px;
    overflow: auto;
    border:1px solid #ccc;
    position: relative;
}

table {
    border-collapse: collapse;
    width: 100%;
    font-size: 12px;
}

table th, table td {
    border: 1px solid #ccc;
    padding: 4px;
    text-align: left;
}

/* Make header row sticky */
table thead th {
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
}

h1, h2, h3 {
    margin-top: 20px;
    margin-bottom: 10px;
}

button {
    margin: 5px 0;
    padding: 5px 10px;
    font-size:12px;
}

.scanned-header {
    background-color: #f0f0f0;
    font-style: italic;
}

/* Highlight processed rows */
.row-done {
    background-color: #d3ffd3; /* light green */
}
