body {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  background-color: #f8f9fb;
  color: #333;
  padding: 40px 20px;
}

.container {
  max-width: 700px;
  background: #fff;
  padding: 25px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

h1, h2, h3 {
  text-align: center;
  margin-bottom: 10px;
}

h1 {
  font-size: 32px;
}

.red { color: #E53238; }
.blue { color: #0064D2; }

.intro {
  text-align: center;
  font-size: 16px;
  margin-bottom: 20px;
}

.section {
  padding: 15px 0;
  border-top: 2px solid #f0f0f0;
  border-bottom: 2px solid #f0f0f0;
}

label {
  font-weight: 500;
  display: block;
  margin-top: 10px;
  margin-bottom: 6px;
}

textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  font-size: 15px;
  border: 2px solid #ddd;
  border-radius: 6px;
  resize: vertical;
  margin-bottom: 15px;
}

.buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

button {
  padding: 10px 14px;
  border-radius: 6px;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
}

#testBtn { background-color: #28a745; }
#testBtn:disabled { background-color: #bbb; cursor: not-allowed; }

#exampleBtn { background-color: #0071bc; }
#clearBtn { background-color: #e53238; }
#downloadBtn { background-color: #f5af02; color: #333; }

.title-box {
  background: #eaf6ff;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  text-align: center;
  min-height: 40px;
  margin-top: 10px;
}



.toggle-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.toggle-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input { opacity: 0; width: 0; height: 0; }

.slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0; right: 0; bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 28px;
}

.slider::before {
  content: "";
  position: absolute;
  height: 20px; width: 20px;
  left: 4px; bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #86B817; /* eBay green */
}

input:checked + .slider::before {
  transform: translateX(22px);
}
