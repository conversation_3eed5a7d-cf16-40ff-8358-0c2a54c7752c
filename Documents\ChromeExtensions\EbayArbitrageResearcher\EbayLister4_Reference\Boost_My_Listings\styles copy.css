/* styles.css */
.loading-bar {
    width: 100%;
    background-color: #ddd;
    border-radius: 5px;
    margin-top: 20px;
}

.loading-progress {
    height: 20px;
    background-color: #4CAF50;
    width: 0%; /* Initial width */
    border-radius: 5px;
    transition: width 0.4s ease-in-out;
}

#status_message {
    font-size: 16px;
    color: #666;
    margin-top: 10px;
}


#filters_container {
    background: #f1f1f1;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    width: 350px;
    margin: auto;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.filter {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.filter label {
    font-weight: bold;
    flex-basis: 50%;
    cursor: help;
    position: relative;
    display: flex;
    align-items: center;
}

.filter input[type="number"] {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.checkbox-filter {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.tooltip-icon {
    margin-left: 5px;
    color: #6c757d; /* Icon color for normal state */
}

.filter label:hover .tooltip-icon {
    color: #495057; /* Darken icon on hover for feedback */
}

.tooltip {
    visibility: hidden;
    width: 250px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: 130%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.filter label:hover .tooltip {
    visibility: visible;
    opacity: 1;
}



#end_and_sell_similar_button {
    padding: 5px 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}


#end_and_sell_similar_button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#toggle_options_button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px; /* Adjust size of the gear icon */
    color: #4CAF50; /* Color to match the theme */
    vertical-align: middle; /* Aligns button with text or other elements */
}

#toggle_options_button:hover {
    color: #367c39; /* Darker shade on hover for visual feedback */
}


.checkbox-filter {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
}

.checkbox-filter .filter {
    display: flex;
    align-items: center; /* Aligns items vertically */
    justify-content: flex-start; /* Aligns items to the start (left) */
    width: 100%; /* Ensures the container takes full width */
    margin-bottom: 10px;
}
s
.checkbox-filter .filter label {
    display: flex;
    align-items: center;
    cursor: help;
    width: calc(100% - 40px); /* Adjusts width accounting for checkbox and any padding/margin */
    flex-shrink: 0; /* Prevents the label from shrinking */
}

.checkbox-filter .filter input[type="checkbox"] {
    margin-left: 10px; /* Spacing between label and checkbox */
    width: 20px; /* Fixed width for checkbox */
    height: 20px; /* Ensures a fixed height for checkboxes */
}