var a0_0x1ffa75=a0_0x2b05;(function(_0x2f43c2,_0x483634){var _0xe5ced2=a0_0x2b05,_0x3ee6bb=_0x2f43c2();while(!![]){try{var _0x37dccd=-parseInt(_0xe5ced2(0x1c5))/0x1*(parseInt(_0xe5ced2(0x225))/0x2)+-parseInt(_0xe5ced2(0x21b))/0x3+-parseInt(_0xe5ced2(0x25f))/0x4+-parseInt(_0xe5ced2(0x244))/0x5*(parseInt(_0xe5ced2(0x233))/0x6)+-parseInt(_0xe5ced2(0x235))/0x7*(-parseInt(_0xe5ced2(0x1c7))/0x8)+-parseInt(_0xe5ced2(0x1a2))/0x9*(parseInt(_0xe5ced2(0x212))/0xa)+parseInt(_0xe5ced2(0x1f5))/0xb;if(_0x37dccd===_0x483634)break;else _0x3ee6bb['push'](_0x3ee6bb['shift']());}catch(_0x546086){_0x3ee6bb['push'](_0x3ee6bb['shift']());}}}(a0_0x41db,0x94c24),console[a0_0x1ffa75(0x22b)](a0_0x1ffa75(0x1ff)));var skuList=[];chrome[a0_0x1ffa75(0x1d2)][a0_0x1ffa75(0x20f)]['addListener'](function(_0x5d823b,_0x11dc59,_0x5b7a4b){var _0x7edc93=a0_0x1ffa75;console['log'](_0x11dc59[_0x7edc93(0x19f)]?'from\x20a\x20content\x20script:'+_0x11dc59[_0x7edc93(0x19f)][_0x7edc93(0x217)]:_0x7edc93(0x201)),console[_0x7edc93(0x22b)](_0x5d823b);if(_0x5d823b[_0x7edc93(0x224)]=='get-top-items')return console[_0x7edc93(0x22b)](_0x7edc93(0x25b)),retrieveFilteredCards()[_0x7edc93(0x22e)](function(_0x182a43){var _0x51a29a=[];for(var _0x1c4abd=0x0;_0x1c4abd<_0x182a43['length'];_0x1c4abd++){var _0x212e11=_0x182a43[_0x1c4abd],_0x161f31=extractAsinFromCard(_0x212e11),_0x57eab8=extractTitleFromCard(_0x212e11),_0x4819af=extractPriceFromElement(_0x212e11),_0x3f2aa3=extractImageUrlFromCard(_0x212e11),_0x464b55=extractReviewsFromCard(_0x212e11),_0x37c040={'asin':_0x161f31,'title':_0x57eab8,'price':_0x4819af,'image':_0x3f2aa3,'reviewCount':_0x464b55};_0x51a29a['push'](_0x37c040);}_0x5b7a4b({'products':_0x51a29a});}),!![];});async function getPriceRangeFromStorage(){var _0x28c9bf=a0_0x1ffa75;let {maxPrice:_0x201742}=await chrome[_0x28c9bf(0x232)][_0x28c9bf(0x1c9)][_0x28c9bf(0x1df)](_0x28c9bf(0x1f8)),{minPrice:_0xda3735}=await chrome[_0x28c9bf(0x232)]['local'][_0x28c9bf(0x1df)](_0x28c9bf(0x1f3));return _0x201742=parseFloat(_0x201742),_0xda3735=parseFloat(_0xda3735),{'minPrice':_0xda3735,'maxPrice':_0x201742};}function extractPriceFromElement(_0x3f035a){var _0x27127a=a0_0x1ffa75;const _0xbe066=_0x3f035a[_0x27127a(0x252)](_0x27127a(0x24e));if(_0xbe066==null)return null;const _0x24609f=_0xbe066[_0x27127a(0x24a)][_0x27127a(0x213)](/,/g,'');return parseFloat(_0x24609f);}function filterCardsBasedOnPrice(_0x3ad729,_0x1b14bd,_0x70587c){var _0x281f9e=a0_0x1ffa75;return _0x3ad729[_0x281f9e(0x1cf)](_0x4957a7=>{const _0x4e8a9b=extractPriceFromElement(_0x4957a7);return _0x4e8a9b!=null&&_0x4e8a9b>_0x1b14bd&&_0x4e8a9b<_0x70587c;});}function extractImageUrlFromCard(_0x21d72c){var _0x4c843a=a0_0x1ffa75,_0x24471f='';try{var _0x33f9a5=_0x21d72c[_0x4c843a(0x252)]('img.s-image');_0x33f9a5!=null&&(_0x24471f=_0x33f9a5[_0x4c843a(0x1cd)]);}catch(_0xd4024d){}return _0x24471f;}function extractAsinFromCard(_0x30e102){var _0x10867d=a0_0x1ffa75;return _0x30e102[_0x10867d(0x250)](_0x10867d(0x1ac));}function filterCardsWithAsins(_0x4c0482){var _0x29db21=a0_0x1ffa75;return _0x4c0482[_0x29db21(0x1cf)](_0x305bfa=>{const _0x36f260=extractAsinFromCard(_0x305bfa);return _0x36f260!=null&&_0x36f260!=='';});}function getAsinsFromCards(_0x86f106){var _0x9e63e0=a0_0x1ffa75;return _0x86f106[_0x9e63e0(0x211)](_0x1dbcfd=>extractAsinFromCard(_0x1dbcfd));}function a0_0x41db(){var _0x386931=['Duplicate\x20Protection','div','results-button-group','target','/dp/','onMessage','Kindle\x20Edition','map','2236120avCvtV','replace','htmlFor','Prime\x20Video','itemNumber','url','innerHTML','param-','push','591891MQxJaR','Hardcover','No\x20image\x20element\x20found','error','getElementById','set','titleElement','number','appendToAmazonLinks','type','18ndqCQf','farewell','list-asin-button','text','alreadyListedAsinsInPage','Max\x20Similar\x20Niche\x20Items','log','preventDefault','Error\x20extracting\x20reviews\x20from\x20card:','then','Error\x20saving\x20params:','asin','remove','storage','30LHQVAY','Minimum\x20Reviews','1009351wEytit','ASIN\x20','warn','error\x20parsing\x20review\x20count','cards\x20cards','Results\x20cleared.','readText','Collect\x20ASINs','Found\x20','Sort\x20By\x20Reviews','settings-modal-content','duplicate_protection','totalAsinsToFetchFromSearch','.p13n-sc-dynamic-image','createElement','407125lUEHSW','writeText','nextSibling','join','veroProtectionEnabled','keywords','innerText','combined\x20cards','button','key','.a-price-whole','max_similiar_niche_items','getAttribute','label','querySelector','[id*=\x22-best-seller\x22]','/VeroList.txt','trim','addedNodes','No\x20results\x20to\x20copy.','getAsins\x20price','restricted\x20words\x20filtered\x20cards','listing-now','get-top-items','click','s-result-item','best\x20seller\x20cards','4475700TFCPSz','remove_books','Listing...','Error\x20retrieving\x20params:','error\x20adding\x20ebay\x20button\x20to\x20carousel\x20item','tab','collect-asins-button','amazonLinks','18WADfjQ','Prioritize\x20Amazon\x20Choice','slice','form','DOMContentLoaded','\x20SKUs\x20from\x20local\x20storage','addEventListener','&times;','list-asin-card--duplicate-text','list-asin-card--duplicate','data-asin','.a-dropdown-container','startsWith','Clear\x20Results','[data-asin]','.s-image','.s-title-instructions-style','snipeModeEnabled','Audio\x20CD','min_reviews','add','title','settings-modal-form','You\x20must\x20copy\x20the\x20item\x20details\x20from\x20the\x20ebay\x20page\x20and\x20have\x20it\x20in\x20the\x20clipboard\x20to\x20snipe.\x20Please\x20try\x20again.','observe','Paperback','price','clipboard','textContent','min_reviews\x20filtered\x20cards','requirementType','body','contains','includes','Append\x20to\x20Existing\x20ASINs','133466BVhCPU','max_reviews','56pGyYId','Copy\x20Results','local','forEach','filteredCards','domain','src','.s-result-item','filter','.sg-col-inner\x20.s-widget-container\x20.a-section','useRequiredKeywords','runtime','settings-modal','?th=1&psc=1','DVD','lastError','error\x20parsing\x20json','getAsins\x20','span','https://www.amazon.','alreadyListedAsinsInPage\x20length','Already\x20Listed','toLowerCase','input','get','Snipe\x20ASIN:\x20','Audible','Blu-ray','.s-include-content-margin','from','prioritizeBestSellers','after','value','Vero\x20Protection\x20Enabled','boolean','[id*=\x22-amazons-choice-label\x22]','Audiobook','Maximum\x20Price','querySelectorAll','productList','\x20New\x20ASINs\x20Copied\x20to\x20Clipboard\x20(Total:\x20','extractTitleFromCarouselCard','prioritizeAmazonChoice','sortByReviews','minPrice','.a-carousel-card','32689030IveLnq','findIndex','Results\x20copied\x20to\x20clipboard.','maxPrice','settings-modal-overlay','checkbox','insertBefore','Save','sendMessage','.p13n-sc-truncate-desktop-type2','amazon_search_page.js\x20loaded','length','from\x20the\x20extension','some','Adding\x20ASIN\x20button\x20to\x20card:','The\x20title,\x20price,\x20or\x20item\x20number\x20is\x20missing\x20from\x20the\x20clipboard.\x20Please\x20try\x20again.','appendChild','Adjust\x20Settings','classList','checked','parentNode'];a0_0x41db=function(){return _0x386931;};return a0_0x41db();}function a0_0x2b05(_0x2c719b,_0x53d4ea){var _0x41dbb6=a0_0x41db();return a0_0x2b05=function(_0x2b056c,_0x42ecfc){_0x2b056c=_0x2b056c-0x19d;var _0x1d5189=_0x41dbb6[_0x2b056c];return _0x1d5189;},a0_0x2b05(_0x2c719b,_0x53d4ea);}function filterBestSellerCards(_0x5955e6){return _0x5955e6['filter'](_0x5bdc0a=>{var _0x12feb1=a0_0x2b05;return _0x5bdc0a[_0x12feb1(0x252)](_0x12feb1(0x253));});}function filterAmazonChoiceCards(_0x1db5cf){var _0x3dfa2d=a0_0x1ffa75;return _0x1db5cf[_0x3dfa2d(0x1cf)](_0x27fc6c=>{var _0x1c473d=_0x3dfa2d;return _0x27fc6c['querySelector'](_0x1c473d(0x1ea));});}function filterBooksFromCards(_0x18cf3e){var _0x56b8cb=a0_0x1ffa75;const _0x118db7=[_0x56b8cb(0x21c),_0x56b8cb(0x1bb),_0x56b8cb(0x1eb),_0x56b8cb(0x1e1),_0x56b8cb(0x210),_0x56b8cb(0x1b4),_0x56b8cb(0x1e2),_0x56b8cb(0x1d5),_0x56b8cb(0x215)];return _0x18cf3e[_0x56b8cb(0x1cf)](_0x1cbcc1=>{var _0x3c12a9=_0x56b8cb;const _0x174c99=extractAsinFromCard(_0x1cbcc1);if(_0x174c99==null||!_0x174c99[_0x3c12a9(0x1ae)]('B0'))return![];const _0x88acac=_0x1cbcc1[_0x3c12a9(0x24a)]||_0x1cbcc1[_0x3c12a9(0x218)];return!_0x118db7[_0x3c12a9(0x202)](_0x535bb1=>_0x88acac[_0x3c12a9(0x1c3)](_0x535bb1));});}function extractReviewsFromCard(_0x52b00e){var _0x3959d4=a0_0x1ffa75,_0x177ddc=0x0;try{var _0x1d3d7f=_0x52b00e[_0x3959d4(0x252)]('span.a-size-base.s-underline-text');if(_0x1d3d7f!=null&&_0x1d3d7f!=undefined&&_0x1d3d7f[_0x3959d4(0x1be)]!=''){var _0x554ae2=_0x1d3d7f['innerText'];_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/,/g,''),_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/\s/g,''),_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/\(/g,''),_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/\)/g,''),_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/\+/g,'');if(_0x554ae2[_0x3959d4(0x1c3)]('K'))_0x554ae2=_0x554ae2[_0x3959d4(0x213)](/K/g,''),_0x177ddc=parseInt(_0x554ae2)*0x3e8;else try{_0x177ddc=parseInt(_0x554ae2);}catch(_0x21464e){console[_0x3959d4(0x22b)](_0x3959d4(0x238),_0x21464e);}}isNaN(_0x177ddc)&&(_0x177ddc=0x0);}catch(_0x431487){console[_0x3959d4(0x21e)](_0x3959d4(0x22d),_0x431487);}return _0x177ddc;}function extractTitleFromCard(_0x31706b){var _0x5839fb=a0_0x1ffa75,_0x471eaa='';try{var _0x5e7cdf=_0x31706b[_0x5839fb(0x252)](_0x5839fb(0x1b2));_0x5e7cdf!=null&&(_0x471eaa=_0x5e7cdf[_0x5839fb(0x24a)],_0x471eaa=_0x471eaa['replace'](/\n/g,'\x20'),_0x471eaa=_0x471eaa[_0x5839fb(0x255)]());}catch(_0x4fa260){}return _0x471eaa;}function extractTitleFromCarouselCard(_0x3e683a){var _0x53c7d6=a0_0x1ffa75,_0x538cd7='';console['log'](_0x53c7d6(0x1f0),_0x3e683a);try{var _0x2b2f34=_0x3e683a[_0x53c7d6(0x252)](_0x53c7d6(0x1fe));console['log'](_0x53c7d6(0x221),_0x2b2f34),_0x2b2f34!=null&&(_0x538cd7=_0x2b2f34[_0x53c7d6(0x1be)],_0x538cd7=_0x538cd7['replace'](/\n/g,'\x20'),_0x538cd7=_0x538cd7[_0x53c7d6(0x255)]());}catch(_0x152711){}return _0x538cd7;}function sortCardsByReviews(_0x530519){return _0x530519['sort']((_0x298d18,_0x18c02a)=>{const _0x417917=extractReviewsFromCard(_0x298d18),_0x2616ed=extractReviewsFromCard(_0x18c02a);return _0x2616ed-_0x417917;});}async function retrieveCards(){var _0x72ee71=a0_0x1ffa75;const _0xf97003=Array[_0x72ee71(0x1e4)](document['querySelectorAll'](_0x72ee71(0x1ce)));return _0xf97003;}async function retrieveFilteredCards(_0x361c25={}){var _0x2522f1=a0_0x1ffa75;async function _0x2194b4(_0x47996c){var _0x2fe23b=a0_0x2b05;if(_0x361c25[_0x47996c]!=null)return _0x361c25[_0x47996c];else{const _0x59e72f=await chrome[_0x2fe23b(0x232)][_0x2fe23b(0x1c9)][_0x2fe23b(0x1df)](_0x47996c);return _0x59e72f[_0x47996c];}}var _0x2abe5c=parseInt(await _0x2194b4(_0x2522f1(0x241))),_0x48d841=await retrieveCards();console[_0x2522f1(0x22b)](_0x2522f1(0x239),_0x48d841);const _0x4627cc=await _0x2194b4(_0x2522f1(0x1f3)),_0x3614a7=await _0x2194b4(_0x2522f1(0x1f8));var _0x2c5c9a=filterCardsBasedOnPrice(_0x48d841,_0x4627cc,_0x3614a7);_0x2c5c9a=filterCardsWithAsins(_0x2c5c9a),console['log'](_0x2522f1(0x1cb),_0x2c5c9a);var _0x57ee75=await _0x2194b4(_0x2522f1(0x24f)),_0x2f17c2=await getSkuListFromLocalStorage(),_0x4e564f=getAsinsFromCards(_0x2c5c9a),_0x352f96=_0x2f17c2[_0x2522f1(0x1cf)](_0x1b6350=>_0x4e564f[_0x2522f1(0x1c3)](_0x1b6350));console['log'](_0x2522f1(0x229),_0x352f96),console['log'](_0x2522f1(0x1db),_0x352f96['length']),console[_0x2522f1(0x22b)](_0x2522f1(0x24f),_0x57ee75);if(_0x352f96['length']>=_0x57ee75)return console[_0x2522f1(0x22b)]('Max\x20similar\x20niche\x20items\x20reached'),[];else console[_0x2522f1(0x22b)]('Max\x20similar\x20niche\x20items\x20not\x20reached');var _0x428578=await _0x2194b4(_0x2522f1(0x1ee));!_0x428578&&(_0x428578=[]);var _0x4ce35f=_0x428578[_0x2522f1(0x211)](_0x4b6594=>_0x4b6594[_0x2522f1(0x230)]);_0x2c5c9a=_0x2c5c9a[_0x2522f1(0x1cf)](_0xb56849=>!_0x4ce35f[_0x2522f1(0x1c3)](extractAsinFromCard(_0xb56849))),console['log']('filtered\x20cards\x20after\x20removing\x20already\x20scraped\x20asins',_0x2c5c9a);var _0x2f8369=await _0x2194b4(_0x2522f1(0x260));_0x2f8369&&(_0x2c5c9a=filterBooksFromCards(_0x2c5c9a),console[_0x2522f1(0x22b)]('remove_books\x20filtered\x20cards',_0x2c5c9a));var _0x38f10b=await _0x2194b4('restricted_words');_0x38f10b&&_0x38f10b['length']>0x0&&(_0x2c5c9a=_0x2c5c9a['filter'](_0x3c77a9=>{var _0x58e476=extractTitleFromCard(_0x3c77a9);return!titleContainsRestrictedWords(_0x58e476,_0x38f10b);}),console[_0x2522f1(0x22b)](_0x2522f1(0x259),_0x2c5c9a));var _0x47659f=await _0x2194b4(_0x2522f1(0x1d1));if(_0x47659f){var _0x4a8d74=await _0x2194b4(_0x2522f1(0x249)),_0x7eac2b=await _0x2194b4(_0x2522f1(0x1c0));_0x4a8d74&&_0x4a8d74[_0x2522f1(0x200)]>0x0&&(_0x2c5c9a=_0x2c5c9a[_0x2522f1(0x1cf)](_0x1a2e91=>{var _0xea18f4=extractTitleFromCard(_0x1a2e91);return titleContainsRequiredKeywords(_0xea18f4,_0x4a8d74,_0x7eac2b);}),console[_0x2522f1(0x22b)]('required\x20keywords\x20filtered\x20cards',_0x2c5c9a));}var _0x50e894=await _0x2194b4(_0x2522f1(0x248));if(_0x50e894){var _0x5a84ee=await _0x2194b4('veroBrands');!_0x5a84ee&&(_0x5a84ee=await fetch(chrome['runtime']['getURL'](_0x2522f1(0x254))),_0x5a84ee=await _0x5a84ee[_0x2522f1(0x228)](),_0x5a84ee=_0x5a84ee['split']('\x0a')),_0x5a84ee=_0x5a84ee[_0x2522f1(0x1cf)](_0xf76da0=>_0xf76da0[_0x2522f1(0x200)]>0x0)['map'](_0x336083=>_0x336083[_0x2522f1(0x1dd)]()[_0x2522f1(0x255)]()),_0x5a84ee['length']>0x0&&(_0x2c5c9a=_0x2c5c9a[_0x2522f1(0x1cf)](_0x54e98f=>{var _0xaa7cf3=extractTitleFromCard(_0x54e98f);return!titleContainsRestrictedWords(_0xaa7cf3,_0x5a84ee);}),console['log']('vero\x20filtered\x20cards',_0x2c5c9a));}var _0x4f0e65=await _0x2194b4(_0x2522f1(0x1b5));console[_0x2522f1(0x22b)]('min_reviews',_0x4f0e65);_0x4f0e65>0x0&&(_0x2c5c9a=_0x2c5c9a['filter'](_0x1f80b9=>{var _0x3accd9=extractReviewsFromCard(_0x1f80b9);return _0x3accd9>=_0x4f0e65;}),console[_0x2522f1(0x22b)](_0x2522f1(0x1bf),_0x2c5c9a));var _0x493afe=await _0x2194b4(_0x2522f1(0x1c6));console['log'](_0x2522f1(0x1c6),_0x493afe);_0x493afe>0x0&&(_0x2c5c9a=_0x2c5c9a[_0x2522f1(0x1cf)](_0x2c6141=>{var _0x1afbc0=extractReviewsFromCard(_0x2c6141);return _0x1afbc0<=_0x493afe;}),console[_0x2522f1(0x22b)]('max_reviews\x20filtered\x20cards',_0x2c5c9a));var _0x9a528b=await _0x2194b4(_0x2522f1(0x1f2));console['log'](_0x2522f1(0x1f2),_0x9a528b);_0x9a528b&&(_0x2c5c9a=sortCardsByReviews(_0x2c5c9a),console[_0x2522f1(0x22b)]('sorted\x20cards',_0x2c5c9a),_0x2c5c9a[_0x2522f1(0x1ca)](_0x5cc1aa=>{var _0x195f08=_0x2522f1;console[_0x195f08(0x22b)](extractReviewsFromCard(_0x5cc1aa));}));var _0x3c9f9b=filterBestSellerCards(_0x2c5c9a);_0x3c9f9b=sortCardsByReviews(_0x3c9f9b),console[_0x2522f1(0x22b)](_0x2522f1(0x25e),_0x3c9f9b);var _0x3443c9=filterAmazonChoiceCards(_0x2c5c9a);_0x3443c9=sortCardsByReviews(_0x3443c9),console['log']('amazon\x20choice\x20cards',_0x3443c9);var _0x1b41cc=await _0x2194b4(_0x2522f1(0x1f1)),_0x43a0be=await _0x2194b4(_0x2522f1(0x1e5)),_0x3c5431=[];if(_0x1b41cc&&_0x43a0be)_0x3c5431=[..._0x3443c9,..._0x3c9f9b,..._0x2c5c9a];else{if(_0x1b41cc)_0x3c5431=[..._0x3443c9,..._0x2c5c9a];else _0x43a0be?_0x3c5431=[..._0x3c9f9b,..._0x2c5c9a]:_0x3c5431=[..._0x2c5c9a];}_0x3c5431=_0x3c5431['filter']((_0x1eaf83,_0x1d6fa8,_0x14fafb)=>_0x1d6fa8===_0x14fafb[_0x2522f1(0x1f6)](_0x3ff796=>extractAsinFromCard(_0x3ff796)===extractAsinFromCard(_0x1eaf83))),console[_0x2522f1(0x22b)](_0x2522f1(0x24b),_0x3c5431);var _0x3f71c6=await _0x2194b4(_0x2522f1(0x240));return console[_0x2522f1(0x22b)](_0x2522f1(0x240),_0x3f71c6),_0x3f71c6&&(_0x3c5431=_0x3c5431['filter'](_0x32d9f5=>{var _0x44aaf4=_0x2522f1,_0x1b03e8=extractAsinFromCard(_0x32d9f5);return!_0x2f17c2[_0x44aaf4(0x1c3)](_0x1b03e8);})),_0x3c5431=_0x3c5431[_0x2522f1(0x1a4)](0x0,_0x2abe5c),_0x3c5431;}async function getAsins(){var _0x5d0ce0=a0_0x1ffa75,_0x100eb4=document['querySelectorAll'](_0x5d0ce0(0x1ce)),_0x1c0623=[],_0x1b94c7=[],{maxPrice:_0x7459b5}=await chrome['storage'][_0x5d0ce0(0x1c9)][_0x5d0ce0(0x1df)](_0x5d0ce0(0x1f8));_0x7459b5=parseFloat(_0x7459b5);var {minPrice:_0x545cb9}=await chrome[_0x5d0ce0(0x232)][_0x5d0ce0(0x1c9)][_0x5d0ce0(0x1df)](_0x5d0ce0(0x1f3));_0x545cb9=parseFloat(_0x545cb9);for(var _0x118db2=0x0;_0x118db2<_0x100eb4[_0x5d0ce0(0x200)];_0x118db2++){var _0x5eb988=_0x100eb4[_0x118db2]['querySelector'](_0x5d0ce0(0x24e));console[_0x5d0ce0(0x22b)](_0x5d0ce0(0x1d8),_0x5eb988);var _0x2fbaf2=null;_0x5eb988!=null&&(_0x2fbaf2=_0x5eb988[_0x5d0ce0(0x24a)][_0x5d0ce0(0x213)](/,/g,'')),console['log'](_0x5d0ce0(0x258),_0x2fbaf2),_0x2fbaf2=parseFloat(_0x2fbaf2),_0x2fbaf2!=null&&_0x2fbaf2<_0x7459b5&&_0x2fbaf2>_0x545cb9&&_0x1c0623[_0x5d0ce0(0x21a)](_0x100eb4[_0x118db2]);}for(var _0x118db2=0x0;_0x118db2<_0x1c0623[_0x5d0ce0(0x200)];_0x118db2++){var _0x329164=_0x1c0623[_0x118db2][_0x5d0ce0(0x250)](_0x5d0ce0(0x1ac));_0x329164!=null&&_0x329164!==''&&!_0x1b94c7[_0x5d0ce0(0x1c3)](_0x329164)&&_0x1b94c7['push'](_0x329164);}return console[_0x5d0ce0(0x22b)](_0x1b94c7),_0x1b94c7;}function addAsinButton(_0x3e8bc4){var _0x5e3417=a0_0x1ffa75;chrome['storage'][_0x5e3417(0x1c9)][_0x5e3417(0x1df)]('snipeModeEnabled',function(_0x580d7b){var _0x2866d8=_0x5e3417,_0x4ec17d=_0x580d7b[_0x2866d8(0x1b3)];console['log'](_0x2866d8(0x203),_0x3e8bc4);var _0x5f44e8=_0x3e8bc4[_0x2866d8(0x250)]('data-asin');if(!_0x5f44e8){var _0x1ddc2f=_0x3e8bc4[_0x2866d8(0x1ed)](_0x2866d8(0x1b0));_0x1ddc2f[_0x2866d8(0x200)]>0x0&&(_0x5f44e8=_0x1ddc2f[0x0][_0x2866d8(0x250)](_0x2866d8(0x1ac)));}if(_0x5f44e8){}else return;var _0x46e67d=_0x3e8bc4[_0x2866d8(0x252)](_0x2866d8(0x1b1))||_0x3e8bc4[_0x2866d8(0x252)](_0x2866d8(0x1e3))||_0x3e8bc4[_0x2866d8(0x252)](_0x2866d8(0x242));!_0x46e67d&&console[_0x2866d8(0x22b)](_0x2866d8(0x21d),_0x3e8bc4);if(skuList['includes'](_0x5f44e8)){console[_0x2866d8(0x22b)](_0x2866d8(0x236)+_0x5f44e8+'\x20is\x20already\x20in\x20the\x20list\x20of\x20SKUs'),_0x3e8bc4['classList'][_0x2866d8(0x1b6)](_0x2866d8(0x1ab));const _0x5b86d7=document[_0x2866d8(0x243)](_0x2866d8(0x1d9));_0x5b86d7[_0x2866d8(0x207)][_0x2866d8(0x1b6)](_0x2866d8(0x1aa)),_0x5b86d7['textContent']=_0x2866d8(0x1dc);_0x46e67d?_0x46e67d[_0x2866d8(0x1e6)](_0x5b86d7):_0x3e8bc4[_0x2866d8(0x205)](_0x5b86d7);return;}const _0x56b0d4=document['createElement'](_0x2866d8(0x24c));_0x56b0d4['classList'][_0x2866d8(0x1b6)]('list-asin-button'),_0x4ec17d?_0x56b0d4['textContent']=_0x2866d8(0x1e0)+_0x5f44e8:_0x56b0d4[_0x2866d8(0x1be)]='List\x20ASIN:\x20'+_0x5f44e8,_0x56b0d4['addEventListener'](_0x2866d8(0x25c),async _0x2c0008=>{var _0x485300=_0x2866d8;_0x2c0008[_0x485300(0x22c)](),console[_0x485300(0x22b)](_0x5f44e8),skuList[_0x485300(0x21a)](_0x5f44e8),_0x56b0d4['disabled']=!![],_0x3e8bc4[_0x485300(0x207)][_0x485300(0x231)](_0x485300(0x227)),_0x56b0d4[_0x485300(0x207)]['add'](_0x485300(0x25a)),_0x4ec17d?(_0x56b0d4[_0x485300(0x1be)]='Sniping...',navigator[_0x485300(0x1bd)][_0x485300(0x23b)]()[_0x485300(0x22e)](_0x3c8b83=>{var _0x6f4de9=_0x485300;console[_0x6f4de9(0x22b)](_0x3c8b83);var _0x4f9e32=null;try{_0x4f9e32=JSON['parse'](_0x3c8b83);}catch(_0x1de254){console[_0x6f4de9(0x22b)](_0x6f4de9(0x1d7),_0x1de254),alert(_0x6f4de9(0x1b9));return;}if(_0x4f9e32[_0x6f4de9(0x1b7)]==null||_0x4f9e32[_0x6f4de9(0x1bc)]==null||_0x4f9e32[_0x6f4de9(0x216)]==null){alert(_0x6f4de9(0x204));return;}var _0x302bfd=extractPriceFromElement(_0x3e8bc4);if(_0x302bfd>_0x4f9e32[_0x6f4de9(0x1bc)]){alert('The\x20price\x20from\x20Amazon\x20is\x20greater\x20than\x20the\x20price\x20from\x20Ebay.\x20You\x20will\x20not\x20make\x20any\x20profit.\x20Please\x20try\x20again.');return;}chrome['runtime'][_0x6f4de9(0x1fd)]({'type':'open_amazon_and_snipe_to_ebay','asin':_0x5f44e8,'snipeData':_0x4f9e32},function(_0x1a1d08){var _0x259ebe=_0x6f4de9;console[_0x259ebe(0x22b)](_0x1a1d08[_0x259ebe(0x226)]);});})):(_0x56b0d4['textContent']=_0x485300(0x261),chrome[_0x485300(0x1d2)]['sendMessage']({'type':'open_amazon_and_list_to_ebay','asin':_0x5f44e8},function(_0xf1641e){var _0x44a0f7=_0x485300;console[_0x44a0f7(0x22b)](_0xf1641e[_0x44a0f7(0x226)]);}));}),_0x46e67d?_0x46e67d[_0x2866d8(0x1e6)](_0x56b0d4):_0x3e8bc4['appendChild'](_0x56b0d4);});}function addAsinToItems(){var _0xe3d57d=a0_0x1ffa75,_0x4c101c=document[_0xe3d57d(0x1ed)](_0xe3d57d(0x1ce));console[_0xe3d57d(0x22b)](_0xe3d57d(0x23d)+_0x4c101c['length']+'\x20cards'),_0x4c101c[_0xe3d57d(0x1ca)](_0x58e028=>{var _0x555f96=_0xe3d57d;addAsinButton(_0x58e028);var _0x22e908=extractTitleFromCard(_0x58e028),_0x3517d3=createEbaySearchButton(_0x22e908);try{_0x58e028[_0x555f96(0x252)](_0x555f96(0x1d0))[_0x555f96(0x205)](_0x3517d3);}catch(_0x10d17c){}});}function addAsinToCarouselItems(){var _0x5be1fd=a0_0x1ffa75,_0x58a80c=document['querySelectorAll'](_0x5be1fd(0x1f4));console['log'](_0x5be1fd(0x23d)+_0x58a80c['length']+'\x20carousel\x20cards\x20cards'),_0x58a80c[_0x5be1fd(0x1ca)](_0xe52a61=>{var _0x537500=_0x5be1fd;addAsinButton(_0xe52a61);var _0x32e11b=extractTitleFromCarouselCard(_0xe52a61);console[_0x537500(0x22b)]('title:',_0x32e11b);var _0x111ebc=createEbaySearchButton(_0x32e11b);try{_0xe52a61[_0x537500(0x205)](_0x111ebc);}catch(_0x1c8750){console[_0x537500(0x237)](_0x537500(0x19e),_0x1c8750);}});}async function addAsinButtons(){var _0x3ca013=a0_0x1ffa75;console['log']('Retrieved\x20'+skuList[_0x3ca013(0x200)]+_0x3ca013(0x1a7)),addAsinToItems(),addAsinToCarouselItems();const _0x88de8=new MutationObserver(_0x6bc2e5=>{var _0x171760=_0x3ca013;_0x6bc2e5[_0x171760(0x1ca)](_0x3e76c5=>{var _0xf0a8d1=_0x171760;_0x3e76c5[_0xf0a8d1(0x256)]['forEach'](_0x1c53fd=>{var _0x4b1e24=_0xf0a8d1;_0x1c53fd['classList']&&_0x1c53fd[_0x4b1e24(0x207)][_0x4b1e24(0x1c2)](_0x4b1e24(0x25d))&&addAsinButton(_0x1c53fd);});});});_0x88de8[_0x3ca013(0x1ba)](document[_0x3ca013(0x1c1)],{'childList':!![],'subtree':!![]});}function collectAsins(){var _0x216a35=a0_0x1ffa75;const _0x36d65a=[],_0x2f4cec=document[_0x216a35(0x1ed)](_0x216a35(0x1ce));return _0x2f4cec[_0x216a35(0x1ca)](_0x458e7e=>{var _0x2ae76c=_0x216a35;const _0x4243be=_0x458e7e['getAttribute'](_0x2ae76c(0x1ac));_0x4243be&&_0x36d65a[_0x2ae76c(0x21a)](_0x4243be);}),console['log'](_0x36d65a),_0x36d65a;}const defaultParams={'totalAsinsToFetchFromSearch':0x64,'appendToAmazonLinks':![],'minPrice':0x0,'maxPrice':0x3e8,'min_reviews':0x1,'max_reviews':0xf423f,'sortByReviews':!![],'remove_books':!![],'useRequiredKeywords':![],'veroProtectionEnabled':![],'prioritizeAmazonChoice':!![],'prioritizeBestSellers':!![],'duplicate_protection':!![],'max_similiar_niche_items':0x1869f};var paramsStorageKey='collectAsinsParams';async function addCollectAsinsButton(){var _0x1824d2=a0_0x1ffa75;const _0x2d738a=document['querySelector'](_0x1824d2(0x1ad));if(!_0x2d738a)return;const _0x216696=document[_0x1824d2(0x243)](_0x1824d2(0x24c));_0x216696['classList'][_0x1824d2(0x1b6)](_0x1824d2(0x1a0)),_0x216696['textContent']=_0x1824d2(0x23c);const _0x143de5=document['createElement'](_0x1824d2(0x24c));_0x143de5[_0x1824d2(0x207)][_0x1824d2(0x1b6)]('settings-button'),_0x143de5[_0x1824d2(0x218)]='⚙️',_0x143de5[_0x1824d2(0x1b7)]=_0x1824d2(0x206),_0x143de5[_0x1824d2(0x1a8)](_0x1824d2(0x25c),_0x5aabba=>{var _0x456029=_0x1824d2;_0x5aabba[_0x456029(0x22c)](),openSettingsModal();}),_0x216696['addEventListener'](_0x1824d2(0x25c),async _0x208f60=>{var _0x22d913=_0x1824d2;_0x208f60['preventDefault']();const _0x313ea8=await getDomain();let _0x5f547e=await getParamsFromStorage();console[_0x22d913(0x22b)]('Parameters\x20used\x20for\x20collecting\x20ASINs:',_0x5f547e);var _0x2fa72f=await retrieveFilteredCards(_0x5f547e),_0x5555c6=[];for(var _0xb69abc=0x0;_0xb69abc<_0x2fa72f['length'];_0xb69abc++){var _0x117a6c=_0x2fa72f[_0xb69abc],_0xffc461=extractAsinFromCard(_0x117a6c),_0x167cb5=extractTitleFromCard(_0x117a6c),_0x2001a1=extractPriceFromElement(_0x117a6c),_0x4489b4=extractImageUrlFromCard(_0x117a6c),_0x54590e=extractReviewsFromCard(_0x117a6c),_0x5b7725={'asin':_0xffc461,'title':_0x167cb5,'price':_0x2001a1,'image':_0x4489b4,'reviewCount':_0x54590e};_0x5555c6[_0x22d913(0x21a)](_0x5b7725);}var _0x4f45a7=_0x5555c6[_0x22d913(0x211)](_0x14620b=>_0x14620b[_0x22d913(0x230)]),_0x1da612=_0x4f45a7[_0x22d913(0x211)](_0x278bf1=>_0x22d913(0x1da)+_0x313ea8+_0x22d913(0x20e)+_0x278bf1+_0x22d913(0x1d4)),{amazonLinks:_0x1d2035}=await chrome[_0x22d913(0x232)][_0x22d913(0x1c9)][_0x22d913(0x1df)](_0x22d913(0x1a1));_0x1d2035=_0x1d2035||[];!_0x5f547e[_0x22d913(0x223)]&&(_0x1d2035=[]);const _0x154646=[...new Set([..._0x1d2035,..._0x1da612])];chrome[_0x22d913(0x232)][_0x22d913(0x1c9)][_0x22d913(0x220)]({'amazonLinks':_0x154646}),navigator[_0x22d913(0x1bd)][_0x22d913(0x245)](_0x154646[_0x22d913(0x247)]('\x0a')),_0x216696[_0x22d913(0x1be)]=_0x1da612['length']+_0x22d913(0x1ef)+_0x154646[_0x22d913(0x200)]+')';}),_0x2d738a[_0x1824d2(0x209)][_0x1824d2(0x1fb)](_0x216696,_0x2d738a[_0x1824d2(0x246)]),_0x2d738a[_0x1824d2(0x209)][_0x1824d2(0x1fb)](_0x143de5,_0x216696[_0x1824d2(0x246)]);}async function getParamsFromStorage(){return new Promise((_0x1f35f0,_0x1f7845)=>{var _0x56aed9=a0_0x2b05;chrome[_0x56aed9(0x232)][_0x56aed9(0x1c9)][_0x56aed9(0x1df)](paramsStorageKey,_0x21aa8b=>{var _0x223918=_0x56aed9;if(chrome[_0x223918(0x1d2)][_0x223918(0x1d6)])console[_0x223918(0x21e)](_0x223918(0x19d),chrome[_0x223918(0x1d2)]['lastError']),_0x1f35f0({...defaultParams});else{const _0x1a8672=_0x21aa8b[paramsStorageKey];if(_0x1a8672){const _0x452a78={...defaultParams,..._0x1a8672};_0x1f35f0(_0x452a78);}else _0x1f35f0({...defaultParams});}});});}async function saveParamsToStorage(_0x379519){return new Promise((_0x1f1292,_0x1b534c)=>{var _0x3c0528=a0_0x2b05;const _0x481b1f={};_0x481b1f[paramsStorageKey]={...defaultParams,..._0x379519},chrome[_0x3c0528(0x232)][_0x3c0528(0x1c9)]['set'](_0x481b1f,()=>{var _0x56717d=_0x3c0528;chrome['runtime'][_0x56717d(0x1d6)]?(console[_0x56717d(0x21e)](_0x56717d(0x22f),chrome[_0x56717d(0x1d2)][_0x56717d(0x1d6)]),_0x1b534c(chrome[_0x56717d(0x1d2)][_0x56717d(0x1d6)])):_0x1f1292();});});}function openSettingsModal(){createSettingsModal();}async function createSettingsModal(){var _0x29c962=a0_0x1ffa75;if(document[_0x29c962(0x21f)](_0x29c962(0x1f9)))return;let _0x34e652=await getParamsFromStorage();!_0x34e652&&(_0x34e652=defaultParams,await saveParamsToStorage(defaultParams));const _0x59ee56=document[_0x29c962(0x243)]('div');_0x59ee56['id']=_0x29c962(0x1f9),_0x59ee56[_0x29c962(0x207)]['add'](_0x29c962(0x1f9)),_0x59ee56[_0x29c962(0x1a8)](_0x29c962(0x25c),function(_0x14cd12){var _0x4efdae=_0x29c962;_0x14cd12[_0x4efdae(0x20d)]===_0x59ee56&&closeSettingsModal();});const _0x139cf2=document[_0x29c962(0x243)](_0x29c962(0x20b));_0x139cf2['id']=_0x29c962(0x1d3),_0x139cf2[_0x29c962(0x207)][_0x29c962(0x1b6)](_0x29c962(0x1d3));const _0x3b16d6=document['createElement']('div');_0x3b16d6[_0x29c962(0x207)][_0x29c962(0x1b6)](_0x29c962(0x23f));const _0x76b3ef=document['createElement'](_0x29c962(0x20b));_0x76b3ef[_0x29c962(0x207)]['add']('settings-modal-header');const _0xba8781=document[_0x29c962(0x243)]('h2');_0xba8781[_0x29c962(0x1be)]='Adjust\x20Settings',_0x76b3ef[_0x29c962(0x205)](_0xba8781);const _0x473277=document[_0x29c962(0x243)](_0x29c962(0x1d9));_0x473277[_0x29c962(0x207)][_0x29c962(0x1b6)]('settings-modal-close'),_0x473277[_0x29c962(0x218)]=_0x29c962(0x1a9),_0x473277[_0x29c962(0x1a8)](_0x29c962(0x25c),closeSettingsModal),_0x76b3ef['appendChild'](_0x473277),_0x3b16d6[_0x29c962(0x205)](_0x76b3ef);const _0x4b8ee8=document[_0x29c962(0x243)](_0x29c962(0x1a5));_0x4b8ee8['id']=_0x29c962(0x1b8);const _0x4e505a=[{'key':'totalAsinsToFetchFromSearch','label':'Total\x20ASINs\x20to\x20Fetch'},{'key':'appendToAmazonLinks','label':_0x29c962(0x1c4)},{'key':_0x29c962(0x1f3),'label':'Minimum\x20Price'},{'key':_0x29c962(0x1f8),'label':_0x29c962(0x1ec)},{'key':_0x29c962(0x1b5),'label':_0x29c962(0x234)},{'key':_0x29c962(0x1c6),'label':'Maximum\x20Reviews'},{'key':_0x29c962(0x1f2),'label':_0x29c962(0x23e)},{'key':'remove_books','label':'Remove\x20Books'},{'key':_0x29c962(0x1d1),'label':'Use\x20Required\x20Keywords'},{'key':_0x29c962(0x248),'label':_0x29c962(0x1e8)},{'key':'prioritizeAmazonChoice','label':_0x29c962(0x1a3)},{'key':'prioritizeBestSellers','label':'Prioritize\x20Best\x20Sellers'},{'key':'duplicate_protection','label':_0x29c962(0x20a)},{'key':_0x29c962(0x24f),'label':_0x29c962(0x22a)}];_0x4e505a[_0x29c962(0x1ca)](_0x5820ee=>{var _0x511bd5=_0x29c962;const _0x4df484=_0x5820ee[_0x511bd5(0x24d)],_0x29b6e8=_0x5820ee[_0x511bd5(0x251)],_0x3002a1=_0x34e652[_0x4df484],_0x41ff13=document[_0x511bd5(0x243)]('div');_0x41ff13[_0x511bd5(0x207)]['add']('form-group');if(typeof _0x3002a1===_0x511bd5(0x1e9)){const _0xd3bb23=document[_0x511bd5(0x243)](_0x511bd5(0x1de));_0xd3bb23[_0x511bd5(0x224)]=_0x511bd5(0x1fa),_0xd3bb23['id']='param-'+_0x4df484,_0xd3bb23[_0x511bd5(0x208)]=_0x3002a1;const _0xd83748=document['createElement'](_0x511bd5(0x251));_0xd83748[_0x511bd5(0x214)]=_0x511bd5(0x219)+_0x4df484,_0xd83748[_0x511bd5(0x1be)]=_0x29b6e8,_0x41ff13['appendChild'](_0xd3bb23),_0x41ff13['appendChild'](_0xd83748);}else{const _0x232c9b=document[_0x511bd5(0x243)](_0x511bd5(0x251));_0x232c9b[_0x511bd5(0x214)]=_0x511bd5(0x219)+_0x4df484,_0x232c9b[_0x511bd5(0x1be)]=_0x29b6e8;const _0x38094f=document[_0x511bd5(0x243)](_0x511bd5(0x1de));_0x38094f[_0x511bd5(0x224)]=_0x511bd5(0x222),_0x38094f['id']=_0x511bd5(0x219)+_0x4df484,_0x38094f[_0x511bd5(0x1e7)]=_0x3002a1,_0x41ff13[_0x511bd5(0x205)](_0x232c9b),_0x41ff13['appendChild'](_0x38094f);}_0x4b8ee8[_0x511bd5(0x205)](_0x41ff13);});const _0x213613=document[_0x29c962(0x243)]('div');_0x213613['classList'][_0x29c962(0x1b6)](_0x29c962(0x20c));const _0x34e579=document[_0x29c962(0x243)](_0x29c962(0x24c));_0x34e579[_0x29c962(0x224)]=_0x29c962(0x24c),_0x34e579[_0x29c962(0x1be)]=_0x29c962(0x1c8),_0x34e579[_0x29c962(0x1a8)](_0x29c962(0x25c),copyResults);const _0x585cdc=document[_0x29c962(0x243)](_0x29c962(0x24c));_0x585cdc[_0x29c962(0x224)]=_0x29c962(0x24c),_0x585cdc[_0x29c962(0x1be)]=_0x29c962(0x1af),_0x585cdc[_0x29c962(0x1a8)](_0x29c962(0x25c),clearResults),_0x213613[_0x29c962(0x205)](_0x34e579),_0x213613[_0x29c962(0x205)](_0x585cdc),_0x4b8ee8[_0x29c962(0x205)](_0x213613);const _0x214043=document[_0x29c962(0x243)](_0x29c962(0x20b));_0x214043['classList'][_0x29c962(0x1b6)]('button-group');const _0xb15f21=document[_0x29c962(0x243)](_0x29c962(0x24c));_0xb15f21[_0x29c962(0x224)]=_0x29c962(0x24c),_0xb15f21[_0x29c962(0x1be)]=_0x29c962(0x1fc),_0xb15f21['addEventListener'](_0x29c962(0x25c),saveSettings);const _0x44469f=document[_0x29c962(0x243)]('button');_0x44469f['type']='button',_0x44469f[_0x29c962(0x1be)]='Cancel',_0x44469f[_0x29c962(0x1a8)]('click',closeSettingsModal),_0x214043[_0x29c962(0x205)](_0x44469f),_0x214043[_0x29c962(0x205)](_0xb15f21),_0x4b8ee8['appendChild'](_0x214043),_0x3b16d6[_0x29c962(0x205)](_0x4b8ee8),_0x139cf2[_0x29c962(0x205)](_0x3b16d6),_0x59ee56[_0x29c962(0x205)](_0x139cf2),document[_0x29c962(0x1c1)][_0x29c962(0x205)](_0x59ee56);}function copyResults(){var _0x22905e=a0_0x1ffa75;chrome['storage'][_0x22905e(0x1c9)][_0x22905e(0x1df)](_0x22905e(0x1a1),_0x21404e=>{var _0x28c53c=_0x22905e;const _0x581c2b=_0x21404e[_0x28c53c(0x1a1)]||[];_0x581c2b[_0x28c53c(0x200)]>0x0?navigator[_0x28c53c(0x1bd)]['writeText'](_0x581c2b['join']('\x0a'))[_0x28c53c(0x22e)](()=>{var _0xde6380=_0x28c53c;alert(_0xde6380(0x1f7));}):alert(_0x28c53c(0x257));});}function clearResults(){chrome['storage']['local']['set']({'amazonLinks':[]},()=>{var _0x3721da=a0_0x2b05;alert(_0x3721da(0x23a));});}function closeSettingsModal(){var _0x2f1150=a0_0x1ffa75;const _0x6adb72=document[_0x2f1150(0x21f)]('settings-modal-overlay');_0x6adb72&&_0x6adb72['parentNode']['removeChild'](_0x6adb72);}async function saveSettings(){var _0x57193d=a0_0x1ffa75;const _0x154fc3={};for(const _0x135efa in defaultParams){const _0x35e946=document[_0x57193d(0x21f)](_0x57193d(0x219)+_0x135efa);if(_0x35e946){if(_0x35e946[_0x57193d(0x224)]===_0x57193d(0x1fa))_0x154fc3[_0x135efa]=_0x35e946[_0x57193d(0x208)];else{if(_0x35e946[_0x57193d(0x224)]===_0x57193d(0x222))_0x154fc3[_0x135efa]=Number(_0x35e946['value']);else _0x35e946[_0x57193d(0x224)]==='text'&&(_0x154fc3[_0x135efa]=_0x35e946[_0x57193d(0x1e7)]);}}}await saveParamsToStorage(_0x154fc3),closeSettingsModal();}async function getDomain(){return new Promise((_0x912b6,_0x5d073a)=>{var _0x1831b9=a0_0x2b05;chrome['storage']['local'][_0x1831b9(0x1df)]('domain',_0x1ec941=>{var _0x4726d5=_0x1831b9;chrome[_0x4726d5(0x1d2)][_0x4726d5(0x1d6)]?_0x5d073a(chrome[_0x4726d5(0x1d2)][_0x4726d5(0x1d6)]):_0x912b6(_0x1ec941[_0x4726d5(0x1cc)]||'com');});});}document[a0_0x1ffa75(0x1a8)](a0_0x1ffa75(0x1a6),async()=>{var _0x197d3c=a0_0x1ffa75;skuList=await getSkuListFromLocalStorage(),console[_0x197d3c(0x22b)]('DOM\x20loaded'),addAsinButtons(),addCollectAsinsButton();});