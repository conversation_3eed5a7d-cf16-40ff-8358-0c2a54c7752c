<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Options</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <h1>Settings</h1>

    <div>
        <label>
            <!-- is international -->
            <input type="checkbox" id="is-international">
            Is International

        </label>
    </div>

    <div>
        <label>
            <input type="checkbox" id="doesUserHaveEbaySubscription" checked>
            Does User Have Ebay Subscription
        </label>
    </div>

    <div>
        <label>
            <input type="checkbox" id="isUserTaxExempt" checked>
            Is User Tax Exempt
        </label>
    </div>


    <div id = "item_location_settings_container">
        <h2>Item Location Settings</h2>

        <div>
            <label>
                <input type="checkbox" id="forceItemLocationSwitch" checked>
                Force Item Location
            </label>
        </div>

        <div>
            <label>
                Item Location
                <input type="text" id="itemLocation" placeholder="49132" >
            </label>
        </div>

        <div>
            <label>
                Item Location Country
                <select id="itemLocationCountry">
                    <option value="" disabled selected>Select a country</option>
                    <option value="US">US</option>
                    <option value="UK">UK</option>
                    <option value="CA">CA</option>
                    <option value="AU">AU</option>
                    <option value="DE">DE</option>
                    <option value="FR">FR</option>
                    <option value="IT">IT</option>
                    <option value="ES">ES</option>
                    <option value="NL">NL</option>
                    <option value="BE">BE</option>
                    <option value="CH">CH</option>
                </select>
            </label>
        </div>

        <div>
            <label>
                Item Location City, State
                <input type="text" id="itemLocationCityState" placeholder="Enter state: Kentwood, MI">
            </label>
        </div>



    </div>


    <div id = "return_policy_settings_container">
        <h2>Return Policy Settings</h2>

        <div>
            <label>
                <input type="checkbox" id="forceReturnPolicySwitch" checked>
                Force Return Policy
            </label>
        </div>

        <div>
            <label>
                Return Policy ID
                <input type="number" id="returnPolicyId" placeholder="Enter return policy ID">
            </label>
        </div>

    </div>

    <button id="save">Save</button>

    <br/><br/>


    <div id="listing_settings">
        <div>
            <label>
                <input type="checkbox" id="disableThankYouMessage">
                Disable Thank You Message
            </label>
        </div>
    </div>

    <div id="listing_settings">
        <div>
            <label>
                <input type="checkbox" id="onlyListOnePicture">
                Only List One Picture
            </label>
        </div>


    <div>

        <h1>Gmail Monitor Options</h1>
        <button id="sign-in-button">Sign In with Gmail</button>
        <br /><br />
        <label>
          <input type="checkbox" id="monitor-switch">
          Monitor Gmail
        </label>
            

    </div>


    <div id="listing_settings">
        <h2>Remove Words From Listing</h2>
        <div>
            <label>
                <input type="checkbox" id="removeWordsFromListingSwitch">
                Remove Words From Listing
            </label>
        </div>

        <div>
            <label>
                Words to Remove
                <textarea id="wordsToRemove" placeholder="Enter words to remove from listing"></textarea>
            </label>
        </div>


    <script src="options.js"></script>
</body>
</html>
