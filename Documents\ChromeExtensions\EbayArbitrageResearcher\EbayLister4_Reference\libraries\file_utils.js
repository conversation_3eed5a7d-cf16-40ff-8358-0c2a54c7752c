function a0_0x31f2(){const _0x42a9fb=['91JYYBxK','585570TjbSOI','8651344aDUIlp','blob','29219440VZQIdh','4ZVYdnQ','result','522344ektgmj','4IljsuT','312240QntgjD','4083147FrOBqj','11rUZaaw','readAsDataURL','onloadend','1958577zWkucO'];a0_0x31f2=function(){return _0x42a9fb;};return a0_0x31f2();}function a0_0x43c6(_0x28029b,_0xce833b){const _0x31f296=a0_0x31f2();return a0_0x43c6=function(_0x43c654,_0x497280){_0x43c654=_0x43c654-0xd0;let _0x6f5a4e=_0x31f296[_0x43c654];return _0x6f5a4e;},a0_0x43c6(_0x28029b,_0xce833b);}(function(_0x19667f,_0x184b36){const _0x1fdbc6=a0_0x43c6,_0x47e2b4=_0x19667f();while(!![]){try{const _0x4eb22c=parseInt(_0x1fdbc6(0xd3))/0x1*(-parseInt(_0x1fdbc6(0xd2))/0x2)+-parseInt(_0x1fdbc6(0xd9))/0x3*(parseInt(_0x1fdbc6(0xd0))/0x4)+parseInt(_0x1fdbc6(0xd4))/0x5+parseInt(_0x1fdbc6(0xdb))/0x6*(-parseInt(_0x1fdbc6(0xda))/0x7)+parseInt(_0x1fdbc6(0xdc))/0x8+-parseInt(_0x1fdbc6(0xd5))/0x9+parseInt(_0x1fdbc6(0xde))/0xa*(parseInt(_0x1fdbc6(0xd6))/0xb);if(_0x4eb22c===_0x184b36)break;else _0x47e2b4['push'](_0x47e2b4['shift']());}catch(_0x2c18f0){_0x47e2b4['push'](_0x47e2b4['shift']());}}}(a0_0x31f2,0x9dad5));const fetchURLtoBlob=async _0x215e6b=>{const _0x396d7e=a0_0x43c6;let _0x5c8758=await fetch(_0x215e6b),_0x29bc2a=await _0x5c8758[_0x396d7e(0xdd)]();return _0x29bc2a;},convertBlobTob64=async _0x28465e=>{return new Promise(_0x359939=>{const _0x2c8fc8=a0_0x43c6,_0x316ea7=new FileReader();_0x316ea7[_0x2c8fc8(0xd7)](_0x28465e),_0x316ea7[_0x2c8fc8(0xd8)]=()=>{const _0x101fdf=_0x2c8fc8,_0x46d955=_0x316ea7[_0x101fdf(0xd1)];_0x359939(_0x46d955);};});};