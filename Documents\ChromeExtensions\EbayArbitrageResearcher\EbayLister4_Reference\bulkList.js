function a0_0x14d8(_0x2de730,_0x14a0f4){const _0x3e93a8=a0_0x3e93();return a0_0x14d8=function(_0x14d80e,_0x2b581d){_0x14d80e=_0x14d80e-0x148;let _0x3525ae=_0x3e93a8[_0x14d80e];return _0x3525ae;},a0_0x14d8(_0x2de730,_0x14a0f4);}(function(_0x2d0b42,_0x3228aa){const _0x86fac9=a0_0x14d8,_0x14c074=_0x2d0b42();while(!![]){try{const _0x3b5520=parseInt(_0x86fac9(0x15c))/0x1*(-parseInt(_0x86fac9(0x163))/0x2)+parseInt(_0x86fac9(0x14b))/0x3*(-parseInt(_0x86fac9(0x16f))/0x4)+parseInt(_0x86fac9(0x150))/0x5+-parseInt(_0x86fac9(0x15a))/0x6*(-parseInt(_0x86fac9(0x15e))/0x7)+parseInt(_0x86fac9(0x16b))/0x8*(-parseInt(_0x86fac9(0x160))/0x9)+-parseInt(_0x86fac9(0x162))/0xa+parseInt(_0x86fac9(0x169))/0xb*(parseInt(_0x86fac9(0x15b))/0xc);if(_0x3b5520===_0x3228aa)break;else _0x14c074['push'](_0x14c074['shift']());}catch(_0x4410d6){_0x14c074['push'](_0x14c074['shift']());}}}(a0_0x3e93,0x50a3e));async function bulkListAmazonProducts(){const _0x944aac=a0_0x14d8,_0x1f16ee=await getAmazonLinks();console[_0x944aac(0x149)](_0x944aac(0x148),_0x1f16ee);var _0x538b35=await new Promise(_0x49a639=>{const _0x46329d=_0x944aac;chrome['storage'][_0x46329d(0x164)][_0x46329d(0x159)]([_0x46329d(0x161)],function(_0x1a98f4){const _0x20d20e=_0x46329d;_0x49a639(_0x1a98f4[_0x20d20e(0x161)]||0x0);});});const _0x2baebc=await new Promise(_0x25c406=>{const _0x248852=_0x944aac;chrome[_0x248852(0x14d)][_0x248852(0x164)][_0x248852(0x159)]([_0x248852(0x151)],function(_0x464352){_0x25c406(_0x464352['threadCount']||0x1);});});await processUrls(_0x1f16ee['slice'](_0x538b35),_0x2baebc,_0x538b35),console['log'](_0x944aac(0x16e)+numListed+'\x20items'),console[_0x944aac(0x149)](_0x944aac(0x156)+numFailed+_0x944aac(0x14c));}function a0_0x3e93(){const _0x422048=['itemFailed','49399xftgAa','bulk\x20list\x20mode\x20-\x20amazonLinks[i]:','9EIiXqH','position','5554980FSaVNL','24Buvuyu','local','length','Position\x20saved:','failed','tabs','3443SoSpdZ','remove','4374664KABLwl','bulk\x20list\x20mode\x20-\x20amazonSku:','type','Successfully\x20listed\x20','8mwqTxv','amazonLinks','log','itemListed','237882GryuBk','\x20items','storage','all','message','710815laTMXg','threadCount','bulk\x20list\x20mode\x20-\x20closing\x20amazon\x20tab','bulk\x20list\x20mode\x20-\x20messageType:','set','amazonLinksStatus','Failed\x20to\x20list\x20','push','Amazon\x20links\x20status\x20saved:','get','438uesuTZ','55836rOcsAk','43541RsOjfs'];a0_0x3e93=function(){return _0x422048;};return a0_0x3e93();}const processSingleUrl=async(_0x3aa243,_0x49a644)=>{const _0x263562=a0_0x14d8;var _0x26a7ed=await new Promise(_0x4a5ecc=>{const _0x44e4f1=a0_0x14d8;chrome[_0x44e4f1(0x14d)][_0x44e4f1(0x164)][_0x44e4f1(0x159)]([_0x44e4f1(0x155)],function(_0x9c9021){const _0x260d49=_0x44e4f1;_0x4a5ecc(_0x9c9021[_0x260d49(0x155)]||[]);});});console[_0x263562(0x149)](_0x263562(0x15f),_0x3aa243),await waitForRunStatus(),chrome[_0x263562(0x14d)][_0x263562(0x164)][_0x263562(0x154)]({'position':_0x49a644+0x1},()=>{const _0x486dee=_0x263562;console[_0x486dee(0x149)](_0x486dee(0x166),_0x49a644+0x1);});var _0x2b69e9=await openAmazonItemUrlAndList(_0x3aa243),_0x2b54d8=getTheAsinFromUrl(_0x3aa243);console[_0x263562(0x149)](_0x263562(0x16c),_0x2b54d8);const {message:_0x2a10e4,ebayTabID:_0x3b57d9}=await waitForMessage([_0x263562(0x14a),_0x263562(0x15d)],_0x2b54d8);console[_0x263562(0x149)](_0x263562(0x153),_0x2a10e4[_0x263562(0x16d)]);_0x3b57d9!==_0x2b69e9?(console[_0x263562(0x149)](_0x263562(0x152)),chrome[_0x263562(0x168)][_0x263562(0x16a)](_0x2b69e9)):(console['log']('bulk\x20list\x20mode\x20-\x20amazon\x20tab\x20and\x20ebay\x20tab\x20are\x20same'),chrome['tabs'][_0x263562(0x16a)](_0x2b69e9),console[_0x263562(0x149)]('bulk\x20list\x20mode\x20-\x20amazon\x20tab\x20closed'));if(_0x2a10e4[_0x263562(0x16d)]===_0x263562(0x14a))_0x26a7ed['push']({'url':_0x3aa243,'status':'listed','message':_0x2a10e4[_0x263562(0x14f)]});else _0x2a10e4[_0x263562(0x16d)]==='itemFailed'&&_0x26a7ed[_0x263562(0x157)]({'url':_0x3aa243,'status':_0x263562(0x167),'message':_0x2a10e4['message']});chrome[_0x263562(0x14d)]['local'][_0x263562(0x154)]({'amazonLinksStatus':_0x26a7ed},()=>{const _0x3b7a41=_0x263562;console[_0x3b7a41(0x149)](_0x3b7a41(0x158),_0x26a7ed);});},processUrlsInBatches=async(_0x340b9d,_0x4b0cca,_0x2f189f)=>{const _0x50ae58=a0_0x14d8;while(_0x340b9d[_0x50ae58(0x165)]>0x0){let _0x49ac74=[];for(let _0x1d329f=0x0;_0x1d329f<_0x4b0cca&&_0x340b9d[_0x50ae58(0x165)]>0x0;_0x1d329f++){let _0x504c53=_0x340b9d['shift']();_0x49ac74[_0x50ae58(0x157)](processSingleUrl(_0x504c53,_0x2f189f++));}await Promise[_0x50ae58(0x14e)](_0x49ac74);}},processUrls=async(_0x5e10d2,_0x298164,_0x3264b6)=>{const _0x5a7041=a0_0x14d8;let _0x430964=0x0;const _0x3bb835=()=>_0x5e10d2[_0x430964++],_0x1d3c3b=async()=>{let _0x4496d2;while(_0x4496d2=_0x3bb835()){await processSingleUrl(_0x4496d2,_0x3264b6++);}},_0x48b232=[];for(let _0x152502=0x0;_0x152502<_0x298164;_0x152502++){_0x48b232[_0x5a7041(0x157)](_0x1d3c3b());}await Promise[_0x5a7041(0x14e)](_0x48b232);};