/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // For search-specific messages, let the search handler deal with them
      if (isSearchPage() && isSearchSpecificMessage(message.action)) {
        console.log('🔄 Search-specific message, letting search handler handle it:', message.action);
        // Don't respond - let the search handler handle it
        return false;
      }
      
      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
