/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);

        // Use async pattern to keep message port open
        (async () => {
          try {
            const listings = document.querySelectorAll('.s-item');
            console.log(`🔍 Universal handler: Found ${listings.length} listings`);

            const products = [];
            for (let i = 0; i < Math.min(listings.length, 20); i++) {
              const listing = listings[i];

              // Skip if this is a navigation or ad element
              if (listing.classList.contains('s-item--navigation') ||
                  listing.classList.contains('s-item--ad') ||
                  listing.querySelector('.s-item__title')?.textContent?.includes('Shop on eBay')) {
                console.log(`⏭️ Skipping navigation/ad element ${i}`);
                continue;
              }

              const titleEl = listing.querySelector('.s-item__title');
              const priceEl = listing.querySelector('.s-item__price');
              const linkEl = listing.querySelector('.s-item__link');

              if (titleEl && priceEl) {
                const title = titleEl.textContent.trim();
                const priceText = priceEl.textContent.trim();
                const link = linkEl ? linkEl.href : '';

                // Skip if title is generic or empty
                if (!title || title === 'Shop on eBay' || title.length < 5) {
                  console.log(`⏭️ Skipping invalid title: "${title}"`);
                  continue;
                }

                // Extract price number
                const priceMatch = priceText.match(/[\d,]+\.?\d*/);
                const price = priceMatch ? parseFloat(priceMatch[0].replace(/,/g, '')) : 0;

                // Skip if no valid price
                if (price <= 0) {
                  console.log(`⏭️ Skipping invalid price: "${priceText}"`);
                  continue;
                }

                // Extract item ID
                let itemId = '';
                if (link) {
                  const itemIdMatch = link.match(/\/itm\/(\d+)/);
                  if (itemIdMatch) {
                    itemId = itemIdMatch[1];
                  }
                }

                console.log(`📦 Extracted product ${products.length}: "${title}" - ${priceText}`);

                products.push({
                  title: title,
                  price: price,
                  priceText: priceText,
                  link: link,
                  itemId: itemId,
                  source: 'universal-handler',
                  index: products.length,
                  timestamp: Date.now()
                });
              }
            }

            console.log(`✅ Universal handler: Extracted ${products.length} products`);
            sendResponse({
              success: true,
              products: products,
              handler: 'universal-ebay-handler',
              action: message.action,
              mode: 'universal-scraping'
            });
          } catch (error) {
            console.error('❌ Universal handler scraping failed:', error);
            sendResponse({
              success: false,
              error: 'Universal scraping failed: ' + error.message,
              handler: 'universal-ebay-handler'
            });
          }
        })();

        return true; // Keep message channel open
      }

      // For other search-specific messages, let the search handler deal with them
      if (isSearchPage() && isSearchSpecificMessage(message.action)) {
        console.log('🔄 Universal handler: Other search-specific message, letting search handler handle it:', message.action);
        // Don't respond - let the search handler handle it
        return false;
      }
      


      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
