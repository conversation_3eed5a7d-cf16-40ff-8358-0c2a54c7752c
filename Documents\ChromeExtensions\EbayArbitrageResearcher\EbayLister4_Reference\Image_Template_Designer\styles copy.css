body, html {
    height: 100%;
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

#canvasContainer, #testCanvasContainer {
    position: relative;
    margin: auto;
    border: 3px solid #ccc;
    width: 1504px; /* Adjusted width to account for border */
    height: 1504px; /* Adjusted height to account for border */
    background-color: white; /* Background color for better visibility */
}

#canvasContainer {
    transform: scale(0.5);
    transform-origin: 0 0;
}

#testCanvasContainer {
    transform: scale(0.5);
    transform-origin: 0 0;
}


canvas {
    display: block;
}

#controls {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    border: 1px solid #ccc;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

@media (max-width: 1500px) {
    #controls {
        position: static;
        transform: none;
        margin-bottom: 2px;
        width: auto;
        box-shadow: none;
    }

    #canvasContainer, #testCanvasContainer {
        margin: auto;
    }
}


button {
    display: block;
    margin-bottom: 10px;
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    color: #fff;
    background-color: #007BFF;
}

button:hover {
    background-color: #0056b3;
}

input[type="file"] {
    margin-bottom: 20px;
}


#backToEditor, #downloadImage {
    position: absolute;
    top: 20px;
}

#backToEditor {
    left: 20px;
}

#downloadImage {
    left: 200px; /* Adjust as necessary */
}