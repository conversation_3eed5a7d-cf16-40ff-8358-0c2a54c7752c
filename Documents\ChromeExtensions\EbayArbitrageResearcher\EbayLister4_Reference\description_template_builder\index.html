<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>eBay Template Editor</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="container">
    <h1>eBay Listing Template Editor</h1>

    <!-- Instructions -->
    <div class="instructions-section">
      <h2>⚠️ Template Instructions</h2>
      <p>Your template must include these placeholders exactly as shown:</p>
      <ul>
        <li><strong>{{Product_Title}}</strong> - Product's title</li>
        <li><strong>{{Product_Carousel}}</strong> - Product images carousel</li>
        <li><strong>{{Product_Description}}</strong> - Product description content</li>
      </ul>
      <p><strong>Important:</strong> The placeholders will be automatically replaced with real product data when listing. Don't remove or alter these exact placeholders.</p>
    </div>

    <!-- AI Description Section -->
    <div class="ai-section">
      <h2>🪄 AI Description Generator</h2>
      <label for="descPromptBox">Customize how ChatGPT generates your description:</label>
      <textarea id="descPromptBox" placeholder="Enter AI prompt..."></textarea>

      <div class="ai-btn-row">
        <button id="generateAiDescBtn">✨ Generate AI Description</button>
        <button id="resetPromptBtn">🔄 Reset Prompt</button>
      </div>
    </div>

    <!-- Editor Mode Buttons -->
    <div class="mode-buttons">
      <button id="liveModeBtn" class="active">🖥️ Live Editor</button>
      <button id="codeModeBtn">📄 HTML Editor</button>
    </div>

    <!-- Editor Area -->
    <div class="editor-wrapper">
      <div id="liveEditor" contenteditable="true"></div>
      <textarea id="codeEditor"></textarea>
    </div>

    <!-- Template Action Buttons -->
    <div class="action-btn-row">
      <button id="fillDummyBtn">📝 Fill Dummy Data</button>
      <button id="saveTemplateBtn" disabled style="display: none;">💾 Save Template</button>
      <button id="resetBtn">♻️ Reset Template</button>
    </div>
  </div>

  <div id="previewModal" class="modal" style="display: none;">
    <div class="modal-content">
      <button id="closePreviewModal">Close</button>
      <div id="modalPreviewContent" contenteditable="true" style="margin-top: 20px;"></div>
    </div>
  </div>
  
  

  <script src="../libraries/post_to_server_utils.js"></script>
  <script src="../content/ebay/ebay.desc_function.js"></script>
  <script src="script.js"></script>
</body>
</html>
