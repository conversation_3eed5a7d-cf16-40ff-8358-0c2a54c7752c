var a0_0x19d3e3=a0_0xbd90;function a0_0x49e4(){var _0x4e1b46=['end\x20trying\x20eBay\x20identify\x20product\x20-\x20selecting\x20new\x20condition\x205','condition\x20exists','trying\x20eBay\x20identify\x20product\x20-\x20selecting\x20new\x20condition\x201','tab','.prelist-radix__next-action','condition','252hBEIoR','productData','eBay\x20identify\x20product\x20-\x20click\x20continue\x20to\x20listing','identify','clicked_continued_without_matching','error','type','href','11459490OHXjsE','4468671IlIIQb','eBay\x20identify\x20product\x20-\x20choose\x20category','title','eBay\x20identify\x20product\x20-\x20continue\x20without\x20matching\x20#0','From\x20a\x20content\x20script:','eBay\x20identify\x20product\x20-\x20clicked\x20continue\x20#3','eBay\x20identify\x20product\x20-\x20continue\x20without\x20matching\x20#4','Ebay\x20Pre\x20List\x20Identify\x20initialized','product','log','658617nqTFCD','identify_product','22158DgJZQX','eBay\x20identify\x20product\x20-\x20clicked\x20continue\x20#1','trying\x20eBay\x20identify\x20product\x20-\x20selected\x20new\x20condition\x202','runtime','17564kJBZYn','sendMessage','error\x20test','Condition\x20Grading\x20Error,\x20not\x20supported','click','querySelector','url','152vEajUM','addListener','470opejaq','location','767184JnqSyV','identify_product\x20begins','27198ZFIIrZ'];a0_0x49e4=function(){return _0x4e1b46;};return a0_0x49e4();}function a0_0xbd90(_0x548481,_0xd75461){var _0x49e4ea=a0_0x49e4();return a0_0xbd90=function(_0xbd9031,_0x5ba357){_0xbd9031=_0xbd9031-0x186;var _0x19254b=_0x49e4ea[_0xbd9031];return _0x19254b;},a0_0xbd90(_0x548481,_0xd75461);}(function(_0x5a4c58,_0x52d2ef){var _0x23fbc4=a0_0xbd90,_0x5a0701=_0x5a4c58();while(!![]){try{var _0x536ba1=parseInt(_0x23fbc4(0x196))/0x1+parseInt(_0x23fbc4(0x1a7))/0x2+parseInt(_0x23fbc4(0x18c))/0x3+parseInt(_0x23fbc4(0x19c))/0x4*(-parseInt(_0x23fbc4(0x1a5))/0x5)+-parseInt(_0x23fbc4(0x1a9))/0x6*(parseInt(_0x23fbc4(0x1b0))/0x7)+-parseInt(_0x23fbc4(0x1a3))/0x8*(parseInt(_0x23fbc4(0x198))/0x9)+-parseInt(_0x23fbc4(0x18b))/0xa;if(_0x536ba1===_0x52d2ef)break;else _0x5a0701['push'](_0x5a0701['shift']());}catch(_0x226aa2){_0x5a0701['push'](_0x5a0701['shift']());}}}(a0_0x49e4,0xba4d9),console[a0_0x19d3e3(0x195)](a0_0x19d3e3(0x193)),onPageLoadAndStableNotifyBackground());var sentMessage=![];chrome['runtime']['onMessage'][a0_0x19d3e3(0x1a4)](async(_0x1f16fe,_0x44f8f5,_0x48657)=>{var _0xbb5e0f=a0_0x19d3e3;console[_0xbb5e0f(0x195)](_0x44f8f5[_0xbb5e0f(0x1ad)]?_0xbb5e0f(0x190)+_0x44f8f5[_0xbb5e0f(0x1ad)][_0xbb5e0f(0x1a2)]:'From\x20the\x20extension\x20request.type\x20ebay.js'+_0x1f16fe[_0xbb5e0f(0x189)]);if(_0x1f16fe[_0xbb5e0f(0x189)]===_0xbb5e0f(0x197)&&window[_0xbb5e0f(0x1a6)][_0xbb5e0f(0x18a)]['includes'](_0xbb5e0f(0x186))){document[_0xbb5e0f(0x18e)]='eBay\x20identify\x20product',await onPageLoadAndStable(),_0x48657({'type':'identify_product','message':_0xbb5e0f(0x1a8)}),console[_0xbb5e0f(0x195)](_0xbb5e0f(0x1a8));var _0x4c1e03=_0x1f16fe[_0xbb5e0f(0x1b1)];console[_0xbb5e0f(0x195)](_0xbb5e0f(0x194),_0x4c1e03);var _0x36ca3c=document[_0xbb5e0f(0x1a1)]('[name=\x22condition\x22]');_0x36ca3c&&(console[_0xbb5e0f(0x195)](_0xbb5e0f(0x1ab)),console['log'](_0xbb5e0f(0x1af),_0x36ca3c),await selectNewCondition(),!sentMessage&&chrome['runtime']['sendMessage']({'type':_0xbb5e0f(0x187),'productData':_0x4c1e03},function(_0x4e3d64){var _0x47ef9a=_0xbb5e0f;document[_0x47ef9a(0x18e)]=_0x47ef9a(0x18f),sentMessage=!![];}),continueToListing());await chooseCategoryIfExists(_0x4c1e03),document['title']=_0xbb5e0f(0x18d);var _0x2164aa=document[_0xbb5e0f(0x1a1)](_0xbb5e0f(0x1ae));_0x2164aa&&(document[_0xbb5e0f(0x1a1)](_0xbb5e0f(0x1ae))['addEventListener'](_0xbb5e0f(0x1a0),function(){var _0x48e4e2=_0xbb5e0f;document[_0x48e4e2(0x18e)]=_0x48e4e2(0x199),!sentMessage&&chrome[_0x48e4e2(0x19b)][_0x48e4e2(0x19d)]({'type':_0x48e4e2(0x187),'productData':_0x4c1e03},function(_0x259286){var _0x21c47f=_0x48e4e2;document[_0x21c47f(0x18e)]='eBay\x20identify\x20product\x20-\x20continue\x20without\x20matching\x20#1',sentMessage=!![];});}),clickContinueWithoutMatch());document[_0xbb5e0f(0x18e)]='eBay\x20identify\x20product\x20-\x20clicked\x20continue\x20without\x20matching\x20\x20#2';try{await wait(0x7d0),document['title']=_0xbb5e0f(0x1ac),console[_0xbb5e0f(0x195)](_0xbb5e0f(0x1ac)),await selectNewCondition(),document[_0xbb5e0f(0x18e)]=_0xbb5e0f(0x19a),console[_0xbb5e0f(0x195)](_0xbb5e0f(0x19a));}catch(_0x464ea0){console[_0xbb5e0f(0x195)](_0xbb5e0f(0x188),_0x464ea0);!sentMessage&&chrome[_0xbb5e0f(0x19b)][_0xbb5e0f(0x19d)]({'type':'clicked_continued_without_matching','productData':_0x4c1e03},function(_0x4f1233){var _0x457391=_0xbb5e0f;document[_0x457391(0x18e)]='eBay\x20identify\x20product\x20-\x20continue\x20without\x20matching\x20#3',sentMessage=!![];});clickContinueWithoutMatch(),document['title']=_0xbb5e0f(0x191),await wait(0x7d0);try{await selectNewCondition();}catch(_0x45c312){console[_0xbb5e0f(0x195)](_0xbb5e0f(0x188),_0x45c312);var _0x4d5215=document[_0xbb5e0f(0x1a1)]('.prelist-radix__body-container.prelist-radix__condition-grading');_0x4d5215&&(console[_0xbb5e0f(0x195)](_0xbb5e0f(0x19f)),chrome[_0xbb5e0f(0x19b)]['sendMessage']({'type':'itemFailed','sku':'error','message':_0xbb5e0f(0x19f)}));}}await wait(0x7d0);!sentMessage&&chrome[_0xbb5e0f(0x19b)][_0xbb5e0f(0x19d)]({'type':_0xbb5e0f(0x187),'productData':_0x4c1e03},function(_0xf7f588){var _0x57ee22=_0xbb5e0f;document[_0x57ee22(0x18e)]=_0x57ee22(0x192),sentMessage=!![];});try{console[_0xbb5e0f(0x195)]('end\x20trying\x20eBay\x20identify\x20product\x20-\x20selecting\x20new\x20condition\x203'),await ClickContinueToListing(),document[_0xbb5e0f(0x18e)]=_0xbb5e0f(0x1b2),await wait(0x7d0),console[_0xbb5e0f(0x195)]('end\x20trying\x20eBay\x20identify\x20product\x20-\x20selecting\x20new\x20condition\x204'),await selectNewCondition(),await wait(0x7d0),console['log'](_0xbb5e0f(0x1aa)),await ClickContinueToListing();}catch(_0x519b3e){try{await ClickContinueToListing(),await wait(0x7d0);}catch(_0xfb1c21){console[_0xbb5e0f(0x195)](_0xbb5e0f(0x19e),_0xfb1c21);}try{await selectNewCondition(),await wait(0x7d0);}catch(_0x3ec8d7){console[_0xbb5e0f(0x195)](_0xbb5e0f(0x19e),_0x3ec8d7),await wait(0x7d0);try{await selectNewCondition(),await wait(0x7d0);}catch(_0x38b323){console['log']('error\x20test',_0x38b323);}}await ClickContinueToListing();}}});