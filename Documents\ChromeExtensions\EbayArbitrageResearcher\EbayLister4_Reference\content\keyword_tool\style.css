#keyword_modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

#keyword_modal_content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */

    text-align: center;  /* Center the content */
}

#keyword_modal_close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

#keyword_modal_close:hover,
#keyword_modal_close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}


#keyword_modal_body textarea,
#keyword_modal_footer button {
    display: block;
    margin: 0 auto;  /* Center the elements */
}




.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .spinner {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 5px solid #fff;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  #overlay_text {
    font-weight: bold;
    font-size: 50px;
    color: white;
  }





  .search {
    background-color: lightblue;
    color: white;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    margin: 4px 2px;
    cursor: pointer;
  }
  
  .search:hover {
    background-color: blue;
  }


  
  .search:active {
    background-color: darkblue;
  }

  #scrape_keywords_button {
    background-color: lightgreen;
    color: white;
    font-weight: bold;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
  }
  
  #scrape_keywords_button:hover {
    background-color: green;
  }
  
  #scrape_keywords_button:active {
    background-color: darkgreen;
  }