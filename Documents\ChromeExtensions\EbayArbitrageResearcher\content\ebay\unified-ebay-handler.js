/**
 * ✅ UNIFIED EBAY HANDLER - Single script approach for reliable message handling
 * Combines all-ebay-handler and enhanced-integration functionality
 * Loads at document_idle to ensure all dependencies are available
 */

(function() {
  'use strict';
  
  console.log('🌐 Unified eBay Handler loading...');
  
  // Global state
  let isInitialized = false;
  let initializationAttempts = 0;
  const maxInitAttempts = 50;
  let dependencies = {};
  
  /**
   * Check if current page is an eBay search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Initialize all dependencies
   */
  async function initializeDependencies() {
    console.log('🔄 Initializing dependencies...');
    
    const requiredDeps = {
      eventBus: 'ArbitrageEventBus',
      errorHandler: 'ArbitrageErrorHandler', 
      rateLimiter: 'ArbitrageRateLimiter',
      profitCalculator: 'ArbitrageProfitCalculator',
      scanner: 'EbaySearchScanner'
    };
    
    const missing = [];
    
    for (const [key, globalName] of Object.entries(requiredDeps)) {
      if (typeof window[globalName] !== 'undefined') {
        try {
          // Instantiate the class
          dependencies[key] = new window[globalName]();
          console.log(`✅ ${globalName} instantiated successfully`);
        } catch (error) {
          console.error(`❌ Failed to instantiate ${globalName}:`, error);
          missing.push(globalName);
        }
      } else {
        missing.push(globalName);
      }
    }
    
    if (missing.length > 0) {
      throw new Error(`Missing dependencies: ${missing.join(', ')}`);
    }
    
    // Validate that all dependencies have required methods
    const validations = [
      ['errorHandler.handleError', typeof dependencies.errorHandler?.handleError === 'function'],
      ['scanner.scanCurrentPage', typeof dependencies.scanner?.scanCurrentPage === 'function'],
      ['eventBus.emit', typeof dependencies.eventBus?.emit === 'function'],
      ['profitCalculator.calculateProfit', typeof dependencies.profitCalculator?.calculateProfit === 'function']
    ];
    
    for (const [name, isValid] of validations) {
      if (!isValid) {
        throw new Error(`${name} is not available or not a function`);
      }
    }
    
    console.log('✅ All dependencies validated successfully');
    return true;
  }
  
  /**
   * Attempt initialization with retry logic
   */
  async function attemptInitialization() {
    initializationAttempts++;
    console.log(`🔍 Initialization attempt ${initializationAttempts}/${maxInitAttempts}`);
    
    try {
      await initializeDependencies();
      isInitialized = true;
      console.log('✅ Unified eBay Handler initialized successfully');
      
      // Emit ready event
      if (dependencies.eventBus) {
        dependencies.eventBus.emit('integration:ready', {
          timestamp: Date.now(),
          handler: 'unified-ebay-handler'
        });
      }
      
      return true;
    } catch (error) {
      console.log(`⏳ Initialization attempt ${initializationAttempts} failed:`, error.message);
      
      if (initializationAttempts < maxInitAttempts) {
        // Retry after a delay
        setTimeout(attemptInitialization, 200);
      } else {
        console.error('❌ Unified eBay Handler initialization failed after maximum attempts');
      }
      
      return false;
    }
  }
  
  /**
   * Handle messages from service worker
   */
  async function handleMessage(message, sender, sendResponse) {
    console.log('📨 Unified eBay Handler received message:', message);
    
    try {
      // Handle ping requests immediately
      if (message.action === 'ping') {
        console.log('🏓 Responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'unified-ebay-handler',
          url: window.location.href,
          initialized: isInitialized
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Confirming content script ready');
        sendResponse({
          success: true,
          ready: isInitialized,
          handler: 'unified-ebay-handler',
          dependencies: Object.keys(dependencies)
        });
        return true;
      }
      
      // For other messages, ensure we're initialized
      if (!isInitialized) {
        console.log('⏳ Handler not initialized yet, waiting...');
        
        // Wait for initialization with timeout
        const timeout = 10000; // 10 seconds
        const startTime = Date.now();
        
        while (!isInitialized && (Date.now() - startTime) < timeout) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        if (!isInitialized) {
          sendResponse({
            success: false,
            error: 'Handler initialization timeout',
            action: message.action,
            waitedMs: Date.now() - startTime
          });
          return true;
        }
      }
      
      // Handle scraping requests
      if (message.action === 'startScraping') {
        console.log('🔍 Starting eBay scraping...');
        
        try {
          const results = await dependencies.scanner.scanCurrentPage(message.config || {});
          console.log('✅ Scraping completed successfully:', results);
          
          sendResponse({
            success: true,
            data: results,
            action: message.action
          });
        } catch (error) {
          console.error('❌ Scraping failed:', error);
          sendResponse({
            success: false,
            error: error.message,
            action: message.action
          });
        }
        
        return true;
      }
      
      // Handle other actions
      console.log('❓ Unhandled message action:', message.action);
      sendResponse({
        success: false,
        error: 'Unhandled message action',
        action: message.action,
        handler: 'unified-ebay-handler'
      });
      
    } catch (error) {
      console.error('❌ Message handling error:', error);
      sendResponse({
        success: false,
        error: 'Message handling crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true; // Keep message channel open
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Start initialization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', attemptInitialization);
  } else {
    // DOM already ready, start immediately
    attemptInitialization();
  }
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'unified-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      initialized: isInitialized
    }).catch(error => {
      console.log('📡 Service worker not ready yet, will retry later');
    });
  }
  
  // Notify immediately and after initialization
  notifyServiceWorker();
  
  // Set up periodic notification until initialized
  const notificationInterval = setInterval(() => {
    if (isInitialized) {
      clearInterval(notificationInterval);
    }
    notifyServiceWorker();
  }, 1000);
  
  // Global status for debugging
  window.unifiedEbayHandler = {
    isInitialized: () => isInitialized,
    getAttempts: () => initializationAttempts,
    getDependencies: () => Object.keys(dependencies),
    restart: attemptInitialization
  };
  
  console.log('✅ Unified eBay Handler setup complete');
  
})();
