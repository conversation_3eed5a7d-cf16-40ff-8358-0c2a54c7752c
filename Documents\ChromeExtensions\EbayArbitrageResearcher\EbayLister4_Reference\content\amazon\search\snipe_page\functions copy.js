const a0_0x57b2bb=a0_0x4548;(function(_0x2a5742,_0x161576){const _0x29c843=a0_0x4548,_0x5413cd=_0x2a5742();while(!![]){try{const _0x42f355=-parseInt(_0x29c843(0x26d))/0x1+parseInt(_0x29c843(0x244))/0x2*(-parseInt(_0x29c843(0x263))/0x3)+parseInt(_0x29c843(0x235))/0x4+parseInt(_0x29c843(0x1ec))/0x5+parseInt(_0x29c843(0x27e))/0x6*(parseInt(_0x29c843(0x1f3))/0x7)+parseInt(_0x29c843(0x27c))/0x8*(-parseInt(_0x29c843(0x226))/0x9)+parseInt(_0x29c843(0x23f))/0xa*(parseInt(_0x29c843(0x1f7))/0xb);if(_0x42f355===_0x161576)break;else _0x5413cd['push'](_0x5413cd['shift']());}catch(_0x11bf4b){_0x5413cd['push'](_0x5413cd['shift']());}}}(a0_0x323a,0x361f6),console[a0_0x57b2bb(0x284)](a0_0x57b2bb(0x210)));async function activateSnipeMode(_0x491bce){const _0x37bd59=a0_0x57b2bb;console[_0x37bd59(0x284)]('snipedItemData:\x20',_0x491bce),addBanner(_0x491bce[_0x37bd59(0x2a7)],_0x491bce['price'],_0x491bce['asin'],_0x491bce['image']);var _0x51a438=document[_0x37bd59(0x204)](_0x37bd59(0x260));for(let _0x35739f=0x0;_0x35739f<_0x51a438[_0x37bd59(0x20b)];_0x35739f++){let _0x5b9a71=_0x51a438[_0x35739f];addCustomTitleTextBox(_0x5b9a71),modifyAddToCartButton(_0x5b9a71),movePriceToTop(_0x5b9a71),addCustomEditablePrice(_0x5b9a71,_0x37bd59(0x283)),addCleaningIcon(_0x5b9a71);}}function wrapLetters(_0x2d7b8f){const _0x2a00e0=a0_0x57b2bb,_0xcca5c2=_0x2d7b8f[_0x2a00e0(0x203)],_0x3ddcf3=_0xcca5c2[_0x2a00e0(0x1f0)](''),_0x4a12e0=_0x3ddcf3[_0x2a00e0(0x23a)]((_0x564627,_0x41fa53)=>{const _0x15140a=_0x2a00e0;if(_0x564627==='\x20')return _0x15140a(0x282)+_0x41fa53+_0x15140a(0x275);return'<span\x20class=\x22letter\x22\x20style=\x22--animation-order:'+_0x41fa53+'\x22>'+_0x564627+_0x15140a(0x21e);})['join']('');_0x2d7b8f[_0x2a00e0(0x21c)]=_0x4a12e0;}async function getCleanTitle2(){const _0x6d9d11=a0_0x57b2bb,_0x43a41b=Math[_0x6d9d11(0x240)](Math[_0x6d9d11(0x1fa)]()*0xbb8)+0x7d0;return new Promise(_0x3bbd8a=>{setTimeout(()=>{const _0x2e1e06=a0_0x4548;_0x3bbd8a(_0x2e1e06(0x26c));},_0x43a41b);});}async function addCleaningIcon(_0x11ac00){const _0x4de451=a0_0x57b2bb,_0x48b3a2=_0x11ac00[_0x4de451(0x232)](_0x4de451(0x280));if(!_0x48b3a2){console[_0x4de451(0x270)](_0x4de451(0x252),_0x11ac00);return;}if(_0x48b3a2[_0x4de451(0x232)](_0x4de451(0x26a))){console[_0x4de451(0x270)](_0x4de451(0x239),_0x11ac00);return;}const _0x48b4b1=document[_0x4de451(0x253)](_0x4de451(0x27f));_0x48b4b1[_0x4de451(0x246)]=_0x4de451(0x2a8),_0x48b4b1[_0x4de451(0x29f)](_0x4de451(0x2a7),_0x4de451(0x294)),_0x48b4b1[_0x4de451(0x29f)](_0x4de451(0x220),_0x4de451(0x297)),_0x48b4b1[_0x4de451(0x29f)](_0x4de451(0x225),_0x4de451(0x294));var _0xe750f9=await createSpinner('sniper-mark-single');console[_0x4de451(0x284)]('spinner:\x20',_0xe750f9),_0x48b4b1[_0x4de451(0x274)](_0xe750f9);const _0x3ed35d=_0x48b3a2[_0x4de451(0x232)]('.char-counter');if(_0x3ed35d){const _0x2a7a4f=_0x48b3a2[_0x4de451(0x232)](_0x4de451(0x249));_0x2a7a4f?_0x2a7a4f[_0x4de451(0x245)](_0x48b4b1,_0x3ed35d):_0x48b3a2[_0x4de451(0x245)](_0x48b4b1,_0x3ed35d);}else _0x48b3a2[_0x4de451(0x274)](_0x48b4b1);_0x48b4b1[_0x4de451(0x277)](_0x4de451(0x238),async function(_0x5c0ddc){const _0x1cc530=_0x4de451;var _0x44f783=_0x5c0ddc[_0x1cc530(0x215)][_0x1cc530(0x20a)],_0x2289fd=_0x44f783['querySelector']('.sniper-mark');startSpinSingleElement(_0x2289fd),_0x48b4b1['classList'][_0x1cc530(0x222)](_0x1cc530(0x28d)),_0x48b4b1[_0x1cc530(0x234)][_0x1cc530(0x28f)]=_0x1cc530(0x21f);const _0x2bb047=_0x48b3a2[_0x1cc530(0x232)](_0x1cc530(0x23d));if(!_0x2bb047){console['warn'](_0x1cc530(0x24b),_0x11ac00);return;}!_0x2bb047['classList'][_0x1cc530(0x1ea)](_0x1cc530(0x262))&&(wrapLetters(_0x2bb047),_0x2bb047[_0x1cc530(0x1f2)][_0x1cc530(0x222)](_0x1cc530(0x262)));_0x2bb047[_0x1cc530(0x1f2)][_0x1cc530(0x222)](_0x1cc530(0x24c));var _0x3d136a=extractTitleFromCard(_0x11ac00),_0x1b337c=_0x2bb047[_0x1cc530(0x203)],_0x2cdf38=await getCleanTitle2();startMagicalAnimation(_0x2bb047,_0x2cdf38,_0x48b3a2,_0x48b4b1),stopSpinSingleElement(_0x2289fd),startFlashSingleElement(_0x2289fd),await new Promise(_0x16450b=>setTimeout(_0x16450b,0x7d0)),stopFlashSingleElement(_0x2289fd);});}function startMagicalAnimation(_0x34dc16,_0x5881dc,_0x3bfe99,_0x19a571){const _0x238d38=a0_0x57b2bb;_0x34dc16[_0x238d38(0x1f2)]['remove']('floating-loop');const _0x4d750f=Array[_0x238d38(0x214)](_0x34dc16['querySelectorAll'](_0x238d38(0x243)));_0x34dc16[_0x238d38(0x234)][_0x238d38(0x1ed)]='relative',_0x4d750f[_0x238d38(0x29c)](_0x147ec3=>{const _0x432a4b=_0x238d38,_0x2b56a4=_0x147ec3['getBoundingClientRect'](),_0x10096e=_0x34dc16[_0x432a4b(0x299)](),_0x71ffcf=_0x2b56a4[_0x432a4b(0x269)]-_0x10096e[_0x432a4b(0x269)],_0x4aea7c=_0x2b56a4[_0x432a4b(0x2a4)]-_0x10096e[_0x432a4b(0x2a4)];_0x147ec3['style'][_0x432a4b(0x1ed)]=_0x432a4b(0x279),_0x147ec3['style'][_0x432a4b(0x269)]=_0x71ffcf+'px',_0x147ec3[_0x432a4b(0x234)][_0x432a4b(0x2a4)]=_0x4aea7c+'px',_0x147ec3[_0x432a4b(0x234)][_0x432a4b(0x1f4)]='0',_0x147ec3[_0x432a4b(0x211)]=_0x71ffcf,_0x147ec3[_0x432a4b(0x24d)]=_0x4aea7c,_0x147ec3[_0x432a4b(0x1eb)]=![];}),_0x4d750f[_0x238d38(0x29c)](_0x8eea60=>{const _0x240911=_0x238d38,_0xf1845a=(Math[_0x240911(0x1fa)]()-0.5)*0xc8,_0x13cb96=(Math[_0x240911(0x1fa)]()-0.5)*0xc8,_0x1fad7f=_0x8eea60[_0x240911(0x211)]+_0xf1845a,_0x7fe754=_0x8eea60[_0x240911(0x24d)]+_0x13cb96;_0x8eea60['style'][_0x240911(0x273)]=_0x240911(0x288),_0x8eea60['style']['left']=_0x1fad7f+'px',_0x8eea60[_0x240911(0x234)][_0x240911(0x2a4)]=_0x7fe754+'px',_0x8eea60[_0x240911(0x211)]=_0x1fad7f,_0x8eea60[_0x240911(0x24d)]=_0x7fe754;}),setTimeout(()=>{rearrangeLetters(_0x34dc16,_0x5881dc,_0x3bfe99,_0x19a571);},0x3e8);}function rearrangeLetters(_0x505731,_0x23ed08,_0x36e9ed,_0x3a52d2){const _0x13796f=a0_0x57b2bb,_0x23c0fb=Array[_0x13796f(0x214)](_0x505731[_0x13796f(0x204)](_0x13796f(0x243))),_0x6c37fa=_0x23ed08[_0x13796f(0x1f0)](''),_0xd396a2=[];_0x6c37fa[_0x13796f(0x29c)](_0x380fb8=>{const _0x303337=_0x13796f;let _0x10f99b=null;for(const _0x26c3b9 of _0x23c0fb){if(!_0x26c3b9[_0x303337(0x1eb)]&&_0x26c3b9[_0x303337(0x203)]['trim']()===_0x380fb8){_0x10f99b=_0x26c3b9,_0x26c3b9[_0x303337(0x1eb)]=!![];break;}}_0xd396a2[_0x303337(0x23c)]({'char':_0x380fb8,'assignedOldLetter':_0x10f99b,'position':null});});const _0xc0ea25=document['createElement'](_0x13796f(0x27f));_0xc0ea25['style'][_0x13796f(0x251)]='hidden',_0xc0ea25[_0x13796f(0x234)][_0x13796f(0x1ed)]=_0x13796f(0x279),_0xc0ea25[_0x13796f(0x234)][_0x13796f(0x2a4)]=_0x13796f(0x1fd),_0xc0ea25[_0x13796f(0x234)][_0x13796f(0x269)]=_0x13796f(0x1fd),_0xc0ea25['style'][_0x13796f(0x22e)]=_0x13796f(0x276),_0x505731[_0x13796f(0x274)](_0xc0ea25);const _0x1af8b5=_0x6c37fa[_0x13796f(0x23a)]((_0x28902b,_0x23e2f6)=>{const _0x4e9bb9=_0x13796f;if(_0x28902b==='\x20')return _0x4e9bb9(0x1f5)+_0x23e2f6+_0x4e9bb9(0x275);return _0x4e9bb9(0x1f5)+_0x23e2f6+'\x22>'+_0x28902b+_0x4e9bb9(0x21e);})['join']('');_0xc0ea25['innerHTML']=_0x1af8b5;const _0x318855=Array['from'](_0xc0ea25[_0x13796f(0x204)](_0x13796f(0x267)));_0x318855[_0x13796f(0x29c)]((_0x49e9f1,_0xa65ea5)=>{const _0x1fa7d4=_0x13796f,_0x17a5c9=_0x49e9f1[_0x1fa7d4(0x299)](),_0xc2a79c=_0x505731[_0x1fa7d4(0x299)](),_0x3cf18e=_0x17a5c9['left']-_0xc2a79c[_0x1fa7d4(0x269)],_0x661dd=_0x17a5c9[_0x1fa7d4(0x2a4)]-_0xc2a79c[_0x1fa7d4(0x2a4)];_0xd396a2[_0xa65ea5][_0x1fa7d4(0x1ed)]={'left':_0x3cf18e,'top':_0x661dd};}),_0x505731[_0x13796f(0x27a)](_0xc0ea25),_0xd396a2[_0x13796f(0x29c)]((_0x37dab2,_0x1f9f97)=>{const _0x547432=_0x13796f;if(_0x37dab2[_0x547432(0x28c)]){const _0xb946eb=_0x37dab2[_0x547432(0x28c)];_0xb946eb[_0x547432(0x234)][_0x547432(0x273)]=_0x547432(0x290)+_0x1f9f97*0.05+'s,\x20top\x201.5s\x20ease-in-out\x20'+_0x1f9f97*0.05+'s',_0xb946eb[_0x547432(0x234)][_0x547432(0x269)]=_0x37dab2['position']['left']+'px',_0xb946eb[_0x547432(0x234)][_0x547432(0x2a4)]=_0x37dab2['position'][_0x547432(0x2a4)]+'px';}}),_0x23c0fb['forEach'](_0x527bf4=>{const _0x529437=_0x13796f;!_0x527bf4[_0x529437(0x1eb)]&&(setTimeout(()=>{const _0x1bc24b=_0x529437;_0x527bf4[_0x1bc24b(0x234)]['transition']=_0x1bc24b(0x2a3),_0x527bf4['style'][_0x1bc24b(0x2a4)]=parseFloat(_0x527bf4[_0x1bc24b(0x234)][_0x1bc24b(0x2a4)])+0x64+'px',_0x527bf4[_0x1bc24b(0x234)]['opacity']='0';},0x1f4),setTimeout(()=>{const _0x20cdfd=_0x529437;_0x527bf4[_0x20cdfd(0x21d)]();},0x5dc));}),_0xd396a2[_0x13796f(0x29c)]((_0x2f5fe5,_0x762cb2)=>{const _0x11f4db=_0x13796f;if(!_0x2f5fe5[_0x11f4db(0x28c)]){const _0x368c90=document[_0x11f4db(0x253)]('span');_0x368c90[_0x11f4db(0x246)]=_0x11f4db(0x1f9),_0x368c90[_0x11f4db(0x234)][_0x11f4db(0x1ed)]=_0x11f4db(0x279),_0x368c90[_0x11f4db(0x234)]['left']=_0x2f5fe5[_0x11f4db(0x1ed)][_0x11f4db(0x269)]+'px',_0x368c90['style'][_0x11f4db(0x2a4)]='-'+(Math['random']()*0x32+0x32)+'px',_0x368c90[_0x11f4db(0x234)]['opacity']='0',_0x2f5fe5[_0x11f4db(0x255)]==='\x20'?_0x368c90['innerHTML']=_0x11f4db(0x231):_0x368c90[_0x11f4db(0x203)]=_0x2f5fe5[_0x11f4db(0x255)],_0x505731[_0x11f4db(0x274)](_0x368c90),setTimeout(()=>{const _0x55cda1=_0x11f4db;_0x368c90[_0x55cda1(0x234)][_0x55cda1(0x273)]='top\x201s\x20ease-out\x20'+_0x762cb2*0.05+'s,\x20opacity\x201s\x20ease\x20'+_0x762cb2*0.05+'s',_0x368c90[_0x55cda1(0x234)][_0x55cda1(0x2a4)]=_0x2f5fe5[_0x55cda1(0x1ed)][_0x55cda1(0x2a4)]+'px',_0x368c90[_0x55cda1(0x234)][_0x55cda1(0x29b)]='1';},0x64);}}),setTimeout(()=>{const _0xceacae=_0x13796f,_0x3ca72c=_0x36e9ed['querySelector'](_0xceacae(0x291));_0x3ca72c&&(_0x3ca72c[_0xceacae(0x203)]=_0x23ed08[_0xceacae(0x20b)]+_0xceacae(0x28a)),_0x505731['classList'][_0xceacae(0x222)](_0xceacae(0x1fe)),setTimeout(()=>{const _0x44c808=_0xceacae;_0x505731[_0x44c808(0x1f2)][_0x44c808(0x21d)](_0x44c808(0x1fe));const _0xa6385b=_0x505731[_0x44c808(0x204)](_0x44c808(0x243));_0xa6385b[_0x44c808(0x29c)](_0x38c523=>{const _0x203d3b=_0x44c808;_0x38c523[_0x203d3b(0x234)][_0x203d3b(0x1ed)]='',_0x38c523[_0x203d3b(0x234)][_0x203d3b(0x269)]='',_0x38c523[_0x203d3b(0x234)][_0x203d3b(0x2a4)]='',_0x38c523[_0x203d3b(0x234)][_0x203d3b(0x273)]='',_0x38c523[_0x203d3b(0x234)]['transform']='',_0x38c523['style'][_0x203d3b(0x29b)]='';}),_0x505731[_0x44c808(0x203)]=_0x23ed08,wrapLetters(_0x505731);},0x7d0),_0x3a52d2[_0xceacae(0x1f2)]['remove'](_0xceacae(0x28d)),_0x3a52d2[_0xceacae(0x234)][_0xceacae(0x28f)]=_0xceacae(0x206);},0x9c4);}function addCustomEditablePrice(_0x3ac755,_0x153a0b=a0_0x57b2bb(0x254)){const _0x38b567=a0_0x57b2bb;if(!_0x3ac755){console[_0x38b567(0x270)](_0x38b567(0x213));return;}if(_0x3ac755[_0x38b567(0x232)]('.custom-price-element')){console[_0x38b567(0x270)](_0x38b567(0x207),_0x3ac755);return;}const _0xc46bac=_0x3ac755[_0x38b567(0x232)]('.puis-card-container');if(!_0xc46bac){console[_0x38b567(0x270)](_0x38b567(0x25f),_0x3ac755);return;}const _0x33a1c7=window[_0x38b567(0x298)](_0xc46bac);_0x33a1c7[_0x38b567(0x1ed)]===_0x38b567(0x230)&&(_0xc46bac[_0x38b567(0x234)][_0x38b567(0x1ed)]=_0x38b567(0x272));const _0x487deb=document[_0x38b567(0x253)](_0x38b567(0x27f));_0x487deb['className']='custom-price-element',_0x487deb[_0x38b567(0x29f)](_0x38b567(0x208),_0x38b567(0x259)),_0x487deb[_0x38b567(0x29f)](_0x38b567(0x20f),'Enter\x20your\x20price'),_0x487deb[_0x38b567(0x29f)](_0x38b567(0x22f),'false'),_0x487deb[_0x38b567(0x29f)](_0x38b567(0x220),_0x38b567(0x25a)),_0x487deb[_0x38b567(0x29f)](_0x38b567(0x225),'Custom\x20Listing\x20Price'),_0x487deb[_0x38b567(0x29f)](_0x38b567(0x228),'0'),_0x487deb[_0x38b567(0x203)]=_0x153a0b,_0x487deb['setAttribute'](_0x38b567(0x20c),_0x153a0b),_0x487deb[_0x38b567(0x277)](_0x38b567(0x29e),function(){const _0x518d3f=_0x38b567;this['textContent'][_0x518d3f(0x223)]()===_0x518d3f(0x254)&&(this[_0x518d3f(0x203)]='');}),_0x487deb[_0x38b567(0x277)](_0x38b567(0x265),function(){const _0x126dfb=_0x38b567;this[_0x126dfb(0x203)][_0x126dfb(0x223)]()===''?this['textContent']=_0x126dfb(0x254):_0x3398c1(this);});function _0x3398c1(_0x19a9fa){const _0x4d5aac=_0x38b567;let _0x4b37ff=_0x19a9fa[_0x4d5aac(0x203)][_0x4d5aac(0x223)]();const _0x3ed6b8=/^\$\d{1,3}(,\d{3})*(\.\d{2})?$/;!_0x3ed6b8[_0x4d5aac(0x26b)](_0x4b37ff)?(_0x19a9fa[_0x4d5aac(0x1f2)][_0x4d5aac(0x222)](_0x4d5aac(0x212)),setTimeout(()=>{const _0x34cde2=_0x4d5aac;_0x19a9fa[_0x34cde2(0x203)]=_0x19a9fa['getAttribute']('data-last-valid-price')||'$0.00',_0x19a9fa[_0x34cde2(0x1f2)]['remove'](_0x34cde2(0x212));},0x5dc)):(_0x19a9fa['setAttribute'](_0x4d5aac(0x20c),_0x4b37ff),_0x19a9fa[_0x4d5aac(0x1f2)]['remove'](_0x4d5aac(0x212)),_0x1e6b31(_0x19a9fa));}function _0x1e6b31(_0x5a960d){const _0x5e10b5=_0x38b567,_0x21446b=document[_0x5e10b5(0x253)](_0x5e10b5(0x27f));_0x21446b[_0x5e10b5(0x246)]=_0x5e10b5(0x285),_0x21446b[_0x5e10b5(0x203)]='Price\x20Updated!',_0x5a960d[_0x5e10b5(0x274)](_0x21446b),setTimeout(()=>{const _0x28d932=_0x5e10b5;_0x21446b[_0x28d932(0x1f2)][_0x28d932(0x222)](_0x28d932(0x281)),_0x21446b[_0x28d932(0x277)]('transitionend',()=>{const _0x352f92=_0x28d932;_0x21446b[_0x352f92(0x25b)]&&_0x21446b['parentNode'][_0x352f92(0x27a)](_0x21446b);});},0x3e8);}_0xc46bac[_0x38b567(0x274)](_0x487deb);}function a0_0x4548(_0x737d23,_0x396cb0){const _0x323a52=a0_0x323a();return a0_0x4548=function(_0x4548ee,_0x41b7d5){_0x4548ee=_0x4548ee-0x1e9;let _0x2ba8a3=_0x323a52[_0x4548ee];return _0x2ba8a3;},a0_0x4548(_0x737d23,_0x396cb0);}function movePriceToTop(_0x888d6f){const _0x21f064=a0_0x57b2bb,_0x5384f8=_0x888d6f[_0x21f064(0x232)](_0x21f064(0x292));if(!_0x5384f8){console['warn'](_0x21f064(0x266),_0x888d6f);return;}const _0x38231b=_0x888d6f[_0x21f064(0x232)](_0x21f064(0x27b));if(!_0x38231b){console[_0x21f064(0x270)](_0x21f064(0x2a1),_0x888d6f);return;}const _0x433a93=_0x38231b[_0x21f064(0x25b)];_0x433a93[_0x21f064(0x245)](_0x5384f8,_0x38231b),_0x5384f8[_0x21f064(0x1f2)]['add'](_0x21f064(0x22c));}function modifyAddToCartButton(_0x28aa3a){const _0x22d820=a0_0x57b2bb;let _0x24f1f5=_0x28aa3a[_0x22d820(0x232)]('.puis-atcb-add-container\x20.a-button-primary');if(_0x24f1f5){let _0x33b72f=_0x24f1f5[_0x22d820(0x232)](_0x22d820(0x1ee));_0x33b72f&&(_0x33b72f[_0x22d820(0x203)]=_0x22d820(0x241)),_0x24f1f5['classList'][_0x22d820(0x21d)](_0x22d820(0x237)),_0x24f1f5[_0x22d820(0x1f2)][_0x22d820(0x222)](_0x22d820(0x26f)),_0x24f1f5[_0x22d820(0x277)](_0x22d820(0x238),function(_0x3a2b01){const _0x2ac621=_0x22d820;_0x3a2b01[_0x2ac621(0x216)](),_0x3a2b01[_0x2ac621(0x25d)](),_0x3f1b78(),_0x33b72f[_0x2ac621(0x28d)]=!![],_0x33b72f[_0x2ac621(0x203)]=_0x2ac621(0x205);});}function _0x3f1b78(){const _0x2f6ecc=_0x22d820;if(document[_0x2f6ecc(0x232)]('.sniper-animation-container'))return;let _0xc33710=document[_0x2f6ecc(0x253)]('div');_0xc33710[_0x2f6ecc(0x246)]=_0x2f6ecc(0x1f8),_0xc33710['setAttribute'](_0x2f6ecc(0x220),_0x2f6ecc(0x1e9)),_0xc33710[_0x2f6ecc(0x29f)](_0x2f6ecc(0x1f6),_0x2f6ecc(0x259));let _0x421f85=document[_0x2f6ecc(0x253)](_0x2f6ecc(0x27f));_0x421f85[_0x2f6ecc(0x246)]=_0x2f6ecc(0x1f1);let _0x57bdb0=document[_0x2f6ecc(0x253)](_0x2f6ecc(0x221));_0x57bdb0[_0x2f6ecc(0x246)]=_0x2f6ecc(0x24e),_0x57bdb0[_0x2f6ecc(0x247)]=chrome[_0x2f6ecc(0x224)]['getURL']('libraries/target-spin/target.png'),_0x57bdb0[_0x2f6ecc(0x264)]=_0x2f6ecc(0x227),_0x421f85[_0x2f6ecc(0x274)](_0x57bdb0),document[_0x2f6ecc(0x217)][_0x2f6ecc(0x274)](_0xc33710),document['body'][_0x2f6ecc(0x274)](_0x421f85);let _0x2a80ae=document[_0x2f6ecc(0x253)](_0x2f6ecc(0x27f));_0x2a80ae['className']=_0x2f6ecc(0x24a),_0x2a80ae[_0x2f6ecc(0x203)]=_0x2f6ecc(0x22b),document[_0x2f6ecc(0x217)]['appendChild'](_0x2a80ae);function _0x159e9e(){const _0x393137=_0x2f6ecc;_0x421f85[_0x393137(0x1f2)][_0x393137(0x222)](_0x393137(0x25c)),_0xc33710[_0x393137(0x1f2)][_0x393137(0x222)]('sniper-overlay-fade-out'),_0x2a80ae['classList'][_0x393137(0x222)](_0x393137(0x21b)),setTimeout(function(){const _0x3f63dc=_0x393137;_0x421f85[_0x3f63dc(0x25b)]&&_0x421f85[_0x3f63dc(0x25b)]['removeChild'](_0x421f85),_0xc33710[_0x3f63dc(0x25b)]&&_0xc33710[_0x3f63dc(0x25b)][_0x3f63dc(0x27a)](_0xc33710),_0x2a80ae[_0x3f63dc(0x25b)]&&_0x2a80ae[_0x3f63dc(0x25b)][_0x3f63dc(0x27a)](_0x2a80ae);},0x1f4);}_0xc33710[_0x2f6ecc(0x277)](_0x2f6ecc(0x238),function(_0x5cf6a4){_0x159e9e();}),_0x421f85[_0x2f6ecc(0x277)]('click',function(_0x390733){const _0x330c2a=_0x2f6ecc;_0x390733[_0x330c2a(0x25d)]();}),setTimeout(function(){_0x159e9e();},0x7d0);}}function a0_0x323a(){const _0x4e3436=['Custom\x20title\x20container\x20not\x20found\x20in\x20the\x20card:','createElement','$0.00','char','selectNodeContents','s-line-clamp-1','addRange','true','textbox','parentNode','sniper-animation-fade-out','stopPropagation','snipeMode-content','Parent\x20container\x20not\x20found\x20in\x20the\x20card:','.s-result-item','execCommand','letters-wrapped','506079XYtMGd','alt','blur','Price\x20element\x20not\x20found\x20in\x20the\x20card:','.new-letter','normal','left','.cleaning-icon','test','High\x20Quality\x20Cat6\x20Cable','338170FcQEKe','data-custom-title','snipe-button','warn','closest','relative','transition','appendChild','\x22>&nbsp;</span>','nowrap','addEventListener','snipeMode-competitor-image','absolute','removeChild','.s-product-image-container','1336pifHZh','false','4038LZeJkj','div','.custom-title-container','fade-out','<span\x20class=\x22letter\x22\x20style=\x22--animation-order:','$29.99','log','price-update-confirmation','collapse','textOverflow','left\x201s\x20ease-in-out,\x20top\x201s\x20ease-in-out','getElementById','/80','#007600','assignedOldLetter','disabled','text/plain','cursor','left\x201.5s\x20ease-in-out\x20','.char-counter','.s-price-instructions-style','snipeMode-competitor-price','Clean\x20Custom\x20Title','contentEditable','height','button','getComputedStyle','getBoundingClientRect','snipeMode-close-button','opacity','forEach','word-bank-label','focus','setAttribute','char-counter','Product\x20image\x20container\x20not\x20found\x20in\x20the\x20card:','data-snipe-item-number','top\x201s\x20ease,\x20opacity\x201s\x20ease','top','auto','data-snipe-title','title','cleaning-icon','dialog','contains','assigned','1305180usCcuz','position','.a-button-text','input','split','sniper-animation-container','classList','763DCwkUf','margin','<span\x20class=\x22new-letter\x22\x20data-index=\x22','aria-modal','12386ackWlF','sniper-overlay','letter','random','none','clipboardData','0px','wow-animation','undefined','snipeMode-competitor-title','snipeMode-banner-container','data-snipe-price','textContent','querySelectorAll','Sniped','pointer','Custom\x20price\x20element\x20already\x20exists\x20in\x20this\x20card:','contenteditable','getSelection','parentElement','length','data-last-valid-price','snipeMode-refresh-button','exceeds-limit','data-placeholder','amazon_search_page_functions_snipe.js','currentLeft','invalid','Invalid\x20card\x20element\x20provided.','from','target','preventDefault','body','keyup','&#x21bb;','custom-title-element','sniper-message-fade-out','innerHTML','remove','</span>','not-allowed','role','img','add','trim','runtime','aria-label','15138zOZEmH','Sniper\x20Mark','tabindex','.s-title-instructions-style','replaceChild','Sniped!','price-at-top','createRange','whiteSpace','spellcheck','static','&nbsp;','querySelector','custom-title-container','style','605012XdKHph','text','a-button-primary','click','Cleaning\x20icon\x20already\x20exists\x20in\x20this\x20card:','map','custom-title-features-container','push','.custom-title-element','word-button','6150VoPhKy','floor','Snipe\x20Listing','Refresh\x20Custom\x20Titles','.letter','4eoimnB','insertBefore','className','src','span','.custom-title-features-container','sniper-message','Custom\x20title\x20element\x20not\x20found:','floating-loop','currentTop','sniper-animation-image','Price:\x20$','s-line-clamp-4','visibility'];a0_0x323a=function(){return _0x4e3436;};return a0_0x323a();}function addCustomTitleTextBox(_0x499d04){const _0x4ba613=a0_0x57b2bb;var _0x46e2ee=document[_0x4ba613(0x289)](_0x4ba613(0x201)),_0x2e5604='';_0x46e2ee&&(_0x2e5604=_0x46e2ee['getAttribute'](_0x4ba613(0x2a6))||'');let _0x199d54=_0x499d04[_0x4ba613(0x232)](_0x4ba613(0x229));if(!_0x199d54)return;let _0x3c57f8=_0x199d54[_0x4ba613(0x204)]('h2');_0x3c57f8[_0x4ba613(0x29c)](function(_0x24c2a9){const _0x2785c=_0x4ba613;_0x24c2a9[_0x2785c(0x1f2)][_0x2785c(0x21d)](_0x2785c(0x250),_0x2785c(0x257)),_0x24c2a9[_0x2785c(0x234)]['whiteSpace']=_0x2785c(0x268),_0x24c2a9['style']['overflow']='visible',_0x24c2a9['style'][_0x2785c(0x287)]='clip',_0x24c2a9[_0x2785c(0x234)]['webkitLineClamp']='unset',_0x24c2a9[_0x2785c(0x234)]['maxHeight']=_0x2785c(0x1fb),_0x24c2a9[_0x2785c(0x234)][_0x2785c(0x296)]=_0x2785c(0x2a5),_0x24c2a9[_0x2785c(0x234)]['color']=_0x2785c(0x28b);let _0x122181=_0x24c2a9[_0x2785c(0x204)]('a');_0x122181[_0x2785c(0x29c)](function(_0x50b0e1){const _0x5bab71=_0x2785c;let _0x314401=_0x50b0e1['innerHTML'],_0x4089f9=document[_0x5bab71(0x253)](_0x5bab71(0x248));_0x4089f9['innerHTML']=_0x314401,_0x4089f9[_0x5bab71(0x234)][_0x5bab71(0x28f)]=_0x5bab71(0x236),_0x50b0e1['parentNode'][_0x5bab71(0x22a)](_0x4089f9,_0x50b0e1);});});let _0x11cb41=_0x3c57f8[_0x3c57f8[_0x4ba613(0x20b)]-0x1][_0x4ba613(0x203)][_0x4ba613(0x223)](),_0xcb188=Array[_0x4ba613(0x214)](new Set(_0x11cb41[_0x4ba613(0x1f0)](/\s+/))),_0x2ce803=document[_0x4ba613(0x253)]('div');_0x2ce803[_0x4ba613(0x246)]='word-bank-container';let _0x225ab8=document[_0x4ba613(0x253)]('div');_0x225ab8[_0x4ba613(0x246)]=_0x4ba613(0x29d),_0x225ab8[_0x4ba613(0x203)]='Click\x20words\x20to\x20add\x20to\x20your\x20custom\x20title:',_0x2ce803['appendChild'](_0x225ab8),_0xcb188[_0x4ba613(0x29c)](function(_0x2f5212){const _0x56e7ab=_0x4ba613;let _0x12ba95=document[_0x56e7ab(0x253)](_0x56e7ab(0x297));_0x12ba95['className']=_0x56e7ab(0x23e),_0x12ba95[_0x56e7ab(0x203)]=_0x2f5212,_0x12ba95['addEventListener'](_0x56e7ab(0x238),function(){const _0x163ee9=_0x56e7ab;let _0x50adb6=_0x5f24ad[_0x163ee9(0x203)]['trim'](),_0x4f1d4e=_0x50adb6?_0x50adb6+'\x20'+_0x2f5212:_0x2f5212;_0x5f24ad[_0x163ee9(0x203)]=_0x4f1d4e,_0x35d016(_0x5f24ad),_0x11c4b3();}),_0x2ce803[_0x56e7ab(0x274)](_0x12ba95);});let _0x27addc=document[_0x4ba613(0x253)]('div');_0x27addc[_0x4ba613(0x246)]=_0x4ba613(0x233);var _0x5a5e55=document[_0x4ba613(0x253)](_0x4ba613(0x27f));_0x5a5e55['className']=_0x4ba613(0x2a0),_0x5a5e55[_0x4ba613(0x203)]=_0x2e5604[_0x4ba613(0x20b)]+_0x4ba613(0x28a);let _0x5f24ad=document[_0x4ba613(0x253)](_0x4ba613(0x27f));_0x5f24ad[_0x4ba613(0x246)]=_0x4ba613(0x21a),_0x5f24ad[_0x4ba613(0x295)]='true',_0x5f24ad[_0x4ba613(0x203)]=_0x2e5604,_0x5f24ad['setAttribute'](_0x4ba613(0x20f),'Click\x20here\x20to\x20edit\x20custom\x20title\x20(max\x2080\x20characters)'),_0x5f24ad['setAttribute'](_0x4ba613(0x22f),_0x4ba613(0x27d));function _0x11c4b3(){const _0x2db15c=_0x4ba613;let _0x3bde89=_0x5f24ad[_0x2db15c(0x203)]||'',_0x57f923=_0x3bde89['trim']()['length'];_0x5a5e55[_0x2db15c(0x203)]=_0x57f923+_0x2db15c(0x28a),_0x57f923>0x50?(_0x5f24ad['classList'][_0x2db15c(0x222)](_0x2db15c(0x20e)),_0x5a5e55[_0x2db15c(0x1f2)][_0x2db15c(0x222)](_0x2db15c(0x20e))):(_0x5f24ad[_0x2db15c(0x1f2)][_0x2db15c(0x21d)](_0x2db15c(0x20e)),_0x5a5e55[_0x2db15c(0x1f2)][_0x2db15c(0x21d)]('exceeds-limit')),_0x499d04['setAttribute']('data-custom-title',_0x3bde89[_0x2db15c(0x223)]());}_0x5f24ad[_0x4ba613(0x277)](_0x4ba613(0x1ef),_0x11c4b3),_0x5f24ad['addEventListener'](_0x4ba613(0x218),_0x11c4b3),_0x5f24ad[_0x4ba613(0x277)]('paste',function(_0x127454){const _0x13b443=_0x4ba613;_0x127454[_0x13b443(0x216)]();let _0x55d161=(_0x127454['clipboardData']||window[_0x13b443(0x1fc)])['getData'](_0x13b443(0x28e));document[_0x13b443(0x261)]('insertText',![],_0x55d161),_0x11c4b3();});function _0x35d016(_0x30cf20){const _0x4ec84c=_0x4ba613;_0x30cf20['focus']();if(typeof window['getSelection']!=_0x4ec84c(0x1ff)&&typeof document[_0x4ec84c(0x22d)]!=_0x4ec84c(0x1ff)){let _0xa83d7e=document[_0x4ec84c(0x22d)]();_0xa83d7e[_0x4ec84c(0x256)](_0x30cf20),_0xa83d7e[_0x4ec84c(0x286)](![]);let _0x5c6d48=window[_0x4ec84c(0x209)]();_0x5c6d48['removeAllRanges'](),_0x5c6d48[_0x4ec84c(0x258)](_0xa83d7e);}}var _0x33ccd9=document[_0x4ba613(0x253)]('div');_0x33ccd9[_0x4ba613(0x246)]=_0x4ba613(0x23b),_0x33ccd9[_0x4ba613(0x274)](_0x5a5e55),_0x27addc[_0x4ba613(0x274)](_0x5f24ad),_0x27addc[_0x4ba613(0x274)](_0x33ccd9),_0x199d54[_0x4ba613(0x274)](_0x27addc),_0x199d54[_0x4ba613(0x274)](_0x2ce803),modifyAddToCartButton(_0x499d04);}function addBanner(_0x41858f,_0x5ae2aa,_0x11bd29,_0x4f7c03){const _0x23390b=a0_0x57b2bb;var _0x253626=document[_0x23390b(0x289)](_0x23390b(0x201));_0x253626&&_0x253626['remove']();var _0x50d562=document['createElement']('div');_0x50d562['id']=_0x23390b(0x201),_0x50d562[_0x23390b(0x246)]=_0x23390b(0x201),document[_0x23390b(0x217)]['appendChild'](_0x50d562);var _0x19008e=document[_0x23390b(0x253)](_0x23390b(0x221));_0x19008e[_0x23390b(0x246)]=_0x23390b(0x278),_0x19008e[_0x23390b(0x247)]=_0x4f7c03,_0x50d562[_0x23390b(0x274)](_0x19008e);var _0x581918=document[_0x23390b(0x253)]('div');_0x581918[_0x23390b(0x246)]=_0x23390b(0x25e),_0x50d562[_0x23390b(0x274)](_0x581918);var _0x7528f4=document[_0x23390b(0x253)]('div');_0x7528f4[_0x23390b(0x246)]='snipeMode-title-container',_0x581918['appendChild'](_0x7528f4);var _0xc2b08f=document[_0x23390b(0x253)](_0x23390b(0x27f));_0xc2b08f[_0x23390b(0x246)]=_0x23390b(0x200),_0xc2b08f[_0x23390b(0x295)]=_0x23390b(0x259),_0xc2b08f[_0x23390b(0x21c)]=_0x41858f,_0x7528f4[_0x23390b(0x274)](_0xc2b08f);var _0x5bc8d8=document[_0x23390b(0x253)](_0x23390b(0x297));_0x5bc8d8[_0x23390b(0x246)]=_0x23390b(0x20d),_0x5bc8d8[_0x23390b(0x29f)](_0x23390b(0x2a7),_0x23390b(0x242)),_0x5bc8d8['innerHTML']=_0x23390b(0x219),_0x7528f4[_0x23390b(0x274)](_0x5bc8d8),_0x5bc8d8[_0x23390b(0x277)](_0x23390b(0x238),function(){const _0x4833c9=_0x23390b;var _0x529c9a=_0xc2b08f[_0x4833c9(0x203)][_0x4833c9(0x223)]();_0x50d562[_0x4833c9(0x29f)]('data-snipe-title',_0x529c9a),updateAllCustomTitles(_0x529c9a);});var _0x2c182e=document[_0x23390b(0x253)](_0x23390b(0x27f));_0x2c182e['className']=_0x23390b(0x293),_0x2c182e['innerHTML']=_0x23390b(0x24f)+_0x5ae2aa,_0x581918[_0x23390b(0x274)](_0x2c182e);var _0x5a0f78=document['createElement'](_0x23390b(0x297));_0x5a0f78['id']=_0x23390b(0x29a),_0x5a0f78[_0x23390b(0x246)]=_0x23390b(0x29a),_0x5a0f78['innerHTML']='&times;',_0x50d562[_0x23390b(0x274)](_0x5a0f78),_0x5a0f78[_0x23390b(0x277)](_0x23390b(0x238),function(){const _0x4bd8b5=_0x23390b;_0x50d562[_0x4bd8b5(0x21d)]();}),_0x50d562[_0x23390b(0x29f)](_0x23390b(0x2a6),_0x41858f),_0x50d562[_0x23390b(0x29f)](_0x23390b(0x202),_0x5ae2aa),_0x50d562['setAttribute'](_0x23390b(0x2a2),_0x11bd29);}function updateAllCustomTitles(_0x2d9fe6){const _0x292684=a0_0x57b2bb;var _0x53a131=document[_0x292684(0x204)](_0x292684(0x23d));_0x53a131['forEach'](function(_0x538bf0){const _0x381dd5=_0x292684;_0x538bf0[_0x381dd5(0x203)]=_0x2d9fe6;var _0x23a569=_0x538bf0[_0x381dd5(0x271)](_0x381dd5(0x260));_0x23a569&&_0x23a569[_0x381dd5(0x29f)](_0x381dd5(0x26e),_0x2d9fe6);var _0x45ecbc=_0x538bf0['parentElement'][_0x381dd5(0x232)](_0x381dd5(0x291));_0x45ecbc&&(_0x45ecbc['textContent']=_0x2d9fe6[_0x381dd5(0x20b)]+'/80');if(_0x2d9fe6[_0x381dd5(0x20b)]>0x50){_0x538bf0[_0x381dd5(0x1f2)]['add']('exceeds-limit');if(_0x45ecbc)_0x45ecbc[_0x381dd5(0x1f2)]['add']('exceeds-limit');}else{_0x538bf0[_0x381dd5(0x1f2)]['remove'](_0x381dd5(0x20e));if(_0x45ecbc)_0x45ecbc['classList'][_0x381dd5(0x21d)]('exceeds-limit');}});}