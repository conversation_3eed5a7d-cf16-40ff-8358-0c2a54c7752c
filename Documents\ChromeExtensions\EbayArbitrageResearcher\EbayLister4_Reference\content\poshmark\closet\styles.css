/* styles.css */

/* Modal Styles */
#share-progress-modal {
    position: fixed;
    top: 20%;
    left: 50%;
    transform: translate(-50%, -20%);
    background-color: #ffffff;
    border: 2px solid #cccccc;
    padding: 20px;
    z-index: 9999;
    width: 320px;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#share-progress-modal .modal-content h2 {
    margin-top: 0;
    color: #333;
}

#progress-bar-container {
    width: 100%;
    background-color: #e0e0e0;
    height: 20px;
    border-radius: 10px;
    margin: 20px 0;
    overflow: hidden;
}

#progress-bar {
    width: 0%;
    height: 100%;
    background-color: #76c7c0;
    border-radius: 10px;
    transition: width 0.3s ease-in-out;
}

#progress-text {
    margin-top: 10px;
    color: #555;
}

/* Highlight Effect */
.highlight-item {
    border: 2px solid #76c7c0 !important;
    box-shadow: 0 0 15px rgba(118, 199, 192, 0.6);
    transition: all 0.3s ease-in-out;
}
