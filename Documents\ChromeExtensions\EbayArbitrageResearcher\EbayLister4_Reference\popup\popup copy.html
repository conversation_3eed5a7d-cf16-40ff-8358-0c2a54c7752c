<!DOCTYPE html>
<html lang="en">

    <head>
        <title>Ebay Inserting Extension</title>
        <meta charset="UTF-8">
        <link rel="stylesheet" href="popup.css">
    </head>

    <body>


            <!-- //create input to enter image url -->
    




                
    

        <form id="main_form">
            <label>
                SKU SAVED:
            <h4><span id = "skuFound">SkuFound: 0</span> </h4>
            <button id="main_button">Get Active SKU List: </button>
            //create input with page to start on
            <input type="text" id="page_number" value="1">
        </label>
        </form>



        
            <button id="save_sku_on_page">Save SKU on Page</button>


       
            <br>

          
            <button id="delete_skus">Delete SKUs</button>
            <br>



            <input id="watermark_url" type="text" placeholder="Enter Image URL">
            <button id="save_image_to_local_storage">Save Image to Local Storage</button>
            Watermark:
            <img id="image_from_local_storage" src="" width="200" 
            height="200">

            

            //create input and button to enter api key
            <input id="api_key" type="text" placeholder="Enter API Key">
            <button id="save_api_key">Save API Key</button>
            <br>
            <br>

            API Key: 
            <br>
            <span id="api_key_display"></span>
            <br>
            <br>






            <br>
            <br>
            Auto Enable AI Title Creations:
            <label class="switch">
                <input type="checkbox" id="auto_ai_title_checkbox" checked>
                <span class="slider round"></span>



            <br>
            <br>

            

            <!-- <textarea id="id_memo" cols="30" rows="10"></textarea><br>
        <input type="button" id="id_save" value="Save to localStorage"><br>
        <input type="button" id="id_savefile" value="Save to File"><br>
        <input type="file" id="id_openfile">





        //create button to start scraping data from amazon and updating google sheets
        <button id="start_scraping_amazon_and_upload_google_sheets">Start Scraping</button> -->

        




        <script src="/libraries/csv_utils.js"></script>
        <script src="/libraries/chrome_storage_utils.js"></script>
        <script src="popup.js"></script>

    </body>

    

</html>