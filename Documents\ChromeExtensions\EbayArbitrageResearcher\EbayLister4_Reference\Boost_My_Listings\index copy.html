<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boost My Listings</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

</head>
<body>

<h1>Boost My Listings</h1>

<div id="end_and_sell_similar_container">
    <button id="toggle_options_button"><i class="fas fa-cog"></i></button>
    <button id="end_and_sell_similar_button">End & Sell Similar</button>



    <div id="filters_container" style="display: none;">

        <h2>Settings</h2>
        
        <h3>Filters (Optional)</h3>
        <div class="input-group">
            <div class="filter">
                <label for="minSold">Minimum Sold:
                    <i class="fas fa-info-circle tooltip-icon"></i>
                    <span class="tooltip">Set the maximum number of sales an item should have to be considered for relisting. Items with sales less than or equal to this number will be targeted. For example, setting this to '5' will target items that have sold 5 times or fewer.</span>
                </label>
                <input type="number" id="minSold" name="minSold" placeholder="0">
            </div>
    
            <div class="filter">
                <label for="minViews">Minimum Views:
                    <i class="fas fa-info-circle tooltip-icon"></i>
                    <span class="tooltip">Define the minimum views threshold. Items with views not exceeding this number are considered underperforming. For instance, entering '100' will target items that have received fewer than 100 views.</span>
                </label>
                <input type="number" id="minViews" name="minViews" placeholder="0">
            </div>
    
            <div class="filter">
                <label for="timeLeft">Hours Left:
                    <i class="fas fa-info-circle tooltip-icon"></i>
                    <span class="tooltip">Specify how many hours left until the listing ends. Items with a remaining time less than this will be flagged for relisting. Setting this to '24' targets items ending within the next 24 hours.</span>
                </label>
                <input type="number" id="timeLeft" name="timeLeft" placeholder="72">
            </div>
        </div>
        
        <h3>Options</h3>
    
        <div class="checkbox-filter">

            <div class="filter">
            <label for="autoCloseToggle">Auto Close:
                <i class="fas fa-info-circle tooltip-icon"></i>
                <span class="tooltip">Enable this option to automatically close the browser tab once the process is complete. This is useful for running the tool in the background.</span>
            </label>
            <input type="checkbox" id="autoCloseToggle">
            </div>

            <div class="filter">
            <label for="autoRepeatToggle">Auto Repeat:
                <i class="fas fa-info-circle tooltip-icon"></i>
                <span class="tooltip">Enable this option to automatically repeat the process until no more low-performing items are found. This is useful for bulk relisting.</span>
            </label>
            <input type="checkbox" id="autoRepeatToggle">
            </div>  

        </div>
    </div>
    


    
    <div id="loading_bar" class="loading-bar">
        <div id="loading_progress" class="loading-progress"></div>
    </div>
    <p id="status_message">Ready to start.</p>
</div>

<script src="script.js"></script>
    
</body>
</html>
