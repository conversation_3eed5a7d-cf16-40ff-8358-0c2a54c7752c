/**
 * Centralized Event Bus for inter-component communication
 * Replaces tangled callback patterns with clean event-driven architecture
 */
class EventBus {
  constructor() {
    this.listeners = new Map();
    this.debug = true;
  }

  /**
   * Register an event listener
   * @param {string} event - Event name
   * @param {function} callback - Callback function
   * @param {object} options - Options (once, priority)
   */
  on(event, callback, options = {}) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listener = {
      callback,
      once: options.once || false,
      priority: options.priority || 0,
      id: Math.random().toString(36).substr(2, 9)
    };

    this.listeners.get(event).push(listener);
    
    // Sort by priority (higher priority first)
    this.listeners.get(event).sort((a, b) => b.priority - a.priority);

    if (this.debug) {
      console.log(`📡 EventBus: Registered listener for '${event}'`, listener.id);
    }

    return listener.id;
  }

  /**
   * Register a one-time event listener
   * @param {string} event - Event name
   * @param {function} callback - Callback function
   */
  once(event, callback) {
    return this.on(event, callback, { once: true });
  }

  /**
   * Emit an event to all listeners
   * @param {string} event - Event name
   * @param {*} data - Data to pass to listeners
   */
  emit(event, data) {
    const listeners = this.listeners.get(event);
    
    if (!listeners || listeners.length === 0) {
      if (this.debug) {
        console.log(`📡 EventBus: No listeners for '${event}'`);
      }
      return;
    }

    if (this.debug) {
      console.log(`📡 EventBus: Emitting '${event}' to ${listeners.length} listeners`, data);
    }

    // Create a copy to avoid issues if listeners modify the array
    const listenersToCall = [...listeners];

    listenersToCall.forEach((listener, index) => {
      try {
        listener.callback(data);
        
        // Remove one-time listeners
        if (listener.once) {
          const listenerIndex = listeners.findIndex(l => l.id === listener.id);
          if (listenerIndex !== -1) {
            listeners.splice(listenerIndex, 1);
          }
        }
      } catch (error) {
        console.error(`📡 EventBus: Error in listener for '${event}':`, error);
      }
    });
  }

  /**
   * Remove a specific listener
   * @param {string} event - Event name
   * @param {string} listenerId - Listener ID returned by on()
   */
  off(event, listenerId) {
    const listeners = this.listeners.get(event);
    if (!listeners) return false;

    const index = listeners.findIndex(l => l.id === listenerId);
    if (index !== -1) {
      listeners.splice(index, 1);
      if (this.debug) {
        console.log(`📡 EventBus: Removed listener ${listenerId} for '${event}'`);
      }
      return true;
    }
    return false;
  }

  /**
   * Remove all listeners for an event
   * @param {string} event - Event name
   */
  removeAllListeners(event) {
    if (event) {
      this.listeners.delete(event);
      if (this.debug) {
        console.log(`📡 EventBus: Removed all listeners for '${event}'`);
      }
    } else {
      this.listeners.clear();
      if (this.debug) {
        console.log(`📡 EventBus: Removed all listeners`);
      }
    }
  }

  /**
   * Get listener count for an event
   * @param {string} event - Event name
   */
  listenerCount(event) {
    const listeners = this.listeners.get(event);
    return listeners ? listeners.length : 0;
  }

  /**
   * Enable/disable debug logging
   * @param {boolean} enabled - Debug enabled
   */
  setDebug(enabled) {
    this.debug = enabled;
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.EventBus = EventBus;
window.ArbitrageEventBus = window.ArbitrageEventBus || new EventBus();
window.globalContext.EventBus = EventBus;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EventBus;
}

console.log('✅ ArbitrageEventBus loaded and exported to window.ArbitrageEventBus');
console.log('🕐 EventBus initialization timing:', {
  timestamp: performance.now(),
  readyState: document.readyState
});
