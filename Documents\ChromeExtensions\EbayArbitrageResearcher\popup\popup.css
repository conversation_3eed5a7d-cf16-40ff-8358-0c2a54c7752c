/* eBay Arbitrage Researcher Pro - Popup Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 420px;
    min-height: 500px;
    max-height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    overflow-y: auto;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    text-align: center;
    position: relative;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.version {
    font-size: 11px;
    opacity: 0.8;
}

/* Quick Test Section */
.quick-test-section {
    padding: 12px 20px;
    background: #e8f4fd;
    border-bottom: 1px solid #bee5eb;
}

.quick-test-section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #0c5460;
}

.quick-test-controls {
    display: flex;
    gap: 16px;
    align-items: end;
}

.test-item {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.test-item label {
    font-size: 12px;
    font-weight: 500;
    color: #0c5460;
    margin-bottom: 4px;
}

.test-input {
    padding: 8px 10px;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    background: white;
    transition: border-color 0.2s;
}

.test-input:focus {
    outline: none;
    border-color: #17a2b8;
    box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.1);
}

/* Configuration Section */
.config-section {
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.config-header {
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.config-header:hover {
    background: #f8f9fa;
}

.config-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.toggle-icon {
    font-size: 12px;
    color: #6c757d;
    transition: transform 0.2s;
}

.toggle-icon.expanded {
    transform: rotate(180deg);
}

.config-content {
    padding: 0 20px 16px 20px;
}

.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.config-item {
    display: flex;
    flex-direction: column;
}

.config-item label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 4px;
}

.config-item input {
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.2s;
}

.config-item input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.price-range h4 {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-inputs input {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
}

.range-inputs span {
    font-size: 12px;
    color: #6c757d;
}

/* Control Section */
.control-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.main-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    min-height: 48px;
    align-items: center;
}

.btn-primary {
    flex: 1;
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-primary:disabled {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
}

.btn-stop {
    flex: 1;
    width: 100%;
    padding: 14px 16px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.btn-stop:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.status-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.status-text {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.ready {
    background: #28a745;
}

.status-dot.processing {
    background: #ffc107;
}

.status-dot.error {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}



.btn-secondary {
    padding: 12px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Progress Section */
.progress-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.progress-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

#progressPercent {
    font-size: 14px;
    font-weight: 600;
    color: #667eea;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-status {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.progress-stats {
    font-size: 11px;
    color: #868e96;
}

/* Results Section */
.results-section {
    flex: 1;
    padding: 16px 20px;
    background: white;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.results-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.count-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.btn-export, .btn-refresh {
    padding: 6px 10px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-export:hover:not(:disabled), .btn-refresh:hover {
    background: #e9ecef;
}

.btn-export:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #28a745;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.filter-controls select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

/* Results List */
.results-list {
    max-height: 300px;
    overflow-y: auto;
}

.no-results {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-results p {
    margin-bottom: 8px;
    font-size: 13px;
}

.opportunity {
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.opportunity:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.opportunity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.opportunity-rank {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
}

.opportunity-grade {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    color: white;
}

.grade-a-plus { background: #28a745; }
.grade-a { background: #20c997; }
.grade-b-plus { background: #ffc107; color: #333; }
.grade-b { background: #fd7e14; }
.grade-c-plus { background: #6f42c1; }
.grade-c { background: #6c757d; }
.grade-d { background: #dc3545; }

.opportunity-profit {
    font-size: 14px;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 6px;
}

.opportunity-titles {
    margin-bottom: 8px;
}

.opportunity-title {
    font-size: 11px;
    color: #495057;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.opportunity-metrics {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #6c757d;
}

/* Footer */
.footer {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.footer-links a {
    font-size: 11px;
    color: #6c757d;
    text-decoration: none;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: #667eea;
}

/* Scrollbar Styling */
.results-list::-webkit-scrollbar {
    width: 6px;
}

.results-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
