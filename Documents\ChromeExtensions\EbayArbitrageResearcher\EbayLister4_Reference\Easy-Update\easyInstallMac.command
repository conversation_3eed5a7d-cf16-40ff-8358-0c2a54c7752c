#!/bin/bash

########################################################################
# update_ebaylister.command
# Replicates the Windows .bat functionality on macOS.
#
# 1) Downloads eBayLister.zip if it's not already in the folder.
# 2) Deletes the "EbayLister4" folder if it exists.
# 3) Unzips the new file.
# 4) Pauses at the end so the user sees the result.
########################################################################

# Move into the directory containing this script
cd "$(dirname "$0")"

# Define the URL and file names
url="https://firebasestorage.googleapis.com/v0/b/ecomsniper-cb046.appspot.com/o/software%2FeBayLister.zip?alt=media&token=a84ad875-2120-4ce9-9911-ed4595a0be38"
zipFile="eBayLister.zip"
folder="EbayLister4"

echo "--------------------------------------"
echo "URL:      $url"
echo "Zip File: $zipFile"
echo "Folder:   $folder"
echo "--------------------------------------"
echo

# 1) Download the file if not present
if [ ! -f "$zipFile" ]; then
    echo "Downloading $zipFile..."
    curl -L "$url" -o "$zipFile"
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to download $zipFile"
        echo "Exiting..."
        exit 1
    fi
else
    echo "$zipFile already exists, skipping download."
fi

# 2) Check if the folder exists and delete it
if [ -d "$folder" ]; then
    echo "Deleting existing folder: $folder..."
    rm -rf "$folder"
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to delete folder $folder"
        echo "Exiting..."
        exit 1
    fi
fi

# 3) Extract the zip file
if [ -f "$zipFile" ]; then
    echo "Extracting $zipFile..."
    unzip -o "$zipFile"
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to unzip $zipFile"
        echo "Exiting..."
        exit 1
    fi
else
    echo "ERROR: $zipFile not found. Cannot extract."
    exit 1
fi

echo
echo "Done updating EbayLister4!"
echo "--------------------------------------"

# 4) Pause so the user sees the Terminal output
#    (Closes after pressing any key)
read -n 1 -s -r -p "Press any key to close..."
echo
