(function(_0x491c54,_0x299f05){var _0xe1b62f=a0_0x3c15,_0x53ce29=_0x491c54();while(!![]){try{var _0x3b196d=parseInt(_0xe1b62f(0x15e))/0x1*(parseInt(_0xe1b62f(0x117))/0x2)+-parseInt(_0xe1b62f(0x140))/0x3*(-parseInt(_0xe1b62f(0x118))/0x4)+parseInt(_0xe1b62f(0x161))/0x5+-parseInt(_0xe1b62f(0x19e))/0x6*(parseInt(_0xe1b62f(0x194))/0x7)+-parseInt(_0xe1b62f(0x15f))/0x8+parseInt(_0xe1b62f(0x1ac))/0x9+parseInt(_0xe1b62f(0x171))/0xa*(-parseInt(_0xe1b62f(0x177))/0xb);if(_0x3b196d===_0x299f05)break;else _0x53ce29['push'](_0x53ce29['shift']());}catch(_0x15c296){_0x53ce29['push'](_0x53ce29['shift']());}}}(a0_0x5af2,0x1cb6f));function createButtonWithText(_0x495c46,_0x57146e,_0x5e6fe2){var _0x4fb1d7=a0_0x3c15,_0x4763cc=document[_0x4fb1d7(0x119)](_0x4fb1d7(0x14b));return _0x4763cc['id']=_0x495c46,_0x4763cc[_0x4fb1d7(0x122)]=_0x57146e,_0x5e6fe2[_0x4fb1d7(0x150)](_0x23ad81=>_0x4763cc[_0x4fb1d7(0x163)][_0x4fb1d7(0x14e)](_0x23ad81)),_0x4763cc;}function createCheckboxWithLabel(_0x35e930,_0x3758e3,_0x48f0bb=!![]){var _0x2b6a77=a0_0x3c15,_0xc2bec8=document[_0x2b6a77(0x119)]('input');_0xc2bec8['type']=_0x2b6a77(0x174),_0xc2bec8['id']=_0x35e930,_0xc2bec8['checked']=_0x48f0bb;var _0x1003f2=document[_0x2b6a77(0x119)](_0x2b6a77(0x135));_0x1003f2['htmlFor']=_0x35e930,_0x1003f2['textContent']=_0x3758e3;var _0x24478e=document[_0x2b6a77(0x119)](_0x2b6a77(0x154));return _0x24478e[_0x2b6a77(0x17c)](_0x1003f2),_0x24478e[_0x2b6a77(0x17c)](_0xc2bec8),{'checkbox':_0xc2bec8,'container':_0x24478e};}function a0_0x5af2(){var _0x28c412=['Unable\x20to\x20auto-generate\x20mask\x20image\x20with\x20given\x20input\x20image\x20and\x20mask\x20prompt.','image.jpg','div','DEFAULT','checked','ultimate','min','item-type-input','outPaintingParams','height','length','select','13037alIbry','803408JMtOcQ','complete','750640CYXVuH','Prompt\x20Style','classList','prompt-settings-input-div','Original\x20Image\x20Size:','Enter\x20a\x20description\x20for\x20the\x20background\x20image,\x20or\x20leave\x20blank\x20for\x20a\x20random\x20background\x20suggestion.','black','Number\x20of\x20Images','freeDraw','text','clearRect','parse','strokeRect','input','drawImage','generate-image-button-success','5410KLRLhL','generate-image-button-working','max','checkbox','title','),\x20Mask:\x20(','3135owsNbH','log','stroke','getContext','placeholder','appendChild','maskPrompt','fillStyle','rectangle','getImageData','Clear\x20Masks','Confirm\x20Mask','fillRect','error','body','image/jpeg','generate-image-container','prompt-style-input','modal-button','push','modal-canvas','getBoundingClientRect','images','width','style','mouseup','href','disabled','IMAGE_VARIATION','1631yKCiOk','Sizes\x20do\x20not\x20match.\x20Not\x20resolving.','PRECISE','lineTo','border','onclick','✨\x20Make\x20an\x20image!\x20✨','mask-buttons','overflow','Mismatch\x20in\x20sizes\x20-\x20Image:\x20(','1770tIifgk','Switch\x20to\x20Free\x20Draw','get','top','image-controls-container','generate-image-title','local','Error\x20creating\x20outpainting\x20images:\x20','data:image/jpeg;base64,','cols','Sizes\x20match.\x20Resolving\x20with\x20mask.','-container','left','background_suggestion','855621FYDgth','Background\x20Description','value','removeChild','use-variations-checkbox','generate-image-div','moveTo','toDataURL','option','2px\x20solid\x20red','Enter\x20the\x20prompt\x20style','set','fill','getElementById','includes','data','lineWidth','Image\x20resized\x20if\x20needed','storage','22yrrLVd','5108wVkrtu','createElement','background-description-textarea','clientX','span','click','generate-image-button\x20','generate-image-button','clientY','textarea','textContent','strokeStyle','mousemove','runtime','use-auto-mask-checkbox','Keep\x20Exact\x20Item','Generate\x20Image','src','setAttribute','data-text','Could\x20not\x20compress\x20image\x20to\x20less\x20than\x2050KB.','task_params','putImageData','Generating\x20Image...','OUTPAINTING','You\x20must\x20be\x20an\x20Ultimate\x20member\x20to\x20use\x20this\x20feature.','https://generate-images-djybcnnsgq-uc.a.run.app','mousedown','Unable\x20to\x20auto-generate\x20mask\x20image\x20with\x20given\x20input\x20image\x20and\x20mask\x20prompt.\x20Please\x20manually\x20create\x20a\x20mask\x20image\x20using\x20the\x20mask\x20image\x20modal.','label','Error\x20generating\x20image:\x20','canvas','beginPath','htmlFor','floor','naturalHeight','Use\x20Auto\x20Mask','rows','type','naturalWidth','123DfFjoE','You\x20have\x20no\x20credits\x20available.','createOutpaintingImages\x20response','modal-content','Error\x20Generating\x20Image','white','className','keep-exact-item-checkbox','Error\x20creating\x20variation\x20images:\x20','bad\x20quality,\x20low\x20res','replace','button','main_hd_images','addEventListener','add','maskImage','forEach','image-number-dropdown'];a0_0x5af2=function(){return _0x28c412;};return a0_0x5af2();}function createTextArea(_0x45cf50,_0x17c73e,_0xa39fa8,_0x50f972,_0x5e4ff0,_0x16f392=''){var _0x674a1=a0_0x3c15,_0x1fdfcc=document[_0x674a1(0x119)](_0x674a1(0x121));_0x1fdfcc['id']=_0x45cf50,_0x1fdfcc['cols']=_0x17c73e,_0x1fdfcc[_0x674a1(0x13d)]=_0xa39fa8,_0x1fdfcc[_0x674a1(0x17b)]=_0x50f972;_0x16f392!==''&&(_0x1fdfcc[_0x674a1(0x1ae)]=_0x16f392);var _0x465e1e=document[_0x674a1(0x119)](_0x674a1(0x135));_0x465e1e[_0x674a1(0x139)]=_0x45cf50,_0x465e1e[_0x674a1(0x122)]=_0x5e4ff0;var _0x4c7105=document[_0x674a1(0x119)]('div');return _0x4c7105['id']=_0x45cf50+_0x674a1(0x1a9),_0x4c7105[_0x674a1(0x17c)](_0x465e1e),_0x4c7105['appendChild'](_0x1fdfcc),_0x4c7105;}function createInput(_0x51799e,_0x4e341d,_0x3c0897,_0x31e01f,_0x47b460=''){var _0x3cee7d=a0_0x3c15,_0x42b183=document[_0x3cee7d(0x119)](_0x3cee7d(0x16e));_0x42b183['id']=_0x51799e,_0x42b183[_0x3cee7d(0x13e)]=_0x4e341d,_0x42b183[_0x3cee7d(0x17b)]=_0x3c0897,_0x42b183[_0x3cee7d(0x1ae)]=_0x47b460;var _0x268a95=document['createElement']('label');_0x268a95['htmlFor']=_0x51799e,_0x268a95['textContent']=_0x31e01f;var _0x5dac7a=document[_0x3cee7d(0x119)]('div');return _0x5dac7a[_0x3cee7d(0x17c)](_0x268a95),_0x5dac7a[_0x3cee7d(0x17c)](_0x42b183),_0x5dac7a;}function createDropdown(_0x519bde,_0x17fca9,_0x12eff6,_0x2a616d){var _0x1a1c29=a0_0x3c15,_0x29a5a1=document['createElement'](_0x1a1c29(0x15d));_0x29a5a1['id']=_0x519bde;for(let _0x2005e5=0x0;_0x2005e5<_0x12eff6[_0x1a1c29(0x15c)];_0x2005e5++){var _0x638723=document[_0x1a1c29(0x119)](_0x1a1c29(0x1b4));_0x638723[_0x1a1c29(0x1ae)]=_0x12eff6[_0x2005e5],_0x638723[_0x1a1c29(0x16a)]=_0x12eff6[_0x2005e5],_0x29a5a1['appendChild'](_0x638723);}var _0x54a863=document[_0x1a1c29(0x119)](_0x1a1c29(0x135));_0x54a863['htmlFor']=_0x519bde,_0x54a863[_0x1a1c29(0x122)]=_0x17fca9;var _0x521b65=document['createElement'](_0x1a1c29(0x154));return _0x521b65[_0x1a1c29(0x17c)](_0x54a863),_0x521b65[_0x1a1c29(0x17c)](_0x29a5a1),_0x521b65['id']=_0x519bde+_0x1a1c29(0x1a9),_0x2a616d!==''&&(_0x29a5a1[_0x1a1c29(0x1ae)]=_0x2a616d),_0x521b65;}function createGenerateImageDiv(){var _0xd05291=a0_0x3c15,_0x5d4749=document['createElement'](_0xd05291(0x11c));_0x5d4749['id']=_0xd05291(0x1b1);var _0x4c695c=document[_0xd05291(0x119)](_0xd05291(0x154));_0x4c695c['classList'][_0xd05291(0x14e)]('checkbox-div');var {checkbox:_0x4b35fc,container:_0x42194c}=createCheckboxWithLabel('use-auto-mask-checkbox',_0xd05291(0x13c)),{checkbox:_0x2cb024,container:_0x1bafd0}=createCheckboxWithLabel(_0xd05291(0x147),_0xd05291(0x127)),{checkbox:_0x34345b,container:_0x59f005}=createCheckboxWithLabel(_0xd05291(0x1b0),'Use\x20Variation',![]);_0x4c695c['appendChild'](_0x42194c),_0x4c695c['appendChild'](_0x1bafd0),_0x4c695c[_0xd05291(0x17c)](_0x59f005),_0x5d4749[_0xd05291(0x17c)](_0x4c695c);var _0x138825=createButtonWithText(_0xd05291(0x11f),_0xd05291(0x128),[_0xd05291(0x11f)]);_0x138825[_0xd05291(0x199)]=generateImageOnClick;var _0x5b184c=createDropdown('image-number-dropdown',_0xd05291(0x168),['1','2','3'],'1'),_0x1b9ff4=document[_0xd05291(0x119)](_0xd05291(0x154));_0x1b9ff4['classList']['add'](_0xd05291(0x1a2)),_0x1b9ff4['appendChild'](_0x138825),_0x1b9ff4[_0xd05291(0x17c)](_0x5b184c),_0x5d4749[_0xd05291(0x17c)](_0x1b9ff4);var _0x3ed980=createTextArea(_0xd05291(0x11a),0x5,0x3,_0xd05291(0x166),_0xd05291(0x1ad));_0x5d4749[_0xd05291(0x17c)](_0x3ed980);var _0x42eb75=document[_0xd05291(0x119)](_0xd05291(0x154));_0x42eb75['id']=_0xd05291(0x164);var _0x52a9a2=createInput(_0xd05291(0x159),_0xd05291(0x16a),'Enter\x20the\x20item\x20type','Item\x20Type'),_0x54813c='hyper\x20realistic\x20lighting\x20bokeh\x20hyper\x20realistic\x20lighting\x20hyper\x20light\x20refractions',_0x50281e=createInput(_0xd05291(0x188),_0xd05291(0x16a),_0xd05291(0x1b6),_0xd05291(0x162),_0x54813c);_0x42eb75['appendChild'](_0x52a9a2),_0x42eb75[_0xd05291(0x17c)](_0x50281e),_0x5d4749['appendChild'](_0x42eb75);var _0x812c0b=document[_0xd05291(0x119)]('h2');_0x812c0b['id']=_0xd05291(0x1a3),_0x812c0b[_0xd05291(0x12a)](_0xd05291(0x12b),_0xd05291(0x19a)),_0x812c0b[_0xd05291(0x122)]='✨\x20Make\x20an\x20image!\x20✨';var _0x1ad0ef=document['createElement']('div');return _0x1ad0ef['id']=_0xd05291(0x187),_0x1ad0ef[_0xd05291(0x17c)](_0x812c0b),_0x1ad0ef[_0xd05291(0x17c)](_0x5d4749),_0x1ad0ef;}async function createGenerateImageDivV2(){var _0x8495d4=a0_0x3c15,_0x4e8108=document[_0x8495d4(0x119)](_0x8495d4(0x11c));_0x4e8108['id']=_0x8495d4(0x1b1);var _0x469805=createButtonWithText(_0x8495d4(0x11f),_0x8495d4(0x128),[_0x8495d4(0x11f)]);_0x469805[_0x8495d4(0x199)]=generateImageOnClickV2;var {custom_image_prompt:_0x3513cc}=await chrome['storage']['local'][_0x8495d4(0x1a0)]('custom_image_prompt'),_0x3ec04f=document[_0x8495d4(0x119)](_0x8495d4(0x121));return _0x3ec04f['id']=_0x8495d4(0x11a),_0x3ec04f[_0x8495d4(0x1a7)]=0x5,_0x3ec04f[_0x8495d4(0x13d)]=0x3,_0x3ec04f[_0x8495d4(0x17b)]=_0x8495d4(0x166),_0x3ec04f[_0x8495d4(0x1ae)]=_0x3513cc,_0x3ec04f[_0x8495d4(0x14d)](_0x8495d4(0x16e),function(){var _0x50818e=_0x8495d4;chrome[_0x50818e(0x116)][_0x50818e(0x1a4)][_0x50818e(0x1b7)]({'custom_image_prompt':this[_0x50818e(0x1ae)]});}),_0x4e8108[_0x8495d4(0x17c)](_0x3ec04f),_0x4e8108[_0x8495d4(0x17c)](_0x469805),_0x4e8108;}async function generateImageOnClickV2(){}async function changeButtonState(_0x10ade6,_0x4aab71,_0x418649,_0x537e72=0x0){var _0x5ef6d5=a0_0x3c15;_0x10ade6[_0x5ef6d5(0x122)]=_0x4aab71,_0x10ade6[_0x5ef6d5(0x146)]=_0x5ef6d5(0x11e)+_0x418649,_0x537e72>0x0&&setTimeout(()=>{var _0x5be7d3=_0x5ef6d5;_0x10ade6['textContent']='Generate\x20Image',_0x10ade6[_0x5be7d3(0x146)]=_0x5be7d3(0x11f);},_0x537e72);}async function generateImageOnClick(){var _0x2e27f9=a0_0x3c15,_0x2f719c=this,_0x3be1c9=document[_0x2e27f9(0x1b9)](_0x2e27f9(0x159));_0x3be1c9[_0x2e27f9(0x163)]['remove'](_0x2e27f9(0x184));try{changeButtonState(_0x2f719c,_0x2e27f9(0x12f),_0x2e27f9(0x172)),_0x2f719c['disabled']=!![];var _0x5cbdf4=await checkMembership();if(_0x5cbdf4!=_0x2e27f9(0x157))throw new Error(_0x2e27f9(0x131));var _0x1b6741=await checkIfCreditsAreAvailable();if(_0x1b6741==![])throw new Error(_0x2e27f9(0x141));var _0x3be1c9=document[_0x2e27f9(0x1b9)](_0x2e27f9(0x159))[_0x2e27f9(0x1ae)],_0x57118a=document[_0x2e27f9(0x1b9)]('background-description-textarea')[_0x2e27f9(0x1ae)];(_0x3be1c9===''||_0x57118a==='')&&await fetchAndApplyBackgroundAndItemTypeSuggestions();var _0x3230d9=await prepareInputImage(),_0xf918bf=document[_0x2e27f9(0x1b9)](_0x2e27f9(0x151))[_0x2e27f9(0x1ae)];_0xf918bf=parseInt(_0xf918bf);var _0xc0f9b0=await generateEnhancedBackgroundImage(_0x3230d9,_0xf918bf);for(let _0x428161=0x0;_0x428161<_0xc0f9b0[_0x2e27f9(0x15c)];_0x428161++){var _0x322898=_0x2e27f9(0x1a6)+_0xc0f9b0[_0x428161];createImageAndAppendBase64(_0x322898);}_0x2f719c[_0x2e27f9(0x192)]=![];var _0x32c9ed=0.2*_0xf918bf;changeButtonState(_0x2f719c,'Image\x20Generated!',_0x2e27f9(0x170),0xbb8),chrome[_0x2e27f9(0x125)]['sendMessage']({'type':'deductCredits','amount':_0x32c9ed});}catch(_0x697ece){console[_0x2e27f9(0x184)](_0x2e27f9(0x136),_0x697ece),changeButtonState(_0x2f719c,_0x2e27f9(0x144),'generate-image-button-error',0xbb8),_0x2f719c[_0x2e27f9(0x192)]=![],alert(_0x697ece['message']);}}async function prepareInputImage(){var _0x3518b9=a0_0x3c15,_0x458eba=product_data[_0x3518b9(0x14c)][0x0],_0x406164=await urlToImage(_0x458eba);return _0x406164=await makeImageAspectSame(_0x406164),_0x406164=await resizeImageIfNeeded(_0x406164),_0x406164['src'];}async function fetchAndApplyBackgroundAndItemTypeSuggestions(){var _0x434d27=a0_0x3c15,_0x596b37='https://suggest-background-djybcnnsgq-uc.a.run.app',_0x24c313=await postToServer(_0x596b37,{'item_title':product_data[_0x434d27(0x175)]});_0x24c313=JSON[_0x434d27(0x16c)](_0x24c313);var _0x80d9b9=_0x24c313['item_type'],_0x372df9=_0x24c313[_0x434d27(0x1ab)],_0x2c0f9e=document['getElementById'](_0x434d27(0x159)),_0x1ee557=document[_0x434d27(0x1b9)](_0x434d27(0x11a));_0x2c0f9e['value']===''&&(_0x2c0f9e[_0x434d27(0x1ae)]=_0x80d9b9),_0x1ee557[_0x434d27(0x1ae)]===''&&(_0x1ee557[_0x434d27(0x1ae)]=_0x372df9);}async function generateEnhancedBackgroundImage(_0x9756cc,_0x1ab1cd=0x3){var _0x2f76e3=a0_0x3c15,_0x187b66=document[_0x2f76e3(0x1b9)](_0x2f76e3(0x126))[_0x2f76e3(0x156)],_0x2e1420=document[_0x2f76e3(0x1b9)](_0x2f76e3(0x147))[_0x2f76e3(0x156)],_0x16ebdb=document[_0x2f76e3(0x1b9)]('use-variations-checkbox')[_0x2f76e3(0x156)],_0x5a6d77=document[_0x2f76e3(0x1b9)](_0x2f76e3(0x11a))['value'],_0x4bcfed=document[_0x2f76e3(0x1b9)]('prompt-style-input')[_0x2f76e3(0x1ae)],_0x599596=document[_0x2f76e3(0x1b9)](_0x2f76e3(0x159))[_0x2f76e3(0x1ae)];if(_0x16ebdb){var _0x4d9fae=await createVariationImages(_0x9756cc,_0x5a6d77+','+_0x4bcfed,_0x1ab1cd);return _0x4d9fae;}var _0x414915=_0x2f76e3(0x196);!_0x2e1420&&(_0x414915=_0x2f76e3(0x155));var _0x4d9fae=await createOutpaintingImages(_0x9756cc,_0x5a6d77+','+_0x4bcfed,_0x414915,_0x599596,_0x187b66,_0x1ab1cd);return _0x4d9fae;}async function createOutpaintingImages(_0x3258cf,_0x22a6ba,_0xb1083f,_0x5d598c,_0x4d55f0=!![],_0x2d7e1f=0x3){var _0x59a231=a0_0x3c15,_0x8c2730={'task_params':{'taskType':_0x59a231(0x130),'outPaintingParams':{'text':_0x22a6ba,'image':cleanBase64(_0x3258cf),'outPaintingMode':_0xb1083f}},'num_image':_0x2d7e1f};_0x4d55f0&&(_0x8c2730[_0x59a231(0x12d)]['outPaintingParams'][_0x59a231(0x17d)]=_0x5d598c);if(!_0x4d55f0){var _0x566797=await createImageMaskingModal(_0x3258cf);_0x566797=cleanBase64(_0x566797),_0x8c2730[_0x59a231(0x12d)][_0x59a231(0x15a)][_0x59a231(0x14f)]=_0x566797;}var _0x2b1ddb,_0x23a7d9=_0x59a231(0x132);try{_0x2b1ddb=await postToServer2(_0x23a7d9,{..._0x8c2730});}catch(_0x57ce92){console[_0x59a231(0x184)](_0x59a231(0x1a5),_0x57ce92);if(_0x57ce92[_0x59a231(0x1ba)](_0x59a231(0x152))){var _0x5d598c=document['getElementById'](_0x59a231(0x159));_0x5d598c['classList'][_0x59a231(0x14e)](_0x59a231(0x184)),alert(_0x59a231(0x134));}return;}return console['log'](_0x59a231(0x142),_0x2b1ddb),_0x2b1ddb[_0x59a231(0x18d)];}function a0_0x3c15(_0x174cd1,_0x536325){var _0x5af27e=a0_0x5af2();return a0_0x3c15=function(_0x3c15bb,_0x38ec5b){_0x3c15bb=_0x3c15bb-0x114;var _0x5ac6dc=_0x5af27e[_0x3c15bb];return _0x5ac6dc;},a0_0x3c15(_0x174cd1,_0x536325);}async function createVariationImages(_0xa7f643,_0x430e74,_0x4e1f96=0x3){var _0xff107=a0_0x3c15,_0x443162={'task_params':{'taskType':_0xff107(0x193),'imageVariationParams':{'text':_0x430e74,'images':[cleanBase64(_0xa7f643)],'negativeText':_0xff107(0x149)}},'num_image':_0x4e1f96},_0x3c71c9,_0x3030fe=_0xff107(0x132);try{_0x3c71c9=await postToServer2(_0x3030fe,{..._0x443162});}catch(_0x37daea){console[_0xff107(0x184)](_0xff107(0x148),_0x37daea);return;}return console['log']('createVariationImages\x20response',_0x3c71c9),_0x3c71c9[_0xff107(0x18d)];}function cleanBase64(_0x29190c){var _0xf26a0a=a0_0x3c15;return _0x29190c[_0xf26a0a(0x14a)](/^data:image\/[a-z]+;base64,/,'');}function downloadImage(_0x5ac98e){var _0x3a8832=a0_0x3c15,_0x5ecb36=document[_0x3a8832(0x119)]('a');_0x5ecb36[_0x3a8832(0x191)]=_0x5ac98e,_0x5ecb36['download']=_0x3a8832(0x153),document[_0x3a8832(0x185)]['appendChild'](_0x5ecb36),_0x5ecb36[_0x3a8832(0x11d)](),document[_0x3a8832(0x185)][_0x3a8832(0x1af)](_0x5ecb36);}function compressImageToUnder50kb(_0x263b0a){return new Promise((_0x3a7da4,_0x26e4d0)=>{var _0x1bb41f=a0_0x3c15;const _0x2c15bc=0x19*0x400;let _0x433079=document[_0x1bb41f(0x119)](_0x1bb41f(0x137)),_0x43556d=_0x433079['getContext']('2d'),_0x318e60=0.8;_0x433079[_0x1bb41f(0x18e)]=_0x263b0a[_0x1bb41f(0x18e)],_0x433079[_0x1bb41f(0x15b)]=_0x263b0a[_0x1bb41f(0x15b)];let _0x3cd3ad=0.95,_0x4d4874=null;function _0x5a91f9(){var _0x28c444=_0x1bb41f;_0x43556d[_0x28c444(0x16b)](0x0,0x0,_0x433079[_0x28c444(0x18e)],_0x433079[_0x28c444(0x15b)]),_0x433079['width']=_0x263b0a['width']*_0x3cd3ad,_0x433079['height']=_0x263b0a['height']*_0x3cd3ad,_0x43556d[_0x28c444(0x16f)](_0x263b0a,0x0,0x0,_0x433079[_0x28c444(0x18e)],_0x433079[_0x28c444(0x15b)]);var _0xc13757=_0x433079[_0x28c444(0x1b3)](_0x28c444(0x186),_0x318e60);if(_0xc13757[_0x28c444(0x15c)]<_0x2c15bc){let _0x367448=new Image();_0x367448['onload']=()=>_0x3a7da4(_0x367448),_0x367448[_0x28c444(0x129)]=_0xc13757;return;}else{if(_0x318e60>0.1)_0x318e60-=0.05;else{if(_0x3cd3ad>0.5)_0x3cd3ad-=0.05,_0x318e60=0.8;else{if(_0x4d4874){let _0x356a6b=new Image();_0x356a6b['onload']=()=>_0x3a7da4(_0x356a6b),_0x356a6b[_0x28c444(0x129)]=_0x4d4874;return;}else{_0x26e4d0(new Error(_0x28c444(0x12c)));return;}}}_0x4d4874=_0xc13757,_0x5a91f9();}}_0x5a91f9();});}async function ensureWhitespace(_0x465e30,_0x4a5ace=0.2,_0x48ca7d=0x400){var _0xe5f056=a0_0x3c15;let _0x2d40b1=_0x465e30[_0xe5f056(0x13f)],_0x7e8db5=_0x465e30[_0xe5f056(0x13b)],_0xc7f390=Math[_0xe5f056(0x158)](_0x2d40b1,_0x7e8db5),_0x3be7d3=Math[_0xe5f056(0x13a)](_0xc7f390*_0x4a5ace),_0xf05e59=_0x2d40b1+0x2*_0x3be7d3,_0x702b95=_0x7e8db5+0x2*_0x3be7d3,_0x40185f=document[_0xe5f056(0x119)](_0xe5f056(0x137));_0x40185f[_0xe5f056(0x18e)]=_0xf05e59,_0x40185f[_0xe5f056(0x15b)]=_0x702b95;let _0x45566b=_0x40185f[_0xe5f056(0x17a)]('2d');_0x45566b[_0xe5f056(0x17e)]='white',_0x45566b[_0xe5f056(0x183)](0x0,0x0,_0xf05e59,_0x702b95),_0x45566b[_0xe5f056(0x16f)](_0x465e30,_0x3be7d3,_0x3be7d3,_0x2d40b1,_0x7e8db5);if(Math[_0xe5f056(0x173)](_0xf05e59,_0x702b95)>_0x48ca7d){let _0x30b825=Math[_0xe5f056(0x158)](_0x48ca7d/_0xf05e59,_0x48ca7d/_0x702b95),_0x2501db=Math[_0xe5f056(0x13a)](_0xf05e59*_0x30b825),_0x2aadf9=Math['floor'](_0x702b95*_0x30b825),_0x8f3d34=document['createElement'](_0xe5f056(0x137));_0x8f3d34['width']=_0x2501db,_0x8f3d34[_0xe5f056(0x15b)]=_0x2aadf9;let _0xb9b20f=_0x8f3d34[_0xe5f056(0x17a)]('2d');_0xb9b20f['drawImage'](_0x40185f,0x0,0x0,_0x2501db,_0x2aadf9),_0x465e30[_0xe5f056(0x129)]=_0x8f3d34[_0xe5f056(0x1b3)]();}else _0x465e30['src']=_0x40185f[_0xe5f056(0x1b3)]();return _0x465e30;}async function resizeImageIfNeeded(_0x2334b1,_0x22c7d9=0x400){var _0x1ecb8b=a0_0x3c15;let _0x31f39f=_0x2334b1[_0x1ecb8b(0x13f)],_0xa6389b=_0x2334b1[_0x1ecb8b(0x13b)];if(Math[_0x1ecb8b(0x173)](_0x31f39f,_0xa6389b)>_0x22c7d9){let _0x11c73c,_0x223e11;_0x31f39f>_0xa6389b?(_0x11c73c=_0x22c7d9,_0x223e11=Math[_0x1ecb8b(0x13a)](_0x22c7d9*_0xa6389b/_0x31f39f)):(_0x223e11=_0x22c7d9,_0x11c73c=Math[_0x1ecb8b(0x13a)](_0x22c7d9*_0x31f39f/_0xa6389b));let _0x5b1022=document[_0x1ecb8b(0x119)](_0x1ecb8b(0x137));_0x5b1022['width']=_0x11c73c,_0x5b1022[_0x1ecb8b(0x15b)]=_0x223e11;let _0x4e8c2d=_0x5b1022[_0x1ecb8b(0x17a)]('2d');_0x4e8c2d['drawImage'](_0x2334b1,0x0,0x0,_0x11c73c,_0x223e11),_0x2334b1[_0x1ecb8b(0x129)]=_0x5b1022[_0x1ecb8b(0x1b3)]();}return console[_0x1ecb8b(0x178)](_0x1ecb8b(0x115)),_0x2334b1;}function createImageMaskingModal(_0x504d67){return new Promise((_0x46a06a,_0x162d58)=>{var _0x212158=a0_0x3c15;let _0x549888=new Image();_0x549888['src']=_0x504d67;if(!_0x549888[_0x212158(0x160)]||_0x549888[_0x212158(0x13b)]===0x0){_0x162d58(new Error('Image\x20has\x20not\x20been\x20loaded.'));return;}let _0x241894=![],_0x5e3630=0x0,_0x1009ad=0x0;var _0x309319=[];let _0x596c15=document[_0x212158(0x119)]('div'),_0xf945ea=document[_0x212158(0x119)](_0x212158(0x154)),_0x4a1bde=document[_0x212158(0x119)](_0x212158(0x137)),_0x5a896a=document['createElement'](_0x212158(0x137)),_0x48fcd6=document[_0x212158(0x119)]('button'),_0x455f5b=document[_0x212158(0x119)](_0x212158(0x14b)),_0x2abefa=document[_0x212158(0x119)](_0x212158(0x14b)),_0x1e3bf7=_0x4a1bde[_0x212158(0x17a)]('2d'),_0x12ea3d=_0x5a896a[_0x212158(0x17a)]('2d');_0x596c15[_0x212158(0x146)]='modal-overlay',_0xf945ea[_0x212158(0x146)]=_0x212158(0x143),_0x4a1bde[_0x212158(0x146)]=_0x212158(0x18b),_0x5a896a[_0x212158(0x146)]=_0x212158(0x18b),_0x48fcd6['className']='modal-button',_0x455f5b['className']=_0x212158(0x189),_0x2abefa[_0x212158(0x146)]=_0x212158(0x189),_0x4a1bde['width']=_0x549888['naturalWidth'],_0x4a1bde['height']=_0x549888['naturalHeight'],_0x5a896a[_0x212158(0x18e)]=_0x549888[_0x212158(0x13f)],_0x5a896a['height']=_0x549888[_0x212158(0x13b)],_0x4a1bde['style'][_0x212158(0x198)]=_0x212158(0x1b5),_0xf945ea[_0x212158(0x18f)][_0x212158(0x19c)]='auto',_0x1e3bf7[_0x212158(0x16f)](_0x549888,0x0,0x0,_0x4a1bde[_0x212158(0x18e)],_0x4a1bde[_0x212158(0x15b)]),_0x12ea3d['fillStyle']='white',_0x12ea3d[_0x212158(0x183)](0x0,0x0,_0x5a896a[_0x212158(0x18e)],_0x5a896a[_0x212158(0x15b)]);let _0x46dd31=[],_0x115486=[],_0x1054c9=_0x212158(0x169);_0x2abefa['textContent']='Switch\x20to\x20Rectangle\x20Draw';function _0xc85ae1(){var _0x65bd48=_0x212158;_0x4a1bde[_0x65bd48(0x14d)](_0x65bd48(0x133),_0xc09804=>{var _0x92865e=_0x65bd48;const _0x5a10a2=_0x4a1bde[_0x92865e(0x18c)]();_0x5e3630=_0xc09804['clientX']-_0x5a10a2[_0x92865e(0x1aa)],_0x1009ad=_0xc09804[_0x92865e(0x120)]-_0x5a10a2[_0x92865e(0x1a1)],_0x241894=!![],_0x1054c9===_0x92865e(0x169)&&(_0x309319=[[_0x5e3630,_0x1009ad]]);}),_0x4a1bde['addEventListener'](_0x65bd48(0x124),_0x28554b=>{var _0x484ffa=_0x65bd48;if(!_0x241894)return;const _0x39f648=_0x4a1bde[_0x484ffa(0x18c)](),_0x221f1d=_0x28554b[_0x484ffa(0x11b)]-_0x39f648[_0x484ffa(0x1aa)],_0x282c88=_0x28554b[_0x484ffa(0x120)]-_0x39f648[_0x484ffa(0x1a1)];if(_0x1054c9==='freeDraw')_0x309319['push']([_0x221f1d,_0x282c88]),_0xbd1d69(_0x221f1d,_0x282c88,!![]);else _0x1054c9===_0x484ffa(0x17f)&&_0xbd1d69(_0x221f1d,_0x282c88);}),_0x4a1bde[_0x65bd48(0x14d)](_0x65bd48(0x190),_0x1550b8=>{var _0x2247ab=_0x65bd48;if(!_0x241894)return;_0x241894=![];if(_0x1054c9===_0x2247ab(0x17f))_0x4dd092(_0x5e3630,_0x1009ad,_0x1550b8[_0x2247ab(0x11b)]-_0x4a1bde[_0x2247ab(0x18c)]()['left'],_0x1550b8[_0x2247ab(0x120)]-_0x4a1bde[_0x2247ab(0x18c)]()[_0x2247ab(0x1a1)]);else _0x1054c9===_0x2247ab(0x169)&&(_0x115486[_0x2247ab(0x18a)](_0x309319),_0x309319=[],_0x5e0606());});}function _0x4dd092(_0x49d067,_0x139b83,_0x38fbf9,_0x5dfb42){var _0x2502c9=_0x212158;const _0x537c24=_0x38fbf9-_0x49d067,_0x3372e8=_0x5dfb42-_0x139b83;_0x46dd31[_0x2502c9(0x18a)]({'x':_0x49d067,'y':_0x139b83,'w':_0x537c24,'h':_0x3372e8}),_0xbd1d69(),_0x5e0606();}function _0xbd1d69(_0x5f4b07=_0x5e3630,_0x103b9d=_0x1009ad,_0x5031cb=![]){var _0x2ebdf2=_0x212158;_0x1e3bf7['clearRect'](0x0,0x0,_0x4a1bde['width'],_0x4a1bde[_0x2ebdf2(0x15b)]),_0x1e3bf7[_0x2ebdf2(0x16f)](_0x549888,0x0,0x0,_0x4a1bde[_0x2ebdf2(0x18e)],_0x4a1bde[_0x2ebdf2(0x15b)]),_0x46dd31['forEach'](_0x435386=>{var _0x3b8a8b=_0x2ebdf2;_0x1e3bf7[_0x3b8a8b(0x16d)](_0x435386['x'],_0x435386['y'],_0x435386['w'],_0x435386['h']);}),_0x115486[_0x2ebdf2(0x150)](_0x16e9c4=>{var _0x45a8a9=_0x2ebdf2;_0x1e3bf7[_0x45a8a9(0x138)](),_0x1e3bf7[_0x45a8a9(0x1b2)](_0x16e9c4[0x0][0x0],_0x16e9c4[0x0][0x1]);for(let _0x212338=0x1;_0x212338<_0x16e9c4[_0x45a8a9(0x15c)];_0x212338++){_0x1e3bf7[_0x45a8a9(0x197)](_0x16e9c4[_0x212338][0x0],_0x16e9c4[_0x212338][0x1]);}_0x1e3bf7[_0x45a8a9(0x179)]();});if(_0x5031cb){_0x1e3bf7[_0x2ebdf2(0x138)](),_0x1e3bf7[_0x2ebdf2(0x1b2)](_0x309319[0x0][0x0],_0x309319[0x0][0x1]);for(let _0x120b43=0x1;_0x120b43<_0x309319[_0x2ebdf2(0x15c)];_0x120b43++){_0x1e3bf7[_0x2ebdf2(0x197)](_0x309319[_0x120b43][0x0],_0x309319[_0x120b43][0x1]);}_0x1e3bf7[_0x2ebdf2(0x114)]=0x2,_0x1e3bf7[_0x2ebdf2(0x123)]='black',_0x1e3bf7[_0x2ebdf2(0x179)]();}else{if(_0x1054c9===_0x2ebdf2(0x17f)&&_0x241894){const _0x270304=_0x5f4b07-_0x5e3630,_0x3e8369=_0x103b9d-_0x1009ad;_0x1e3bf7[_0x2ebdf2(0x16d)](_0x5e3630,_0x1009ad,_0x270304,_0x3e8369);}}}function _0x5e0606(){var _0x2f2c22=_0x212158;_0x12ea3d[_0x2f2c22(0x16b)](0x0,0x0,_0x5a896a['width'],_0x5a896a[_0x2f2c22(0x15b)]),_0x12ea3d[_0x2f2c22(0x17e)]=_0x2f2c22(0x145),_0x12ea3d['fillRect'](0x0,0x0,_0x5a896a[_0x2f2c22(0x18e)],_0x5a896a[_0x2f2c22(0x15b)]),_0x12ea3d[_0x2f2c22(0x17e)]=_0x2f2c22(0x167),_0x46dd31[_0x2f2c22(0x150)](_0xce062f=>{var _0x1067be=_0x2f2c22;_0x12ea3d[_0x1067be(0x183)](_0xce062f['x'],_0xce062f['y'],_0xce062f['w'],_0xce062f['h']);}),_0x115486[_0x2f2c22(0x150)](_0x3f6fe4=>{var _0x122efa=_0x2f2c22;_0x12ea3d[_0x122efa(0x138)](),_0x12ea3d[_0x122efa(0x1b2)](_0x3f6fe4[0x0][0x0],_0x3f6fe4[0x0][0x1]);for(let _0x1aaf5d=0x1;_0x1aaf5d<_0x3f6fe4[_0x122efa(0x15c)];_0x1aaf5d++){_0x12ea3d[_0x122efa(0x197)](_0x3f6fe4[_0x1aaf5d][0x0],_0x3f6fe4[_0x1aaf5d][0x1]);}_0x12ea3d[_0x122efa(0x1b8)]();});let _0x1b1c0c=_0x12ea3d[_0x2f2c22(0x180)](0x0,0x0,_0x5a896a[_0x2f2c22(0x18e)],_0x5a896a['height']),_0x4973ed=_0x1b1c0c[_0x2f2c22(0x1bb)];for(let _0x5e8f1b=0x0;_0x5e8f1b<_0x4973ed[_0x2f2c22(0x15c)];_0x5e8f1b+=0x4){_0x4973ed[_0x5e8f1b]>0x0&&(_0x4973ed[_0x5e8f1b]=0xff,_0x4973ed[_0x5e8f1b+0x1]=0xff,_0x4973ed[_0x5e8f1b+0x2]=0xff);}_0x12ea3d[_0x2f2c22(0x12e)](_0x1b1c0c,0x0,0x0);}_0x2abefa[_0x212158(0x14d)](_0x212158(0x11d),()=>{var _0x48bf00=_0x212158;_0x1054c9===_0x48bf00(0x169)?(_0x1054c9=_0x48bf00(0x17f),_0x2abefa['textContent']=_0x48bf00(0x19f)):(_0x1054c9='freeDraw',_0x2abefa[_0x48bf00(0x122)]='Switch\x20to\x20Rectangle\x20Draw');}),_0x48fcd6[_0x212158(0x122)]=_0x212158(0x182),_0x48fcd6[_0x212158(0x14d)](_0x212158(0x11d),()=>{var _0x593233=_0x212158;console[_0x593233(0x178)](_0x593233(0x165),_0x549888[_0x593233(0x13f)],'x',_0x549888[_0x593233(0x13b)]),console[_0x593233(0x178)]('Mask\x20Size:',_0x5a896a['width'],'x',_0x5a896a['height']),_0x549888['naturalWidth']===_0x5a896a[_0x593233(0x18e)]&&_0x549888[_0x593233(0x13b)]===_0x5a896a[_0x593233(0x15b)]?(console[_0x593233(0x178)](_0x593233(0x1a8)),_0x46a06a(_0x5a896a[_0x593233(0x1b3)]())):(console[_0x593233(0x178)](_0x593233(0x195)),_0x162d58(new Error(_0x593233(0x19d)+_0x549888['naturalWidth']+'x'+_0x549888[_0x593233(0x13b)]+_0x593233(0x176)+_0x5a896a[_0x593233(0x18e)]+'x'+_0x5a896a[_0x593233(0x15b)]+')'))),document['body'][_0x593233(0x1af)](_0x596c15);}),_0x455f5b[_0x212158(0x122)]=_0x212158(0x181),_0x455f5b[_0x212158(0x14d)](_0x212158(0x11d),()=>{var _0x2b7012=_0x212158;_0x46dd31=[],_0x115486=[],_0x1e3bf7[_0x2b7012(0x16b)](0x0,0x0,_0x4a1bde[_0x2b7012(0x18e)],_0x4a1bde['height']),_0x1e3bf7['drawImage'](_0x549888,0x0,0x0,_0x4a1bde[_0x2b7012(0x18e)],_0x4a1bde[_0x2b7012(0x15b)]),_0x5e0606();});var _0x16b460=document[_0x212158(0x119)](_0x212158(0x154));_0x16b460[_0x212158(0x163)][_0x212158(0x14e)](_0x212158(0x19b)),_0x16b460['appendChild'](_0x2abefa),_0x16b460[_0x212158(0x17c)](_0x48fcd6),_0x16b460[_0x212158(0x17c)](_0x455f5b),_0xf945ea[_0x212158(0x17c)](_0x16b460),_0xf945ea['appendChild'](_0x4a1bde),_0xf945ea[_0x212158(0x17c)](_0x5a896a),_0x596c15['appendChild'](_0xf945ea),document['body'][_0x212158(0x17c)](_0x596c15),_0xc85ae1(),_0x5e0606();});}