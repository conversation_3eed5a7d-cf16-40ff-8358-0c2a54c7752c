{"manifest_version": 3, "name": "Ebay Sniper 4", "version": "18.2", "permissions": ["storage", "tabs", "activeTabs", "webRequest", "activeTab", "clipboardRead", "clipboardWrite", "contentSettings", "contextMenus", "cookies", "history", "scripting", "contextMenus"], "host_permissions": ["https://*.etsy.ca/* ", "https://cors-anywhere.herokuapp.com/*", "*://amazon.ca/*", "*://amazon.com/*", "*://etsy.ca/*", "*://*.amazon.ca/*", "*://*.amazon.com/*", "*://*.etsy.ca/*", "https://*.etsy.ca/* ", "https://cors-anywhere.herokuapp.com/*", "file:///*/", "*://*.ebay.ca/*", "<all_urls>", "*://*/*", "http://127.0.0.1:9003/", "http://localhost:9003/", "http://********:3102/", "http://********:1102/", "https://script.google.com/macros/s/*", "https://script.google.com/*"], "web_accessible_resources": [{"resources": ["dictionary/dictionary.json"], "matches": ["<all_urls>"]}, {"resources": ["js/iframe_inject.min.js"], "matches": ["<all_urls>"]}, {"resources": ["Ebay_Description_Templates/Template_with_head.html", "Ebay_Description_Templates/Shipping_Handling.html", "Ebay_Description_Templates/*"], "matches": ["<all_urls>"]}, {"resources": ["text_prompts/*"], "matches": ["<all_urls>"]}, {"resources": ["Call_To_Action_HTML/call_to_action.Html"], "matches": ["<all_urls>"]}, {"resources": ["Favicons/*"], "matches": ["https://*/*"]}, {"resources": ["VeroList.txt"], "matches": ["https://*/*"]}, {"resources": ["Image_Badges/*"], "matches": ["<all_urls>"]}, {"resources": ["icons/Sniper.png"], "matches": ["<all_urls>"]}], "options_page": "options/options.html", "icons": {"16": "icons/Sniper.png", "32": "icons/Sniper.png", "48": "icons/Sniper.png", "128": "icons/Sniper.png"}, "content_scripts": [{"matches": ["*://*.amazon.com/*dp*", "*://*.amazon.ca/*dp*", "*://*.amazon.co.uk/*dp*", "*://*.amazon.com.au/*dp*", "*://*.amazon.de/*dp*", "*://*.amazon.fr/*dp*", "*://*.amazon.it/*dp*", "*://*.amazon.es/*dp*", "*://*.amazon.nl/*dp*", "*://*.amazon.co.jp/*dp*", "*://*.amazon.in/*dp*", "*://*.amazon.com.br/*dp*", "*://*.amazon.com.mx/*dp*"], "js": ["libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/jquery.highlight-within-textarea.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/google_sheets_utils.js", "libraries/animation_utils.js", "libraries/jsonrepair.js", "content/amazon/amazon_ai_title_functions.js", "content/amazon/amazon.functions.js", "content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_vero_list_new.js", "content/amazon/amazon_vero_list_old.js", "content/amazon/amazon_vero_list_personal.js", "content/amazon/amazon_vero_list_from_ebay.js", "content/amazon/amazon_vero_function.js", "content/amazon/amazon.desc_template.js", "content/amazon/amazon.img_function.js", "content/amazon/amazon_title_builder.js", "content/amazon/amazon_auto_list_functions.js", "content/amazon/amazon_auto_list.js", "content/amazon/amazon.js", "content/amazon/amazon_check_duplicate.js", "content/amazon/amazon_image_test.js"], "css": ["content/amazon/amazon.css", "libraries/jquery.highlight-within-textarea.css"], "run_at": "document_idle"}, {"matches": ["https://www.amazon.ca/s?k=*"], "js": ["content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_search_page_functions.js", "content/amazon/amazon_search_page.js"]}, {"matches": ["https://www.etsy.com/your/shops/*/tools/listings/*", "https://www.etsy.com/your/listings/create?ref=listings_manager_prototype&from_page=/your/listings", "https://www.etsy.com/your/shops/MomAndPopLovesToShop/tools/listings/create"], "js": ["libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/html-to-img.js", "libraries/custom_utils.js", "libraries/image_utils.js", "content/etsy/listing_functions.js", "content/etsy/listing_page.js"], "run_at": "document_idle"}, {"all_frames": true, "matches": ["https://www.picupload.ebay.ca/*"], "js": ["libraries/chrome_storage_utils.js", "libraries/image_utils.js", "content/ebay/picupload.js"], "css": ["content/ebay/ebay.css"], "run_at": "document_idle"}, {"all_frames": true, "matches": ["https://bulkedit.ebay.ca/*"], "js": ["libraries/chrome_storage_utils.js", "libraries/custom_utils.js", "content/ebay/promoted_listing.js"], "run_at": "document_idle"}, {"all_frames": false, "matches": ["*://bulksell.ebay.com/ws/*", "*://bulksell.ebay.ca/ws/*", "*://bulksell.ebay.co.uk/ws/*", "*://bulksell.ebay.com.au/ws/*", "*://bulksell.ebay.de/ws/*", "*://bulksell.ebay.fr/ws/*", "*://bulksell.ebay.it/ws/*", "*://bulksell.ebay.es/ws/*", "*://bulksell.ebay.nl/ws/*", "*://bulksell.ebay.com.hk/ws/*", "*://bulksell.ebay.in/ws/*", "*://bulksell.ebay.com.sg/ws/*", "*://bulksell.ebay.com.my/ws/*", "*://bulksell.ebay.ie/ws/*", "*://bulksell.ebay.ch/ws/*", "*://bulksell.ebay.at/ws/*", "*://bulksell.ebay.be/ws/*", "*://bulksell.ebay.com.tw/ws/*", "*://bulksell.ebay.com.ph/ws/*", "*://*.ebay.com/lstng?draftId=*", "*://*.ebay.ca/lstng?draftId=*", "*://*.ebay.co.uk/lstng?draftId=*", "*://*.ebay.com.au/lstng?draftId=*", "*://*.ebay.de/lstng?draftId=*", "*://*.ebay.fr/lstng?draftId=*", "*://*.ebay.it/lstng?draftId=*", "*://*.ebay.es/lstng?draftId=*", "*://*.ebay.nl/lstng?draftId=*", "*://*.ebay.com.hk/lstng?draftId=*", "*://*.ebay.in/lstng?draftId=*", "*://*.ebay.com.sg/lstng?draftId=*", "*://*.ebay.com.my/lstng?draftId=*", "*://*.ebay.ie/lstng?draftId=*", "*://*.ebay.ch/lstng?draftId=*", "*://*.ebay.at/lstng?draftId=*", "*://*.ebay.be/lstng?draftId=*", "*://*.ebay.com.tw/lstng?draftId=*", "*://*.ebay.com.ph/lstng?draftId=*"], "js": ["libraries/gpt_encoder_browser_compatible.js", "libraries/change-favicon.js", "libraries/open_ai_content_script_functions.js", "libraries/find_element_util.js", "libraries/chrome_storage_utils.js", "libraries/jquery.min.js", "libraries/image_transform_utils.js", "libraries/image_utils.js", "libraries/custom_utils.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/post_to_server_utils.js", "content/ebay/ebay_item_specifics_functions.js", "content/ebay/ebay_item_specifics_ai_functions.js", "content/ebay/ebay.functions.js", "content/ebay/ebay.desc_function.js", "content/ebay/ebay.image_functions.js", "content/ebay/ebay.js"], "css": ["content/ebay/ebay.css"], "run_at": "document_idle"}, {"all_frames": false, "matches": ["*://*.ebay.com/sl/prelist/suggest?*", "*://*.ebay.ca/sl/prelist/suggest?*", "*://*.ebay.co.uk/sl/prelist/suggest?*", "*://*.ebay.com.au/sl/prelist/suggest?*", "*://*.ebay.de/sl/prelist/suggest?*", "*://*.ebay.fr/sl/prelist/suggest?*", "*://*.ebay.it/sl/prelist/suggest?*", "*://*.ebay.es/sl/prelist/suggest?*", "*://*.ebay.nl/sl/prelist/suggest?*", "*://*.ebay.com.hk/sl/prelist/suggest?*", "*://*.ebay.in/sl/prelist/suggest?*", "*://*.ebay.com.sg/sl/prelist/suggest?*", "*://*.ebay.com.my/sl/prelist/suggest?*", "*://*.ebay.ie/sl/prelist/suggest?*", "*://*.ebay.ch/sl/prelist/suggest?*", "*://*.ebay.at/sl/prelist/suggest?*", "*://*.ebay.be/sl/prelist/suggest?*", "*://*.ebay.com.tw/sl/prelist/suggest?*", "*://*.ebay.com.ph/sl/prelist/suggest?*"], "js": ["libraries/jquery.min.js", "libraries/custom_utils.js", "libraries/post_to_server_utils.js", "content/ebay/ebay_pre_list_suggest_functions.js", "content/ebay/ebay_pre_list_suggest.js"], "run_at": "document_idle"}, {"all_frames": false, "matches": ["*://*.ebay.com/sl/prelist/identify?*", "*://*.ebay.ca/sl/prelist/identify?*", "*://*.ebay.co.uk/sl/prelist/identify?*", "*://*.ebay.com.au/sl/prelist/identify?*", "*://*.ebay.de/sl/prelist/identify?*", "*://*.ebay.fr/sl/prelist/identify?*", "*://*.ebay.it/sl/prelist/identify?*", "*://*.ebay.es/sl/prelist/identify?*", "*://*.ebay.nl/sl/prelist/identify?*", "*://*.ebay.com.hk/sl/prelist/identify?*", "*://*.ebay.in/sl/prelist/identify?*", "*://*.ebay.com.sg/sl/prelist/identify?*", "*://*.ebay.com.my/sl/prelist/identify?*", "*://*.ebay.ie/sl/prelist/identify?*", "*://*.ebay.ch/sl/prelist/identify?*", "*://*.ebay.at/sl/prelist/identify?*", "*://*.ebay.be/sl/prelist/identify?*", "*://*.ebay.com.tw/sl/prelist/identify?*", "*://*.ebay.com.ph/sl/prelist/identify?*"], "js": ["libraries/jquery.min.js", "libraries/custom_utils.js", "libraries/post_to_server_utils.js", "content/ebay/ebay_pre_list_identify_functions.js", "content/ebay/ebay_pre_list_identify.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/sh/lst/active*"], "js": ["libraries/jquery.min.js", "content/ebay/ebay.active_listings.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/sh/research*", "https://www.ebay.com/sh/research*"], "js": ["libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/slot_machine_utils.js", "libraries/post_to_server_utils.js", "libraries/amazon_search_utils.js", "content/ebay/terapeak_functions.js", "content/ebay/terapeak.js"], "css": ["content/ebay/slot_machine.css", "content/ebay/button.css"], "run_at": "document_idle"}, {"matches": ["https://www.zikanalytics.com/SearchCompetitor/Index?search=true&Competitor=*", "https://www.zikanalytics.com/CategoryResearch/Result?*", "https://www.zikanalytics.com/SearchCompetitor/Index"], "js": ["libraries/post_to_server_utils.js", "libraries/amazon_search_utils.js", "content/zikanalytics/search_competitor/functions.js", "content/zikanalytics/search_competitor/content.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/str/*"], "js": ["libraries/post_to_server_utils.js", "libraries/amazon_search_utils.js", "content/ebay/store/functions.js", "content/ebay/store/content.js"], "css": ["content/ebay/store/button.css"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/itm/*"], "js": ["content/ebay/item/functions.js", "content/ebay/item/content.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup/popup.html"}, "background": {"service_worker_script": [{"type": "module", "extension": "js", "value": "background.js"}, {"type": "module", "extension": "js", "value": "libraries/firebase-main.js"}]}, "key": "abcdefghijklmnopqrstuvwxyz123456"}