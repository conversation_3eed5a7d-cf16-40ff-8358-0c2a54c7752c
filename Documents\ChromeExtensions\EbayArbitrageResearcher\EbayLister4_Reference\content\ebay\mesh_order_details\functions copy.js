var a0_0x1ce27a=a0_0x224f;(function(_0x27158a,_0x5b820d){var _0x18f779=a0_0x224f,_0x49dc3e=_0x27158a();while(!![]){try{var _0x1383da=parseInt(_0x18f779(0x209))/0x1*(-parseInt(_0x18f779(0x253))/0x2)+-parseInt(_0x18f779(0x1ca))/0x3+-parseInt(_0x18f779(0x216))/0x4+parseInt(_0x18f779(0x288))/0x5*(parseInt(_0x18f779(0x211))/0x6)+-parseInt(_0x18f779(0x1d2))/0x7+-parseInt(_0x18f779(0x167))/0x8*(parseInt(_0x18f779(0x1d4))/0x9)+parseInt(_0x18f779(0x1f9))/0xa;if(_0x1383da===_0x5b820d)break;else _0x49dc3e['push'](_0x49dc3e['shift']());}catch(_0x51e8d7){_0x49dc3e['push'](_0x49dc3e['shift']());}}}(a0_0x7960,0x46fe9),console[a0_0x1ce27a(0x176)](a0_0x1ce27a(0x18a)));function getSku(){var _0x568a35=a0_0x1ce27a,_0x58bf86=null,_0x508da7=document[_0x568a35(0x1bc)]('.lineItemCardInfo__sku.spaceTop');if(_0x508da7)return _0x58bf86=_0x508da7['querySelectorAll'](_0x568a35(0x271))[0x1]['innerText'],(_0x58bf86[_0x568a35(0x21d)]('B')||!isNaN(_0x58bf86[_0x568a35(0x239)](0x0)))&&(_0x58bf86=btoa(_0x58bf86)),_0x58bf86;return _0x58bf86;}function getElementToPlaceButtton(){var _0x2725ca=a0_0x1ce27a,_0x2bb391=document[_0x2725ca(0x1bc)](_0x2725ca(0x257));return _0x2bb391;}function createSkuElementButton(){var _0x3c3901=a0_0x1ce27a;console[_0x3c3901(0x176)](_0x3c3901(0x1f2));var _0x1fe4c7=getSku();if(_0x1fe4c7[_0x3c3901(0x198)]('=='))return;else console[_0x3c3901(0x176)](_0x3c3901(0x20e),_0x1fe4c7),alert(_0x3c3901(0x23d),_0x1fe4c7);var _0x4c0b40=document[_0x3c3901(0x1bc)]('.lineItemCardInfo__content\x20.details');!_0x4c0b40&&(_0x4c0b40=document[_0x3c3901(0x1e8)]);var _0x196a79=document[_0x3c3901(0x19f)](_0x3c3901(0x24a));_0x196a79['textContent']='Get\x20SKU',_0x4c0b40[_0x3c3901(0x1ce)](_0x196a79,_0x4c0b40[_0x3c3901(0x221)][0x1]),_0x196a79[_0x3c3901(0x25e)](_0x3c3901(0x1e2),async function(){var _0x26f2ac=_0x3c3901,_0x5844ae=document[_0x26f2ac(0x19f)](_0x26f2ac(0x171));_0x5844ae[_0x26f2ac(0x23c)][_0x26f2ac(0x18c)](_0x26f2ac(0x226),_0x26f2ac(0x26a));var _0x614fb2=document[_0x26f2ac(0x19f)](_0x26f2ac(0x27a));_0x614fb2['classList'][_0x26f2ac(0x18c)](_0x26f2ac(0x21f)),_0x614fb2['textContent']=_0x26f2ac(0x141);var _0x56b616=document[_0x26f2ac(0x19f)](_0x26f2ac(0x27a));_0x56b616[_0x26f2ac(0x23c)][_0x26f2ac(0x18c)]('sh-secondary');var _0x3afc57=getItemNumber(),_0x53e54a=await getSkuFromDescription(_0x3afc57);_0x56b616[_0x26f2ac(0x14b)]=_0x53e54a,_0x5844ae['appendChild'](_0x614fb2),_0x5844ae['appendChild'](_0x56b616),_0x4c0b40[_0x26f2ac(0x1c2)](_0x5844ae,_0x196a79);});}async function createButtonToCopyAmazonLink(){var _0x3becfb=a0_0x1ce27a,_0x1fbb50=document[_0x3becfb(0x19f)]('button');return _0x1fbb50[_0x3becfb(0x272)]=_0x3becfb(0x1c1),_0x1fbb50[_0x3becfb(0x23c)][_0x3becfb(0x18c)](_0x3becfb(0x27f)),_0x1fbb50[_0x3becfb(0x25e)](_0x3becfb(0x1e2),async function(){var _0x185934=_0x3becfb,_0x2e7f3c=await getSku();if(!_0x2e7f3c)return null;console['log']('sku',_0x2e7f3c);var _0x168510=await convertSkuToLink(_0x2e7f3c);navigator[_0x185934(0x1c0)][_0x185934(0x181)](_0x168510),_0x1fbb50['classList']['add']('amazon-copy-link-button-clicked'),_0x1fbb50[_0x185934(0x272)]=_0x185934(0x1b5);}),_0x1fbb50;}async function copyOrderDetails(_0x218983=![],_0x4c697c){var _0x2735ac=a0_0x1ce27a;console[_0x2735ac(0x176)](_0x2735ac(0x212));var _0x3b7eb4=await getOrderDetails();console['log']('orderDetails',_0x3b7eb4),await chrome[_0x2735ac(0x26f)][_0x2735ac(0x165)]['set']({'orderDetails':_0x3b7eb4});var _0x1d3bba=JSON[_0x2735ac(0x19b)](_0x3b7eb4);console['log'](_0x2735ac(0x1bf),_0x1d3bba);if(!_0x218983){navigator['clipboard'][_0x2735ac(0x181)](_0x1d3bba);return;}console[_0x2735ac(0x176)](_0x2735ac(0x25f),_0x1d3bba);var _0x450e50={'clipKey':_0x4c697c,'content':JSON['stringify'](_0x3b7eb4)};console['log'](_0x2735ac(0x1e4),_0x450e50);var _0x439e25=_0x2735ac(0x151);try{console['log'](_0x2735ac(0x1cb));var _0x295815=await fetch(_0x439e25,{'method':_0x2735ac(0x196),'headers':{'Content-Type':_0x2735ac(0x23f)},'body':JSON[_0x2735ac(0x19b)](_0x450e50)});console[_0x2735ac(0x176)](_0x2735ac(0x175),_0x295815);if(!_0x295815['ok'])throw new Error(_0x2735ac(0x262));var _0x1a7891=await _0x295815[_0x2735ac(0x24c)]();return console[_0x2735ac(0x176)](_0x2735ac(0x23b),_0x1a7891),_0x1a7891;}catch(_0x499831){console[_0x2735ac(0x1dd)](_0x2735ac(0x21e),_0x499831);throw _0x499831;}}async function createShouldCoppyAddressField(){var _0xb449ab=a0_0x1ce27a,{shouldCopyAddress:_0x1af2c3}=await chrome[_0xb449ab(0x26f)][_0xb449ab(0x165)][_0xb449ab(0x152)](_0xb449ab(0x205));_0x1af2c3===undefined&&(_0x1af2c3=!![],await chrome[_0xb449ab(0x26f)]['local'][_0xb449ab(0x1c3)]({'shouldCopyAddress':_0x1af2c3}));var _0x339033=document[_0xb449ab(0x19f)]('input');_0x339033[_0xb449ab(0x20a)](_0xb449ab(0x267),_0xb449ab(0x161)),_0x339033[_0xb449ab(0x23c)][_0xb449ab(0x18c)]('amazon-copy-address-field'),_0x339033[_0xb449ab(0x20a)]('id',_0xb449ab(0x153));var _0x3793a1=document[_0xb449ab(0x19f)]('label');_0x3793a1[_0xb449ab(0x272)]=_0xb449ab(0x197),_0x3793a1['setAttribute'](_0xb449ab(0x232),_0xb449ab(0x153)),_0x3793a1[_0xb449ab(0x23c)][_0xb449ab(0x18c)](_0xb449ab(0x25a));var _0x783bcd=document[_0xb449ab(0x19f)](_0xb449ab(0x171));return _0x783bcd['id']='amazon-copy-address-div',_0x783bcd[_0xb449ab(0x259)](_0x3793a1),_0x783bcd['appendChild'](_0x339033),_0x339033[_0xb449ab(0x281)]=_0x1af2c3,_0x339033[_0xb449ab(0x25e)](_0xb449ab(0x26b),async function(){var _0x16e4b9=_0xb449ab;await chrome['storage'][_0x16e4b9(0x165)][_0x16e4b9(0x1c3)]({'shouldCopyAddress':_0x339033[_0x16e4b9(0x281)]});}),_0x783bcd;}function createButtonToIncreaseQuantity(){var _0x4854ba=a0_0x1ce27a,_0x2bd60d=document[_0x4854ba(0x19f)](_0x4854ba(0x171));_0x2bd60d[_0x4854ba(0x23c)][_0x4854ba(0x18c)](_0x4854ba(0x1d3));var _0x252207=document[_0x4854ba(0x19f)](_0x4854ba(0x1b7));_0x252207['innerText']=_0x4854ba(0x22c),_0x252207[_0x4854ba(0x23c)][_0x4854ba(0x18c)](_0x4854ba(0x17c)),_0x2bd60d[_0x4854ba(0x259)](_0x252207);var _0x48ccbc=document[_0x4854ba(0x19f)](_0x4854ba(0x24a));_0x48ccbc[_0x4854ba(0x272)]=_0x4854ba(0x22c),_0x48ccbc[_0x4854ba(0x23c)]['add'](_0x4854ba(0x217)),_0x2bd60d['appendChild'](_0x48ccbc),_0x48ccbc[_0x4854ba(0x25e)](_0x4854ba(0x1e2),async function(){var _0x3e7dce=_0x4854ba;_0x48ccbc[_0x3e7dce(0x23c)][_0x3e7dce(0x18c)](_0x3e7dce(0x162));var _0x4f0437=document[_0x3e7dce(0x1bc)](_0x3e7dce(0x257)),_0x34af72=_0x4f0437[_0x3e7dce(0x1f4)](_0x3e7dce(0x271))[0x1][_0x3e7dce(0x272)];_0x48ccbc[_0x3e7dce(0x272)]=_0x3e7dce(0x1c5);try{var _0x11cf54=await new Promise(_0x114e02=>{var _0x30078f=_0x3e7dce;chrome['runtime'][_0x30078f(0x27d)]({'type':'increaseQuantityOfItem','itemNumber':_0x34af72},function(_0x33ffc9){var _0x1ff09b=_0x30078f;console[_0x1ff09b(0x176)](_0x33ffc9),_0x114e02(_0x33ffc9);});});_0x48ccbc[_0x3e7dce(0x23c)][_0x3e7dce(0x15b)]('amazon-update-quantity-button-working');if(_0x11cf54&&_0x11cf54[_0x3e7dce(0x175)])_0x48ccbc[_0x3e7dce(0x23c)][_0x3e7dce(0x18c)](_0x3e7dce(0x1b4)),_0x48ccbc[_0x3e7dce(0x272)]='Updated';else throw new Error('Response\x20Error');}catch(_0x42f1e0){_0x48ccbc['classList'][_0x3e7dce(0x15b)](_0x3e7dce(0x162)),_0x48ccbc['classList'][_0x3e7dce(0x18c)]('amazon-update-quantity-button-error'),_0x48ccbc[_0x3e7dce(0x272)]=_0x3e7dce(0x249),console[_0x3e7dce(0x176)](_0x3e7dce(0x1dd),_0x42f1e0);}});var _0x52e02f=document[_0x4854ba(0x19f)](_0x4854ba(0x25d));_0x52e02f[_0x4854ba(0x23c)][_0x4854ba(0x18c)](_0x4854ba(0x222));for(var _0x5c79e9=0x0;_0x5c79e9<=0xa;_0x5c79e9++){var _0x38c523=document['createElement']('option');_0x38c523['value']=_0x5c79e9,_0x38c523[_0x4854ba(0x1a1)]=_0x5c79e9,_0x52e02f[_0x4854ba(0x259)](_0x38c523);}return chrome[_0x4854ba(0x26f)]['local'][_0x4854ba(0x152)](_0x4854ba(0x1d6),function(_0xfb97fe){var _0x33d3c3=_0x4854ba;_0xfb97fe[_0x33d3c3(0x1d6)]?_0x52e02f[_0x33d3c3(0x187)]=_0xfb97fe[_0x33d3c3(0x1d6)]:(_0x52e02f[_0x33d3c3(0x187)]=0x1,chrome[_0x33d3c3(0x26f)]['local'][_0x33d3c3(0x1c3)]({'quantityToUpdate':0x1}));}),_0x52e02f[_0x4854ba(0x25e)](_0x4854ba(0x26b),function(){var _0x1457ac=_0x4854ba,_0x238269=_0x52e02f[_0x1457ac(0x187)];chrome['storage'][_0x1457ac(0x165)][_0x1457ac(0x1c3)]({'quantityToUpdate':_0x238269});}),_0x2bd60d['appendChild'](_0x52e02f),_0x2bd60d;}function createButtonToCopyETAMessageToClipboard(){var _0x40ddc9=a0_0x1ce27a,_0x18281c=document['createElement'](_0x40ddc9(0x24a));return _0x18281c[_0x40ddc9(0x272)]=_0x40ddc9(0x194),_0x18281c[_0x40ddc9(0x23c)][_0x40ddc9(0x18c)](_0x40ddc9(0x190)),_0x18281c['addEventListener'](_0x40ddc9(0x1e2),async function(){var _0x4dff57=_0x40ddc9,_0x199e59=getETA(),{etaMessage:_0x5afe50}=await chrome[_0x4dff57(0x26f)][_0x4dff57(0x165)]['get'](_0x4dff57(0x1f1));!_0x5afe50&&(_0x5afe50=await fetch(chrome[_0x4dff57(0x1ac)][_0x4dff57(0x241)](_0x4dff57(0x185))),_0x5afe50=await _0x5afe50[_0x4dff57(0x1a1)]());var _0x2ebcd6=document[_0x4dff57(0x1f4)]('.shipping-address\x20.tooltip'),_0x17fe3a=_0x2ebcd6[0x1][_0x4dff57(0x272)];_0x17fe3a=_0x17fe3a[_0x4dff57(0x237)]('\x20')[0x0],_0x17fe3a=_0x17fe3a[_0x4dff57(0x239)](0x0)[_0x4dff57(0x274)]()+_0x17fe3a['slice'](0x1)[_0x4dff57(0x266)](),_0x5afe50=_0x5afe50[_0x4dff57(0x214)](_0x4dff57(0x1fb),_0x17fe3a),_0x5afe50=_0x5afe50[_0x4dff57(0x214)](_0x4dff57(0x156),_0x199e59),navigator['clipboard'][_0x4dff57(0x181)](_0x5afe50),_0x18281c['classList']['add'](_0x4dff57(0x1c8)),_0x18281c['innerText']=_0x4dff57(0x1b5);}),_0x18281c;}function createButtonToCopyFeedbackMessageToClipboard(){var _0x33c238=a0_0x1ce27a,_0xdd020b=document[_0x33c238(0x19f)]('button');return _0xdd020b[_0x33c238(0x272)]=_0x33c238(0x1f5),_0xdd020b['classList']['add']('amazon-copy-feedback-button'),_0xdd020b[_0x33c238(0x25e)](_0x33c238(0x1e2),async function(){var _0x136970=_0x33c238,{feedbackMessage:_0x59faab}=await chrome[_0x136970(0x26f)]['local']['get']('feedbackMessage');!_0x59faab&&(_0x59faab=await fetch(chrome['runtime'][_0x136970(0x241)](_0x136970(0x285))),_0x59faab=await _0x59faab[_0x136970(0x1a1)]());var _0x180ed5=await getOrderDetails(),_0x3638aa=_0x180ed5[_0x136970(0x218)][_0x136970(0x275)],_0x4a49f8=_0x3638aa[_0x136970(0x237)]('\x20')[0x0],_0x472a70=_0x59faab[_0x136970(0x214)](_0x136970(0x1fb),_0x4a49f8);navigator[_0x136970(0x1c0)][_0x136970(0x181)](_0x472a70);}),_0xdd020b;}function createFieldToAddETA(){var _0x2a60ed=a0_0x1ce27a,_0x74156e=document[_0x2a60ed(0x19f)]('input');_0x74156e[_0x2a60ed(0x20a)](_0x2a60ed(0x267),_0x2a60ed(0x230)),_0x74156e[_0x2a60ed(0x23c)][_0x2a60ed(0x18c)](_0x2a60ed(0x178));var _0x3a315e=document[_0x2a60ed(0x19f)](_0x2a60ed(0x1b7));_0x3a315e[_0x2a60ed(0x272)]='ETA',_0x3a315e[_0x2a60ed(0x20a)](_0x2a60ed(0x232),'amazon-eta-field'),_0x3a315e[_0x2a60ed(0x23c)]['add'](_0x2a60ed(0x144));var _0x534af3=document[_0x2a60ed(0x19f)](_0x2a60ed(0x171));_0x534af3[_0x2a60ed(0x259)](_0x3a315e),_0x534af3[_0x2a60ed(0x259)](_0x74156e);var _0x331a55=document[_0x2a60ed(0x19f)]('a');return _0x331a55[_0x2a60ed(0x272)]=_0x2a60ed(0x14e),_0x331a55['classList']['add'](_0x2a60ed(0x148)),_0x331a55[_0x2a60ed(0x20a)](_0x2a60ed(0x264),'_blank'),_0x331a55['setAttribute'](_0x2a60ed(0x1f0),chrome[_0x2a60ed(0x1ac)][_0x2a60ed(0x241)](_0x2a60ed(0x1b9))),_0x534af3['appendChild'](_0x331a55),_0x534af3;}function createFieldToAddFeedbackMessage(){var _0x2b25c5=a0_0x1ce27a,_0xa31a5a=document['createElement'](_0x2b25c5(0x171)),_0x41b0ee=document[_0x2b25c5(0x19f)]('a');return _0x41b0ee[_0x2b25c5(0x272)]=_0x2b25c5(0x170),_0x41b0ee[_0x2b25c5(0x23c)]['add'](_0x2b25c5(0x145)),_0x41b0ee[_0x2b25c5(0x20a)]('target',_0x2b25c5(0x18d)),_0x41b0ee[_0x2b25c5(0x20a)]('href',chrome[_0x2b25c5(0x1ac)][_0x2b25c5(0x241)](_0x2b25c5(0x26d))),_0xa31a5a[_0x2b25c5(0x259)](_0x41b0ee),_0xa31a5a;}function getETA(){var _0x366dac=a0_0x1ce27a,_0xb82472=document['querySelector'](_0x366dac(0x258)),_0x4d0a46=new Date(_0xb82472[_0x366dac(0x187)]);_0x4d0a46[_0x366dac(0x244)](_0x4d0a46[_0x366dac(0x1be)]()+0x1);var _0x4b3b19={'year':'numeric','month':_0x366dac(0x278),'day':'numeric'};return _0x4d0a46=_0x4d0a46[_0x366dac(0x157)](_0x366dac(0x1a4),_0x4b3b19),_0x4d0a46;}async function convertSkuToLink(_0x441807){var _0x12a609=a0_0x1ce27a,{domain:_0x5c9e3d}=await chrome['storage']['local'][_0x12a609(0x152)]('domain'),_0x4b459f=_0x12a609(0x19a)+_0x5c9e3d+_0x12a609(0x240)+atob(_0x441807)+'?th=1&psc=1',{applyAmazonAffiliateTag:_0x557522}=await chrome['storage'][_0x12a609(0x165)][_0x12a609(0x152)](_0x12a609(0x1ef));if(_0x557522){var _0x195189='&tag=bestbatteri00-20';_0x4b459f=_0x4b459f+_0x195189;}return _0x4b459f;}async function submitEbayOrder(_0x416592){var _0x321569=a0_0x1ce27a,_0x368d51=_0x321569(0x1ff);try{var _0x221287=await fetch(_0x368d51,{'method':'POST','headers':{'Content-Type':_0x321569(0x23f)},'body':JSON[_0x321569(0x19b)](_0x416592)});if(!_0x221287['ok'])throw new Error('Failed\x20to\x20submit\x20eBay\x20order');var _0x49093a=await _0x221287[_0x321569(0x24c)]();return console[_0x321569(0x176)]('eBay\x20order\x20submitted\x20successfully:',_0x49093a),_0x49093a;}catch(_0xa926f7){console[_0x321569(0x1dd)](_0x321569(0x229),_0xa926f7);throw _0xa926f7;}}async function getOrderDetails(){var _0x3ea94c=a0_0x1ce27a,_0x472028={};console[_0x3ea94c(0x176)]('Initialized\x20orderDetails:',_0x472028),_0x472028[_0x3ea94c(0x215)]=getItemName(),console[_0x3ea94c(0x176)](_0x3ea94c(0x174),_0x472028[_0x3ea94c(0x215)]),_0x472028[_0x3ea94c(0x24d)]=getDateOfSale(),console[_0x3ea94c(0x176)]('Date\x20of\x20Sale:',_0x472028['dateOfSale']),_0x472028['ebayOrderNumber']=getEbayOrderNumber(),console[_0x3ea94c(0x176)](_0x3ea94c(0x255),_0x472028[_0x3ea94c(0x15c)]),_0x472028[_0x3ea94c(0x1b6)]=getQuantitySold(),console[_0x3ea94c(0x176)](_0x3ea94c(0x282),_0x472028['quantitySold']),_0x472028[_0x3ea94c(0x18b)]=getOrderEarnings(),console[_0x3ea94c(0x176)]('Order\x20Earnings:',_0x472028[_0x3ea94c(0x18b)]),_0x472028[_0x3ea94c(0x14a)]=getSoldPrice(),_0x472028['ebayFees']=getEbayFees(),_0x472028['addFee']=getAddFee();var _0x3e3b05=await getSku();_0x472028[_0x3ea94c(0x140)]=_0x3e3b05,console[_0x3ea94c(0x176)]('eBay\x20SKU:',_0x472028[_0x3ea94c(0x140)]);var _0x520260=getCustomer2();_0x472028[_0x3ea94c(0x218)]=_0x520260,console[_0x3ea94c(0x176)](_0x3ea94c(0x1b2),_0x472028[_0x3ea94c(0x218)]);var {domain:_0x2bf787}=await chrome[_0x3ea94c(0x26f)]['local'][_0x3ea94c(0x152)]('domain');_0x472028['domain']=_0x2bf787,console['log'](_0x3ea94c(0x235),_0x472028['domain']);var _0x2fca5d=getFulfillmentDetails();if(_0x2fca5d){_0x472028={..._0x472028,..._0x2fca5d};var _0x5a7d8c=await calculateProfit(_0x472028[_0x3ea94c(0x18b)],_0x472028[_0x3ea94c(0x186)],_0x472028[_0x3ea94c(0x20c)],_0x472028[_0x3ea94c(0x1fc)]);console['log'](_0x3ea94c(0x25c),_0x5a7d8c),_0x472028[_0x3ea94c(0x233)]=_0x5a7d8c;}var _0x4ebe85=getRemark();_0x4ebe85&&(_0x472028[_0x3ea94c(0x1f3)]=_0x4ebe85);var _0x462213=document[_0x3ea94c(0x1bc)]('#googleSheetRowNumber');return _0x462213&&(_0x472028[_0x3ea94c(0x1f7)]=_0x462213[_0x3ea94c(0x187)]),_0x472028;}function getEbayFees(){const _0x31379a=findFeeItemWith2Negatives();if(!_0x31379a)return'0';const _0x209bf1=getNegativeAmounts(_0x31379a);return parseFee(_0x209bf1[0x0]);}function getAddFee(){const _0x109556=findFeeItemWith2Negatives();if(!_0x109556)return'0';const _0x1f4f41=getNegativeAmounts(_0x109556);return parseFee(_0x1f4f41[0x1]);}function findFeeItemWith2Negatives(){var _0x2e948c=a0_0x1ce27a;const _0x3bf2f1=document[_0x2e948c(0x1f4)](_0x2e948c(0x14c));for(const _0x4e207e of _0x3bf2f1){const _0x1f03f0=getNegativeAmounts(_0x4e207e);if(_0x1f03f0[_0x2e948c(0x16f)]===0x2)return _0x4e207e;}return null;}function getNegativeAmounts(_0x13601a){var _0x522f7f=a0_0x1ce27a;return Array['from'](_0x13601a[_0x522f7f(0x1f4)](_0x522f7f(0x22a)))[_0x522f7f(0x1ed)](_0x51a15d=>_0x51a15d[_0x522f7f(0x14b)][_0x522f7f(0x23e)]())[_0x522f7f(0x208)](_0x1e9b33=>_0x1e9b33[_0x522f7f(0x21d)]('-'));}function parseFee(_0x3bf630){if(!_0x3bf630||_0x3bf630==='0')return'0';const _0x1f654c=_0x3bf630['match'](/[-–]\s*([A-Za-z]{1,3}\s?\$)\s*([\d.,]+)/);if(!_0x1f654c)return'0';return _0x1f654c[0x1]+_0x1f654c[0x2];}function getSoldPrice(){var _0xde4cbc=a0_0x1ce27a,_0x3bfe3a=document[_0xde4cbc(0x1bc)](_0xde4cbc(0x22b))[_0xde4cbc(0x272)];return _0x3bfe3a=_0x3bfe3a[_0xde4cbc(0x214)](/[^0-9.-]+/g,''),_0x3bfe3a=_0x3bfe3a['replace'](',','.'),_0x3bfe3a;}function getEbayOrderNumber(){var _0x49258e=a0_0x1ce27a;return document[_0x49258e(0x1f4)](_0x49258e(0x1eb))[0x0][_0x49258e(0x272)];}function getDateOfSale(){var _0x406e4e=a0_0x1ce27a,_0x1feaf2=selectElementByText(_0x406e4e(0x1fe))||selectElementByText(_0x406e4e(0x23a))||selectElementByText(_0x406e4e(0x1cc))||selectElementByText('Verkauft'),_0x1c717d=_0x1feaf2[_0x406e4e(0x261)][_0x406e4e(0x1fd)][_0x406e4e(0x1bc)](_0x406e4e(0x200))[_0x406e4e(0x272)];return _0x1c717d;}function getItemName(){var _0x49aa11=a0_0x1ce27a;return document[_0x49aa11(0x1bc)](_0x49aa11(0x223))['innerText'];}function getQuantitySold(){var _0x103c12=a0_0x1ce27a;return document[_0x103c12(0x1bc)](_0x103c12(0x173))[_0x103c12(0x272)];}function getOrderEarnings(){var _0x479569=a0_0x1ce27a;return document[_0x479569(0x1bc)](_0x479569(0x1ee))['innerText'];}function a0_0x224f(_0x592dd5,_0x2b2a67){var _0x796082=a0_0x7960();return a0_0x224f=function(_0x224fba,_0x3482b4){_0x224fba=_0x224fba-0x13e;var _0x28ef11=_0x796082[_0x224fba];return _0x28ef11;},a0_0x224f(_0x592dd5,_0x2b2a67);}function a0_0x7960(){var _0x56b93e=['.level-2\x20.amount\x20.value','.soldPrice__value','Increase\x20Quantity','disabled','readText','state:','date','New\x20Data','for','profit','getElementById','Domain:','amazonSku','split','Nov','charAt','Verkauft\x20am','Order\x20details\x20saved\x20successfully:','classList','sku\x20does\x20not\x20end\x20with\x20==','trim','application/json','/dp/','getURL','address','amazonEmail','setDate','from','order_item_same_browser','Thursday','join','Error','button','orderDetails','json','dateOfSale','SKU\x20not\x20found.\x20Please\x20enter\x20the\x20SKU\x20manually.','.edit-note','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20SKU:</strong>\x20<span\x20id=\x22amazonSku\x22>','\x20\x20City\x20\x20\x20\x20\x20=','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20Domain:</strong>\x20<span\x20id=\x22amazonDomain\x22>','66358MhqEll','phoneNumberElement:','eBay\x20Order\x20Number:','Date\x20found:','.lineItemCardInfo__itemId.spaceTop','.amazon-eta-field','appendChild','amazon-copy-address-label','content','Profit:','select','addEventListener','orderDetailsString','POST','parentElement','Failed\x20to\x20save\x20order\x20details','toFixed','target','SS/','toLowerCase','type','textArea','www.amazon.com','spaceTop','change','line_1:','content/ebay/mesh_order_details/feedback_options/feedback_options.html','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Tax:</strong>\x20<span\x20id=\x22totalTax\x22>','storage','updateEta:\x20.amazon-eta-field\x20element\x20not\x20found','.sh-secondary','innerText','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20','toUpperCase','name','Wednesday','Dec','long','city:','span','Use\x20Gift\x20Option:\x20','indexOf','sendMessage','Copy\x20Auto\x20Link','amazon-copy-link-button','\x20\x20Name\x20\x20\x20\x20\x20=','checked','Quantity\x20Sold:','Order\x20Earnings:','\x20\x20Line\x201\x20\x20\x20=','content/ebay/mesh_order_details/feedback_options/feedback_message_default.txt','zip:','.shipping-address\x20.address','65lTJZlG','updateSheetButton','#amazonDomain','none','AUD','ebaySku','Custom\x20label\x20(SKU):\x20','Conversion\x20Rate:','parse','amazon-eta-label','amazon-feedback-link','dispatchEvent','Row\x20Number:\x20','amazon-eta-link','May','soldPrice','textContent','.earnings\x20.data-item','Unsupported\x20domain','Change\x20ETA\x20Message','CLIENT_SPECIFIC_SPREADSHEET_ID','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Estimated\x20Delivery\x20Date:</strong>\x20<span\x20id=\x22estimatedDeliveryDate\x22>','https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/virtualClipboard/','get','amazon-copy-address-field','Gift\x20Message:\x20','phone','{{Delivery_Date}}','toLocaleDateString','Exported!','No\x20date\x20found','Jan','remove','ebayOrderNumber','\x20\x20Zip\x20\x20\x20\x20\x20\x20=','rowNumber','childNodes','settings-modal','checkbox','amazon-update-quantity-button-working','No\x20shipping\x20address\x20element\x20found.','Error\x20importing\x20fulfillment\x20details:','local','settings-modal-content','179008PUJPNE','forEach','line_1','result','Add\x20Note','phone:','Add\x20note','Jun','length','Change\x20Feedback\x20Message','div','input','.quantity__value\x20.sh-bold','Item\x20Name:','response','log','Auto\x20Order','amazon-eta-field','prepend','#amazonEmail','amazonOrderNumber','amazon-quantity-label','giftMessage','importButton','Domain\x20Currency:','shouldUseGiftOption','writeText','style','#amazonOrderNumber','Apr','content/ebay/mesh_order_details/eta_options/eta_message_default.txt','totalBeforeTax','value','Sep','customer:','ebay/mesh_order_details/functions.js','orderEarnings','add','_blank','ebay-submit-order-details-button-error','cols','amazon-copy-eta-button','amazon-copy-link-button-clicked','preventDefault','7999','Copy\x20ETA\x20Message','setHours','PATCH','Copy\x20Address','endsWith','getMonth','https://www.amazon.','stringify','updateEta:\x20Formatted\x20date','Export\x20to\x20EcomSniper','GBP','createElement','textarea[name=\x22note-content\x22]','text','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22fulfillmentDetails\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<h2>Fulfillment\x20Details</h2>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Customer\x20Address:</strong>\x20<span\x20id=\x22customerAddress\x22>','ebayFees','en-US','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20Email:</strong>\x20<span\x20id=\x22amazonEmail\x22>','addFee','country:','updateEta:\x20.amazon-eta-field\x20is\x20disabled\x20or\x20read-only','Monday','Tuesday','Update\x20result:','runtime','autoOrderButton\x20clicked\x20SKU:','readOnly','#totalTax','name:','ebay-submit-order-details-button','Customer:','\x20\x20Line\x202\x20\x20\x20=','amazon-update-quantity-button-success','Copied','quantitySold','label','ebay-submit-order-details-button-success','content/ebay/mesh_order_details/eta_options/eta_options.html','\x20\x20Country\x20\x20=','ebay-submit-order-details-button-working','querySelector','UNKNOWN','getDate','stringified\x20orderDetails','clipboard','Copy','replaceChild','set','fullfilmentData','In\x20Progress','.phone.ship-itm','bold','amazon-copy-eta-button-clicked','\x20\x20State\x20\x20\x20\x20=','1285671UgNsRB','making\x20request','Sold','className','insertBefore','children:','?autoOrder=true&orderDetails=','fulfillmentDate','1986278FoJkpj','amazon-quantity-container','18FywvJX','Total\x20Tax:','quantityToUpdate','block','#fulfillmentDate','#estimatedDeliveryDate','Import\x20Fulfillment\x20Details','_TO_','Mar','error','WARNING:\x20SKU\x20does\x20not\x20match!\x20Please\x20verify\x20the\x20fulfillment\x20details.','Error\x20Exporting\x20Data','get_sku_from_description','Gift\x20Message\x20Sender:\x20','click','agentName','data','.note-content','updateEta:\x20Invalid\x20date\x20provided','city','body','line_2','display','.order-info\x20.info-item\x20.info-value','innerHTML','map','.payment-info\x20.earnings\x20.total\x20.amount\x20.sh-bold','applyAmazonAffiliateTag','href','etaMessage','createSkuElementButton','remarks','querySelectorAll','Copy\x20Feedback\x20Message','WARNING:\x20Customer\x20name\x20does\x20not\x20match!\x20Please\x20verify\x20the\x20fulfillment\x20details.','googleSheetRowNumber','success','13127340YwLTME','match','{{Customer_Name}}','amazonDomain','nextElementSibling','Date\x20sold','https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/ebayOrders','.info-value','www.amazon.com.au','.copy-to-clipboard','Sheet1!A1','USD','shouldCopyAddress','updateEta:\x20ETA\x20updated\x20successfully','www.amazon.de','filter','5mqUAxx','setAttribute','Raw\x20Order\x20Earnings:','totalTax','Oct','sku','CAD','#totalBeforeTax','187974wimoAQ','copyOrderDetails','getFullYear','replace','itemName','2025040MAyKdg','amazon-update-quantity-button','customer','onclick','slice','customerAddress','shippingAddressElement:','startsWith','Error\x20saving\x20order\x20details:','sh-secondary','Saturday','children','amazon-quantity-select','.item-tile','.lineItemCardInfo__itemId','Jul','lineItemCardInfo__sku','auto-order-button','Parsed\x20address:','Error\x20submitting\x20eBay\x20order:'];a0_0x7960=function(){return _0x56b93e;};return a0_0x7960();}function getCustomer(){var _0x874ba7=a0_0x1ce27a,_0x4ac188=document[_0x874ba7(0x1bc)](_0x874ba7(0x287));console[_0x874ba7(0x176)](_0x874ba7(0x21c),_0x4ac188);var _0x383006=_0x4ac188[_0x874ba7(0x15f)];console[_0x874ba7(0x176)](_0x874ba7(0x1cf),_0x383006);var _0xda548c=_0x383006[0x0][_0x874ba7(0x272)];console[_0x874ba7(0x176)](_0x874ba7(0x1b0),_0xda548c);var _0x595fae,_0x130ef8,_0x46430e,_0x1b88bd,_0x58b857,_0x158615;if(_0x383006[_0x874ba7(0x16f)]==0x5){_0x595fae=_0x383006[0x1][_0x874ba7(0x272)],console[_0x874ba7(0x176)](_0x874ba7(0x26c),_0x595fae),_0x130ef8=_0x383006[0x2][_0x874ba7(0x272)],console['log']('line_2:',_0x130ef8);var _0xb01a79=_0x383006[0x3][_0x874ba7(0x1f4)](_0x874ba7(0x202));_0x46430e=_0xb01a79[0x0]['innerText'],console[_0x874ba7(0x176)](_0x874ba7(0x279),_0x46430e),_0x1b88bd=_0xb01a79[0x1]['innerText'],console[_0x874ba7(0x176)]('state:',_0x1b88bd),_0x58b857=_0xb01a79[0x2][_0x874ba7(0x272)],console[_0x874ba7(0x176)](_0x874ba7(0x286),_0x58b857),_0x158615=_0x383006[0x4][_0x874ba7(0x272)],console[_0x874ba7(0x176)](_0x874ba7(0x1a7),_0x158615);}else{if(_0x383006['length']==0x4){_0x595fae=_0x383006[0x1][_0x874ba7(0x272)],console[_0x874ba7(0x176)](_0x874ba7(0x26c),_0x595fae);var _0xb01a79=_0x383006[0x2][_0x874ba7(0x1f4)](_0x874ba7(0x202));_0x46430e=_0xb01a79[0x0][_0x874ba7(0x272)],console[_0x874ba7(0x176)]('city:',_0x46430e),_0x1b88bd=_0xb01a79[0x1]['innerText'],console['log'](_0x874ba7(0x22f),_0x1b88bd),_0x58b857=_0xb01a79[0x2]['innerText'],console[_0x874ba7(0x176)](_0x874ba7(0x286),_0x58b857),_0x158615=_0x383006[0x3]['innerText'],console['log'](_0x874ba7(0x1a7),_0x158615);}}var _0x552a22=document['querySelector']('.phone.ship-itm');console[_0x874ba7(0x176)](_0x874ba7(0x254),_0x552a22);var _0x39a4b4='';_0x552a22&&(_0x39a4b4=_0x552a22[_0x874ba7(0x1bc)](_0x874ba7(0x200))[_0x874ba7(0x272)],console[_0x874ba7(0x176)](_0x874ba7(0x16c),_0x39a4b4));var _0x4f2175={'name':_0xda548c,'phone':_0x39a4b4,'address':{'line_1':_0x595fae,'line_2':_0x130ef8,'city':_0x46430e,'state':_0x1b88bd,'zip':_0x58b857,'country':_0x158615}};return console[_0x874ba7(0x176)](_0x874ba7(0x189),_0x4f2175),_0x4f2175;}function getCustomer2(){var _0x3c45ea=a0_0x1ce27a,_0x16aebd=document[_0x3c45ea(0x1bc)](_0x3c45ea(0x287));if(!_0x16aebd){console[_0x3c45ea(0x176)](_0x3c45ea(0x163));return;}var _0x119f38=Array[_0x3c45ea(0x245)](_0x16aebd[_0x3c45ea(0x1f4)](_0x3c45ea(0x171))),_0x13d819='',_0x337981='',_0x4caeab='',_0x12922a='',_0x322463='',_0x3d65c0='',_0x592d9a='';function _0x37a6e6(_0xf98750,_0x237fdd){var _0x18e030=_0x3c45ea,_0x1cbf48=_0xf98750[_0x18e030(0x1f4)]('.copy-to-clipboard');if(_0x1cbf48&&_0x1cbf48[_0x237fdd])return _0x1cbf48[_0x237fdd]['innerText'][_0x18e030(0x23e)]();return'';}_0x119f38[0x0]&&(_0x13d819=_0x119f38[0x0][_0x3c45ea(0x272)][_0x3c45ea(0x23e)]());_0x119f38[0x1]&&(_0x337981=_0x119f38[0x1][_0x3c45ea(0x272)][_0x3c45ea(0x23e)]());if(_0x119f38[0x2]){let _0x34a991=_0x119f38[0x2][_0x3c45ea(0x1f4)]('.copy-to-clipboard')[_0x3c45ea(0x16f)];_0x34a991===0x0||_0x34a991===0x1?_0x4caeab=_0x119f38[0x2][_0x3c45ea(0x272)][_0x3c45ea(0x23e)]():_0x34a991===0x2?(_0x3d65c0=_0x37a6e6(_0x119f38[0x2],0x0),_0x12922a=_0x37a6e6(_0x119f38[0x2],0x1)):(_0x12922a=_0x37a6e6(_0x119f38[0x2],0x0),_0x322463=_0x37a6e6(_0x119f38[0x2],0x1),_0x3d65c0=_0x37a6e6(_0x119f38[0x2],0x2));}if(_0x4caeab&&_0x119f38[0x3]){let _0x2d5e8c=_0x119f38[0x3][_0x3c45ea(0x1f4)](_0x3c45ea(0x202))[_0x3c45ea(0x16f)];if(_0x2d5e8c===0x2)_0x3d65c0=_0x37a6e6(_0x119f38[0x3],0x0),_0x12922a=_0x37a6e6(_0x119f38[0x3],0x1);else _0x2d5e8c===0x3&&(_0x12922a=_0x37a6e6(_0x119f38[0x3],0x0),_0x322463=_0x37a6e6(_0x119f38[0x3],0x1),_0x3d65c0=_0x37a6e6(_0x119f38[0x3],0x2));}let _0x4c6c86=_0x119f38['length']-0x1;if(_0x4c6c86>=0x0){let _0x15d74b=_0x119f38[_0x4c6c86][_0x3c45ea(0x272)][_0x3c45ea(0x23e)]();if(_0x119f38[_0x4c6c86][_0x3c45ea(0x1bc)](_0x3c45ea(0x202)))_0x592d9a=_0x15d74b;else{}}console[_0x3c45ea(0x176)](_0x3c45ea(0x228)),console['log'](_0x3c45ea(0x280),_0x13d819),console[_0x3c45ea(0x176)](_0x3c45ea(0x284),_0x337981),console[_0x3c45ea(0x176)](_0x3c45ea(0x1b3),_0x4caeab),console['log'](_0x3c45ea(0x251),_0x12922a),console[_0x3c45ea(0x176)](_0x3c45ea(0x1c9),_0x322463),console[_0x3c45ea(0x176)](_0x3c45ea(0x15d),_0x3d65c0),console['log'](_0x3c45ea(0x1ba),_0x592d9a);var _0x4064ed=document['querySelector'](_0x3c45ea(0x1c6));console[_0x3c45ea(0x176)](_0x3c45ea(0x254),_0x4064ed);var _0x7ae80d='';_0x4064ed&&(_0x7ae80d=_0x4064ed[_0x3c45ea(0x1bc)](_0x3c45ea(0x200))['innerText'],console['log'](_0x3c45ea(0x16c),_0x7ae80d));try{_0x337981=_0x337981['replace'](/\bebay\w*/gi,'')[_0x3c45ea(0x23e)](),_0x4caeab=_0x4caeab[_0x3c45ea(0x214)](/\bebay\w*/gi,'')['trim']();}catch(_0x6de0b7){console[_0x3c45ea(0x176)](_0x3c45ea(0x1dd),_0x6de0b7);}return{'phone':_0x7ae80d,'name':_0x13d819,'address':{'line_1':_0x337981,'line_2':_0x4caeab,'city':_0x12922a,'state':_0x322463,'zip':_0x3d65c0,'country':_0x592d9a}};}function getRemark(){var _0x9a1ffe=a0_0x1ce27a,_0x963df8=document[_0x9a1ffe(0x1bc)](_0x9a1ffe(0x1e5));if(_0x963df8)return _0x963df8[_0x9a1ffe(0x272)];return null;}function getFulfillmentDetails(){var _0x5f0824=a0_0x1ce27a,_0x2b411b=document[_0x5f0824(0x1bc)](_0x5f0824(0x183))?.[_0x5f0824(0x272)],_0x1788fa=document[_0x5f0824(0x1bc)](_0x5f0824(0x210))?.[_0x5f0824(0x272)],_0x32709b=document[_0x5f0824(0x1bc)](_0x5f0824(0x1af))?.[_0x5f0824(0x272)],_0x4716fe=document[_0x5f0824(0x1bc)](_0x5f0824(0x1d9))?.[_0x5f0824(0x272)],_0x317ef4=document[_0x5f0824(0x1bc)]('#amazonEmail')?.[_0x5f0824(0x272)],_0x5c4865=document[_0x5f0824(0x1bc)](_0x5f0824(0x1d8))?.[_0x5f0824(0x272)],_0x5e0ea0=document[_0x5f0824(0x1bc)]('#amazonSku')?.['innerText'],_0x34d7df=document[_0x5f0824(0x1bc)](_0x5f0824(0x28a))?.[_0x5f0824(0x272)];if(!_0x2b411b||!_0x1788fa||!_0x32709b||!_0x4716fe||!_0x317ef4||!_0x5c4865)return;return{'amazonOrderNumber':_0x2b411b,'totalBeforeTax':_0x1788fa,'totalTax':_0x32709b,'estimatedDeliveryDate':_0x4716fe,'amazonEmail':_0x317ef4,'fulfillmentDate':_0x5c4865,'amazonSku':_0x5e0ea0,'amazonDomain':_0x34d7df};}function createButtonToSubmitOrderDetailsToEcomSniper(){var _0x305f9a=a0_0x1ce27a,_0x4930ad=document['createElement'](_0x305f9a(0x24a));return _0x4930ad[_0x305f9a(0x272)]=_0x305f9a(0x19d),_0x4930ad[_0x305f9a(0x23c)]['add'](_0x305f9a(0x1b1)),_0x4930ad['addEventListener'](_0x305f9a(0x1e2),async function(){var _0xe20574=_0x305f9a;_0x4930ad[_0xe20574(0x23c)][_0xe20574(0x18c)](_0xe20574(0x1bb));var _0x1a9a82=await getOrderDetails();console['log'](_0xe20574(0x24b),_0x1a9a82);try{var _0x41a505=await submitEbayOrder(_0x1a9a82);console[_0xe20574(0x176)](_0xe20574(0x175),_0x41a505),_0x4930ad[_0xe20574(0x23c)][_0xe20574(0x15b)](_0xe20574(0x1bb)),_0x4930ad[_0xe20574(0x23c)][_0xe20574(0x18c)](_0xe20574(0x1b8)),_0x4930ad[_0xe20574(0x272)]=_0xe20574(0x158);}catch(_0x3ffed7){_0x4930ad[_0xe20574(0x23c)][_0xe20574(0x15b)]('ebay-submit-order-details-button-working'),_0x4930ad[_0xe20574(0x23c)][_0xe20574(0x18c)](_0xe20574(0x18e)),_0x4930ad[_0xe20574(0x272)]=_0xe20574(0x1df),console['log'](_0xe20574(0x1dd),_0x3ffed7);}}),_0x4930ad;}async function exportToEcomSniper(){var _0x5eb76b=a0_0x1ce27a,_0x79f0bc=await getOrderDetails();console['log'](_0x5eb76b(0x24b),_0x79f0bc);try{var _0x113540=await submitEbayOrder(_0x79f0bc);console[_0x5eb76b(0x176)]('response',_0x113540);}catch(_0x598eb5){console['log']('error',_0x598eb5);}}async function submitToGoogleSheets(){var _0x13fb45=a0_0x1ce27a,_0xa4d076=await getOrderDetails();console[_0x13fb45(0x176)](_0x13fb45(0x24b),_0xa4d076);var _0x4c62d9=[null,null,_0x13fb45(0x193),_0xa4d076[_0x13fb45(0x15c)],_0xa4d076['ebaySku'],_0xa4d076[_0x13fb45(0x218)][_0x13fb45(0x275)],_0xa4d076['customer'][_0x13fb45(0x242)][_0x13fb45(0x169)],_0xa4d076[_0x13fb45(0x218)]['address'][_0x13fb45(0x1e9)],_0xa4d076[_0x13fb45(0x218)][_0x13fb45(0x242)][_0x13fb45(0x1e7)],_0xa4d076[_0x13fb45(0x218)]['address']['state'],_0xa4d076[_0x13fb45(0x218)]['address']['zip'],_0xa4d076[_0x13fb45(0x218)][_0x13fb45(0x155)],_0xa4d076[_0x13fb45(0x1b6)],_0xa4d076['dateOfSale'],_0xa4d076['itemName'],_0xa4d076[_0x13fb45(0x14a)],_0xa4d076[_0x13fb45(0x1a3)],_0xa4d076[_0x13fb45(0x1a6)]];console[_0x13fb45(0x176)]('rowValues',_0x4c62d9);var _0x5602e9=await new Promise(_0x1add8d=>{var _0x1114b9=_0x13fb45;chrome[_0x1114b9(0x1ac)]['sendMessage']({'type':'createSheetRow','rowValues':_0x4c62d9},function(_0x1f8bd6){console['log'](_0x1f8bd6),_0x1add8d(_0x1f8bd6);});});console['log']('response',_0x5602e9);if(_0x5602e9&&_0x5602e9[_0x13fb45(0x16a)]==_0x13fb45(0x1f8)){var _0x4e0621=_0x5602e9[_0x13fb45(0x15e)],_0x29b1ab=document[_0x13fb45(0x19f)](_0x13fb45(0x171));_0x29b1ab['id']=_0x13fb45(0x1f7),_0x29b1ab[_0x13fb45(0x187)]=_0x4e0621,_0x29b1ab[_0x13fb45(0x1ec)]=_0x13fb45(0x147)+_0x4e0621,_0x29b1ab['style']['color']='green',_0x29b1ab['style']['fontWeight']=_0x13fb45(0x1c7);var _0x595dfe=getElementToPlaceButtton();_0x595dfe[_0x13fb45(0x259)](_0x29b1ab);}return _0x5602e9;}async function changeActionsLinkToOpenInNewTab(){var _0x2eaa78=a0_0x1ce27a,_0x13230c=document[_0x2eaa78(0x1f4)]('.actions\x20a');_0x13230c[_0x2eaa78(0x168)](_0x41c7aa=>{var _0x5bc030=_0x2eaa78;_0x41c7aa['setAttribute'](_0x5bc030(0x264),_0x5bc030(0x18d));});}function createButtonToImportFulfillmentDetails(){var _0x5afd57=a0_0x1ce27a,_0xc479ce=document[_0x5afd57(0x19f)](_0x5afd57(0x24a));return _0xc479ce[_0x5afd57(0x1ec)]=_0x5afd57(0x1da),_0xc479ce[_0x5afd57(0x1cd)]=_0x5afd57(0x17e),_0xc479ce[_0x5afd57(0x219)]=async function(){importFulfillmentDetails();},_0xc479ce;}async function importFulfillmentDetails(_0x45a074=![],_0x4378a3){var _0x5b0a49=a0_0x1ce27a;try{if(_0x45a074){var _0x37e3f3=_0x5b0a49(0x151),_0x516c00=await fetch(_0x37e3f3+_0x4378a3);_0x516c00=await _0x516c00[_0x5b0a49(0x24c)](),console[_0x5b0a49(0x176)](_0x5b0a49(0x175),_0x516c00),console[_0x5b0a49(0x176)](_0x516c00[_0x5b0a49(0x1e4)]['content']),fullfilmentData=JSON['parse'](_0x516c00[_0x5b0a49(0x1e4)][_0x5b0a49(0x25b)]),console[_0x5b0a49(0x176)](_0x5b0a49(0x1c4),fullfilmentData);}else{const _0x4882fe=await navigator[_0x5b0a49(0x1c0)][_0x5b0a49(0x22e)]();fullfilmentData=JSON[_0x5b0a49(0x143)](_0x4882fe);}await appendFulfillmentDetails(fullfilmentData);}catch(_0xb63fba){console['error'](_0x5b0a49(0x164),_0xb63fba);throw _0xb63fba;}}async function appendFulfillmentDetails(_0x3c468a){var _0x41e2e3=a0_0x1ce27a;const _0xae0ffb=_0x41e2e3(0x1a2)+_0x3c468a[_0x41e2e3(0x21b)]['replace'](/\n/g,'<br>')+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Order\x20Number:</strong>\x20<span\x20id=\x22amazonOrderNumber\x22>'+_0x3c468a[_0x41e2e3(0x17b)]+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Before\x20Tax:</strong>\x20<span\x20id=\x22totalBeforeTax\x22>'+_0x3c468a[_0x41e2e3(0x186)]+_0x41e2e3(0x26e)+_0x3c468a['totalTax']+_0x41e2e3(0x150)+_0x3c468a['estimatedDeliveryDate']+_0x41e2e3(0x1a5)+_0x3c468a[_0x41e2e3(0x243)]+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>fulfillment\x20Date:</strong>\x20<span\x20id=\x22fulfillmentDate\x22>'+_0x3c468a[_0x41e2e3(0x1d1)]+_0x41e2e3(0x250)+_0x3c468a[_0x41e2e3(0x236)]+_0x41e2e3(0x252)+_0x3c468a[_0x41e2e3(0x1fc)]+_0x41e2e3(0x273),_0x43c6f7=document[_0x41e2e3(0x19f)](_0x41e2e3(0x171));_0x43c6f7[_0x41e2e3(0x1ec)]=_0xae0ffb;var _0x1a1c51=document['querySelector']('.soldPrice');_0x1a1c51[_0x41e2e3(0x261)][_0x41e2e3(0x261)]['appendChild'](_0x43c6f7);var _0x5463e5=await getOrderDetails(),_0x4a6804=_0x5463e5['customer']['name'],_0x2ede0b=_0x3c468a[_0x41e2e3(0x21b)]['split']('\x0a')[0x0],_0xbd84e3=atob(_0x5463e5[_0x41e2e3(0x140)]),_0x155e9b=_0x3c468a[_0x41e2e3(0x236)];checkIffulfillmentDetailsAreCorrect(_0x4a6804,_0x2ede0b,_0xbd84e3,_0x155e9b);var _0x4dd98a=document[_0x41e2e3(0x1bc)](_0x41e2e3(0x1d9))['innerText'],_0x17f4dc=extractDateFromString(_0x4dd98a);_0x17f4dc?(console[_0x41e2e3(0x176)](_0x41e2e3(0x256),_0x17f4dc),updateEta(_0x17f4dc)):console['log'](_0x41e2e3(0x159));var _0x432c3e=await calculateProfit(_0x5463e5[_0x41e2e3(0x18b)],_0x3c468a[_0x41e2e3(0x186)],_0x3c468a[_0x41e2e3(0x20c)],_0x3c468a['amazonDomain']);console[_0x41e2e3(0x176)](_0x41e2e3(0x25c),_0x432c3e);}function checkIffulfillmentDetailsAreCorrect(_0x56fd4f,_0x3fbc30,_0x20d477,_0x2efa0d){var _0x45762f=a0_0x1ce27a;_0x3fbc30!==_0x56fd4f&&alert(_0x45762f(0x1f6)),_0x20d477!==_0x2efa0d&&alert(_0x45762f(0x1de));}function extractDateFromString(_0x21d3ac){var _0xdd591f=a0_0x1ce27a;const _0x4a9cb=new Date(),_0x83dfc0=['Sunday',_0xdd591f(0x1a9),_0xdd591f(0x1aa),_0xdd591f(0x276),_0xdd591f(0x247),'Friday',_0xdd591f(0x220)];function _0x53be71(_0x439463,_0x259158){var _0xc27bd8=_0xdd591f;const _0x2d73a1=new Date(_0x439463);return _0x2d73a1['setDate'](_0x2d73a1[_0xc27bd8(0x1be)]()+_0x259158),_0x2d73a1;}function _0x442f2c(_0x163bf0,_0x24c9cb){var _0x478023=_0xdd591f;const _0x2a7c37=_0x83dfc0[_0x478023(0x27c)](_0x24c9cb),_0x2f5300=new Date(_0x163bf0);return _0x2f5300[_0x478023(0x244)](_0x2f5300[_0x478023(0x1be)]()+(0x7+_0x2a7c37-_0x163bf0['getDay']())%0x7),_0x2f5300;}function _0x1cb933(_0x3ea08a,_0x4923fa){var _0x1419fb=_0xdd591f;let [_0x19d5db,_0x2067d4]=_0x4923fa[_0x1419fb(0x237)]('\x20'),[_0x1887e7,_0x196310]=_0x19d5db['split'](':');if(_0x2067d4==='PM'&&_0x1887e7<0xc)_0x1887e7=parseInt(_0x1887e7)+0xc;return _0x3ea08a[_0x1419fb(0x195)](_0x1887e7,_0x196310||0x0,0x0,0x0),_0x3ea08a;}function _0x3f535d(_0x328a85){var _0x568947=_0xdd591f;const [_0x3207cf,_0x127df0]=_0x328a85[_0x568947(0x237)](',\x20'),[_0x1dc5a3,_0x5e73f3]=_0x127df0['split']('\x20'),_0x37e9a7=[_0x568947(0x15a),'Feb',_0x568947(0x1dc),_0x568947(0x184),_0x568947(0x149),_0x568947(0x16e),_0x568947(0x225),'Aug',_0x568947(0x188),_0x568947(0x20d),_0x568947(0x238),_0x568947(0x277)],_0xbbf51f=_0x37e9a7[_0x568947(0x27c)](_0x5e73f3);if(_0xbbf51f===-0x1)return null;const _0x51ab36=_0x4a9cb[_0x568947(0x199)]()>_0xbbf51f?_0x4a9cb[_0x568947(0x213)]()+0x1:_0x4a9cb['getFullYear']();return new Date(_0x51ab36,_0xbbf51f,_0x1dc5a3);}const _0x8b7f9=[{'regex':/Delivered today/,'date':new Date(_0x4a9cb)},{'regex':/Arriving today/,'date':new Date(_0x4a9cb)},{'regex':/Arriving tomorrow/,'date':_0x53be71(new Date(_0x4a9cb),0x1)},{'regex':/Delivered (\w+ \d+)/,'dateFunction':_0x40ab7a=>new Date(_0x40ab7a[0x1]+',\x20'+_0x4a9cb[_0xdd591f(0x213)]())},{'regex':/Arriving (\w+)$/,'dateFunction':_0x3e1f55=>_0x442f2c(_0x4a9cb,_0x3e1f55[0x1])},{'regex':/Now expected by (\w+ \d+)/,'dateFunction':_0x4d211f=>new Date(_0x4d211f[0x1]+',\x20'+_0x4a9cb['getFullYear']())},{'regex':/Arriving tomorrow by (\d+ [AP]M)/,'dateFunction':_0x42f3b7=>_0x1cb933(_0x53be71(new Date(_0x4a9cb),0x1),_0x42f3b7[0x1])},{'regex':/Arriving today by (\d+ [AP]M)/,'dateFunction':_0x2a5a10=>_0x1cb933(new Date(_0x4a9cb),_0x2a5a10[0x1])},{'regex':/Arriving by (\w+ \d+)/,'dateFunction':_0x566b77=>new Date(_0x566b77[0x1]+',\x20'+_0x4a9cb[_0xdd591f(0x213)]())},{'regex':/Delivered (\w+ \d+),/,'dateFunction':_0x57ad6d=>new Date(_0x57ad6d[0x1]+',\x20'+_0x4a9cb[_0xdd591f(0x213)]())},{'regex':/Expected by (\w+ \d+)/,'dateFunction':_0xa6f1de=>new Date(_0xa6f1de[0x1]+',\x20'+_0x4a9cb[_0xdd591f(0x213)]())},{'regex':/Arriving (\w+, \d+ \w+)/,'dateFunction':_0x5b6512=>_0x3f535d(_0x5b6512[0x1])}];for(let _0x3fe40c of _0x8b7f9){const {regex:_0x21a148,date:_0x40320e,dateFunction:_0x447e63}=_0x3fe40c,_0x573ba4=_0x21d3ac[_0xdd591f(0x1fa)](_0x21a148);if(_0x573ba4)return _0x447e63?_0x447e63(_0x573ba4):_0x40320e;}return null;}function updateEta(_0x2d4703){var _0x4609c7=a0_0x1ce27a;if(!(_0x2d4703 instanceof Date)||isNaN(_0x2d4703)){console['error'](_0x4609c7(0x1e6),_0x2d4703);return;}var _0x40768f=[_0x2d4703[_0x4609c7(0x213)](),('0'+(_0x2d4703['getMonth']()+0x1))['slice'](-0x2),('0'+_0x2d4703[_0x4609c7(0x1be)]())[_0x4609c7(0x21a)](-0x2)][_0x4609c7(0x248)]('-');console['log'](_0x4609c7(0x19c),_0x40768f);var _0x28f866=document[_0x4609c7(0x1bc)](_0x4609c7(0x258));if(!_0x28f866){console[_0x4609c7(0x1dd)](_0x4609c7(0x270));return;}if(_0x28f866['disabled']||_0x28f866[_0x4609c7(0x1ae)]){console[_0x4609c7(0x1dd)](_0x4609c7(0x1a8));return;}_0x28f866['value']=_0x40768f,console[_0x4609c7(0x176)](_0x4609c7(0x206));}const exchangeRates={'CAD_TO_USD':0x1/1.3507,'USD_TO_CAD':1.3507,'GBP_TO_USD':0x1/0.789173,'USD_TO_GBP':0.789173,'AUD_TO_USD':0x1/1.64866,'USD_TO_AUD':1.64866,'EUR_TO_USD':0x1/1.0821,'USD_TO_EUR':1.0821};function extractCurrencyAndAmount(_0x106923){var _0x27d52f=a0_0x1ce27a;let _0x42bcdd=_0x106923[_0x27d52f(0x1fa)](/(C \$|\$|£|A \$|€)/),_0x123e0b=_0x42bcdd?_0x42bcdd[0x0]:null,_0x4b0971=parseFloat(_0x106923[_0x27d52f(0x214)](/[^\d.-]/g,''));return{'currencySymbol':_0x123e0b,'amount':_0x4b0971};}function getDomainCurrency(_0x4014b2){var _0x1253c2=a0_0x1ce27a;switch(_0x4014b2){case'www.amazon.ca':return _0x1253c2(0x20f);case _0x1253c2(0x269):return _0x1253c2(0x204);case'www.amazon.co.uk':return _0x1253c2(0x19e);case _0x1253c2(0x201):return _0x1253c2(0x13f);case _0x1253c2(0x207):return'EUR';default:return _0x1253c2(0x1bd);}}function sanitizeCurrency(_0x4e0f37){var _0x5734b5=a0_0x1ce27a;return parseFloat(_0x4e0f37[_0x5734b5(0x214)](/[^\d.-]/g,''));}async function calculateProfit(_0x709ada,_0x41b902,_0x1244f1,_0x480da9){var _0x12185f=a0_0x1ce27a;console['log'](_0x12185f(0x20b),_0x709ada);var {currencySymbol:_0x2fd25c,amount:_0x9e5d55}=extractCurrencyAndAmount(_0x709ada);console[_0x12185f(0x176)]('Currency\x20Symbol:',_0x2fd25c),console[_0x12185f(0x176)](_0x12185f(0x283),_0x9e5d55),console[_0x12185f(0x176)]('Total\x20Before\x20Tax:',_0x41b902),console[_0x12185f(0x176)](_0x12185f(0x1d5),_0x1244f1),console['log']('Amazon\x20Domain:',_0x480da9);var _0x51697b=getDomainCurrency(_0x480da9);console[_0x12185f(0x176)](_0x12185f(0x17f),_0x51697b);if(_0x51697b===_0x12185f(0x1bd))return console['error']('Unsupported\x20Amazon\x20domain'),_0x12185f(0x14d);_0x41b902=sanitizeCurrency(_0x41b902),_0x1244f1=sanitizeCurrency(_0x1244f1);var _0x51f2b1=_0x41b902+_0x1244f1;console[_0x12185f(0x176)]('Total\x20Cost:',_0x51f2b1);var _0x1dcd03=_0x51f2b1;const _0x36f105={'C\x20$':_0x12185f(0x20f),'$':'USD','£':_0x12185f(0x19e),'A\x20$':'AUD','€':'EUR'};let _0x2bcef5=_0x36f105[_0x2fd25c]||_0x12185f(0x204);console['log']('Earnings\x20Currency:',_0x2bcef5);if(_0x2bcef5!==_0x51697b){let _0x3cf2b6=_0x51697b+_0x12185f(0x1db)+_0x2bcef5,_0xc417d=exchangeRates[_0x3cf2b6]||0x1;_0x1dcd03=_0x51f2b1*_0xc417d,console[_0x12185f(0x176)]('Conversion\x20Key:',_0x3cf2b6),console[_0x12185f(0x176)](_0x12185f(0x142),_0xc417d),console[_0x12185f(0x176)]('Converted\x20Cost:',_0x1dcd03);}let _0x2264e3=_0x9e5d55-_0x1dcd03;console['log'](_0x12185f(0x25c),_0x2264e3);let _0x39208f=''+_0x2fd25c+_0x2264e3[_0x12185f(0x263)](0x2);return console[_0x12185f(0x176)]('Formatted\x20Profit:',_0x39208f),_0x39208f;}function selectElementByText(_0x17a827){var _0x4598d0=a0_0x1ce27a;const _0x10c7a0=document[_0x4598d0(0x1f4)]('*');for(let _0x467e39 of _0x10c7a0){if(_0x467e39[_0x4598d0(0x272)]===_0x17a827)return _0x467e39;}return null;}async function createNote(_0x26b19f){var _0x4e2187=a0_0x1ce27a,_0x4ead9f=document[_0x4e2187(0x1bc)](_0x4e2187(0x24f));if(_0x4ead9f)_0x4ead9f[_0x4e2187(0x1e2)]();else{var _0x301086=selectElementByText(_0x4e2187(0x16d));_0x301086[_0x4e2187(0x1e2)]();}var _0x2e808d=await waitForElementVanillaJs(_0x4e2187(0x1a0));_0x2e808d['value']=_0x26b19f,_0x2e808d['dispatchEvent'](new Event(_0x4e2187(0x172),{'bubbles':!![]})),_0x2e808d[_0x4e2187(0x146)](new Event('change',{'bubbles':!![]}));var _0x4a8f42=selectElementByText('Save');_0x4a8f42[_0x4e2187(0x1e2)]();}function createShippedNoteButton(){var _0x1680f4=a0_0x1ce27a,_0x2a1c46=document[_0x1680f4(0x19f)](_0x1680f4(0x24a));return _0x2a1c46[_0x1680f4(0x272)]=_0x1680f4(0x16b),_0x2a1c46[_0x1680f4(0x23c)][_0x1680f4(0x18c)]('add-note-button'),_0x2a1c46[_0x1680f4(0x25e)](_0x1680f4(0x1e2),async function(){await createAndAppendNote();}),_0x2a1c46;}async function addShippedNote(){var _0x5ca0b8=getPreviousNoteText(),_0x4a4244=await createShippedNoteText(),_0x4a4906=_0x5ca0b8?_0x5ca0b8+'\x0a'+_0x4a4244:_0x4a4244;await createNote(_0x4a4906);}function getPreviousNoteText(){var _0x486b0c=a0_0x1ce27a,_0x2377fe=document['querySelector'](_0x486b0c(0x1e5));if(_0x2377fe)return _0x2377fe[_0x486b0c(0x272)];return null;}async function createShippedNoteText(){var _0x845f8=a0_0x1ce27a,_0x2f1147=document['querySelector'](_0x845f8(0x17a))['innerText'],_0x5e2feb=document[_0x845f8(0x1bc)](_0x845f8(0x1d9))[_0x845f8(0x272)],_0x5bf07d=document[_0x845f8(0x1bc)](_0x845f8(0x1d8))[_0x845f8(0x272)],{agentName:_0x2ff187}=await chrome[_0x845f8(0x26f)][_0x845f8(0x165)][_0x845f8(0x152)](_0x845f8(0x1e3)),_0x354c3b=_0x845f8(0x265)+_0x2f1147+'/'+_0x5e2feb+'/'+_0x5bf07d+'-'+_0x2ff187;return _0x354c3b;}async function createAutoOrderContainer(){var _0x1434de=a0_0x1ce27a;const {shouldGetLinkInstead:shouldGetLinkInstead=![],giftMessage:giftMessage='-',giftMessageSender:giftMessageSender='-',shouldUseGiftOption:shouldUseGiftOption=![]}=await chrome[_0x1434de(0x26f)][_0x1434de(0x165)][_0x1434de(0x152)]({'shouldGetLinkInstead':![],'giftMessage':'-','giftMessageSender':'-','shouldUseGiftOption':![]}),_0x38e406=document[_0x1434de(0x19f)](_0x1434de(0x171));_0x38e406[_0x1434de(0x23c)][_0x1434de(0x18c)]('auto-order-container');const _0x1f46a2=document[_0x1434de(0x19f)](_0x1434de(0x24a));shouldGetLinkInstead?_0x1f46a2[_0x1434de(0x272)]=_0x1434de(0x27e):_0x1f46a2['innerText']=_0x1434de(0x177);_0x1f46a2[_0x1434de(0x23c)][_0x1434de(0x18c)](_0x1434de(0x227)),_0x1f46a2[_0x1434de(0x25e)](_0x1434de(0x1e2),async _0x5f4c8c=>{var _0x36b693=_0x1434de;_0x5f4c8c['preventDefault']();const {shouldGetLinkInstead:_0x50b42b,giftMessage:giftMessage='-',giftMessageSender:giftMessageSender='-'}=await chrome['storage']['local'][_0x36b693(0x152)]({'shouldGetLinkInstead':![],'giftMessage':'-','giftMessageSender':'-'});if(_0x50b42b){let _0x5e3f7c=await getSku();if(!_0x5e3f7c){alert(_0x36b693(0x24e));return;}console['log'](_0x36b693(0x1ad),_0x5e3f7c);let _0x101f40=await convertSkuToLink(_0x5e3f7c);console[_0x36b693(0x176)]('autoOrderButton\x20clicked\x20link:',_0x101f40);const _0x2c599d=await getOrderDetails();_0x2c599d[_0x36b693(0x17d)]=giftMessage,_0x2c599d['giftMessageSender']=giftMessageSender,_0x2c599d[_0x36b693(0x180)]=shouldUseGiftOption,console['log']('orderDetails\x20to\x20be\x20saved',_0x2c599d),_0x101f40=_0x101f40+(_0x36b693(0x1d0)+encodeURIComponent(JSON[_0x36b693(0x19b)](_0x2c599d))),navigator[_0x36b693(0x1c0)][_0x36b693(0x181)](_0x101f40),_0x1f46a2[_0x36b693(0x23c)][_0x36b693(0x18c)]('amazon-copy-link-button-clicked'),setTimeout(()=>{var _0x455132=_0x36b693;_0x1f46a2[_0x455132(0x23c)][_0x455132(0x15b)](_0x455132(0x191));},0x1388);}else console[_0x36b693(0x176)]('Auto\x20order\x20logic\x20triggered.'),_0x1f46a2[_0x36b693(0x22d)]=!![],await autoOrder();});const _0x4b61c7=document[_0x1434de(0x19f)](_0x1434de(0x27a));_0x4b61c7[_0x1434de(0x1ec)]='⚙',_0x4b61c7[_0x1434de(0x23c)][_0x1434de(0x18c)]('settings-icon');const _0x5d2f97=document[_0x1434de(0x19f)](_0x1434de(0x171));_0x5d2f97[_0x1434de(0x23c)][_0x1434de(0x18c)](_0x1434de(0x160)),_0x5d2f97[_0x1434de(0x182)]['display']=_0x1434de(0x13e);const _0x5ced81=document[_0x1434de(0x19f)]('div');_0x5ced81[_0x1434de(0x23c)][_0x1434de(0x18c)](_0x1434de(0x166));const _0x5e2e90=document[_0x1434de(0x19f)](_0x1434de(0x27a));_0x5e2e90[_0x1434de(0x272)]='x',_0x5e2e90[_0x1434de(0x23c)][_0x1434de(0x18c)]('settings-modal-close'),_0x5e2e90[_0x1434de(0x25e)]('click',()=>{var _0x1e4b67=_0x1434de;_0x5d2f97[_0x1e4b67(0x182)][_0x1e4b67(0x1ea)]=_0x1e4b67(0x13e);});const _0x1c012c=document['createElement'](_0x1434de(0x1b7));_0x1c012c[_0x1434de(0x272)]='Get\x20link\x20instead\x20of\x20auto\x20order';const _0x13a873=document[_0x1434de(0x19f)](_0x1434de(0x172));_0x13a873[_0x1434de(0x267)]=_0x1434de(0x161),_0x13a873[_0x1434de(0x281)]=shouldGetLinkInstead,_0x13a873[_0x1434de(0x25e)]('change',async()=>{var _0x4e1c5b=_0x1434de;const _0xf66574=_0x13a873[_0x4e1c5b(0x281)];await chrome[_0x4e1c5b(0x26f)][_0x4e1c5b(0x165)]['set']({'shouldGetLinkInstead':_0xf66574}),_0x1f46a2[_0x4e1c5b(0x272)]=_0xf66574?_0x4e1c5b(0x27e):_0x4e1c5b(0x177);}),_0x1c012c[_0x1434de(0x179)](_0x13a873);const _0x5052e4=document[_0x1434de(0x19f)](_0x1434de(0x1b7));_0x5052e4[_0x1434de(0x272)]=_0x1434de(0x27b);const _0xa7659c=document[_0x1434de(0x19f)](_0x1434de(0x172));_0xa7659c['type']=_0x1434de(0x161),_0xa7659c[_0x1434de(0x281)]=![],_0xa7659c[_0x1434de(0x281)]=shouldUseGiftOption,_0xa7659c[_0x1434de(0x25e)]('change',async()=>{var _0xc1e4bb=_0x1434de;const _0x5ac696=_0xa7659c[_0xc1e4bb(0x281)];await chrome[_0xc1e4bb(0x26f)][_0xc1e4bb(0x165)]['set']({'shouldUseGiftOption':_0x5ac696});}),_0x5052e4['prepend'](_0xa7659c);const _0x1a0bb8=document[_0x1434de(0x19f)](_0x1434de(0x1b7));_0x1a0bb8[_0x1434de(0x272)]=_0x1434de(0x154);const _0x4e512b=document[_0x1434de(0x19f)](_0x1434de(0x268));_0x4e512b['rows']=0x3,_0x4e512b[_0x1434de(0x18f)]=0x19,_0x4e512b[_0x1434de(0x187)]=giftMessage,_0x4e512b[_0x1434de(0x25e)](_0x1434de(0x172),async _0x646de9=>{var _0x2aa51d=_0x1434de;await chrome[_0x2aa51d(0x26f)][_0x2aa51d(0x165)][_0x2aa51d(0x1c3)]({'giftMessage':_0x646de9['target'][_0x2aa51d(0x187)]});}),_0x1a0bb8[_0x1434de(0x259)](_0x4e512b);const _0x3e5410=document[_0x1434de(0x19f)](_0x1434de(0x1b7));_0x3e5410[_0x1434de(0x272)]=_0x1434de(0x1e1);const _0x457a59=document[_0x1434de(0x19f)]('input');return _0x457a59['type']=_0x1434de(0x1a1),_0x457a59[_0x1434de(0x187)]=giftMessageSender,_0x457a59['addEventListener'](_0x1434de(0x172),async _0x326631=>{var _0x4ea3ff=_0x1434de;await chrome[_0x4ea3ff(0x26f)]['local'][_0x4ea3ff(0x1c3)]({'giftMessageSender':_0x326631['target'][_0x4ea3ff(0x187)]});}),_0x3e5410[_0x1434de(0x259)](_0x457a59),_0x5ced81[_0x1434de(0x259)](_0x5e2e90),_0x5ced81['appendChild'](_0x1c012c),_0x5ced81['appendChild'](document['createElement']('br')),_0x5ced81[_0x1434de(0x259)](_0x5052e4),_0x5ced81[_0x1434de(0x259)](document['createElement']('br')),_0x5ced81['appendChild'](_0x1a0bb8),_0x5ced81[_0x1434de(0x259)](document[_0x1434de(0x19f)]('br')),_0x5ced81['appendChild'](_0x3e5410),_0x5d2f97[_0x1434de(0x259)](_0x5ced81),_0x4b61c7[_0x1434de(0x25e)](_0x1434de(0x1e2),_0x27bede=>{var _0x286ef3=_0x1434de;_0x27bede[_0x286ef3(0x192)](),_0x5d2f97['style'][_0x286ef3(0x1ea)]=_0x286ef3(0x1d7);}),_0x38e406[_0x1434de(0x259)](_0x1f46a2),_0x38e406[_0x1434de(0x259)](_0x4b61c7),_0x38e406[_0x1434de(0x259)](_0x5d2f97),_0x38e406;}async function autoOrder(){var _0x23c241=a0_0x1ce27a,_0x1737e5=await getOrderDetails();console[_0x23c241(0x176)](_0x23c241(0x24b),_0x1737e5);var _0x41fce9=await chrome[_0x23c241(0x1ac)][_0x23c241(0x27d)]({'type':_0x23c241(0x246),'orderDetails':_0x1737e5},function(_0x4e45f6){var _0x221d88=_0x23c241;console[_0x221d88(0x176)]('response',_0x4e45f6);});console[_0x23c241(0x176)]('response',_0x41fce9);}function getItemNumber(){var _0x2c1e76=a0_0x1ce27a,_0x31b93f=document[_0x2c1e76(0x1bc)](_0x2c1e76(0x224)),_0xa365b9=_0x31b93f[_0x2c1e76(0x1f4)](_0x2c1e76(0x271))[0x1][_0x2c1e76(0x272)];return _0xa365b9;}async function getSkuFromDescription(_0xd48d9d){var _0x5ac23c=a0_0x1ce27a,_0x31a77e=await new Promise(_0x57991c=>{var _0x6a9f4c=a0_0x224f;chrome['runtime'][_0x6a9f4c(0x27d)]({'type':_0x6a9f4c(0x1e0),'itemNumber':_0xd48d9d},function(_0x215a46){_0x57991c(_0x215a46);});});return _0x31a77e?.[_0x5ac23c(0x20e)];}async function updateSheet(_0xc97ff9,_0x512375,_0x2c1522){var _0x5a1b1a=a0_0x1ce27a;const _0x14ce18='https://script.google.com/macros/s/AKfycbwTOktup4oRvsKGNPtMSwUTNX41Z93HZt_PqFDzzgu0jPysi9Tmmrcs-LPf86z2nc8tnQ/exec',_0x3ebf38={'spreadsheetId':_0xc97ff9,'range':_0x512375,'values':_0x2c1522};try{const _0x3a405b=await fetch(_0x14ce18,{'method':_0x5a1b1a(0x260),'headers':{'Content-Type':_0x5a1b1a(0x23f)},'body':JSON[_0x5a1b1a(0x19b)](_0x3ebf38)}),_0x3e2e63=await _0x3a405b[_0x5a1b1a(0x24c)]();console[_0x5a1b1a(0x176)](_0x5a1b1a(0x1ab),_0x3e2e63);}catch(_0x25e23a){console[_0x5a1b1a(0x1dd)]('Error\x20updating\x20sheet:',_0x25e23a);}}document[a0_0x1ce27a(0x234)](a0_0x1ce27a(0x289))['addEventListener'](a0_0x1ce27a(0x1e2),()=>{var _0x3f6981=a0_0x1ce27a;const _0x56c4af=_0x3f6981(0x14f),_0x1eb5bb=_0x3f6981(0x203),_0xce07dd=[[_0x3f6981(0x231)]];updateSheet(_0x56c4af,_0x1eb5bb,_0xce07dd);});