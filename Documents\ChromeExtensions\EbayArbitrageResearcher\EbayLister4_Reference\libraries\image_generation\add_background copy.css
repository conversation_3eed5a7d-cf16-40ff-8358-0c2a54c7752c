.generate-image-button {
    background-color: #3B5998; /* Facebook's signature blue for a different look */
    color: white; /* White text for contrast */
    padding: 5px 10px; /* Padding */
    border: none; /* No border */
    border-radius: 3px; /* Rounded corners */
    font-size: 12px; /* Font size */
    font-weight: bold; /* Bold text */
    cursor: pointer; /* Pointer on hover */
    transition: background-color 0.3s; /* Smooth background transition */
    position: relative; /* Needed for the loading animation positioning */
    overflow: hidden; /* Ensures the pseudo-elements do not overflow the button */

    margin-left: 5px; /* Spacing between buttons */
}

.generate-image-button:hover {
    background-color: #334D77; /* Darker shade of blue for hover effect */
}

.generate-image-button:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, 0.2);
    animation: loadingAnimation 2s linear infinite;
    opacity: 0; /* Start hidden */
    transition: opacity 0.3s; /* Fade effect */
}

.generate-image-button-working:before {
    opacity: 1; /* Show animation when working */
}

@keyframes loadingAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.generate-image-button-success {
    background-color: #4CAF50; /* Green background for success state */
    color: white; /* White text for contrast */
}

.generate-image-button-error {
    background-color: #F44336; /* Red background for error state */
    color: white; /* White text for contrast */
}


.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    position: relative;
    padding: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    max-height: 80%;
    overflow: auto;
}

.modal-canvas {
    border: 1px solid #ddd;
    cursor: crosshair;
}

.modal-button {
    margin-top: 20px;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    outline: none;
}

.modal-button:hover {
    background-color: #0056b3;
}


.mask-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}



#generate-image-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    margin: 20px auto;
    background-color: #f4f4f4;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 500px;
}

.generate-image-button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-bottom: 20px;
}

.generate-image-button-working {
    background-color: #f0ad4e; /* Change to any working-status color */
}

.generate-image-button-success {
    background-color: #5cb85c; /* Change to any success-status color */
}

.generate-image-button-error {
    background-color: #d9534f; /* Change to any error-status color */
}

#use-auto-mask-checkbox + label {
    cursor: pointer;
    padding-left: 5px;
}

#background-description {
    width: calc(100% - 40px); /* Account for padding */
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    resize: vertical; /* Allows the user to resize the textarea vertically */
}

div.use-auto-mask {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
