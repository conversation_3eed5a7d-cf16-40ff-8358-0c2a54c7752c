{"manifest_version": 3, "name": "EcomSniper", "version": "42.63", "description": "When silence fails and chaos begins, EcomSniper stands. It’s time. The war begins…", "permissions": ["alarms", "storage", "tabs", "webRequest", "activeTab", "clipboardRead", "clipboardWrite", "contentSettings", "contextMenus", "cookies", "history", "scripting", "contextMenus", "unlimitedStorage", "identity", "offscreen", "declarativeNetRequest", "declarativeNetRequestWithHostAccess"], "externally_connectable": {"matches": ["http://127.0.0.1:5501/*", "http://localhost:5173/*"]}, "oauth2": {"client_id": "304679917793-k0uh4nuqo57i2ke9vt0lno9hif5cejl1.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/gmail.modify"]}, "host_permissions": ["https://*.etsy.ca/* ", "https://cors-anywhere.herokuapp.com/*", "*://etsy.ca/*", "*://*.amazon.ca/*", "*://*.amazon.com/*", "*://*.etsy.ca/*", "https://*.etsy.ca/* ", "https://cors-anywhere.herokuapp.com/*", "file:///*/", "*://*.ebay.ca/*", "<all_urls>", "*://*/*", "http://127.0.0.1:9003/", "http://127.0.0.1:3333/", "http://localhost:3333/", "http://localhost:9003/", "http://********:3102/", "http://********:1102/", "https://script.google.com/macros/s/*", "https://script.google.com/*", "https://www.homedepot.ca/*", "https://www.homedepot.com/*", "https://images.thdstatic.com/*", "https://pro.nocaptchaai.com/*", "https://video.aliexpress-media.com/*", "https://gv-vod-cdn.aliexpress-media.com/"], "web_accessible_resources": [{"resources": ["availability-messages/*"], "matches": ["<all_urls>"]}, {"resources": ["dictionary/dictionary.json"], "matches": ["<all_urls>"]}, {"resources": ["js/iframe_inject.min.js"], "matches": ["<all_urls>"]}, {"resources": ["Ebay_Description_Templates/Template_with_head.html", "Ebay_Description_Templates/Shipping_Handling.html", "Ebay_Description_Templates/*", "prompts_json/*"], "matches": ["<all_urls>"]}, {"resources": ["text_prompts/*"], "matches": ["<all_urls>"]}, {"resources": ["Call_To_Action_HTML/call_to_action.Html"], "matches": ["<all_urls>"]}, {"resources": ["Favicons/*"], "matches": ["https://*/*"]}, {"resources": ["VeroList.txt"], "matches": ["https://*/*"]}, {"resources": ["Image_Badges/*"], "matches": ["<all_urls>"]}, {"resources": ["icons/Sniper.png"], "matches": ["<all_urls>"]}, {"resources": ["icons/*"], "matches": ["<all_urls>"]}, {"resources": ["libraries/*"], "matches": ["<all_urls>"]}, {"resources": ["restricted_words.txt"], "matches": ["<all_urls>"]}, {"resources": ["*"], "matches": ["<all_urls>"]}, {"resources": ["offscreen.html"], "matches": ["<all_urls>"]}, {"resources": ["content/aliexpress/injection.js"], "matches": ["<all_urls>"]}, {"resources": ["libraries/safety-lib/*"], "matches": ["<all_urls>"]}], "options_page": "options/options.html", "icons": {"16": "icons/Sniper.png", "32": "icons/Sniper.png", "48": "icons/Sniper.png", "128": "icons/Sniper.png"}, "content_scripts": [{"matches": ["*://*.amazon.com/*dp*", "*://*.amazon.ca/*dp*", "*://*.amazon.co.uk/*dp*", "*://*.amazon.com.au/*dp*", "*://*.amazon.de/*dp*", "*://*.amazon.fr/*dp*", "*://*.amazon.it/*dp*", "*://*.amazon.es/*dp*", "*://*.amazon.nl/*dp*", "*://*.amazon.co.jp/*dp*", "*://*.amazon.in/*dp*", "*://*.amazon.com.br/*dp*", "*://*.amazon.com.mx/*dp*"], "js": ["libraries/Sortable.js", "libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "libraries/context_window/ContextMenu.js", "libraries/captcha_solver.js", "libraries/chat_gpt_web.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/jquery.highlight-within-textarea.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/google_sheets_utils.js", "libraries/animation_utils.js", "libraries/jsonrepair.js", "libraries/amazon_lib.js", "libraries/search_marketplace_functions.js", "libraries/fabric.min.js", "libraries/image_generation/add_background.js", "libraries/address_parser.js", "content/amazon/amazon_search_page_functions.js", "content/amazon/amazon_ai_title_functions.js", "content/amazon/amazon.functions.js", "content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_vero_list_new.js", "content/amazon/amazon_vero_list_old.js", "content/amazon/amazon_vero_list_personal.js", "content/amazon/amazon_vero_list_from_ebay.js", "content/amazon/amazon_vero_function.js", "content/amazon/amazon.desc_template.js", "content/amazon/amazon.img_function.js", "research_seo/tf_idf_functions.js", "Advanced_Title_Builder/title_functions.js", "content/amazon/amazon_title_builder.js", "content/amazon/amazon_auto_list_functions.js", "content/amazon/amazon_auto_list.js", "content/amazon/amazon.js", "content/amazon/amazon_check_duplicate.js", "libraries/fabric.min.js", "Image_Template_Designer/functions.js", "libraries/templateImage.js", "content/amazon/amazon_image_test.js", "content/amazon/amazon_test.js", "content/amazon/auto_order/functions.js", "content/amazon/auto_order/content.js"], "css": ["libraries/context_window/ContextMenu.css", "content/amazon/amazon.css", "libraries/jquery.highlight-within-textarea.css", "libraries/target-spin/spin.css", "libraries/image_generation/add_background.css"], "run_at": "document_idle"}, {"matches": ["https://www.amazon.ca/s*", "https://www.amazon.com/s*", "https://www.amazon.co.uk/s*", "https://www.amazon.com.au/s*", "https://www.amazon.de/s*", "https://www.amazon.fr/s*", "https://www.amazon.it/s*", "https://www.amazon.es/s*", "https://www.amazon.nl/s*", "https://www.amazon.co.jp/s*", "https://www.amazon.in/s*", "https://www.amazon.com.br/s*", "https://www.amazon.com.mx/s*", "https://www.amazon.com/gp/bestsellers/", "https://www.amazon.ca/gp/bestsellers/", "https://www.amazon.co.uk/gp/bestsellers/", "https://www.amazon.com.au/gp/bestsellers/", "https://www.amazon.de/gp/bestsellers/", "https://www.amazon.fr/gp/bestsellers/", "https://www.amazon.it/gp/bestsellers/", "https://www.amazon.es/gp/bestsellers/", "https://www.amazon.nl/gp/bestsellers/", "https://www.amazon.co.jp/gp/bestsellers/", "https://www.amazon.in/gp/bestsellers/", "https://www.amazon.com.br/gp/bestsellers/", "https://www.amazon.com.mx/gp/bestsellers/", "https://www.amazon.com/gp/bestsellers/*", "https://www.amazon.ca/gp/bestsellers/*"], "js": ["libraries/target-spin/spin.js", "libraries/search_marketplace_functions.js", "content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_title_builder.js", "content/amazon/amazon_search_page_functions.js", "content/amazon/search/snipe_page/functions.js", "content/amazon/amazon_search_page.js"], "css": ["libraries/target-spin/spin.css", "content/amazon/amazon_search.css", "content/amazon/search/snipe_page/styles.css"], "run_at": "document_start"}, {"matches": ["https://www.lcgfoods.com/product-group/*"], "js": ["libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "content/displayData.js", "content/lcgfoods/lcgfoods.js"], "css": ["content/lcgfoods/lcgfoods.css", "content/displayData.css", "content/table.css", "libraries/target-spin/spin.css"], "run_at": "document_start"}, {"matches": ["https://www.temu.com/*"], "js": ["libraries/Sortable.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "content/displayData.js", "content/temu/scrape_functions.js", "content/temu/main.js"], "css": ["content/temu/temu.css", "content/displayData.css", "content/table.css", "libraries/target-spin/spin.css"], "run_at": "document_start"}, {"matches": ["https://www.etsy.com/your/shops/*/tools/listings/*", "https://www.etsy.com/your/listings/create?ref=listings_manager_prototype&from_page=/your/listings", "https://www.etsy.com/your/shops/MomAndPopLovesToShop/tools/listings/create"], "js": ["libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/html-to-img.js", "libraries/custom_utils.js", "libraries/image_utils.js", "content/etsy/listing_functions.js", "content/etsy/listing_page.js"], "run_at": "document_idle"}, {"matches": ["https://www.etsy.com/*/listing/*", "https://www.etsy.com/listing/*"], "js": ["libraries/Sortable.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "content/displayData.js", "libraries/listing_page_utils.js", "content/etsy/item/scrape_functions.js", "content/etsy/item/content.js"], "css": ["content/etsy/item/styles.css", "content/displayData.css", "content/table.css", "libraries/target-spin/spin.css"], "run_at": "document_start"}, {"matches": ["https://www.walmart.com/*ip/*", "https://www.walmart.ca/*ip/*", "https://www.walmart.com.mx/ip/*", "https://www.walmart.com.br/ip/*", "https://www.walmart.com.ar/ip/*", "https://www.walmart.com.co/ip/*", "https://www.walmart.com.uy/ip/*", "https://www.walmart.com.pe/ip/*"], "js": ["libraries/Sortable.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/page_functions.js", "content/displayData.js", "libraries/listing_page_utils.js", "content/walmart/item/scrape_functions.js", "content/walmart/item/content.js"], "css": ["content/walmart/item/styles.css", "content/displayData.css", "content/table.css", "libraries/target-spin/spin.css"], "run_at": "document_start"}, {"all_frames": true, "matches": ["https://www.picupload.ebay.ca/*"], "js": ["libraries/chrome_storage_utils.js", "libraries/image_utils.js", "content/ebay/picupload.js"], "css": ["content/ebay/ebay.css"], "run_at": "document_idle"}, {"all_frames": true, "matches": ["https://bulkedit.ebay.ca/*"], "js": ["libraries/chrome_storage_utils.js", "libraries/custom_utils.js", "content/ebay/promoted_listing.js"], "run_at": "document_idle"}, {"all_frames": false, "matches": ["*://bulksell.ebay.com/ws/*", "*://bulksell.ebay.ca/ws/*", "*://bulksell.ebay.co.uk/ws/*", "*://bulksell.ebay.com.au/ws/*", "*://bulksell.ebay.de/ws/*", "*://bulksell.ebay.fr/ws/*", "*://bulksell.ebay.it/ws/*", "*://bulksell.ebay.es/ws/*", "*://bulksell.ebay.nl/ws/*", "*://bulksell.ebay.com.hk/ws/*", "*://bulksell.ebay.in/ws/*", "*://bulksell.ebay.com.sg/ws/*", "*://bulksell.ebay.com.my/ws/*", "*://bulksell.ebay.ie/ws/*", "*://bulksell.ebay.ch/ws/*", "*://bulksell.ebay.at/ws/*", "*://bulksell.ebay.be/ws/*", "*://bulksell.ebay.com.tw/ws/*", "*://bulksell.ebay.com.ph/ws/*", "*://*.ebay.com/lstng?draftId=*", "*://*.ebay.ca/lstng?draftId=*", "*://*.ebay.co.uk/lstng?draftId=*", "*://*.ebay.com.au/lstng?draftId=*", "*://*.ebay.de/lstng?draftId=*", "*://*.ebay.fr/lstng?draftId=*", "*://*.ebay.it/lstng?draftId=*", "*://*.ebay.es/lstng?draftId=*", "*://*.ebay.nl/lstng?draftId=*", "*://*.ebay.com.hk/lstng?draftId=*", "*://*.ebay.in/lstng?draftId=*", "*://*.ebay.com.sg/lstng?draftId=*", "*://*.ebay.com.my/lstng?draftId=*", "*://*.ebay.ie/lstng?draftId=*", "*://*.ebay.ch/lstng?draftId=*", "*://*.ebay.at/lstng?draftId=*", "*://*.ebay.be/lstng?draftId=*", "*://*.ebay.com.tw/lstng?draftId=*", "*://*.ebay.com.ph/lstng?draftId=*", "*://*.ebay.com/lstng?*", "*://*.ebay.ca/lstng?*", "*://*.ebay.co.uk/lstng?*", "*://*.ebay.com.au/lstng?*", "*://*.ebay.de/lstng?*", "*://*.ebay.fr/lstng?*", "*://*.ebay.it/lstng?*", "*://*.ebay.es/lstng?*", "*://*.ebay.nl/lstng?*", "*://*.ebay.com.hk/lstng?*", "*://*.ebay.in/lstng?*", "*://*.ebay.com.sg/lstng?*", "*://*.ebay.com.my/lstng?*", "*://*.ebay.ie/lstng?*", "*://*.ebay.ch/lstng?*", "*://*.ebay.at/lstng?*", "*://*.ebay.be/lstng?*", "*://*.ebay.com.tw/lstng?*", "*://*.ebay.com.ph/lstng?*", "https://bulkedit.ebay.com/msku?draftId=*"], "js": ["libraries/page_functions.js", "libraries/custom.functions.js", "libraries/safety-lib/text_pruner.js", "libraries/chat_gpt_web.js", "libraries/gpt_encoder_browser_compatible.js", "libraries/change-favicon.js", "libraries/open_ai_content_script_functions.js", "libraries/find_element_util.js", "libraries/chrome_storage_utils.js", "libraries/jquery.min.js", "libraries/image_transform_utils.js", "libraries/image_utils.js", "libraries/custom_utils.js", "libraries/htmlSanitizer.js", "libraries/wait_for_element_utils.js", "libraries/post_to_server_utils.js", "content/amazon/amazon_check_duplicate_functions.js", "content/ebay/ebay_item_specifics_functions.js", "content/ebay/ebay_item_specifics_ai_functions.js", "content/ebay/ebay.functions.js", "content/ebay/ebay.desc_function.js", "content/ebay/ebay.video_functions.js", "content/ebay/ebay.image_functions.js", "content/ebay/ebay.js", "content/ebay/ebay_test.js"], "css": ["content/ebay/ebay.css"], "run_at": "document_start"}, {"all_frames": false, "matches": ["https://www.ebay.ca/sl/success*", "https://www.ebay.com/sl/success*", "https://www.ebay.co.uk/sl/success*", "https://www.ebay.com.au/sl/success*", "https://www.ebay.de/sl/success*", "https://www.ebay.fr/sl/success*", "https://www.ebay.it/sl/success*", "https://www.ebay.es/sl/success*", "https://www.ebay.nl/sl/success*", "https://www.ebay.com.hk/sl/success*", "https://www.ebay.in/sl/success*", "https://www.ebay.com.sg/sl/success*", "https://www.ebay.com.my/sl/success*", "https://www.ebay.ie/sl/success*", "https://www.ebay.ch/sl/success*", "https://www.ebay.at/sl/success*", "https://www.ebay.be/sl/success*", "https://www.ebay.com.tw/sl/success*", "https://www.ebay.com.ph/sl/success*"], "js": ["content/ebay/listing/sucess.js"], "run_at": "document_start"}, {"all_frames": false, "matches": ["*://*.ebay.com/sl/prelist/suggest*", "*://*.ebay.ca/sl/prelist/suggest*", "*://*.ebay.co.uk/sl/prelist/suggest*", "*://*.ebay.com.au/sl/prelist/suggest*", "*://*.ebay.de/sl/prelist/suggest*", "*://*.ebay.fr/sl/prelist/suggest*", "*://*.ebay.it/sl/prelist/suggest*", "*://*.ebay.es/sl/prelist/suggest*", "*://*.ebay.nl/sl/prelist/suggest*", "*://*.ebay.com.hk/sl/prelist/suggest*", "*://*.ebay.in/sl/prelist/suggest*", "*://*.ebay.com.sg/sl/prelist/suggest*", "*://*.ebay.com.my/sl/prelist/suggest*", "*://*.ebay.ie/sl/prelist/suggest*", "*://*.ebay.ch/sl/prelist/suggest*", "*://*.ebay.at/sl/prelist/suggest*", "*://*.ebay.be/sl/prelist/suggest*", "*://*.ebay.com.tw/sl/prelist/suggest*", "*://*.ebay.com.ph/sl/prelist/suggest*"], "js": ["libraries/chat_gpt_web.js", "libraries/jquery.min.js", "libraries/custom_utils.js", "libraries/post_to_server_utils.js", "libraries/page_functions.js", "content/ebay/ebay_pre_list_suggest_functions.js", "content/ebay/ebay_pre_list_suggest.js"], "run_at": "document_idle"}, {"all_frames": false, "matches": ["*://*.ebay.com/sl/prelist/identify?*", "*://*.ebay.ca/sl/prelist/identify?*", "*://*.ebay.co.uk/sl/prelist/identify?*", "*://*.ebay.com.au/sl/prelist/identify?*", "*://*.ebay.de/sl/prelist/identify?*", "*://*.ebay.fr/sl/prelist/identify?*", "*://*.ebay.it/sl/prelist/identify?*", "*://*.ebay.es/sl/prelist/identify?*", "*://*.ebay.nl/sl/prelist/identify?*", "*://*.ebay.com.hk/sl/prelist/identify?*", "*://*.ebay.in/sl/prelist/identify?*", "*://*.ebay.com.sg/sl/prelist/identify?*", "*://*.ebay.com.my/sl/prelist/identify?*", "*://*.ebay.ie/sl/prelist/identify?*", "*://*.ebay.ch/sl/prelist/identify?*", "*://*.ebay.at/sl/prelist/identify?*", "*://*.ebay.be/sl/prelist/identify?*", "*://*.ebay.com.tw/sl/prelist/identify?*", "*://*.ebay.com.ph/sl/prelist/identify?*"], "js": ["libraries/jquery.min.js", "libraries/wait_for_element_utils.js", "libraries/post_to_server_utils.js", "libraries/page_functions.js", "content/ebay/ebay_pre_list_identify_functions.js", "content/ebay/ebay_pre_list_identify.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/sh/lst/active*", "https://www.ebay.com/sh/lst/active*", "https://www.ebay.co.uk/sh/lst/active*", "https://www.ebay.com.au/sh/lst/active*", "https://www.ebay.de/sh/lst/active*", "https://www.ebay.fr/sh/lst/active*", "https://www.ebay.it/sh/lst/active*", "https://www.ebay.es/sh/lst/active*", "https://www.ebay.ca/sh/lst/active?keyword=*", "https://www.ebay.com/sh/lst/active?keyword=*", "https://www.ebay.co.uk/sh/lst/active?keyword=*", "https://www.ebay.com.au/sh/lst/active?keyword=*", "https://www.ebay.de/sh/lst/active?keyword=*", "https://www.ebay.fr/sh/lst/active?keyword=*", "https://www.ebay.it/sh/lst/active?keyword=*", "https://www.ebay.es/sh/lst/active?keyword=*"], "js": ["libraries/change-favicon.js", "libraries/jquery.min.js", "libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "libraries/search_marketplace_functions.js", "libraries/time_out_utils.js", "libraries/radar/radar.js", "libraries/price_converter.js", "content/ebay/ebay_active_listings/ebay.active_listings_functions.js", "content/ebay/ebay_active_listings/revise_listings_functions.js", "content/ebay/all_ebay/customViewSettings.js", "content/ebay/ebay_active_listings/ebay.active_listings.js", "content/ebay/ebay_active_listings/ebay_tracking_functions.js", "content/ebay/ebay_active_listings/ebay_tracking.js"], "css": ["content/ebay/ebay_active_listings/ebay.active_listings.css", "libraries/search_marketplace_functions.css", "libraries/radar/radar.css"], "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/sh/research*", "https://www.ebay.com/sh/research*", "https://www.ebay.co.uk/sh/research*", "https://www.ebay.com.au/sh/research*", "https://www.ebay.de/sh/research*", "https://www.ebay.fr/sh/research*", "https://www.ebay.it/sh/research*"], "js": ["libraries/context_window/ContextMenu.js", "libraries/page_functions.js", "libraries/search_marketplace_functions.js", "content/ebay/terapeak_functions.js", "content/ebay/terapeak.js"], "css": ["libraries/context_window/ContextMenu.css", "libraries/search_marketplace_functions.css"], "run_at": "document_start"}, {"matches": ["https://www.zikanalytics.com/SearchCompetitor/Index?search=true&Competitor=*", "https://www.zikanalytics.com/CategoryResearch/Result?*", "https://www.zikanalytics.com/SearchCompetitor/Index*"], "js": ["libraries/post_to_server_utils.js", "libraries/amazon_search_utils.js", "content/zikanalytics/search_competitor/functions.js", "content/zikanalytics/search_competitor/content.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/str/*", "https://www.ebay.com/str/*"], "js": ["libraries/post_to_server_utils.js", "libraries/amazon_search_utils.js", "content/ebay/store/functions.js", "content/ebay/store/content.js"], "css": ["content/ebay/store/button.css"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.com/sch/i.html?_dkr=1*", "https://www.ebay.ca/sch/i.html?_dkr=1*", "https://www.ebay.co.uk/sch/i.html?_dkr=1*", "https://www.ebay.com.au/sch/i.html?_dkr=1*", "https://www.ebay.de/sch/i.html?_dkr=1*", "https://www.ebay.fr/sch/i.html?_dkr=1*", "https://www.ebay.it/sch/i.html?_dkr=1*", "https://www.ebay.es/sch/i.html?_dkr=1*", "https://www.ebay.nl/sch/i.html?_dkr=1*", "https://www.ebay.com.hk/sch/i.html?_dkr=1*", "https://www.ebay.in/sch/i.html?_dkr=1*", "https://www.ebay.com.sg/sch/i.html?_dkr=1*", "https://www.ebay.com.my/sch/i.html?_dkr=1*", "https://www.ebay.ie/sch/i.html?_dkr=1*", "https://www.ebay.ch/sch/i.html?_dkr=1*", "https://www.ebay.at/sch/i.html?_dkr=1*", "https://*.ebay.com/*_ssn*", "https://*.ebay.ca/*_ssn*", "https://*.ebay.co.uk/*_ssn*", "https://*.ebay.com.au/*_ssn*", "https://*.ebay.de/*_ssn*", "https://*.ebay.fr/*_ssn*", "https://*.ebay.it/*_ssn*", "https://*.ebay.es/*_ssn*", "https://*.ebay.nl/*_ssn*", "https://*.ebay.com.hk/*_ssn*", "https://*.ebay.in/*_ssn*", "https://*.ebay.com.sg/*_ssn*", "https://*.ebay.com.my/*_ssn*"], "js": ["libraries/context_window/ContextMenu.js", "content/ebay/all_ebay/customViewSettings.js", "libraries/search_marketplace_functions.js", "libraries/date_utils.js", "libraries/price_converter.js", "content/ebay/public_search/functions.js", "content/ebay/store_search/functions.js", "content/ebay/store_search/content.js"], "css": ["libraries/context_window/ContextMenu.css", "libraries/search_marketplace_functions.css", "content/ebay/public_search/styles.css", "content/ebay/store_search/styles.css"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.ca/itm/*", "https://www.ebay.com/itm/*", "https://www.ebay.co.uk/itm/*", "https://www.ebay.com.au/itm/*", "https://www.ebay.de/itm/*", "https://www.ebay.fr/itm/*", "https://www.ebay.it/itm/*", "https://www.ebay.es/itm/*", "https://www.ebay.nl/itm/*"], "js": ["libraries/page_functions.js", "libraries/change-favicon.js", "libraries/firebase_ecomsniper_functions.js", "libraries/search_marketplace_functions.js", "libraries/price_converter.js", "libraries/safety-lib/text_pruner.js", "content/ebay/store_search/functions.js", "content/ebay/item/functions.js", "content/ebay/item/content.js"], "css": ["libraries/search_marketplace_functions.css", "content/ebay/item/styles.css", "content/ebay/public_search/styles.css", "content/ebay/store_search/styles.css"], "run_at": "document_start"}, {"matches": ["https://vi.vipr.ebaydesc.com/ws/eBayISAPI.dll?*", "https://vi.vipr.ebaydesc.com/itmdesc/*", "https://itm.ebaydesc.com/itmdesc/*"], "js": ["content/ebay/item/iframe_description.js"], "all_frames": true, "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/sch/i.html?*_nkw=*", "https://www.ebay.com/sch/i.html?*_nkw=*", "https://www.ebay.co.uk/sch/i.html?*_nkw=*", "https://www.ebay.com.au/sch/i.html?*_nkw=*", "https://www.ebay.de/sch/i.html?*_nkw=*", "https://www.ebay.fr/sch/i.html?*_nkw=*", "https://www.ebay.it/sch/i.html?*_nkw=*", "https://www.ebay.es/sch/i.html?*_nkw=*", "https://www.ebay.nl/sch/i.html?*_nkw=*", "https://www.ebay.com.hk/sch/i.html?*_nkw=*", "https://www.ebay.in/sch/i.html?*_nkw=*", "https://www.ebay.com.sg/sch/i.html?*_nkw=*", "https://www.ebay.com.my/sch/i.html?*_nkw=*", "https://www.ebay.ie/sch/i.html?*_nkw=*", "https://www.ebay.ch/sch/i.html?*_nkw=*", "https://www.ebay.at/sch/i.html?*_nkw=*", "https://www.ebay.be/sch/i.html?*_nkw=*"], "js": ["libraries/context_window/ContextMenu.js", "content/ebay/all_ebay/customViewSettings.js", "libraries/search_marketplace_functions.js", "libraries/date_utils.js", "libraries/price_converter.js", "content/ebay/store_search/functions.js", "content/ebay/public_search/functions.js", "content/ebay/public_search/content.js"], "css": ["libraries/context_window/ContextMenu.css", "libraries/search_marketplace_functions.css", "content/ebay/public_search/styles.css", "content/ebay/store_search/styles.css"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.com/bin/purchaseHistory?item=*", "https://www.ebay.ca/bin/purchaseHistory?item=*", "https://www.ebay.co.uk/bin/purchaseHistory?item=*", "https://www.ebay.com.au/bin/purchaseHistory?item=*", "https://www.ebay.de/bin/purchaseHistory?item=*", "https://www.ebay.fr/bin/purchaseHistory?item=*", "https://www.ebay.it/bin/purchaseHistory?item=*", "https://www.ebay.es/bin/purchaseHistory?item=*"], "js": ["libraries/search_marketplace_functions.css", "libraries/page_functions.js", "libraries/date_utils.js", "content/ebay/purchase_history/functions.js", "content/ebay/purchase_history/content.js"], "css": ["content/ebay/purchase_history/styles.css"], "run_at": "document_start"}, {"matches": ["https://keywordtool.io/*"], "js": ["content/keyword_tool/content.js"], "css": ["content/keyword_tool/style.css"], "run_at": "document_start"}, {"matches": ["https://mktg.ebay.ca/ppl/smart/create", "https://mktg.ebay.com/ppl/smart/create"], "js": ["content/ebay/promoted_listings/functions.js", "content/ebay/promoted_listings/content.js"], "css": ["content/ebay/public_search/styles.css"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.com/lstng/error?*", "https://www.ebay.ca/lstng/error?*", "https://www.ebay.co.uk/lstng/error?*", "https://www.ebay.com.au/lstng/error?*", "https://www.ebay.de/lstng/error?*", "https://www.ebay.fr/lstng/error?*", "https://www.ebay.it/lstng/error?*", "https://www.ebay.es/lstng/error?*", "https://www.ebay.nl/lstng/error?*", "https://www.ebay.com.hk/lstng/error?*", "https://www.ebay.in/lstng/error?*", "https://www.ebay.com.sg/lstng/error?*", "https://www.ebay.com.my/lstng/error?*", "https://www.ebay.ie/lstng/error?*"], "js": ["libraries/change-favicon.js", "libraries/page_functions.js", "content/ebay/ebay_error_page.js"], "css": [], "run_at": "document_start"}, {"matches": ["https://chat.openai.com/*"], "js": ["libraries/change-favicon.js", "libraries/page_functions.js", "libraries/change-favicon.js", "content/open_ai/chat.js"], "css": ["content/open_ai/chat.css"], "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/mesh/ord/details?*", "https://www.ebay.com/mesh/ord/details?*", "https://www.ebay.co.uk/mesh/ord/details?*", "https://www.ebay.com.au/mesh/ord/details?*", "https://www.ebay.de/mesh/ord/details?*", "https://www.ebay.fr/mesh/ord/details?*", "https://www.ebay.it/mesh/ord/details?*", "https://www.ebay.es/mesh/ord/details?*", "https://www.ebay.nl/mesh/ord/details?*", "https://www.ebay.com.hk/mesh/ord/details?*", "https://www.ebay.in/mesh/ord/details?*", "https://www.ebay.com.sg/mesh/ord/details?*", "https://www.ebay.com.my/mesh/ord/details?*", "https://www.ebay.ie/mesh/ord/details?*", "https://www.ebay.ch/mesh/ord/details?*", "https://www.ebay.at/mesh/ord/details?*", "https://www.ebay.be/mesh/ord/details?*", "https://www.ebay.com.tw/mesh/ord/details?*", "https://www.ebay.com.ph/mesh/ord/details?*"], "js": ["libraries/buttonIcons/functions.js", "libraries/wait_for_element_utils.js", "libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "content/ebay/mesh_order_details/scraping_functions.js", "content/ebay/mesh_order_details/functions.js", "content/ebay/mesh_order_details/content.js"], "css": ["libraries/buttonIcons/styles.css", "content/ebay/mesh_order_details/styles.css"], "run_at": "document_start"}, {"matches": ["https://*.ebay.com/*", "https://*.ebay.ca/*", "https://*.ebay.co.uk/*", "https://*.ebay.com.au/*", "https://*.ebay.de/*", "https://*.ebay.fr/*", "https://*.ebay.it/*", "https://*.ebay.es/*", "https://*.ebay.nl/*", "https://*.ebay.com.hk/*", "https://*.ebay.in/*", "https://*.ebay.com.sg/*", "https://*.ebay.com.my/*", "https://*.ebay.ie/*", "https://*.ebay.ch/*", "https://*.ebay.at/*", "https://*.ebay.be/*"], "js": ["libraries/page_functions.js", "libraries/ebay_general.js", "content/ebay/all_ebay/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.com/mys/sold*", "https://www.ebay.ca/mys/sold*", "https://www.ebay.co.uk/mys/sold*", "https://www.ebay.com.au/mys/sold*", "https://www.ebay.de/mys/sold*", "https://www.ebay.fr/mys/sold*", "https://www.ebay.it/mys/sold*", "https://www.ebay.es/mys/sold*", "https://www.ebay.nl/mys/sold*", "https://www.ebay.com.hk/mys/sold*", "https://www.ebay.in/mys/sold*", "https://www.ebay.com.sg/mys/sold*", "https://www.ebay.com.my/mys/sold*", "https://www.ebay.ie/mys/sold*", "https://www.ebay.ch/mys/sold*"], "js": ["libraries/page_functions.js", "libraries/search_marketplace_functions.js", "content/ebay/my_ebay_sold/functions.js", "content/ebay/my_ebay_sold/content.js"], "css": ["libraries/search_marketplace_functions.css"], "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/splashui/*", "https://www.ebay.com/splashui/*", "https://www.ebay.co.uk/splashui/*", "https://www.ebay.com.au/splashui/*", "https://www.ebay.de/splashui/*", "https://www.ebay.fr/splashui/*", "https://www.ebay.it/splashui/*", "https://www.ebay.es/splashui/*", "https://www.ebay.nl/splashui/*", "https://www.ebay.com.hk/splashui/*"], "js": ["libraries/page_functions.js", "content/ebay/splashui/functions.js", "content/ebay/splashui/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.com/n/error?*", "https://www.ebay.ca/n/error?*", "https://www.ebay.co.uk/n/error?*", "https://www.ebay.com.au/n/error?*", "https://www.ebay.de/n/error?*", "https://www.ebay.fr/n/error?*", "https://www.ebay.it/n/error?*"], "js": ["libraries/page_functions.js", "content/ebay/error/functions.js", "content/ebay/error/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.com/contact/*", "https://www.ebay.ca/contact/*", "https://www.ebay.co.uk/contact/*", "https://www.ebay.com.au/contact/*", "https://www.ebay.de/contact/*", "https://www.ebay.fr/contact/*", "https://www.ebay.it/contact/*", "https://www.ebay.es/contact/*", "https://www.ebay.nl/contact/*", "https://www.ebay.ca/cnt/viewMessage?*", "https://www.ebay.com/cnt/viewMessage?*", "https://www.ebay.co.uk/cnt/viewMessage?*", "https://www.ebay.com.au/cnt/viewMessage?*", "https://www.ebay.de/cnt/viewMessage?*", "https://www.ebay.fr/cnt/viewMessage?*", "https://www.ebay.it/cnt/viewMessage?*", "https://www.ebay.es/cnt/viewMessage?*", "https://www.ebay.nl/cnt/viewMessage?*", "https://www.ebay.ca/cnt/ReplyToMessages*", "https://www.ebay.com/cnt/ReplyToMessages*", "https://www.ebay.co.uk/cnt/ReplyToMessages*", "https://www.ebay.com.au/cnt/ReplyToMessages*", "https://www.ebay.de/cnt/ReplyToMessages*", "https://www.ebay.fr/cnt/ReplyToMessages*", "https://www.ebay.it/cnt/ReplyToMessages*", "https://www.ebay.es/cnt/ReplyToMessages*", "https://www.ebay.nl/cnt/ReplyToMessages*"], "js": ["libraries/page_functions.js", "Quick_Chat_Settings/quick_chat_content.js", "content/ebay/contact/functions.js", "content/ebay/contact/content.js"], "css": ["content/ebay/contact/styles.css", "Quick_Chat_Settings/quick_chat_content.css"], "run_at": "document_start"}, {"matches": ["https://www.facebook.com/marketplace/create/item"], "js": ["libraries/page_functions.js", "content/facebook_marketplace/create/functions.js", "content/facebook_marketplace/create/content.js"], "run_at": "document_start"}, {"matches": ["https://lens.google.com/uploadbyurl?url=*"], "js": ["libraries/page_functions.js", "content/google_lens/functions.js", "content/google_lens/content.js"], "run_at": "document_start"}, {"matches": ["https://www.homedepot.com/p/*", "https://www.homedepot.ca/p/*"], "js": ["libraries/Sortable.js", "libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "libraries/context_window/ContextMenu.js", "libraries/captcha_solver.js", "libraries/chat_gpt_web.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/jquery.highlight-within-textarea.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/google_sheets_utils.js", "libraries/animation_utils.js", "libraries/jsonrepair.js", "libraries/amazon_lib.js", "libraries/search_marketplace_functions.js", "libraries/fabric.min.js", "libraries/image_generation/add_background.js", "Image_Template_Designer/functions.js", "libraries/templateImage.js", "content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_title_builder.js", "content/amazon/amazon.functions.js", "content/amazon/amazon.img_function.js", "libraries/listing_ui/functions.js", "libraries/listing_ui/content.js", "content/amazon/amazon_image_test.js", "content/homedepot/product_page/functions.js", "content/homedepot/product_page/content.js"], "css": ["content/amazon/amazon.css", "content/homedepot/product_page/styles.css", "libraries/context_window/ContextMenu.css", "libraries/jquery.highlight-within-textarea.css", "libraries/target-spin/spin.css", "libraries/image_generation/add_background.css"], "run_at": "document_start"}, {"matches": ["https://www.homedepot.com/s/*", "https://www.homedepot.ca/s/*"], "js": ["libraries/page_functions.js", "content/homedepot/search/functions.js", "content/homedepot/search/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.com/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.co.uk/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.com.au/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.de/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.fr/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.it/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.es/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.nl/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.com.hk/sh/lst/ended?status=UNSOLD_NOT_RELISTED*", "https://www.ebay.in/sh/lst/ended?status=UNSOLD_NOT_RELISTED*"], "js": ["libraries/page_functions.js", "libraries/time_out_utils.js", "libraries/price_converter.js", "content/ebay/ebay_active_listings/ebay.active_listings_functions.js", "content/ebay/all_ebay/customViewSettings.js", "content/ebay/ebay_ended_listings/functions.js", "content/ebay/ebay_ended_listings/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.ca/bulksell?workspaceId=*", "https://www.ebay.com/bulksell?workspaceId=*", "https://www.ebay.co.uk/bulksell?workspaceId=*", "https://www.ebay.com.au/bulksell?workspaceId=*", "https://www.ebay.de/bulksell?workspaceId=*", "https://www.ebay.fr/bulksell?workspaceId=*", "https://www.ebay.it/bulksell?workspaceId=*", "https://www.ebay.es/bulksell?workspaceId=*", "https://www.ebay.nl/bulksell?workspaceId=*", "https://www.ebay.com.hk/bulksell?workspaceId=*", "https://www.ebay.in/bulksell?workspaceId=*", "https://www.ebay.com.sg/bulksell?workspaceId=*"], "js": ["content/ebay/all_ebay/errorDetectorEbay.js", "libraries/page_functions.js", "libraries/change-favicon.js", "content/ebay/bulksell/functions.js", "content/ebay/bulksell/content.js"], "run_at": "document_start"}, {"matches": ["https://newassets.hcaptcha.com/captcha/*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "content/captcha/hcaptcha/functions.js", "content/captcha/hcaptcha/content.js"], "all_frames": true, "run_at": "document_start"}, {"matches": ["https://www.ebay.com/bo/seller/reviewOffer/*", "https://www.ebay.ca/bo/seller/reviewOffer/*", "https://www.ebay.co.uk/bo/seller/reviewOffer/*", "https://www.ebay.com.au/bo/seller/reviewOffer/*", "https://www.ebay.de/bo/seller/reviewOffer/*", "https://www.ebay.fr/bo/seller/reviewOffer/*", "https://www.ebay.it/bo/seller/reviewOffer/*", "https://www.ebay.es/bo/seller/reviewOffer/*", "https://www.ebay.nl/bo/seller/reviewOffer/*", "https://www.ebay.com.hk/bo/seller/reviewOffer/*"], "js": ["libraries/page_functions.js", "/libraries/firebase_ecomsniper_functions.js", "libraries/search_marketplace_functions.js", "content/ebay/best_offer/functions.js", "content/ebay/best_offer/content.js"], "css": ["libraries/search_marketplace_functions.css"], "run_at": "document_start"}, {"matches": ["*://*.poshmark.com/*", "*://*.poshmark.ca/*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/duplicate/protection.js", "content/poshmark/listing/functions.js", "content/poshmark/listing/content.js"], "run_at": "document_start"}, {"matches": ["*://*.poshmark.com/closet/*", "*://*.poshmark.ca/closet/*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "content/poshmark/closet/functions.js", "content/poshmark/closet/content.js"], "css": ["content/poshmark/closet/styles.css"], "run_at": "document_start"}, {"matches": ["*://*.amazon.com/sp?*", "*://*.amazon.ca/sp?*", "*://*.amazon.co.uk/sp?*", "*://*.amazon.com.au/sp?*", "*://*.amazon.de/sp?*", "*://*.amazon.fr/sp?*", "*://*.amazon.it/sp?*", "*://*.amazon.es/sp?*", "*://*.amazon.nl/sp?*", "*://*.amazon.com.tr/sp?*", "*://*.amazon.ae/sp?*", "*://*.amazon.sa/sp?*", "*://*.amazon.sg/sp?*", "*://*.amazon.com.au/sp?*", "*://*.amazon.com.mx/sp?*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "content/amazon/seller_profile/functions.js", "content/amazon/seller_profile/content.js"], "run_at": "document_start"}, {"matches": ["https://*.aliexpress.com/item/*", "https://*.aliexpress.us/item/*", "https://*.aliexpress.us/gcp/*", "https://*.aliexpress.com/gcp/*", "https://*.aliexpress.us/?*", "https://*.aliexpress.com/?*", "https://*.aliexpress.us/i/*", "https://*.aliexpress.com/i/*"], "js": ["libraries/page_functions.js", "libraries/address_parser.js", "libraries/Sortable.js", "libraries/firebase_ecomsniper_functions.js", "libraries/context_window/ContextMenu.js", "libraries/captcha_solver.js", "libraries/chat_gpt_web.js", "libraries/target-spin/spin.js", "libraries/fly_in_text_utils.js", "libraries/simulate_rain_utils.js", "libraries/change-favicon.js", "libraries/post_to_server_utils.js", "libraries/open_ai_content_script_functions.js", "libraries/chrome_storage_utils.js", "libraries/table_utils.js", "libraries/button_utils.js", "libraries/jquery.min.js", "libraries/jquery-ui.min.js", "libraries/jquery.highlight-within-textarea.js", "libraries/htmlSanitizer.js", "libraries/custom.functions.js", "libraries/custom_utils.js", "libraries/image_utils.js", "libraries/image_transform_utils.js", "libraries/google_sheets_utils.js", "libraries/animation_utils.js", "libraries/jsonrepair.js", "libraries/amazon_lib.js", "libraries/search_marketplace_functions.js", "libraries/fabric.min.js", "libraries/image_generation/add_background.js", "Image_Template_Designer/functions.js", "libraries/templateImage.js", "content/amazon/amazon_check_duplicate_functions.js", "content/amazon/amazon_title_builder.js", "content/amazon/amazon.functions.js", "content/amazon/amazon.img_function.js", "libraries/listing_ui/functions.js", "libraries/listing_ui/content.js", "content/amazon/amazon_image_test.js", "content/amazon/amazon.functions.js", "content/aliexpress/item/functions.js", "content/aliexpress/item/content.js"], "css": ["content/amazon/amazon.css", "libraries/context_window/ContextMenu.css", "libraries/jquery.highlight-within-textarea.css", "libraries/target-spin/spin.css", "libraries/image_generation/add_background.css"], "run_at": "document_start"}, {"matches": ["https://www.amazon.com/checkout/*", "https://www.amazon.ca/checkout/*", "https://www.amazon.co.uk/checkout/*", "https://www.amazon.com.au/checkout/*", "https://www.amazon.de/checkout/*", "https://www.amazon.fr/checkout/*", "https://www.amazon.it/checkout/*", "https://www.amazon.es/checkout/*", "https://www.amazon.nl/checkout/*", "https://www.amazon.com.tr/checkout/*", "https://www.amazon.ae/checkout/*", "https://www.amazon.sa/checkout/*", "https://www.amazon.sg/checkout/*", "https://www.amazon.com.mx/checkout/*", "https://www.amazon.ca/gp/buy/*", "https://www.amazon.com/gp/buy/*", "https://www.amazon.co.uk/gp/buy/*", "https://www.amazon.com.au/gp/buy/*", "https://www.amazon.de/gp/buy/*", "https://www.amazon.fr/gp/buy/*", "https://www.amazon.it/gp/buy/*", "https://www.amazon.es/gp/buy/*", "https://www.amazon.nl/gp/buy/*", "https://www.amazon.com.tr/gp/buy/*", "https://www.amazon.ae/gp/buy/*", "https://www.amazon.sa/gp/buy/*", "https://www.amazon.sg/gp/buy/*", "https://www.amazon.com.mx/gp/buy/*", "https://www.amazon.ca/gp/product/handle-buy-box/*", "https://www.amazon.com/gp/product/handle-buy-box/*", "https://www.amazon.co.uk/gp/product/handle-buy-box/*", "https://www.amazon.com.au/gp/product/handle-buy-box/*", "https://www.amazon.de/gp/product/handle-buy-box/*", "https://www.amazon.fr/gp/product/handle-buy-box/*", "https://www.amazon.it/gp/product/handle-buy-box/*", "https://www.amazon.es/gp/product/handle-buy-box/*", "https://www.amazon.nl/gp/product/handle-buy-box/*", "https://www.amazon.com.tr/gp/product/handle-buy-box/*", "https://www.amazon.ae/gp/product/handle-buy-box/*", "https://www.amazon.sa/gp/product/handle-buy-box/*", "https://www.amazon.sg/gp/product/handle-buy-box/*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "libraries/wait_for_element_utils.js", "libraries/address_utils.js", "content/amazon/auto_order/checkout/functions.js", "content/amazon/auto_order/content.js"], "run_at": "document_start"}, {"matches": ["*://*.amazon.com/*dp*?autoOrder=true*", "*://*.amazon.ca/*dp*?autoOrder=true*", "*://*.amazon.co.uk/*dp*?autoOrder=true*", "*://*.amazon.com.au/*dp*?autoOrder=true*", "*://*.amazon.de/*dp*?autoOrder=true*", "*://*.amazon.fr/*dp*?autoOrder=true*", "*://*.amazon.it/*dp*?autoOrder=true*", "*://*.amazon.es/*dp*?autoOrder=true*", "*://*.amazon.nl/*dp*?autoOrder=true*", "*://*.amazon.com.tr/*dp*?autoOrder=true*", "*://*.amazon.ae/*dp*?autoOrder=true*"], "js": ["content/amazon/auto_order/product_page/functions.js", "content/amazon/auto_order/product_page/content.js"], "run_at": "document_start"}, {"matches": ["*://*.amazon.com/*cart*", "*://*.amazon.ca/*cart*", "*://*.amazon.co.uk/*cart*", "*://*.amazon.com.au/*cart*", "*://*.amazon.de/*cart*", "*://*.amazon.fr/*cart*", "*://*.amazon.it/*cart*", "*://*.amazon.es/*cart*", "*://*.amazon.nl/*cart*", "*://*.amazon.com.tr/*cart*", "*://*.amazon.ae/*cart*"], "js": ["content/amazon/auto_order/cart/functions.js", "content/amazon/auto_order/cart/content.js"], "run_at": "document_start"}, {"matches": ["*://*.amazon.com/DP/*", "*://*.amazon.ca/DP/*", "*://*.amazon.co.uk/DP/*", "*://*.amazon.com.au/DP/*", "*://*.amazon.de/DP/*", "*://*.amazon.fr/DP/*", "*://*.amazon.it/DP/*", "*://*.amazon.es/DP/*", "*://*.amazon.nl/DP/*", "*://*.amazon.com.tr/DP/*", "*://*.amazon.ae/DP/*", "https://www.amazon.ca/errors/validateCaptcha", "https://www.amazon.com/errors/validateCaptcha", "https://www.amazon.co.uk/errors/validateCaptcha", "https://www.amazon.com.au/errors/validateCaptcha", "https://www.amazon.de/errors/validateCaptcha", "https://www.amazon.fr/errors/validateCaptcha", "https://www.amazon.it/errors/validateCaptcha", "https://www.amazon.es/errors/validateCaptcha", "https://www.amazon.nl/errors/validateCaptcha", "https://www.amazon.com.tr/errors/validateCaptcha", "https://www.amazon.ae/errors/validateCaptcha"], "js": ["libraries/captcha_solver.js", "content/amazon/amazon.functions.js", "content/amazon/DP_Page/functions.js", "content/amazon/DP_Page/content.js"], "run_at": "document_start"}, {"matches": ["http://127.0.0.1:5501/*", "http://localhost:5173/*"], "js": ["content/ecomsniper/content.js"], "run_at": "document_start"}, {"matches": ["*://*.autods.com/*", "*://*.autods.ca/*", "*://*.autods.co.uk/*", "*://*.autods.com.au/*", "*://*.autods.de/*", "*://*.autods.fr/*", "*://*.autods.it/*", "*://*.autods.es/*", "*://*.autods.nl/*", "*://*.autods.com.tr/*", "*://*.autods.ae/*"], "js": ["libraries/page_functions.js", "content/autods/functions.js", "content/autods/content.js"], "run_at": "document_start"}, {"matches": ["https://www.ebay.com/sh/*", "https://www.ebay.ca/sh/*", "https://www.ebay.co.uk/sh/*", "https://www.ebay.com.au/sh/*", "https://www.ebay.de/sh/*", "https://www.ebay.fr/sh/*", "https://www.ebay.it/sh/*", "https://www.ebay.es/sh/*", "https://www.ebay.nl/sh/*", "https://www.ebay.com.hk/sh/*", "https://www.ebay.in/sh/*"], "js": ["libraries/page_functions.js", "libraries/firebase_ecomsniper_functions.js", "content/ebay/mesh_order_details/scraping_functions.js", "content/ebay/ebay_order_history/functions.js", "content/ebay/ebay_order_history/content.js"], "css": ["content/ebay/ebay_order_history/styles.css"], "run_at": "document_start"}], "action": {"default_popup": "popup/popup.html"}, "background": {"service_worker": "background.js"}, "key": "abcdefghijklmnopqrstuvwxyz123456", "homepage_url": "https://ecomsniper.io/terms-and-conditions"}