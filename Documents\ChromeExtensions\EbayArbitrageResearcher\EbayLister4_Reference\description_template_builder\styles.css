body {
    font-family: Arial, sans-serif;
    background: #f5f8fb;
    color: #333;
    padding: 20px;
    margin: 0;
  }
  
  .container {
    max-width: 1200px;
    margin: auto;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
  
  h1 {
    text-align: center;
    color: #0064D2;
    margin-bottom: 25px;
  }
  
  /* AI Description Section */
  .ai-section {
    background: #f0f7ff;
    border: 1px solid #cce3ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 25px;
  }
  
  .ai-section h2 {
    margin-top: 0;
    color: #007bff;
  }
  
  .ai-section textarea {
    width: 100%;
    min-height: 80px;
    border-radius: 5px;
    padding: 10px;
    border: 1px solid #ddd;
    resize: vertical;
    box-sizing: border-box;
  }
  
  .ai-btn-row {
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
    gap: 10px;
  }
  
  .ai-btn-row button {
    padding: 8px 15px;
    font-size: 14px;
    border: none;
    cursor: pointer;
    background-color: #17a2b8;
    color: white;
    border-radius: 5px;
    transition: 0.2s;
  }
  
  .ai-btn-row button:hover {
    background-color: #138496;
  }
  
  /* Mode Buttons */
  .mode-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
  }
  
  .mode-buttons button {
    padding: 8px 15px;
    font-size: 14px;
    border: none;
    cursor: pointer;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
    transition: 0.2s;
  }
  
  .mode-buttons button.active,
  .mode-buttons button:hover {
    background-color: #0056b3;
  }
  
  /* Editor Wrapper */
  .editor-wrapper {
    background: #fafafa;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
  }
  
  #liveEditor, #codeEditor {
    min-height: 350px;
    padding: 15px;
    border: none;
    resize: vertical;
    width: 100%;
    box-sizing: border-box;
    font-size: 14px;
  }
  
  #codeEditor {
    display: none;
    font-family: monospace;
  }
  
  /* Template Action Buttons */
  .action-btn-row {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
  
  .action-btn-row button {
    padding: 8px 15px;
    font-size: 14px;
    border: none;
    cursor: pointer;
    background-color: #28a745;
    color: white;
    border-radius: 5px;
    transition: 0.2s;
  }
  
  .action-btn-row button:hover {
    background-color: #218838;
  }
  
  button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
  

  /* Instructions Section */
.instructions-section {
    background: #fff8e5;
    border: 1px solid #f0d39e;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 25px;
  }
  
  .instructions-section h2 {
    margin-top: 0;
    color: #d97706;
  }
  
  .instructions-section ul {
    list-style-type: disc;
    margin-left: 20px;
  }
  
  .instructions-section ul li {
    margin-bottom: 5px;
  }
  
  .instructions-section p {
    font-size: 14px;
  }
  
/* Modal Styling */
.modal {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }
  
  .modal-content {
    background: #fff;
    padding: 20px;
    width: 90%;
    max-width: 960px;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }
  
  #closePreviewModal {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ccc;
    color: #333;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
  }
  
  #closePreviewModal:hover {
    background: #bbb;
  }
  