function a0_0x425d(){const _0x2d08e8=['1531998AWXuQt','63861niEXPn','6824PXPcSe','toDataURL','2pYqvHC','onload','clear','7219388EPKojm','1411941FFnFBt','688rPzwwA','crossOrigin','png','291774ZPnbqT','1175dnAjEk','onerror','26577iKGCFH','10utfGrz','src'];a0_0x425d=function(){return _0x2d08e8;};return a0_0x425d();}function a0_0x165f(_0x455f24,_0xbca600){const _0x425d85=a0_0x425d();return a0_0x165f=function(_0x165faa,_0x19da74){_0x165faa=_0x165faa-0x99;let _0xf2e794=_0x425d85[_0x165faa];return _0xf2e794;},a0_0x165f(_0x455f24,_0xbca600);}(function(_0x398b8a,_0x2fdaa9){const _0x4f190e=a0_0x165f,_0x138740=_0x398b8a();while(!![]){try{const _0x3202e1=-parseInt(_0x4f190e(0xa5))/0x1+-parseInt(_0x4f190e(0x9d))/0x2*(parseInt(_0x4f190e(0xa1))/0x3)+-parseInt(_0x4f190e(0x9b))/0x4*(-parseInt(_0x4f190e(0xa6))/0x5)+-parseInt(_0x4f190e(0x99))/0x6+parseInt(_0x4f190e(0x9a))/0x7+-parseInt(_0x4f190e(0xa2))/0x8*(-parseInt(_0x4f190e(0xa8))/0x9)+-parseInt(_0x4f190e(0xa9))/0xa*(-parseInt(_0x4f190e(0xa0))/0xb);if(_0x3202e1===_0x2fdaa9)break;else _0x138740['push'](_0x138740['shift']());}catch(_0xc5525b){_0x138740['push'](_0x138740['shift']());}}}(a0_0x425d,0x49dd1));async function loadImage(_0x8cdda1){return new Promise((_0x2fdfb7,_0x42472e)=>{const _0xa19265=a0_0x165f,_0x135671=new Image();_0x135671[_0xa19265(0xa3)]='anonymous',_0x135671['onload']=()=>_0x2fdfb7(_0x135671),_0x135671[_0xa19265(0xa7)]=_0x42472e,_0x135671['src']=_0x8cdda1;});}async function generateImageFromTemplate(_0x1993f6,_0x3ceda7,_0x8c2a66){const _0x5b32d3=a0_0x165f,_0x13a399=new fabric['Canvas'](null,{'width':0x5dc,'height':0x5dc});_0x13a399[_0x5b32d3(0x9f)](),await populateTemplate(_0x13a399,_0x3ceda7,_0x8c2a66);const _0x14c3b6=_0x13a399[_0x5b32d3(0x9c)]({'format':_0x5b32d3(0xa4),'quality':0x1}),_0x530a0f=new Image();return await new Promise(_0x5b9263=>{const _0x232a8c=_0x5b32d3;_0x530a0f[_0x232a8c(0x9e)]=_0x5b9263,_0x530a0f[_0x232a8c(0xaa)]=_0x14c3b6;}),_0x530a0f;}async function processImage(_0x322df7,_0x3b8eca,_0x534d0c){var _0x45a1c7=await resizeImage(_0x322df7,_0x3b8eca,_0x534d0c);return _0x45a1c7=await makeImageTransparent(_0x45a1c7),await getRoundedImage(_0x45a1c7,0x5);}