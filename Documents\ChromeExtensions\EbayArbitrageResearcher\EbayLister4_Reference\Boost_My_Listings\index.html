<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boost My Listings</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

</head>
<body>

<h1>Boost My Listings</h1>

<!-- <div id="schedule_container">
    <p>Current Time: <span id="current_time"></span></p>
    <p id="time_until_schedule" style="display: none;">Time until next schedule: <span id="countdown"></span></p>
</div> -->


<div id="schedule_container">
    <button id="toggle_schedule_options_button" class ="toggle_options_button" data-modal-target="#schedule_settings_modal"><i class="fas fa-cog" style="font-size: small;"> Schedule Automation</i></button>
    <p>Current Time: <span id="current_time"></span></p>
    <p id="time_until_schedule" style="display: none;">Time until next schedule: <span id="countdown"></span></p>

</div>

<div id="schedule_settings_modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Schedule Automation</h2>
        <p>Set a time to automatically end and relist low-performing listings. Enable the option below and choose a time to start the process.</p>
        <div id="schedule_container">

            <div class="checkbox-filter">

                <div class="filter">
                    <label for="sell_similar_toggle_schedule_automation">Enable Sell Similar Automation
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Enable this option to automatically end and relist low-performing listings. This will help boost their visibility on eBay. Ensure to configure the settings for this automation below.</span>
                    </label>
                    <input type="checkbox" id="sell_similar_toggle_schedule_automation" checked>
                </div>

                <div class="filter">
                    <label for="revise_listing_toggle_schedule_automation">Enable Bulk Revise Listing Automation
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Enable this option to automatically revise listings in bulk. This will help you manage your listings more efficiently. Ensure to configure the settings for this automation below.</span>
                    </label>
                    <input type="checkbox" id="revise_listing_toggle_schedule_automation">
                </div>



                <div id="divider"></div>

                <div class="filter">
                    <label for="schedule_toggle" style="color: red;">Enable Scheduled Relisting
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Enable this option to automatically start the process at a specific time. This is useful for running the tool at a convenient time. Ensure to set the scheduled time and interval below.</span>
                    </label>
                    <input type="checkbox" id="schedule_toggle">
                </div>

                <div class="filter">
                    <label for="scheduled_time">Scheduled Start Time:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Set the time to start the automation process. Ensure to set the time in 24-hour format. For example, entering '14:30' will start the process at 2:30 PM.</span>
                    </label>
                    <input type="time" id="scheduled_time">
                </div>


                <div class = "filter">
                    <label for="scheduled_interval">Scheduled Interval:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Set the interval to repeat the automation process. For example, selecting '24 hours' will repeat the process every 24 hours. Ensure to set the interval based on your requirements.</span>
                    </label>
                    <select id="scheduled_interval">
                        <option value="1">1 hour</option>
                        <option value="2">2 hours</option>
                        <option value="3">3 hours</option>
                        <option value="4">4 hours</option>
                        <option value="5">5 hours</option>
                        <option value="6">6 hours</option>
                        <option value="7">7 hours</option>
                        <option value="8">8 hours</option>
                        <option value="9">9 hours</option>
                        <option value="10">10 hours</option>
                        <option value="11">11 hours</option>
                        <option value="12">12 hours</option>
                        <option value="13">13 hours</option>
                        <option value="14">14 hours</option>
                        <option value="15">15 hours</option>
                        <option value="16">16 hours</option>
                        <option value="17">17 hours</option>
                        <option value="18">18 hours</option>
                        <option value="19">19 hours</option>
                        <option value="20">20 hours</option>
                        <option value="21">21 hours</option>
                        <option value="22">22 hours</option>
                        <option value="23">23 hours</option>
                        <option value="24" selected>24 hours</option>
                    </select>
                </div>


            </div>
           
        </div>
    </div>
</div>



<div id="end_and_sell_similar_container" class="main_container">
    <h3>End & Sell Similar</h3>
    



    <button id="toggle_similar_options_button" class ="toggle_options_button" data-modal-target="#sell_similar_settings_modal"><i class="fas fa-cog"></i></button>
    <button id="end_and_sell_similar_button">End & Sell Similar</button>
    <button id="info_button" class="info-button" data-modal-target="#info_modal"><i class="fas fa-info-circle"></i></button>

      <!-- Modal for General Information -->
      <div id="info_modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Welcome to Boost My Listings!</h2>
            <p>This tool helps you automatically end and relist low-performing listings to boost their visibility on eBay. Set filters, schedule automation, and let the tool manage your listings efficiently. Ensure to configure settings below before starting.</p>
            
            <p>Please turn on <strong>Customize ended listings view</strong> with the following options:</p>
            <ul>
                <li>Views (30 days)</li>
                <li>Item number</li>
                <li>Sold quantity</li>
                <li>Start date</li>
                <li>Available quantity</li>
            </ul>
            <p>For detailed steps, refer to the image below or visit this <a id="activeListingLink" href="https://www.ebay.com/sh/lst/active?action=pagination&sort=timeRemaining&limit=200" target="_blank">link</a> and click on the <strong>customize</strong> button to see the options.</p>
    
            <img src="CustomizeActiveView.png" alt="Customize Ended Listings View" style="width:100%; max-width: 600px; height: auto;">
    
            <p>Do the same for <a id="endedListingLink" href="https://www.ebay.com/sh/lst/ended?status=UNSOLD_NOT_RELISTED" target="_blank">ended listings</a> and enable the following option:</p>
            <ul>
                <li>Item number</li>
            </ul>
            
        </div>
    </div>
    
    </div>

        <!-- Additional Modal Example -->
        <!-- <div id="another_modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Advanced Settings</h2>
                <p>Configure advanced settings to fine-tune how your listings are managed. Understand each option to maximize the effectiveness of the tool.</p>
            </div>
        </div> -->



    <div id="sell_similar_settings_modal" class = "modal" style="display: none;">

        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Settings for Sell Similar Automation</h3>
            
            <h4>Filters (Optional)</h4>
            <div class="input-group">
                <div class="filter">
                    <label for="minSold">Minimum Sold:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Set the maximum number of sales an item should have to be considered for relisting. Items with sales less than or equal to this number will be targeted. For example, setting this to '5' will target items that have sold 5 times or fewer.</span>
                    </label>
                    <input type="number" id="minSold" name="minSold" placeholder="0" value="0">
                </div>
        
                <div class="filter">
                    <label for="minViews">Minimum Views:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Define the minimum views threshold. Items with views not exceeding this number are considered underperforming. For instance, entering '100' will target items that have received fewer than 100 views.</span>
                    </label>
                    <input type="number" id="minViews" name="minViews" placeholder="0" value="1000">
                </div>
        
                <div class="filter">
                    <label for="timeLeft">Hours Left:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Specify how many hours left until the listing ends. Items with a remaining time less than this will be flagged for relisting. Setting this to '24' targets items ending within the next 24 hours.</span>
                    </label>
                    <input type="number" id="timeLeft" name="timeLeft" placeholder="72" value="24">
                </div>
            </div>
            
            <h4>Options</h4>
        
            <div class="checkbox-filter">

                <div class="filter">
                <label for="autoCloseToggle">Auto Close:
                    <i class="fas fa-info-circle tooltip-icon"></i>
                    <span class="tooltip">Enable this option to automatically close the browser tab once the process is complete. This is useful for running the tool in the background.</span>
                </label>
                <input type="checkbox" id="autoCloseToggle" checked>
                </div>

                <div class="filter">
                <label for="autoRepeatToggle">Auto Repeat:
                    <i class="fas fa-info-circle tooltip-icon"></i>
                    <span class="tooltip">Enable this option to automatically repeat the process until no more low-performing items are found. This is useful for bulk relisting.</span>
                </label>
                <input type="checkbox" id="autoRepeatToggle" checked>
                </div>  

            </div>


            <!-- <h3>Schedule Automation</h3>
            <div id="schedule_container">
                <input type="checkbox" id="schedule_toggle">
                <label for="schedule_toggle">Enable Scheduled Relisting</label>
                <input type="time" id="scheduled_time">
            </div> -->
        
        </div>
    </div>
    


    
    <div id="loading_bar" class="loading-bar">
        <div id="loading_progress" class="loading-progress"></div>
    </div>
    <p id="status_message">Ready to start.</p>
    
    <!-- Log Modal Trigger Button -->
    <button id="log_button" class="log-button" data-modal-target="#log_modal"><i class="fas fa-list-alt"></i> View Logs</button>

    <!-- Log Modal -->
    <div id="log_modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Progress Logs</h2>
            <ul id="log_list">
                <!-- Logs will be dynamically added here -->
            </ul>
        </div>
    </div>

</div>


<div id="revise_listing_container" class="main_container" style="display: block;">
    <h3>Bulk Revise Listing</h3>
    <button id="revise_options_button" class="toggle_options_button" data-modal-target="#revise_settings_modal"><i class="fas fa-cog"></i></button>

    <div id="revise_settings_modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Settings for Bulk Revise Listing</h3>
            
            <div class="checkbox-filter">
             

                <div class="filter">
                    <label for="switchOfferToggle">Switch Offer:
                        <i class="fas fa-info-circle tooltip-icon"></i>
                        <span class="tooltip">Enable this option to automatically switch offer after revise. For example, if you have offer on, it will turn off after revise and vice versa.</span>
                    </label>
                    <input type="checkbox" id="switchOfferToggle" checked>

                </div>


            </div>
        </div>
    </div>

    <button id="bulk_revise_listing_button">Revise Listing</button>
    <select id="offers_dropdown">
        <option value="0">Turn On Offers</option>
        <option value="1" selected>Turn Off Offers</option>
    </select>


</div>


<!-- //send offers , accept offers , mark down sale etc , make general container and add those buttons -->

<div id="optimization_container" class="main_container" style="display: block;">
    <h3>Optimization</h3>
    <button id="send_offers_button">Send Offers</button>
    <!-- <button id="accept_offers_button">Accept Offers</button>
    <button id="markdown_sale_button">Markdown Sale</button> -->
</div>

<script src="/libraries/firebase_ecomsniper_functions.js"></script>
<script src="script.js"></script>
    
</body>
</html>
