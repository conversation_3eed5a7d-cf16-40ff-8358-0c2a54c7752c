body {
    font-family: Arial, sans-serif;
}

h1 {
    text-align: center;
}

form {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

#seo-tables {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
}

.table-container {
    width: 30%;
    margin: 10px;
}

table {
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed;
}

table, th, td {
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    text-align: left;
    padding: 8px;
}

td {
    word-wrap: break-word;
    padding: 8px;
}

#explanation {
    margin: 0 auto;
    width: 80%;
}
