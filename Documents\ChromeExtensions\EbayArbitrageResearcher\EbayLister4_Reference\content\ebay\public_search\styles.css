.btn-open-seller-page {
    background-color: #5bc0de;
    border-color: #46b8da;
    color: #fff;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    cursor: pointer;

}

.btn-open-seller-page:hover {
    background-color: #31b0d5;
    border-color: #269abc;
    color: #fff;
}

.btn-open-seller-page:active {
    background-color: #31b0d5;
    border-color: #269abc;
    color: #fff;
}

.btn-open-seller-page:focus {
    background-color: #5bc0de;
    border-color: #46b8da;
    color: #fff;
}

.btn-open-seller-page:visited {
    background-color: #5bc0de;
    border-color: #46b8da;
    color: #fff;
}




.vim a.custom-button.fake-btn--primary.ux-call-to-action {
    background-color: #90EE90 !important;
    /* Light green background */
    border-color: #90EE90 !important;
    /* Light green border */
    color: #fff !important;
    /* White text for contrast */
    margin-top: 5px !important;
    /* Add space above the button */

}

.vim a.custom-button.fake-btn--primary.ux-call-to-action:hover {
    background-color: #76C476 !important;
    /* Slightly darker green for hover */
    border-color: #76C476 !important;
    /* Slightly darker green border for hover */
    color: #fff !important;
    /* Keeping text white during hover */
}

/* If you want to change the color of text during hover, add this */
.vim a.custom-button.fake-btn--primary.ux-call-to-action:hover .ux-call-to-action__text {
    color: #fff !important;
    /* Keeping text white during hover */
}


.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled {
    background-color: #ccc !important;
    /* Grey background to indicate disabled state */
    border-color: #ccc !important;
    /* Grey border to indicate disabled state */
    color: #888 !important;
    /* Grey text to indicate disabled state */
    cursor: not-allowed !important;
    /* Change cursor to indicate it's not clickable */
    pointer-events: none;
}

/* Ensure the disabled button does not have hover or active styles */
.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled:hover,
.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled:active {
    background-color: #ccc !important;
    border-color: #ccc !important;
    color: #888 !important;
    cursor: not-allowed !important;
}


/* Style for the info-box */
.info-box {
    max-width: 300px;
    background-color: #f0f0f0;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-size: 0.9em;
}

.info-box p {
    margin: 5px 0;
}

/* Adding a bit of style to the URL for aesthetics */
.info-box a {
    color: #3498db;
    text-decoration: none;
}

.info-box a:hover {
    text-decoration: underline;
}


#sort-div {
    display: flex;
    /* Use flexbox */
    justify-content: flex-end;
    /* Align items to the end (right) */
    width: 100%;
    /* Ensure the div takes full width */
}

#sort-label {
    margin: 0 10px 0 0;
    /* Add space to the right of the label */
    line-height: 30px;
    /* Vertically center the label */
}

#sort-div button {
    background-color: #555;
    /* Dark grey background */
    color: #fff;
    /* White text */
    padding: 5px 10px;
    /* Minimal padding for a more compact form */
    border: 1px solid #444;
    /* Slightly darker border for depth */
    border-radius: 3px;
    /* Subtle rounded corners */
    cursor: pointer;
    /* Cursor pointer to indicate button */
    font-size: 12px;
    /* Smaller font size for compact text */
    font-family: Arial, sans-serif;
    /* Standard, compact font-family */
    transition: background-color 0.2s, box-shadow 0.2s;
    /* Smooth transition for background and shadow */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    /* Subtle shadow for a slight depth */

    margin: 0 1px 0 0;
    /* Add space to the right of the button */
}

#sort-div button:hover {
    background-color: #666;
    /* Slightly lighter grey on hover */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    /* Increased shadow for more depth on hover */
}

#sort-div button:active {
    background-color: #444;
    /* Darker grey on click */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    /* Revert to subtle shadow */
    position: relative;
    top: 1px;
    /* Slightly move down the button on click for tactile feedback */
}


#btn-check-all-purchase-history {
    background-color: #333;
    /* Dark background */
    color: #fff;
    /* White text */
    padding: 5px 10px;
    /* Compact padding */
    border: 1px solid #2c2c2c;
    /* Subtle border */
    border-radius: 3px;
    /* Rounded corners */
    cursor: pointer;
    /* Pointer cursor */
    font-size: 12px;
    /* Small font size */
    font-family: Arial, sans-serif;
    /* Sans-serif font */
    transition: background-color 0.2s, box-shadow 0.2s;
    /* Smooth transitions */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    /* Subtle shadow */
    display: block;
    /* Block display */
    margin-right: auto;
    /* Align left */
    margin-bottom: 0;
    /* Push to the bottom */

}

#btn-check-all-purchase-history:hover {
    background-color: #444;
    /* Darker on hover */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    /* Deeper shadow on hover */
}

#btn-check-all-purchase-history:active {
    background-color: #2b2b2b;
    /* Darker on click */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    /* Subtle shadow */
    position: relative;
    top: 1px;
    /* Slight move down on click */
}



@keyframes jumpAndBounce {
    0% {
      transform: translateY(0) scale(1);
    }
    20% {
      transform: translateY(-20px) scale(1.2);
    }
    40% {
      transform: translateY(0) scale(1);
    }
    60% {
      transform: translateY(-10px) scale(1.1);
    }
    80% {
      transform: translateY(0) scale(1);
    }
    100% {
      transform: translateY(0) scale(1);
    }
  }
  
  .icon {
    transition: transform 0.2s ease;
    display: inline-block;
    cursor: pointer; /* Suggests that the icon is clickable */
  }
  
  .icon:hover {
    transform: scale(1.25);
  }
  
  .icon.clicked {
    animation: jumpAndBounce 0.8s ease;
  }
  