<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Settings</title>
    <link rel="stylesheet" href="page.css">
</head>

<body>

    <h2>Duplicate Checker</h2>


    <div id="fetch-skus-container">

        <label for="fetch-skus">This scans your eBay account to grab all of the SKUS, please wait until its
            complete.</label>
        <button id="fetch-skus">Start Scanning for SKUs</button>
        <div style="display: flex; flex-direction: row;">
            <div style="display: flex; flex-direction: column;">
              <label for="page-number">Page Number:</label>
              <input type="number" id="page-number" value="1" min="1" max="9000">
            </div>
          
            <div style="display: flex; flex-direction: column;">
              <label for="results-per-page-select">Results per page:</label>
              <select id="results-per-page-select" name="resultsPerPage">
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="200" selected>200</option>
              </select>
            </div>
          </div>

        <label for="skus">SKUS Saved:</label>
        <textarea id="skus" cols="30" rows="10" placeholder="You havn't added any SKUS yet"></textarea>

        <div id="sku_buttons">

            <button id="clear-skus">Clear SKUS</button>
            <button id="remove-duplicates">Remove Duplicates</button>
            <button id="save-skus" class="un-copied">Save SKUS to clipboard</button>
        </div>



        <h4>Total SKUs Saved So Far: <span id="total-skus">0</span></h4>
    </div>


    
    <label for="add-skus">You can import your SKUS Here. <span id="total-imported-skus"></span></label>
    <div id="add-skus-container">
        <textarea id="add-skus" cols="30" rows="10" placeholder="Enter SKUS seperated with new lines"></textarea>
        <button id="import-skus">Import SKUS</button>

        <div id="duplicate-skus-container">
            <textarea id="duplicate-skus" cols="30" rows="10" placeholder="No duplicate SKUS found"></textarea>
            <h4>Total Duplicate SKUS Found: <span id="total-duplicate-skus">0</span></h4>



            <p>Choose Batch Number:  <input type="number" id="duplicate-batch-number" value="1" min="1" max="10">/<span id="duplicate-batch-total">0</span></p>
            <button id="open-duplicate-skus-10">Open Duplicate SKUS in new tab (10 at a time)</button>



        </div>
        
    </div>






    

    <h2>Advanced Users</h2>

    
    <label for="asins-to-skus">Convert ASINS to SKUS</label>
    <div id="asins-to-skus-container" class="convert-container">  
        <textarea id="asins-to-skus" cols="30" rows="10" placeholder="Enter ASINS seperated with new lines"></textarea>
        <button id="convert-asins-to-skus">Convert ASINS to SKUS</button>
        <textarea id="asins-to-skus-results" cols="30" rows="10" placeholder="Results will appear here"></textarea>
    </div>


    <label for="skus-to-asins">Convert SKUS to ASINS</label>
    <div id="skus-to-asins-container" class="convert-container">
        <textarea id="skus-to-asins" cols="30" rows="10" placeholder="Enter SKUS seperated with new lines"></textarea>
        <button id="convert-skus-to-asins">Convert SKUS to ASINS</button>
        <textarea id="skus-to-asins-results" cols="30" rows="10" placeholder="Results will appear here"></textarea>
    </div>


    <label for="url-to-asin">Convert URL to ASIN</label>
    <div id="url-to-asin-container" class="convert-container">
        <textarea id="url-to-asin" cols="30" rows="10" placeholder="Enter URLS seperated with new lines"></textarea>
        <button id="convert-url-to-asin">Convert URL to ASIN</button>
        <textarea id="url-to-asin-results" cols="30" rows="10" placeholder="Results will appear here"></textarea>
    </div>












    <script src ="../libraries/amazon_lib.js"></script>
    <script src="/content/amazon/amazon_check_duplicate_functions.js"></script>
    <script src="functions.js"></script>

</body>

</html>