var a0_0x1f5e9e=a0_0x2158;(function(_0x356409,_0x277fda){var _0x19b15b=a0_0x2158,_0xc90d95=_0x356409();while(!![]){try{var _0x4b45ee=parseInt(_0x19b15b(0x183))/0x1*(-parseInt(_0x19b15b(0x171))/0x2)+parseInt(_0x19b15b(0x16f))/0x3+parseInt(_0x19b15b(0x156))/0x4*(parseInt(_0x19b15b(0x17a))/0x5)+parseInt(_0x19b15b(0x15a))/0x6+parseInt(_0x19b15b(0x178))/0x7*(parseInt(_0x19b15b(0x16b))/0x8)+-parseInt(_0x19b15b(0x181))/0x9*(parseInt(_0x19b15b(0x168))/0xa)+parseInt(_0x19b15b(0x17b))/0xb;if(_0x4b45ee===_0x277fda)break;else _0xc90d95['push'](_0xc90d95['shift']());}catch(_0x4164a0){_0xc90d95['push'](_0xc90d95['shift']());}}}(a0_0x1db5,0x8eca1));function a0_0x2158(_0x5a60dd,_0x41f370){var _0x1db561=a0_0x1db5();return a0_0x2158=function(_0x21582f,_0x526006){_0x21582f=_0x21582f-0x14c;var _0x2520f0=_0x1db561[_0x21582f];return _0x2520f0;},a0_0x2158(_0x5a60dd,_0x41f370);}var skuList=[];console[a0_0x1f5e9e(0x16d)](a0_0x1f5e9e(0x185));var table_rows_pattern=a0_0x1f5e9e(0x180);chrome[a0_0x1f5e9e(0x17d)][a0_0x1f5e9e(0x15b)][a0_0x1f5e9e(0x14d)](async(_0x334d8d,_0x4de897,_0x500b53)=>{var _0x415b1a=a0_0x1f5e9e;console[_0x415b1a(0x16d)](_0x4de897[_0x415b1a(0x170)]?_0x415b1a(0x179)+_0x4de897[_0x415b1a(0x170)][_0x415b1a(0x165)]:_0x415b1a(0x169)+_0x334d8d[_0x415b1a(0x15c)]),_0x334d8d[_0x415b1a(0x15c)]===_0x415b1a(0x162)&&(console[_0x415b1a(0x16d)](_0x415b1a(0x162)),getAllSKU(_0x334d8d[_0x415b1a(0x153)])),_0x334d8d[_0x415b1a(0x15c)]==='save_sku_on_page'&&(console[_0x415b1a(0x16d)](_0x415b1a(0x164)),chrome[_0x415b1a(0x184)][_0x415b1a(0x152)]['get']('skuList',_0x19bb57=>{var _0x1a9e53=_0x415b1a,_0xc6354d=_0x19bb57['skuList'];console[_0x1a9e53(0x16d)](_0x1a9e53(0x17e),_0xc6354d['length']),console['log'](_0x1a9e53(0x17e),_0xc6354d),saveTheSkusOnPageManual();}));});function clearSKUsListFromLocalStorage(){return new Promise((_0x3be848,_0x3a19b2)=>{var _0x188fd1=a0_0x2158;chrome['storage'][_0x188fd1(0x152)][_0x188fd1(0x150)]({'skuList':[]},()=>{_0x3be848();});});}async function getAllSKU(_0x5b8862){var _0x2b3fec=a0_0x1f5e9e,_0xcf78a1=!![],_0x35986c=getTotalPgNumber(),_0x50c3b4=getCurrentPgNumber(),_0x9843f2=0x1;_0x5b8862['page_number']&&(_0x9843f2=_0x5b8862[_0x2b3fec(0x186)]);while(_0xcf78a1){console['log']('1'),_0x50c3b4=getCurrentPgNumber();var _0x5af66a=await getSkusOnPage();console['log']('2'),console[_0x2b3fec(0x16d)]('skusOnPage',_0x5af66a),skuList=skuList[_0x2b3fec(0x16c)](_0x5af66a),await saveSkuListToLocalStorage(_0x5af66a),console[_0x2b3fec(0x16d)](_0x2b3fec(0x17e),skuList),document[_0x2b3fec(0x14e)]='Page\x20'+_0x50c3b4+'/'+_0x35986c+_0x2b3fec(0x16e)+skuList[_0x2b3fec(0x155)],_0xcf78a1=await doesNextPageExistFunc(),console[_0x2b3fec(0x16d)](_0x2b3fec(0x167),_0xcf78a1),console[_0x2b3fec(0x16d)]('3');if(!_0xcf78a1)break;_0x9843f2++;var _0x977010=waitForNextPageToLoad(_0x9843f2);await goToNextPage(),console['log']('4'),await waitSomeTime(0x3e8),await _0x977010,chrome[_0x2b3fec(0x184)]['local']['set']({'page_number':_0x9843f2}),chrome[_0x2b3fec(0x17d)][_0x2b3fec(0x16a)]({'type':_0x2b3fec(0x177)}),console['log']('5');}document[_0x2b3fec(0x14e)]=_0x2b3fec(0x15f)+document[_0x2b3fec(0x14e)],await saveSkuListToLocalStorage(_0x5af66a),document[_0x2b3fec(0x14e)]='Saved\x20SKUS:\x20'+skuList[_0x2b3fec(0x155)];}async function saveTheSkusOnPageManual(){return new Promise((_0x56d7e1,_0x35f682)=>{var _0x51d5bd=a0_0x2158;chrome[_0x51d5bd(0x184)][_0x51d5bd(0x152)][_0x51d5bd(0x15e)](_0x51d5bd(0x17e),async _0x416ed3=>{var _0x409f5a=_0x51d5bd,_0xe2248a=_0x416ed3['skuList'],_0x4b1c88=await getSkusOnPage();console[_0x409f5a(0x16d)](_0x409f5a(0x175),_0x4b1c88),console[_0x409f5a(0x16d)](_0x409f5a(0x17e),_0xe2248a),_0xe2248a=_0xe2248a['concat'](_0x4b1c88),chrome[_0x409f5a(0x184)]['local'][_0x409f5a(0x150)]({'skuList':_0xe2248a},()=>{_0x56d7e1();});});});}function a0_0x1db5(){var _0x3c3099=['label','querySelectorAll','skusOnPage','push','updateData','3710QsGQAt','From\x20a\x20content\x20script:','7235UXjqie','9163UmOxdl','getAttribute','runtime','skuList','click','tbody[id*=grid-row]','63UfoERD','trim','6559QkGdbX','storage','ebay.active_listings.js','page_number','pagination__next','addListener','title','pageNumberToBeOn','set','getElementsByClassName','local','data','waiting\x20for\x20next\x20page\x20to\x20load,\x20current\x20PageNumber','length','956oOhGzW','true','go-to-page','currentPageNumber','616176sXYJeX','onMessage','type','textbox__control','get','Saving\x20Skus\x20-\x20','value','waiting\x20for\x20next\x20page\x20to\x20load,\x20pageNumberToBeOn','isWorkingPage','next\x20page\x20loaded','save_sku_on_page','url','aria-disabled','doesNextPageExist','208070SKvnOz','From\x20the\x20extension\x20request.type\x20ebay.js','sendMessage','13200HMfLMP','concat','log','\x20SKUS:\x20','187656pRaJzP','tab','200HnstuW','querySelector'];a0_0x1db5=function(){return _0x3c3099;};return a0_0x1db5();}function saveSkuListToLocalStorage(_0x1207a4){return new Promise((_0x1d950b,_0x5a2b46)=>{var _0x2359f0=a0_0x2158;chrome[_0x2359f0(0x184)][_0x2359f0(0x152)][_0x2359f0(0x15e)](_0x2359f0(0x17e),async _0x4210a4=>{var _0x2663e8=_0x2359f0,_0x2cca6a=_0x4210a4[_0x2663e8(0x17e)],_0xaf5452;_0x2cca6a&&(_0x2cca6a=_0x2cca6a[_0x2663e8(0x16c)](_0x1207a4),_0xaf5452=_0x2cca6a),!_0x2cca6a&&(_0xaf5452=_0x1207a4),chrome[_0x2663e8(0x184)][_0x2663e8(0x152)][_0x2663e8(0x150)]({'skuList':_0xaf5452},()=>{_0x1d950b();});});});}function waitSomeTime(_0x1d6e30){return new Promise((_0x5d6d7a,_0x11a5da)=>{setTimeout(()=>{_0x5d6d7a();},_0x1d6e30);});}async function waitForNextPageToLoad(_0x22f68a){var _0x4b8845=a0_0x1f5e9e,_0x42df00=getCurrentPgNumber();console[_0x4b8845(0x16d)](_0x4b8845(0x159),_0x42df00),console[_0x4b8845(0x16d)](_0x4b8845(0x14f),_0x22f68a);while(!(_0x42df00===_0x22f68a)){_0x42df00=await getCurrentPgNumber(),await waitSomeTime(0x3e8),console['log'](_0x4b8845(0x154),_0x42df00),console[_0x4b8845(0x16d)](_0x4b8845(0x161),_0x22f68a);if(_0x42df00===_0x22f68a){console['log'](_0x4b8845(0x163));return!![];break;}}}function goToNextPage(){return new Promise((_0x14931c,_0x2273c4)=>{var _0x6f0374=a0_0x2158,_0x319377=document[_0x6f0374(0x151)](_0x6f0374(0x14c))[0x0];_0x319377[_0x6f0374(0x17f)](),_0x14931c();});}function doesNextPageExistFunc(){return new Promise((_0x4ad642,_0x5d96b9)=>{var _0xad5fe9=a0_0x2158,_0x40eec5=document[_0xad5fe9(0x151)](_0xad5fe9(0x14c))[0x0],_0x5a4eda=_0x40eec5[_0xad5fe9(0x17c)](_0xad5fe9(0x166));_0x5a4eda==_0xad5fe9(0x157)?_0x4ad642(![]):_0x4ad642(!![]);});}function getTotalPgNumber(){var _0x3afce7=a0_0x1f5e9e,_0x2e415f=document[_0x3afce7(0x151)](_0x3afce7(0x158))[0x0];return totalPageNumber=_0x2e415f[_0x3afce7(0x151)](_0x3afce7(0x173))[0x0]['innerText']['replace'](/\D/g,''),parseInt(totalPageNumber);}function getCurrentPgNumber(){var _0x16aea9=a0_0x1f5e9e,_0x2cf02e=document[_0x16aea9(0x151)](_0x16aea9(0x158))[0x0];return currentPageNumber=_0x2cf02e[_0x16aea9(0x151)](_0x16aea9(0x15d))[0x0][_0x16aea9(0x160)],parseInt(currentPageNumber);}function sendDataToBackground(){}function getSkusOnPage(){return new Promise((_0x27f2d7,_0x3c7f16)=>{var _0x3aca23=a0_0x2158;const _0xa6df0d=[],_0x4f39bd=getItemNodes();for(const _0xd8f285 of _0x4f39bd){for(const _0x3c03d7 of _0xd8f285[_0x3aca23(0x174)]('tr')){const _0x12eebd=_0x3c03d7[_0x3aca23(0x172)]('td[class*=\x22listingSKU\x22]\x20.cell-wrapper');if(_0x12eebd){const _0x2f2ba8=_0x12eebd['textContent'][_0x3aca23(0x182)]();_0x2f2ba8&&_0xa6df0d[_0x3aca23(0x176)](_0x2f2ba8);}}}_0x27f2d7(_0xa6df0d);});}function getItemNodes(){var _0x189a62=a0_0x1f5e9e;return document[_0x189a62(0x174)]('table[id*=gridData]\x20tbody');}