var a0_0x7f504d=a0_0x3379;function a0_0x12c4(){var _0x46516f=['Ready\x20to\x20submit!\x20:)','parse','409Oksfhe','Adding\x20The\x20Brand','receive\x20page\x20load\x20waitForPicUploadIFrameToLoad','Adding\x20Item\x20Specifics','Adding\x20Title','receive\x20pageLoaded','removeEventListener','scrollHeight','Unbranded','.jpg','Does\x20Not\x20Apply','10.5','postMessage','2988469yMQmTx','selected_image','Adding\x20Shipping\x20Details','3314060zijEVu','9XSjBvq','promotedListing_iframe','2186ToKFEx','title','type','2815168PVleQG','insert_ebay_data','label','\x20-\x20Checking\x20If\x20Listing\x20Succeeded!','\x20-\x20ERROR!\x20Not\x20Yet\x20Listed!\x20Waiting\x202\x20seconds\x20to\x20try\x20again...','picUploadUpdate','Uploading\x20Selected\x20Image','pageLoaded_for_promoted_listing','Submitting\x20The\x20Item!','send_message_to_promoted_listing_to_click_promoted_listing_button','addListener','log','actionbar','uploader_iframe','\x20Listed\x20Successfully!\x20:)','pickItemCondition\x20error','data','Adding\x20NA\x20to\x20Required\x20Item\x20Specifics','filteredItemSpecifics','Started\x20inserting\x20product','[value=\x27List\x20item\x27]','onMessage','value','click','Adding\x20UPC','main_hd_images','productData','4Auutod','body','insert_draft_details','main_sd_images','getElementById','PLDataUpdate','Adding\x20Product\x20Format','ebay.js','message','2521477iXaJIv','inserted_draft','floor','435430LcSEbc','addEventListener','1323429ZljxgS','6oPsMpT','length','15.6','product_data_as_text','price','input[id^=\x27upl-\x27]','12aXyrzP','Promoting\x20The\x20listing','runtime','Adding\x20Price','Adding\x20The\x20SKU','Adding\x20Product\x20Condition','sku','cmd','contentWindow','FixedPrice','Adding\x20The\x20Description'];a0_0x12c4=function(){return _0x46516f;};return a0_0x12c4();}(function(_0x5ce196,_0x16720d){var _0x4626fd=a0_0x3379,_0x35923d=_0x5ce196();while(!![]){try{var _0x4cf15b=parseInt(_0x4626fd(0x7b))/0x1*(-parseInt(_0x4626fd(0x8e))/0x2)+-parseInt(_0x4626fd(0xba))/0x3*(-parseInt(_0x4626fd(0xac))/0x4)+parseInt(_0x4626fd(0x8b))/0x5+parseInt(_0x4626fd(0xbb))/0x6*(parseInt(_0x4626fd(0xb5))/0x7)+-parseInt(_0x4626fd(0x91))/0x8*(parseInt(_0x4626fd(0x8c))/0x9)+-parseInt(_0x4626fd(0xb8))/0xa+parseInt(_0x4626fd(0x88))/0xb*(-parseInt(_0x4626fd(0xc1))/0xc);if(_0x4cf15b===_0x16720d)break;else _0x35923d['push'](_0x35923d['shift']());}catch(_0x420326){_0x35923d['push'](_0x35923d['shift']());}}}(a0_0x12c4,0x5573b),console[a0_0x7f504d(0x9c)](a0_0x7f504d(0xb3)),chrome[a0_0x7f504d(0xc3)][a0_0x7f504d(0xa6)][a0_0x7f504d(0x9b)]((_0x2435e0,_0x3cc09,_0x529b6a)=>{var _0x680647=a0_0x7f504d;_0x2435e0[_0x680647(0x90)]===_0x680647(0xae)&&(pasteDraftTitle(_0x2435e0[_0x680647(0xab)]),chrome[_0x680647(0xc3)]['sendMessage']({'type':_0x680647(0xb6),'productData':_0x2435e0[_0x680647(0xab)]})),_0x2435e0[_0x680647(0x90)]===_0x680647(0x92)&&postEbayData(_0x2435e0[_0x680647(0xab)]);}));async function postEbayData(_0x2f1e9a){var _0x3b0deb=a0_0x7f504d;console['log'](_0x2f1e9a),console[_0x3b0deb(0x9c)]('0'),document['title']='Waiting\x20For\x20The\x20Image\x20Upload\x20Iframe\x20To\x20Load',await waitForPicUploadIFrameToLoad(),document['title']=_0x3b0deb(0xa4),console['log']('1'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x7f),await pasteTitle(_0x2f1e9a['custom_title']),console[_0x3b0deb(0x9c)]('2'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xc4);var _0x694caa=Math[_0x3b0deb(0xb7)](_0x2f1e9a[_0x3b0deb(0xbf)]*1.15*0x64)/0x64;await pastePrice(_0x694caa),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xc6),console[_0x3b0deb(0x9c)]('3');try{await pickItemCondition();}catch(_0x1a4c64){console[_0x3b0deb(0x9c)](_0x3b0deb(0xa0));}console[_0x3b0deb(0x9c)]('4'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xa9);try{await pasteUpc(_0x3b0deb(0x85)),console[_0x3b0deb(0x9c)]('5');}catch(_0x3c6c09){}document['title']=_0x3b0deb(0xc5),await pasteSKU(btoa(_0x2f1e9a[_0x3b0deb(0xc7)])),console['log']('6'),document['title']=_0x3b0deb(0x78),await pasteDescription(_0x2f1e9a),console[_0x3b0deb(0x9c)]('6.7'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xb2),await selectFormat(_0x3b0deb(0x77)),console[_0x3b0deb(0x9c)]('7');var _0x3936c9=await getAllEbayRecommendedItemSpecifics();console['log']('ebayRecommendedItemSpecifics',_0x3936c9),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x7e),await pasteItemSpecifics(_0x2f1e9a[_0x3b0deb(0xa3)]),console[_0x3b0deb(0x9c)]('8'),await pasteCustomItemSpecifics(_0x2f1e9a[_0x3b0deb(0xa3)]),console[_0x3b0deb(0x9c)]('9'),await waitSomeTime(0x7d0);for(var _0xcaf981=0x0;_0xcaf981<_0x3936c9[_0x3b0deb(0xbc)];_0xcaf981++){var _0x3eff8e=_0x3936c9[_0xcaf981];await pasteItemSpecificWithLabelAndValue(_0x3eff8e[_0x3b0deb(0x93)],_0x3eff8e[_0x3b0deb(0xa7)]);}console[_0x3b0deb(0x9c)]('10'),await waitSomeTime(0x1b58),await getAllItemSpecificsAndFillDetailsWithMachineLearning(_0x2f1e9a[_0x3b0deb(0xbe)]),console['log'](_0x3b0deb(0x86)),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xa2),await pasteNAToRequiredItemSpecifics(),console[_0x3b0deb(0x9c)]('11'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x8a),await pasteShippingWeight(_0x2f1e9a),console[_0x3b0deb(0x9c)]('12'),document['title']='Adding\x20Product\x20Dimensions',await pasteDimensions(_0x2f1e9a),console[_0x3b0deb(0x9c)]('13'),document[_0x3b0deb(0x8f)]='Allowing\x20Buyers\x20To\x20Stay\x20Anonymous',await allowBuyersToStayAnonymous(),console[_0x3b0deb(0x9c)]('14'),console['log']('15'),console[_0x3b0deb(0x9c)]('15.5'),console[_0x3b0deb(0x9c)](_0x3b0deb(0xbd));var _0x913251,_0x3e8751=_0x2f1e9a[_0x3b0deb(0xaa)],_0x36a7c7=_0x2f1e9a[_0x3b0deb(0xaf)];_0x913251=_0x3e8751;_0x3e8751[_0x3b0deb(0xbc)]<0x2&&_0x36a7c7[_0x3b0deb(0xbc)]>0x2&&(_0x913251=_0x36a7c7);var _0x13fb9d=_0x2f1e9a['filteredTitle'],_0x50b0a0=_0x2f1e9a[_0x3b0deb(0x89)];document[_0x3b0deb(0x8f)]=_0x3b0deb(0x97),await uploadImageToIframeOnEbay(_0x50b0a0,_0x13fb9d['substring'](0x0,0x64)+'-'+0x1+_0x3b0deb(0x84),_0x3b0deb(0xc0)),await pasteImages(_0x913251,_0x13fb9d),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x7c),await pasteBrand(_0x3b0deb(0x83)),console['log']('16'),window['scrollTo'](0x0,document[_0x3b0deb(0xad)][_0x3b0deb(0x82)]),document[_0x3b0deb(0x8f)]=_0x3b0deb(0xc2);try{await promoteListing(0x2);}catch(_0x44c253){console[_0x3b0deb(0x9c)](_0x44c253);}console['log']('17'),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x79),await waitSomeTime(0x7d0),document[_0x3b0deb(0x8f)]=_0x3b0deb(0x99),await listItem();var _0x5333a9=![];await waitSomeTime(0x1b58),_0x5333a9=await checkIfListingSucceeded(_0x2f1e9a['sku']);while(!_0x5333a9){document['title']=_0x2f1e9a[_0x3b0deb(0xc7)]+_0x3b0deb(0x95),await waitSomeTime(0x7d0),document[_0x3b0deb(0x8f)]=_0x2f1e9a[_0x3b0deb(0xc7)]+_0x3b0deb(0x94),_0x5333a9=await checkIfListingSucceeded(_0x2f1e9a[_0x3b0deb(0xc7)]),await waitSomeTime(0x7d0);}document[_0x3b0deb(0x8f)]=_0x2f1e9a[_0x3b0deb(0xc7)]+_0x3b0deb(0x9f);}function uploadImageToIframeOnEbay(_0x25f0c9,_0x59ba72,_0x26d7a8){return new Promise((_0x14e01c,_0x1da1e0)=>{var _0x44ddee=a0_0x3379,_0xf03832={'inputTagSelector':_0x26d7a8,'imageName':_0x59ba72,'b64Image':_0x25f0c9};document['getElementById'](_0x44ddee(0x9e))[_0x44ddee(0xc9)]['postMessage']({'type':'window_upload_pic_to_ebay_iframe','imageObject':_0xf03832},'*'),window[_0x44ddee(0xb9)]('message',_0x4341af,![]);function _0x4341af(_0x20f699){var _0x31edb0=_0x44ddee,_0x50733b=JSON['parse'](_0x20f699[_0x31edb0(0xa1)]);_0x50733b[_0x31edb0(0xc8)]===_0x31edb0(0x96)&&(window['removeEventListener'](_0x31edb0(0xb4),_0x4341af,![]),_0x14e01c());}});}function waitForPicUploadIFrameToLoad(){return new Promise((_0x27f55d,_0x5e5696)=>{var _0xceb5b8=a0_0x3379;window[_0xceb5b8(0xb9)](_0xceb5b8(0xb4),_0x11590d,![]);function _0x11590d(_0xbb528d){var _0x51fb2b=_0xceb5b8,_0x2bcdcc=_0xbb528d[_0x51fb2b(0xa1)];console['log'](_0x51fb2b(0x7d),_0x2bcdcc),(_0x2bcdcc['pageLoaded']||_0x2bcdcc[_0x51fb2b(0x98)])&&(console[_0x51fb2b(0x9c)](_0x51fb2b(0x80)),window[_0x51fb2b(0x81)](_0x51fb2b(0xb4),_0x11590d,![]),_0x27f55d());}});}function waitForPromotedListingIFrameToLoad(){return new Promise((_0x31e9fa,_0x14bea2)=>{var _0x357865=a0_0x3379;window[_0x357865(0xb9)]('message',_0x2f4d4f,![]);function _0x2f4d4f(_0x33def6){var _0x21258e=_0x357865,_0x5d8153=_0x33def6['data'];_0x5d8153[_0x21258e(0x98)]&&(window[_0x21258e(0x81)](_0x21258e(0xb4),_0x2f4d4f,![]),_0x31e9fa());}});}function promoteListing(_0x110ca2){return new Promise((_0xc869bb,_0x407dd4)=>{var _0x1765a6=a0_0x3379,_0x238599=document['getElementById'](_0x1765a6(0x8d));_0x238599[_0x1765a6(0xc9)][_0x1765a6(0x87)]({'type':_0x1765a6(0x9a),'adRate':_0x110ca2},_0x238599['src']),window[_0x1765a6(0xb9)]('message',_0x249f21,![]);function _0x249f21(_0xf187d7){var _0x27af54=_0x1765a6,_0x56bf83=JSON[_0x27af54(0x7a)](_0xf187d7[_0x27af54(0xa1)]);_0x56bf83[_0x27af54(0xc8)]===_0x27af54(0xb1)&&(window[_0x27af54(0x81)](_0x27af54(0xb4),_0x249f21,![]),_0xc869bb());}});}async function listItem(){return new Promise((_0x1476bc,_0x424e60)=>{var _0x24bb4f=a0_0x3379,_0x3e74c3=document[_0x24bb4f(0xb0)](_0x24bb4f(0x9d)),_0x37bbb9=_0x3e74c3['querySelector'](_0x24bb4f(0xa5));_0x37bbb9[_0x24bb4f(0xa8)](),_0x37bbb9[_0x24bb4f(0xa8)](),_0x1476bc();});}function checkIfListingSucceeded(){return new Promise((_0xdccc6b,_0x1e9672)=>{var _0x1ee1b6=a0_0x3379,_0x36e92f=document[_0x1ee1b6(0xb0)]('success_msg');_0x36e92f['style']['visibility']!=='visible'&&_0xdccc6b(![]),_0x36e92f['style']['visibility']==='visible'&&_0xdccc6b(!![]);});}function waitSomeTime(_0x384de1){return new Promise((_0x1d0853,_0x3965c5)=>{setTimeout(()=>{_0x1d0853();},_0x384de1);});}function a0_0x3379(_0x5e958f,_0x21c141){var _0x12c4b1=a0_0x12c4();return a0_0x3379=function(_0x3379f1,_0x282af1){_0x3379f1=_0x3379f1-0x77;var _0x4956ff=_0x12c4b1[_0x3379f1];return _0x4956ff;},a0_0x3379(_0x5e958f,_0x21c141);}function tellChromeToCloseTab(){chrome['runtime']['sendMessage']({'type':'close_tab'},function(_0x4c813c){});}