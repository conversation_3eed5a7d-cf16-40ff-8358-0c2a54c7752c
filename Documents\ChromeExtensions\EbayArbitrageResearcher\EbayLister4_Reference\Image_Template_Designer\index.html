<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collage Creator</title>
    <script src="../libraries/fabric.min.js"></script>
    <script src="../libraries/image_utils.js"></script>
    <script src="../libraries/image_transform_utils.js"></script>

    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div id="editor" class="editor-container">
        <div id="canvasWrapper">
            <div id="canvasContainer">
                <canvas id="c" width="1500" height="1500"></canvas>
            </div>
        </div>


        <div id="controls">

            <div class="control-section">
                <h3>Template Creation</h3>
                <label for="opacityInput">Transparency:</label>
                <!-- <input type="number" id="opacityInput" value="1" /> -->
                <input type="range" id="opacitySlider" min="0" max="1" step="0.01" value="1">

                <button id="addRect">Add Placeholder</button>
                <button id="sendToBack">Send to Back</button>



                <button id="clearCanvas">Clear Canvas</button>
                <button id="saveTemplate">Save Template</button>
                <button id="loadTemplate">Load Template</button>
            </div>

            <div class="control-section">
                <h3>Add Images</h3>
                <input type="file" id="uploadBadge" multiple />
                <button id="fetchBadgeFromUrl">Add Image from URL</button>
                <input type="text" id="badgeUrl" placeholder="Image URL" />
            </div>

            <div class="control-section">
                <h3>Banner</h3>
                <label for="bannerText">Text:</label>
                <input type="text" id="bannerText" placeholder="Enter banner text" />

                <!-- Predefined Options Dropdown -->
                <label for="predefinedBannerText">Or Choose Predefined Text:</label>
                <select id="predefinedBannerText">
                    <option value="">Select an Option</option>
                    <option value="Best Seller">Best Seller</option>
                    <option value="{{productTitle}}">{{productTitle}}</option>
                    <option value="{{mainBenefit}}">{{mainBenefit}}</option>
                    <!-- Add more predefined options here -->
                </select>


                <label for="bannerColor">Color:</label>
                <input type="color" id="bannerColor" value="#FF0000" />
                <label for="bannerFont">Font Style:</label>
                <select id="bannerFont">
                    <option value="Arial">Arial</option>
                    <option value="'Amazon Ember', sans-serif" selected>Amazon Ember</option>


                    //add more unique fonts here
                    <option value="Impact">Impact</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Verdana">Verdana</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Comic Sans MS">Comic Sans MS</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Lucida Sans Unicode">Lucida Sans Unicode</option>
                    <option value="Tahoma">Tahoma</option>
                    <option value="Trebuchet MS">Trebuchet MS</option>
                    <option value="Arial Black">Arial Black</option>
                    <option value="Garamond">Garamond</option>
                    <option value="Palatino Linotype">Palatino Linotype</option>

                    //script fonts
                    <option value="Brush Script MT">Brush Script MT</option>
                    <option value="Lucida Handwriting">Lucida Handwriting</option>
                    <option value="Monotype Corsiva">Monotype Corsiva</option>

                    //serif fonts
                    <option value="Book Antiqua">Book Antiqua</option>
                    <option value="Bookman Old Style">Bookman Old Style</option>
                    <option value="Goudy Old Style">Goudy Old Style</option>
                    <option value="MS Serif">MS Serif</option>
                    <option value="Palatino Linotype">Palatino Linotype</option>

                    //bold serif fonts
                    <option value="Baskerville Old Face">Baskerville Old Face</option>
                    <option value="Calisto MT">Calisto MT</option>
                    <option value="Century">Century</option>
                    <option value="Century Gothic">Century Gothic</option>

                </select>

                <label for="bannerFontColor">Font Color:</label>
                <select name="bannerFontColor" id="bannerFontColor">
                    <option value="black">Black</option>
                    <option value="white" selected>White</option>
                </select>


                <label for="bannerPadding">Padding:</label>
                <input type="number" id="bannerPadding" value="10" />
                <button id="addBanner">Add Banner</button>
                <button id="createRibbon">Create Best Seller Ribbon</button>

            </div>

            <div class="control-section">
                <input type="color" id="canvasBorderColor" value="#ff0000"> <!-- Default red -->
                <input type="number" id="canvasBorderThickness" value="30"> <!-- Default thickness 5px -->
                <button id="addBorder">Add Border to Canvas</button>

            </div>

            <div class="control-section">
                <h3>Testing and Export</h3>
                <button id="testTemplate">Test Template</button>
                <button id="exportTemplate">Export Template</button>
                <input type="file" id="importTemplate" style="display: none;" />
                <button id="btnImportTemplate">Import Template</button>
            </div>

            <button id="delete" class="danger">Delete</button>
        </div>

        <div id="tester" hidden>
            <div id="testCanvasContainer">
                <canvas id="testCanvas" width="1500" height="1500"></canvas>
            </div>
            <button id="backToEditor">Back to Editor</button>
            <button id="downloadImage">Download Image</button>
        </div>

        <script src="../libraries/chat_gpt_web.js"></script>
        <script src="functions.js"></script>
        <script src="index.js"></script>
</body>

</html>