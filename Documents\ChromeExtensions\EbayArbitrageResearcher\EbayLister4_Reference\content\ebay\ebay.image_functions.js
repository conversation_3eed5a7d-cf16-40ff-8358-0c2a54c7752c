(function(_0x42e0f5,_0x27278c){var _0x4db99e=a0_0x5b94,_0x2551e0=_0x42e0f5();while(!![]){try{var _0x2b508d=parseInt(_0x4db99e(0xb8))/0x1*(parseInt(_0x4db99e(0xd0))/0x2)+parseInt(_0x4db99e(0xbb))/0x3*(-parseInt(_0x4db99e(0xa7))/0x4)+parseInt(_0x4db99e(0x93))/0x5+parseInt(_0x4db99e(0xc2))/0x6*(-parseInt(_0x4db99e(0xaf))/0x7)+-parseInt(_0x4db99e(0xc9))/0x8+parseInt(_0x4db99e(0x92))/0x9*(-parseInt(_0x4db99e(0xa5))/0xa)+parseInt(_0x4db99e(0xb7))/0xb;if(_0x2b508d===_0x27278c)break;else _0x2551e0['push'](_0x2551e0['shift']());}catch(_0x1d6c1d){_0x2551e0['push'](_0x2551e0['shift']());}}}(a0_0x566b,0x7d9ed));var imgCounter=0x0,imgCheckMarks='';function convertImgToBase64(_0x33fdc9,_0x81f071,_0x46d7f2){var _0x32b01d=a0_0x5b94,_0x2ec559=document['createElement'](_0x32b01d(0x97)),_0x2c4fac=_0x2ec559['getContext']('2d'),_0x4b9e58=new Image();_0x4b9e58[_0x32b01d(0xc6)]=_0x32b01d(0xa0),_0x4b9e58[_0x32b01d(0xca)]=function(){var _0x250098=_0x32b01d;_0x2ec559['height']=_0x4b9e58[_0x250098(0xbe)],_0x2ec559[_0x250098(0xcb)]=_0x4b9e58[_0x250098(0xcb)],_0x2c4fac[_0x250098(0x8f)](_0x4b9e58,0x0,0x0);var _0x4821b3=_0x2ec559['toDataURL'](_0x46d7f2||'image/png');_0x81f071[_0x250098(0xa6)](this,_0x4821b3),_0x2ec559=null;},_0x4b9e58[_0x32b01d(0xb1)]=_0x33fdc9;}function get_ebay_tab_id(){return new Promise((_0x4798e3,_0x3f5ecc)=>{var _0x551631=a0_0x5b94;chrome[_0x551631(0xd2)][_0x551631(0xa4)][_0x551631(0xb5)](_0x551631(0xc1),_0xfa78b9=>{var _0x390be8=_0x551631;_0x4798e3(_0xfa78b9[_0x390be8(0xc1)]);});});}async function uploadImages(_0x39d676){var _0x23da00=a0_0x5b94,_0x158d9b=await get_ebay_tab_id();console['log'](_0x23da00(0xb3),_0x158d9b);var _0x24f86d=_0x39d676[_0x23da00(0x9c)],_0x57beeb=_0x39d676[_0x23da00(0xc3)],_0x570f08=_0x39d676['extra'][_0x23da00(0x9f)],_0x48601e=[];_0x48601e=_0x57beeb;_0x48601e[_0x23da00(0xd3)]<0x1&&(_0x48601e=_0x24f86d);if(_0x48601e[_0x23da00(0xd3)]<0xc){var _0x4e5717=0xc-_0x48601e[_0x23da00(0xd3)];for(var _0x4998b2=0x0;_0x4998b2<_0x24f86d[_0x23da00(0xd3)];_0x4998b2++){var _0x440520=_0x24f86d[_0x4998b2];_0x4998b2<_0x4e5717&&_0x4e5717>0x0&&_0x48601e[_0x23da00(0xba)](_0x440520);}}_0x48601e[_0x23da00(0xd3)]===0x1&&_0x48601e[_0x23da00(0xba)](_0x48601e[0x0]);var _0xa51a66=_0x39d676[_0x23da00(0xac)];console['log'](_0x23da00(0xae)),_0xa51a66=insensitiveReplaceAll(_0xa51a66,_0x39d676['brand'],''),_0xa51a66=insensitiveReplaceAll(_0xa51a66,_0x23da00(0xcd),''),uploadImageCounter(),initializeImageCounter(_0x48601e[_0x23da00(0xd3)]),await uploadMultiImage(_0x158d9b,_0xa51a66,_0x48601e,_0x570f08),await uploadFirstImage(_0x158d9b,_0xa51a66,_0x48601e[0x0]);for(var _0x44d9a7=0x0;_0x44d9a7<_0x48601e[_0x23da00(0xd3)];_0x44d9a7++){var _0x4beb39=_0x48601e[_0x44d9a7];console[_0x23da00(0xc8)]('#'+_0x44d9a7+_0x23da00(0xa2)+_0x4beb39),_0x44d9a7<0xa&&await uploadNormalImage(_0x158d9b,_0xa51a66,_0x4beb39);}updateLoaderMessage(),console[_0x23da00(0xc8)]('\x0a\x0a\x0a-------------------------[Ending\x20image\x20Post]-----------------------\x0a\x0a\x0a');}function uploadFirstImage(_0x32c9a3,_0x2af57e,_0x4f60ef){return updateImageCounter(),turnOnLoader(),new Promise((_0x311984,_0x1783c5)=>{var _0x43efc4=a0_0x5b94;chrome[_0x43efc4(0xaa)][_0x43efc4(0x90)]({'from':_0x43efc4(0x9d),'type':_0x43efc4(0x94),'payload':{'name':_0x2af57e,'imgUrl':_0x4f60ef,'imgVariation':_0x43efc4(0xcc),'ebayTab':_0x32c9a3}},function(_0x167475){var _0x35b6e9=_0x43efc4;console['log'](_0x35b6e9(0xa8),_0x167475),updateImgCheckMark(),turnOffLoader(),_0x311984(_0x167475);});});}function a0_0x5b94(_0x366072,_0x2cda57){var _0x566b41=a0_0x566b();return a0_0x5b94=function(_0x5b9447,_0x967da2){_0x5b9447=_0x5b9447-0x8f;var _0x2ba17b=_0x566b41[_0x5b9447];return _0x2ba17b;},a0_0x5b94(_0x366072,_0x2cda57);}function a0_0x566b(){var _0x293b09=['image-started','local','10zToxyl','call','13556wHModQ','response','<span\x20id=\x27image_loader_message\x27>Please\x20be\x20Patient...\x20Uploading\x20Image\x20\x20</span>\x20\x20-\x20\x20<span\x20id=\x27my-upload-count\x27>0</span>/<span\x20id=\x27total-upload-images\x27>0</span><br><span\x20id=\x27image-started\x27></span>','runtime','style','custom_title','6px\x20solid\x20#f3f3f3','\x0a\x0a\x0a-------------------------[Starting\x20image\x20Post]-----------------------\x0a\x0a\x0a','1195572FKnyNA','50%','src','loader','ebayTab:','getElementById','get','animation','12194842WBnwUi','19HDfwXu','total-upload-images','push','33wtsHBW','innerText','fontStyle','height','borderRadius','display','ebayTab','18hwQdcT','main_hd_images','createElement','div','crossOrigin','borderTop','log','260936IwNjLa','onload','width','first_image','amazon','block','upload_normal_img','30994rafZYA','innerHTML','storage','length','drawImage','sendMessage','border','9188163tgYStN','3573335KnoeEl','upload_first_img','italic','appendChild','CANVAS','lister_image_upload','12px','image_loader_message','my-upload-count','main_sd_images','ebay_draft','upload_multi_img','color','Anonymous','other_image','\x20Uploading\x20Image:\x20'];a0_0x566b=function(){return _0x293b09;};return a0_0x566b();}function uploadNormalImage(_0x5578ea,_0x11c03b,_0x6c0b8d){return updateImageCounter(),turnOnLoader(),new Promise((_0x25afa7,_0x491d39)=>{var _0x20f682=a0_0x5b94;chrome[_0x20f682(0xaa)][_0x20f682(0x90)]({'from':_0x20f682(0x9d),'type':_0x20f682(0xcf),'payload':{'name':_0x11c03b,'imgUrl':_0x6c0b8d,'imgVariation':_0x20f682(0xa1),'ebayTab':_0x5578ea}},function(_0x34e580){var _0x281ad6=_0x20f682;console[_0x281ad6(0xc8)](_0x281ad6(0xa8),_0x34e580),updateImgCheckMark(),turnOffLoader(),_0x25afa7(_0x34e580);});});}function uploadMultiImage(_0x7d4227,_0x29a46d,_0x5225e2,_0x2254d4){return updateImageCounter(),turnOnLoader(),new Promise((_0x764c83,_0x5e183a)=>{var _0x497543=a0_0x5b94;chrome[_0x497543(0xaa)][_0x497543(0x90)]({'from':'ebay_draft','type':_0x497543(0x9e),'ebayTab':_0x7d4227,'payload':{'name':_0x29a46d,'imgUrl':{'mainImage':_0x5225e2[0x0],'sideImage':_0x5225e2[0x1]},'color':_0x2254d4,'imgVariation':'multi_image','ebayTab':_0x7d4227}},function(_0x3c9d2f){var _0x505791=_0x497543;console['log'](_0x505791(0xa8),_0x3c9d2f),updateImgCheckMark(),turnOffLoader(),_0x764c83(_0x3c9d2f);});});}function turnOnLoader(){var _0x47fa3a=a0_0x5b94;document[_0x47fa3a(0xb4)](_0x47fa3a(0xb2))['style'][_0x47fa3a(0xc0)]=_0x47fa3a(0xce);}function turnOffLoader(){var _0x3d0fad=a0_0x5b94;document[_0x3d0fad(0xb4)](_0x3d0fad(0xb2))[_0x3d0fad(0xab)]['display']='none';}function updateImageCounter(){var _0x14b6b8=a0_0x5b94;imgCounter=imgCounter+0x1,document[_0x14b6b8(0xb4)](_0x14b6b8(0x9b))[_0x14b6b8(0xbc)]=imgCounter;}function updateImgCheckMark(){var _0x3ee16e=a0_0x5b94;imgCheckMarks=imgCheckMarks+'✅',document[_0x3ee16e(0xb4)](_0x3ee16e(0xa3))[_0x3ee16e(0xbc)]=imgCheckMarks;}function initializeImageCounter(_0x1022be){var _0x35d840=a0_0x5b94;_0x1022be=_0x1022be+0x2,_0x1022be>0xc&&(_0x1022be=0xc),document[_0x35d840(0xb4)](_0x35d840(0xb9))['innerHTML']=_0x1022be;}function uploadImageCounter(){var _0x1fc522=a0_0x5b94,_0x381e18=document[_0x1fc522(0xb4)]('editpaneSect_VIN'),_0x5b65af=document[_0x1fc522(0xc4)](_0x1fc522(0xc5));_0x5b65af[_0x1fc522(0xd1)]=_0x1fc522(0xa9),_0x5b65af['id']=_0x1fc522(0x98),_0x5b65af['style']['border']='thick\x20solid\x20rgb(172,\x20235,\x20100)',_0x5b65af[_0x1fc522(0xab)][_0x1fc522(0xbd)]=_0x1fc522(0x95);var _0x1da8e1=document['createElement'](_0x1fc522(0xc5));_0x1da8e1['id']=_0x1fc522(0xb2),_0x1da8e1[_0x1fc522(0xab)][_0x1fc522(0x91)]=_0x1fc522(0xad),_0x1da8e1[_0x1fc522(0xab)][_0x1fc522(0xc7)]='6px\x20solid\x20#3498db',_0x1da8e1['style'][_0x1fc522(0xbf)]=_0x1fc522(0xb0),_0x1da8e1['style']['width']=_0x1fc522(0x99),_0x1da8e1[_0x1fc522(0xab)][_0x1fc522(0xbe)]=_0x1fc522(0x99),_0x1da8e1[_0x1fc522(0xab)][_0x1fc522(0xb6)]='spin\x202s\x20linear\x20infinite',_0x1da8e1[_0x1fc522(0xab)]['display']=_0x1fc522(0xce),_0x5b65af[_0x1fc522(0x96)](_0x1da8e1),_0x381e18['append'](_0x5b65af);}function updateLoaderMessage(){var _0x7681cd=a0_0x5b94,_0x5ac3e0='Completed',_0x45ec2f=document['getElementById'](_0x7681cd(0x9a));_0x45ec2f[_0x7681cd(0xbc)]=_0x5ac3e0;}