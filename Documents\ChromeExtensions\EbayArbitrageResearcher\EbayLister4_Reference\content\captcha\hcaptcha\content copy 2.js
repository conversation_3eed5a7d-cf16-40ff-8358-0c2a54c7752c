function a0_0x5418(_0x35be4b,_0x1074d7){const _0x8426b3=a0_0x8426();return a0_0x5418=function(_0x54188d,_0x3e6c56){_0x54188d=_0x54188d-0x136;let _0x58b781=_0x8426b3[_0x54188d];return _0x58b781;},a0_0x5418(_0x35be4b,_0x1074d7);}(function(_0x37e2dc,_0x52f932){const _0x1eae49=a0_0x5418,_0x49f037=_0x37e2dc();while(!![]){try{const _0x46bfa4=parseInt(_0x1eae49(0x19f))/0x1*(-parseInt(_0x1eae49(0x1b4))/0x2)+-parseInt(_0x1eae49(0x152))/0x3*(-parseInt(_0x1eae49(0x194))/0x4)+parseInt(_0x1eae49(0x19b))/0x5*(parseInt(_0x1eae49(0x180))/0x6)+-parseInt(_0x1eae49(0x142))/0x7*(parseInt(_0x1eae49(0x18e))/0x8)+parseInt(_0x1eae49(0x156))/0x9*(-parseInt(_0x1eae49(0x173))/0xa)+parseInt(_0x1eae49(0x1a4))/0xb*(-parseInt(_0x1eae49(0x166))/0xc)+-parseInt(_0x1eae49(0x15a))/0xd*(-parseInt(_0x1eae49(0x1b9))/0xe);if(_0x46bfa4===_0x52f932)break;else _0x49f037['push'](_0x49f037['shift']());}catch(_0x116034){_0x49f037['push'](_0x49f037['shift']());}}}(a0_0x8426,0xd2fd7),((async()=>{const _0x23e04b=a0_0x5418;var _0x260843=await getNoCaptchaApiKey();let _0x4d916d={'logsEnabled':_0x23e04b(0x175),'APIKEY':_0x260843,'extensionEnabled':_0x23e04b(0x175),'hCaptchaEnabled':'true','PLANTYPE':_0x23e04b(0x154),'hCaptchaAutoOpen':_0x23e04b(0x175),'hCaptchaAutoSolve':_0x23e04b(0x175),'hCaptchaAlwaysSolve':_0x23e04b(0x16a),'debugMode':_0x23e04b(0x16a),'debugModeGridOnly':_0x23e04b(0x16a),'englishLanguage':_0x23e04b(0x175),'hCaptchaMultiSolveTime':0xa,'hCaptchaBoundingBoxSolveTime':0xa,'hCaptchaGridSolveTime':0xa,'customEndpoint':_0x23e04b(0x168)},_0x7d7ee1=_0x4d916d[_0x23e04b(0x1bf)]===_0x23e04b(0x175)?!![]:![];function _0x52010e(..._0x5d47ab){_0x7d7ee1&&console['log'](..._0x5d47ab);}if(!_0x4d916d[_0x23e04b(0x161)])return;function _0xfa81d5(){const _0x4092c0=_0x23e04b;return document[_0x4092c0(0x179)](_0x4092c0(0x170))!==null;}function _0x153ec0(){const _0x2434f1=_0x23e04b;return document[_0x2434f1(0x195)]('.task-image\x20.image')?.[_0x2434f1(0x192)]===0x9;}function _0xc9f41f(){const _0x3da9e0=_0x23e04b;return document[_0x3da9e0(0x179)](_0x3da9e0(0x169))!==null;}chrome[_0x23e04b(0x1b1)][_0x23e04b(0x1af)][_0x23e04b(0x16d)](function(_0xf524ef,_0x515b56,_0x5e36bc){const _0x55d612=_0x23e04b;if(_0xf524ef[_0x55d612(0x1a0)]===_0x55d612(0x1bb)){const _0x4723ef=[...document[_0x55d612(0x195)](_0x55d612(0x1bc))];if(_0x4723ef)for(const _0x1663a4 of _0x4723ef){const _0x517b50=_0x1663a4['src'];_0x1663a4['src']=_0x55d612(0x164),setTimeout(function(){const _0x65b035=_0x55d612;_0x1663a4[_0x65b035(0x1b8)]=_0x517b50;},0x64);}Array[_0x55d612(0x1c0)](document[_0x55d612(0x1aa)](_0x55d612(0x1c8)))['forEach'](_0x47f89f=>{const _0x2ce613=_0x55d612;_0x47f89f[_0x2ce613(0x1b8)]=_0x47f89f[_0x2ce613(0x1b8)];});}}),((async()=>{const _0x52a29e=_0x23e04b;if(_0x4d916d[_0x52a29e(0x191)]===_0x52a29e(0x16a))return;if(_0x4d916d[_0x52a29e(0x171)]==='false')return;const _0x599675=_0x517dcc=>document[_0x52a29e(0x179)](_0x517dcc),_0x4efdc7=_0x3662f6=>document[_0x52a29e(0x195)](_0x3662f6);function _0x1664f5(){const _0x53c69c=_0x52a29e,_0x3dd876=document[_0x53c69c(0x17b)][_0x53c69c(0x16c)]();if(_0x3dd876?.[_0x53c69c(0x15e)]===0x0||_0x3dd876?.[_0x53c69c(0x139)]===0x0)return![];return document[_0x53c69c(0x179)]('div.check')!==null;}function _0x29092d(_0x209ad1){return new Promise(_0x4c4f32=>setTimeout(_0x4c4f32,_0x209ad1));}function _0x310525(_0x1a239a,_0x50f60a){const _0x36b752=_0x52a29e;return Math[_0x36b752(0x155)](Math[_0x36b752(0x146)]()*(_0x50f60a-_0x1a239a)+_0x1a239a);}function _0xaadddd(){const _0x5eec26=_0x52a29e;try{const _0x372d18=document[_0x5eec26(0x179)](_0x5eec26(0x149));return _0x372d18&&_0x372d18[_0x5eec26(0x147)][_0x5eec26(0x141)]==='block';}catch(_0x1361dc){return console[_0x5eec26(0x13d)](_0x1361dc),![];}}const _0x27ed4f=()=>{const _0x551d07=_0x52a29e;return!navigator[_0x551d07(0x14b)]||_0x255f54||_0x4d916d['PLANTYPE']==null||_0x4d916d['APIKEY']===undefined||_0x4d916d[_0x551d07(0x161)]==='';};async function _0x3ed141(_0x4faf95){const _0x22b559=_0x52a29e,_0x392a05=await(await fetch(_0x4faf95))[_0x22b559(0x1c2)]();return new Promise(function(_0x1c78dd){const _0x326d96=_0x22b559,_0x13ee8f=new FileReader();_0x13ee8f[_0x326d96(0x196)](_0x392a05),_0x13ee8f[_0x326d96(0x1ae)]('loadend',function(){const _0x127e36=_0x326d96;_0x1c78dd(_0x13ee8f[_0x127e36(0x1a5)][_0x127e36(0x198)](/^data:image\/(png|jpeg);base64,/,''));}),_0x13ee8f[_0x326d96(0x1ae)](_0x326d96(0x182),function(){const _0x49850a=_0x326d96;_0x52010e(_0x49850a(0x151));});});}let _0x18c78e='';const _0x11e6e4=new URLSearchParams(location[_0x52a29e(0x1a8)]);let _0x490821,_0x255f54=![];const _0x3c73e9={'Content-Type':_0x52a29e(0x13c),'apikey':_0x4d916d['APIKEY']},_0x3478ab=_0x4d916d['logsEnabled']===_0x52a29e(0x175);while(!_0x27ed4f()){await _0x29092d(0x3e8);if(_0x4d916d['hCaptchaAutoOpen']===_0x52a29e(0x175)&&_0x1664f5()){if(_0xaadddd()){_0x52010e(_0x52a29e(0x1a1));if(_0x4d916d['hCaptchaAlwaysSolve']==='false')break;}await _0x29092d(0x3e8),_0x599675(_0x52a29e(0x13a))?.[_0x52a29e(0x187)]();}else _0x4d916d['hCaptchaAutoSolve']===_0x52a29e(0x175)&&_0x599675(_0x52a29e(0x199))!==null&&(_0x52010e('opening\x20box'),await _0x29092d(0x3e8),await _0x2665b6());}async function _0x511911(){const _0x4eaaa7=_0x52a29e;if(_0xc9f41f())await _0x29092d(0xc8),document[_0x4eaaa7(0x179)](_0x4eaaa7(0x174))[_0x4eaaa7(0x187)](),await _0x29092d(0x64),document[_0x4eaaa7(0x179)]('.language-selector\x20.option:nth-child(23)')[_0x4eaaa7(0x187)]();else{if(_0x153ec0())await _0x29092d(0x64),document['querySelector'](_0x4eaaa7(0x14e))[_0x4eaaa7(0x187)]();else _0xfa81d5()&&(document[_0x4eaaa7(0x179)]('.display-language.button')[_0x4eaaa7(0x187)](),await _0x29092d(0xc8),document[_0x4eaaa7(0x179)]('.language-selector\x20.option:nth-child(23)')[_0x4eaaa7(0x187)](),await _0x29092d(0xc8));}}function _0xe63f91(){const _0x82ca47=_0x52a29e;document[_0x82ca47(0x179)](_0x82ca47(0x144))[_0x82ca47(0x187)]();}async function _0x2665b6(){const _0x1caf03=_0x52a29e;_0x490821=new Date();let _0xfd25ad=[];await _0x29092d(0x1f4);if(_0x4d916d[_0x1caf03(0x186)]==_0x1caf03(0x175)&&!_0xc9f41f()){document['querySelector']('.button-submit')[_0x1caf03(0x187)]();return;}if(_0x4d916d['debugModeGridOnly']==_0x1caf03(0x175)&&!_0x153ec0()){document['querySelector'](_0x1caf03(0x144))[_0x1caf03(0x187)]();return;}_0x4d916d[_0x1caf03(0x1c6)]===_0x1caf03(0x175)&&(document['documentElement'][_0x1caf03(0x148)]||navigator['language'])!=='en'&&await _0x511911();_0x52010e(_0xfd25ad);if(!_0xfd25ad!=[])return;const {target:_0x522a7c,cells:_0x3df0f3,images:_0x1b6e0f,example:_0x5cdb0b,examples:_0x2dc7dc,choices:_0x36eb41}=await _0x373afe();if(!_0x4d916d[_0x1caf03(0x13e)]===_0x1caf03(0x175))return;const _0x2b0d49=new URLSearchParams(location[_0x1caf03(0x1a8)]),_0x4eccae=_0xfa81d5()?_0x1caf03(0x1a2):_0xc9f41f()?'bbox':'grid';if(_0x3478ab)console[_0x1caf03(0x13d)](_0x4eccae,_0x522a7c,_0x3df0f3,_0x1b6e0f,_0x5cdb0b,_0x36eb41);try{_0xfd25ad=_0x1b6e0f,_0x52010e(_0xfd25ad);let _0x5c75ea=await fetch(await _0x4975d1('solve'),{'method':_0x1caf03(0x153),'headers':_0x3c73e9,'body':JSON[_0x1caf03(0x18c)]({'images':_0x1b6e0f,'target':_0x522a7c,'examples':_0x2dc7dc,'method':_0x1caf03(0x176),'type':_0x4eccae,'choices':_0xfa81d5()?_0x36eb41:[],'sitekey':_0x2b0d49[_0x1caf03(0x1ac)](_0x1caf03(0x163)),'site':_0x2b0d49[_0x1caf03(0x1ac)](_0x1caf03(0x145)),'ln':document['documentElement']['lang']||navigator['language']})});_0x5c75ea=await _0x5c75ea['json']();if(_0x5c75ea[_0x1caf03(0x182)]){_0x5bf4b3(_0x5c75ea[_0x1caf03(0x17a)]);return;}const _0x209da0=_0x5c75ea['answer'],_0x49234c=_0x5c75ea['message'],_0x1b6723=_0x5c75ea['status'],_0x4113ef=_0x5c75ea[_0x1caf03(0x193)],_0x1045a9=_0x310525(0xfa,0x15e);_0x52010e(_0x209da0,_0x49234c,_0x1b6723,_0x4113ef,_0x1045a9);let _0x10b413=0x0;if(_0x5c75ea[_0x1caf03(0x182)])_0x52010e(_0x49234c),_0x5bf4b3('⚠'+_0x49234c);else{if(_0x1b6723===_0x1caf03(0x167))_0x52010e(_0x49234c),_0x5bf4b3('⚠'+'\x20'+_0x49234c);else{if(_0x1b6723===_0x1caf03(0x184)){if(_0xfa81d5()){const _0x4d856b=_0x5c75ea[_0x1caf03(0x14f)];console[_0x1caf03(0x13d)](_0x1caf03(0x1a2),_0x4d856b);if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);await _0xb4b529(_0x4d856b),_0x10b413=_0x10b413+0x1;}else{if(_0x153ec0()){const _0x59afb6=await(await fetch(_0x4113ef))[_0x1caf03(0x1a3)]();if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);for(const _0x51cc98 of _0x59afb6['solution']){_0x3df0f3[_0x51cc98][_0x1caf03(0x187)](),await _0x29092d(_0x1045a9);}}else{if(_0xc9f41f()){const _0x3b2663=await(await fetch(_0x4113ef))[_0x1caf03(0x1a3)](),_0x30d65b=_0x3b2663['answer'];if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);if(!_0x30d65b)return;_0x30d65b?.[_0x1caf03(0x192)]===0x2&&(await _0x9b2822(_0x30d65b),_0x10b413=_0x10b413+0x1);}}}}else{if(_0x1b6723===_0x1caf03(0x150)){if(_0xfa81d5()){const _0x3a9932=_0x5c75ea[_0x1caf03(0x14f)];_0x52010e('multi',_0x3a9932);if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);await _0xb4b529(_0x3a9932),_0x10b413=_0x10b413+0x1,_0x52010e(_0x1caf03(0x18d),_0x10b413);}else{if(_0x153ec0()){const _0x1aadc9=_0x30b694(_0x5c75ea?.['solution']);if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);for(const _0x43a264 of _0x1aadc9){_0x3df0f3[_0x43a264][_0x1caf03(0x187)](),await _0x29092d(_0x1045a9);}}else{if(_0xc9f41f()){const _0x478561=await(await fetch(_0x4113ef))[_0x1caf03(0x1a3)](),_0x4c44ff=_0x478561[_0x1caf03(0x14f)];if(!_0x4c44ff)return;if(_0x49234c)_0x5bf4b3('⚠'+'\x20'+_0x49234c);_0x4c44ff?.[_0x1caf03(0x192)]===0x2&&(await _0x9b2822(_0x4c44ff),_0x10b413=_0x10b413+0x1);}}}}else _0x1b6723==='falied'&&(_0x52010e(_0x49234c),_0x5bf4b3('⚠'+_0x49234c));}}}const _0x246a50=new Date()-_0x490821,_0xdaa305=_0xfa81d5()?_0x4d916d[_0x1caf03(0x188)]*0x3e8-_0x246a50:_0xc9f41f()?_0x4d916d[_0x1caf03(0x1c4)]*0x3e8-_0x246a50:_0x4d916d[_0x1caf03(0x138)]*0x3e8-_0x246a50;_0xdaa305<0x0&&await _0x29092d(0x12c),await _0x29092d(_0xdaa305),document['querySelector'](_0x1caf03(0x144))[_0x1caf03(0x187)](),_0x490821=0x0,_0xfd25ad=[];}catch(_0x507dde){if(_0x507dde instanceof TypeError&&_0x507dde[_0x1caf03(0x17a)]===_0x1caf03(0x18f)){_0x5bf4b3(_0x1caf03(0x1c5),0x1388,![]);throw new Error(_0x1caf03(0x1c5));}else _0x5bf4b3('✘'+_0x507dde,0x1388,![]);}}function _0x30b694(_0x535c54){const _0x55e249=_0x52a29e;for(let _0x19d1df=_0x535c54[_0x55e249(0x192)]-0x1;_0x19d1df>0x0;_0x19d1df--){const _0x5d85f3=Math[_0x55e249(0x155)](Math[_0x55e249(0x146)]()*(_0x19d1df+0x1));[_0x535c54[_0x19d1df],_0x535c54[_0x5d85f3]]=[_0x535c54[_0x5d85f3],_0x535c54[_0x19d1df]];}return _0x535c54;}function _0x2fedd0(_0x331064){const _0x3241d8=_0x52a29e;[_0x3241d8(0x177),_0x3241d8(0x1c7),_0x3241d8(0x1a9),'click'][_0x3241d8(0x178)](_0xbaa175=>{const _0x4d2895=_0x3241d8;if(_0x331064[_0x4d2895(0x19c)])_0x331064[_0x4d2895(0x19c)]('on'+_0xbaa175);else{const _0x1616c0=document[_0x4d2895(0x14d)](_0x4d2895(0x16e));_0x1616c0[_0x4d2895(0x1ba)](_0xbaa175,!![],![]),_0x331064[_0x4d2895(0x172)](_0x1616c0);}});}async function _0x9b2822(_0x347cb7){const _0x1c00cd=_0x52a29e;function _0x15ee16(_0x4b5449,_0x1de1a6,_0xd5acfb){const _0x15067b=a0_0x5418,_0x2f044d=_0x4b5449['getBoundingClientRect'](),_0x133345=[_0x15067b(0x177),_0x15067b(0x1c7),_0x15067b(0x1a9),_0x15067b(0x187)],_0x553a01={'clientX':_0x1de1a6+_0x2f044d[_0x15067b(0x183)],'clientY':_0xd5acfb+_0x2f044d[_0x15067b(0x181)],'bubbles':!![]};for(let _0xfe3ff0=0x0;_0xfe3ff0<_0x133345['length'];_0xfe3ff0++){const _0x4a6de9=new MouseEvent(_0x133345[_0xfe3ff0],_0x553a01);_0x4b5449[_0x15067b(0x172)](_0x4a6de9);}}const _0x17bbae=document['querySelector'](_0x1c00cd(0x165));_0x17bbae['addEventListener'](_0x1c00cd(0x1c7),function(_0x27ea88){const _0x2bae8e=_0x1c00cd,_0x35ef5f=_0x17bbae[_0x2bae8e(0x16c)](),_0x3799fe=event['clientX']-_0x35ef5f[_0x2bae8e(0x183)],_0x91db1f=event['clientY']-_0x35ef5f[_0x2bae8e(0x181)];_0x52010e(_0x2bae8e(0x19d)+_0x3799fe+_0x2bae8e(0x1b0)+_0x91db1f,_0x347cb7);});const [_0x374695,_0x287ee5]=_0x347cb7;_0x15ee16(_0x17bbae,_0x374695,_0x287ee5);}async function _0x4975d1(_0x154a9b){const _0x5281be=_0x52a29e;if(_0x4d916d[_0x5281be(0x15f)]===_0x5281be(0x1c1))return'https://'+_0x4d916d[_0x5281be(0x17f)]+'/'+_0x154a9b;return _0x5281be(0x13f)+_0x4d916d[_0x5281be(0x15f)]+_0x5281be(0x1be)+_0x154a9b;}async function _0xb4b529(_0x1d9e08){const _0xd40660=_0x52a29e;for(const _0x7d4acc of _0x1d9e08){const _0x5a2ce2=[...document[_0xd40660(0x195)](_0xd40660(0x19e))][_0xd40660(0x18b)](_0x5d4964=>_0x5d4964['outerText']===_0x7d4acc);_0x2fedd0(_0x5a2ce2),![...document['querySelectorAll'](_0xd40660(0x162))][_0xd40660(0x14c)](_0x44dece=>_0x44dece[_0xd40660(0x147)][_0xd40660(0x190)]===_0xd40660(0x13b))&&_0x2fedd0(_0x5a2ce2);}}async function _0x1806c6(){const _0xae435=_0x52a29e,_0x49cb4a=document['querySelector']('canvas');if(!_0x49cb4a)return null;const [_0x5ed3e5,_0x4afbb7]=[_0x49cb4a['width'],_0x49cb4a['height']],_0x864c21=document[_0xae435(0x15c)](_0xae435(0x165));_0x864c21[_0xae435(0x15e)]=_0x5ed3e5,_0x864c21[_0xae435(0x139)]=_0x4afbb7;const _0x5ef566=_0x864c21[_0xae435(0x137)]('2d',{'willReadFrequently':!![]});_0x5ef566[_0xae435(0x159)](_0x49cb4a,0x0,0x0);const _0x4ab184=_0x5ef566['getImageData'](0x0,0x0,_0x5ed3e5,_0x4afbb7),_0x1db65e=Array[_0xae435(0x1c0)](_0x4ab184[_0xae435(0x1b5)])[_0xae435(0x14a)]((_0x1febe5,_0x3ced2f)=>_0x3ced2f%0x4===0x3||_0x1febe5===0x0);if(_0x1db65e)return console[_0xae435(0x182)](_0xae435(0x1b3)),null;const _0x356861=parseInt(_0x49cb4a['style']['width'],0xa),_0x1e5a71=parseInt(_0x49cb4a[_0xae435(0x147)][_0xae435(0x139)],0xa);if(_0x356861<=0x0||_0x1e5a71<=0x0)return console['error'](_0xae435(0x1b2)),null;const _0x1d3fb5=Math[_0xae435(0x1a6)](_0x356861/_0x5ed3e5,_0x1e5a71/_0x4afbb7),[_0x39cf77,_0x5a8e77]=[_0x5ed3e5*_0x1d3fb5,_0x4afbb7*_0x1d3fb5],_0x3e9d7f=document[_0xae435(0x15c)]('canvas');Object[_0xae435(0x17c)](_0x3e9d7f,{'width':_0x39cf77,'height':_0x5a8e77});const _0x2a66f7=_0x3e9d7f[_0xae435(0x137)]('2d',{'willReadFrequently':![]});return _0x2a66f7['drawImage'](_0x49cb4a,0x0,0x0,_0x5ed3e5,_0x4afbb7,0x0,0x0,_0x39cf77,_0x5a8e77,{'willReadFrequently':![]}),_0x3e9d7f[_0xae435(0x189)](_0xae435(0x16f))[_0xae435(0x198)](/^data:image\/(png|jpeg);base64,/,'');}function _0x1c96fc(){const _0xc92744=_0x52a29e,_0x8ee0e5=document['querySelector'](_0xc92744(0x165));if(!_0x8ee0e5)return null;const _0x418d54=_0x8ee0e5[_0xc92744(0x189)](_0xc92744(0x16f),0.1);if(!/^data:image\/(png|jpeg);base64,([A-Za-z0-9+/=])+/[_0xc92744(0x160)](_0x418d54))return null;const _0x4443c3=_0x418d54['replace'](/^data:image\/(png|jpeg);base64,/,''),_0x247bf4=new Image();return _0x247bf4[_0xc92744(0x1b8)]='data:image/jpeg;base64,'+_0x4443c3,new Promise((_0x3d7a64,_0x210a9b)=>{const _0x5584b8=_0xc92744;_0x247bf4[_0x5584b8(0x17d)]=()=>{const _0x5cbcae=_0x5584b8;_0x247bf4[_0x5cbcae(0x15e)]>0x0&&_0x247bf4[_0x5cbcae(0x139)]>0x0?_0x3d7a64(_0x4443c3):_0x210a9b(new Error(_0x5cbcae(0x17e)));},_0x247bf4['onerror']=()=>_0x210a9b(new Error(_0x5584b8(0x140)));});}async function _0x3cb714(_0x39e735,_0x2da213){const _0x18f230=_0x52a29e,_0x5d9a0a=document[_0x18f230(0x179)](_0x18f230(0x1a7))[_0x18f230(0x147)]['background'];if(!_0x5d9a0a)return;let _0x11376f='';try{_0x11376f=await _0x3ed141(_0x5d9a0a[_0x18f230(0x143)](/url\("(.*)"/)?.['at'](0x1))||'';}catch(_0x41d444){_0x52010e(_0x41d444);}if(!_0x39e735)return;Object[_0x18f230(0x17c)](_0x2da213,{[Object[_0x18f230(0x1b7)](_0x2da213)[_0x18f230(0x192)]]:_0x11376f}),_0x52010e('images',_0x2da213);const _0x2bacb6=document[_0x18f230(0x195)]('.answer-text');choices=Array['from'](_0x2bacb6)[_0x18f230(0x185)](_0xa10c08=>_0xa10c08['outerText']);}async function _0x37c1f7(_0x22312a,_0x2c71a0){const _0x4fd8dc=_0x52a29e,_0x9be2ef=await _0x1806c6();if(!_0x9be2ef)return;_0x52010e('canvasImg',_0x9be2ef),Object[_0x4fd8dc(0x17c)](_0x22312a,{[Object['keys'](_0x22312a)[_0x4fd8dc(0x192)]]:_0x9be2ef});let _0xd5a817=document['querySelectorAll'](_0x4fd8dc(0x1ad));for(const _0x5d1fd5 of _0xd5a817){let _0x8055d9=_0x5d1fd5[_0x4fd8dc(0x147)][_0x4fd8dc(0x16b)][_0x4fd8dc(0x143)](/url\("([^"]+)"\)/);if(_0x8055d9)try{const _0x31ee0b=await _0x3ed141(_0x8055d9[0x1]);_0x2c71a0[_0x4fd8dc(0x15b)](_0x31ee0b);}catch(_0x4a5fa7){console[_0x4fd8dc(0x182)]('Error\x20converting\x20image\x20to\x20Base64:',_0x4a5fa7);}}_0x52010e(_0x4fd8dc(0x1ab),_0x22312a);}function _0x373afe(_0x38caf3=0x1f4){return new Promise(async _0x3356e3=>{const _0x3e6bb4=setInterval(async function(){const _0x34689a=a0_0x5418;let _0x3a5e28=document[_0x34689a(0x179)]('.prompt-text')?.[_0x34689a(0x157)];if(!_0x3a5e28)return;let _0x2f4b28=0x0,_0x133b17={},_0x317443={},_0x39e17b=[],_0xc47015=[];if(_0x153ec0()){_0x2f4b28=document[_0x34689a(0x195)](_0x34689a(0x1a7));if(_0x2f4b28[_0x34689a(0x192)]!==0x9)return;for(let _0x46e348=0x0;_0x46e348<_0x2f4b28['length'];_0x46e348++){const _0x185022=_0x2f4b28[_0x46e348];if(!_0x185022)return;const _0x58b888=_0x185022[_0x34689a(0x147)][_0x34689a(0x16b)][_0x34689a(0x143)](/url\("(.*)"/)?.['at'](0x1)||null;if(!_0x58b888||_0x58b888==='')return;_0x133b17[_0x46e348]=await _0x3ed141(_0x58b888);}}else{if(_0xfa81d5())await _0x3cb714(_0x317443,_0x133b17);else _0xc9f41f()&&await _0x37c1f7(_0x133b17,_0x39e17b);}return clearInterval(_0x3e6bb4),_0x3356e3({'target':_0x3a5e28,'cells':_0x2f4b28,'images':_0x133b17,'examples':_0x39e17b,'example':_0x317443,'choices':_0xc47015});},_0x38caf3);});}})());const _0x5bf4b3=(_0x377f25,_0x1ff205)=>{const _0x1ab54e=_0x23e04b,_0x45d942=document[_0x1ab54e(0x15c)](_0x1ab54e(0x15d));_0x45d942['style']['cssText']=_0x1ab54e(0x158),_0x45d942[_0x1ab54e(0x19a)]=_0x377f25,document[_0x1ab54e(0x17b)]['appendChild'](_0x45d942);const _0x2b8012=document[_0x1ab54e(0x15c)]('style');_0x2b8012[_0x1ab54e(0x19a)]=_0x1ab54e(0x1b6),document[_0x1ab54e(0x1bd)][_0x1ab54e(0x136)](_0x2b8012),setTimeout(()=>{const _0x1e5933=_0x1ab54e;_0x45d942[_0x1e5933(0x147)][_0x1e5933(0x197)]='slideOut\x201s\x20forwards',setTimeout(()=>{const _0xe2d291=_0x1e5933;document[_0xe2d291(0x17b)][_0xe2d291(0x1c3)](_0x45d942);},0x3e8);},_0x1ff205||0xbb8);};})()));function a0_0x8426(){const _0x38acc2=['#checkbox','rgb(116,\x20116,\x20116)','application/json','log','hCaptchaAutoSolve','https://','Invalid\x20image','display','9387119CHdpoD','match','.button-submit','host','random','style','lang','div.check','every','onLine','some','createEvent','.language-selector\x20.option:nth-child(23)','answer','solved','❌\x20Failed\x20to\x20convert\x20url\x20to\x20base64','3adZeRX','POST','pro','floor','175086JyZnlP','textContent','position:fixed;top:10%;left:0;background-color:rgba(0,0,0,.8);border-radius:4px;padding:16px;color:#fff;font:calc(14px\x20+\x20.5vw)\x20\x27Arial\x27,sans-serif;font-weight:bold;text-transform:uppercase;letter-spacing:1px;z-index:9999;transition:all\x201s;animation:slideIn\x201s\x20forwards','drawImage','26OZCARz','push','createElement','div','width','PLANTYPE','test','APIKEY','.answer-example','sitekey','about:blank','canvas','312fegOlB','skip','api.example.com','.bounding-box-example','false','background','getBoundingClientRect','addListener','MouseEvents','image/jpeg','.task-answers','hCaptchaEnabled','dispatchEvent','40OiBoxz','.display-language.button','true','hcaptcha_base64','mouseover','forEach','querySelector','message','body','assign','onload','Corrupted\x20image','customEndpoint','558708duqYXc','top','error','left','new','map','debugMode','click','hCaptchaMultiSolveTime','toDataURL','sendMessage','find','stringify','multi\x20hcap\x20~\x20clicks','8repRGT','Failed\x20to\x20fetch','backgroundColor','extensionEnabled','length','url','4581332nxepbU','querySelectorAll','readAsDataURL','animation','replace','h2.prompt-text','innerHTML','85DyngDl','fireEvent','x:\x20','.answer-text','12377Kvainu','action','found\x20solved','multi','json','553157uxGNtc','result','min','.task-image\x20.image','hash','mouseup','getElementsByTagName','images','get','.example-image\x20.image','addEventListener','onMessage','\x20y:\x20','runtime','Desired\x20width\x20and\x20height\x20should\x20be\x20positive\x20numbers','The\x20original\x20canvas\x20has\x20no\x20valid\x20content','166zylkih','data','@keyframes\x20slideIn{0%{transform:translateX(-100%)}100%{transform:translateX(0)}}@keyframes\x20slideOut{0%{transform:translateX(0)}100%{transform:translateX(100%)}}','keys','src','13226234XggWey','initEvent','refresh_iframes','[src*=newassets]','head','.nocaptchaai.com/','logsEnabled','from','custom','blob','removeChild','hCaptchaBoundingBoxSolveTime','✘\x20Failed\x20to\x20connect\x20to\x20api,\x20try\x20again\x20or\x20check\x20config','englishLanguage','mousedown','iframe','appendChild','getContext','hCaptchaGridSolveTime','height'];a0_0x8426=function(){return _0x38acc2;};return a0_0x8426();}function fetchFromBackground(_0x2eaa98,_0x17ac6b){return new Promise((_0x8dfdb4,_0x5545d9)=>{const _0x4201eb=a0_0x5418;chrome[_0x4201eb(0x1b1)][_0x4201eb(0x18a)]({'action':'fetch','url':_0x2eaa98,'options':_0x17ac6b},_0x1d5640=>{const _0x4f2f15=_0x4201eb;_0x1d5640&&_0x1d5640[_0x4f2f15(0x182)]?_0x5545d9(_0x1d5640[_0x4f2f15(0x182)]):_0x8dfdb4(_0x1d5640[_0x4f2f15(0x1b5)]);});});}