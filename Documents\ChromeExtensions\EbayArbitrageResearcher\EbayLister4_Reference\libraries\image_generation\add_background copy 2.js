(function(_0x29f985,_0x146284){const _0xe936b3=a0_0x34cf,_0x3a7bc5=_0x29f985();while(!![]){try{const _0x194bb4=parseInt(_0xe936b3(0x1d1))/0x1+-parseInt(_0xe936b3(0x1f1))/0x2*(parseInt(_0xe936b3(0x1c2))/0x3)+parseInt(_0xe936b3(0x211))/0x4*(parseInt(_0xe936b3(0x1fd))/0x5)+-parseInt(_0xe936b3(0x1cd))/0x6+-parseInt(_0xe936b3(0x20e))/0x7+parseInt(_0xe936b3(0x1a5))/0x8*(-parseInt(_0xe936b3(0x207))/0x9)+-parseInt(_0xe936b3(0x1f2))/0xa*(-parseInt(_0xe936b3(0x1e2))/0xb);if(_0x194bb4===_0x146284)break;else _0x3a7bc5['push'](_0x3a7bc5['shift']());}catch(_0x1cdb42){_0x3a7bc5['push'](_0x3a7bc5['shift']());}}}(a0_0x22dd,0x8e9d2));function createGenerateImageDiv(){const _0x5759b5=a0_0x34cf;var _0x2c8431=document[_0x5759b5(0x1d6)](_0x5759b5(0x1d2));_0x2c8431['id']='generate-image-div';var _0x1e222f=document[_0x5759b5(0x1d6)](_0x5759b5(0x1ec));_0x1e222f['id']=_0x5759b5(0x1f5),_0x1e222f[_0x5759b5(0x1d5)][_0x5759b5(0x1af)](_0x5759b5(0x1f5)),_0x1e222f[_0x5759b5(0x205)]=_0x5759b5(0x1ef),_0x1e222f[_0x5759b5(0x1f6)]=async function(){const _0x328758=_0x5759b5;try{_0x1e222f[_0x328758(0x205)]=_0x328758(0x20a),_0x1e222f[_0x328758(0x1d5)][_0x328758(0x1af)](_0x328758(0x214));var _0x10f78c=product_data[_0x328758(0x1c6)][0x0],_0x3f929c=await urlToImage(_0x10f78c);console[_0x328758(0x1cc)]('Url\x20image\x20done\x20\x20\x20'),_0x3f929c=await makeImageAspectSame(_0x3f929c),console['log'](_0x328758(0x1a0)),_0x3f929c=await resizeImageIfNeeded(_0x3f929c);var _0x4e2f87=_0x3f929c[_0x328758(0x1ae)];console[_0x328758(0x1cc)]('base64Image:\x20',_0x4e2f87);var _0x483807=await postToServer('http://localhost:5555/suggest-background',{'item_title':product_data['title']});console['log']('backgroundSuggestionResponse:\x20',_0x483807),_0x483807=JSON[_0x328758(0x213)](_0x483807);var _0x5af616=_0x483807[_0x328758(0x1f3)],_0x24a35b=document['getElementById'](_0x328758(0x1b8));if(_0x24a35b['value']==''){var _0x121cd3=_0x328758(0x202);_0x24a35b[_0x328758(0x1dd)]=_0x483807[_0x328758(0x212)]+_0x121cd3;}var _0x1e4ae3=document[_0x328758(0x1e4)](_0x328758(0x1e1))[_0x328758(0x1e3)],_0x284527=await generateEnhancedBackgroundImage(_0x1e4ae3,_0x24a35b[_0x328758(0x1dd)],_0x5af616,_0x4e2f87);_0x1e222f[_0x328758(0x1d5)][_0x328758(0x1d0)](_0x328758(0x214)),_0x1e222f[_0x328758(0x1d5)][_0x328758(0x1af)](_0x328758(0x1a3)),_0x1e222f['textContent']=_0x328758(0x206),setTimeout(()=>{const _0x35b4a9=_0x328758;_0x1e222f[_0x35b4a9(0x1d5)][_0x35b4a9(0x1d0)](_0x35b4a9(0x1a3)),_0x1e222f[_0x35b4a9(0x205)]=_0x35b4a9(0x1ef);},0xbb8);for(var _0x40951a=0x0;_0x40951a<_0x284527[_0x328758(0x19e)];_0x40951a++){var _0x197c0a='data:image/jpeg;base64,'+_0x284527[_0x40951a];createImageAndAppendBase64(_0x197c0a);}console[_0x328758(0x1cc)]('clicked\x20button');}catch(_0x6a6079){console[_0x328758(0x1cc)](_0x328758(0x1e9),_0x6a6079),_0x1e222f['classList']['remove'](_0x328758(0x214)),_0x1e222f[_0x328758(0x1d5)]['add']('generate-image-button-error'),_0x1e222f[_0x328758(0x205)]='Error\x20Generating\x20Image',setTimeout(()=>{const _0x489e41=_0x328758;_0x1e222f[_0x489e41(0x1d5)][_0x489e41(0x1d0)](_0x489e41(0x1c4)),_0x1e222f[_0x489e41(0x205)]=_0x489e41(0x1ef);},0xbb8);}};var _0x44a827=document[_0x5759b5(0x1d6)]('input');_0x44a827[_0x5759b5(0x1ce)]=_0x5759b5(0x1ac),_0x44a827['id']=_0x5759b5(0x1e1),_0x44a827[_0x5759b5(0x1e3)]=!![];var _0x212f90=document[_0x5759b5(0x1d6)]('label');_0x212f90[_0x5759b5(0x1a8)]=_0x5759b5(0x1e1),_0x212f90['textContent']=_0x5759b5(0x1b4);var _0x4c53f2=document['createElement']('div');_0x4c53f2['appendChild'](_0x212f90),_0x4c53f2[_0x5759b5(0x1c9)](_0x44a827),_0x2c8431[_0x5759b5(0x1c9)](_0x4c53f2),_0x2c8431[_0x5759b5(0x1c9)](_0x1e222f);var _0x558a88=document[_0x5759b5(0x1d6)](_0x5759b5(0x1f0));return _0x558a88['id']='background-description',_0x558a88[_0x5759b5(0x1a6)]='5',_0x558a88[_0x5759b5(0x208)]='3',_0x558a88[_0x5759b5(0x1cb)]=_0x5759b5(0x1cf),_0x2c8431['appendChild'](_0x558a88),_0x2c8431;}async function generateEnhancedBackgroundImage(_0xa49943,_0x9be93c,_0x2fe131,_0x3391ae){const _0x72f994=a0_0x34cf;var _0x3ab518;_0xa49943&&(_0x3ab518=await postToServer('http://localhost:3333/generate-images',{'prompt':_0x9be93c,'image':_0x3391ae,'imageMaskPrompt':_0x2fe131}));if(!_0xa49943){var _0x1bf059=await createImageMaskingModal(_0x3391ae);console[_0x72f994(0x1cc)](_0x72f994(0x209),_0x1bf059),_0x3ab518=await postToServer(_0x72f994(0x1bf),{'prompt':_0x9be93c,'image':_0x3391ae,'maskImage':_0x1bf059});}var _0x1b657c=_0x3ab518[_0x72f994(0x1c1)];return _0x1b657c;}function downloadImage(_0x3bd644){const _0x1aaba9=a0_0x34cf;var _0x31e392=document[_0x1aaba9(0x1d6)]('a');_0x31e392[_0x1aaba9(0x1e7)]=_0x3bd644,_0x31e392[_0x1aaba9(0x20c)]=_0x1aaba9(0x20f),document[_0x1aaba9(0x1a7)][_0x1aaba9(0x1c9)](_0x31e392),_0x31e392['click'](),document[_0x1aaba9(0x1a7)][_0x1aaba9(0x20d)](_0x31e392);}function a0_0x22dd(){const _0x1e02af=['length','data','makeImageAspectSame\x20done\x20\x20\x20','strokeRect','overflow','generate-image-button-success','Original\x20Image\x20Size:','85768NnhZpC','cols','body','htmlFor','getContext','Could\x20not\x20compress\x20image\x20to\x20less\x20than\x2050KB.','Switch\x20to\x20Free\x20Draw','checkbox','image/jpeg','src','add','div','max','getBoundingClientRect','stroke','Use\x20Auto\x20Mask','push','naturalHeight','border','background-description','style','clientY','className','fillStyle','lineWidth','complete','http://localhost:3333/generate-images','forEach','images','2205969Xcbejg','rectangle','generate-image-button-error','left','main_hd_images','min','mouseup','appendChild','click','placeholder','log','5289732trMzCR','type','Enter\x20a\x20description\x20for\x20the\x20background\x20image,\x20or\x20leave\x20blank\x20for\x20a\x20random\x20background\x20suggestion.','remove','776879BIlTcU','span','floor','fillRect','classList','createElement','naturalWidth','Mismatch\x20in\x20sizes\x20-\x20Image:\x20(','modal-button','Image\x20has\x20not\x20been\x20loaded.','auto','toDataURL','value','clientX','top','2px\x20solid\x20red','use-auto-mask-checkbox','385KGqCqa','checked','getElementById','black','height','href','white','error:\x20','fill','Sizes\x20do\x20not\x20match.\x20Not\x20resolving.','button','moveTo','Sizes\x20match.\x20Resolving\x20with\x20mask.','Generate\x20Image','textarea','2SZbBrW','547570cHGYcy','item_type','putImageData','generate-image-button','onclick','freeDraw','mask-buttons','strokeStyle','drawImage','),\x20Mask:\x20(','addEventListener','10LIhVwt','clearRect','Switch\x20to\x20Rectangle\x20Draw','width','modal-overlay',',\x20photo\x20realistic,\x20ultra\x20realistic,\x20photography,\x20shallow\x20depth\x20of\x20field','onload','Confirm\x20Mask','textContent','Image\x20Generated!','621PDufbJ','rows','maskImage:\x20','Generating\x20Image...','canvas','download','removeChild','171920tpvExJ','image.jpg','getImageData','544052Nyerih','background_suggestion','parse','generate-image-button-working','lineTo'];a0_0x22dd=function(){return _0x1e02af;};return a0_0x22dd();}function compressImageToUnder50kb(_0x155901){return new Promise((_0x51ee79,_0x29ba18)=>{const _0x5a84ab=a0_0x34cf,_0x169568=0x19*0x400;let _0x2df29c=document[_0x5a84ab(0x1d6)](_0x5a84ab(0x20b)),_0x2ac12a=_0x2df29c[_0x5a84ab(0x1a9)]('2d'),_0x45b5c9=0.8;_0x2df29c[_0x5a84ab(0x200)]=_0x155901['width'],_0x2df29c[_0x5a84ab(0x1e6)]=_0x155901['height'];let _0x89f335=0.95,_0x126882=null;function _0x2f3406(){const _0x1489e7=_0x5a84ab;_0x2ac12a[_0x1489e7(0x1fe)](0x0,0x0,_0x2df29c[_0x1489e7(0x200)],_0x2df29c['height']),_0x2df29c[_0x1489e7(0x200)]=_0x155901['width']*_0x89f335,_0x2df29c[_0x1489e7(0x1e6)]=_0x155901[_0x1489e7(0x1e6)]*_0x89f335,_0x2ac12a[_0x1489e7(0x1fa)](_0x155901,0x0,0x0,_0x2df29c[_0x1489e7(0x200)],_0x2df29c[_0x1489e7(0x1e6)]);var _0x1d0ae9=_0x2df29c[_0x1489e7(0x1dc)](_0x1489e7(0x1ad),_0x45b5c9);if(_0x1d0ae9[_0x1489e7(0x19e)]<_0x169568){let _0x50ddc3=new Image();_0x50ddc3[_0x1489e7(0x203)]=()=>_0x51ee79(_0x50ddc3),_0x50ddc3[_0x1489e7(0x1ae)]=_0x1d0ae9;return;}else{if(_0x45b5c9>0.1)_0x45b5c9-=0.05;else{if(_0x89f335>0.5)_0x89f335-=0.05,_0x45b5c9=0.8;else{if(_0x126882){let _0x309198=new Image();_0x309198['onload']=()=>_0x51ee79(_0x309198),_0x309198[_0x1489e7(0x1ae)]=_0x126882;return;}else{_0x29ba18(new Error(_0x1489e7(0x1aa)));return;}}}_0x126882=_0x1d0ae9,_0x2f3406();}}_0x2f3406();});}function a0_0x34cf(_0x3fe54b,_0x4e37e5){const _0x22dde9=a0_0x22dd();return a0_0x34cf=function(_0x34cfdf,_0xc9ae15){_0x34cfdf=_0x34cfdf-0x19e;let _0x3edf72=_0x22dde9[_0x34cfdf];return _0x3edf72;},a0_0x34cf(_0x3fe54b,_0x4e37e5);}async function ensureWhitespace(_0x393108,_0x152b6a=0.2,_0xebf843=0x400){const _0x550d95=a0_0x34cf;let _0x58ca1f=_0x393108['naturalWidth'],_0x1e7f72=_0x393108['naturalHeight'],_0x57df9c=Math[_0x550d95(0x1c7)](_0x58ca1f,_0x1e7f72),_0x410484=Math[_0x550d95(0x1d3)](_0x57df9c*_0x152b6a),_0x2e76bf=_0x58ca1f+0x2*_0x410484,_0x416d66=_0x1e7f72+0x2*_0x410484,_0x571815=document['createElement'](_0x550d95(0x20b));_0x571815[_0x550d95(0x200)]=_0x2e76bf,_0x571815[_0x550d95(0x1e6)]=_0x416d66;let _0x4f2e4e=_0x571815[_0x550d95(0x1a9)]('2d');_0x4f2e4e[_0x550d95(0x1bc)]=_0x550d95(0x1e8),_0x4f2e4e[_0x550d95(0x1d4)](0x0,0x0,_0x2e76bf,_0x416d66),_0x4f2e4e[_0x550d95(0x1fa)](_0x393108,_0x410484,_0x410484,_0x58ca1f,_0x1e7f72);if(Math['max'](_0x2e76bf,_0x416d66)>_0xebf843){let _0x4024ad=Math[_0x550d95(0x1c7)](_0xebf843/_0x2e76bf,_0xebf843/_0x416d66),_0x2fe609=Math[_0x550d95(0x1d3)](_0x2e76bf*_0x4024ad),_0x385de3=Math[_0x550d95(0x1d3)](_0x416d66*_0x4024ad),_0x2c2de0=document[_0x550d95(0x1d6)](_0x550d95(0x20b));_0x2c2de0[_0x550d95(0x200)]=_0x2fe609,_0x2c2de0[_0x550d95(0x1e6)]=_0x385de3;let _0x2a7a49=_0x2c2de0[_0x550d95(0x1a9)]('2d');_0x2a7a49[_0x550d95(0x1fa)](_0x571815,0x0,0x0,_0x2fe609,_0x385de3),_0x393108[_0x550d95(0x1ae)]=_0x2c2de0[_0x550d95(0x1dc)]();}else _0x393108[_0x550d95(0x1ae)]=_0x571815['toDataURL']();return _0x393108;}async function resizeImageIfNeeded(_0x4c4703,_0x8a2406=0x400){const _0xfe53f6=a0_0x34cf;let _0x456c9d=_0x4c4703[_0xfe53f6(0x1d7)],_0x23b7a8=_0x4c4703[_0xfe53f6(0x1b6)];if(Math[_0xfe53f6(0x1b1)](_0x456c9d,_0x23b7a8)>_0x8a2406){let _0x50527e,_0x279a13;_0x456c9d>_0x23b7a8?(_0x50527e=_0x8a2406,_0x279a13=Math[_0xfe53f6(0x1d3)](_0x8a2406*_0x23b7a8/_0x456c9d)):(_0x279a13=_0x8a2406,_0x50527e=Math['floor'](_0x8a2406*_0x456c9d/_0x23b7a8));let _0x1e2fb9=document[_0xfe53f6(0x1d6)](_0xfe53f6(0x20b));_0x1e2fb9[_0xfe53f6(0x200)]=_0x50527e,_0x1e2fb9[_0xfe53f6(0x1e6)]=_0x279a13;let _0x14b451=_0x1e2fb9['getContext']('2d');_0x14b451[_0xfe53f6(0x1fa)](_0x4c4703,0x0,0x0,_0x50527e,_0x279a13),_0x4c4703['src']=_0x1e2fb9['toDataURL']();}return console[_0xfe53f6(0x1cc)]('Image\x20resized\x20if\x20needed'),_0x4c4703;}function createImageMaskingModal(_0xc4bb9b){return new Promise((_0x1211a7,_0x563fec)=>{const _0x228ddd=a0_0x34cf;let _0x2e10fb=new Image();_0x2e10fb[_0x228ddd(0x1ae)]=_0xc4bb9b;if(!_0x2e10fb[_0x228ddd(0x1be)]||_0x2e10fb[_0x228ddd(0x1b6)]===0x0){_0x563fec(new Error(_0x228ddd(0x1da)));return;}let _0x2d1285=![],_0x304685=0x0,_0x39e027=0x0;var _0x1b9c15=[];let _0x53b21c=document[_0x228ddd(0x1d6)](_0x228ddd(0x1b0)),_0x56ae35=document['createElement'](_0x228ddd(0x1b0)),_0x488594=document[_0x228ddd(0x1d6)](_0x228ddd(0x20b)),_0x281095=document[_0x228ddd(0x1d6)]('canvas'),_0x5a24fd=document[_0x228ddd(0x1d6)]('button'),_0x37dd90=document[_0x228ddd(0x1d6)](_0x228ddd(0x1ec)),_0x259650=document[_0x228ddd(0x1d6)](_0x228ddd(0x1ec)),_0x390f49=_0x488594[_0x228ddd(0x1a9)]('2d'),_0x35e610=_0x281095['getContext']('2d');_0x53b21c[_0x228ddd(0x1bb)]=_0x228ddd(0x201),_0x56ae35[_0x228ddd(0x1bb)]='modal-content',_0x488594[_0x228ddd(0x1bb)]='modal-canvas',_0x281095['className']='modal-canvas',_0x5a24fd[_0x228ddd(0x1bb)]=_0x228ddd(0x1d9),_0x37dd90[_0x228ddd(0x1bb)]='modal-button',_0x259650[_0x228ddd(0x1bb)]=_0x228ddd(0x1d9),_0x488594[_0x228ddd(0x200)]=_0x2e10fb[_0x228ddd(0x1d7)],_0x488594[_0x228ddd(0x1e6)]=_0x2e10fb[_0x228ddd(0x1b6)],_0x281095['width']=_0x2e10fb[_0x228ddd(0x1d7)],_0x281095[_0x228ddd(0x1e6)]=_0x2e10fb[_0x228ddd(0x1b6)],_0x488594[_0x228ddd(0x1b9)][_0x228ddd(0x1b7)]=_0x228ddd(0x1e0),_0x56ae35[_0x228ddd(0x1b9)][_0x228ddd(0x1a2)]=_0x228ddd(0x1db),_0x390f49['drawImage'](_0x2e10fb,0x0,0x0,_0x488594[_0x228ddd(0x200)],_0x488594[_0x228ddd(0x1e6)]),_0x35e610[_0x228ddd(0x1bc)]='white',_0x35e610[_0x228ddd(0x1d4)](0x0,0x0,_0x281095[_0x228ddd(0x200)],_0x281095[_0x228ddd(0x1e6)]);let _0x1c21c1=[],_0x10b37d=[],_0xdba958=_0x228ddd(0x1f7);_0x259650[_0x228ddd(0x205)]=_0x228ddd(0x1ff);function _0x33fd60(){const _0x1a0a85=_0x228ddd;_0x488594['addEventListener']('mousedown',_0x275220=>{const _0x42842e=a0_0x34cf,_0x1bb208=_0x488594[_0x42842e(0x1b2)]();_0x304685=_0x275220[_0x42842e(0x1de)]-_0x1bb208[_0x42842e(0x1c5)],_0x39e027=_0x275220[_0x42842e(0x1ba)]-_0x1bb208[_0x42842e(0x1df)],_0x2d1285=!![],_0xdba958==='freeDraw'&&(_0x1b9c15=[[_0x304685,_0x39e027]]);}),_0x488594[_0x1a0a85(0x1fc)]('mousemove',_0x11bde8=>{const _0x39356=_0x1a0a85;if(!_0x2d1285)return;const _0x2840ce=_0x488594[_0x39356(0x1b2)](),_0xece34f=_0x11bde8[_0x39356(0x1de)]-_0x2840ce[_0x39356(0x1c5)],_0x5c3cd6=_0x11bde8[_0x39356(0x1ba)]-_0x2840ce[_0x39356(0x1df)];if(_0xdba958===_0x39356(0x1f7))_0x1b9c15[_0x39356(0x1b5)]([_0xece34f,_0x5c3cd6]),_0xec3cfb(_0xece34f,_0x5c3cd6,!![]);else _0xdba958===_0x39356(0x1c3)&&_0xec3cfb(_0xece34f,_0x5c3cd6);}),_0x488594['addEventListener'](_0x1a0a85(0x1c8),_0x1fe4d1=>{const _0x297876=_0x1a0a85;if(!_0x2d1285)return;_0x2d1285=![];if(_0xdba958==='rectangle')_0x3023fe(_0x304685,_0x39e027,_0x1fe4d1[_0x297876(0x1de)]-_0x488594[_0x297876(0x1b2)]()[_0x297876(0x1c5)],_0x1fe4d1['clientY']-_0x488594['getBoundingClientRect']()[_0x297876(0x1df)]);else _0xdba958===_0x297876(0x1f7)&&(_0x10b37d[_0x297876(0x1b5)](_0x1b9c15),_0x1b9c15=[],_0xac09e1());});}function _0x3023fe(_0x1d6c67,_0x5d4569,_0x314cac,_0x5cff3f){const _0x337ae0=_0x228ddd,_0x57823a=_0x314cac-_0x1d6c67,_0x4d6789=_0x5cff3f-_0x5d4569;_0x1c21c1[_0x337ae0(0x1b5)]({'x':_0x1d6c67,'y':_0x5d4569,'w':_0x57823a,'h':_0x4d6789}),_0xec3cfb();}function _0xec3cfb(_0x22d953=_0x304685,_0x419003=_0x39e027,_0x261062=![]){const _0x1bbb99=_0x228ddd;_0x390f49[_0x1bbb99(0x1fe)](0x0,0x0,_0x488594['width'],_0x488594[_0x1bbb99(0x1e6)]),_0x390f49[_0x1bbb99(0x1fa)](_0x2e10fb,0x0,0x0,_0x488594[_0x1bbb99(0x200)],_0x488594[_0x1bbb99(0x1e6)]),_0x1c21c1[_0x1bbb99(0x1c0)](_0x9918e1=>{const _0x40cd8e=_0x1bbb99;_0x390f49[_0x40cd8e(0x1a1)](_0x9918e1['x'],_0x9918e1['y'],_0x9918e1['w'],_0x9918e1['h']);}),_0x10b37d[_0x1bbb99(0x1c0)](_0x4638a7=>{const _0x2ceb66=_0x1bbb99;_0x390f49['beginPath'](),_0x390f49[_0x2ceb66(0x1ed)](_0x4638a7[0x0][0x0],_0x4638a7[0x0][0x1]);for(let _0x26f991=0x1;_0x26f991<_0x4638a7[_0x2ceb66(0x19e)];_0x26f991++){_0x390f49[_0x2ceb66(0x215)](_0x4638a7[_0x26f991][0x0],_0x4638a7[_0x26f991][0x1]);}_0x390f49[_0x2ceb66(0x1b3)]();});if(_0x261062){_0x390f49['beginPath'](),_0x390f49[_0x1bbb99(0x1ed)](_0x1b9c15[0x0][0x0],_0x1b9c15[0x0][0x1]);for(let _0x2c18ce=0x1;_0x2c18ce<_0x1b9c15[_0x1bbb99(0x19e)];_0x2c18ce++){_0x390f49[_0x1bbb99(0x215)](_0x1b9c15[_0x2c18ce][0x0],_0x1b9c15[_0x2c18ce][0x1]);}_0x390f49[_0x1bbb99(0x1bd)]=0x2,_0x390f49[_0x1bbb99(0x1f9)]=_0x1bbb99(0x1e5),_0x390f49[_0x1bbb99(0x1b3)]();}else{if(_0xdba958===_0x1bbb99(0x1c3)&&_0x2d1285){const _0x2e8f21=_0x22d953-_0x304685,_0x348346=_0x419003-_0x39e027;_0x390f49[_0x1bbb99(0x1a1)](_0x304685,_0x39e027,_0x2e8f21,_0x348346);}}}function _0xac09e1(){const _0x51c11c=_0x228ddd;_0x35e610[_0x51c11c(0x1fe)](0x0,0x0,_0x281095['width'],_0x281095['height']),_0x35e610[_0x51c11c(0x1bc)]=_0x51c11c(0x1e8),_0x35e610[_0x51c11c(0x1d4)](0x0,0x0,_0x281095[_0x51c11c(0x200)],_0x281095[_0x51c11c(0x1e6)]),_0x35e610[_0x51c11c(0x1bc)]='black',_0x1c21c1['forEach'](_0x136e33=>{const _0x4a1174=_0x51c11c;_0x35e610[_0x4a1174(0x1d4)](_0x136e33['x'],_0x136e33['y'],_0x136e33['w'],_0x136e33['h']);}),_0x10b37d['forEach'](_0x3e11df=>{const _0x299b14=_0x51c11c;_0x35e610['beginPath'](),_0x35e610['moveTo'](_0x3e11df[0x0][0x0],_0x3e11df[0x0][0x1]);for(let _0x45fb80=0x1;_0x45fb80<_0x3e11df['length'];_0x45fb80++){_0x35e610[_0x299b14(0x215)](_0x3e11df[_0x45fb80][0x0],_0x3e11df[_0x45fb80][0x1]);}_0x35e610[_0x299b14(0x1ea)]();});let _0x4f7548=_0x35e610[_0x51c11c(0x210)](0x0,0x0,_0x281095[_0x51c11c(0x200)],_0x281095[_0x51c11c(0x1e6)]),_0x181bbd=_0x4f7548[_0x51c11c(0x19f)];for(let _0x36444c=0x0;_0x36444c<_0x181bbd['length'];_0x36444c+=0x4){_0x181bbd[_0x36444c]>0x0&&(_0x181bbd[_0x36444c]=0xff,_0x181bbd[_0x36444c+0x1]=0xff,_0x181bbd[_0x36444c+0x2]=0xff);}_0x35e610[_0x51c11c(0x1f4)](_0x4f7548,0x0,0x0);}_0x259650[_0x228ddd(0x1fc)](_0x228ddd(0x1ca),()=>{const _0x1dc6fd=_0x228ddd;_0xdba958===_0x1dc6fd(0x1f7)?(_0xdba958='rectangle',_0x259650[_0x1dc6fd(0x205)]=_0x1dc6fd(0x1ab)):(_0xdba958=_0x1dc6fd(0x1f7),_0x259650['textContent']=_0x1dc6fd(0x1ff));}),_0x5a24fd[_0x228ddd(0x205)]=_0x228ddd(0x204),_0x5a24fd[_0x228ddd(0x1fc)](_0x228ddd(0x1ca),()=>{const _0x3d6b99=_0x228ddd;console[_0x3d6b99(0x1cc)](_0x3d6b99(0x1a4),_0x2e10fb[_0x3d6b99(0x1d7)],'x',_0x2e10fb[_0x3d6b99(0x1b6)]),console[_0x3d6b99(0x1cc)]('Mask\x20Size:',_0x281095[_0x3d6b99(0x200)],'x',_0x281095['height']),_0x2e10fb['naturalWidth']===_0x281095[_0x3d6b99(0x200)]&&_0x2e10fb['naturalHeight']===_0x281095[_0x3d6b99(0x1e6)]?(console['log'](_0x3d6b99(0x1ee)),_0x1211a7(_0x281095['toDataURL']())):(console[_0x3d6b99(0x1cc)](_0x3d6b99(0x1eb)),_0x563fec(new Error(_0x3d6b99(0x1d8)+_0x2e10fb[_0x3d6b99(0x1d7)]+'x'+_0x2e10fb[_0x3d6b99(0x1b6)]+_0x3d6b99(0x1fb)+_0x281095['width']+'x'+_0x281095[_0x3d6b99(0x1e6)]+')'))),document[_0x3d6b99(0x1a7)]['removeChild'](_0x53b21c);}),_0x37dd90[_0x228ddd(0x205)]='Clear\x20Masks',_0x37dd90[_0x228ddd(0x1fc)](_0x228ddd(0x1ca),()=>{const _0x239973=_0x228ddd;_0x1c21c1=[],_0x10b37d=[],_0x390f49[_0x239973(0x1fe)](0x0,0x0,_0x488594[_0x239973(0x200)],_0x488594[_0x239973(0x1e6)]),_0x390f49[_0x239973(0x1fa)](_0x2e10fb,0x0,0x0,_0x488594[_0x239973(0x200)],_0x488594[_0x239973(0x1e6)]),_0xac09e1();});var _0x19ff49=document[_0x228ddd(0x1d6)](_0x228ddd(0x1b0));_0x19ff49['classList']['add'](_0x228ddd(0x1f8)),_0x19ff49[_0x228ddd(0x1c9)](_0x259650),_0x19ff49['appendChild'](_0x5a24fd),_0x19ff49['appendChild'](_0x37dd90),_0x56ae35[_0x228ddd(0x1c9)](_0x19ff49),_0x56ae35[_0x228ddd(0x1c9)](_0x488594),_0x56ae35[_0x228ddd(0x1c9)](_0x281095),_0x53b21c[_0x228ddd(0x1c9)](_0x56ae35),document[_0x228ddd(0x1a7)]['appendChild'](_0x53b21c),_0x33fd60(),_0xac09e1();});}