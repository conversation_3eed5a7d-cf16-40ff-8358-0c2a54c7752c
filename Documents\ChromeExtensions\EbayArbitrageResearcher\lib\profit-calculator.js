/**
 * Advanced Profit Calculator - Comprehensive arbitrage analysis
 * Includes all fees, taxes, shipping costs, and risk assessment
 */
class ProfitCalculator {
  constructor() {
    // Get global context (window in content scripts, self in service workers)
    const globalContext = typeof window !== 'undefined' ? window : self;
    this.eventBus = globalContext.ArbitrageEventBus;
    this.debug = true;
    
    // Current fee structures (updated 2024)
    this.feeStructures = {
      ebay: {
        // Final Value Fees by category
        finalValueFees: {
          default: 0.1295, // 12.95%
          electronics: 0.1295,
          fashion: 0.1295,
          home: 0.1295,
          automotive: 0.10, // 10%
          business: 0.12 // 12%
        },
        
        // Payment processing fees
        paymentFees: {
          rate: 0.0349, // 3.49%
          fixed: 0.49 // $0.49 per transaction
        },
        
        // Listing fees
        listingFees: {
          basic: 0, // First 250 listings free
          additional: 0.35 // $0.35 per additional listing
        },
        
        // Optional fees
        optionalFees: {
          subtitle: 1.50,
          gallery: 0.35,
          bold: 4.00,
          featured: 24.95
        }
      },
      
      amazon: {
        // Referral fees by category
        referralFees: {
          default: 0.15, // 15%
          electronics: 0.08, // 8%
          books: 0.15,
          clothing: 0.17, // 17%
          home: 0.15,
          automotive: 0.12 // 12%
        },
        
        // FBA fees (Fulfillment by Amazon)
        fbaFees: {
          // Standard size items
          standard: {
            pick: 1.06,
            pack: 1.06,
            weight: 0.50 // per lb
          },
          
          // Oversize items
          oversize: {
            pick: 1.06,
            pack: 4.09,
            weight: 0.50 // per lb
          }
        },
        
        // Storage fees (monthly)
        storageFees: {
          standard: 0.75, // per cubic foot
          oversize: 0.48,
          longTerm: 6.90 // after 365 days
        },
        
        // Closing fees
        closingFees: {
          media: 1.80, // Books, music, video, software
          other: 0
        }
      },
      
      // Tax considerations
      taxes: {
        salesTax: 0.08, // Average 8% (varies by state)
        incomeTax: 0.22, // 22% marginal rate (varies)
        selfEmployment: 0.1413 // 14.13% (Social Security + Medicare)
      },
      
      // Shipping estimates
      shipping: {
        domestic: {
          small: 4.50, // Under 1 lb
          medium: 8.50, // 1-5 lbs
          large: 15.00, // 5-20 lbs
          oversized: 25.00 // Over 20 lbs
        },
        
        international: {
          small: 15.00,
          medium: 25.00,
          large: 45.00,
          oversized: 75.00
        }
      }
    };
    
    // Risk assessment factors
    this.riskFactors = {
      competition: {
        low: 1.0,    // Unique product
        medium: 0.8, // Some competition
        high: 0.6    // Heavy competition
      },
      
      demand: {
        high: 1.2,   // High demand
        medium: 1.0, // Normal demand
        low: 0.7     // Low demand
      },
      
      seasonality: {
        stable: 1.0,    // Year-round demand
        seasonal: 0.8,  // Seasonal item
        trending: 1.3   // Trending item
      },
      
      condition: {
        new: 1.0,
        used: 0.8,
        refurbished: 0.9,
        parts: 0.6
      }
    };
  }

  /**
   * Calculate comprehensive profit analysis
   * @param {object} item - Item data from eBay
   * @param {object} amazonData - Amazon pricing data
   * @param {object} options - Calculation options
   * @returns {object} Detailed profit analysis
   */
  calculateProfit(item, amazonData, options = {}) {
    try {
      const {
        category = 'default',
        weight = 1, // pounds
        dimensions = { length: 12, width: 8, height: 4 }, // inches
        condition = 'used',
        competitionLevel = 'medium',
        demandLevel = 'medium',
        seasonality = 'stable',
        includeShipping = true,
        includeTaxes = true,
        fbaEnabled = false
      } = options;

      // Base prices
      const ebayPrice = parseFloat(item.price) || 0;
      const amazonPrice = parseFloat(amazonData.price) || 0;
      
      if (ebayPrice <= 0 || amazonPrice <= 0) {
        throw new Error('Invalid price data');
      }

      // Calculate all costs
      const costs = this.calculateAllCosts(ebayPrice, amazonPrice, {
        category,
        weight,
        dimensions,
        includeShipping,
        includeTaxes,
        fbaEnabled
      });

      // Calculate profit metrics
      const grossProfit = amazonPrice - ebayPrice;
      const netProfit = amazonPrice - ebayPrice - costs.total;
      const profitMargin = (netProfit / amazonPrice) * 100;
      const roi = (netProfit / ebayPrice) * 100;

      // Risk assessment
      const riskScore = this.calculateRiskScore({
        condition,
        competitionLevel,
        demandLevel,
        seasonality,
        profitMargin
      });

      // Opportunity grading
      const grade = this.calculateGrade(profitMargin, riskScore, netProfit);

      const analysis = {
        // Basic metrics
        ebayPrice,
        amazonPrice,
        grossProfit,
        netProfit,
        profitMargin,
        roi,
        
        // Cost breakdown
        costs,
        
        // Risk assessment
        riskScore,
        grade,
        
        // Recommendations
        recommendations: this.generateRecommendations({
          profitMargin,
          riskScore,
          netProfit,
          costs,
          item,
          amazonData
        }),
        
        // Metadata
        calculationDate: new Date().toISOString(),
        options
      };

      if (this.debug) {
        console.log('💰 ProfitCalculator: Analysis complete', analysis);
      }

      // Emit calculation event
      this.eventBus.emit('profit:calculated', {
        item,
        amazonData,
        analysis
      });

      return analysis;

    } catch (error) {
      console.error('❌ ProfitCalculator: Calculation failed:', error);
      
      // Return error analysis
      return {
        error: error.message,
        ebayPrice: item.price || 0,
        amazonPrice: amazonData.price || 0,
        netProfit: 0,
        profitMargin: 0,
        grade: 'F',
        calculationDate: new Date().toISOString()
      };
    }
  }

  /**
   * Calculate all costs involved in the arbitrage
   * @param {number} ebayPrice - eBay purchase price
   * @param {number} amazonPrice - Amazon selling price
   * @param {object} options - Cost calculation options
   * @returns {object} Detailed cost breakdown
   */
  calculateAllCosts(ebayPrice, amazonPrice, options) {
    const {
      category = 'default',
      weight = 1,
      dimensions = { length: 12, width: 8, height: 4 },
      includeShipping = true,
      includeTaxes = true,
      fbaEnabled = false
    } = options;

    const costs = {
      // eBay costs (when buying)
      ebay: {
        purchase: ebayPrice,
        shipping: includeShipping ? this.calculateShippingCost(weight, 'domestic') : 0,
        tax: includeTaxes ? ebayPrice * this.feeStructures.taxes.salesTax : 0
      },
      
      // Amazon costs (when selling)
      amazon: {
        referralFee: amazonPrice * (this.feeStructures.amazon.referralFees[category] || this.feeStructures.amazon.referralFees.default),
        fulfillment: fbaEnabled ? this.calculateFBAFees(weight, dimensions) : 0,
        storage: fbaEnabled ? this.feeStructures.amazon.storageFees.standard : 0,
        closing: this.feeStructures.amazon.closingFees.other
      },
      
      // Additional costs
      additional: {
        packaging: 2.00, // Estimated packaging costs
        labels: 0.50, // Shipping labels
        time: 5.00 // Time value (15 minutes at $20/hour)
      }
    };

    // Calculate totals
    costs.ebayTotal = Object.values(costs.ebay).reduce((sum, cost) => sum + cost, 0);
    costs.amazonTotal = Object.values(costs.amazon).reduce((sum, cost) => sum + cost, 0);
    costs.additionalTotal = Object.values(costs.additional).reduce((sum, cost) => sum + cost, 0);
    costs.total = costs.ebayTotal + costs.amazonTotal + costs.additionalTotal;

    return costs;
  }

  /**
   * Calculate shipping costs based on weight and destination
   * @param {number} weight - Package weight in pounds
   * @param {string} destination - 'domestic' or 'international'
   * @returns {number} Shipping cost
   */
  calculateShippingCost(weight, destination = 'domestic') {
    const rates = this.feeStructures.shipping[destination];

    if (weight <= 1) return rates.small;
    if (weight <= 5) return rates.medium;
    if (weight <= 20) return rates.large;
    return rates.oversized;
  }

  /**
   * Calculate FBA (Fulfillment by Amazon) fees
   * @param {number} weight - Item weight in pounds
   * @param {object} dimensions - Item dimensions in inches
   * @returns {number} Total FBA fees
   */
  calculateFBAFees(weight, dimensions) {
    const { length, width, height } = dimensions;
    const volume = (length * width * height) / 1728; // cubic feet

    // Determine if standard or oversize
    const isOversize = length > 18 || width > 14 || height > 8 || weight > 20;
    const feeStructure = isOversize ?
      this.feeStructures.amazon.fbaFees.oversize :
      this.feeStructures.amazon.fbaFees.standard;

    return feeStructure.pick + feeStructure.pack + (weight * feeStructure.weight);
  }

  /**
   * Calculate risk score based on multiple factors
   * @param {object} factors - Risk assessment factors
   * @returns {number} Risk score (0-100, lower is better)
   */
  calculateRiskScore(factors) {
    const {
      condition = 'used',
      competitionLevel = 'medium',
      demandLevel = 'medium',
      seasonality = 'stable',
      profitMargin = 0
    } = factors;

    let riskScore = 50; // Base risk score

    // Adjust for condition
    const conditionMultiplier = this.riskFactors.condition[condition] || 0.8;
    riskScore *= (2 - conditionMultiplier); // Lower multiplier = higher risk

    // Adjust for competition
    const competitionMultiplier = this.riskFactors.competition[competitionLevel] || 0.8;
    riskScore *= (2 - competitionMultiplier);

    // Adjust for demand
    const demandMultiplier = this.riskFactors.demand[demandLevel] || 1.0;
    riskScore /= demandMultiplier; // Higher demand = lower risk

    // Adjust for seasonality
    const seasonalityMultiplier = this.riskFactors.seasonality[seasonality] || 1.0;
    riskScore /= seasonalityMultiplier;

    // Adjust for profit margin (higher margin = lower risk)
    if (profitMargin > 30) riskScore *= 0.7;
    else if (profitMargin > 20) riskScore *= 0.8;
    else if (profitMargin > 10) riskScore *= 0.9;
    else if (profitMargin < 5) riskScore *= 1.3;

    return Math.min(100, Math.max(0, Math.round(riskScore)));
  }

  /**
   * Calculate opportunity grade
   * @param {number} profitMargin - Profit margin percentage
   * @param {number} riskScore - Risk score (0-100)
   * @param {number} netProfit - Net profit amount
   * @returns {string} Grade (A+ to F)
   */
  calculateGrade(profitMargin, riskScore, netProfit) {
    // Base grade on profit margin
    let grade = 'F';

    if (profitMargin >= 40 && netProfit >= 20) grade = 'A+';
    else if (profitMargin >= 30 && netProfit >= 15) grade = 'A';
    else if (profitMargin >= 20 && netProfit >= 10) grade = 'B+';
    else if (profitMargin >= 15 && netProfit >= 8) grade = 'B';
    else if (profitMargin >= 10 && netProfit >= 5) grade = 'C+';
    else if (profitMargin >= 5 && netProfit >= 3) grade = 'C';
    else if (profitMargin >= 2 && netProfit >= 1) grade = 'D';

    // Adjust grade based on risk
    if (riskScore > 70) {
      // High risk - downgrade
      const gradeMap = { 'A+': 'A', 'A': 'B+', 'B+': 'B', 'B': 'C+', 'C+': 'C', 'C': 'D', 'D': 'F' };
      grade = gradeMap[grade] || 'F';
    } else if (riskScore < 30) {
      // Low risk - upgrade
      const gradeMap = { 'A': 'A+', 'B+': 'A', 'B': 'B+', 'C+': 'B', 'C': 'C+', 'D': 'C' };
      grade = gradeMap[grade] || grade;
    }

    return grade;
  }

  /**
   * Generate actionable recommendations
   * @param {object} data - Analysis data
   * @returns {Array} Array of recommendation objects
   */
  generateRecommendations(data) {
    const { profitMargin, riskScore, netProfit, costs, item, amazonData } = data;
    const recommendations = [];

    // Profit-based recommendations
    if (profitMargin < 10) {
      recommendations.push({
        type: 'warning',
        category: 'profit',
        message: 'Low profit margin. Consider negotiating eBay price or finding higher-priced Amazon listings.',
        priority: 'high'
      });
    }

    if (netProfit < 5) {
      recommendations.push({
        type: 'warning',
        category: 'profit',
        message: 'Low absolute profit. May not be worth the time investment.',
        priority: 'medium'
      });
    }

    // Risk-based recommendations
    if (riskScore > 60) {
      recommendations.push({
        type: 'caution',
        category: 'risk',
        message: 'High risk opportunity. Verify demand and competition before proceeding.',
        priority: 'high'
      });
    }

    // Cost optimization recommendations
    if (costs.amazon.fulfillment > 5) {
      recommendations.push({
        type: 'tip',
        category: 'cost',
        message: 'Consider merchant fulfillment to reduce Amazon fees.',
        priority: 'low'
      });
    }

    // Market-based recommendations
    if (profitMargin > 30 && riskScore < 40) {
      recommendations.push({
        type: 'opportunity',
        category: 'market',
        message: 'Excellent opportunity! Consider buying multiple units if available.',
        priority: 'high'
      });
    }

    return recommendations;
  }

  /**
   * Batch calculate profits for multiple items
   * @param {Array} items - Array of item/Amazon data pairs
   * @param {object} defaultOptions - Default calculation options
   * @returns {Promise<Array>} Array of profit analyses
   */
  async batchCalculate(items, defaultOptions = {}) {
    const results = [];

    for (let i = 0; i < items.length; i++) {
      try {
        const { item, amazonData, options = {} } = items[i];
        const mergedOptions = { ...defaultOptions, ...options };

        const analysis = this.calculateProfit(item, amazonData, mergedOptions);
        results.push({
          index: i,
          success: true,
          analysis
        });

        // Emit progress
        this.eventBus.emit('profit:batch-progress', {
          completed: i + 1,
          total: items.length,
          progress: ((i + 1) / items.length) * 100
        });

      } catch (error) {
        console.error(`❌ ProfitCalculator: Batch calculation failed for item ${i}:`, error);
        results.push({
          index: i,
          success: false,
          error: error.message
        });
      }
    }

    this.eventBus.emit('profit:batch-complete', {
      totalItems: items.length,
      successCount: results.filter(r => r.success).length,
      results
    });

    return results;
  }

  /**
   * Update fee structures with current rates
   * @param {object} newFees - Updated fee structure
   */
  updateFeeStructures(newFees) {
    this.feeStructures = { ...this.feeStructures, ...newFees };

    if (this.debug) {
      console.log('💰 ProfitCalculator: Fee structures updated', newFees);
    }

    this.eventBus.emit('profit:fees-updated', newFees);
  }

  /**
   * Get current fee structures
   * @returns {object} Current fee structures
   */
  getFeeStructures() {
    return { ...this.feeStructures };
  }

  /**
   * Enable/disable debug logging
   * @param {boolean} enabled - Debug enabled
   */
  setDebug(enabled) {
    this.debug = enabled;
  }
}

// Global profit calculator instance (compatible with both content scripts and service workers)
const globalContext = typeof window !== 'undefined' ? window : self;
globalContext.ArbitrageProfitCalculator = globalContext.ArbitrageProfitCalculator || new ProfitCalculator();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProfitCalculator;
}

console.log('✅ ArbitrageProfitCalculator loaded and exported to globalContext.ArbitrageProfitCalculator');
console.log('🕐 ProfitCalculator initialization timing:', {
  timestamp: performance.now(),
  readyState: typeof document !== 'undefined' ? document.readyState : 'N/A (service worker)',
  context: typeof window !== 'undefined' ? 'content script' : 'service worker',
  dependencies: {
    eventBus: !!globalContext.ArbitrageEventBus
  }
});
