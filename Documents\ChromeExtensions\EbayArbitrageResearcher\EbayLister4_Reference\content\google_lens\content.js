function a0_0x202d(){var _0x42a90a=['15qHQqMI','find_amazon_item\x20message\x20received','248096SwSFdI','6268BVKXWA','type','find_amazon_item','then','onMessage','120996NDwhYa','1040991IpdhAV','2846934GivkIs','354TWyJlw','131938sPpNJF','addListener','111154PQLvEb','log','runtime'];a0_0x202d=function(){return _0x42a90a;};return a0_0x202d();}var a0_0x386c37=a0_0x11fa;function a0_0x11fa(_0x4d5a16,_0x439e17){var _0x202d0f=a0_0x202d();return a0_0x11fa=function(_0x11fa4e,_0x1d5f87){_0x11fa4e=_0x11fa4e-0xd1;var _0x1afaf2=_0x202d0f[_0x11fa4e];return _0x1afaf2;},a0_0x11fa(_0x4d5a16,_0x439e17);}(function(_0x2cdf0a,_0x2ca333){var _0x431832=a0_0x11fa,_0x56331f=_0x2cdf0a();while(!![]){try{var _0x552fe4=-parseInt(_0x431832(0xd2))/0x1+-parseInt(_0x431832(0xd4))/0x2+-parseInt(_0x431832(0xd1))/0x3*(parseInt(_0x431832(0xda))/0x4)+parseInt(_0x431832(0xd7))/0x5*(parseInt(_0x431832(0xdf))/0x6)+parseInt(_0x431832(0xe0))/0x7+parseInt(_0x431832(0xd9))/0x8+parseInt(_0x431832(0xe1))/0x9;if(_0x552fe4===_0x2ca333)break;else _0x56331f['push'](_0x56331f['shift']());}catch(_0x3dd1a1){_0x56331f['push'](_0x56331f['shift']());}}}(a0_0x202d,0x2cf40),chrome[a0_0x386c37(0xd6)][a0_0x386c37(0xde)][a0_0x386c37(0xd3)](function(_0x7bf984,_0xc48435,_0x201392){var _0x46c1b7=a0_0x386c37;console[_0x46c1b7(0xd5)]('request',_0x7bf984);if(_0x7bf984[_0x46c1b7(0xdb)]===_0x46c1b7(0xdc))return console['log'](_0x46c1b7(0xd8)),onPageLoadAndStable()[_0x46c1b7(0xdd)](()=>{console['log']('onPageLoadAndStable\x20callback'),navigateToAmazonItem();}),!![];return!![];}));