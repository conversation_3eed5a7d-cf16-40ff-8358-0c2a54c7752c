var a0_0x30e1e8=a0_0x2981;function a0_0x2981(_0x2218d4,_0x1af43d){var _0xf14a2d=a0_0xf14a();return a0_0x2981=function(_0x298132,_0x24a723){_0x298132=_0x298132-0x106;var _0x190ea5=_0xf14a2d[_0x298132];return _0x190ea5;},a0_0x2981(_0x2218d4,_0x1af43d);}function a0_0xf14a(){var _0x15adbc=['catch','321957NanKoO','automatic','14095680HmLSQI','1085226HYPITv','get_ended_listings\x20message\x20received\x20in\x20ebay\x20ended\x20listings\x20content.js','prepare_sell_similar_workspace','Available\x20quantity','Custom\x20label\x20(SKU)','Artikelnummer','sendMessage','onMessage','runtime','waiting_for_sell_similar_to_be_clicked','resend_message_to_tab_on_update_and_notified','3727759NpOfCs','type','769330guqIrs','24mCkxIk','log','246670yDSHcm','ebay\x20ended\x20listings\x20content.js\x20loaded','message\x20received\x20in\x20ebay\x20ended\x20listings\x20content.js','599105PkGfNV'];a0_0xf14a=function(){return _0x15adbc;};return a0_0xf14a();}(function(_0x3a9d87,_0x27fa77){var _0x2816a3=a0_0x2981,_0x6a92b5=_0x3a9d87();while(!![]){try{var _0x341cac=-parseInt(_0x2816a3(0x113))/0x1+parseInt(_0x2816a3(0x110))/0x2+-parseInt(_0x2816a3(0x118))/0x3+parseInt(_0x2816a3(0x111))/0x4*(-parseInt(_0x2816a3(0x116))/0x5)+-parseInt(_0x2816a3(0x11b))/0x6+-parseInt(_0x2816a3(0x10e))/0x7+parseInt(_0x2816a3(0x11a))/0x8;if(_0x341cac===_0x27fa77)break;else _0x6a92b5['push'](_0x6a92b5['shift']());}catch(_0x16541d){_0x6a92b5['push'](_0x6a92b5['shift']());}}}(a0_0xf14a,0x57f6e),console[a0_0x30e1e8(0x112)](a0_0x30e1e8(0x114)),onPageLoadAndStableNotifyBackground(),chrome[a0_0x30e1e8(0x10b)][a0_0x30e1e8(0x10a)]['addListener'](function(_0x37561e,_0x7ec0c4,_0x32f9c7){var _0x16044c=a0_0x30e1e8;console[_0x16044c(0x112)](_0x16044c(0x115),_0x37561e);if(_0x37561e[_0x16044c(0x10f)]=='sell_similar_ended_low_performing_items')return console[_0x16044c(0x112)](_0x16044c(0x11c)),_0x37561e[_0x16044c(0x119)]==![]?chrome['runtime']['sendMessage']({'type':'sell-similar-button-clicked'}):_0x32f9c7({'response':_0x16044c(0x10c)}),sellSimilarEndedItems(),!![];if(_0x37561e[_0x16044c(0x10f)]==_0x16044c(0x11d)){var _0x424a21=[['Item\x20number',_0x16044c(0x108)],[_0x16044c(0x107),'Bestandseinheit\x20(SKU)'],['Views\x20(30\x20days)','Aufrufe\x20(30\x20Tage)'],[_0x16044c(0x106),'Verfügbare\x20Anzahl'],['End\x20date','Enddatum']],_0x2c96f2=areToggleOptionsDisplayed(_0x424a21);!_0x2c96f2?(chrome['runtime'][_0x16044c(0x109)]({'type':_0x16044c(0x10d),'message':_0x37561e}),withTimeout(0x1d4c0,applyCustomViewSettings(_0x424a21))['then'](()=>{})[_0x16044c(0x117)](_0x6e2362=>{var _0x50c517=_0x16044c;console[_0x50c517(0x112)]('applyCustomViewSettings\x20error',_0x6e2362),location['reload']();})):(chrome[_0x16044c(0x10b)][_0x16044c(0x109)]({'type':'preparing_sell_similar_workspace'}),sellSimilarEndedItems());}return!![];}));async function main(){await onPageLoadAndStableNotifyBackground(),await sellSimilarEndedItems();}