/**
 * Comprehensive Error Handler with retry logic and recovery mechanisms
 * Ensures robust operation even when eBay/Amazon change their DOM structure
 */
class ErrorHandler {
  constructor() {
    this.errorCounts = new Map();
    this.maxRetries = 3;
    this.baseDelay = 1000;
    this.maxDelay = 30000;
    this.debug = true;

    // ✅ CRITICAL FIX: Bind methods to maintain 'this' context
    this.handleError = this.handleError.bind(this);
    this.retryWithBackoff = this.retryWithBackoff.bind(this);
    this.logError = this.logError.bind(this);
  }

  /**
   * Execute function with exponential backoff retry
   * @param {function} fn - Function to execute
   * @param {object} options - Retry options
   * @returns {Promise} Result or throws final error
   */
  async retryWithBackoff(fn, options = {}) {
    const {
      maxRetries = this.maxRetries,
      baseDelay = this.baseDelay,
      maxDelay = this.maxDelay,
      retryCondition = () => true,
      onRetry = null,
      context = 'unknown'
    } = options;

    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (this.debug && attempt > 0) {
          console.log(`🔄 ErrorHandler: Retry attempt ${attempt}/${maxRetries} for ${context}`);
        }
        
        const result = await fn();
        
        // Reset error count on success
        this.errorCounts.delete(context);
        
        if (this.debug && attempt > 0) {
          console.log(`✅ ErrorHandler: Success on attempt ${attempt + 1} for ${context}`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Track error frequency
        const errorCount = this.errorCounts.get(context) || 0;
        this.errorCounts.set(context, errorCount + 1);
        
        if (this.debug) {
          console.warn(`❌ ErrorHandler: Attempt ${attempt + 1} failed for ${context}:`, error.message);
        }
        
        // Check if we should retry
        if (attempt === maxRetries || !retryCondition(error, attempt)) {
          break;
        }
        
        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
          maxDelay
        );
        
        if (this.debug) {
          console.log(`⏳ ErrorHandler: Waiting ${delay}ms before retry`);
        }
        
        await this.delay(delay);
        
        if (onRetry) {
          onRetry(error, attempt, delay);
        }
      }
    }
    
    // All retries failed
    this.logFinalError(context, lastError, maxRetries);
    throw lastError;
  }

  /**
   * Safely execute DOM operations with fallback selectors
   * @param {function} operation - DOM operation function
   * @param {Array<string>} fallbackSelectors - Alternative selectors to try
   * @param {string} context - Context for error reporting
   * @returns {Promise} Result or null if all fail
   */
  async safeDOMOperation(operation, fallbackSelectors = [], context = 'DOM operation') {
    try {
      return await operation();
    } catch (error) {
      if (this.debug) {
        console.warn(`🔍 ErrorHandler: Primary DOM operation failed for ${context}, trying fallbacks`);
      }
      
      for (let i = 0; i < fallbackSelectors.length; i++) {
        try {
          const element = document.querySelector(fallbackSelectors[i]);
          if (element) {
            if (this.debug) {
              console.log(`✅ ErrorHandler: Fallback selector ${i + 1} worked: ${fallbackSelectors[i]}`);
            }
            return element;
          }
        } catch (fallbackError) {
          if (this.debug) {
            console.warn(`❌ ErrorHandler: Fallback ${i + 1} failed:`, fallbackError.message);
          }
        }
      }
      
      // All fallbacks failed
      console.error(`💥 ErrorHandler: All DOM operations failed for ${context}:`, error);
      return null;
    }
  }

  /**
   * Graceful degradation wrapper
   * @param {function} primaryFn - Primary function to try
   * @param {function} fallbackFn - Fallback function
   * @param {string} context - Context for logging
   * @returns {Promise} Result from primary or fallback
   */
  async withFallback(primaryFn, fallbackFn, context = 'operation') {
    try {
      return await primaryFn();
    } catch (error) {
      if (this.debug) {
        console.warn(`⚠️ ErrorHandler: Primary function failed for ${context}, using fallback:`, error.message);
      }
      
      try {
        const result = await fallbackFn();
        if (this.debug) {
          console.log(`✅ ErrorHandler: Fallback succeeded for ${context}`);
        }
        return result;
      } catch (fallbackError) {
        console.error(`💥 ErrorHandler: Both primary and fallback failed for ${context}:`, {
          primary: error.message,
          fallback: fallbackError.message
        });
        throw fallbackError;
      }
    }
  }

  /**
   * Validate and sanitize extracted data
   * @param {*} data - Data to validate
   * @param {object} schema - Validation schema
   * @param {string} context - Context for error reporting
   * @returns {object} Validated and sanitized data
   */
  validateData(data, schema, context = 'data validation') {
    const result = { valid: true, data: {}, errors: [] };
    
    for (const [field, rules] of Object.entries(schema)) {
      try {
        const value = data[field];
        
        // Required field check
        if (rules.required && (value === undefined || value === null || value === '')) {
          result.errors.push(`${field} is required`);
          result.valid = false;
          continue;
        }
        
        // Type validation
        if (value !== undefined && rules.type) {
          if (rules.type === 'number' && isNaN(Number(value))) {
            result.errors.push(`${field} must be a number`);
            result.valid = false;
            continue;
          }
          
          if (rules.type === 'string' && typeof value !== 'string') {
            result.errors.push(`${field} must be a string`);
            result.valid = false;
            continue;
          }
        }
        
        // Custom validation
        if (rules.validate && typeof rules.validate === 'function') {
          const customResult = rules.validate(value);
          if (customResult !== true) {
            result.errors.push(`${field}: ${customResult}`);
            result.valid = false;
            continue;
          }
        }
        
        // Sanitization
        let sanitizedValue = value;
        if (rules.sanitize && typeof rules.sanitize === 'function') {
          sanitizedValue = rules.sanitize(value);
        }
        
        result.data[field] = sanitizedValue;
        
      } catch (error) {
        result.errors.push(`${field}: validation error - ${error.message}`);
        result.valid = false;
      }
    }
    
    if (!result.valid && this.debug) {
      console.warn(`⚠️ ErrorHandler: Data validation failed for ${context}:`, result.errors);
    }
    
    return result;
  }

  /**
   * Log final error after all retries failed
   * @param {string} context - Operation context
   * @param {Error} error - Final error
   * @param {number} attempts - Number of attempts made
   */
  logFinalError(context, error, attempts) {
    const errorInfo = {
      context,
      message: error.message,
      stack: error.stack,
      attempts,
      timestamp: new Date().toISOString(),
      errorCount: this.errorCounts.get(context) || 0
    };
    
    console.error(`💥 ErrorHandler: Final failure for ${context} after ${attempts} attempts:`, errorInfo);
    
    // Emit error event for monitoring
    if (window.ArbitrageEventBus) {
      window.ArbitrageEventBus.emit('error:final', errorInfo);
    }
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error statistics
   * @returns {object} Error statistics
   */
  getErrorStats() {
    return {
      totalContexts: this.errorCounts.size,
      errorCounts: Object.fromEntries(this.errorCounts),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Reset error counts
   * @param {string} context - Specific context to reset (optional)
   */
  resetErrorCounts(context = null) {
    if (context) {
      this.errorCounts.delete(context);
    } else {
      this.errorCounts.clear();
    }
  }

  /**
   * ✅ CRITICAL FIX: Main error handling method
   * @param {Error|string} error - Error to handle
   * @param {string} context - Context where error occurred
   * @returns {object} Error details
   */
  handleError(error, context = 'Unknown') {
    const errorDetails = {
      message: error?.message || String(error),
      context,
      timestamp: Date.now(),
      stack: error?.stack || null
    };

    console.error(`🚨 [${context}] Error:`, errorDetails);

    // Track error count for this context
    const contextCount = this.errorCounts.get(context) || 0;
    this.errorCounts.set(context, contextCount + 1);

    return errorDetails;
  }

  /**
   * Log error message with context
   * @param {string} message - Error message
   * @param {any} data - Additional data
   */
  logError(message, data = null) {
    console.error(`📝 Error Log: ${message}`, data);
  }

  /**
   * Enable/disable debug logging
   * @param {boolean} enabled - Debug enabled
   */
  setDebug(enabled) {
    this.debug = enabled;
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.ErrorHandler = ErrorHandler;
window.ArbitrageErrorHandler = window.ArbitrageErrorHandler || ErrorHandler;
window.globalContext.ErrorHandler = ErrorHandler;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ErrorHandler;
}

console.log('✅ ArbitrageErrorHandler loaded and exported to window.ArbitrageErrorHandler');
console.log('🕐 ErrorHandler initialization timing:', {
  timestamp: performance.now(),
  readyState: document.readyState,
  dependencies: {
    eventBus: !!window.ArbitrageEventBus
  }
});
