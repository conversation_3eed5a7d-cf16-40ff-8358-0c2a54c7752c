var a0_0x48f7b2=a0_0x7d53;(function(_0x1641d4,_0x10cf35){var _0x307493=a0_0x7d53,_0x50573a=_0x1641d4();while(!![]){try{var _0x49f2f0=parseInt(_0x307493(0x1a6))/0x1*(-parseInt(_0x307493(0xe1))/0x2)+-parseInt(_0x307493(0x150))/0x3*(-parseInt(_0x307493(0x1b0))/0x4)+parseInt(_0x307493(0x1a1))/0x5*(parseInt(_0x307493(0x162))/0x6)+-parseInt(_0x307493(0x16a))/0x7+parseInt(_0x307493(0x119))/0x8+parseInt(_0x307493(0x158))/0x9+parseInt(_0x307493(0x109))/0xa;if(_0x49f2f0===_0x10cf35)break;else _0x50573a['push'](_0x50573a['shift']());}catch(_0x45cff1){_0x50573a['push'](_0x50573a['shift']());}}}(a0_0xaf15,0x4d02c),console['log'](a0_0x48f7b2(0x1be)),onPageLoadAndStableNotifyBackground());var startedListing=![],totalNetworkResponse=0x0,previousTotalNetworkResponse=0x0,itemSpecificPrompt='',itemSpecificPromptResponse='',previousTabId,previousWindowId,receivedDescriptionNetworkResponse=![],documentTitle=a0_0x48f7b2(0x124),isScriptCompleted=![],requestHeaders=[];async function testMain(){var _0xd62df3=a0_0x48f7b2;console[_0xd62df3(0x143)](_0xd62df3(0x184)),await onPageLoadAndStable(),await fillEmptyRequiredItemSpecificsV2('Details\x20In\x20Description');}chrome[a0_0x48f7b2(0xfc)][a0_0x48f7b2(0x197)][a0_0x48f7b2(0x16b)](async(_0x17d923,_0x25d49f,_0x162a24)=>{var _0x4a4fc7=a0_0x48f7b2;console[_0x4a4fc7(0x143)](_0x25d49f[_0x4a4fc7(0x194)]?_0x4a4fc7(0x19c)+_0x25d49f[_0x4a4fc7(0x194)][_0x4a4fc7(0x1c0)]:'From\x20the\x20extension\x20request.type\x20ebay.js'+_0x17d923[_0x4a4fc7(0x13b)]);if((_0x17d923[_0x4a4fc7(0x13b)]===_0x4a4fc7(0x1a9)||_0x17d923[_0x4a4fc7(0x13b)]==='identify_product')&&startedListing===![]){console[_0x4a4fc7(0x143)]('insert_ebay_data\x20begins'),startedListing=!![],console['log'](_0x4a4fc7(0x1a3),_0x17d923),await waitForPageToLoad(),setInterval(function(){!isScriptCompleted?document['title']=documentTitle:clearInterval();},0x64);if(_0x17d923[_0x4a4fc7(0xe0)][_0x4a4fc7(0x10e)]==='EbayRelist')postEbayDataForEbayItem(_0x17d923[_0x4a4fc7(0xe0)]);else try{postEbayData(_0x17d923[_0x4a4fc7(0xe0)]);}catch(_0x590188){console[_0x4a4fc7(0x143)](_0x4a4fc7(0x13d)+_0x590188),chrome['runtime'][_0x4a4fc7(0x1ab)]({'type':_0x4a4fc7(0x11b),'sku':_0x17d923['productData'][_0x4a4fc7(0x11d)],'message':'Error\x20Posting\x20Item:\x20'+_0x590188});}}_0x17d923['type']===_0x4a4fc7(0x130)&&(console[_0x4a4fc7(0x143)]('networkResponse',_0x17d923[_0x4a4fc7(0x100)]),previousTotalNetworkResponse=totalNetworkResponse,totalNetworkResponse++),_0x17d923[_0x4a4fc7(0x13b)]===_0x4a4fc7(0xdd)&&(console['log']('ebay_description_network_response',_0x17d923[_0x4a4fc7(0x100)]),receivedDescriptionNetworkResponse=!![]),_0x17d923['type']===_0x4a4fc7(0xeb)&&(console['log']('received\x20message\x20requestVariables',_0x17d923),requestHeaders=_0x17d923[_0x4a4fc7(0x126)]),_0x17d923[_0x4a4fc7(0x13b)]==='remove_specs'&&(console[_0x4a4fc7(0x143)](_0x4a4fc7(0x140),_0x17d923),testDraft());}),chrome[a0_0x48f7b2(0xfc)][a0_0x48f7b2(0x197)]['addListener']((_0x48dd96,_0x1a83d9,_0x382850)=>{var _0x518a79=a0_0x48f7b2;console[_0x518a79(0x143)](_0x1a83d9[_0x518a79(0x194)]?_0x518a79(0x19c)+_0x1a83d9[_0x518a79(0x194)]['url']:_0x518a79(0x199)+_0x48dd96[_0x518a79(0x13b)]);if(_0x48dd96[_0x518a79(0x13b)]===_0x518a79(0x16e)){console[_0x518a79(0x143)](_0x518a79(0x16e),_0x48dd96);var _0x2e5174=_0x48dd96[_0x518a79(0xd7)];return setGSPRWithClicks(_0x2e5174[_0x518a79(0x185)],_0x2e5174['responsiblePersonEU'],!![])[_0x518a79(0x149)](_0x4af13b=>{var _0x272678=_0x518a79;console[_0x272678(0x143)](_0x272678(0x1b4),_0x4af13b),_0x382850({'gsprSet':_0x4af13b});}),!![];}});async function attemptToSetGSPR(_0x3fd0f9,_0x2d5bb5,_0x6df464,_0x51ef18){var _0x11941f=a0_0x48f7b2;await onPageLoadAndStable();var _0x5ee4bf=_0x11941f(0x17b),_0x36985e=document[_0x11941f(0x1c2)](_0x5ee4bf)[_0x11941f(0x128)],_0x4469a5=_0x36985e['slice'](0x0,-0x1);console['log']('titleWithoutLastCharacter',_0x4469a5),await pasteTitle(_0x4469a5),documentTitle=_0x11941f(0x12a),await waitForNetworkResponseCountIncrease(),await waitForRequestHeaders(),console['log']('waitForRequestHeaders\x20Done');var _0x56450f=![];try{_0x56450f=await setGSPR(_0x3fd0f9,_0x2d5bb5);}catch(_0xfacd42){console['log'](_0x11941f(0x15a));}if(_0x56450f){await pasteTitle(_0x36985e),documentTitle=_0x11941f(0x12a),await waitForNetworkResponseCountIncrease();var _0x5f0c9a=getSubmitButton();scrollIntoView(_0x5f0c9a),_0x5f0c9a[_0x11941f(0x18b)]();var _0x4475a1=await checkIfItemWasSuccessfullyPosted();!_0x4475a1&&(_0x56450f=![]);}return _0x56450f;}function getHeader(_0x95737,_0x49f301){var _0x29bc4f=a0_0x48f7b2;for(var _0xc9fb4a=0x0;_0xc9fb4a<_0x49f301[_0x29bc4f(0x1b5)];_0xc9fb4a++){if(_0x49f301[_0xc9fb4a][_0x29bc4f(0x15c)]===_0x95737)return _0x49f301[_0xc9fb4a];}return null;}async function postViaNetworkRequest(_0x40364e,_0x1ba1d1){var _0x5ccd61=a0_0x48f7b2,_0x163278=new Headers();for(var _0x31a301=0x0;_0x31a301<requestHeaders[_0x5ccd61(0x1b5)];_0x31a301++){var _0x257d22=requestHeaders[_0x31a301];_0x163278[_0x5ccd61(0x131)](_0x257d22[_0x5ccd61(0x15c)],_0x257d22['value']);}var _0x3c0429=new URLSearchParams(new URL(window['location'][_0x5ccd61(0xf8)])['search']),_0x5ca7c8=_0x3c0429[_0x5ccd61(0x10d)](_0x5ccd61(0x15d)),_0x21f997=window[_0x5ccd61(0x12e)][_0x5ccd61(0xf8)][_0x5ccd61(0x193)]('/')[0x2],_0x390c7=_0x5ccd61(0x186)+_0x21f997+_0x5ccd61(0xe3)+_0x5ca7c8+_0x5ccd61(0x1c1),_0x1bd61e=_0x5ccd61(0x17a),_0xbccefd={[_0x40364e]:_0x1ba1d1},_0x6ce11a=new TextEncoder()[_0x5ccd61(0x10f)](JSON[_0x5ccd61(0x159)](_0xbccefd));try{const _0x54d9f7=await fetch(_0x390c7,{'method':_0x1bd61e,'headers':_0x163278,'body':_0x6ce11a});console[_0x5ccd61(0x143)](_0x5ccd61(0xe7),_0x54d9f7);}catch(_0x2d5d5f){console['error']('Error:',_0x2d5d5f);}}async function postObjectViaNetworkRequest(_0x27e0ca){var _0x2899e9=a0_0x48f7b2,_0x469b81=new Headers();for(var _0x4e6448=0x0;_0x4e6448<requestHeaders[_0x2899e9(0x1b5)];_0x4e6448++){var _0x19969e=requestHeaders[_0x4e6448];_0x469b81[_0x2899e9(0x131)](_0x19969e[_0x2899e9(0x15c)],_0x19969e[_0x2899e9(0x128)]);}var _0x364aec=new URLSearchParams(new URL(window['location'][_0x2899e9(0xf8)])['search']),_0x223b15=_0x364aec[_0x2899e9(0x10d)](_0x2899e9(0x15d)),_0x57bc4b=window[_0x2899e9(0x12e)][_0x2899e9(0xf8)][_0x2899e9(0x193)]('/')[0x2],_0x1b8ad4=_0x2899e9(0x186)+_0x57bc4b+_0x2899e9(0xe3)+_0x223b15+_0x2899e9(0x1c1),_0x4dda34=_0x2899e9(0x17a),_0x486939=new TextEncoder()[_0x2899e9(0x10f)](JSON['stringify'](_0x27e0ca));try{const _0xe0bee4=await fetch(_0x1b8ad4,{'method':_0x4dda34,'headers':_0x469b81,'body':_0x486939});console[_0x2899e9(0x143)](_0x2899e9(0xe7),_0xe0bee4);}catch(_0x40a7b5){console[_0x2899e9(0x1b8)](_0x2899e9(0x105),_0x40a7b5);}}function waitForRequestHeaders(){return new Promise((_0x55b41d,_0xdeb02)=>{var _0x136fed=setInterval(()=>{var _0x18e9fa=a0_0x7d53;requestHeaders[_0x18e9fa(0x1b5)]>0x0&&(clearInterval(_0x136fed),_0x55b41d());},0x3e8);});}function parseCookies(_0x2df983){var _0xb2d2c1=a0_0x48f7b2,_0x48ee0b=[],_0x39067a=_0x2df983[_0xb2d2c1(0x193)](';');for(var _0x435109=0x0;_0x435109<_0x39067a[_0xb2d2c1(0x1b5)];_0x435109++){var _0x297742=_0x39067a[_0x435109],_0x2218e6=_0x297742['split']('='),_0xd9963d=_0x2218e6[0x0],_0x1df48a=_0x2218e6[0x1],_0x22b826={'name':_0xd9963d,'value':_0x1df48a};_0x48ee0b[_0xb2d2c1(0x103)](_0x22b826);}return _0x48ee0b;}function waitForPageToLoad(){return new Promise((_0x2d66d9,_0x5cea53)=>{var _0x358830=setInterval(()=>{var _0x113967=a0_0x7d53;document[_0x113967(0xf9)]===_0x113967(0x117)&&(clearInterval(_0x358830),_0x2d66d9());},0x3e8);});}async function testDraft(){var _0x3905d3=a0_0x48f7b2;window[_0x3905d3(0x12e)][_0x3905d3(0xf8)]['includes'](_0x3905d3(0x15d))&&chrome[_0x3905d3(0xea)]['local'][_0x3905d3(0x10d)]('amazon',async function(_0x62b1ed){var _0x31ad83=_0x3905d3,_0x15fee9=_0x62b1ed[_0x31ad83(0x115)];console[_0x31ad83(0x143)](_0x31ad83(0xe6),_0x15fee9),await waitForPageToLoad(),setInterval(function(){var _0xa6962f=_0x31ad83;document[_0xa6962f(0x173)]=documentTitle;},0x32),console[_0x31ad83(0x143)]('0'),await pasteTitle(_0x15fee9['custom_title']),documentTitle='Title\x20Pasted',await waitForNetworkResponseCountIncrease(),console['log'](_0x31ad83(0xf6)),await waitForRequestHeaders(),console[_0x31ad83(0x143)]('waitForRequestHeaders\x20Done');});}async function postEbayDataForEbayItem(_0x5a1bba){var _0x5376b5=a0_0x48f7b2;changeFaviconToTaskBar(),console[_0x5376b5(0x143)]('1'),documentTitle='Started\x20inserting\x20product',await pasteOnlyImages(_0x5a1bba[_0x5376b5(0x136)],_0x5a1bba[_0x5376b5(0x173)]),await clickShowMoreButton(),documentTitle=_0x5376b5(0x191),await pasteTitle(_0x5a1bba[_0x5376b5(0x173)]),await waitForNetworkResponseCountIncrease(),console[_0x5376b5(0x143)](_0x5376b5(0xf6)),await waitForRequestHeaders(),await new Promise((_0x3a0b4c,_0xe4f6c)=>{setTimeout(_0x3a0b4c,0x7d0);}),documentTitle=_0x5376b5(0xff),await selectAllExtractedAspects(),await clickShowMoreButton();_0x5a1bba[_0x5376b5(0x11d)]&&await pasteSKU(_0x5a1bba[_0x5376b5(0x11d)]);documentTitle=_0x5376b5(0x19e);_0x5a1bba[_0x5376b5(0x19e)]&&await fillEmptyItemSpecifics(_0x5a1bba[_0x5376b5(0x19e)]);documentTitle=_0x5376b5(0x1bb),await fillEmptyRequiredItemSpecificsV2(_0x5376b5(0x139)),await pastePrice(_0x5a1bba['price']),await postViaNetworkRequest(_0x5376b5(0x118),_0x5a1bba[_0x5376b5(0x118)]),await waitForNetworkResponseCountIncrease(),documentTitle=_0x5376b5(0xf0);var _0x1a92d5=await replaceCarouselImages(_0x5a1bba[_0x5376b5(0x179)]);await addHtmlToEbayDescription(_0x1a92d5),await postViaNetworkRequest(_0x5376b5(0x179),_0x1a92d5),await waitForNetworkResponseCountIncrease();var _0x50bb8d=document[_0x5376b5(0x1c2)](_0x5376b5(0x1b9));scrollIntoView(_0x50bb8d),documentTitle='Promoting\x20Listing';var _0x1cc366=document['querySelector'](_0x5376b5(0x134));if(_0x1cc366){await postViaNetworkRequest(_0x5376b5(0x13f),!![]),await waitForNetworkResponseCountIncrease();var {promoted_listing_ad_rate:_0x82f52d}=await chrome[_0x5376b5(0xea)]['local'][_0x5376b5(0x10d)]('promoted_listing_ad_rate');console['log'](_0x5376b5(0x113),_0x82f52d);try{console[_0x5376b5(0x143)]('promoted_listing_ad_rate\x20from\x20product',_0x5a1bba[_0x5376b5(0x17c)]),_0x5a1bba[_0x5376b5(0x17c)]&&(_0x82f52d=_0x5a1bba['promoted_listing_ad_rate']);}catch(_0x3fc4bf){console['log']('error\x20setting\x20promoted_listing_ad_rate');}await postViaNetworkRequest(_0x5376b5(0x14a),_0x82f52d),await waitForNetworkResponseCountIncrease();}documentTitle='Adding\x20UPC',await clickShowMoreButton();var {domain:_0x42cd40}=await chrome['storage'][_0x5376b5(0x166)][_0x5376b5(0x10d)](_0x5376b5(0x1a4));if(_0x42cd40==_0x5376b5(0xe8)||_0x42cd40=='de'){await postViaNetworkRequest(_0x5376b5(0x198),_0x5376b5(0x153)),await waitForNetworkResponseCountIncrease();var _0x93bcd4={'EAN':['Does\x20not\x20apply']};await postViaNetworkRequest(_0x5376b5(0xf7),_0x93bcd4),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest(_0x5376b5(0x104),_0x5376b5(0x153)),await waitForNetworkResponseCountIncrease();var _0x93bcd4={'UPC':['Does\x20not\x20apply']};await postViaNetworkRequest('attributes',_0x93bcd4),await waitForNetworkResponseCountIncrease();}console[_0x5376b5(0x143)](_0x5376b5(0x1a8)),documentTitle='Ready\x20To\x20Submit!',addSubmitButtonListener(_0x5a1bba);var {autoSubmitEnabled:_0x11673a}=await chrome[_0x5376b5(0xea)]['local'][_0x5376b5(0x10d)](_0x5376b5(0xda));_0x11673a&&await submitTheListing();}async function postEbayData(_0x434e96){var _0x2587e1=a0_0x48f7b2;console['log'](_0x2587e1(0x1b2));if(_0x434e96['suggestedQuery']){if(_0x434e96[_0x2587e1(0x1ac)]['length']>0x41)do{var _0x4f8d37=_0x434e96[_0x2587e1(0x1ac)]['split']('\x20');_0x4f8d37[_0x2587e1(0x1b1)](),_0x434e96[_0x2587e1(0x1ac)]=_0x4f8d37[_0x2587e1(0x10b)]('\x20');}while(_0x434e96[_0x2587e1(0x1ac)]['length']>0x41);}await removeRestrictedWordsFromItemSpecifics(),await selectCondition(),changeFaviconToTaskBar(),console['log'](_0x434e96),await uploadImageConfig(_0x434e96),console[_0x2587e1(0x143)]('0'),documentTitle=_0x2587e1(0x160),await clickShowMoreButton(),documentTitle=_0x2587e1(0x191);var _0x18fdf3=_0x434e96[_0x2587e1(0xf5)];_0x18fdf3=await removeRestrictedWordsFromText(_0x434e96[_0x2587e1(0xf5)]),_0x18fdf3=_0x18fdf3[_0x2587e1(0x138)](),await pasteTitle(_0x18fdf3),console[_0x2587e1(0x143)](_0x2587e1(0x151)),await waitForNetworkResponseCountIncrease(),console[_0x2587e1(0x143)](_0x2587e1(0xf6)),await waitForRequestHeaders(),console[_0x2587e1(0x143)](_0x2587e1(0xde)),await selectAllExtractedAspects();var {shouldGetGspr:_0x7a1324}=await chrome['storage'][_0x2587e1(0x166)][_0x2587e1(0x10d)](_0x2587e1(0x19b)),_0x15971b=![];_0x7a1324&&(documentTitle=_0x2587e1(0x14c),_0x15971b=await setGSPR(_0x434e96[_0x2587e1(0x185)],_0x434e96[_0x2587e1(0x182)]),documentTitle='gspr\x20set'+_0x15971b);documentTitle='Adding\x20Item\x20Specifics';var {shouldFillRequiredItemSpecifics:_0x5573c6}=await chrome[_0x2587e1(0xea)][_0x2587e1(0x166)]['get'](_0x2587e1(0x18a)),{shouldFillOptionalItemSpecifics:_0x35c228}=await chrome['storage'][_0x2587e1(0x166)]['get'](_0x2587e1(0x188));console[_0x2587e1(0x143)](_0x2587e1(0x18a),_0x5573c6),console[_0x2587e1(0x143)](_0x2587e1(0x188),_0x35c228),await clickShowMoreButton();_0x434e96[_0x2587e1(0x10e)]=='paid'&&_0x5573c6?(console[_0x2587e1(0x143)](_0x2587e1(0x164)),console[_0x2587e1(0x143)]('shouldFillOptionalItemSpecifics\x20is\x20'+_0x35c228),await clickShowMoreButton(),await fillItemSpecificValuesWithAiV6(_0x434e96)):(console[_0x2587e1(0x143)](_0x2587e1(0x176)),console[_0x2587e1(0x143)](_0x2587e1(0x18f)+_0x35c228));(_0x434e96['listingType']==_0x2587e1(0x148)||_0x434e96['listingType']=='paid2')&&(_0x5573c6&&await fillItemSpecificValuesWithAiV5(_0x434e96,_0x2587e1(0x1a5),!![]),await clickShowMoreButton(),_0x35c228&&await fillItemSpecificValuesWithAiV5(_0x434e96,_0x2587e1(0xf4),!![]));documentTitle='Adding\x20UPC';if(_0x434e96[_0x2587e1(0x17f)]){var _0x25d9c9=capitalizeFirstLetterOfEachWordInItemSpecifics(_0x434e96[_0x2587e1(0x17f)]);for(const _0x37bf80 of _0x25d9c9){_0x37bf80[_0x2587e1(0x128)]=await removeRestrictedWordsFromText(_0x37bf80[_0x2587e1(0x128)]),_0x37bf80['value']=_0x37bf80['value'][_0x2587e1(0x135)](/,(\s*,)+/g,''),_0x37bf80[_0x2587e1(0x128)]=_0x37bf80[_0x2587e1(0x128)][_0x2587e1(0x135)](/,+/g,',')[_0x2587e1(0x135)](/,$/,''),_0x37bf80['value']=_0x37bf80[_0x2587e1(0x128)][_0x2587e1(0x138)]();}_0x25d9c9=_0x25d9c9[_0x2587e1(0x175)](_0x12e23b=>_0x12e23b[_0x2587e1(0x128)]!=='');for(const _0x31d244 of _0x25d9c9){const _0x4842de=await detectRestrictedWords(_0x31d244['label']);console[_0x2587e1(0x143)](_0x2587e1(0x116),_0x4842de),_0x4842de&&(_0x25d9c9=_0x25d9c9[_0x2587e1(0x175)](_0x53391a=>_0x53391a[_0x2587e1(0x10a)]!==_0x31d244['label']));}console[_0x2587e1(0x143)](_0x2587e1(0x17f),_0x25d9c9),await fillEmptyItemSpecifics(_0x25d9c9);}if(_0x5573c6&&_0x434e96[_0x2587e1(0x1ac)])try{await fillEmptyRequiredItemSpecificsV2(_0x434e96['suggestedQuery']);}catch(_0x9686c8){console[_0x2587e1(0x143)](_0x2587e1(0xd8));}else await fillEmptyRequiredItemSpecificsV2(_0x2587e1(0x139));if(_0x35c228&&_0x434e96['listingType']==_0x2587e1(0x183))try{await fillEmptyOptionalItemSpecificsV2(_0x434e96['suggestedQuery']);}catch(_0x57f9fb){console[_0x2587e1(0x143)](_0x2587e1(0x189));}console[_0x2587e1(0x143)]('1'),console[_0x2587e1(0x143)]('2'),documentTitle=_0x2587e1(0x1bb);var _0x40babb=_0x434e96['custom_price'];await pastePrice(_0x40babb),console[_0x2587e1(0x143)]('4'),console[_0x2587e1(0x143)]('5'),documentTitle=_0x2587e1(0xe9);var _0x11c26e=checkIfSkuEnabled();if(!_0x11c26e)try{await enableSku();}catch(_0x10ae37){console[_0x2587e1(0x143)](_0x2587e1(0x122));}if(_0x434e96['listingType']==_0x2587e1(0x183)||_0x434e96[_0x2587e1(0x10e)]==_0x2587e1(0x14f)||_0x434e96[_0x2587e1(0x10e)]==_0x2587e1(0x148))try{await pasteSKU(btoa(_0x434e96['sku']));}catch(_0x4a3e77){console[_0x2587e1(0x143)](_0x2587e1(0xe5));}console[_0x2587e1(0x143)]('6'),console[_0x2587e1(0x143)]('7'),console[_0x2587e1(0x143)](_0x2587e1(0xdb));if(!_0x434e96['useBrand']){await pasteBrand('Unbranded');try{await fillSingleSelectItemSpecific(_0x2587e1(0x114),'Unbranded'),await fillSingleSelectItemSpecific(_0x2587e1(0x1b7),_0x2587e1(0x132));}catch(_0x5639fd){console[_0x2587e1(0x143)](_0x2587e1(0x192));}}if(_0x434e96[_0x2587e1(0x161)]){await pasteBrand(_0x434e96[_0x2587e1(0xfe)]);try{await fillSingleSelectItemSpecific(_0x2587e1(0x114),_0x434e96[_0x2587e1(0xfe)]);}catch(_0x2abdef){console[_0x2587e1(0x143)]('error\x20pasting\x20brand');}}console[_0x2587e1(0x143)](_0x2587e1(0x11a)),console[_0x2587e1(0x143)]('7.2'),documentTitle=_0x2587e1(0x16c);async function _0x243a0f(_0x5bc09d){var _0x368e53=_0x2587e1;if(_0x7a1324&&!_0x15971b){documentTitle=_0x368e53(0x15b);var _0x206c5d=await createGsprSectionFromText(_0x434e96[_0x368e53(0x17d)],_0x434e96[_0x368e53(0x141)]);_0x5bc09d+=_0x206c5d;}_0x5bc09d=await removeRestrictedWordsFromText(_0x5bc09d),_0x5bc09d=_0x5bc09d[_0x368e53(0x12b)]('<>',''),await addHtmlToEbayDescription(_0x5bc09d),await postViaNetworkRequest('description',_0x5bc09d),await waitForNetworkResponseCountIncrease();}var _0x527b02;console[_0x2587e1(0x143)](_0x2587e1(0x154),_0x434e96[_0x2587e1(0x10e)]);switch(_0x434e96[_0x2587e1(0x10e)]){case'free':documentTitle=_0x2587e1(0x171);const _0x1bf273=await getBasicDescription(_0x434e96);console[_0x2587e1(0x143)](_0x2587e1(0x137),_0x1bf273),await _0x243a0f(_0x1bf273);break;case _0x2587e1(0x183):var _0x1b3180,{useCustomDescriptionPrompt:_0x3bc970}=await chrome[_0x2587e1(0xea)]['local'][_0x2587e1(0x10d)](_0x2587e1(0x129));if(_0x3bc970){var _0x4e4978=(_0x434e96['bullet_points']||[])[_0x2587e1(0x10b)]('\x0a')+'\x0a'+(_0x434e96[_0x2587e1(0x1ae)]||'');_0x1b3180=await getDescriptionHtmlFromOpenAiWithCustomPrompt(_0x4e4978);}else _0x1b3180=await getDescriptionHtmlFromOpenAi(_0x434e96);const {useSimpleDescription:_0x547e3c}=await chrome['storage'][_0x2587e1(0x166)]['get']('useSimpleDescription');_0x527b02=_0x547e3c?_0x1b3180:await createDescriptionFromTemplate2024(_0x1b3180,_0x434e96);try{const _0x2934f5=await createItemSpecificsHtmlTable();_0x527b02+=_0x2934f5;}catch(_0x424622){console['error'](_0x424622);}await _0x243a0f(_0x527b02);break;case _0x2587e1(0x190):const _0xe5baf8=await getDescriptionHtmlFromOpenAi(_0x434e96);await _0x243a0f(_0xe5baf8);break;case'chatGpt':const _0xf6f9b7=await getDescriptionHtmlFromChatGpt(_0x434e96);_0x527b02=await createDescriptionFromTemplate2024(_0xf6f9b7,_0x434e96),await _0x243a0f(_0x527b02);break;}console['log'](_0x2587e1(0xf2));var _0x44e975=document[_0x2587e1(0x1c2)](_0x2587e1(0x1b9));scrollIntoView(_0x44e975),documentTitle=_0x2587e1(0x142);var _0x2cf50b=document[_0x2587e1(0x1c2)](_0x2587e1(0x134));!_0x2cf50b&&(_0x2cf50b=document['querySelector'](_0x2587e1(0x17e)));!_0x2cf50b&&(_0x2cf50b=document[_0x2587e1(0x1c2)](_0x2587e1(0x147)));if(_0x2cf50b){await postViaNetworkRequest('promotedListingSelection',!![]),await waitForNetworkResponseCountIncrease();var {promoted_listing_ad_rate:_0x184ec0}=await chrome[_0x2587e1(0xea)][_0x2587e1(0x166)][_0x2587e1(0x10d)](_0x2587e1(0x17c));console[_0x2587e1(0x143)](_0x2587e1(0x113),_0x184ec0);try{console[_0x2587e1(0x143)]('promoted_listing_ad_rate\x20from\x20product',_0x434e96[_0x2587e1(0x17c)]),_0x434e96['promoted_listing_ad_rate']&&(_0x184ec0=_0x434e96[_0x2587e1(0x17c)]);}catch(_0x440a4f){console['log'](_0x2587e1(0x12f));}await postViaNetworkRequest('adRate',_0x184ec0),await waitForNetworkResponseCountIncrease();}try{documentTitle=_0x2587e1(0x101);var {scheduleListingTime:_0x3ef9c6}=await chrome[_0x2587e1(0xea)][_0x2587e1(0x166)]['get'](_0x2587e1(0x108));_0x3ef9c6?(console['log'](_0x2587e1(0xdc),_0x3ef9c6),await scheduleListing(Number(_0x3ef9c6))):console['log']('scheduleListingTime\x20is\x20false',_0x3ef9c6);}catch(_0x18f79d){console['log'](_0x2587e1(0x170));}var {forceItemLocationSwitch:_0x4cc0e9}=await chrome[_0x2587e1(0xea)][_0x2587e1(0x166)]['get'](_0x2587e1(0x1bf));_0x4cc0e9&&await setItemLocation();var {forceReturnPolicySwitch:_0x8f8279}=await chrome['storage'][_0x2587e1(0x166)][_0x2587e1(0x10d)]('forceReturnPolicySwitch');_0x8f8279&&await setReturnPolicy();documentTitle='Adding\x20UPC',await clickShowMoreButton();var {domain:_0x3174e8}=await chrome[_0x2587e1(0xea)]['local'][_0x2587e1(0x10d)](_0x2587e1(0x1a4));if(_0x3174e8==_0x2587e1(0xe8)||_0x3174e8=='de'||_0x3174e8=='fr'||_0x3174e8=='it'||_0x3174e8=='es'){await postViaNetworkRequest(_0x2587e1(0x198),_0x2587e1(0x153)),await waitForNetworkResponseCountIncrease();var _0x321e29={'EAN':[_0x2587e1(0xf1)]};await postViaNetworkRequest(_0x2587e1(0xf7),_0x321e29),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest(_0x2587e1(0x104),_0x2587e1(0x153)),await waitForNetworkResponseCountIncrease();var _0x321e29={'UPC':['Does\x20not\x20apply']};await postViaNetworkRequest(_0x2587e1(0xf7),_0x321e29),await waitForNetworkResponseCountIncrease();}if(_0x7a1324){var _0x3586b7=await checkIfGPSRAdded();if(!_0x3586b7){var _0x91743b=await setGSPRWithClicks(_0x434e96[_0x2587e1(0x185)],_0x434e96[_0x2587e1(0x182)],![]);console[_0x2587e1(0x143)](_0x2587e1(0x19f),_0x91743b);}}documentTitle=_0x2587e1(0xfd),await switchToAdvancedShippingOptionsUK(),console[_0x2587e1(0x143)](_0x2587e1(0x1a8)),documentTitle=_0x2587e1(0x1a8),addSubmitButtonListener(_0x434e96);var {autoSubmitEnabled:_0x5da4fb}=await chrome[_0x2587e1(0xea)]['local']['get'](_0x2587e1(0xda));_0x5da4fb&&await submitTheListing();}function addSubmitButtonListener(_0x342e40){var _0x46d744=a0_0x48f7b2,_0x4ab197=getSubmitButton();console[_0x46d744(0x143)](_0x46d744(0x120),_0x4ab197),_0x4ab197[_0x46d744(0x1bc)](_0x46d744(0x18b),async function(){var _0x50de6e=_0x46d744;console[_0x50de6e(0x143)]('submitButton\x20clicked');var _0x2b1922=await checkIfItemWasSuccessfullyPosted(),_0x509cd3='';try{_0x509cd3=await getSuccessLink();}catch(_0x4bf385){console['log'](_0x50de6e(0x181));}if(_0x2b1922)console[_0x50de6e(0x143)]('Item\x20Listed\x20Successfully!,\x20sending\x20message\x20to\x20background.js'),chrome[_0x50de6e(0xfc)][_0x50de6e(0x1ab)]({'type':'itemListed','sku':_0x342e40[_0x50de6e(0x11d)],'message':_0x50de6e(0x163),'ebayItemLink':_0x509cd3,'eventType':_0x50de6e(0x133)}),console[_0x50de6e(0x143)](_0x50de6e(0xe4)),changeFaviconOfPage(_0x50de6e(0x11f)),documentTitle=_0x50de6e(0x178),addSkuToSkuListAndSaveToLocalStorage(_0x342e40[_0x50de6e(0x11d)]);else{var _0x2ba43b=getPageErrorMessage();console[_0x50de6e(0x143)](_0x50de6e(0xed),_0x2ba43b),!_0x2ba43b&&(_0x2ba43b='Item\x20Failed\x20to\x20List!\x20'+window[_0x50de6e(0x12e)]['href']),chrome[_0x50de6e(0xfc)][_0x50de6e(0x1ab)]({'type':'itemFailed','sku':_0x342e40['sku'],'message':_0x2ba43b,'eventType':_0x50de6e(0x133)}),changeFaviconOfPage('https://www.freeiconspng.com/uploads/error-icon-4.png'),documentTitle=_0x50de6e(0x1a0);}});}function getPageErrorMessage(){var _0x343f6a=a0_0x48f7b2,_0x3768cd=document[_0x343f6a(0x1c2)]('.page-notice__main');if(_0x3768cd){var _0x48631f=_0x3768cd[_0x343f6a(0x1bd)]+'\x20'+window[_0x343f6a(0x12e)][_0x343f6a(0xf8)];return _0x48631f;}else return null;}async function uploadImageConfig(_0x5be814){var _0x4feca4=a0_0x48f7b2,_0xdc6397=[],_0x4f9617=_0x5be814[_0x4feca4(0x157)],_0x1f7f37=_0x5be814[_0x4feca4(0x1aa)];console[_0x4feca4(0x143)](_0x4feca4(0xec),_0x4f9617[_0x4feca4(0x1b5)]),console['log'](_0x4feca4(0x121),_0x1f7f37[_0x4feca4(0x1b5)]),console[_0x4feca4(0x143)](_0x4feca4(0x1a2),typeof _0x4f9617),console[_0x4feca4(0x143)](_0x4feca4(0x14d),typeof _0x1f7f37),_0xdc6397=_0x4f9617;_0x4f9617['length']<0x2&&_0x1f7f37[_0x4feca4(0x1b5)]>0x2?_0xdc6397=_0x1f7f37:_0xdc6397=_0x4f9617;var _0x1401ce=_0x5be814[_0x4feca4(0xf5)];documentTitle=_0x4feca4(0xef);var _0x54f305=_0x5be814[_0x4feca4(0x18d)];console[_0x4feca4(0x143)](_0x4feca4(0xe2)),await uploadImageAndWaitForCounterToUpload(_0x54f305,_0x1401ce[_0x4feca4(0x19d)](0x0,0x64)+'-'+0x1+_0x4feca4(0x1b3),IMAGES_TYPES['BASE_64'],0x0),console[_0x4feca4(0x143)](_0x4feca4(0x152)),await waitForNetworkResponseCountIncrease(),console[_0x4feca4(0x143)](_0x4feca4(0x1ad)),console[_0x4feca4(0x143)]('uploading\x20other\x20images'),console[_0x4feca4(0x143)](_0x4feca4(0x136),_0xdc6397[_0x4feca4(0x1b5)]),documentTitle=_0x4feca4(0x12c);var {onlyListOnePicture:_0x42f299}=await chrome['storage'][_0x4feca4(0x166)][_0x4feca4(0x10d)](_0x4feca4(0x106));!_0x42f299&&await pasteImages(_0xdc6397,_0x1401ce,_0x5be814);try{var {shouldGetGspr:_0x12c8f0}=await chrome[_0x4feca4(0xea)][_0x4feca4(0x166)]['get']('shouldGetGspr');if(_0x12c8f0){var _0x4850a0=await generateGPSRPicture(_0x5be814[_0x4feca4(0x17d)],_0x5be814[_0x4feca4(0x141)]),_0x22a8d7=_0x4850a0[_0x4feca4(0x12d)];await uploadImage(_0x22a8d7,_0x4feca4(0x145),_0x4feca4(0x18e),IMAGES_TYPES[_0x4feca4(0x167)]);}}catch(_0x102d81){console['log'](_0x4feca4(0xfb));}documentTitle='Done\x20Uploading\x20Images';}async function waitForDescriptionNetworkResponse(){return new Promise((_0x448ef3,_0x2e99ce)=>{async function _0x3ba4b7(){var _0x20d5c8=a0_0x7d53;receivedDescriptionNetworkResponse===!![]?(documentTitle=_0x20d5c8(0x112),console[_0x20d5c8(0x143)](_0x20d5c8(0x110)),_0x448ef3()):(documentTitle=_0x20d5c8(0x13c),setTimeout(async()=>{await _0x3ba4b7();},0x64));}_0x3ba4b7();});}async function waitForNetworkResponseCountIncrease(){let _0x3769f2=totalNetworkResponse;return new Promise((_0x5eb352,_0x4f44fa)=>{async function _0x3cd064(_0x3f4ff4=0x0){var _0xfd71c3=a0_0x7d53;totalNetworkResponse>_0x3769f2?(waitSomeTime(0x64),_0x5eb352()):(_0x3f4ff4++,_0x3f4ff4%0x3c===0x0&&(console[_0xfd71c3(0x143)](_0xfd71c3(0x177),_0x3f4ff4),console[_0xfd71c3(0x143)](_0xfd71c3(0x169)),await makeTabActive()),setTimeout(async()=>{await _0x3cd064(_0x3f4ff4);},0x64)),_0x3f4ff4>0x3c&&(totalNetworkResponse=_0x3769f2+0x1,_0x5eb352());}_0x3cd064();});}function makeTabActive(){}function waitUntilDocumentIsVisible(){return new Promise((_0x34a5aa,_0x19a119)=>{var _0x186ada=setInterval(()=>{var _0x4abec9=a0_0x7d53;document[_0x4abec9(0x165)]===_0x4abec9(0x14e)&&(clearInterval(_0x186ada),_0x34a5aa());},0x32);});}function uploadImageAndWaitForCounterToUpload(_0xa74d59,_0x52ba97,_0x468cbf,_0x393559){return new Promise((_0x3febac,_0x48ba7c)=>{var _0x45e8cc=a0_0x7d53;console['log'](_0x45e8cc(0xfa),_0x393559);!_0x393559&&_0x393559!==0x0&&(console[_0x45e8cc(0x143)](_0x45e8cc(0x195)),_0x393559=getTotalImagesUploaded());console['log'](_0x45e8cc(0xfa),_0x393559),uploadImage(_0xa74d59,_0x45e8cc(0x145),_0x52ba97,_0x468cbf);var _0x3ab29a=setInterval(()=>{var _0x464418=getTotalImagesUploaded();_0x464418>_0x393559&&(clearInterval(_0x3ab29a),_0x3febac());},0x3e8);});}function uploadImageToIframeOnEbay(_0x41c35b,_0xe778f2,_0x4ccb77){return new Promise((_0x4554ad,_0x180f48)=>{var _0x398214=a0_0x7d53,_0x274c32={'inputTagSelector':_0x4ccb77,'imageName':_0xe778f2,'b64Image':_0x41c35b};console[_0x398214(0x143)]('uploadImageToIframeOnEbay\x20imageObject',_0x274c32),uploadImage(_0x274c32[_0x398214(0x1b6)],_0x398214(0x145),_0x274c32[_0x398214(0x11e)],IMAGES_TYPES[_0x398214(0x167)]);function _0x27c2a6(_0x1fb6b5){var _0xfa16a7=_0x398214,_0x4b8615=JSON[_0xfa16a7(0x18c)](_0x1fb6b5[_0xfa16a7(0xd9)]);_0x4b8615[_0xfa16a7(0xf3)]==='picUploadUpdate'&&(window[_0xfa16a7(0x1ba)](_0xfa16a7(0x174),_0x27c2a6,![]),_0x4554ad());}});}function a0_0xaf15(){var _0x274fce=['push','universalProductCode','Error:','onlyListOnePicture','center','scheduleListingTime','465300yCJKYh','label','join','actionbar','get','listingType','encode','receivedDescriptionNetworkResponse\x20received','[value=\x27List\x20item\x27]','Received\x20Description\x20Network\x20Response','promoted_listing_ad_rate\x20from\x20storage','Brand','amazon','isRestricted:\x20','complete','price','3177584LJyOPS','7.1.1','itemFailed','.summary__promoted-listing','sku','imageName','https://favicon-generator.org/favicon-generator/htdocs/favicons/2014-12-02/b14c9dab43e1876748e6262327d99884.ico','submitButton\x20initiated','mainSdImages','error\x20enabling\x20sku','postMessage','Inserted\x20Script','style','requestHeaders','receive\x20page\x20load\x20waitForPicUploadIFrameToLoad','value','useCustomDescriptionPrompt','Title\x20Pasted','replaceAll','Uploading\x20Images','src','location','error\x20setting\x20promoted_listing_ad_rate','networkResponse','append','Unbranded','listing_result','input[name=\x22promotedListingSelection\x22]','replace','images','basicDescription','trim','Details\x20In\x20Description','button[innerHTML=\x27','type','Waiting\x20For\x20Description\x20Network\x20Response','Error\x20Posting\x20Item:\x20','pageLoaded','promotedListingSelection','remove_specs','responsiblePersonEUText','Promoting\x20Listing','log','pageLoaded_for_promoted_listing','#fehelix-uploader','switchToWindowAndTab','.promoted-listing-program-wrapper\x20input[type=\x22checkbox\x22]','chatGpt','then','adRate','visibility','adding\x20gspr','mainSdImages\x20type','visible','free','3fNhWoC','Paste\x20Title\x20Done','uploading\x20selected\x20image\x20done','Does\x20Not\x20Apply','product.listingType','close_tab','pasteDescriptionFromOpenAi','main_hd_images','1284516EWURDs','stringify','error\x20setting\x20gspr','adding\x20gspr\x20to\x20description','name','draftId','contentWindow','send_message_to_promoted_listing_to_click_promoted_listing_button','Started\x20inserting\x20product','useBrand','12RSExfs','Item\x20Listed\x20Successfully!','shouldFillRequiredItemSpecifics\x20is\x20true,\x20filling\x20required\x20item\x20specifics\x20with\x20AI','visibilityState','local','BASE_64','.summary__price-fields\x20.se-textbox--input','make\x20tab\x20active','3639993ZqbhfZ','addListener','Adding\x20The\x20AI\x20Description','uploadImageConfig','update_gspr_on_listing','promotedListing_iframe','error\x20scheduling\x20the\x20listing','Adding\x20The\x20free\x20Description','success_msg','title','message','filter','shouldFillRequiredItemSpecifics\x20is\x20false,\x20not\x20filling\x20required\x20item\x20specifics\x20with\x20AI','waiting\x20for\x20network\x20response\x20count\x20increase','Submitted\x20the\x20Listing!','description','PUT','.smry.summary__title\x20input[name=\x27title\x27]','promoted_listing_ad_rate','manufacturerInfoText','input[name=\x22Toggle\x20General\x22]','filteredItemSpecifics','stopPropagation','error\x20getting\x20success\x20link','responsiblePersonEU','paid','testMain','manufacturerInfo','https://','PLDataUpdate','shouldFillOptionalItemSpecifics','error\x20filling\x20optional\x20item\x20specifics','shouldFillRequiredItemSpecifics','click','parse','selected_image','GPSR.jpg','shouldFillOptionalItemSpecifics\x20is\x20','paid2','Adding\x20Title','error\x20pasting\x20brand','split','tab','totalImagesUploaded\x20is\x20undefined\x20or\x20another\x20falsy\x20value\x20(but\x20not\x20zero)','appendChild','onMessage','europeanArticleNumber','From\x20the\x20extension\x20request.type\x20ebay.js','round','shouldGetGspr','From\x20a\x20content\x20script:','substring','itemSpecifics','isNewGPSRSet','Failed\x20to\x20Submit\x20the\x20Listing!','1081320HfMiFU','mainHdImages\x20type','request','domain','required','177wwldYz','response:\x20','Ready\x20To\x20Submit!','insert_ebay_data','main_sd_images','sendMessage','suggestedQuery','waitForNetworkResponseCountIncrease\x20Done','descriptionText','smooth','71524MymAkS','pop','postEbayData\x20begins','.jpg','gsprSet','length','b64Image','Marke','error','#gh','removeEventListener','Adding\x20Price','addEventListener','textContent','ebay.js','forceItemLocationSwitch','url','?mode=AddItem&forceValidation=true','querySelector','gspr','error\x20filling\x20required\x20item\x20specifics','data','autoSubmitEnabled','7.1','scheduleListingTime\x20is\x20true','ebay_description_network_response','waitForRequestHeaders\x20Done','innerHTML','productData','2276DAiAdb','uploading\x20selected\x20image','/lstng/api/listing_draft/','Item\x20Listed\x20Successfully!,\x20sent\x20message\x20to\x20background.js','error\x20pasting\x20sku','DraftURL\x20product','Response:','co.uk','Adding\x20The\x20SKU','storage','requestVariables','mainHdImages','pageErrorMessage','getElementById','Uploading\x20Selected\x20Image','Adding\x20The\x20descriptionHtml','Does\x20not\x20apply','7.3','cmd','optional','custom_title','\x20waitForNetworkResponseCountIncrease\x20Done','attributes','href','readyState','totalImagesUploaded','error\x20uploading\x20gpsr\x20image','runtime','Switching\x20to\x20Advanced\x20Shipping\x20Options','brand','Adding\x20Item\x20Specifics','details','Scheduling\x20the\x20Listing','applyPromotedListingV2'];a0_0xaf15=function(){return _0x274fce;};return a0_0xaf15();}function waitForPicUploadIFrameToLoad(){return new Promise((_0xe400c5,_0x522f07)=>{var _0xc0df31=a0_0x7d53;window[_0xc0df31(0x1bc)](_0xc0df31(0x174),_0x4c69cc,![]);function _0x4c69cc(_0x5eea34){var _0x159dbc=_0xc0df31,_0x474661=_0x5eea34[_0x159dbc(0xd9)];console['log'](_0x159dbc(0x127),_0x474661),(_0x474661[_0x159dbc(0x13e)]||_0x474661[_0x159dbc(0x144)])&&(console[_0x159dbc(0x143)]('receive\x20pageLoaded'),window[_0x159dbc(0x1ba)]('message',_0x4c69cc,![]),_0xe400c5());}});}function waitForPromotedListingIFrameToLoad(){return new Promise((_0x3ebae9,_0xe504a0)=>{window['addEventListener']('message',_0x34d0f4,![]);function _0x34d0f4(_0x32aef0){var _0x57e929=a0_0x7d53,_0x52c3aa=_0x32aef0[_0x57e929(0xd9)];_0x52c3aa[_0x57e929(0x144)]&&(window[_0x57e929(0x1ba)](_0x57e929(0x174),_0x34d0f4,![]),_0x3ebae9());}});}function promoteListing(_0x531733){return new Promise((_0x4e39dd,_0x3ece0c)=>{var _0x279daa=a0_0x7d53,_0x275664=document['getElementById'](_0x279daa(0x16f));_0x275664[_0x279daa(0x15e)][_0x279daa(0x123)]({'type':_0x279daa(0x15f),'adRate':_0x531733},_0x275664[_0x279daa(0x12d)]),window[_0x279daa(0x1bc)](_0x279daa(0x174),_0x51925a,![]);function _0x51925a(_0x5850b7){var _0x5c189e=_0x279daa,_0x5bc84a=JSON[_0x5c189e(0x18c)](_0x5850b7[_0x5c189e(0xd9)]);_0x5bc84a[_0x5c189e(0xf3)]===_0x5c189e(0x187)&&(window[_0x5c189e(0x1ba)](_0x5c189e(0x174),_0x51925a,![]),_0x4e39dd());}});}async function listItem(){return new Promise((_0x975e9a,_0x377632)=>{var _0x57e5a0=a0_0x7d53,_0x2cf438=document[_0x57e5a0(0xee)](_0x57e5a0(0x10c)),_0x189bca=_0x2cf438[_0x57e5a0(0x1c2)](_0x57e5a0(0x111));_0x189bca[_0x57e5a0(0x18b)](),_0x189bca[_0x57e5a0(0x18b)](),_0x975e9a();});}function checkIfListingSucceeded(){return new Promise((_0x99f343,_0x50b9c0)=>{var _0x5b8f7a=a0_0x7d53,_0x48d9ca=document[_0x5b8f7a(0xee)](_0x5b8f7a(0x172));_0x48d9ca[_0x5b8f7a(0x125)][_0x5b8f7a(0x14b)]!==_0x5b8f7a(0x14e)&&_0x99f343(![]),_0x48d9ca[_0x5b8f7a(0x125)][_0x5b8f7a(0x14b)]===_0x5b8f7a(0x14e)&&_0x99f343(!![]);});}function waitSomeTime(_0x4b3ca3){return new Promise((_0x307fa6,_0x310795)=>{setTimeout(()=>{_0x307fa6();},_0x4b3ca3);});}function tellChromeToCloseTab(){var _0xc0a609=a0_0x48f7b2;chrome[_0xc0a609(0xfc)][_0xc0a609(0x1ab)]({'type':_0xc0a609(0x155)},function(_0xb12230){});}function tellChromeToSwitchToPreviousTab(){return new Promise((_0x4981ab,_0x381642)=>{var _0x38801e=a0_0x7d53;console[_0x38801e(0x143)]('previousTabId:\x20',previousTabId),console[_0x38801e(0x143)]('previousWindowId:\x20',previousWindowId),previousTabId&&previousWindowId?chrome[_0x38801e(0xfc)][_0x38801e(0x1ab)]({'type':_0x38801e(0x146),'windowId':previousWindowId,'tabId':previousTabId},function(_0xf73811){var _0x4050db=_0x38801e;console[_0x4050db(0x143)](_0x4050db(0x1a7),_0xf73811),_0x4981ab();}):_0x4981ab();});}function scrollIntoView(_0xf8103c){var _0x375795=a0_0x48f7b2;_0xf8103c['scrollIntoView']({'behavior':_0x375795(0x1af),'block':'center','inline':_0x375795(0x107)});}function a0_0x7d53(_0x1a446a,_0x359503){var _0xaf153a=a0_0xaf15();return a0_0x7d53=function(_0x7d5368,_0x429a9f){_0x7d5368=_0x7d5368-0xd7;var _0x451c2a=_0xaf153a[_0x7d5368];return _0x451c2a;},a0_0x7d53(_0x1a446a,_0x359503);}function createButtonToExecuteFunctionWithParameter(_0x5a945e,_0x5c2f27,_0xa766eb){var _0x58d36f=a0_0x48f7b2,_0x17ca72=document['createElement']('button');_0x17ca72[_0x58d36f(0xdf)]=_0x5a945e,_0x17ca72['onclick']=function(_0x5bb27e){var _0x158d71=_0x58d36f;_0x5bb27e[_0x158d71(0x180)](),window[_0x5a945e](_0xa766eb);},_0x5c2f27[_0x58d36f(0x1c2)](_0x58d36f(0x13a)+_0x5a945e+'\x27]')===null&&_0x5c2f27[_0x58d36f(0x1c2)](_0x58d36f(0x13a)+_0x5a945e+'\x27]')===undefined&&_0x5c2f27[_0x58d36f(0x196)](_0x17ca72);}function createManuelInputButtons(_0x3f9740){var _0x2d4c51=a0_0x48f7b2,_0x1a2029=document[_0x2d4c51(0x1c2)]('.summary__description');createButtonToExecuteFunctionWithParameter(_0x2d4c51(0x156),_0x1a2029,_0x3f9740);var _0x1a2029=document[_0x2d4c51(0x1c2)]('.summary__title\x20.se-textbox--fluid');createButtonToExecuteFunctionWithParameter('pasteTitle',_0x1a2029,_0x3f9740[_0x2d4c51(0xf5)]);var _0x1a2029=document[_0x2d4c51(0x1c2)](_0x2d4c51(0x168));createButtonToExecuteFunctionWithParameter('pastePrice',_0x1a2029,Math[_0x2d4c51(0x19a)](_0x3f9740[_0x2d4c51(0x118)]*1.25));var _0x1a2029=document[_0x2d4c51(0x1c2)](_0x2d4c51(0x11c));createButtonToExecuteFunctionWithParameter(_0x2d4c51(0x102),_0x1a2029,null);var _0x1a2029=document['querySelector']('.smry.summary__photos');createButtonToExecuteFunctionWithParameter(_0x2d4c51(0x16d),_0x1a2029,_0x3f9740);}function checkIfTotalNetworkResponseIncreased(_0x2f33bb,_0x40dcb4){window[_0x2f33bb](_0x40dcb4),setInterval(function(){totalNetworkResponse>previousTotalNetworkResponse&&(previousTotalNetworkResponse=totalNetworkResponse,window[_0x2f33bb](_0x40dcb4));},0x3e8);}