<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product × Hunter</title>
    <link rel="stylesheet" href="product_finder.css">
</head>

<body>
    <h1>Product × Hunter</h1>

    <div id="myModal"
        style="display: none; max-width: 600px; padding: 20px; margin: 100px auto; background-color: #fff; border: 1px solid #000; position: fixed; left: 0; right: 0; z-index: 1000; overflow: auto;">

        <span id="closeModal" style="float: right; cursor: pointer;">❌</span>
        <h1>Amazon Links</h1>
        <textarea id="myTextarea" style="width: 100%; height: 200px;"></textarea>
        <button id="myButton">Copy All Links</button>


<!-- 
        <label for="batchSize">Batch Size:</label>
        <input type="number" id="batchSize" value="5" min="1" style="margin-bottom: 10px;">

        <button id="shuffleLinks">Shuffle Links</button>
        <div id="batchesContainer"></div> -->


        <p id="totalLinks">0</p>
    </div>


    <div id="filterModal">
        <div class="modal-content">
            <span id="closeFilterModal">&times;</span>

            <h1>Filter Results <span id="itemPosition">0</span>/<span id="totalItems">0</span></h1>

            <p id="modalContent">Some text in the Modal..</p>

            <div class="modal-buttons">

                <button id="prevButton">Prev</button>
                <button id="nextButton">Next</button>

            </div>

        </div>
    </div>







    <div class="container">

        <p>Results</p>
        <div class="result-container" id="resultContainer">
            <div class="textarea-wrapper">
                <div>
                    <table id="productTable">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Price</th>
                                <th>Reviews</th>
                                <th style="display: none;">Asin</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Table rows will be dynamically added here -->
                        </tbody>
                    </table>
                </div>
                <p id="totalResults">0</p>
                <button id="clearResults" class="btn clear">Clear</button>
                <button id="exportResults" class="btn exportResults">Export</button>
                <button id="quickFilter" class="btn" style="display: none;">Quick Filter</button>

                <button id="openResults" class="btn" style="display: none;">Open Results</button>

                <button id="open-duplicate-skus-10" style="display: none;">Open Results (5 at a time)</button>
                <button id="open-advanced-filter" style="display: none;">Open Advanced Filter</button>

                <p style="display: none;">Choose Batch Number: <input type="number" id="duplicate-batch-number" value="1" min="1"
                        max="10">/<span id="duplicate-batch-total">0</span></p>


                        <div id="filterResultsContainer" style="display: none;">
                            <div>
                                <label for="minPriceForResults">Min Price:</label>
                                <input type="number" id="minPriceForResults" value="0">
                                <label for="maxPriceForResults">Max Price:</label>
                                <input type="number" id="maxPriceForResults" value="1000">
                            </div>
                            <div>
                                <label for="minReviewsForResults">Min Reviews:</label>
                                <input type="number" id="minReviewsForResults" value="0">
                                <label for="maxReviewsForResults">Max Reviews:</label>
                                <input type="number" id="maxReviewsForResults" value="1000000">
                            </div>
                            <button id="applyFiltersForResults">Apply Filters</button>
                            <button id="sortButtonForResults">Sort Price: High to Low</button>
                        </div>
                        


            </div>

            <div class="progress-container">
                <p>Progress <span class="progress-percentage"></span></p>
                <div id="progress-bar">
                    <span class="progress-percentage"></span>
                </div>
            </div>



        </div>






        <div class="search-container">
            <p>Enter a title or keyword to search for</p>
            <div class="textarea-wrapper">
                <div>
                    <textarea id="searchTerms" rows="10" cols="100"></textarea>
                </div>
                <button id="clearTerms" class="btn clear">Clear</button>
                <button id="importTerms" class="btn exportResults">Import</button>
            </div>
            <p id="totalSearchTerms"></p>
            <button id="searchTitles" class="btn searchTitle">Search Titles</button>
            <button id="searchKeywords" class="btn searchTitle" style="display: none;">Search Keywords (Finds the Meaning behind the
                Title)</button>
            <button id="generateRandomWords" class="btn" style="display: none;">Generate 100 Random Words</button>
            <button id="openKeywordTool" class="btn" style="display: none;">Open Keyword Tool</button>

            <div id="loadingBarContainer" class="loading-bar-container">
                <div id="loadingBar" class="loading-bar"></div>
            </div>

            <input type="number" id="searchTermPosition" value="0" min="0" max="9999">/<span
                id="searchTermTotal">0</span> Total Search Terms
        </div>



        <div class="settings-container">
            <h2>Settings</h2>
            <p>Number of items to extract for each search term:</p>
            <input type="number" id="numberToExtract" value="2" min="1">

            <div class="filter-container">
                <h4 id="filter_heading">Filters:</h4>
                <label for="minPrice">Min Price</label>
                <input type="number" id="minPrice" name="minPrice" value="0" min="0" max="9999">
                <label for="maxPrice">Max Price</label>
                <input type="number" id="maxPrice" name="maxPrice" value="100" min="0" max="9999">

                <div>
                    <input type="checkbox" id="prioritizeAmazonChoice" name="amazonChoice" value="amazonChoice" checked>
                    <label for="prioritizeAmazonChoice">Amazon Choice (Prioritize)</label>
                </div>
                <div>
                    <input type="checkbox" id="prioritizeBestSellers" name="bestSeller" value="bestSeller" checked>
                    <label for="prioritizeBestSellers">Best Seller (Prioritize)</label>
                </div>

                <div>
                    <input type="checkbox" id="sort-by-reviews" value="sort-by-reviews" checked>
                    <label for="sort-by-reviews">Sort By: Highest Reviewed</label>
                </div>

                <div>
                    <input type="checkbox" id="duplicate_protection" value="dont-get-duplicate" checked>
                    <label for="duplicate_protection">Don't Get Duplicate Item</label>
                </div>

                <div>
                    <input type="checkbox" id="vero_protection_switch" value="dont-get-duplicate" checked>
                    <label for="vero_protection_switch">Don't Get Vero Item</label>
                </div>

                <div>
                    <input type="checkbox" id="remove_books" value="remove_books" checked>
                    <label for="remove_books">Remove Books</label>
                </div>

                <div>
                    <input type="checkbox" id="amazon_is_seller" value="amazon_is_seller">
                    <label for="amazon_is_seller">Amazon is the Seller</label>
                </div>

                <div>
                    <input type="checkbox" id="required_keywords_checkbox">
                    <label for="required_keywords_checkbox">Required Keywords In Title - <a href="#" data-modal-target="#keywordModal">click here</a> to set your required keyword list</label>
                </div>

                <div>
                    <select id="product_hunter_supplier">
                        <option value="amazon">Amazon</option>
                        <option value="home-depot">Home Depot</option>
                    </select>
                    <label for="product_hunter_supplier"> - Choose Supplier</label>
                </div>

                <!-- Keyword Modal -->
             <!-- Keyword Modal -->
                <div class="keyword-modal modal" id="keywordModal" style="display: none;">
                    <div class="modal-content">
                        <span class="close" style="cursor: pointer;">&times;</span>
                        <h2>Set Your Required Keywords</h2>
                        <p>Enter each keyword on a new line. Specify whether all keywords or at least one must be present in the product titles when hunting for products.</p>
                        <textarea id="keywordsArea" placeholder="Enter keywords separated by new lines"></textarea>

                        <div class="required_keyword_radio_container">
                            <div>
                                <input type="radio" id="allKeywords" name="keywordRequirement" value="all" checked>
                                <label for="allKeywords">All keywords must be present</label>
                            </div>
                            <div>
                                <input type="radio" id="anyKeywords" name="keywordRequirement" value="any">
                                <label for="anyKeywords">At least one keyword must be present</label>
                            </div>
                        </div>
                 

                    </div>
                </div>


                <div>
                    <input type="number" id="min_reviews" value="100" min="0" max="9999">
                    <label for="min_reviews">Minimum Reviews</label>
                </div>

                <div>
                    <input type="number" id="max_reviews" value="100000" min="0" max="999999">
                    <label for="max_reviews">Maximum Reviews</label>
                </div>

                <div>
                    <input type="number" id="max_similiar_niche_items" value="2" min="0" max="9999">
                    <label for="max_similiar_niche_items">Maximum Similiar Niche Items (This means if you already listed
                        x amount of items for the search query, itll skip it)</label>
                </div>

                <label for="restricted_words">Don't Get Items With These Words (Seperate with newlines)</label>
                <div>
                    <textarea id="restricted_words" rows="10" cols="100"></textarea>
                </div>


            </div>

            <div id="pause_resume_buttons">
                <button id="pause_button">Pause</button>
                <button id="resume_button">Resume</button>
            </div>
        </div>


    </div>


    <script src="product_finder.js"></script>
</body>

</html>