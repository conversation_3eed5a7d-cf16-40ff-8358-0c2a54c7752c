#fetch-skus-container {
    display: flex;
    flex-direction: column;
}

#fetch-skus-container label {
    margin-bottom: 5px;
}

#fetch-skus {
    font-size: 16px;
    padding: 10px 20px;
    margin-right: auto;
}

#page-number {
    font-size: 16px;
    padding: 10px 20px;
    margin-right: auto;
}

#results-per-page-select {
    font-size: 16px;
    padding: 10px 20px;
    margin-right: auto;
}

#sku_buttons {
    font-size: 16px;
    padding: 10px 20px;
    margin-top: 10px;

    margin-left: auto;
}



#add-skus-container {
    display: flex;
    flex-direction: row;
}

#add-skus-container {
    margin-bottom: 30px;
}


.convert-container {
    display: flex;
    flex-direction: row;
}

.row-container {
    display: flex;
    flex-direction: row;
    margin-bottom: 30px;
}



.animation {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    animation: fadeOut 3s ease-in-out;
    font-size: 24px;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}


.copied {
    color: white;
    background-color: #4CAF50;
}

.un-copied {
    color: white;
    background-color: #36a8f4;
}