var a0_0x47f536=a0_0x1744;(function(_0x52fc33,_0x284b48){var _0x5d33b8=a0_0x1744,_0x490905=_0x52fc33();while(!![]){try{var _0x3ee56d=-parseInt(_0x5d33b8(0x14f))/0x1*(parseInt(_0x5d33b8(0x118))/0x2)+-parseInt(_0x5d33b8(0xf9))/0x3*(parseInt(_0x5d33b8(0x145))/0x4)+-parseInt(_0x5d33b8(0xf7))/0x5+parseInt(_0x5d33b8(0x14d))/0x6*(parseInt(_0x5d33b8(0x155))/0x7)+parseInt(_0x5d33b8(0x15a))/0x8+parseInt(_0x5d33b8(0x14e))/0x9+-parseInt(_0x5d33b8(0x159))/0xa;if(_0x3ee56d===_0x284b48)break;else _0x490905['push'](_0x490905['shift']());}catch(_0x348eff){_0x490905['push'](_0x490905['shift']());}}}(a0_0x4e17,0x437c6),console[a0_0x47f536(0x125)]('Amazon.js\x20loaded'));var requestParams,product_data,detailsAddedToTitle=0x0,div=document['createElement'](a0_0x47f536(0x104));div['id']='listingToEbayUi';var divTable=document[a0_0x47f536(0x126)](a0_0x47f536(0x104));divTable['id']=a0_0x47f536(0x148);var divButtons=document[a0_0x47f536(0x126)](a0_0x47f536(0x104));divButtons['id']='listingToEbayUiButtons';var divStatus=document[a0_0x47f536(0x126)](a0_0x47f536(0x104));divStatus['id']=a0_0x47f536(0x120);var imagesContainer=document[a0_0x47f536(0x126)](a0_0x47f536(0x104));imagesContainer['id']=a0_0x47f536(0x121),div['appendChild'](imagesContainer),div[a0_0x47f536(0x15f)](divTable),div['appendChild'](divStatus),div['appendChild'](divButtons),document[a0_0x47f536(0x131)][a0_0x47f536(0x100)](div,document[a0_0x47f536(0x131)]['firstChild']),makeImagesDraggable(),chrome[a0_0x47f536(0x142)][a0_0x47f536(0x12c)][a0_0x47f536(0x105)]((_0xd2f2a7,_0x420130,_0x336656)=>{var _0x17551c=a0_0x47f536;requestParams=_0xd2f2a7;if(_0xd2f2a7[_0x17551c(0x150)]==='list_item_to_ebay')return listItemToEbayAutomatically(_0xd2f2a7)[_0x17551c(0x160)](function(_0x1ee381){console['log']('listItemToEbayAutomatically\x20response:\x20',_0x1ee381);var _0x95c0f3=_0x1ee381;_0x336656({'autoListing':_0x95c0f3});}),!![];if(_0xd2f2a7['type']===_0x17551c(0x116))return console[_0x17551c(0x125)]('get_supplier_product_data'),listItemToEbayAutomatically(_0xd2f2a7,_0x17551c(0x113))[_0x17551c(0x160)](function(_0x3ab3af){var _0x4948b4=_0x17551c;console[_0x4948b4(0x125)]('listItemToEbayAutomatically\x20response:\x20',_0x3ab3af);var _0x300fe7=_0x3ab3af;_0x336656({'autoListing':_0x300fe7});}),!![];if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x103))return listItemToEbayAutomatically(_0xd2f2a7,mode=_0x17551c(0x103))['then'](function(_0x7dbfe5){var _0x16d6d3=_0x17551c;console[_0x16d6d3(0x125)](_0x16d6d3(0xff),_0x7dbfe5);var _0x3dc007=_0x7dbfe5;_0x336656({'autoListing':_0x3dc007});}),!![];if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x138)){var _0x49a157=_0xd2f2a7[_0x17551c(0x13b)];_0x49a157=_0x49a157[_0x17551c(0xf6)](/\w\S*/g,function(_0x347a8e){var _0x570625=_0x17551c;return _0x347a8e['charAt'](0x0)['toUpperCase']()+_0x347a8e[_0x570625(0x102)](0x1)[_0x570625(0x137)]();});detailsAddedToTitle==0x0&&(_0x49a157=_0x17551c(0x11e)+_0x49a157);detailsAddedToTitle===0x1&&(_0x49a157=_0x17551c(0x15e)+_0x49a157);detailsAddedToTitle===0x2&&(_0x49a157=_0x17551c(0x109)+_0x49a157);detailsAddedToTitle===0x3&&(_0x49a157='\x20and\x20'+_0x49a157);var _0x1628cc=document[_0x17551c(0x12b)](_0x17551c(0x10b));_0x1628cc[_0x17551c(0x154)]+=_0x49a157,detailsAddedToTitle++,updateCharacterCount();}if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x14c)){var _0x49a157=_0xd2f2a7[_0x17551c(0x13b)];_0x49a157=_0x49a157[_0x17551c(0xf6)](/\w\S*/g,function(_0x54a7f6){var _0x5c10e2=_0x17551c;return _0x54a7f6[_0x5c10e2(0xfb)](0x0)[_0x5c10e2(0x133)]()+_0x54a7f6[_0x5c10e2(0x102)](0x1)[_0x5c10e2(0x137)]();}),_0x49a157=_0x17551c(0x11a)+_0x49a157;var _0x1628cc=document[_0x17551c(0x12b)]('the-textarea');_0x1628cc[_0x17551c(0x154)]+=_0x49a157,updateCharacterCount();}if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x156)){var _0x49a157=_0xd2f2a7[_0x17551c(0x13b)];_0x49a157=_0x49a157[_0x17551c(0xf6)](/\w\S*/g,function(_0x4e4eba){var _0x12f6aa=_0x17551c;return _0x4e4eba[_0x12f6aa(0xfb)](0x0)[_0x12f6aa(0x133)]()+_0x4e4eba[_0x12f6aa(0x102)](0x1)[_0x12f6aa(0x137)]();}),_0x49a157='\x20and\x20'+_0x49a157;var _0x1628cc=document['getElementById'](_0x17551c(0x10b));_0x1628cc[_0x17551c(0x154)]+=_0x49a157,updateCharacterCount();}if(_0xd2f2a7[_0x17551c(0x150)]==='With-Add-To-Custom-Title'){var _0x49a157=_0xd2f2a7['selectionText'];_0x49a157=_0x49a157[_0x17551c(0xf6)](/\w\S*/g,function(_0x5f56c9){var _0x2b9fc1=_0x17551c;return _0x5f56c9[_0x2b9fc1(0xfb)](0x0)[_0x2b9fc1(0x133)]()+_0x5f56c9['substr'](0x1)[_0x2b9fc1(0x137)]();}),_0x49a157=_0x17551c(0x11e)+_0x49a157;var _0x1628cc=document[_0x17551c(0x12b)](_0x17551c(0x10b));_0x1628cc[_0x17551c(0x154)]+=_0x49a157,updateCharacterCount();}if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x11b)){var _0x49a157=_0xd2f2a7[_0x17551c(0x13b)];_0x49a157=_0x49a157[_0x17551c(0xf6)](/\w\S*/g,function(_0x3c1a80){var _0x394a06=_0x17551c;return _0x3c1a80[_0x394a06(0xfb)](0x0)['toUpperCase']()+_0x3c1a80[_0x394a06(0x102)](0x1)[_0x394a06(0x137)]();}),_0x49a157='\x20'+_0x49a157;var _0x1628cc=document['getElementById'](_0x17551c(0x10b));_0x1628cc[_0x17551c(0x154)]+=_0x49a157,updateCharacterCount();}if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0x13d)){var _0x2408d5=_0xd2f2a7[_0x17551c(0x164)],_0x344046=[],_0x41e32f=_0x2408d5['asin'],_0x412e8f=_0x2408d5['optimized_title'],_0x4f2c0c=getProductDescriptionAndFeatures(),_0x1c83f7=getProductTitle();_0x344046[_0x17551c(0x12f)]({'key':_0x17551c(0x114),'value':_0x4f2c0c}),_0x344046['push']({'key':_0x17551c(0x13e),'value':_0x1c83f7}),_0x344046['push']({'key':_0x17551c(0x11d),'value':_0x41e32f}),_0x344046[_0x17551c(0x12f)]({'key':_0x17551c(0x11f),'value':_0x412e8f});var _0x207d01=_0x17551c(0x101);updateGoogleSheets(_0x344046,_0x207d01),_0x336656({'type':_0x17551c(0x10e)});}if(_0xd2f2a7['type']==='get_product_data')return!![];_0xd2f2a7[_0x17551c(0x150)]==='solve_amazon_captcha_on_page'&&solveCaptchaOnPage();if(_0xd2f2a7['type']==='select_image_url'){console[_0x17551c(0x125)](_0x17551c(0x162),_0xd2f2a7);var _0x1e081f=_0xd2f2a7[_0x17551c(0x13c)];_0x1e081f=enlargeAmazonImage(_0x1e081f),urlToImage(_0x1e081f)['then'](function(_0x3f51d1){var _0x267830=_0x17551c;chrome[_0x267830(0x158)][_0x267830(0x11c)]['get']([_0x267830(0x110)],function(_0x50ad94){if(_0x50ad94['useReviewImages']){product_data['main_hd_images']=[];var _0x296c84=createImageAndAppend(_0x3f51d1,!![]);selectImageIndex(_0x296c84);}else{var _0x296c84=createImageAndAppend(_0x3f51d1,![]);selectImageIndex(_0x296c84);}});});}if(_0xd2f2a7[_0x17551c(0x150)]==='select_template_image_url'){console[_0x17551c(0x125)](_0x17551c(0x14a),_0xd2f2a7);var _0x1e081f=_0xd2f2a7[_0x17551c(0x13c)];_0x1e081f=enlargeAmazonImage(_0x1e081f),createImageWithTemplateGivenSource(_0x1e081f);}if(_0xd2f2a7['type']===_0x17551c(0x123)){console[_0x17551c(0x125)](_0x17551c(0x123)),_0x336656({'message_received':!![]});var _0x48f7ee=isCaptchaPage();_0x48f7ee?(chrome[_0x17551c(0x142)][_0x17551c(0xfe)]({'type':'resend_message_to_tab_on_update','message':requestParams}),solveCaptchaOnPage()):getAmazonItemData()['then'](function(_0xee611){var _0xdf1c4b=_0x17551c;chrome[_0xdf1c4b(0x142)][_0xdf1c4b(0xfe)]({'type':_0xdf1c4b(0x12e),'amazonData':_0xee611});});}if(_0xd2f2a7[_0x17551c(0x150)]===_0x17551c(0xfc))return console[_0x17551c(0x125)]('check_if_chinese_seller'),checkIfChineseSeller()[_0x17551c(0x160)](function(_0x1796d5){var _0x3285d7=_0x17551c;console[_0x3285d7(0x125)](_0x3285d7(0x134),_0x1796d5),!_0x1796d5?document[_0x3285d7(0x13e)]=_0x3285d7(0x141)+document[_0x3285d7(0x13e)]:document[_0x3285d7(0x13e)]=_0x3285d7(0x147)+document[_0x3285d7(0x13e)],_0x336656({'isChineseSeller':_0x1796d5});}),!![];if(_0xd2f2a7['type']===_0x17551c(0x111))return console[_0x17551c(0x125)]('get_gspr'),fetchGSPR()[_0x17551c(0x160)](function(_0x34ad08){var _0x1debf4=_0x17551c;console['log'](_0x1debf4(0x140),_0x34ad08),_0x336656({'gspr':_0x34ad08});}),!![];return!![];});function a0_0x4e17(){var _0x22ca38=['\x20also\x20includes\x20','filteredTitle','the-textarea','create-10-title-from-openai-button','#listing-data-table','done_scrape_data_and_update_to_google_sheets','isItemInternational','useReviewImages','get_gspr','text','get_product_data','description','set','get_supplier_product_data','isTheItemVero','482uinaTK','choices','\x20-\x20','Normal-Add-To-Custom-Title','local','asin','\x20with\x20','customTitle','listingToEbayUiStatus','imagesContainer','href','get_amazon_data','getURL','log','createElement','sku','main_sd_images','Title','Action','getElementById','onMessage','Scraping\x20data\x202','amazon_data','push','Total\x20Characters','body','WARNING!\x20NOT\x20PRIME\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','toUpperCase','isChineseSeller','listed_asins_etsy','html','toLowerCase','add_to_custom_title','bullet_points','auto_ai_title_checkbox','selectionText','imageUrl','scrape_data_and_update_to_google_sheets','title','click','gspr','WARNING!\x20NOT\x20CHINESE\x20SELLER\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','runtime','toFixed','customPrice','176iWaloj','listing-data-table','CHINESE\x20SELLER\x20\x20✅-\x20','listingToEbayUiTable','Favicons/Warning/1.ico','select_template_image_url','WARNING!\x20ASIN\x20AND\x20URL\x20NOT\x20THE\x20SAME\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','-Add-To-Custom-Title','4302jZQgGx','4666491HgksIP','815NumVuF','type','create-title-from-openai-button','isPrime','WARNING!\x20USED\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','value','3654HAJadl','And-Add-To-Custom-Title','main_hd_images','storage','3392720PsLuwH','3695280bRRdAw','Change','Type','markupPrice','\x20and\x20','appendChild','then','#create-title-from-openai-button','select_image_url','#create-10-title-from-openai-button','data','Does\x20Not\x20Apply','replace','1788430sauxSq','WARNING!\x20INTERNATIONAL\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','12606dWSKQw','used','charAt','check_if_chinese_seller','location','sendMessage','snipeItemToEbayAutomatically\x20response:\x20','insertBefore','https://script.google.com/macros/s/AKfycbwmsEhXcgqCK9CnpqdQPxdgNl3hiVs-gAsF75DpIKuzetTdhbtGSb61XQzJ7FY2OunV/exec?','substr','snipe_item','div','addListener','WARNING!\x20VERO!\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20','get','.total-characters-header'];a0_0x4e17=function(){return _0x22ca38;};return a0_0x4e17();}if(isProductPage()){if(!document[a0_0x47f536(0xfd)][a0_0x47f536(0x122)]['includes']('.amazon.ca/dp/')){}var hasFreeShipping=checkFreeShipping(),itemCondition=checkItemCondition();IsItemAvailable()['then'](function(_0x186ef3){var _0x5be59e=a0_0x47f536;!_0x186ef3&&(document[_0x5be59e(0x13e)]='WARNING!\x20NOT\x20IN\x20STOCK\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20'+document[_0x5be59e(0x13e)],changeFaviconOfPage(chrome['runtime'][_0x5be59e(0x124)](_0x5be59e(0x149))));}),!hasFreeShipping&&(document['title']='WARNING!\x20NO\x20FREE\x20SHIPPING\x20-\x20CONFIRM\x20TO\x20LIST-\x20\x20'+document[a0_0x47f536(0x13e)],changeFaviconOfPage(chrome['runtime']['getURL']('Favicons/Warning/1.ico'))),itemCondition==a0_0x47f536(0xfa)&&(document['title']=a0_0x47f536(0x153)+document['title'],changeFaviconOfPage(chrome[a0_0x47f536(0x142)][a0_0x47f536(0x124)]('Favicons/Warning/1.ico'))),scrapeData();}async function calculateCustomPrice(){var _0x54b96d=a0_0x47f536;const _0x221ff9=await new Promise(_0x1e53ab=>{var _0x13e433=a0_0x1744;chrome[_0x13e433(0x158)][_0x13e433(0x11c)][_0x13e433(0x107)](_0x13e433(0x15d),_0x1e53ab);}),_0x1b095e=_0x221ff9[_0x54b96d(0x15d)];if(_0x1b095e!==undefined){const _0x11502b=getAmazonPrice(),_0x47c9c9=0x1+_0x1b095e/0x64,_0x4fbe6a=_0x11502b*_0x47c9c9;return _0x4fbe6a[_0x54b96d(0x143)](0x2);}}async function scrapeData(){var _0x4ec369=a0_0x47f536;console[_0x4ec369(0x125)]('Scraping\x20data'),console[_0x4ec369(0x125)](_0x4ec369(0x12d));var _0xd02989=await checkIfBrandIsVero();console['log'](_0x4ec369(0x117),_0xd02989);var _0xe525f0=await IsEligibleForPrime();console['log'](_0x4ec369(0x152),_0xe525f0);var _0x102f94=await calculateCustomPrice();console['log'](_0x4ec369(0x144),_0x102f94);var _0x11b380=checkIfItemIsInternational();console['log'](_0x4ec369(0x10f),_0x11b380);_0xd02989&&(document[_0x4ec369(0x13e)]=_0x4ec369(0x106)+document[_0x4ec369(0x13e)]);!_0xe525f0&&(document[_0x4ec369(0x13e)]=_0x4ec369(0x132)+document['title']);_0x11b380&&(document[_0x4ec369(0x13e)]=_0x4ec369(0xf8)+document['title']);var _0x492115=checkIfAsinAndUrlAreSame();!_0x492115&&(document[_0x4ec369(0x13e)]=_0x4ec369(0x14b)+document[_0x4ec369(0x13e)],changeFaviconOfPage(chrome[_0x4ec369(0x142)][_0x4ec369(0x124)]('Favicons/Warning/1.ico')));removeUnwantedElements();var _0x22988e=getBulletPoints();console[_0x4ec369(0x125)](_0x4ec369(0x139),_0x22988e),product_data={'title':getProductTitle(),'custom_title':getFilteredTitle(),'filteredTitle':getFilteredTitle(),'price':getAmazonPrice(),'custom_price':_0x102f94,'brand':getProductBrand(),'sku':getTheAsinFromHref(),'upc':_0x4ec369(0xf5),'descriptionHTML':getProductDescription(),'bullet_points':_0x22988e['list'],'bullet_points_html':_0x22988e[_0x4ec369(0x136)],'desc_template':desc_template,'mpn':getModelPartNumberList(),'tableSpecifics':getTableSpecifics(),'itemSpecifics':getItemSpecifics(),'descriptionText':getProductDescriptionTextOnly(),'descriptionAndFeatures':getProductDescriptionAndFeatures(),'filteredItemSpecifics':getFilteredItemSpecifics(),'main_sd_images':getProductPictures(),'main_hd_images':getHighResProductPictures(),'shippingWeight':getShippingWeight(),'extra':{},'domain_host':window[_0x4ec369(0xfd)]['host'],'categories':getCategories()};var {useReviewImages:_0x345c32}=await chrome[_0x4ec369(0x158)][_0x4ec369(0x11c)][_0x4ec369(0x107)]([_0x4ec369(0x110)]);_0x345c32?(product_data[_0x4ec369(0x157)]=getAmazonReviewImages(),product_data[_0x4ec369(0x128)]=[],product_data[_0x4ec369(0x157)]['forEach'](async _0x3ff14e=>{var _0x4b960e=await urlToImage(_0x3ff14e);createImageAndAppend(_0x4b960e,!![]);})):transformImage();console[_0x4ec369(0x125)]('product_data',product_data),chrome[_0x4ec369(0x158)][_0x4ec369(0x11c)][_0x4ec369(0x115)]({'amazon':product_data},()=>{});var _0x23f681=createTableBoilerPlate();document[_0x4ec369(0x12b)](_0x4ec369(0x148))[_0x4ec369(0x15f)](_0x23f681),createTableHeader(_0x23f681,{'headerName':'Rank'}),createTableHeader(_0x23f681,{'headerName':_0x4ec369(0x15c)}),createTableHeader(_0x23f681,{'headerName':_0x4ec369(0x129)}),createTableHeader(_0x23f681,{'headerName':_0x4ec369(0x130)}),createTableHeader(_0x23f681,{'headerName':_0x4ec369(0x12a)}),clickAnimation(_0x4ec369(0x108),function(){var _0x5466e0=_0x4ec369;sortTable(_0x5466e0(0x146),_0x5466e0(0x130));});var _0x1aace0=createRow(_0x23f681);createCell(_0x23f681,{'rowNumber':_0x1aace0,'cellValue':_0x1aace0,'headerName':'Rank'}),createCell(_0x23f681,{'rowNumber':_0x1aace0,'cellValue':product_data[_0x4ec369(0x10a)],'headerName':'Title'}),createCell(_0x23f681,{'rowNumber':_0x1aace0,'cellValue':'Filtered','headerName':_0x4ec369(0x15c)}),createCell(_0x23f681,{'rowNumber':_0x1aace0,'cellValue':product_data[_0x4ec369(0x10a)]['length'],'headerName':'Total\x20Characters'});var _0x4c72ad=createButtonToUpdateTextArea({'buttonInnerText':_0x4ec369(0x15b),'textAreaSelector':'#the-textarea','valueToSet':product_data[_0x4ec369(0x10a)],'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x23f681,{'button':_0x4c72ad,'rowNumber':_0x1aace0,'headerName':_0x4ec369(0x12a)});}function getAiCorrectedText(_0x365e11){return new Promise((_0x128b68,_0x1593f4)=>{var _0x3324f7=a0_0x1744;chrome[_0x3324f7(0x142)][_0x3324f7(0xfe)]({'type':'get_ai_corrected_text','text':_0x365e11},function(_0x591e62){var _0x28c13a=_0x3324f7,_0x14dc07=_0x591e62[_0x28c13a(0x164)][_0x28c13a(0x119)][0x0][_0x28c13a(0x112)];_0x14dc07=_0x14dc07['replace'](/\n/g,''),_0x14dc07=_0x14dc07[_0x28c13a(0xf6)](/Inlet/g,''),_0x128b68(_0x14dc07);});});}function a0_0x1744(_0x2aaf88,_0xfddc8f){var _0x4e1717=a0_0x4e17();return a0_0x1744=function(_0x174400,_0x5824e7){_0x174400=_0x174400-0xf5;var _0x323f8c=_0x4e1717[_0x174400];return _0x323f8c;},a0_0x1744(_0x2aaf88,_0xfddc8f);}async function testStorage(_0xe12ff5){var _0x29881e=a0_0x47f536,_0x5b4e6b=_0x29881e(0x135),_0x468da1=[_0xe12ff5[_0x29881e(0x127)]];await saveToLocalStorage(_0x5b4e6b,_0x468da1);}async function generateTitlesAutomatically(){var _0x71191=a0_0x47f536,_0x61f338=await _waitForElement(_0x71191(0x10d)),_0x62e89a=await _waitForElement(_0x71191(0x163)),_0x591ba1=await _waitForElement(_0x71191(0x161)),_0x2b9454=await getFromLocalStorage(_0x71191(0x13a));_0x2b9454===!![]&&(document[_0x71191(0x12b)](_0x71191(0x151))[_0x71191(0x13f)](),document[_0x71191(0x12b)](_0x71191(0x10c))['click']());}generateTitlesAutomatically();