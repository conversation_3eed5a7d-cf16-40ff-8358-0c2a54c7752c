function a0_0x5c35(_0x2925ec,_0x52a9fe){var _0x2cf7b0=a0_0x2cf7();return a0_0x5c35=function(_0x5c35ac,_0x1b6760){_0x5c35ac=_0x5c35ac-0x14c;var _0x13d425=_0x2cf7b0[_0x5c35ac];return _0x13d425;},a0_0x5c35(_0x2925ec,_0x52a9fe);}(function(_0x2b782e,_0x476d43){var _0x3c1c71=a0_0x5c35,_0x1e4c73=_0x2b782e();while(!![]){try{var _0x50c624=parseInt(_0x3c1c71(0x166))/0x1*(parseInt(_0x3c1c71(0x17d))/0x2)+-parseInt(_0x3c1c71(0x176))/0x3*(parseInt(_0x3c1c71(0x17a))/0x4)+parseInt(_0x3c1c71(0x16f))/0x5*(-parseInt(_0x3c1c71(0x180))/0x6)+parseInt(_0x3c1c71(0x153))/0x7+-parseInt(_0x3c1c71(0x162))/0x8+-parseInt(_0x3c1c71(0x15f))/0x9*(-parseInt(_0x3c1c71(0x18e))/0xa)+parseInt(_0x3c1c71(0x18d))/0xb;if(_0x50c624===_0x476d43)break;else _0x1e4c73['push'](_0x1e4c73['shift']());}catch(_0x5f8a72){_0x1e4c73['push'](_0x1e4c73['shift']());}}}(a0_0x2cf7,0x208f5));var selectedImage,index=0x1;async function addBasicImageForBasicUsers(_0x43d6d2){var _0x253dce=a0_0x5c35,_0x5458eb=await checkMembership();if(_0x5458eb==_0x253dce(0x14e)||_0x5458eb==_0x253dce(0x174)){var _0x34b1ad=await urlToImage(_0x43d6d2);createImageAndAppend(_0x34b1ad);}}async function createImageWithTemplateGivenSource(_0x336fef){var _0x358ea4=a0_0x5c35,_0x575f48=await getFromLocalStorage(_0x358ea4(0x170)),{useReviewImages:_0x366449}=await chrome[_0x358ea4(0x193)][_0x358ea4(0x184)][_0x358ea4(0x159)](['useReviewImages']);!_0x366449&&(_0x366449=![]);var _0x131cd2=_0x336fef,_0x5ebbdf=product_data[_0x358ea4(0x15c)][0x0],_0x1918f1=product_data[_0x358ea4(0x15c)][0x1],_0x25c218=product_data[_0x358ea4(0x15c)][0x2],_0x59eaea=product_data[_0x358ea4(0x16e)],_0x5f2846=0x2ee,_0x15918c=0x2ee,_0x1178b3=_0x358ea4(0x169),_0x14d336=_0x358ea4(0x15e);if(product_data[_0x358ea4(0x15c)][_0x358ea4(0x178)]<0x3&&product_data['main_sd_images']['length']>0x3)var _0x5ebbdf=product_data[_0x358ea4(0x160)][0x1],_0x1918f1=product_data[_0x358ea4(0x160)][0x2],_0x25c218=product_data[_0x358ea4(0x160)][0x3];if(product_data[_0x358ea4(0x15c)]['length']==0x0)var _0x5ebbdf=product_data[_0x358ea4(0x160)][0x1],_0x1918f1=product_data[_0x358ea4(0x160)][0x2],_0x25c218=product_data[_0x358ea4(0x160)][0x3];var _0x275868={'imageSource':_0x131cd2,'waterMarkUrl':_0x575f48},{enableImageTemplate:_0x33b6a1}=await chrome[_0x358ea4(0x193)]['local'][_0x358ea4(0x159)](['enableImageTemplate']);if(_0x33b6a1==!![]){var {template:_0x4c2c22}=await chrome[_0x358ea4(0x193)][_0x358ea4(0x184)]['get']([_0x358ea4(0x14f)]);try{_0x4c2c22=JSON['parse'](_0x4c2c22);}catch(_0x51c923){alert(_0x358ea4(0x157));throw new Error(_0x358ea4(0x14c));}console[_0x358ea4(0x186)](_0x4c2c22);var _0x133d89=product_data[_0x358ea4(0x15c)];_0x133d89[_0x358ea4(0x17c)](_0x336fef);var _0x475246=await generateImageFromTemplate(_0x4c2c22,_0x133d89,product_data),_0x277512=createImageAndAppend(_0x475246,_0x366449);console['log'](_0x277512),await selectImageIndex(_0x277512);return;}try{if(_0x5ebbdf!=undefined&&_0x1918f1!=undefined&&_0x25c218!=undefined){var _0x2e3e50=await create_multi_image_V2(_0x131cd2,_0x5ebbdf,_0x575f48,_0x1918f1,_0x25c218,_0x59eaea,_0x5f2846,_0x15918c,_0x1178b3,_0x14d336,!![]),_0x277512=createImageAndAppend(_0x2e3e50,_0x366449);await selectImageIndex(_0x277512);return;}return;}catch(_0x239b79){console[_0x358ea4(0x186)](_0x358ea4(0x183),console[_0x358ea4(0x190)]);}}async function transformImage(){var _0x3bee52=a0_0x5c35;console[_0x3bee52(0x186)](_0x3bee52(0x172));var _0xa1ad3f=await getFromLocalStorage(_0x3bee52(0x170)),_0x4db6ed=product_data[_0x3bee52(0x15c)][0x0],_0x39428c=product_data['main_hd_images'][0x1],_0x397be0=product_data[_0x3bee52(0x15c)][0x2],_0x3b0cc3=product_data[_0x3bee52(0x15c)][0x3],_0x551009=product_data[_0x3bee52(0x16e)],_0x4e2647=0x2ee,_0x15d35b=0x2ee,_0x414617=_0x3bee52(0x169),_0x2831d9=_0x3bee52(0x15e);if(product_data[_0x3bee52(0x15c)][_0x3bee52(0x178)]<0x3&&product_data['main_sd_images']['length']>0x3)var _0x4db6ed=product_data[_0x3bee52(0x160)][0x0],_0x39428c=product_data['main_sd_images'][0x1],_0x397be0=product_data[_0x3bee52(0x160)][0x2],_0x3b0cc3=product_data[_0x3bee52(0x160)][0x3];if(product_data['main_hd_images'][_0x3bee52(0x178)]==0x0)var _0x4db6ed=product_data[_0x3bee52(0x160)][0x0],_0x39428c=product_data[_0x3bee52(0x160)][0x1],_0x397be0=product_data[_0x3bee52(0x160)][0x2],_0x3b0cc3=product_data['main_sd_images'][0x3];var _0x30ac13={'imageSource':_0x4db6ed,'waterMarkUrl':_0xa1ad3f};await addBasicImageForBasicUsers(_0x4db6ed);var {enableImageTemplate:_0x5dc704}=await chrome[_0x3bee52(0x193)]['local'][_0x3bee52(0x159)]([_0x3bee52(0x15b)]);if(_0x5dc704==!![]){var {template:_0x427777}=await chrome[_0x3bee52(0x193)][_0x3bee52(0x184)]['get'](['template']);try{_0x427777=JSON['parse'](_0x427777);}catch(_0x559d41){alert(_0x3bee52(0x157));throw new Error(_0x3bee52(0x14c));}console[_0x3bee52(0x186)](_0x3bee52(0x14f),_0x427777);var _0xfd50b3=await generateImageFromTemplate(_0x427777,product_data[_0x3bee52(0x15c)],product_data);console[_0x3bee52(0x186)]('image\x20from\x20template',_0xfd50b3),createImageAndAppend(_0xfd50b3);return;}try{if(_0x39428c!=undefined&&_0x397be0!=undefined&&_0x3b0cc3!=undefined){var _0x2dfdb4=await create_multi_image_V2(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x397be0,_0x3b0cc3,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9,!![]);createImageAndAppend(_0x2dfdb4);var _0x2dfdb4=await create_multi_image_V2(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x397be0,_0x3b0cc3,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9,![]);createImageAndAppend(_0x2dfdb4);}}catch(_0x5d342d){}try{var _0x197e41=await createSingleImageWithWaterMarkV2({'imageSource':_0x4db6ed,'waterMarkUrl':_0xa1ad3f});createImageAndAppend(_0x197e41);}catch(_0x4d47ee){}if(_0x39428c!=undefined)try{var _0x3717cf=await createSingleImageWithWaterMarkV2({'imageSource':_0x39428c,'waterMarkUrl':_0xa1ad3f});createImageAndAppend(_0x3717cf);var _0x1ad8a8=await creating_dual_image(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x1ad8a8);var _0x4bc5b5=await creating_dual_image_V2(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x4bc5b5);var _0x4f4694=await creating_dual_image_V3(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x4f4694);var _0x864a9a=await creating_dual_image_V2(_0x39428c,_0x4db6ed,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x864a9a);}catch(_0x387c77){}try{var _0x27e343=await creating_dual_image_V2(_0x4db6ed,_0x397be0,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x27e343);}catch(_0x8c41fe){}try{var _0x182085=await creating_dual_image_V2(_0x4db6ed,_0x3b0cc3,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x182085);}catch(_0x36531f){}try{var _0x35bb27=await creating_dual_image_with_transparent_boxes(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x35bb27);}catch(_0x3cbf6e){}try{var _0x2dfdb4=await create_multi_image(_0x4db6ed,_0x39428c,_0xa1ad3f,_0x397be0,_0x3b0cc3,_0x551009,_0x4e2647,_0x15d35b,_0x414617,_0x2831d9);createImageAndAppend(_0x2dfdb4);}catch(_0x4ca234){}var _0x37ccd0=await urlToImage(_0x4db6ed);createImageAndAppend(_0x37ccd0),console[_0x3bee52(0x186)](_0x3bee52(0x182));}function a0_0x2cf7(){var _0x47d3fb=['main_hd_images','5px','Ariel','9XfzFBb','main_sd_images','addEventListener','172784tbqBdl','image-selection-number-','click','edited-image-selection','73369CVmOnh','src','img','black','margin','style','onclick','getElementById','filteredTitle','15685lQsPVE','watermark_url','createElement','Starting\x20image\x20transformation','imagesContainer','basic','push','83037unBvbI','set','length','1px\x20solid\x20black','4gXxMHa','pointer','unshift','2mpCjzH','ultimate','appendChild','438QVARKw','add','Image\x20transformation\x20complete','error\x20creating\x20multi\x20image','local','preventDefault','log','round','image-selection-number-1','checkMembership','premium','span','height','676126Lesjsj','602630kvQZNY','image-container','error','textContent','3px\x20solid\x20red','storage','No\x20template\x20found','borderRadius','trial','template','getElementsByClassName','stopPropagation','width','1515815YiymjJ','runtime','classList','deleteButton','You\x20must\x20create\x20a\x20template\x20first,\x20as\x20you\x20have\x20enabled\x20the\x20template\x20feature','border','get','membership','enableImageTemplate'];a0_0x2cf7=function(){return _0x47d3fb;};return a0_0x2cf7();}function createImage(_0x3aacc1,_0x5cce04,_0x43c9d6=![]){var _0x31bf31=a0_0x5c35,_0x5cf4eb=document[_0x31bf31(0x171)](_0x31bf31(0x18b));_0x5cf4eb['className']=_0x31bf31(0x18f);var _0x49fb0c=document[_0x31bf31(0x171)](_0x31bf31(0x168));_0x49fb0c['src']=_0x3aacc1,_0x49fb0c['id']=_0x31bf31(0x163)+_0x5cce04,_0x49fb0c['className']='edited-image-selection';var _0x19f0bf=getNewImageSize(_0x49fb0c[_0x31bf31(0x152)],_0x49fb0c[_0x31bf31(0x18c)]);_0x49fb0c['style'][_0x31bf31(0x152)]=_0x19f0bf[0x0]+'px',_0x49fb0c[_0x31bf31(0x16b)][_0x31bf31(0x18c)]=_0x19f0bf[0x1]+'px',_0x49fb0c['style'][_0x31bf31(0x16a)]='5px',_0x49fb0c[_0x31bf31(0x16b)]['cursor']=_0x31bf31(0x17b),_0x49fb0c['style'][_0x31bf31(0x158)]=_0x31bf31(0x179),_0x49fb0c[_0x31bf31(0x16b)][_0x31bf31(0x14d)]=_0x31bf31(0x15d),_0x5cf4eb['appendChild'](_0x49fb0c),_0x49fb0c[_0x31bf31(0x16c)]=function(){var _0xc8de9d=_0x31bf31,_0x3efea3=document[_0xc8de9d(0x150)](_0xc8de9d(0x165));for(var _0x120f73=0x0;_0x120f73<_0x3efea3[_0xc8de9d(0x178)];_0x120f73++){_0x3efea3[_0x120f73][_0xc8de9d(0x16b)]['border']='1px\x20solid\x20black';}chrome[_0xc8de9d(0x154)]['sendMessage']({'type':_0xc8de9d(0x189)},function(_0x146c93){var _0x54135b=_0xc8de9d,_0xf75d17=_0x146c93[_0x54135b(0x15a)];if(_0xf75d17=='basic')alert('You\x20must\x20upgrade\x20to\x20premium\x20to\x20use\x20this\x20feature'),document[_0x54135b(0x16d)](_0x54135b(0x188))[_0x54135b(0x16b)][_0x54135b(0x158)]='3px\x20solid\x20red';else{if(_0xf75d17==_0x54135b(0x18a)||_0xf75d17==_0x54135b(0x17e)||_0xf75d17==_0x54135b(0x14e)){selectedImage=null,selectedImage=_0x49fb0c[_0x54135b(0x167)],_0x49fb0c['style']['border']=_0x54135b(0x192),product_data['selected_image']=selectedImage;if(_0x43c9d6){var _0x3dddfe=document[_0x54135b(0x150)](_0x54135b(0x165)),_0x4e2460=[];for(var _0x4e5c5b=0x0;_0x4e5c5b<_0x3dddfe['length'];_0x4e5c5b++){_0x4e2460[_0x54135b(0x175)](_0x3dddfe[_0x4e5c5b][_0x54135b(0x167)]);}product_data[_0x54135b(0x15c)]=_0x4e2460,console['log'](product_data[_0x54135b(0x15c)]);}chrome[_0x54135b(0x193)][_0x54135b(0x184)]['set']({'amazon':product_data},()=>{});}}});};_0x5cce04==0x1&&(_0x49fb0c['style']['border']=_0x31bf31(0x192),selectedImage=_0x49fb0c['src'],product_data['selected_image']=selectedImage,chrome[_0x31bf31(0x193)][_0x31bf31(0x184)]['set']({'amazon':product_data},()=>{}));if(_0x43c9d6){var _0x12fb2a=document['createElement'](_0x31bf31(0x18b));_0x12fb2a[_0x31bf31(0x191)]='x',_0x12fb2a[_0x31bf31(0x155)][_0x31bf31(0x181)](_0x31bf31(0x156)),_0x5cf4eb['appendChild'](_0x12fb2a),_0x12fb2a[_0x31bf31(0x161)](_0x31bf31(0x164),function(_0x3a2331){var _0x2bf69d=_0x31bf31;_0x3a2331[_0x2bf69d(0x185)](),_0x3a2331[_0x2bf69d(0x151)](),_0x5cf4eb['remove']();var _0x3d7794=document[_0x2bf69d(0x150)]('edited-image-selection'),_0x439301=[];for(var _0x1e9201=0x0;_0x1e9201<_0x3d7794[_0x2bf69d(0x178)];_0x1e9201++){_0x439301['push'](_0x3d7794[_0x1e9201][_0x2bf69d(0x167)]);}product_data[_0x2bf69d(0x15c)]=_0x439301,console[_0x2bf69d(0x186)](product_data[_0x2bf69d(0x15c)]),chrome[_0x2bf69d(0x193)][_0x2bf69d(0x184)][_0x2bf69d(0x177)]({'amazon':product_data},()=>{});});}return _0x5cf4eb;}function getNewImageSize(_0x50e7ea,_0x559a62){var _0x59ab06=a0_0x5c35,_0x203471=0xe1,_0x2d151d=0xe1;return _0x50e7ea>_0x559a62?_0x2d151d=Math[_0x59ab06(0x187)](_0x559a62/_0x50e7ea*_0x203471):_0x203471=Math['round'](_0x50e7ea/_0x559a62*_0x2d151d),[_0x203471,_0x2d151d];}function createImageAndAppend(_0x29ad69,_0x5043e8=![]){var _0x4fe6e5=a0_0x5c35,_0x12b578=index,_0x22350c=createImage(_0x29ad69[_0x4fe6e5(0x167)],_0x12b578,_0x5043e8);return document[_0x4fe6e5(0x16d)]('imagesContainer')[_0x4fe6e5(0x17f)](_0x22350c),index++,_0x12b578;}function createImageAndAppendBase64(_0x28836c){var _0x21a140=a0_0x5c35,_0xf4ce38=createImage(_0x28836c,index);_0xf4ce38['className']='edited-image-selection',document[_0x21a140(0x16d)](_0x21a140(0x173))[_0x21a140(0x17f)](_0xf4ce38),index++;}async function selectImageIndex(_0x3d8b7){var _0x51face=a0_0x5c35,_0x473953=document[_0x51face(0x16d)](_0x51face(0x163)+_0x3d8b7);console[_0x51face(0x186)](_0x473953,'image-selection-number-'+_0x3d8b7);while(!_0x473953){await new Promise(_0x37007a=>setTimeout(_0x37007a,0x3e8)),_0x473953=document[_0x51face(0x16d)]('image-selection-number-'+_0x3d8b7);}var _0x1d66f7='image-selection-number-'+_0x3d8b7;document[_0x51face(0x16d)](_0x1d66f7)[_0x51face(0x164)]();}