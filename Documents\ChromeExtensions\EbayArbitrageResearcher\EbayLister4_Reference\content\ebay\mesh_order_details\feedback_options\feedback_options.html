<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="feedback_options.css">
    <title>Feedback Options</title>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <p>Edit the feedback message below. You can use {{Customer_Name}} and {{Delivery_Date}} as placeholders which will be replaced in the actual message sent to customers.</p>
        </div>
        <textarea id="feedbackMessage" rows="10" cols="50"></textarea>
        <div class="button-options">
            <button id="resetMessage">Revert to Default</button>
            <button id="saveMessage">Save Message</button>
        </div>
     
    </div>
    <script src="feedback_options.js"></script>
</body>
</html>
