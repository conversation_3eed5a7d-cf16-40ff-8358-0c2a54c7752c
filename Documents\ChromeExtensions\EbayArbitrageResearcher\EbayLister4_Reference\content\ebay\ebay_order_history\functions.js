const a0_0x536aba=a0_0x3e6c;(function(_0x409213,_0x5cea66){const _0x513d4d=a0_0x3e6c,_0x47bd3e=_0x409213();while(!![]){try{const _0x9052de=-parseInt(_0x513d4d(0xa8))/0x1+-parseInt(_0x513d4d(0xd4))/0x2+-parseInt(_0x513d4d(0xd7))/0x3*(-parseInt(_0x513d4d(0xae))/0x4)+-parseInt(_0x513d4d(0xa0))/0x5+-parseInt(_0x513d4d(0xc4))/0x6*(parseInt(_0x513d4d(0xb2))/0x7)+-parseInt(_0x513d4d(0xbd))/0x8+-parseInt(_0x513d4d(0xb3))/0x9*(-parseInt(_0x513d4d(0xa3))/0xa);if(_0x9052de===_0x5cea66)break;else _0x47bd3e['push'](_0x47bd3e['shift']());}catch(_0x3ae2af){_0x47bd3e['push'](_0x47bd3e['shift']());}}}(a0_0x19a2,0x6f71a),console[a0_0x536aba(0xaa)](a0_0x536aba(0xd9)));async function getEbayOrderData(_0x231b59){const _0xe05a11=a0_0x536aba,_0x176983=_0xe05a11(0xba)+_0x231b59;try{const _0x464558=await fetchHtmlPage(_0x176983);console[_0xe05a11(0xaa)](_0xe05a11(0xcf),_0x464558);const _0x491a5f=new DOMParser()[_0xe05a11(0xc8)](_0x464558,_0xe05a11(0xc3));var _0x2ee18e=await getOrderDetails(_0x491a5f);return _0x2ee18e;}catch(_0x50cc14){return console[_0xe05a11(0xa4)](_0xe05a11(0xd5),_0x50cc14),null;}}function fetchHtmlPage(_0x7ea215){return new Promise((_0x328e15,_0x455843)=>{const _0x23fb88=a0_0x3e6c;chrome[_0x23fb88(0xa7)][_0x23fb88(0xa5)]({'type':_0x23fb88(0xe1),'url':_0x7ea215},_0x246653=>{const _0x4b6bfc=_0x23fb88;_0x246653?.['error']?_0x455843(_0x246653[_0x4b6bfc(0xa4)]):_0x328e15(_0x246653[_0x4b6bfc(0xc9)]);});});}function a0_0x3e6c(_0x2a7ba6,_0x27352f){const _0x19a240=a0_0x19a2();return a0_0x3e6c=function(_0x3e6cc1,_0xbaace1){_0x3e6cc1=_0x3e6cc1-0x9f;let _0x43674e=_0x19a240[_0x3e6cc1];return _0x43674e;},a0_0x3e6c(_0x2a7ba6,_0x27352f);}function displayOrderDetails(_0x487372,_0x245224=a0_0x536aba(0xd2)){const _0x2d46bb=a0_0x536aba,_0x222621=_0x487372[_0x2d46bb(0xac)];let _0x2fef98=document[_0x2d46bb(0xbb)](_0x2d46bb(0xca)+_0x222621);if(!_0x2fef98){const _0x274c93=document[_0x2d46bb(0xc1)]('tr');for(let _0x4b7f36 of _0x274c93){if(_0x4b7f36[_0x2d46bb(0xc5)][_0x2d46bb(0xcb)](_0x222621)){const _0x2c720e=_0x4b7f36[_0x2d46bb(0xb5)](_0x2d46bb(0xa2));if(_0x2c720e){_0x2fef98=document[_0x2d46bb(0xc2)](_0x2d46bb(0x9f)),_0x2fef98['id']=_0x2d46bb(0xca)+_0x222621,_0x2fef98[_0x2d46bb(0xdc)]=_0x2d46bb(0xd3),_0x2fef98['dataset']['orderId']=_0x222621,_0x2c720e['appendChild'](_0x2fef98);break;}}}}!_0x2fef98&&(_0x2fef98=document[_0x2d46bb(0xc2)]('div'),_0x2fef98['id']=_0x2d46bb(0xca)+_0x222621,_0x2fef98['className']=_0x2d46bb(0xd3),_0x2fef98[_0x2d46bb(0xcc)][_0x2d46bb(0xce)]=_0x222621,document[_0x2d46bb(0xb1)][_0x2d46bb(0xd8)](_0x2fef98,document['body']['firstChild']));_0x2fef98['dataset']['status']=_0x245224;const _0x3db640=_0x487372?.[_0x2d46bb(0xc6)]?.[_0x2d46bb(0xde)]||'',_0x5f3166=_0x487372?.[_0x2d46bb(0xc6)]?.['address']||{},_0x4b00ae=_0x487372?.['customer']?.[_0x2d46bb(0xd1)]||'',_0x28379d=[_0x5f3166[_0x2d46bb(0xd0)],_0x5f3166['line_2']][_0x2d46bb(0xb6)](Boolean)[_0x2d46bb(0xc7)](',\x20'),_0x117f81=[_0x5f3166[_0x2d46bb(0xe2)],_0x5f3166[_0x2d46bb(0xe0)],_0x5f3166['zip']]['filter'](Boolean)['join'](',\x20');_0x2fef98[_0x2d46bb(0xb8)]=_0x2d46bb(0xbe)+_0x245224+'\x22></div>\x0a\x20\x20\x20\x20<div\x20class=\x22order-fields\x22>\x0a\x20\x20\x20\x20\x20\x20'+(_0x3db640?_0x2d46bb(0xc0)+_0x3db640+'</div>':'')+_0x2d46bb(0xa6)+(_0x28379d?_0x2d46bb(0xaf)+_0x28379d+'</div>':'')+_0x2d46bb(0xa6)+(_0x117f81?_0x2d46bb(0xdd)+_0x117f81+_0x2d46bb(0xe3):'')+_0x2d46bb(0xa6)+(_0x5f3166[_0x2d46bb(0xbc)]?_0x2d46bb(0xb7)+_0x5f3166['country']+_0x2d46bb(0xe3):'')+_0x2d46bb(0xa6)+(_0x4b00ae?_0x2d46bb(0xd6)+_0x4b00ae+_0x2d46bb(0xe3):'')+'\x0a\x20\x20\x20\x20</div>\x0a\x20\x20';}function getAllOrderNumbersFromPage(){const _0x35c1a8=a0_0x536aba,_0x1c94da=document[_0x35c1a8(0xc1)](_0x35c1a8(0xb9)),_0x4c9bc7=new Set();return _0x1c94da[_0x35c1a8(0xab)](_0x4cdea7=>{const _0x51e22a=_0x35c1a8,_0x205b1d=_0x4cdea7[_0x51e22a(0xbf)][_0x51e22a(0xa9)](/\d{2}-\d{5}-\d{5}/);if(_0x205b1d)_0x4c9bc7[_0x51e22a(0xad)](_0x205b1d[0x0]);}),Array['from'](_0x4c9bc7);}async function renderAllOrders(_0x56db1a){const _0x502d4f=a0_0x536aba;for(let _0x241727 of _0x56db1a){const _0x1d589f=createLoadingBlock(_0x241727);document[_0x502d4f(0xb1)][_0x502d4f(0xdb)](_0x1d589f);try{const _0x379cc0=await getEbayOrderData(_0x241727);showSuccessState(_0x1d589f),displayOrderDetails(_0x379cc0,_0x1d589f[_0x502d4f(0xb5)](_0x502d4f(0xdf)));}catch(_0x430d2d){showErrorState(_0x1d589f);}}}function a0_0x19a2(){const _0x26091b=['</div>','div','1837395sAodpL','order-status','.actions-cell','30292060lusNRg','error','sendMessage','\x0a\x20\x20\x20\x20\x20\x20','runtime','489351fkYwya','match','log','forEach','ebayOrderNumber','add','4ULbQaj','<div\x20class=\x22order-line\x20order-address\x22>','order-block','body','1066093bmrqjo','9FILPuE','.order-status','querySelector','filter','<div\x20class=\x22order-line\x20order-country\x22>','innerHTML','[id^=\x22RecordNumber\x22]','https://www.ebay.com/sh/ord/details?orderid=','getElementById','country','6426192xvzLof','\x0a\x20\x20\x20\x20<div\x20class=\x22order-status-icon\x20','innerText','<div\x20class=\x22order-line\x20order-name\x22>','querySelectorAll','createElement','text/html','6esAJzx','textContent','customer','join','parseFromString','html','orderId_','includes','dataset','<span\x20class=\x22success-check\x22>✔️</span>','orderId','Fetched\x20HTML:','line_1','phone','success','order-details','1547540LENrYi','Failed\x20to\x20fetch\x20order:','<div\x20class=\x22order-line\x20order-phone\x22>','40323tHnFXC','insertBefore','ebay_order_history\x20functions.js\x20loaded','<span\x20class=\x22spinner\x22></span>','appendChild','className','<div\x20class=\x22order-line\x20order-citystatezip\x22>','name','.order-text','state','fetchPageHtml','city'];a0_0x19a2=function(){return _0x26091b;};return a0_0x19a2();}function getAllOrderNumbersFromPage(){const _0x5518c7=a0_0x536aba,_0x4e53b9=[...document[_0x5518c7(0xc1)]('tr')],_0x309014=new Set();for(const _0x30ced5 of _0x4e53b9){const _0x2d183d=_0x30ced5[_0x5518c7(0xc5)][_0x5518c7(0xa9)](/\d{2}-\d{5}-\d{5}/);if(_0x2d183d)_0x309014[_0x5518c7(0xad)](_0x2d183d[0x0]);}return[..._0x309014];}function createOrderBlock(_0x4832d8){const _0x2d5523=a0_0x536aba,_0x178a71=document[_0x2d5523(0xc2)](_0x2d5523(0x9f));_0x178a71[_0x2d5523(0xdc)]=_0x2d5523(0xb0);const _0x52a4ff=document[_0x2d5523(0xc2)]('div');_0x52a4ff[_0x2d5523(0xdc)]=_0x2d5523(0xa1),_0x52a4ff[_0x2d5523(0xb8)]=_0x2d5523(0xda);const _0x15f332=document[_0x2d5523(0xc2)]('div');return _0x15f332['className']='order-text',_0x15f332[_0x2d5523(0xc5)]='Fetching\x20details\x20for\x20'+_0x4832d8+'...',_0x178a71['appendChild'](_0x52a4ff),_0x178a71['appendChild'](_0x15f332),_0x178a71;}function showSuccessState(_0x42224d){const _0x59dc4d=a0_0x536aba,_0x506dc1=_0x42224d[_0x59dc4d(0xb5)](_0x59dc4d(0xb4));_0x506dc1[_0x59dc4d(0xb8)]=_0x59dc4d(0xcd);}function showErrorState(_0x554039){const _0x48869d=a0_0x536aba,_0x200856=_0x554039[_0x48869d(0xb5)](_0x48869d(0xb4));_0x200856[_0x48869d(0xb8)]='<span\x20class=\x22error-x\x22>❌</span>';}