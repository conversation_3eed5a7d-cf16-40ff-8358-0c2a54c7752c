function a0_0x117d(_0x842b0a,_0x4d0151){const _0x415b2b=a0_0x415b();return a0_0x117d=function(_0x117d23,_0x2ccf96){_0x117d23=_0x117d23-0x13c;let _0x52119b=_0x415b2b[_0x117d23];return _0x52119b;},a0_0x117d(_0x842b0a,_0x4d0151);}(function(_0x1c6a77,_0x43036e){const _0x5c247e=a0_0x117d,_0x126ef8=_0x1c6a77();while(!![]){try{const _0x170be4=-parseInt(_0x5c247e(0x13d))/0x1*(-parseInt(_0x5c247e(0x141))/0x2)+-parseInt(_0x5c247e(0x142))/0x3+parseInt(_0x5c247e(0x14a))/0x4+-parseInt(_0x5c247e(0x140))/0x5+-parseInt(_0x5c247e(0x13c))/0x6+parseInt(_0x5c247e(0x152))/0x7+parseInt(_0x5c247e(0x13f))/0x8*(-parseInt(_0x5c247e(0x157))/0x9);if(_0x170be4===_0x43036e)break;else _0x126ef8['push'](_0x126ef8['shift']());}catch(_0x9741d0){_0x126ef8['push'](_0x126ef8['shift']());}}}(a0_0x415b,0xe4c44));async function getRestrictedWords(){const _0x58e664=a0_0x117d,_0x4a56fe=await fetch(chrome['runtime'][_0x58e664(0x14f)](_0x58e664(0x155))),_0xc4996f=await _0x4a56fe[_0x58e664(0x146)]();return _0xc4996f[_0x58e664(0x150)]('\x0a')[_0x58e664(0x154)](_0x33c319=>_0x33c319[_0x58e664(0x14c)]())[_0x58e664(0x149)](_0x147079=>_0x147079!=='');}function removeWords(_0x4c40e3,_0x5be42a){const _0x2afb4c=a0_0x117d,_0x3d3dfe=_0x5be42a[_0x2afb4c(0x149)](_0x4abad3=>_0x4abad3&&_0x4abad3[_0x2afb4c(0x14c)]())[_0x2afb4c(0x154)](_0x4041c1=>{const _0x52edf4=_0x4041c1['replace'](/[.*+?^${}()|[\]\\]/g,'\x5c$&');return new RegExp('\x5cb'+_0x52edf4+'\x5cb','gi');}),_0x4bd4f6=_0x4c40e3[_0x2afb4c(0x150)](/(<[^>]+>)/g);let _0x364ea1=![];return _0x4bd4f6[_0x2afb4c(0x154)](_0x2378bc=>{const _0x2757c6=_0x2afb4c;if(/^<\s*(style|script)\b/i[_0x2757c6(0x144)](_0x2378bc))return _0x364ea1=!![],_0x2378bc;if(/^<\s*\/\s*(style|script)\s*>/i['test'](_0x2378bc))return _0x364ea1=![],_0x2378bc;if(_0x364ea1||_0x2378bc[_0x2757c6(0x151)]('<'))return _0x2378bc;let _0x4bbaf9=_0x2378bc;for(const _0x2cafe5 of _0x3d3dfe){_0x4bbaf9=_0x4bbaf9[_0x2757c6(0x14b)](_0x2cafe5,'');}return _0x4bbaf9;})['join']('');}function removeWordsWithPrefixFollowedByNonLetter(_0x4ab6d9,_0x1fbc7b){const _0x1df910=a0_0x117d;if(!_0x1fbc7b)return _0x4ab6d9;return!Array[_0x1df910(0x143)](_0x1fbc7b)&&(_0x1fbc7b=[_0x1fbc7b]),_0x1fbc7b[_0x1df910(0x158)](_0x44ca70=>{const _0x434664=_0x1df910;if(_0x44ca70){const _0x247617=_0x44ca70[_0x434664(0x14b)](/[.*+?^${}()|[\]\\]/g,_0x434664(0x14e)),_0x38a690=new RegExp('\x5cb'+_0x247617+_0x434664(0x148),'gi');_0x4ab6d9=_0x4ab6d9[_0x434664(0x14b)](_0x38a690,'');}}),_0x4ab6d9;}async function getRestrictedPrefixes(){const _0x4f5419=a0_0x117d;var _0x1fa24d=await fetch(chrome['runtime'][_0x4f5419(0x14f)](_0x4f5419(0x14d))),_0x1b1db1=await _0x1fa24d[_0x4f5419(0x146)]();return _0x1b1db1['split']('\x0a')[_0x4f5419(0x154)](_0xde6e26=>_0xde6e26[_0x4f5419(0x14c)]())['filter'](_0xf55158=>_0xf55158!=='');}function a0_0x415b(){const _0x2b8d2b=['12159525QKJazO','\x22\x20for\x20word\x20\x22','map','libraries/safety-lib/safety_restricted_words.txt','log','188919WKpsGw','forEach','2461956hRKcAX','1wQUrQj','match','56GChlHc','8711270xGPGoz','3045414DlqJZy','2075067RYvYkh','isArray','test','Restricted\x20prefix\x20detected:\x20\x22','text','\x22\x20and\x20escapedWord\x20\x22','(?=[^A-Za-z])\x5cS*\x5cb','filter','2673808LeOENA','replace','trim','libraries/safety-lib/safety_restricted_prefixes.txt','\x5c$&','getURL','split','startsWith'];a0_0x415b=function(){return _0x2b8d2b;};return a0_0x415b();}async function removeRestrictedWordsFromText(_0x949817){var _0x16f817=_0x949817,_0x2f0f01=await getRestrictedPrefixes();_0x16f817=removeWordsWithPrefixFollowedByNonLetter(_0x16f817,_0x2f0f01);var _0x24969a=await getRestrictedWords();return _0x16f817=await removeWords(_0x16f817,_0x24969a),_0x16f817;}function detectWords(_0x1f47ac,_0x58c28d){const _0x59476a=a0_0x117d;if(!_0x58c28d)return![];return!Array[_0x59476a(0x143)](_0x58c28d)&&(_0x58c28d=[_0x58c28d]),_0x58c28d['some'](_0x3565fd=>{const _0x35abcf=_0x59476a;if(_0x3565fd){const _0x90a829=_0x3565fd['replace'](/[.*+?^${}()|[\]\\]/g,'\x5c$&'),_0x18f86c=new RegExp('\x5cb'+_0x90a829+'\x5cb','i'),_0x374afe=_0x1f47ac[_0x35abcf(0x13e)](_0x18f86c);if(_0x374afe)return console[_0x35abcf(0x156)]('Restricted\x20word\x20detected:\x20\x22'+_0x374afe[0x0]+_0x35abcf(0x153)+_0x3565fd+_0x35abcf(0x147)+_0x90a829+'\x22'),!![];}return![];});}function detectWordsWithPrefix(_0x22263a,_0x3e637c){const _0xb1562e=a0_0x117d;if(!_0x3e637c)return![];return!Array[_0xb1562e(0x143)](_0x3e637c)&&(_0x3e637c=[_0x3e637c]),_0x3e637c['some'](_0x2aabe0=>{const _0x14af70=_0xb1562e;if(_0x2aabe0){const _0x2b9cf6=_0x2aabe0[_0x14af70(0x14b)](/[.*+?^${}()|[\]\\]/g,_0x14af70(0x14e)),_0x2b5eb6=new RegExp('\x5cb'+_0x2b9cf6+_0x14af70(0x148),'i'),_0x23abf8=_0x22263a[_0x14af70(0x13e)](_0x2b5eb6);if(_0x23abf8)return console[_0x14af70(0x156)](_0x14af70(0x145)+_0x23abf8[0x0]+'\x22'),!![];}return![];});}async function detectRestrictedWords(_0x266d48){const _0x5d65ae=await getRestrictedPrefixes(),_0x4a54dc=await getRestrictedWords(),_0x2103eb=detectWordsWithPrefix(_0x266d48,_0x5d65ae),_0x2518d3=detectWords(_0x266d48,_0x4a54dc);return _0x2103eb||_0x2518d3;}