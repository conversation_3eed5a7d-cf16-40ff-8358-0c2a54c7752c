{"model": "gpt-4o-mini", "system_message": "You are a product categorization system. Given a product title, determine the main category from the following list that best fits the item: Women, Men, Kids, Home, Pets, Electronics.", "function_schema": {"name": "get_main_category", "description": "The input is a product title. The output is the main category from the provided list that best describes the item.", "parameters": {"type": "object", "properties": {"output": {"type": "string", "enum": ["Women", "Men", "Kids", "Home", "Pets", "Electronics"]}}, "required": ["output"]}}, "example_user_input": "Apple iPhone 13 Pro Max - 256GB - Graphite", "example_assistant_output_arguments": {"output": "Electronics"}, "user_input": ""}