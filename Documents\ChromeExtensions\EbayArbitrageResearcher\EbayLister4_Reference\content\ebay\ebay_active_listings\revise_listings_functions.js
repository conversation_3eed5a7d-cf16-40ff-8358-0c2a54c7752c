(function(_0x72a318,_0x54cbbc){var _0x43274e=a0_0x5665,_0x2576e0=_0x72a318();while(!![]){try{var _0x4debba=-parseInt(_0x43274e(0x213))/0x1+-parseInt(_0x43274e(0x20b))/0x2*(parseInt(_0x43274e(0x217))/0x3)+parseInt(_0x43274e(0x1fd))/0x4*(-parseInt(_0x43274e(0x201))/0x5)+parseInt(_0x43274e(0x1f0))/0x6*(-parseInt(_0x43274e(0x1f7))/0x7)+-parseInt(_0x43274e(0x207))/0x8+parseInt(_0x43274e(0x218))/0x9*(parseInt(_0x43274e(0x1ff))/0xa)+parseInt(_0x43274e(0x20f))/0xb*(parseInt(_0x43274e(0x210))/0xc);if(_0x4debba===_0x54cbbc)break;else _0x2576e0['push'](_0x2576e0['shift']());}catch(_0x4c906b){_0x2576e0['push'](_0x2576e0['shift']());}}}(a0_0x4583,0xbf1a6));async function endLowPerformingItems(_0x5c3ef2){var _0x57c4f5=a0_0x5665;if(_0x5c3ef2[_0x57c4f5(0x203)]==0x0)return;items=await selectLowPerformingItems(_0x5c3ef2);var _0x179f18=await fetchAllEbayData(_0x5c3ef2);await chrome[_0x57c4f5(0x1f3)]['local'][_0x57c4f5(0x215)]({'lowPerformingItemData':_0x179f18}),await clickBulkEndItemsButton();}function a0_0x4583(){var _0x24df0b=['firstChild','style','10431056ucNIeQ','Second\x20continue\x20button\x20not\x20found.\x20Clicking\x20the\x20first\x20one','textContent','log','8WMIbIr','trim','[class*=se-end-listing]','backgroundColor','488411QJAhXP','684vwhaya','button','sendMessage','302319aYRGLc','endLowPerformingItems','set','End\x20listing\x20modal\x20did\x20not\x20appear\x20in\x20time','317721CnKmBU','8897436oXazFb','querySelectorAll','End\x20listings','from','Second\x20continue\x20button\x20found','filter','click','toLowerCase','6ITXtwe','button\x20text','disabled','storage','sell-similar-end-low-performing-items','includes','preventDefault','397684iRBpJU','innerText','querySelector','Angebote\x20beenden','error','End\x20Low\x20Performing\x20Items','2600248FLKBtH','Edit','10owtJte','innerHTML','5gXIZEd','finalEndListingButton','length','ul[id*=\x22@bulkActionsV2\x22]'];a0_0x4583=function(){return _0x24df0b;};return a0_0x4583();}async function clickBulkEndItemsButton(){var _0x485d0c=a0_0x5665,_0x1e43d7=document[_0x485d0c(0x219)](_0x485d0c(0x211)),_0x37ac85=null;for(var _0x447810 of _0x1e43d7){if(_0x447810[_0x485d0c(0x1f8)]==_0x485d0c(0x21a)||_0x447810[_0x485d0c(0x1f8)]==_0x485d0c(0x1fa)){_0x37ac85=_0x447810;break;}}_0x37ac85[_0x485d0c(0x1ee)]();var _0x17c149=null,_0x40b406=0x0;while(!_0x17c149&&_0x40b406<0xf){await new Promise(_0x2f3d87=>setTimeout(_0x2f3d87,0x7d0)),_0x17c149=document[_0x485d0c(0x1f9)](_0x485d0c(0x20d)),_0x40b406++;}if(!_0x17c149){console[_0x485d0c(0x1fb)](_0x485d0c(0x216));return;}var _0x3045e7=_0x17c149[_0x485d0c(0x219)]('button'),_0x1412ac;for(let _0x26bd92=0x0;_0x26bd92<_0x3045e7[_0x485d0c(0x203)];_0x26bd92++){var _0x447810=_0x3045e7[_0x26bd92];console['log'](_0x485d0c(0x1f1),_0x447810[_0x485d0c(0x1f8)]);if(_0x447810[_0x485d0c(0x1f8)][_0x485d0c(0x1ef)]()[_0x485d0c(0x1f5)]('end\x20listing')||_0x447810[_0x485d0c(0x1f8)][_0x485d0c(0x1ef)]()[_0x485d0c(0x1f5)]('angebote\x20beenden')){_0x1412ac=_0x447810;break;}}console[_0x485d0c(0x20a)](_0x485d0c(0x202),_0x1412ac),await new Promise(_0x420077=>setTimeout(_0x420077,0x7d0)),_0x1412ac[_0x485d0c(0x1ee)]();}function createEndLowPerformingItemsButton(){var _0x465ed6=a0_0x5665,_0x522466=document['createElement']('button');return _0x522466[_0x465ed6(0x200)]=_0x465ed6(0x1fc),_0x522466['id']=_0x465ed6(0x214),_0x522466['onclick']=async function(_0x583ca1){var _0x3ef01a=_0x465ed6;_0x583ca1[_0x3ef01a(0x1f6)](),_0x522466[_0x3ef01a(0x1f2)]=!![],_0x522466[_0x3ef01a(0x206)][_0x3ef01a(0x20e)]='gray';var _0x453d0b=0x0,_0xab9779=0x0,_0x3c8022=getLowPerformingItemNodes(_0x453d0b,_0xab9779);chrome['runtime'][_0x3ef01a(0x212)]({'type':_0x3ef01a(0x1f4)}),endLowPerformingItems(_0x3c8022);},_0x522466;}async function countEditMenuOptions(){var _0x5a69f4=getEditMenuOptions(),_0xf42496=_0x5a69f4['length'];return _0xf42496;}function getEditMenuOptions(){var _0x427871=a0_0x5665,_0x438101=document[_0x427871(0x1f9)](_0x427871(0x204)),_0x3e8a45=_0x438101[_0x427871(0x219)]('li');return _0x3e8a45;}async function reviseListingsByMenuOption(_0x12544d=0x1){var _0x58296f=a0_0x5665,_0x156d86=getEditMenuOptions(),_0x23f040=_0x156d86[_0x12544d];_0x23f040[_0x58296f(0x205)][_0x58296f(0x1ee)](),await clickSecondContinueButton();}async function getEditButton(){var _0x3bba2a=a0_0x5665,_0x57f6d2=document[_0x3bba2a(0x219)](_0x3bba2a(0x211)),_0x21b0d7=null;for(var _0xf51471 of _0x57f6d2){if(_0xf51471['innerText']==_0x3bba2a(0x1fe)){_0x21b0d7=_0xf51471;break;}}return _0x21b0d7;}function a0_0x5665(_0x59dc00,_0x21327b){var _0x458352=a0_0x4583();return a0_0x5665=function(_0x5665f4,_0x35ad3d){_0x5665f4=_0x5665f4-0x1ec;var _0x596ff7=_0x458352[_0x5665f4];return _0x596ff7;},a0_0x5665(_0x59dc00,_0x21327b);}async function clickSecondContinueButton(){var _0x519490=a0_0x5665,_0xc9484c=[],_0x5dde9d=0x0;while(_0xc9484c['length']<0x2){var _0x22ae73=document[_0x519490(0x219)]('button');_0xc9484c=Array[_0x519490(0x21b)](_0x22ae73)[_0x519490(0x1ed)](_0x51e4cd=>_0x51e4cd[_0x519490(0x209)][_0x519490(0x20c)]()[_0x519490(0x1ef)]()==='continue');_0xc9484c[_0x519490(0x203)]<0x2&&(console[_0x519490(0x20a)]('waiting\x20for\x20second\x20continue\x20button\x20to\x20appear'),await new Promise(_0x15e856=>setTimeout(_0x15e856,0x3e8)));_0x5dde9d++;if(_0x5dde9d>0xa){console[_0x519490(0x20a)](_0x519490(0x208));if(_0xc9484c[_0x519490(0x203)]==0x1){_0xc9484c[0x0][_0x519490(0x1ee)]();return;}}}console[_0x519490(0x20a)](_0x519490(0x1ec)),_0xc9484c[0x1][_0x519490(0x1ee)]();}