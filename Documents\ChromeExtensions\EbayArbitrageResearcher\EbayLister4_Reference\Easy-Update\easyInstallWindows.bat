@echo off
SETLOCAL ENABLEDELAYEDEXPANSION

:: Define the URL and file names
echo Setting variables...
SET url=https://firebasestorage.googleapis.com/v0/b/ecomsniper-cb046.appspot.com/o/software%%2FeBayLister.zip?alt=media&token=a84ad875-2120-4ce9-9911-ed4595a0be38
SET zipFile=eBayLister.zip
SET folder=EbayLister4
echo URL: !url!
echo Zip File: !zipFile!
echo Folder: !folder!

:: Download the file using PowerShell
echo Downloading eBayLister.zip...
powershell -command "Invoke-WebRequest -Uri '!url!' -OutFile '.\!zipFile!'"
IF %ERRORLEVEL% NEQ 0 goto error

:: Check if the folder exists and delete it
echo Checking for existing folder...
IF EXIST "!folder!" (
    echo Deleting existing folder...
    RMDIR /S /Q "!folder!"
    IF %ERRORLEVEL% NEQ 0 goto error
)

:: Extract the zip file (requires PowerShell 5.0 or later)
echo Extracting the zip file...
powershell -command "Expand-Archive -LiteralPath '.\!zipFile!' -DestinationPath ."
IF %ERRORLEVEL% NEQ 0 goto error

echo Done.
goto end

:error
echo An error occurred.
goto end

:end
:: Pause the script
pause
