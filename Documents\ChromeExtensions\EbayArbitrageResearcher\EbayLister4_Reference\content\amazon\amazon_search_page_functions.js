var a0_0x12df25=a0_0x4918;(function(_0x39771f,_0x30234c){var _0x192dcb=a0_0x4918,_0x396258=_0x39771f();while(!![]){try{var _0x3fb316=-parseInt(_0x192dcb(0x221))/0x1+-parseInt(_0x192dcb(0x244))/0x2*(-parseInt(_0x192dcb(0x1a7))/0x3)+-parseInt(_0x192dcb(0x230))/0x4+parseInt(_0x192dcb(0x24b))/0x5*(-parseInt(_0x192dcb(0x226))/0x6)+-parseInt(_0x192dcb(0x1b2))/0x7*(parseInt(_0x192dcb(0x1cd))/0x8)+-parseInt(_0x192dcb(0x1f6))/0x9+-parseInt(_0x192dcb(0x1ce))/0xa*(-parseInt(_0x192dcb(0x1ca))/0xb);if(_0x3fb316===_0x30234c)break;else _0x396258['push'](_0x396258['shift']());}catch(_0x1c0b62){_0x396258['push'](_0x396258['shift']());}}}(a0_0x3ace,0xda443),console[a0_0x12df25(0x247)]('amazon_search_page_functions.js\x20loaded'));function titleContainsRestrictedWords(_0x290ab6,_0x53109d){var _0x2012a6=a0_0x12df25,_0x2b5776=_0x290ab6[_0x2012a6(0x257)]()[_0x2012a6(0x1e5)]('\x20'),_0x512cb2=[];for(var _0x334c79=0x0;_0x334c79<_0x53109d[_0x2012a6(0x1b1)];_0x334c79++){var _0x4db2b0=_0x53109d[_0x334c79];if(_0x4db2b0[_0x2012a6(0x208)]('\x20'))_0x290ab6[_0x2012a6(0x257)]()['includes'](_0x4db2b0[_0x2012a6(0x257)]())&&(_0x512cb2[_0x2012a6(0x202)](_0x4db2b0),console[_0x2012a6(0x247)]('Removed\x20phrase:\x20'+_0x4db2b0+_0x2012a6(0x228)+_0x290ab6));else for(var _0x1c4546=0x0;_0x1c4546<_0x2b5776[_0x2012a6(0x1b1)];_0x1c4546++){var _0x1f0959=_0x2b5776[_0x1c4546];if(_0x1f0959===_0x4db2b0[_0x2012a6(0x257)]()){_0x512cb2[_0x2012a6(0x202)](_0x4db2b0),console[_0x2012a6(0x247)](_0x2012a6(0x1a6)+_0x4db2b0+_0x2012a6(0x26f)+_0x1f0959);break;}}}return _0x512cb2['length']>0x0?!![]:![];}function titleContainsRequiredKeywords(_0xf343e5,_0x138e27,_0x4c0fdf='any'){var _0x58b1e8=a0_0x12df25,_0xcc5b9f=_0xf343e5[_0x58b1e8(0x257)]()['split']('\x20'),_0x27abb9=[];for(var _0x16edf0=0x0;_0x16edf0<_0x138e27['length'];_0x16edf0++){var _0x3766ab=_0x138e27[_0x16edf0];if(_0x3766ab[_0x58b1e8(0x208)]('\x20'))_0xf343e5[_0x58b1e8(0x257)]()[_0x58b1e8(0x208)](_0x3766ab[_0x58b1e8(0x257)]())&&(_0x27abb9[_0x58b1e8(0x202)](_0x3766ab),console[_0x58b1e8(0x247)]('Found\x20phrase:\x20'+_0x3766ab+_0x58b1e8(0x228)+_0xf343e5));else for(var _0x4bacac=0x0;_0x4bacac<_0xcc5b9f['length'];_0x4bacac++){var _0x210cb5=_0xcc5b9f[_0x4bacac];if(_0x210cb5===_0x3766ab['toLowerCase']()){_0x27abb9[_0x58b1e8(0x202)](_0x3766ab),console['log'](_0x58b1e8(0x239)+_0x3766ab+_0x58b1e8(0x26f)+_0x210cb5);break;}}}if(_0x4c0fdf===_0x58b1e8(0x1f2)&&_0x27abb9[_0x58b1e8(0x1b1)]===_0x138e27[_0x58b1e8(0x1b1)])return!![];else return _0x4c0fdf==='any'&&_0x27abb9['length']>0x0?!![]:![];}async function getPriceRangeFromStorage(){var _0x4a0700=a0_0x12df25;let {maxPrice:_0x24adf6}=await chrome['storage']['local'][_0x4a0700(0x1ad)]('maxPrice'),{minPrice:_0x430b11}=await chrome[_0x4a0700(0x1d7)][_0x4a0700(0x1af)][_0x4a0700(0x1ad)](_0x4a0700(0x200));return _0x24adf6=parseFloat(_0x24adf6),_0x430b11=parseFloat(_0x430b11),{'minPrice':_0x430b11,'maxPrice':_0x24adf6};}function extractPriceFromElement(_0xccea91){var _0x3ca6f4=a0_0x12df25,_0x440df1=_0xccea91['querySelector'](_0x3ca6f4(0x1e8));if(_0x440df1==null)return null;var _0x3ef2b7=_0x440df1[_0x3ca6f4(0x1a5)]['replace'](/,/g,'');_0x3ef2b7=_0x3ef2b7['replace'](/\./g,''),_0x3ef2b7=_0x3ef2b7[_0x3ca6f4(0x255)](/\$/g,''),_0x3ef2b7=_0x3ef2b7[_0x3ca6f4(0x255)](/\n/g,'');var _0x417c5e=_0xccea91[_0x3ca6f4(0x1f1)](_0x3ca6f4(0x1b9));if(_0x417c5e!=null){var _0x78fb4a=_0x417c5e['innerText'];_0x3ef2b7=_0x3ef2b7+'.'+_0x78fb4a;}return _0x3ef2b7=_0x3ef2b7[_0x3ca6f4(0x255)](/,/g,''),parseFloat(_0x3ef2b7);}function filterCardsBasedOnPrice(_0x1e68ee,_0x9b25a3,_0x304cb0){return _0x1e68ee['filter'](_0x4f0354=>{const _0x129dca=extractPriceFromElement(_0x4f0354);return _0x129dca!=null&&_0x129dca>_0x9b25a3&&_0x129dca<_0x304cb0;});}function extractImageUrlFromCard(_0x2767ad){var _0x47c853=a0_0x12df25,_0x311618='';try{var _0x291f26=_0x2767ad['querySelector']('img.s-image');_0x291f26!=null&&(_0x311618=_0x291f26[_0x47c853(0x22a)]);}catch(_0x53de2d){}return _0x311618;}function extractAsinFromCard(_0x1d4d7f){var _0x2409f4=a0_0x12df25;return _0x1d4d7f[_0x2409f4(0x227)](_0x2409f4(0x1d3));}function filterCardsWithAsins(_0x1feb3e){return _0x1feb3e['filter'](_0x53fa1f=>{const _0x445352=extractAsinFromCard(_0x53fa1f);return _0x445352!=null&&_0x445352!=='';});}function getAsinsFromCards(_0x33fadf){var _0x3a5aa3=a0_0x12df25;return _0x33fadf[_0x3a5aa3(0x1b6)](_0x33485a=>extractAsinFromCard(_0x33485a));}function filterBestSellerCards(_0x3e0735){var _0x3ae149=a0_0x12df25;return _0x3e0735[_0x3ae149(0x23d)](_0x295b8a=>{var _0x38de56=_0x3ae149;return _0x295b8a[_0x38de56(0x1f1)]('[id*=\x22-best-seller\x22]');});}function filterAmazonChoiceCards(_0x363ebc){var _0x1d358b=a0_0x12df25;return _0x363ebc[_0x1d358b(0x23d)](_0x5bac8e=>{var _0x17a6e5=_0x1d358b;return _0x5bac8e['querySelector'](_0x17a6e5(0x1b8));});}function filterBooksFromCards(_0x3443d8){var _0x3eff5b=a0_0x12df25;const _0x1f5c3f=[_0x3eff5b(0x264),'Paperback','Audiobook',_0x3eff5b(0x261),'Kindle\x20Edition',_0x3eff5b(0x205),_0x3eff5b(0x25e),_0x3eff5b(0x1ff),_0x3eff5b(0x249)];return _0x3443d8['filter'](_0x3669d2=>{var _0x2fc10c=_0x3eff5b;const _0x1f83ff=extractAsinFromCard(_0x3669d2);if(_0x1f83ff==null||!_0x1f83ff[_0x2fc10c(0x22c)]('B0'))return![];const _0x43380e=_0x3669d2['innerText']||_0x3669d2[_0x2fc10c(0x1a3)];return!_0x1f5c3f[_0x2fc10c(0x1b5)](_0x20a8cc=>_0x43380e[_0x2fc10c(0x208)](_0x20a8cc));});}function extractReviewsFromCard(_0x4db072){var _0x4036db=a0_0x12df25;let _0x56d012=0x0;try{var _0x440193=_0x4db072['querySelector'](_0x4036db(0x22e));!_0x440193&&(_0x440193=_0x4db072[_0x4036db(0x1f1)](_0x4036db(0x1db)));!_0x440193&&(_0x440193=_0x4db072[_0x4036db(0x1f1)](_0x4036db(0x262)));if(_0x440193&&_0x440193[_0x4036db(0x203)][_0x4036db(0x1f3)]()!==''){var _0x296a8b=_0x440193[_0x4036db(0x203)][_0x4036db(0x1f3)]();_0x296a8b=_0x296a8b[_0x4036db(0x255)](/\s+|\(|\)/g,'');let _0x12cdbe=0x1;/[Kk]$/['test'](_0x296a8b)&&(_0x12cdbe=0x3e8,_0x296a8b=_0x296a8b[_0x4036db(0x1c1)](0x0,-0x1)),_0x56d012=parseNumber(_0x296a8b)*_0x12cdbe,isNaN(_0x56d012)?_0x56d012=0x0:_0x56d012=Math[_0x4036db(0x1b3)](_0x56d012);}}catch(_0x11ed79){console['error'](_0x4036db(0x26c),_0x11ed79);}return _0x56d012;}function parseNumber(_0x42d4f8){var _0x2281f0=a0_0x12df25;_0x42d4f8=_0x42d4f8[_0x2281f0(0x255)](/[^\d.,-]/g,'');let _0x13811d=![];_0x42d4f8['startsWith']('-')&&(_0x13811d=!![],_0x42d4f8=_0x42d4f8[_0x2281f0(0x1fd)](0x1));let _0x38c07d='.',_0x10057c=',';if(_0x42d4f8[_0x2281f0(0x208)]('.')&&_0x42d4f8['includes'](','))_0x42d4f8[_0x2281f0(0x1ee)](',')<_0x42d4f8[_0x2281f0(0x1ee)]('.')?(_0x38c07d='.',_0x10057c=','):(_0x38c07d=',',_0x10057c='.');else{if(_0x42d4f8['includes']('.')){const _0x38b3ba=_0x42d4f8[_0x2281f0(0x1e5)]('.');_0x38b3ba[_0x38b3ba[_0x2281f0(0x1b1)]-0x1][_0x2281f0(0x1b1)]===0x3?(_0x38c07d='',_0x10057c='.'):(_0x38c07d='.',_0x10057c='');}else{if(_0x42d4f8['includes'](',')){const _0x3184a9=_0x42d4f8[_0x2281f0(0x1e5)](',');_0x3184a9[_0x3184a9['length']-0x1]['length']===0x3?(_0x38c07d='',_0x10057c=','):(_0x38c07d=',',_0x10057c='');}else _0x38c07d='',_0x10057c='';}}if(_0x10057c){const _0x13da14=new RegExp('\x5c'+_0x10057c,'g');_0x42d4f8=_0x42d4f8[_0x2281f0(0x255)](_0x13da14,'');}if(_0x38c07d&&_0x38c07d!=='.'){const _0x2c16e8=new RegExp('\x5c'+_0x38c07d,'g');_0x42d4f8=_0x42d4f8[_0x2281f0(0x255)](_0x2c16e8,'.');}let _0x23844a=parseFloat(_0x42d4f8);return _0x13811d&&(_0x23844a=-_0x23844a),_0x23844a;}function extractTitleFromCard(_0x539c81){var _0x8ac545=a0_0x12df25,_0x345389='';try{var _0x2bc3b5=_0x539c81[_0x8ac545(0x1f1)](_0x8ac545(0x1eb));_0x2bc3b5!=null&&(_0x345389=_0x2bc3b5[_0x8ac545(0x1a5)],_0x345389=_0x345389[_0x8ac545(0x255)](/\n/g,'\x20'),_0x345389=_0x345389[_0x8ac545(0x1f3)]());}catch(_0x504917){}return _0x345389;}function extractTitleFromCarouselCard(_0x1c944c){var _0x38587a=a0_0x12df25,_0x3e7d37='';console['log'](_0x38587a(0x232),_0x1c944c);try{var _0x3f76a3=_0x1c944c[_0x38587a(0x1f1)]('.p13n-sc-truncate-desktop-type2');console[_0x38587a(0x247)]('titleElement',_0x3f76a3),_0x3f76a3!=null&&(_0x3e7d37=_0x3f76a3[_0x38587a(0x203)],_0x3e7d37=_0x3e7d37[_0x38587a(0x255)](/\n/g,'\x20'),_0x3e7d37=_0x3e7d37['trim']());}catch(_0x27811c){}return _0x3e7d37;}function a0_0x3ace(){var _0x5397a7=['get','The\x20price\x20from\x20Amazon\x20is\x20greater\x20than\x20the\x20price\x20from\x20Ebay.\x20You\x20will\x20not\x20make\x20any\x20profit.\x20Please\x20try\x20again.','local','nextSibling','length','102032tIeKDT','round','label','some','map','snipeModeEnabled','[id*=\x22-amazons-choice-label\x22]','.a-price-fraction','removeChild','vero\x20filtered\x20cards','sort','No\x20results\x20to\x20copy.','Retrieved\x20','settings-modal-header','Save','slice','results-button-group','getAsins\x20','filtered\x20cards\x20after\x20removing\x20already\x20scraped\x20asins','classList','Minimum\x20Price','value','span','You\x20must\x20copy\x20the\x20item\x20details\x20from\x20the\x20ebay\x20page\x20and\x20have\x20it\x20in\x20the\x20clipboard\x20to\x20snipe.\x20Please\x20try\x20again.','12932128CwZnpT','retrieveFilteredCards\x20params','checkbox','8AgYPXs','50lTBEzr','settings-button','body','add','card','data-asin','list-asin-card--duplicate','error','.a-carousel-card','storage','min_reviews','parse','text','[data-cy=\x22reviews-block\x22]\x20a[aria-label$=\x22ratings\x22]','Remove\x20Books','settings-modal-form','remove','amazon\x20choice\x20cards','.s-include-content-margin','remove_books\x20filtered\x20cards','target','best\x20seller\x20cards','open_amazon_and_list_to_ebay','split','max_reviews','veroProtectionEnabled','.a-price-whole','\x20SKUs\x20from\x20local\x20storage','Error\x20retrieving\x20params:','.s-title-instructions-style','[data-asin]','error\x20parsing\x20json','indexOf','findIndex','number','querySelector','all','trim','sendMessage','preventDefault','1309680BhAHiT','title','\x20is\x20already\x20in\x20the\x20list\x20of\x20SKUs','readText','Found\x20','Max\x20similar\x20niche\x20items\x20not\x20reached','max_reviews\x20filtered\x20cards','substring','getElementById','DVD','minPrice','from','push','textContent','runtime','Audio\x20CD','prioritizeBestSellers','Cancel','includes','restricted\x20words\x20filtered\x20cards','boolean','required\x20keywords\x20filtered\x20cards','addEventListener','Error\x20saving\x20params:','Sniping...','createElement','listing-now','s-result-item','form-group','prioritizeAmazonChoice','list-asin-card--duplicate-text','lastError','Maximum\x20Reviews','getAsins\x20price','.s-result-item','Max\x20Similar\x20Niche\x20Items','settings-modal','appendToAmazonLinks','disabled','price','alreadyListedAsinsInPage\x20length','appendChild','amazonLinks','1673628ZMAZBQ','/dp/','set','List\x20ASIN:\x20','join','5246322AwOdwA','getAttribute','\x20-\x20Found\x20in\x20title:\x20','sortByReviews','src','settings-modal-close','startsWith','insertBefore','span.a-size-base.s-underline-text','Maximum\x20Price','7129544WEYeUL','Results\x20copied\x20to\x20clipboard.','extractTitleFromCarouselCard','max_similiar_niche_items','open_amazon_and_snipe_to_ebay','Prioritize\x20Amazon\x20Choice','getURL','collect-asins-button','then','Found\x20word:\x20','Already\x20Listed','itemNumber','after','filter','settings-modal-content','/VeroList.txt','Copy\x20Results','Total\x20ASINs\x20to\x20Fetch','button','filteredCards','2pAkSJK','totalAsinsToFetchFromSearch','Minimum\x20Reviews','log','param-','Prime\x20Video','clipboard','10wfcTIY','asin','No\x20image\x20element\x20found','Max\x20similar\x20niche\x20items\x20reached','cards\x20cards','contains','warn','remove_books','checked','\x20carousel\x20cards\x20cards','replace','forEach','toLowerCase','maxPrice','type','list-asin-button','sorted\x20cards','restricted_words','Adjust\x20Settings','Blu-ray','Snipe\x20ASIN:\x20','alreadyListedAsinsInPage','Audible','[data-cy=\x22reviews-block\x22]\x20div[data-csa-c-content-id*=\x22ratings\x22]','farewell','Hardcover','.sg-col-inner\x20.s-widget-container\x20.a-section','parentNode','ASIN\x20','title:','duplicate_protection','The\x20title,\x20price,\x20or\x20item\x20number\x20is\x20missing\x20from\x20the\x20clipboard.\x20Please\x20try\x20again.','Sort\x20By\x20Reviews','Error\x20extracting\x20reviews\x20from\x20card:','requirementType','querySelectorAll','\x20-\x20Found\x20in\x20title\x20word:\x20','Results\x20cleared.','div','.s-image','.p13n-sc-dynamic-image','Collect\x20ASINs','error\x20adding\x20ebay\x20button\x20to\x20carousel\x20item','button-group','innerHTML','settings-modal-overlay','innerText','Removed\x20word:\x20','1141989sGqZjU','com','domain','.a-dropdown-container','?th=1&psc=1','click'];a0_0x3ace=function(){return _0x5397a7;};return a0_0x3ace();}function sortCardsByReviews(_0x2d227b){var _0x38c9aa=a0_0x12df25;return _0x2d227b[_0x38c9aa(0x1bc)]((_0x41ac55,_0x24e6b3)=>{const _0x2864c8=extractReviewsFromCard(_0x41ac55),_0x25fd95=extractReviewsFromCard(_0x24e6b3);return _0x25fd95-_0x2864c8;});}async function retrieveCards(){var _0x575e6d=a0_0x12df25;const _0x5dc2e8=Array[_0x575e6d(0x201)](document[_0x575e6d(0x26e)](_0x575e6d(0x218)));return _0x5dc2e8;}async function retrieveFilteredCards(_0x5976c3={}){var _0x33115d=a0_0x12df25;console['log'](_0x33115d(0x1cb),_0x5976c3);async function _0x4caa90(_0x5396e9){var _0x1f51db=_0x33115d;if(_0x5976c3[_0x5396e9]!=null)return _0x5976c3[_0x5396e9];else{const _0x44e8ac=await chrome[_0x1f51db(0x1d7)][_0x1f51db(0x1af)][_0x1f51db(0x1ad)](_0x5396e9);return _0x44e8ac[_0x5396e9];}}var _0x39f774=parseInt(await _0x4caa90(_0x33115d(0x245))),_0x4e241d=await retrieveCards();console[_0x33115d(0x247)](_0x33115d(0x24f),_0x4e241d);var _0x1c9ea8=await _0x4caa90(_0x33115d(0x200)),_0x5ddf57=await _0x4caa90(_0x33115d(0x258));_0x1c9ea8=Number(_0x1c9ea8)['toFixed'](0x2),_0x5ddf57=Number(_0x5ddf57)['toFixed'](0x2),console['log']('minPrice',_0x1c9ea8),console[_0x33115d(0x247)]('maxPrice',_0x5ddf57);var _0x3d5272=filterCardsBasedOnPrice(_0x4e241d,_0x1c9ea8,_0x5ddf57);_0x3d5272=filterCardsWithAsins(_0x3d5272),console[_0x33115d(0x247)](_0x33115d(0x243),_0x3d5272);var _0xfd9635=await _0x4caa90(_0x33115d(0x233)),_0x490a54=await getSkuListFromLocalStorage(),_0xeb0ca2=getAsinsFromCards(_0x3d5272),_0x58d072=_0x490a54[_0x33115d(0x23d)](_0x3dd28e=>_0xeb0ca2[_0x33115d(0x208)](_0x3dd28e));console[_0x33115d(0x247)](_0x33115d(0x260),_0x58d072),console['log'](_0x33115d(0x21e),_0x58d072['length']),console['log'](_0x33115d(0x233),_0xfd9635);if(_0x58d072[_0x33115d(0x1b1)]>=_0xfd9635)return console[_0x33115d(0x247)](_0x33115d(0x24e)),[];else console[_0x33115d(0x247)](_0x33115d(0x1fb));var _0x599480=await _0x4caa90('productList');!_0x599480&&(_0x599480=[]);var _0x129c58=_0x599480[_0x33115d(0x1b6)](_0x1f12e8=>_0x1f12e8['asin']);_0x3d5272=_0x3d5272[_0x33115d(0x23d)](_0x1a5ad3=>!_0x129c58[_0x33115d(0x208)](extractAsinFromCard(_0x1a5ad3))),console[_0x33115d(0x247)](_0x33115d(0x1c4),_0x3d5272);var _0x207b6d=await _0x4caa90(_0x33115d(0x252));_0x207b6d&&(_0x3d5272=filterBooksFromCards(_0x3d5272),console[_0x33115d(0x247)](_0x33115d(0x1e1),_0x3d5272));var _0x11dd87=await _0x4caa90(_0x33115d(0x25c));_0x11dd87&&_0x11dd87[_0x33115d(0x1b1)]>0x0&&(_0x3d5272=_0x3d5272[_0x33115d(0x23d)](_0x53ded4=>{var _0x66583a=extractTitleFromCard(_0x53ded4);return!titleContainsRestrictedWords(_0x66583a,_0x11dd87);}),console['log'](_0x33115d(0x209),_0x3d5272));var _0x2a0b62=await _0x4caa90('useRequiredKeywords');if(_0x2a0b62){var _0x3e3a22=await _0x4caa90('keywords'),_0x536619=await _0x4caa90(_0x33115d(0x26d));_0x3e3a22&&_0x3e3a22[_0x33115d(0x1b1)]>0x0&&(_0x3d5272=_0x3d5272[_0x33115d(0x23d)](_0x1885fb=>{var _0x236a5f=extractTitleFromCard(_0x1885fb);return titleContainsRequiredKeywords(_0x236a5f,_0x3e3a22,_0x536619);}),console[_0x33115d(0x247)](_0x33115d(0x20b),_0x3d5272));}var _0x1ee8f1=await _0x4caa90(_0x33115d(0x1e7));if(_0x1ee8f1){var _0x3709dc=await _0x4caa90('veroBrands');!_0x3709dc&&(_0x3709dc=await fetch(chrome[_0x33115d(0x204)][_0x33115d(0x236)](_0x33115d(0x23f))),_0x3709dc=await _0x3709dc['text'](),_0x3709dc=_0x3709dc[_0x33115d(0x1e5)]('\x0a')),_0x3709dc=_0x3709dc[_0x33115d(0x23d)](_0x2ce2fa=>_0x2ce2fa['length']>0x0)['map'](_0x3c5b45=>_0x3c5b45['toLowerCase']()[_0x33115d(0x1f3)]()),_0x3709dc[_0x33115d(0x1b1)]>0x0&&(_0x3d5272=_0x3d5272['filter'](_0x2ca331=>{var _0x22c635=extractTitleFromCard(_0x2ca331);return!titleContainsRestrictedWords(_0x22c635,_0x3709dc);}),console[_0x33115d(0x247)](_0x33115d(0x1bb),_0x3d5272));}var _0x2b19ff=await _0x4caa90(_0x33115d(0x1d8));console[_0x33115d(0x247)]('min_reviews',_0x2b19ff);_0x2b19ff>0x0&&(_0x3d5272=_0x3d5272['filter'](_0x569e9f=>{var _0x4afb14=_0x33115d,_0x39853c=extractReviewsFromCard(_0x569e9f);return console[_0x4afb14(0x247)](_0x4afb14(0x1d2),_0x569e9f),console[_0x4afb14(0x247)]('reviews\x20from\x20card',_0x39853c),_0x39853c>=_0x2b19ff;}),console['log']('min_reviews\x20filtered\x20cards',_0x3d5272));var _0x5e231b=await _0x4caa90(_0x33115d(0x1e6));console[_0x33115d(0x247)](_0x33115d(0x1e6),_0x5e231b);_0x5e231b>0x0&&(_0x3d5272=_0x3d5272[_0x33115d(0x23d)](_0x3c0011=>{var _0x2c4001=extractReviewsFromCard(_0x3c0011);return _0x2c4001<=_0x5e231b;}),console[_0x33115d(0x247)](_0x33115d(0x1fc),_0x3d5272));var _0x28f0a3=await _0x4caa90(_0x33115d(0x229));console['log'](_0x33115d(0x229),_0x28f0a3);_0x28f0a3&&(_0x3d5272=sortCardsByReviews(_0x3d5272),console[_0x33115d(0x247)](_0x33115d(0x25b),_0x3d5272),_0x3d5272[_0x33115d(0x256)](_0x44e343=>{}));var _0x3c2edf=filterBestSellerCards(_0x3d5272);_0x3c2edf=sortCardsByReviews(_0x3c2edf),console['log'](_0x33115d(0x1e3),_0x3c2edf);var _0x26c174=filterAmazonChoiceCards(_0x3d5272);_0x26c174=sortCardsByReviews(_0x26c174),console['log'](_0x33115d(0x1df),_0x26c174);var _0x2fcbec=await _0x4caa90(_0x33115d(0x213)),_0x4c7976=await _0x4caa90(_0x33115d(0x206)),_0x585fdd=[];if(_0x2fcbec&&_0x4c7976)_0x585fdd=[..._0x26c174,..._0x3c2edf,..._0x3d5272];else{if(_0x2fcbec)_0x585fdd=[..._0x26c174,..._0x3d5272];else _0x4c7976?_0x585fdd=[..._0x3c2edf,..._0x3d5272]:_0x585fdd=[..._0x3d5272];}_0x585fdd=_0x585fdd[_0x33115d(0x23d)]((_0x301b5f,_0x251841,_0x25f592)=>_0x251841===_0x25f592[_0x33115d(0x1ef)](_0x57ecb5=>extractAsinFromCard(_0x57ecb5)===extractAsinFromCard(_0x301b5f))),console['log']('combined\x20cards',_0x585fdd);var _0x1c71b7=await _0x4caa90(_0x33115d(0x269));return console['log'](_0x33115d(0x269),_0x1c71b7),_0x1c71b7&&(_0x585fdd=_0x585fdd[_0x33115d(0x23d)](_0x4e55bc=>{var _0x56de33=extractAsinFromCard(_0x4e55bc);return!_0x490a54['includes'](_0x56de33);})),_0x585fdd=_0x585fdd[_0x33115d(0x1c1)](0x0,_0x39f774),_0x585fdd;}async function getAsins(){var _0x313f2d=a0_0x12df25,_0x2f468e=document[_0x313f2d(0x26e)](_0x313f2d(0x218)),_0x15d533=[],_0x5f3a07=[],{maxPrice:_0x580fa9}=await chrome[_0x313f2d(0x1d7)][_0x313f2d(0x1af)][_0x313f2d(0x1ad)](_0x313f2d(0x258));_0x580fa9=parseFloat(_0x580fa9);var {minPrice:_0x4682f2}=await chrome[_0x313f2d(0x1d7)][_0x313f2d(0x1af)][_0x313f2d(0x1ad)](_0x313f2d(0x200));_0x4682f2=parseFloat(_0x4682f2);for(var _0x140172=0x0;_0x140172<_0x2f468e[_0x313f2d(0x1b1)];_0x140172++){var _0x52f282=_0x2f468e[_0x140172][_0x313f2d(0x1f1)](_0x313f2d(0x1e8));console['log'](_0x313f2d(0x1c3),_0x52f282);var _0x5d6607=null;_0x52f282!=null&&(_0x5d6607=_0x52f282[_0x313f2d(0x1a5)][_0x313f2d(0x255)](/,/g,'')),console['log'](_0x313f2d(0x217),_0x5d6607),_0x5d6607=parseFloat(_0x5d6607),_0x5d6607!=null&&_0x5d6607<_0x580fa9&&_0x5d6607>_0x4682f2&&_0x15d533[_0x313f2d(0x202)](_0x2f468e[_0x140172]);}for(var _0x140172=0x0;_0x140172<_0x15d533[_0x313f2d(0x1b1)];_0x140172++){var _0x338c8f=_0x15d533[_0x140172][_0x313f2d(0x227)](_0x313f2d(0x1d3));_0x338c8f!=null&&_0x338c8f!==''&&!_0x5f3a07['includes'](_0x338c8f)&&_0x5f3a07[_0x313f2d(0x202)](_0x338c8f);}return console[_0x313f2d(0x247)](_0x5f3a07),_0x5f3a07;}function addAsinButton(_0x4ee770){var _0x152670=a0_0x12df25;chrome[_0x152670(0x1d7)][_0x152670(0x1af)][_0x152670(0x1ad)](_0x152670(0x1b7),function(_0x172b49){var _0xb541e6=_0x152670,_0x106474=_0x172b49['snipeModeEnabled'];console[_0xb541e6(0x247)]('Adding\x20ASIN\x20button\x20to\x20card:',_0x4ee770);var _0x451a86=_0x4ee770[_0xb541e6(0x227)](_0xb541e6(0x1d3));if(!_0x451a86){var _0x2a933d=_0x4ee770[_0xb541e6(0x26e)](_0xb541e6(0x1ec));_0x2a933d[_0xb541e6(0x1b1)]>0x0&&(_0x451a86=_0x2a933d[0x0][_0xb541e6(0x227)](_0xb541e6(0x1d3)));}if(_0x451a86){}else return;var _0x1f753f=_0x4ee770[_0xb541e6(0x1f1)](_0xb541e6(0x272))||_0x4ee770['querySelector'](_0xb541e6(0x1e0))||_0x4ee770[_0xb541e6(0x1f1)](_0xb541e6(0x273));!_0x1f753f&&console[_0xb541e6(0x247)](_0xb541e6(0x24d),_0x4ee770);if(skuList[_0xb541e6(0x208)](_0x451a86)){console['log'](_0xb541e6(0x267)+_0x451a86+_0xb541e6(0x1f8)),_0x4ee770[_0xb541e6(0x1c5)][_0xb541e6(0x1d1)](_0xb541e6(0x1d4));const _0x1a8066=document['createElement'](_0xb541e6(0x1c8));_0x1a8066[_0xb541e6(0x1c5)][_0xb541e6(0x1d1)](_0xb541e6(0x214)),_0x1a8066[_0xb541e6(0x203)]=_0xb541e6(0x23a);_0x1f753f?_0x1f753f['after'](_0x1a8066):_0x4ee770[_0xb541e6(0x21f)](_0x1a8066);return;}const _0x53130c=document[_0xb541e6(0x20f)](_0xb541e6(0x242));_0x53130c['classList']['add'](_0xb541e6(0x25a)),_0x106474?_0x53130c['textContent']=_0xb541e6(0x25f)+_0x451a86:_0x53130c[_0xb541e6(0x203)]=_0xb541e6(0x224)+_0x451a86,_0x53130c['addEventListener'](_0xb541e6(0x1ac),async _0x26a108=>{var _0x331365=_0xb541e6;_0x26a108[_0x331365(0x1f5)](),console[_0x331365(0x247)](_0x451a86),skuList['push'](_0x451a86),_0x53130c[_0x331365(0x21c)]=!![],_0x4ee770[_0x331365(0x1c5)][_0x331365(0x1de)](_0x331365(0x25a)),_0x53130c[_0x331365(0x1c5)]['add'](_0x331365(0x210)),_0x106474?(_0x53130c[_0x331365(0x203)]=_0x331365(0x20e),navigator['clipboard'][_0x331365(0x1f9)]()[_0x331365(0x238)](_0x38a443=>{var _0x489d8e=_0x331365;console[_0x489d8e(0x247)](_0x38a443);var _0x554472=null;try{_0x554472=JSON[_0x489d8e(0x1d9)](_0x38a443);}catch(_0x1b7440){console['log'](_0x489d8e(0x1ed),_0x1b7440),alert(_0x489d8e(0x1c9));return;}if(_0x554472[_0x489d8e(0x1f7)]==null||_0x554472[_0x489d8e(0x21d)]==null||_0x554472[_0x489d8e(0x23b)]==null){alert(_0x489d8e(0x26a));return;}var _0x5bec67=extractPriceFromElement(_0x4ee770);if(_0x5bec67>_0x554472['price']){alert(_0x489d8e(0x1ae));return;}chrome[_0x489d8e(0x204)][_0x489d8e(0x1f4)]({'type':_0x489d8e(0x234),'asin':_0x451a86,'snipeData':_0x554472},function(_0x54d480){var _0x18a231=_0x489d8e;console[_0x18a231(0x247)](_0x54d480[_0x18a231(0x263)]);});})):(_0x53130c[_0x331365(0x203)]='Listing...',chrome[_0x331365(0x204)][_0x331365(0x1f4)]({'type':_0x331365(0x1e4),'asin':_0x451a86},function(_0x242340){var _0x5f3778=_0x331365;console[_0x5f3778(0x247)](_0x242340[_0x5f3778(0x263)]);}));}),_0x1f753f?_0x1f753f[_0xb541e6(0x23c)](_0x53130c):_0x4ee770['appendChild'](_0x53130c);});}function addAsinToItems(){var _0x1dff31=a0_0x12df25,_0x17d2ac=document[_0x1dff31(0x26e)](_0x1dff31(0x218));console['log'](_0x1dff31(0x1fa)+_0x17d2ac[_0x1dff31(0x1b1)]+'\x20cards'),_0x17d2ac[_0x1dff31(0x256)](_0x12c9b8=>{var _0x1a6f5e=_0x1dff31;addAsinButton(_0x12c9b8);var _0xc50e16=extractTitleFromCard(_0x12c9b8),_0x83eca8=createEbaySearchButton(_0xc50e16);try{_0x12c9b8[_0x1a6f5e(0x1f1)](_0x1a6f5e(0x265))[_0x1a6f5e(0x21f)](_0x83eca8);}catch(_0x2081ad){}});}function addAsinToCarouselItems(){var _0x1313c1=a0_0x12df25,_0x5965b2=document[_0x1313c1(0x26e)](_0x1313c1(0x1d6));console[_0x1313c1(0x247)](_0x1313c1(0x1fa)+_0x5965b2[_0x1313c1(0x1b1)]+_0x1313c1(0x254)),_0x5965b2[_0x1313c1(0x256)](_0x15920e=>{var _0x928f7b=_0x1313c1;addAsinButton(_0x15920e);var _0x42bea3=extractTitleFromCarouselCard(_0x15920e);console[_0x928f7b(0x247)](_0x928f7b(0x268),_0x42bea3);var _0x25286a=createEbaySearchButton(_0x42bea3);try{_0x15920e[_0x928f7b(0x21f)](_0x25286a);}catch(_0x377676){console[_0x928f7b(0x251)](_0x928f7b(0x275),_0x377676);}});}async function addAsinButtons(){var _0x43cdf3=a0_0x12df25;console[_0x43cdf3(0x247)](_0x43cdf3(0x1be)+skuList[_0x43cdf3(0x1b1)]+_0x43cdf3(0x1e9)),addAsinToItems(),addAsinToCarouselItems();const _0x3024ab=new MutationObserver(_0x257538=>{var _0x362431=_0x43cdf3;_0x257538[_0x362431(0x256)](_0x2e9828=>{_0x2e9828['addedNodes']['forEach'](_0x18f2fd=>{var _0x595f82=a0_0x4918;_0x18f2fd['classList']&&_0x18f2fd[_0x595f82(0x1c5)][_0x595f82(0x250)](_0x595f82(0x211))&&addAsinButton(_0x18f2fd);});});});_0x3024ab['observe'](document['body'],{'childList':!![],'subtree':!![]});}function collectAsins(){var _0x1bf45f=a0_0x12df25;const _0x1480db=[],_0x35a375=document['querySelectorAll']('.s-result-item');return _0x35a375[_0x1bf45f(0x256)](_0x48b6ac=>{var _0x55c332=_0x1bf45f;const _0x1fbed0=_0x48b6ac[_0x55c332(0x227)](_0x55c332(0x1d3));_0x1fbed0&&_0x1480db[_0x55c332(0x202)](_0x1fbed0);}),console['log'](_0x1480db),_0x1480db;}const defaultParams={'totalAsinsToFetchFromSearch':0x64,'appendToAmazonLinks':![],'minPrice':0x0,'maxPrice':0x3e8,'min_reviews':0x1,'max_reviews':0xf423f,'sortByReviews':!![],'remove_books':!![],'useRequiredKeywords':![],'veroProtectionEnabled':![],'prioritizeAmazonChoice':!![],'prioritizeBestSellers':!![],'duplicate_protection':!![],'max_similiar_niche_items':0x1869f};var paramsStorageKey='collectAsinsParams';async function addCollectAsinsButton(){var _0x31eef0=a0_0x12df25;const _0x135844=document[_0x31eef0(0x1f1)](_0x31eef0(0x1aa));if(!_0x135844)return;const _0x3ed57c=document['createElement'](_0x31eef0(0x242));_0x3ed57c[_0x31eef0(0x1c5)]['add'](_0x31eef0(0x237)),_0x3ed57c[_0x31eef0(0x203)]=_0x31eef0(0x274);const _0x42d25c=document['createElement'](_0x31eef0(0x242));_0x42d25c[_0x31eef0(0x1c5)][_0x31eef0(0x1d1)](_0x31eef0(0x1cf)),_0x42d25c[_0x31eef0(0x1a3)]='⚙️',_0x42d25c[_0x31eef0(0x1f7)]=_0x31eef0(0x25d),_0x42d25c[_0x31eef0(0x20c)]('click',_0x219cab=>{var _0x24871b=_0x31eef0;_0x219cab[_0x24871b(0x1f5)](),openSettingsModal();}),_0x3ed57c['addEventListener'](_0x31eef0(0x1ac),async _0x2de6c8=>{var _0x4a7a5a=_0x31eef0;_0x2de6c8[_0x4a7a5a(0x1f5)]();const _0x47a3f1=await getDomain();let _0x57c4fa=await getParamsFromStorage();console[_0x4a7a5a(0x247)]('Parameters\x20used\x20for\x20collecting\x20ASINs:',_0x57c4fa);var _0x71cb69=await retrieveFilteredCards(_0x57c4fa),_0xa9386a=[];for(var _0x95ce22=0x0;_0x95ce22<_0x71cb69['length'];_0x95ce22++){var _0x587382=_0x71cb69[_0x95ce22],_0x38863c=extractAsinFromCard(_0x587382),_0x9f0913=extractTitleFromCard(_0x587382),_0x231282=extractPriceFromElement(_0x587382),_0x1f4613=extractImageUrlFromCard(_0x587382),_0x5a9437=extractReviewsFromCard(_0x587382),_0x570f20={'asin':_0x38863c,'title':_0x9f0913,'price':_0x231282,'image':_0x1f4613,'reviewCount':_0x5a9437};_0xa9386a[_0x4a7a5a(0x202)](_0x570f20);}var _0x36bbf4=_0xa9386a[_0x4a7a5a(0x1b6)](_0x2e294f=>_0x2e294f[_0x4a7a5a(0x24c)]),_0x1ad67a=_0x36bbf4['map'](_0xbe554e=>'https://www.amazon.'+_0x47a3f1+_0x4a7a5a(0x222)+_0xbe554e+_0x4a7a5a(0x1ab)),{amazonLinks:_0x5cbb58}=await chrome[_0x4a7a5a(0x1d7)][_0x4a7a5a(0x1af)][_0x4a7a5a(0x1ad)](_0x4a7a5a(0x220));_0x5cbb58=_0x5cbb58||[];!_0x57c4fa[_0x4a7a5a(0x21b)]&&(_0x5cbb58=[]);const _0x45dc1a=[...new Set([..._0x5cbb58,..._0x1ad67a])];chrome['storage'][_0x4a7a5a(0x1af)][_0x4a7a5a(0x223)]({'amazonLinks':_0x45dc1a}),navigator[_0x4a7a5a(0x24a)]['writeText'](_0x45dc1a[_0x4a7a5a(0x225)]('\x0a')),_0x3ed57c['textContent']=_0x1ad67a[_0x4a7a5a(0x1b1)]+'\x20New\x20ASINs\x20Copied\x20to\x20Clipboard\x20(Total:\x20'+_0x45dc1a[_0x4a7a5a(0x1b1)]+')',setTimeout(()=>{var _0x32e587=_0x4a7a5a;_0x3ed57c['textContent']=_0x32e587(0x274);},0x1388);}),_0x135844[_0x31eef0(0x266)][_0x31eef0(0x22d)](_0x3ed57c,_0x135844[_0x31eef0(0x1b0)]),_0x135844[_0x31eef0(0x266)][_0x31eef0(0x22d)](_0x42d25c,_0x3ed57c['nextSibling']);}function a0_0x4918(_0x29d40c,_0xea2cac){var _0x3ace4e=a0_0x3ace();return a0_0x4918=function(_0x4918e1,_0x1c48b7){_0x4918e1=_0x4918e1-0x1a3;var _0x1daff6=_0x3ace4e[_0x4918e1];return _0x1daff6;},a0_0x4918(_0x29d40c,_0xea2cac);}async function getParamsFromStorage(){return new Promise((_0x2b95a4,_0x4a6889)=>{var _0x3c7f0e=a0_0x4918;chrome['storage'][_0x3c7f0e(0x1af)][_0x3c7f0e(0x1ad)](paramsStorageKey,_0xc022d7=>{var _0x353bec=_0x3c7f0e;if(chrome[_0x353bec(0x204)][_0x353bec(0x215)])console[_0x353bec(0x1d5)](_0x353bec(0x1ea),chrome[_0x353bec(0x204)][_0x353bec(0x215)]),_0x2b95a4({...defaultParams});else{const _0x5c859c=_0xc022d7[paramsStorageKey];if(_0x5c859c){const _0x33cb7d={...defaultParams,..._0x5c859c};_0x2b95a4(_0x33cb7d);}else _0x2b95a4({...defaultParams});}});});}async function saveParamsToStorage(_0x127af4){return new Promise((_0x3118a5,_0x37e992)=>{var _0x20589f=a0_0x4918;const _0xb865c6={};_0xb865c6[paramsStorageKey]={...defaultParams,..._0x127af4},chrome[_0x20589f(0x1d7)][_0x20589f(0x1af)]['set'](_0xb865c6,()=>{var _0x4e9345=_0x20589f;chrome[_0x4e9345(0x204)][_0x4e9345(0x215)]?(console[_0x4e9345(0x1d5)](_0x4e9345(0x20d),chrome['runtime'][_0x4e9345(0x215)]),_0x37e992(chrome[_0x4e9345(0x204)][_0x4e9345(0x215)])):_0x3118a5();});});}function openSettingsModal(){createSettingsModal();}async function createSettingsModal(){var _0x301880=a0_0x12df25;if(document['getElementById'](_0x301880(0x1a4)))return;let _0x37ad47=await getParamsFromStorage();!_0x37ad47&&(_0x37ad47=defaultParams,await saveParamsToStorage(defaultParams));const _0x55a4c0=document[_0x301880(0x20f)]('div');_0x55a4c0['id']=_0x301880(0x1a4),_0x55a4c0[_0x301880(0x1c5)][_0x301880(0x1d1)]('settings-modal-overlay'),_0x55a4c0[_0x301880(0x20c)](_0x301880(0x1ac),function(_0x40a6ee){var _0x403904=_0x301880;_0x40a6ee[_0x403904(0x1e2)]===_0x55a4c0&&closeSettingsModal();});const _0xdd4add=document[_0x301880(0x20f)]('div');_0xdd4add['id']=_0x301880(0x21a),_0xdd4add['classList'][_0x301880(0x1d1)](_0x301880(0x21a));const _0x3dc647=document[_0x301880(0x20f)](_0x301880(0x271));_0x3dc647['classList'][_0x301880(0x1d1)](_0x301880(0x23e));const _0x3e0892=document[_0x301880(0x20f)](_0x301880(0x271));_0x3e0892[_0x301880(0x1c5)][_0x301880(0x1d1)](_0x301880(0x1bf));const _0x37dcd2=document[_0x301880(0x20f)]('h2');_0x37dcd2[_0x301880(0x203)]=_0x301880(0x25d),_0x3e0892[_0x301880(0x21f)](_0x37dcd2);const _0x150c35=document[_0x301880(0x20f)](_0x301880(0x1c8));_0x150c35[_0x301880(0x1c5)]['add'](_0x301880(0x22b)),_0x150c35[_0x301880(0x1a3)]='&times;',_0x150c35[_0x301880(0x20c)](_0x301880(0x1ac),closeSettingsModal),_0x3e0892[_0x301880(0x21f)](_0x150c35),_0x3dc647[_0x301880(0x21f)](_0x3e0892);const _0x14b3f6=document[_0x301880(0x20f)]('form');_0x14b3f6['id']=_0x301880(0x1dd);const _0x1a0bbf=[{'key':_0x301880(0x245),'label':_0x301880(0x241)},{'key':_0x301880(0x21b),'label':'Append\x20to\x20Existing\x20ASINs'},{'key':_0x301880(0x200),'label':_0x301880(0x1c6)},{'key':_0x301880(0x258),'label':_0x301880(0x22f)},{'key':_0x301880(0x1d8),'label':_0x301880(0x246)},{'key':_0x301880(0x1e6),'label':_0x301880(0x216)},{'key':_0x301880(0x229),'label':_0x301880(0x26b)},{'key':_0x301880(0x252),'label':_0x301880(0x1dc)},{'key':'useRequiredKeywords','label':'Use\x20Required\x20Keywords'},{'key':_0x301880(0x1e7),'label':'Vero\x20Protection\x20Enabled'},{'key':_0x301880(0x213),'label':_0x301880(0x235)},{'key':_0x301880(0x206),'label':'Prioritize\x20Best\x20Sellers'},{'key':_0x301880(0x269),'label':'Duplicate\x20Protection'},{'key':'max_similiar_niche_items','label':_0x301880(0x219)}];_0x1a0bbf[_0x301880(0x256)](_0x3175da=>{var _0x412ec0=_0x301880;const _0x16443f=_0x3175da['key'],_0x2d326e=_0x3175da[_0x412ec0(0x1b4)],_0x1113d9=_0x37ad47[_0x16443f],_0x18f08b=document[_0x412ec0(0x20f)]('div');_0x18f08b['classList']['add'](_0x412ec0(0x212));if(typeof _0x1113d9===_0x412ec0(0x20a)){const _0x17385f=document[_0x412ec0(0x20f)]('input');_0x17385f[_0x412ec0(0x259)]=_0x412ec0(0x1cc),_0x17385f['id']=_0x412ec0(0x248)+_0x16443f,_0x17385f[_0x412ec0(0x253)]=_0x1113d9;const _0x273c24=document[_0x412ec0(0x20f)](_0x412ec0(0x1b4));_0x273c24['htmlFor']='param-'+_0x16443f,_0x273c24[_0x412ec0(0x203)]=_0x2d326e,_0x18f08b[_0x412ec0(0x21f)](_0x17385f),_0x18f08b[_0x412ec0(0x21f)](_0x273c24);}else{const _0x357db2=document[_0x412ec0(0x20f)]('label');_0x357db2['htmlFor']=_0x412ec0(0x248)+_0x16443f,_0x357db2[_0x412ec0(0x203)]=_0x2d326e;const _0x5ecd52=document[_0x412ec0(0x20f)]('input');_0x5ecd52[_0x412ec0(0x259)]=_0x412ec0(0x1f0),_0x5ecd52['id']=_0x412ec0(0x248)+_0x16443f,_0x5ecd52[_0x412ec0(0x1c7)]=_0x1113d9,_0x18f08b[_0x412ec0(0x21f)](_0x357db2),_0x18f08b[_0x412ec0(0x21f)](_0x5ecd52);}_0x14b3f6['appendChild'](_0x18f08b);});const _0x1c1bd7=document[_0x301880(0x20f)](_0x301880(0x271));_0x1c1bd7[_0x301880(0x1c5)][_0x301880(0x1d1)](_0x301880(0x1c2));const _0x3e30cf=document[_0x301880(0x20f)](_0x301880(0x242));_0x3e30cf['type']='button',_0x3e30cf[_0x301880(0x203)]=_0x301880(0x240),_0x3e30cf[_0x301880(0x20c)]('click',copyResults);const _0x2f97e1=document[_0x301880(0x20f)](_0x301880(0x242));_0x2f97e1[_0x301880(0x259)]=_0x301880(0x242),_0x2f97e1[_0x301880(0x203)]='Clear\x20Results',_0x2f97e1['addEventListener'](_0x301880(0x1ac),clearResults),_0x1c1bd7[_0x301880(0x21f)](_0x3e30cf),_0x1c1bd7[_0x301880(0x21f)](_0x2f97e1),_0x14b3f6[_0x301880(0x21f)](_0x1c1bd7);const _0x4c137f=document[_0x301880(0x20f)](_0x301880(0x271));_0x4c137f['classList'][_0x301880(0x1d1)](_0x301880(0x276));const _0x312dac=document[_0x301880(0x20f)](_0x301880(0x242));_0x312dac[_0x301880(0x259)]=_0x301880(0x242),_0x312dac['textContent']=_0x301880(0x1c0),_0x312dac[_0x301880(0x20c)](_0x301880(0x1ac),saveSettings);const _0x3c8b53=document['createElement'](_0x301880(0x242));_0x3c8b53[_0x301880(0x259)]='button',_0x3c8b53['textContent']=_0x301880(0x207),_0x3c8b53[_0x301880(0x20c)](_0x301880(0x1ac),closeSettingsModal),_0x4c137f[_0x301880(0x21f)](_0x3c8b53),_0x4c137f['appendChild'](_0x312dac),_0x14b3f6[_0x301880(0x21f)](_0x4c137f),_0x3dc647['appendChild'](_0x14b3f6),_0xdd4add[_0x301880(0x21f)](_0x3dc647),_0x55a4c0[_0x301880(0x21f)](_0xdd4add),document[_0x301880(0x1d0)][_0x301880(0x21f)](_0x55a4c0);}function copyResults(){var _0x380854=a0_0x12df25;chrome[_0x380854(0x1d7)][_0x380854(0x1af)]['get'](_0x380854(0x220),_0x4553db=>{var _0x2cf967=_0x380854;const _0x3992ad=_0x4553db['amazonLinks']||[];_0x3992ad[_0x2cf967(0x1b1)]>0x0?navigator['clipboard']['writeText'](_0x3992ad[_0x2cf967(0x225)]('\x0a'))[_0x2cf967(0x238)](()=>{var _0x4a5d16=_0x2cf967;alert(_0x4a5d16(0x231));}):alert(_0x2cf967(0x1bd));});}function clearResults(){var _0x5c46c7=a0_0x12df25;chrome[_0x5c46c7(0x1d7)][_0x5c46c7(0x1af)][_0x5c46c7(0x223)]({'amazonLinks':[]},()=>{var _0x420294=_0x5c46c7;alert(_0x420294(0x270));});}function closeSettingsModal(){var _0x2fefe3=a0_0x12df25;const _0x5c2a19=document[_0x2fefe3(0x1fe)](_0x2fefe3(0x1a4));_0x5c2a19&&_0x5c2a19[_0x2fefe3(0x266)][_0x2fefe3(0x1ba)](_0x5c2a19);}async function saveSettings(){var _0x4e9d42=a0_0x12df25;const _0x418bd7={};for(const _0xd5c7a7 in defaultParams){const _0x4e4ea0=document[_0x4e9d42(0x1fe)](_0x4e9d42(0x248)+_0xd5c7a7);if(_0x4e4ea0){if(_0x4e4ea0[_0x4e9d42(0x259)]==='checkbox')_0x418bd7[_0xd5c7a7]=_0x4e4ea0[_0x4e9d42(0x253)];else{if(_0x4e4ea0[_0x4e9d42(0x259)]===_0x4e9d42(0x1f0))_0x418bd7[_0xd5c7a7]=Number(_0x4e4ea0[_0x4e9d42(0x1c7)]);else _0x4e4ea0[_0x4e9d42(0x259)]===_0x4e9d42(0x1da)&&(_0x418bd7[_0xd5c7a7]=_0x4e4ea0[_0x4e9d42(0x1c7)]);}}}await saveParamsToStorage(_0x418bd7),closeSettingsModal();}async function getDomain(){return new Promise((_0x2aba2f,_0x2a5820)=>{var _0x5de0a5=a0_0x4918;chrome[_0x5de0a5(0x1d7)][_0x5de0a5(0x1af)][_0x5de0a5(0x1ad)](_0x5de0a5(0x1a9),_0x4d63df=>{var _0x2f937f=_0x5de0a5;chrome['runtime'][_0x2f937f(0x215)]?_0x2a5820(chrome['runtime'][_0x2f937f(0x215)]):_0x2aba2f(_0x4d63df['domain']||_0x2f937f(0x1a8));});});}