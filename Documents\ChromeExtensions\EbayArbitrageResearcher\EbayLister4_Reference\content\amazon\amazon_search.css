.list-asin-button {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  padding: 8px;
  background-color: #0077c2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  
}

.list-asin-button:hover {
  background-color: #005ea6;
}

.list-asin-card--duplicate {
  /* border: 2px solid red; */
}

.list-asin-card--duplicate .list-asin-card--duplicate-text {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  padding: 8px;
  background-color: #f0481f;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
}

.listing-now{
  background-color: #4CAF50;
}

.collect-asins-button {
  background-color: #f0c14b;
  border: none;
  border-radius: 3px;
  color: #111;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  padding: 6px 12px;
  text-align: center;
  text-decoration: none;
  transition: background-color 0.2s ease-in-out;
}

.collect-asins-button:hover {
  background-color: #d4a741;
}



/* #ebayLink {
  position: relative;
  z-index: 9999;


} */

#ebayLink {
  position: relative;
  z-index: 9999;
}






/* modal settings for collect asins*/

/* Modal overlay */
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 9999;
}

/* Modal */
.settings-modal {
  background-color: #fff;
  width: 500px;
  max-width: 90%;
  margin: 50px auto;
  border-radius: 5px;
  overflow: hidden;
}

/* Modal content */
.settings-modal-content {
  padding: 20px;
}

/* Modal header */
.settings-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Modal title */
.settings-modal-header h2 {
  margin: 0;
}

/* Modal close button */
.settings-modal-close {
  cursor: pointer;
  font-size: 24px;
}

/* Form group */
.form-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

/* Form group label */
.form-group label {
  flex: 1;
  margin-right: 10px;
}

/* Form group input */
.form-group input[type="text"],
.form-group input[type="number"] {
  flex: 1;
  padding: 8px;
  box-sizing: border-box;
}

.form-group input[type="checkbox"] {
  margin-right: 10px;
}

/* Button group */
.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Buttons */
.button-group button {
  padding: 10px 20px;
  margin-left: 10px;
  cursor: pointer;
}

/* Settings button */
.settings-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  padding: 0 10px;
}

.settings-button:hover {
  color: #0073e6;
}

/* Collect ASINs button */
.collect-asins-button {
  margin-right: 10px;
  padding: 10px 20px;
  cursor: pointer;
}


/* Additional Buttons */
.clear-results-button,
.copy-results-button {
  margin-right: 10px;
  padding: 10px 20px;
  cursor: pointer;
}

/* Update existing buttons to align properly */
.collect-asins-button,
.settings-button,
.clear-results-button,
.copy-results-button {
  margin-right: 5px;
}

/* Form group for settings modal */
.form-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

/* Label and input alignment */
.form-group label {
  flex: 1;
  margin-right: 10px;
}

/* Form group input */
.form-group input[type="text"],
.form-group input[type="number"] {
  flex: 1;
  padding: 8px;
  box-sizing: border-box;
}

.form-group input[type="checkbox"] {
  margin-right: 10px;
}

/* Section headers (optional) */
.form-section {
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
}

/* Results button group inside modal */
.results-button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Buttons inside results button group */
.results-button-group button {
  padding: 10px 20px;
  margin-left: 10px;
  cursor: pointer;
}
