var a0_0x2a34e2=a0_0x5685;(function(_0x2dd95d,_0x43cb0d){var _0x5e0ded=a0_0x5685,_0x1d9da0=_0x2dd95d();while(!![]){try{var _0x2f3877=parseInt(_0x5e0ded(0x14a))/0x1*(-parseInt(_0x5e0ded(0x192))/0x2)+-parseInt(_0x5e0ded(0xf8))/0x3+parseInt(_0x5e0ded(0x1c4))/0x4*(-parseInt(_0x5e0ded(0x175))/0x5)+parseInt(_0x5e0ded(0x160))/0x6*(parseInt(_0x5e0ded(0x158))/0x7)+-parseInt(_0x5e0ded(0x128))/0x8+parseInt(_0x5e0ded(0x17e))/0x9+parseInt(_0x5e0ded(0x116))/0xa;if(_0x2f3877===_0x43cb0d)break;else _0x1d9da0['push'](_0x1d9da0['shift']());}catch(_0x3403b3){_0x1d9da0['push'](_0x1d9da0['shift']());}}}(a0_0x2c44,0xe9944),console[a0_0x2a34e2(0x161)](a0_0x2a34e2(0x198)));function a0_0x2c44(){var _0x364ad1=['Pago','local','Image\x20','text/html','Ebay_Description_Templates/Template_with_head.html','descriptionPrompt',';\x22>','</div>','innerHTML','\x0a<br><br>\x0a','https://i.ebayimg.com/images/g','\x22\x20alt=\x22Slide\x20','{{Feedback_Header}}','storage','OpenAI:\x20Generating\x20Description...','.uploader-thumbnails__image','blur','Envío','createEvent','.html','className','change','money-back','generate_description','{{productDescription}}','3200133QzZWCB','.uploader-thumbnails-ux__image','isArray','toLowerCase','type','document','120-day','{{Contact_Info}}','/Return_Info/Return_Info-','Contáctenos','{{productTitle}}','error','radio-buttons','OpenAI:\x20Adding\x20Description\x20to\x20Ebay','setAttribute','message','.panel','contentDocument','<br>\x0a\x20\x20<br>\x0a\x0a','{{Shipping_Header}}','tee123','{{Return_Info}}','querySelector','then','{{Product_Image_URL}}',';\x20color:\x20','https://res.cloudinary.com/hmdenahha/image/upload/v1486462923/returns_zpsxr9puwik_mwvvon.jpg','.carousel','Arial','black','33392400pLakDD','E-Book','input','OpenAI:\x20Description\x20Generated','getURL','runtime','<img$1src=\x27\x27$5>','Zahlungsinformationen','Kontaktieren\x20Sie\x20uns','Contattaci','/Main_Template/Main_Template_3.html','lifetime','bullet_points','text_prompts/description_prompt.txt','amazon','removeFlaggedKeywords\x20template','includes','Achetez\x20maintenant\x20et\x20profitez\x20d\x27une\x20expérience\x20de\x20magasinage\x20agréable!','2069992USjwsL','Address','{{Product_Carousel}}','text','worry-free','data-label','Ebay_Description_Templates/Description_Template_2023/','16px','description_html','Revenir','createDescriptionFromTemplate2023','doc','{{Contact_Us_Header}}','Buy\x20it\x20now\x20and\x20enjoy\x20a\x20pleasant\x20shopping\x20experience!','\x22\x20class=\x22prev-slide\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span>&lsaquo;</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22img-','{{Store_Thank_You_Message}}','get','{{Shipping_Image_URL}}','addEventListener','initEvent','se-rte-frame__summary','user_input','<div\x20style=\x22font-family:\x20','/s-l1600.jpg','Call_To_Action_HTML/call_to_action.Html','https://res.cloudinary.com/hmdenahha/image/upload/v1486463259/shipping_zpsehr4l7lo_vs2j70.jpg','replaceAll','contentWindow','Feedback','thumbnail\x20image','{{SKU}}','focus','Thank\x20you\x20for\x20supporting\x20our\x20small\x20family\x20business!','limited','3RuvTRO','/Feedback_Info/Feedback_Info.html','{{Shipping_Info}}','split','replace','/Contact_Info/Contact_Info-','dispatchEvent','30-day','year','<br>','remove','ebay','Error:\x20','HTMLEvents','62643feEjMA','outerHTML','json','Pagamento','https://assets.cashforyourmac.com/image/upload/v1/Static/logo/ebay-rating.svg','join','optimizedDescription','Payment','642CKnWLI','log','phone','https://www.ecommercebytes.com/wp-content/uploads/2020/05/eBay_Managed_Payments.jpg','response.error','/Shipping_Info/Shipping_Info.default.html','amazon.ca','3px\x20solid\x20#DFFF00','Please\x20set\x20a\x20custom\x20description\x20template\x20in\x20the\x20settings.','LINK','match','custom_title','querySelectorAll','sku','Ebay:\x20Description\x20Added',';\x20font-size:\x20','Risposta','Kaufen\x20Sie\x20jetzt\x20und\x20genießen\x20Sie\x20ein\x20angenehmes\x20Einkaufserlebnis!','getAttribute','{{Feedback_Image_URL}}','/Feedback_Info/Feedback_Info-','261645EsLnCe','Contactez\x20nous','checked','{{Payment_Image_URL}}','product','domain','OpenAI:\x20Description\x20Added\x20to\x20Ebay','MONTH','Regreso','2996739iakvQM','length','push','removeSectionsFromDescriptionTemplate','https://us-central1-ecomsniper-cb046.cloudfunctions.net/structured_output','style','\x22\x20class=\x22next-slide\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span>&rsaquo;</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20','str','.se-rte-editor__rich','Return','event:\x20','Contact\x20Us','.ca','resolution','Spedizione','url(\x27','server_error','<a$1href=\x27\x27$5>','E-Mail','prompts_json/description_gen.json','483570ScZFbw','Paiement','Acquista\x20ora\x20e\x20goditi\x20un\x27esperienza\x20di\x20shopping\x20piacevole!','descriptionText','Email\x20Address','24-MONTH','ebay_description.js','{{Product_Description}}','undefined','div','appendChild','{{Payment_Info}}','slides','/Payment_Info/Payment_Info-','satisfaction','Ebay_Description_Templates/Shipping_Handling.html','getBasicDescription','removeMPNFromDescription\x20template','createElement','getDescriptionHtmlFromOpenAi\x20descriptionHtml','getElementById','parseFromString','carousel','/Main_Template/Main_Template_2.html','warran-ty','Expédition','documentElement','display','contentEditable\x20does\x20not\x20exist','mpn','{{Feedback_Info}}','click','SATISFACTION\x20GUARANTEE','<!DOCTYPE\x20html>\x0a','{{Return_Image_URL}}','radio','/Shipping_Info/Shipping_Info.','Trigger\x20Description\x20Network\x20Request','Ebay:\x20Adding\x20Description...','replacement','Retour\x20d\x27information','bullet_points_html','Ritorno','result','https:','body','/Return_Info/Return_Info.html','3-\x20free','optimized_description','removeBrandFromDescription\x20template','124avrvsl','brand','BROWSER','{{Product_Title}}','You\x20are\x20an\x20expert\x20copywriter.\x20Turn\x20the\x20user’s\x20text\x20into\x20an\x20appealing\x20product\x20description.\x20Keep\x20it\x20concise\x20and\x20professional.','footerss','MONEY\x20BACK','Versand','{{Payment_Header}}','contentEditable\x20exists','https://ebaysnipertitlebuilder.ngrok.io/api/TitleBuilder/GenerateDescription','.accordion'];a0_0x2c44=function(){return _0x364ad1;};return a0_0x2c44();}function pasteDescription(_0x5e049d){return new Promise(async(_0x446614,_0xe0da16)=>{var _0x4bf879=a0_0x5685;documentTitle=_0x4bf879(0x1b8);var _0x9905e2=chrome[_0x4bf879(0x11b)]['getURL'](_0x4bf879(0x1d4)),_0x1d78ba=await fetch(_0x9905e2)['then'](_0x2f2303=>_0x2f2303[_0x4bf879(0x12b)]());_0x1d78ba=addDetailsToTemplate(_0x1d78ba,_0x5e049d),_0x1d78ba=removeFlaggedKeywords(_0x1d78ba),_0x1d78ba=removeMPNFromDescription(_0x1d78ba,_0x5e049d[_0x4bf879(0x1af)]),_0x1d78ba=removeBrandFromDescription(_0x1d78ba,_0x5e049d[_0x4bf879(0x1c5)]);var _0x3c4997=chrome[_0x4bf879(0x11b)][_0x4bf879(0x11a)](_0x4bf879(0x1a1)),_0x444c6f=await fetch(_0x3c4997)[_0x4bf879(0x10f)](_0x47d95a=>_0x47d95a['text']()),_0x2492f5=await fetchCallToActionHtml();_0x1d78ba=_0x1d78ba+_0x2492f5+_0x444c6f,await addHtmlToEbayDescription(_0x1d78ba),documentTitle=_0x4bf879(0x16e),await triggerDescriptionNetworkRequest(),await waitForDescriptionNetworkResponse(),_0x446614();});}async function getBasicDescription(_0x17e257){var _0x14f14e=a0_0x2a34e2;console[_0x14f14e(0x161)](_0x14f14e(0x1a2));var _0x4335de=chrome[_0x14f14e(0x11b)]['getURL'](_0x14f14e(0x1d4)),_0x54f5fa=await fetch(_0x4335de)[_0x14f14e(0x10f)](_0x13b8d4=>_0x13b8d4[_0x14f14e(0x12b)]());console['log']('getBasicDescription\x20template'),_0x54f5fa=addDetailsToTemplate(_0x54f5fa,_0x17e257),_0x54f5fa=removeFlaggedKeywords(_0x54f5fa),console[_0x14f14e(0x161)](_0x14f14e(0x125)),_0x54f5fa=removeMPNFromDescription(_0x54f5fa,_0x17e257[_0x14f14e(0x1af)]),console[_0x14f14e(0x161)](_0x14f14e(0x1a3)),_0x54f5fa=removeBrandFromDescription(_0x54f5fa,_0x17e257['brand']),console[_0x14f14e(0x161)](_0x14f14e(0x1c3));try{var _0xb5aae0=btoa(_0x17e257['sku']);_0x54f5fa=_0x54f5fa[_0x14f14e(0x14e)]('{{SKU}}',_0xb5aae0);}catch(_0x586831){console['log'](_0x14f14e(0x103),_0x586831);}return _0x54f5fa;}async function pasteDescriptionFromOpenAi(_0x362c78){var _0x504c27=a0_0x2a34e2,_0x1823f4,_0x192bb3=_0x504c27(0x123),_0x494691=await fetchPrompt(_0x192bb3),_0x3ddcd0=_0x362c78[_0x504c27(0x122)][_0x504c27(0x15d)]('\x0a'),_0x41a4d7=_0x3ddcd0+'\x0a'+_0x362c78[_0x504c27(0x195)],_0x266371=_0x362c78[_0x504c27(0x16b)];_0x494691=_0x494691[_0x504c27(0x14e)](_0x504c27(0x102),_0x266371),_0x494691=_0x494691[_0x504c27(0x14e)](_0x504c27(0xf7),_0x41a4d7),documentTitle=_0x504c27(0x1de);var _0x336775=await receiveOpenAiResponseFromBackgroundScript(_0x494691);if(_0x336775[_0x504c27(0x103)]!=null){console[_0x504c27(0x161)](_0x504c27(0x164),_0x336775['error']);var _0x182c09=_0x336775['error'][_0x504c27(0xfc)];if(_0x182c09[_0x504c27(0x126)](_0x504c27(0x18e))){console[_0x504c27(0x161)]('type.includes(\x27server_error\x27)',_0x182c09[_0x504c27(0x126)]('server_error')),await pasteDescriptionFromOpenAi(_0x362c78);return;}return;}else _0x1823f4=_0x336775['choices'][0x0]['text']['trim']()['replace'](/\n/g,'\x20');documentTitle=_0x504c27(0x119),_0x1823f4=removeMPNFromDescription(_0x1823f4,_0x362c78[_0x504c27(0x1af)]),_0x1823f4=removeBrandFromDescription(_0x1823f4,_0x362c78['brand']);var _0x460ae3=chrome[_0x504c27(0x11b)][_0x504c27(0x11a)](_0x504c27(0x1a1)),_0x5094a3=await fetch(_0x460ae3)[_0x504c27(0x10f)](_0x1aae49=>_0x1aae49[_0x504c27(0x12b)]()),_0x30716f=await fetchCallToActionHtml();_0x1823f4=_0x1823f4+_0x30716f+_0x5094a3,_0x1823f4=wrapWithDivAndStyle(_0x1823f4),documentTitle=_0x504c27(0x105);var _0x48cb61=await addHtmlToEbayDescription(_0x1823f4);documentTitle=_0x504c27(0x17b),await triggerDescriptionNetworkRequest(),await waitForDescriptionNetworkResponse();}async function updateEbayDescriptionWithHtml(_0x24601f){var _0x4dd4cb=a0_0x2a34e2;documentTitle='OpenAI:\x20Adding\x20Description\x20to\x20Ebay';var _0xe0b1c5=await addHtmlToEbayDescription(_0x24601f);documentTitle=_0x4dd4cb(0x17b),await triggerDescriptionNetworkRequest(),await waitForDescriptionNetworkResponse();}async function generateOpenAIDescription(_0x3af5d2){var _0x3a3b80=a0_0x2a34e2,_0x402304,_0x21b409=_0x3af5d2[_0x3a3b80(0x122)][_0x3a3b80(0x15d)]('\x0a'),_0x4d0014=_0x21b409+'\x0a'+_0x3af5d2[_0x3a3b80(0x195)],_0x377450=_0x3af5d2['custom_title'],_0x7a957c=_0x3a3b80(0x1ce),_0x307f7c=await postToServer(_0x7a957c,{'request_type':_0x3a3b80(0xf6),'product_description':_0x4d0014,'product_title':_0x377450});_0x402304=_0x307f7c[_0x3a3b80(0x15e)];(_0x402304==null||_0x402304==''||_0x402304==undefined||_0x402304=='\x20')&&(_0x307f7c=await postToServer(_0x7a957c,{'request_type':'generate_description','product_description':_0x4d0014,'product_title':_0x377450}),_0x402304=_0x307f7c['optimizedDescription']);documentTitle=_0x3a3b80(0x119),_0x402304=removeMPNFromDescription(_0x402304,_0x3af5d2[_0x3a3b80(0x1af)]),_0x402304=removeBrandFromDescription(_0x402304,_0x3af5d2[_0x3a3b80(0x1c5)]);var _0x515a7e=chrome[_0x3a3b80(0x11b)][_0x3a3b80(0x11a)]('Ebay_Description_Templates/Shipping_Handling.html'),_0x4eeb69=await fetch(_0x515a7e)['then'](_0x2e8e09=>_0x2e8e09[_0x3a3b80(0x12b)]()),_0x396777=await fetchCallToActionHtml();return _0x402304=_0x402304+_0x396777+_0x4eeb69,_0x402304=wrapWithDivAndStyle(_0x402304),_0x402304;}function addDetailsToTemplate(_0x3bb9e3,_0x28ef3e){var _0x491e4c=a0_0x2a34e2;_0x3bb9e3=_0x3bb9e3['replace'](/\[TITLE\]/,_0x28ef3e[_0x491e4c(0x16b)]);var _0x3bfa5c=_0x28ef3e[_0x491e4c(0x195)];return _0x3bfa5c=_0x3bfa5c[_0x491e4c(0x14e)](/\n/g,_0x491e4c(0x153)),_0x3bb9e3=_0x3bb9e3['replace'](/\[DESCRIPTION\]/,_0x3bfa5c),_0x28ef3e['bullet_points_html']?_0x3bb9e3=_0x3bb9e3[_0x491e4c(0x14e)](/\[BULLETS\]/,_0x28ef3e[_0x491e4c(0x1bb)]):_0x3bb9e3=_0x3bb9e3[_0x491e4c(0x14e)](/\[BULLETS\]/,''),_0x3bb9e3=_0x3bb9e3[_0x491e4c(0x14e)](/<br>\n  <br>\n\n  \n\n  <br>\n  <br>\n\n/g,_0x491e4c(0x10a)),_0x3bb9e3=_0x3bb9e3[_0x491e4c(0x14e)](/\n\n  <br>\n  <br>\n\n  \n\n  <br>\n  <br>\n\n/g,_0x491e4c(0x1d9)),_0x3bb9e3;}function wrapWithDivAndStyle(_0x589043,_0x27303f=a0_0x2a34e2(0x114),_0x5b4083=a0_0x2a34e2(0x12f),_0x3df548=a0_0x2a34e2(0x115)){var _0x2a6bbe=a0_0x2a34e2;return _0x2a6bbe(0x13e)+_0x27303f+_0x2a6bbe(0x16f)+_0x5b4083+_0x2a6bbe(0x111)+_0x3df548+_0x2a6bbe(0x1d6)+_0x589043+_0x2a6bbe(0x1d7);}function addHtmlToEbayDescription(_0x1702ea){return new Promise(async(_0x341871,_0x50c146)=>{var _0x20f076=a0_0x5685,_0x41d65c=document[_0x20f076(0x1a6)](_0x20f076(0x13c));scrollIntoView(_0x41d65c);var _0x58f006=_0x41d65c[_0x20f076(0x109)]||_0x41d65c[_0x20f076(0x143)][_0x20f076(0xfd)],_0x31dd2e=_0x58f006[_0x20f076(0x1bf)][_0x20f076(0x10e)](_0x20f076(0x186));_0x31dd2e?console[_0x20f076(0x161)](_0x20f076(0x1cd)):console[_0x20f076(0x161)](_0x20f076(0x1ae));var _0x32d82b=_0x1702ea,_0x3556d1=document[_0x20f076(0x1a4)]('div');_0x3556d1['innerHTML']=_0x32d82b,_0x31dd2e[_0x20f076(0x1d8)]=_0x1702ea,await waitSomeTime(0x3e8),_0x31dd2e[_0x20f076(0x147)]();var _0x474ad1=new Event(_0x20f076(0xf4),{'bubbles':!![]});_0x31dd2e[_0x20f076(0x150)](_0x474ad1),_0x41d65c['style']['border']=_0x20f076(0x167),_0x341871();});}async function fetchCallToActionHtml(){var _0x5d5f3e=a0_0x2a34e2,_0x50c64c=chrome[_0x5d5f3e(0x11b)]['getURL'](_0x5d5f3e(0x140)),_0x13c6bc=await fetch(_0x50c64c)[_0x5d5f3e(0x10f)](_0x47d3a6=>_0x47d3a6[_0x5d5f3e(0x12b)]());return _0x13c6bc;}function testInputF(){var _0x2574e7=a0_0x2a34e2,_0x50bcb5=document[_0x2574e7(0x1a6)](_0x2574e7(0x13c)),_0xa47254=_0x50bcb5[_0x2574e7(0x109)]||_0x50bcb5[_0x2574e7(0x143)][_0x2574e7(0xfd)],_0x464e61=_0xa47254[_0x2574e7(0x1bf)][_0x2574e7(0x10e)]('.se-rte-editor__rich');monitorEvents(_0x464e61),_0x464e61[_0x2574e7(0x147)](),_0x4d7894=new CustomEvent(_0x2574e7(0xf4)),_0x464e61[_0x2574e7(0x150)](_0x4d7894),_0x4d7894=document[_0x2574e7(0xf1)](_0x2574e7(0x157)),_0x4d7894[_0x2574e7(0x13b)]('change',!![],![]),_0x464e61[_0x2574e7(0x150)](_0x4d7894),_0x464e61[_0x2574e7(0x1d8)]=_0x2574e7(0x10c),_0x464e61[_0x2574e7(0x147)]();var _0x4d7894=new Event(_0x2574e7(0xf4),{'bubbles':!![]});document[_0x2574e7(0x150)](_0x4d7894);var _0x4d7894=new Event(_0x2574e7(0xf4),{'bubbles':!![]});_0x464e61[_0x2574e7(0x150)](_0x4d7894),_0x464e61['focus'](),_0x464e61[_0x2574e7(0xef)](),_0xa47254['body'][_0x2574e7(0x147)](),document[_0x2574e7(0x1bf)][_0x2574e7(0x1b1)](),_0x4d7894=new CustomEvent(_0x2574e7(0x1b1)),document[_0x2574e7(0x1bf)]['dispatchEvent'](_0x4d7894),_0x4d7894=document[_0x2574e7(0xf1)](_0x2574e7(0x157)),_0x4d7894[_0x2574e7(0x13b)](_0x2574e7(0xf4),!![],![]),document[_0x2574e7(0x1bf)]['dispatchEvent'](_0x4d7894);}function waitFor3PostMessages(){var _0x264e44=a0_0x2a34e2,_0x1ca1ba=0x0;window[_0x264e44(0x13a)]('message',function(_0x3e2fc6){var _0x5e7446=_0x264e44;console['log'](_0x5e7446(0x188),_0x3e2fc6),_0x1ca1ba++,console['log']('postMessageCount:\x20',_0x1ca1ba);},![]);}function removeMPNFromDescription(_0x525cec,_0x22e0d3){var _0x5102f9=a0_0x2a34e2;if(!_0x22e0d3)return _0x525cec;if(!Array[_0x5102f9(0xfa)](_0x22e0d3))return _0x525cec;if(_0x22e0d3[_0x5102f9(0x17f)]>0x0)for(i=0x0;i<_0x22e0d3[_0x5102f9(0x17f)];i++){var _0x340395=_0x22e0d3[i][_0x5102f9(0xfb)]();_0x525cec=insensitiveReplaceAll(_0x525cec,_0x340395,'');}return _0x525cec;}function removeBrandFromDescription(_0x45d410,_0x110f80){var _0xafdddf=a0_0x2a34e2;if(!_0x110f80)return _0x45d410;_0x45d410=insensitiveReplaceAll(_0x45d410,_0x110f80,'');var _0x1154d8=_0x110f80[_0xafdddf(0x14e)](/\s+/g,'-');return _0x45d410=insensitiveReplaceAll(_0x45d410,_0x1154d8,''),_0x45d410;}function removeFlaggedKeywords(_0x443478){var _0x281c71=a0_0x2a34e2;return _0x443478=_0x443478['replace'](/http\:/gi,_0x281c71(0x1be)),_0x443478=_0x443478[_0x281c71(0x14e)](/<a(.*)href=("|')([^'"]+)("|')(.*)>/gi,_0x281c71(0x18f))['replace'](/<img(.*)src=("|')([^'"]+)("|')(.*)>/gi,_0x281c71(0x11c)),_0x443478=_0x443478['replace'](/<script.*?>[\s\S]*?<\/script>/ig,''),_0x443478=insensitiveReplaceAll(_0x443478,'full\x20refund',_0x281c71(0x18b)),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x166),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x18a),''),_0x443478=insensitiveReplaceAll(_0x443478,'Read\x20more',''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x124),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x155),''),_0x443478=insensitiveReplaceAll(_0x443478,'warranty',_0x281c71(0x1a0)),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x1aa),'satisfaction'),_0x443478=insensitiveReplaceAll(_0x443478,'refund',_0x281c71(0x1a0)),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x196),''),_0x443478=insensitiveReplaceAll(_0x443478,'Email',''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x129),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x162),''),_0x443478=insensitiveReplaceAll(_0x443478,'GUARANTEE',_0x281c71(0x1b2)),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x1ca),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x12c),''),_0x443478=insensitiveReplaceAll(_0x443478,'contact\x20us','contact\x20me'),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x152),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x149),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x121),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x197),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x17c),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x151),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0xf5),'satisfaction'),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x151),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x1c1),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x1b9),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0xfe),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x169),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x1c6),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x117),''),_0x443478=insensitiveReplaceAll(_0x443478,_0x281c71(0x190),''),_0x443478=_0x443478['replace'](/[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,''),_0x443478=_0x443478[_0x281c71(0x14e)](/([^.@\s]+)(\.[^.@\s]+)*@([^.@\s]+\.)+([^.@\s]+)/gim,''),_0x443478=_0x443478[_0x281c71(0x14e)](/([^.@\s]+)(\.[^.@\s]+)*@([^.@\s]+\.)+([^.@\s]+)/g,''),_0x443478=_0x443478[_0x281c71(0x14e)](/([^.@\s]+)(\.[^.@\s]+)*@([^.@\s]+\.)+([^.@\s]+)/gi,''),_0x443478=_0x443478['replace'](/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/g,''),_0x443478=_0x443478[_0x281c71(0x14e)]('\x5cb((?:[a-z][\x5cw-]+:(?:\x5c/{1,3}|[a-z0-9%])|www\x5cd{0,3}[.]|[a-z0-9.\x5c-]+[.][a-z]{2,4}\x5c/)(?:[^\x5cs()<>]+|\x5c(([^\x5cs()<>]+|(\x5c([^\x5cs()<>]+\x5c)))*\x5c))+(?:\x5c(([^\x5cs()<>]+|(\x5c([^\x5cs()<>]+\x5c)))*\x5c)|[^\x5cs`!()\x5c[\x5c]{};:\x27\x22.,<>?«»“”‘’]))',''),_0x443478=insensitiveReplaceAll(_0x443478,'brand',_0x281c71(0x179)),_0x443478;}function triggerDescriptionNetworkRequest(){return new Promise((_0x39c278,_0x185ae4)=>{var _0x2cb283=a0_0x5685;documentTitle=_0x2cb283(0x1b7);var _0x785c8e='.smry.summary__title\x20input[name=\x27title\x27]',_0x3ea489=document[_0x2cb283(0x10e)](_0x785c8e);_0x3ea489[_0x2cb283(0x147)](),_0x39c278();});}async function getDescriptionHtmlFromOpenAi(_0x1f69e5){var _0x56083d=a0_0x2a34e2,_0x54fae4=null,_0x57ff4b;try{_0x57ff4b=_0x1f69e5[_0x56083d(0x122)][_0x56083d(0x15d)]('\x0a');}catch(_0xfd2a24){}var _0x55ce65=_0x57ff4b+'\x0a'+_0x1f69e5[_0x56083d(0x195)],_0x31be78=_0x1f69e5[_0x56083d(0x16b)],_0x2e1771=0x0,_0x3e0125='https://openai-function-call-djybcnnsgq-uc.a.run.app';while(_0x2e1771<0x63&&(_0x54fae4==null||_0x54fae4==undefined||_0x54fae4==''||_0x54fae4=='undefined')){var _0x3c3731=_0x56083d(0x191),_0x35dbb7=await fetch(chrome['runtime'][_0x56083d(0x11a)](''+_0x3c3731))['then'](_0x61a653=>_0x61a653[_0x56083d(0x15a)]());_0x35dbb7[_0x56083d(0x13d)]=_0x31be78+'\x0a'+_0x55ce65;var _0x24771a;try{_0x24771a=await postToServer(_0x3e0125,_0x35dbb7),console['log']('getDescriptionHtmlFromOpenAi\x20data',_0x24771a);var _0x24771a=JSON['parse'](_0x24771a);_0x54fae4=_0x24771a[_0x56083d(0x1c2)],console[_0x56083d(0x161)]('getDescriptionHtmlFromOpenAi\x20descriptionHtml',_0x54fae4),(_0x54fae4==null||_0x54fae4==undefined||_0x54fae4==''||_0x54fae4=='undefined')&&await new Promise(_0x27976b=>setTimeout(_0x27976b,0x1388));}catch(_0x37d2fe){console[_0x56083d(0x161)](_0x56083d(0x103),_0x37d2fe);}_0x2e1771++,console[_0x56083d(0x161)]('getDescriptionHtmlFromOpenAi\x20counter',_0x2e1771),await new Promise(_0x3d75b0=>setTimeout(_0x3d75b0,0x1388));}(_0x54fae4==null||_0x54fae4==undefined||_0x54fae4==''||_0x54fae4==_0x56083d(0x19a))&&(_0x54fae4='');console[_0x56083d(0x161)]('getDescriptionHtmlFromOpenAi\x20data',_0x24771a),console[_0x56083d(0x161)](_0x56083d(0x1a5),_0x54fae4),documentTitle=_0x56083d(0x119);try{_0x54fae4=removeMPNFromDescription(_0x54fae4,_0x1f69e5[_0x56083d(0x1af)]);}catch(_0x17a507){}try{_0x54fae4=removeBrandFromDescription(_0x54fae4,_0x1f69e5[_0x56083d(0x1c5)]);}catch(_0x4b7a30){}return _0x54fae4;}async function getDescriptionHtmlFromChatGpt(_0x416326){var _0x1c5727=a0_0x2a34e2,_0x35eaea=null,_0x202841;try{_0x202841=_0x416326[_0x1c5727(0x122)]['join']('\x0a');}catch(_0x55c17d){}var _0x4796f9=_0x202841+'\x0a'+_0x416326[_0x1c5727(0x195)],_0x28cc55=_0x416326[_0x1c5727(0x16b)];_0x35eaea=await askChatGptForDescription(_0x28cc55,_0x4796f9),console[_0x1c5727(0x161)]('getDescriptionHtmlFromChatGpt\x20descriptionHtml',_0x35eaea),console['log'](_0x1c5727(0x1a5),_0x35eaea),documentTitle='OpenAI:\x20Description\x20Generated';try{_0x35eaea=removeMPNFromDescription(_0x35eaea,_0x416326[_0x1c5727(0x1af)]);}catch(_0x7a4658){}try{_0x35eaea=removeBrandFromDescription(_0x35eaea,_0x416326[_0x1c5727(0x1c5)]);}catch(_0x36dae2){}return _0x35eaea;}async function createDescriptionFromTemplate2023(_0x4b15d6,_0x20745c){var _0x4a65f5=a0_0x2a34e2;console[_0x4a65f5(0x161)](_0x4a65f5(0x132));var _0x351a0b=_0x4a65f5(0x12e),_0x3fe49c=await fetch(chrome['runtime'][_0x4a65f5(0x11a)](_0x351a0b+_0x4a65f5(0x1a9)))['then'](_0x361dd0=>_0x361dd0[_0x4a65f5(0x12b)]()),_0x1292ab=await fetch(chrome[_0x4a65f5(0x11b)][_0x4a65f5(0x11a)](_0x351a0b+'/Contact_Info/Contact_Info.html'))['then'](_0x5373d8=>_0x5373d8[_0x4a65f5(0x12b)]()),_0xb117e4=await getShippingInfo(),_0x56a666=await fetch(chrome['runtime'][_0x4a65f5(0x11a)](_0x351a0b+'/Payment_Info/Payment_Info.html'))[_0x4a65f5(0x10f)](_0x4cb34a=>_0x4cb34a['text']()),_0x5d83c0=await fetch(chrome['runtime']['getURL'](_0x351a0b+_0x4a65f5(0x1c0)))[_0x4a65f5(0x10f)](_0x1f56e8=>_0x1f56e8[_0x4a65f5(0x12b)]()),_0xb326a3=await fetch(chrome['runtime'][_0x4a65f5(0x11a)](_0x351a0b+_0x4a65f5(0x14b)))[_0x4a65f5(0x10f)](_0x4bcddc=>_0x4bcddc[_0x4a65f5(0x12b)]());_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x199),_0x4b15d6),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0xff),_0x1292ab),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)]('{{Shipping_Info}}',_0xb117e4),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x19d),_0x56a666),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x10d),_0x5d83c0),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x1b0),_0xb326a3);var _0x34e67e=_0x4a65f5(0x141),_0x3dbd0e=_0x4a65f5(0x112),_0x147145=_0x4a65f5(0x163),_0x3617e7=_0x4a65f5(0x15c),_0x17c746=await getUploadedEbayImages();_0x3fe49c=_0x3fe49c['replace'](_0x4a65f5(0x173),_0x3617e7),_0x3fe49c=_0x3fe49c['replace'](_0x4a65f5(0x139),_0x34e67e),_0x3fe49c=_0x3fe49c['replace'](_0x4a65f5(0x1b4),_0x3dbd0e),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x178),_0x147145),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x110),_0x17c746[0x0]),_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)]('{{Product_Title}}',_0x20745c[_0x4a65f5(0x16b)]);const _0x1ba916=_0x4a65f5(0x148);_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x137),_0x1ba916);try{const _0x5a1b70=_0x17c746,_0x1789c5=createCarousel(_0x5a1b70),_0x40036b=_0x1789c5['outerHTML'];_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x12a),_0x40036b);}catch(_0x11c9b8){_0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x12a),'');}var _0x2ee44e=btoa(_0x20745c[_0x4a65f5(0x16d)]);return _0x3fe49c=_0x3fe49c[_0x4a65f5(0x14e)](_0x4a65f5(0x146),_0x2ee44e),_0x3fe49c;}async function createDescriptionFromTemplate2024(_0x1cecb2,_0x7b5995){var _0x285673=a0_0x2a34e2,{domain:_0x545abb}=await chrome[_0x285673(0x1dd)][_0x285673(0x1d1)][_0x285673(0x138)](_0x285673(0x17a)),_0x8c4ce5=_0x285673(0x12e),_0x4970c2=await fetch(chrome[_0x285673(0x11b)][_0x285673(0x11a)](_0x8c4ce5+_0x285673(0x120)))[_0x285673(0x10f)](_0x3264b9=>_0x3264b9[_0x285673(0x12b)]()),_0x5a695c=await fetch(chrome[_0x285673(0x11b)][_0x285673(0x11a)](_0x8c4ce5+_0x285673(0x14f)+_0x545abb+_0x285673(0xf2)))[_0x285673(0x10f)](_0x319d25=>_0x319d25['text']()),_0x354ab6=await getShippingInfo(),_0x53dbb1=await fetch(chrome[_0x285673(0x11b)][_0x285673(0x11a)](_0x8c4ce5+_0x285673(0x19f)+_0x545abb+'.html'))[_0x285673(0x10f)](_0x3d8eb9=>_0x3d8eb9[_0x285673(0x12b)]()),_0x566fad=await fetch(chrome[_0x285673(0x11b)][_0x285673(0x11a)](_0x8c4ce5+_0x285673(0x100)+_0x545abb+_0x285673(0xf2)))[_0x285673(0x10f)](_0x3e3632=>_0x3e3632[_0x285673(0x12b)]()),_0x27152f=await fetch(chrome[_0x285673(0x11b)][_0x285673(0x11a)](_0x8c4ce5+_0x285673(0x174)+_0x545abb+_0x285673(0xf2)))[_0x285673(0x10f)](_0x9ae799=>_0x9ae799['text']());_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x199),_0x1cecb2),_0x4970c2=_0x4970c2[_0x285673(0x14e)]('{{Contact_Info}}',_0x5a695c),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x14c),_0x354ab6),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x19d),_0x53dbb1),_0x4970c2=_0x4970c2['replace'](_0x285673(0x10d),_0x566fad),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x1b0),_0x27152f);var _0x161273=_0x285673(0x141),_0x33a81e=_0x285673(0x112),_0x2379ba=_0x285673(0x163),_0x359ec3=_0x285673(0x15c),_0x23be2a=await getUploadedEbayImages();_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x173),_0x359ec3),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x139),_0x161273),_0x4970c2=_0x4970c2[_0x285673(0x14e)]('{{Return_Image_URL}}',_0x33a81e),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x178),_0x2379ba),_0x4970c2=_0x4970c2['replace'](_0x285673(0x110),_0x23be2a[0x0]),_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x1c7),_0x7b5995[_0x285673(0x16b)]);var _0xc5cd66=_0x285673(0x135);if(_0x545abb=='de')_0xc5cd66=_0x285673(0x171);else{if(_0x545abb=='fr')_0xc5cd66=_0x285673(0x127);else{if(_0x545abb=='es')_0xc5cd66='¡Cómpralo\x20ahora\x20y\x20disfruta\x20de\x20una\x20agradable\x20experiencia\x20de\x20compra!';else _0x545abb=='it'&&(_0xc5cd66=_0x285673(0x194));}}var {disableThankYouMessage:_0x433224}=await chrome[_0x285673(0x1dd)][_0x285673(0x1d1)][_0x285673(0x138)]('disableThankYouMessage');!_0x433224?_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x137),_0xc5cd66):_0x4970c2=_0x4970c2[_0x285673(0x14e)]('{{Store_Thank_You_Message}}','');var _0x385e44='Shipping';if(_0x545abb=='de')_0x385e44=_0x285673(0x1cb);else{if(_0x545abb=='fr')_0x385e44=_0x285673(0x1ab);else{if(_0x545abb=='es')_0x385e44=_0x285673(0xf0);else _0x545abb=='it'&&(_0x385e44=_0x285673(0x18c));}}_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x10b),_0x385e44);var _0x1e0650=_0x285673(0x187);if(_0x545abb=='de')_0x1e0650='Rücknahmegarantie';else{if(_0x545abb=='fr')_0x1e0650=_0x285673(0x131);else{if(_0x545abb=='es')_0x1e0650=_0x285673(0x17d);else _0x545abb=='it'&&(_0x1e0650=_0x285673(0x1bc));}}_0x4970c2=_0x4970c2[_0x285673(0x14e)]('{{Return_Header}}',_0x1e0650);var _0x9078b1=_0x285673(0x15f);if(_0x545abb=='de')_0x9078b1=_0x285673(0x11d);else{if(_0x545abb=='fr')_0x9078b1=_0x285673(0x193);else{if(_0x545abb=='es')_0x9078b1=_0x285673(0x1d0);else _0x545abb=='it'&&(payment=_0x285673(0x15b));}}_0x4970c2=_0x4970c2['replace'](_0x285673(0x1cc),_0x9078b1);var _0x3f9c68='Feedback';if(_0x545abb=='de')_0x3f9c68=_0x285673(0x144);else{if(_0x545abb=='fr')_0x3f9c68=_0x285673(0x1ba);else{if(_0x545abb=='es')_0x3f9c68='Realimentación';else _0x545abb=='it'&&(_0x3f9c68=_0x285673(0x170));}}_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x1dc),_0x3f9c68);var _0x1665b1=_0x285673(0x189);if(_0x545abb=='de')_0x1665b1=_0x285673(0x11e);else{if(_0x545abb=='fr')_0x1665b1=_0x285673(0x176);else{if(_0x545abb=='es')_0x1665b1=_0x285673(0x101);else _0x545abb=='it'&&(_0x1665b1=_0x285673(0x11f));}}_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x134),_0x1665b1);var {removeSectionsFromDescriptionTemplate:_0x2b4430}=await chrome[_0x285673(0x1dd)]['local'][_0x285673(0x138)](_0x285673(0x181));if(_0x2b4430){var _0x5c06a5=new DOMParser(),_0x28c035=_0x5c06a5[_0x285673(0x1a7)](_0x4970c2,_0x285673(0x1d3)),_0x493994=_0x28c035[_0x285673(0x16c)](_0x285673(0x1cf));for(var _0xc04b7b=0x0;_0xc04b7b<_0x493994[_0x285673(0x17f)];_0xc04b7b++){_0x493994[_0xc04b7b][_0x285673(0x154)]();}var _0x557143=_0x28c035[_0x285673(0x16c)](_0x285673(0x108));for(var _0xc04b7b=0x0;_0xc04b7b<_0x557143[_0x285673(0x17f)];_0xc04b7b++){_0x557143[_0xc04b7b][_0x285673(0x154)]();}_0x4970c2=_0x285673(0x1b3)+_0x28c035[_0x285673(0x1ac)][_0x285673(0x159)];var _0x273b11=document[_0x285673(0x1a4)](_0x285673(0x19b));_0x273b11['className']=_0x285673(0x1c9),_0x273b11[_0x285673(0x106)](_0x285673(0x12d),_0x285673(0x146)),_0x273b11[_0x285673(0x183)][_0x285673(0x1ad)]='none',_0x4970c2=_0x4970c2+_0x273b11[_0x285673(0x159)];}try{const _0x3f389a=_0x23be2a,_0x136ebf=createCarousel(_0x3f389a),_0x292660=_0x136ebf[_0x285673(0x159)];_0x4970c2=_0x4970c2['replace'](_0x285673(0x12a),_0x292660);}catch(_0xbc9f6f){_0x4970c2=_0x4970c2[_0x285673(0x14e)](_0x285673(0x12a),'');}var _0x1ec6ff=btoa(_0x7b5995[_0x285673(0x16d)]);return _0x4970c2=_0x4970c2['replace']('{{SKU}}',_0x1ec6ff),_0x4970c2;}function a0_0x5685(_0x3de135,_0x427833){var _0x2c445e=a0_0x2c44();return a0_0x5685=function(_0x568587,_0x11ba0c){_0x568587=_0x568587-0xef;var _0x35015b=_0x2c445e[_0x568587];return _0x35015b;},a0_0x5685(_0x3de135,_0x427833);}async function replaceCarouselImages(_0x36af74){var _0x5bdc1b=a0_0x2a34e2,_0x4e5ac6=await getUploadedEbayImages(),_0x460d43=new DOMParser(),_0x379610=_0x460d43[_0x5bdc1b(0x1a7)](_0x36af74,_0x5bdc1b(0x1d3));console['log'](_0x5bdc1b(0x133),_0x379610);var _0x5bb99e=_0x379610[_0x5bdc1b(0x10e)](_0x5bdc1b(0x113)),_0x13ea7b;_0x5bb99e&&(_0x13ea7b=_0x5bb99e['parentElement'],_0x5bb99e[_0x5bdc1b(0x154)]());if(_0x4e5ac6[_0x5bdc1b(0x17f)]>0x0&&_0x13ea7b){var _0xf03066=createCarousel(_0x4e5ac6);_0x13ea7b[_0x5bdc1b(0x19c)](_0xf03066);}return _0x379610[_0x5bdc1b(0x1bf)]['innerHTML'];}async function getShippingInfo(){var _0x159e2a=a0_0x2a34e2,_0x14b614=_0x159e2a(0x12e),{domain:_0xb1b060}=await chrome[_0x159e2a(0x1dd)][_0x159e2a(0x1d1)][_0x159e2a(0x138)](_0x159e2a(0x17a)),_0x59d34c;try{_0x59d34c=await fetch(chrome['runtime'][_0x159e2a(0x11a)](_0x14b614+_0x159e2a(0x1b6)+_0xb1b060+_0x159e2a(0xf2)))[_0x159e2a(0x10f)](_0x2f78c5=>_0x2f78c5[_0x159e2a(0x12b)]());}catch(_0x1d20c7){_0x59d34c=await fetch(chrome[_0x159e2a(0x11b)][_0x159e2a(0x11a)](_0x14b614+_0x159e2a(0x165)))[_0x159e2a(0x10f)](_0x630bb7=>_0x630bb7['text']());}return _0x59d34c;}async function getUploadedEbayImages(){var _0x12d7f9=a0_0x2a34e2,_0x15803e=document['querySelectorAll'](_0x12d7f9(0x1df));!_0x15803e[_0x12d7f9(0x17f)]&&(_0x15803e=document['querySelectorAll'](_0x12d7f9(0xf9)));var _0x4e503d=[];for(let _0x37eba6=0x0;_0x37eba6<_0x15803e[_0x12d7f9(0x17f)];_0x37eba6++){console[_0x12d7f9(0x161)](_0x12d7f9(0x1d2)+_0x37eba6),console[_0x12d7f9(0x161)]('Image\x20Thumbnail\x20Button',_0x15803e[_0x37eba6]);var _0x2b8c8e=_0x15803e[_0x37eba6][_0x12d7f9(0x172)](_0x12d7f9(0x183));if(!_0x2b8c8e)continue;var _0x548f0e;_0x2b8c8e[_0x12d7f9(0x126)]('url(\x27')&&(_0x548f0e=_0x2b8c8e[_0x12d7f9(0x14d)](_0x12d7f9(0x18d))[0x1]['split']('\x27)')[0x0]);console['log'](_0x12d7f9(0x145),_0x548f0e);const _0x37a23e=/(?=z\/).*(?<=\/)/,_0x55e0cd=_0x548f0e[_0x12d7f9(0x16a)](_0x37a23e);try{var _0x5952c0=_0x55e0cd[0x0]['substring'](0x1,_0x55e0cd[0x0][_0x12d7f9(0x17f)]-0x1);_0x5952c0=_0x5952c0[_0x12d7f9(0x14e)]('z/','');const _0x3cc1ec=_0x12d7f9(0x1da)+_0x5952c0+_0x12d7f9(0x13f);_0x4e503d[_0x12d7f9(0x180)](_0x3cc1ec);}catch(_0x3e8c0b){console[_0x12d7f9(0x161)]('Error:\x20'+_0x3e8c0b);}}return _0x4e503d;}function createCarousel(_0x1357b3){var _0x1defaa=a0_0x2a34e2;const _0x545052=document[_0x1defaa(0x1a4)](_0x1defaa(0x19b));_0x545052[_0x1defaa(0xf3)]=_0x1defaa(0x1a8);const _0x1c3f6e=document[_0x1defaa(0x1a4)]('ul');return _0x1c3f6e['className']=_0x1defaa(0x19e),_0x1357b3['forEach']((_0x33c8a9,_0x448d40)=>{var _0x1ef9ea=_0x1defaa;const _0x337d34=_0x1357b3[_0x1ef9ea(0x17f)],_0x36c260='img-'+(_0x448d40+0x1),_0x46ab9e=document[_0x1ef9ea(0x1a4)](_0x1ef9ea(0x118));_0x46ab9e[_0x1ef9ea(0xfc)]=_0x1ef9ea(0x1b5),_0x46ab9e['name']=_0x1ef9ea(0x104),_0x46ab9e['id']=_0x36c260;_0x448d40===0x0&&_0x46ab9e[_0x1ef9ea(0x106)](_0x1ef9ea(0x177),_0x1ef9ea(0x177));_0x1c3f6e[_0x1ef9ea(0x19c)](_0x46ab9e);const _0x126ae1=(_0x448d40-0x1+_0x337d34)%_0x337d34+0x1,_0x8877eb=(_0x448d40+0x1)%_0x337d34+0x1,_0x4c5379=document[_0x1ef9ea(0x1a4)]('li');_0x4c5379[_0x1ef9ea(0xf3)]='slide-container',_0x4c5379['innerHTML']='\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22slide-image\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<img\x20src=\x22'+_0x33c8a9+_0x1ef9ea(0x1db)+(_0x448d40+0x1)+'\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22carousel-controls\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22img-'+_0x126ae1+_0x1ef9ea(0x136)+_0x8877eb+_0x1ef9ea(0x184),_0x1c3f6e[_0x1ef9ea(0x19c)](_0x4c5379);}),_0x545052[_0x1defaa(0x19c)](_0x1c3f6e),_0x545052;}async function getGSPRDescription(){var _0x2800a2=null;}async function getDescriptionHtmlFromOpenAiWithCustomPrompt(_0x4eab23){var _0x334912=a0_0x2a34e2;const {descriptionPrompt:_0x493840}=await chrome['storage'][_0x334912(0x1d1)][_0x334912(0x138)]([_0x334912(0x1d5)]),_0x99d3a1=_0x493840||_0x334912(0x1c8),{email:_0x20fe07}=await chrome[_0x334912(0x1dd)]['local'][_0x334912(0x138)](['email']),_0x5cc621={'system_message':_0x99d3a1,'user_message':_0x4eab23,'openai_model':'gpt-4.1-nano','model_definition':{'description_html':_0x334912(0x185)},'email':_0x20fe07};try{const _0x1d6d0b=await postToServer(_0x334912(0x182),_0x5cc621);return _0x1d6d0b['success']?_0x1d6d0b[_0x334912(0x1bd)][_0x334912(0x130)]:_0x334912(0x156)+_0x1d6d0b['errorMessage'];}catch(_0x4f3f89){return'Error:\x20'+_0x4f3f89[_0x334912(0x107)];}}async function generateCustomDescriptionFromTemplate(_0x105120,_0x39cf12,_0x3ed119){var _0x20a26e=a0_0x2a34e2;const {custom_description_template:_0x58ed62}=await chrome[_0x20a26e(0x1dd)][_0x20a26e(0x1d1)]['get'](['custom_description_template']);var _0x4c1fe0=_0x58ed62;if(!_0x4c1fe0){alert(_0x20a26e(0x168));throw new Error('Custom\x20description\x20template\x20not\x20set.');}return _0x4c1fe0=_0x4c1fe0[_0x20a26e(0x142)](_0x20a26e(0x1c7),_0x105120||''),_0x4c1fe0=_0x4c1fe0[_0x20a26e(0x142)](_0x20a26e(0x12a),_0x39cf12||''),_0x4c1fe0=_0x4c1fe0[_0x20a26e(0x142)](_0x20a26e(0x199),_0x3ed119||''),_0x4c1fe0;}