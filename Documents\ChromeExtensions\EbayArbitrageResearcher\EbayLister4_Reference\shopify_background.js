(function(_0x1880df,_0x18f882){const _0x50c28f=a0_0x2395,_0x17fbe9=_0x1880df();while(!![]){try{const _0xa590dc=-parseInt(_0x50c28f(0x1a6))/0x1*(-parseInt(_0x50c28f(0x1a9))/0x2)+-parseInt(_0x50c28f(0x19a))/0x3+parseInt(_0x50c28f(0x1a5))/0x4*(parseInt(_0x50c28f(0x190))/0x5)+parseInt(_0x50c28f(0x180))/0x6+-parseInt(_0x50c28f(0x1a4))/0x7*(-parseInt(_0x50c28f(0x17c))/0x8)+parseInt(_0x50c28f(0x187))/0x9*(parseInt(_0x50c28f(0x19e))/0xa)+-parseInt(_0x50c28f(0x1a7))/0xb;if(_0xa590dc===_0x18f882)break;else _0x17fbe9['push'](_0x17fbe9['shift']());}catch(_0x474695){_0x17fbe9['push'](_0x17fbe9['shift']());}}}(a0_0x4771,0x2a340));function a0_0x2395(_0x1db063,_0x4f0de2){const _0x477109=a0_0x4771();return a0_0x2395=function(_0x2395c5,_0x598faa){_0x2395c5=_0x2395c5-0x17c;let _0x41b4ef=_0x477109[_0x2395c5];return _0x41b4ef;},a0_0x2395(_0x1db063,_0x4f0de2);}async function listToShopify(_0x19c2fa){const _0x1feaf3=a0_0x2395;var {shopify_store_url:_0x21fbbb}=await chrome['storage']['local']['get'](_0x1feaf3(0x1a0)),{shopify_admin_access_token:_0x52ec2f}=await chrome['storage']['local']['get'](_0x1feaf3(0x18c));if(!_0x21fbbb[_0x1feaf3(0x185)]('.com'))return await chrome[_0x1feaf3(0x198)][_0x1feaf3(0x19c)]['remove']('shopify_store_url'),await chrome[_0x1feaf3(0x198)][_0x1feaf3(0x19c)]['remove'](_0x1feaf3(0x18c)),_0x1feaf3(0x188);const _0x3c3d62='https://'+_0x21fbbb+_0x1feaf3(0x184);var _0x209aab=[],_0x5002fb=_0x19c2fa[_0x1feaf3(0x1ad)],_0x15b5ef=_0x19c2fa['main_sd_images'];_0x209aab=_0x5002fb;_0x5002fb[_0x1feaf3(0x197)]<0x2&&_0x15b5ef[_0x1feaf3(0x197)]>0x2?_0x209aab=_0x15b5ef:_0x209aab=_0x5002fb;_0x209aab['unshift'](_0x19c2fa[_0x1feaf3(0x1aa)]);const _0x3501d0={'product':{'title':_0x19c2fa[_0x1feaf3(0x19d)],'body_html':_0x19c2fa[_0x1feaf3(0x17f)],'vendor':_0x19c2fa[_0x1feaf3(0x192)]||_0x1feaf3(0x186),'product_type':_0x19c2fa[_0x1feaf3(0x196)]||_0x1feaf3(0x191),'tags':_0x19c2fa[_0x1feaf3(0x199)]||[],'variants':_0x19c2fa[_0x1feaf3(0x1ac)]||[{'price':_0x19c2fa[_0x1feaf3(0x1ab)],'sku':_0x19c2fa[_0x1feaf3(0x183)]||'','inventory_quantity':_0x19c2fa[_0x1feaf3(0x18e)]||0x0}],'images':[{'src':_0x1feaf3(0x17e)}]}};console['log'](_0x1feaf3(0x1a8),_0x3c3d62),console[_0x1feaf3(0x18d)](_0x1feaf3(0x19b),_0x3501d0),console[_0x1feaf3(0x18d)](_0x1feaf3(0x18a),_0x52ec2f);try{const _0x5163e0=await fetch(_0x3c3d62,{'method':_0x1feaf3(0x1a3),'headers':{'Content-Type':'application/json','X-Shopify-Access-Token':_0x52ec2f},'body':JSON['stringify'](_0x3501d0)});if(!_0x5163e0['ok'])return console[_0x1feaf3(0x181)](_0x1feaf3(0x18b),_0x5163e0[_0x1feaf3(0x19f)],await _0x5163e0[_0x1feaf3(0x1ae)]()),alert('Error:\x20'+_0x5163e0[_0x1feaf3(0x19f)]+_0x1feaf3(0x189)+_0x5163e0[_0x1feaf3(0x194)]),{'error':_0x5163e0[_0x1feaf3(0x194)]};const _0x2896f2=await _0x5163e0['json']();return console[_0x1feaf3(0x18d)](_0x1feaf3(0x17d),_0x2896f2),{'success':!![],'product':_0x2896f2[_0x1feaf3(0x193)]};}catch(_0x743c41){return console['error']('Network\x20error:',_0x743c41),{'error':_0x743c41[_0x1feaf3(0x1a2)]};}}async function uploadFileToShopify(_0x5d4996,_0x37adc6,_0x39c469){const _0x229af7=a0_0x2395,_0x3202d0='https://'+_0x37adc6+'/admin/api/2024-04/files.json',_0x449209=new FormData();_0x449209['append'](_0x229af7(0x195),_0x5d4996);try{const _0x246817=await fetch(_0x3202d0,{'method':'POST','headers':{'X-Shopify-Access-Token':_0x39c469},'body':_0x449209});if(!_0x246817['ok']){const _0x16f11=await _0x246817[_0x229af7(0x1ae)]();return console[_0x229af7(0x181)]('File\x20upload\x20failed:',_0x16f11),null;}const _0x2d1f87=await _0x246817[_0x229af7(0x182)]();return _0x2d1f87[_0x229af7(0x195)][_0x229af7(0x18f)];}catch(_0x39c1bd){return console['error'](_0x229af7(0x1a1),_0x39c1bd),null;}}function a0_0x4771(){const _0x215392=['public_url','248520hltjOx','Default\x20Type','vendor','product','statusText','file','product_type','length','storage','tags','520296PqCMPp','shopifyProductData:','local','custom_title','215310YZhsos','status','shopify_store_url','Error\x20uploading\x20file\x20to\x20Shopify:','message','POST','133khJWCg','8dxJwKF','5ISLiTV','4095322iXlwTO','endpoint:','8972sJvNdc','selected_image','custom_price','variants','main_hd_images','text','61088VIFZTd','Product\x20uploaded\x20successfully:','https://m.media-amazon.com/images/I/610YtqyWhhL._AC_SX569_.jpg','descriptionAndFeatures','1030638goxMdN','error','json','sku','/admin/api/2024-04/products.json','endsWith','Your\x20Vendor\x20Name','117HbjTjl','Invalid\x20Shopify\x20Store\x20URL,\x20removed\x20and\x20try\x20again','\x20-\x20','X-Shopify-Access-Token:','Error\x20response:','shopify_admin_access_token','log','inventory_quantity'];a0_0x4771=function(){return _0x215392;};return a0_0x4771();}