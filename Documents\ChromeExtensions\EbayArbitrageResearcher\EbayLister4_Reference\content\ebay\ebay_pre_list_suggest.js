function a0_0xef07(){var _0x3c0e49=['bullet_points','filteredItemSpecifics','1468256prrPKt','addListener','local','3813960oinCUh','Title\x20was\x20not\x20inputted,\x20trying\x20again','get','5526TFQyFc','join','suggestedQuery','2608220UKzeGX','clicked_go_list_an_item','tab','com','946035alQJfS','title','17303649uMcPsm','12olJhFk','url','59832jIWOOX','descriptionText','runtime','Sending\x20message\x20to\x20background.js\x20to\x20tell\x20it\x20we\x20click\x20go\x20list\x20an\x20item','eBay\x20suggest\x20title\x20-\x20main\x20begins','product','main\x20begins','storage','listingType','9mXXPzb','onMessage','log','com.au','free','label','value','sendMessage','trim','amazon','eBay\x20suggest\x20title','14AIcOHr','4837CgxaTG','domain','3dIMkCj','length','Sent\x20message\x20to\x20background.js\x20to\x20tell\x20it\x20we\x20click\x20go\x20list\x20an\x20item','eBay\x20suggest\x20title\x20-\x20main\x20ends\x20-\x20title\x20was\x20not\x20inputted\x20#2','type','eBay\x20suggest\x20title\x20-\x20main\x20ends\x20-\x20title\x20was\x20not\x20inputted','insert_draft_details'];a0_0xef07=function(){return _0x3c0e49;};return a0_0xef07();}var a0_0x27a0d8=a0_0x46ca;(function(_0x570e79,_0x5db8a9){var _0xf40908=a0_0x46ca,_0x5bb177=_0x570e79();while(!![]){try{var _0x15d421=-parseInt(_0xf40908(0x105))/0x1*(parseInt(_0xf40908(0x123))/0x2)+parseInt(_0xf40908(0x108))/0x3*(parseInt(_0xf40908(0x11a))/0x4)+-parseInt(_0xf40908(0x11e))/0x5+parseInt(_0xf40908(0x117))/0x6*(-parseInt(_0xf40908(0x106))/0x7)+-parseInt(_0xf40908(0x111))/0x8+parseInt(_0xf40908(0xfa))/0x9*(-parseInt(_0xf40908(0x114))/0xa)+-parseInt(_0xf40908(0x120))/0xb*(-parseInt(_0xf40908(0x121))/0xc);if(_0x15d421===_0x5db8a9)break;else _0x5bb177['push'](_0x5bb177['shift']());}catch(_0x410825){_0x5bb177['push'](_0x5bb177['shift']());}}}(a0_0xef07,0x65800),console['log']('Ebay\x20Pre\x20List\x20Suggest\x20initialized'),onPageLoadAndStableNotifyBackground(),chrome[a0_0x27a0d8(0x125)][a0_0x27a0d8(0xfb)][a0_0x27a0d8(0x112)](async(_0x22ee24,_0x400815,_0x459177)=>{var _0x1f7e2b=a0_0x27a0d8;console[_0x1f7e2b(0xfc)](_0x400815[_0x1f7e2b(0x11c)]?'From\x20a\x20content\x20script:'+_0x400815[_0x1f7e2b(0x11c)][_0x1f7e2b(0x122)]:'From\x20the\x20extension\x20request.type\x20ebay.js'+_0x22ee24[_0x1f7e2b(0x10c)]),_0x22ee24[_0x1f7e2b(0x10c)]===_0x1f7e2b(0x10e)&&(console[_0x1f7e2b(0xfc)]('insert_draft_details\x20begins'),document['title']=_0x1f7e2b(0x104),main(_0x22ee24['productData']));}));async function main(_0x1101f8){var _0x261bcf=a0_0x27a0d8;console[_0x261bcf(0xfc)](_0x261bcf(0xf7)),console[_0x261bcf(0xfc)](_0x261bcf(0xf6),_0x1101f8),document[_0x261bcf(0x11f)]=_0x261bcf(0x127);if(_0x1101f8[_0x261bcf(0xf9)]==_0x261bcf(0xfe))tellEbayWhatYourSelling(_0x1101f8['title']);else{if(_0x1101f8[_0x261bcf(0xf9)]=='chatGpt')try{var _0x13bc8c=await askChatGptForProductType(_0x1101f8['title']);_0x1101f8['productType']=_0x13bc8c,tellEbayWhatYourSelling(_0x13bc8c);}catch(_0x4db91e){tellEbayWhatYourSelling(_0x1101f8[_0x261bcf(0x11f)]);}else{document[_0x261bcf(0x11f)]='eBay\x20suggest\x20title\x20-\x20main\x20begins\x20-\x20tellEbayWhatYourSelling';var {domain:_0xb2c36e}=await chrome[_0x261bcf(0xf8)][_0x261bcf(0x113)][_0x261bcf(0x116)](_0x261bcf(0x107)),_0x185125=null;(_0xb2c36e==_0x261bcf(0x11d)||_0xb2c36e=='ca'||_0xb2c36e=='co.uk'||_0xb2c36e==_0x261bcf(0xfd))&&(_0x185125=await getBuyerSearchQueryFromTitle(_0x1101f8[_0x261bcf(0x11f)])),!_0x185125?_0x185125=_0x1101f8[_0x261bcf(0x11f)]:_0x1101f8[_0x261bcf(0x119)]=_0x185125,tellEbayWhatYourSelling(_0x185125);}}document[_0x261bcf(0x11f)]='eBay\x20suggest\x20title\x20-\x20main\x20ends',await wait(0x7d0);var _0x577e00=await checkIfTitleWasInputted();!_0x577e00&&(document[_0x261bcf(0x11f)]=_0x261bcf(0x10d),console[_0x261bcf(0xfc)](_0x261bcf(0x115)),tellEbayWhatYourSelling(_0x1101f8[_0x261bcf(0x11f)]),await wait(0x7d0),document[_0x261bcf(0x11f)]=_0x261bcf(0x10b)),console['log'](_0x261bcf(0x126)),chrome[_0x261bcf(0x125)][_0x261bcf(0x101)]({'type':_0x261bcf(0x11b),'productData':_0x1101f8},function(){var _0xbb1917=_0x261bcf;console[_0xbb1917(0xfc)](_0xbb1917(0x10a)),clickGoToListAnItem();});}function a0_0x46ca(_0x282eb8,_0x21e837){var _0xef0757=a0_0xef07();return a0_0x46ca=function(_0x46ca30,_0x5854a5){_0x46ca30=_0x46ca30-0xf6;var _0x24c488=_0xef0757[_0x46ca30];return _0x24c488;},a0_0x46ca(_0x282eb8,_0x21e837);}function combineProductDataAttributes(_0x4a7d78){var _0x445589=a0_0x27a0d8,_0x2292fe=_0x4a7d78[_0x445589(0x10f)][_0x445589(0x118)]('\x0a'),_0x19f50d=_0x4a7d78[_0x445589(0x124)][_0x445589(0x102)](),_0xc856fc=_0x4a7d78['title'],_0x35d9a6='';for(var _0x52d6bf=0x0;_0x52d6bf<_0x4a7d78['filteredItemSpecifics'][_0x445589(0x109)];_0x52d6bf++){var _0x5eb1ea=_0x4a7d78[_0x445589(0x110)][_0x52d6bf],_0x5aab84=_0x5eb1ea[_0x445589(0xff)],_0x2882a8=_0x5eb1ea[_0x445589(0x100)];_0x35d9a6+=_0x5aab84+':\x20'+_0x2882a8+'\x0a';}var _0x2c0bde=_0xc856fc+'\x0a\x0a'+_0x2292fe+'\x0a\x0a'+_0x19f50d+'\x0a\x0a'+_0x35d9a6;return _0x2c0bde;}function getProductFromLocalStorage(){return new Promise((_0x146c1b,_0x319d0e)=>{var _0x7cca7c=a0_0x46ca;chrome[_0x7cca7c(0xf8)]['local']['get'](_0x7cca7c(0x103),function(_0x12d367){var _0x16cace=_0x7cca7c,_0x52aae2=_0x12d367[_0x16cace(0x103)];_0x146c1b(_0x52aae2);});});}function wait(_0x2f5296){return new Promise(_0xfddcfe=>{setTimeout(_0xfddcfe,_0x2f5296);});}