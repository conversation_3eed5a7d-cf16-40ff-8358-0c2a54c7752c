#data-box {
    background: white;
    padding: 20px;
    width: 50%; 
    margin: auto; 
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    position: relative; /* ensure the z-index works */
    z-index: 9999; /* this should put it on top of other elements */

    border: 5px solid black;

}

#myModal{

    display: none;
    position: fixed;
    z-index: 1;
    padding-top: 100px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;

    background-color: rgba(0, 0, 0, 0.9);
    z-index: 99999; /* this should put it on top of other elements */

}

.close-button {
    position: absolute;
    top: 0;
    right: 0;
    padding: 5px;
    cursor: pointer;
    font-size: 40px;
    color: #FF0000;  
}


#data-box h2 {
    font-size: 20px;
    margin-bottom: 10px;
}

#data-box p {
    margin-bottom: 10px;
}

#data-box textarea, 
#data-box input {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

#data-box button {
    background-color: #4CAF50; 
    color: white; 
    padding: 15px 32px; 
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
}

.imageContainer {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.imageContainer img {
    width: 100px;
    height: 100px;

}

.deleteButton {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px;
    background: red;
    color: white;
    cursor: pointer;
}

.highlight {
    border: 2px solid red;
}


#descriptionText {
    border: 1px solid black;
    padding: 5px;
  }




#listTitleContainer {
    border: 2px solid #007BFF; /* Blue border */
    padding: 10px;
    border-radius: 5px; 
    background-color: #f9f9f9; /* Light grey background */
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Slight blue shadow for emphasis */
    margin-bottom: 10px;
    transition: box-shadow 0.3s ease; /* Smooth transition for effects */
}

#listTitleContainer:hover {
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.8); /* Intensified blue shadow on hover */
}

#listTitle {
    width: 100%; /* Takes the full width of its container */
    border: none;
    background-color: transparent; /* So that it doesn't break the design of the container */
    outline: none; /* Remove default outline */
    font-size: 16px; /* Adjust based on your design needs */
    resize: none; /* Disable manual resizing */
}

#listTitle:focus {
    border: none;
    box-shadow: none; /* No shadow when focused to keep it simple */
}

#charCount {
    margin-top: 5px; /* Some spacing */
    font-size: 12px; /* Smaller font for character count */
    color: #555; /* Darker grey text */
}





#listPriceContainer {
    border: 2px solid #28a745; /* Green border for a positive money vibe */
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9; /* Light grey background */
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); /* Slight green shadow for emphasis */
    margin-bottom: 10px;
    display: flex; /* For aligning label and input side by side */
    align-items: center; /* Vertically center align */
    transition: box-shadow 0.3s ease; /* Smooth transition for effects */
}

#listPriceContainer:hover {
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.8); /* Intensified green shadow on hover */
}

#listPriceContainer label {
    margin-right: 10px; /* Space between the label and the input */
    font-weight: bold; /* Make the label bold */
    color: #333; /* Dark grey color for the label */
}

#listPrice {
    border: 1px solid #28a745; /* Green border */
    border-radius: 4px;
    padding: 5px 10px; /* Some padding for comfort */
    font-size: 16px;
    color: #051d0a; /* Green text to emphasize the monetary nature */
    outline: none; /* Remove default outline */
    transition: border 0.3s ease; /* Transition for hover effect on border */
    flex-grow: 1; /* Allow it to take the available space */
}

#listPrice:focus {
    border: 2px solid #28a745; /* Thicker green border on focus */
    padding: 4px 9px; /* Adjust padding to keep input size consistent after increased border width */
}





#listDescriptionContainer {
    border: 2px solid #007BFF; /* Blue border for emphasis */
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9; /* Light grey background */
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Slight blue shadow for emphasis */
    margin-bottom: 10px;
    transition: box-shadow 0.3s ease; /* Smooth transition for effects */
}

#listDescriptionContainer:hover {
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.8); /* Intensified blue shadow on hover */
}

#listDescription {
    width: 100%; /* Takes the full width of its container */
    border: none;
    background-color: transparent; /* So that it doesn't break the design of the container */
    outline: none; /* Remove default outline */
    font-size: 16px; /* Adjust based on your design needs */
    padding: 10px; /* Padding for better readability */
    resize: vertical; /* Allow vertical resizing, but not horizontal to prevent breaking layout */
    transition: border 0.3s ease, box-shadow 0.3s ease; /* Transition for focus effects */
}

#listDescription:focus {
    border: 1px solid #007BFF; /* Blue border on focus */
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Slight blue shadow on focus */
}



.sortable-chosen {
    opacity: 0.6;
}
.sortable-drag {
    border: 1px solid #aaa;
    box-shadow: 0px 0px 10px #aaa;
}


.modal {
    display: none;
    position: fixed;
    z-index: 1;
    padding-top: 100px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
  }
  
  .modal-content {
    display: block;
    width: auto;
    height: auto;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
  }
  
  .close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
  }
  
  .close:hover,
  .close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
  }
  