// Fee structures for profit calculations

// 🛡️ UNIVERSAL GLOBAL SCOPE: Works in both content scripts (window) and service workers (self)
// Use var to prevent "already declared" errors in service worker importScripts
var globalScope = (typeof globalScope !== 'undefined')
  ? globalScope
  : (typeof window !== 'undefined' ? window : self);

// 🛡️ SINGLETON GUARD: Prevent multiple declarations
if (globalScope.FEE_STRUCTURES) {
  console.log('⚠️ FEE_STRUCTURES already loaded, skipping redeclaration');
} else {
  console.log('✅ Loading FEE_STRUCTURES for the first time');

const FEE_STRUCTURES = {
  ebay: {
    // Category-specific final value fees
    categories: {
      'default': 0.125,        // 12.5% most categories
      'motors': 0.10,          // 10% motors
      'business': 0.15,        // 15% business & industrial
      'collectibles': 0.15,    // 15% collectibles
      'books': 0.15,           // 15% books/media
      'clothing': 0.125        // 12.5% clothing
    },
    
    // Payment processing fees
    payment: {
      'managed_payments': 0.029,  // 2.9% eBay managed payments
      'paypal': 0.029,           // 2.9% PayPal
      'international': 0.039      // 3.9% international
    },
    
    // Additional fees
    listing: 0,                   // Free basic listings
    international_fee: 0.015,     // 1.5% international sales
    promoted_listing: 0.02        // 2% if using promoted listings
  },
  
  amazon: {
    tax_rates: {
      'US': 0.08,      // Average US sales tax
      'CA': 0.12,      // Canadian tax
      'UK': 0.20       // UK VAT
    },
    shipping: {
      'prime': 0,      // Free with Prime
      'standard': 5.99,
      'expedited': 12.99
    }
  },
  
  shipping: {
    domestic: {
      'standard': 5.99,
      'expedited': 12.99,
      'overnight': 25.99
    },
    international: {
      'standard': 15.99,
      'expedited': 35.99
    }
  }
};

// Make globally available in universal context
globalScope.FEE_STRUCTURES = FEE_STRUCTURES;

} // End singleton guard
