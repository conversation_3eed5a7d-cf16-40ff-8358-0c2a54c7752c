body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

header {
    padding: 20px;
    background-color: #333;
    color: #fff;
    text-align: center;
}

section.bulk-list {
    width: 90%;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.bulk-list h2,
.bulk-list p {
    margin-bottom: 15px;
}

textarea#amazon-links {
    width: 100%;
    height: 200px;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
    resize: none;
}



button[disabled] {
    background-color: #ccc;
}

.button-group {
    display: flex;
    /* justify-content: space-between; */

}

.position-container {
    display: flex;
    align-items: center;
}

.form-group {
    margin-top: 20px;
}


.container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}







.add-links-container {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    height: 100%;
}

#add-links {
    flex: 1;
    margin-right: 10px;
}

.button-group-column {

    display: flex;
    flex-direction: column;

}



#add-links-btn {
    background-color: #4CAF50;
    /* Green */
    border: none;
    color: white;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin-top: 5px;
    cursor: pointer;
    border-radius: 5px;
}

#import-links-btn {
    background-color: #008CBA;
    /* Blue */
    border: none;
    color: white;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    border-radius: 5px;
}

#add-links-btn:hover {
    background-color: #3e8e41;
    /* Darker green */
}


#import-links-btn:hover {
    background-color: #00738b;
    /* Darker blue */
}

#add-links-btn:active {
    background-color: #3e8e41;
    /* Darker green */
    box-shadow: 0 5px #666;
    transform: translateY(4px);
}



#import-links-btn:active {

    background-color: #00738b;
    /* Darker blue */
    box-shadow: 0 5px #666;
    transform: translateY(4px);
}








.blocked {

    background: rgba(0, 0, 0, 0.5);
    pointer-events: none;
}


.clear {
    display: inline-block;

    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #dc3545;
    color: #fff;
    border: 2px solid #dc3545;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

.clear:hover {
    background-color: #fff;
    color: #dc3545;
}


#reset-btn {
    display: inline-block;

    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #dc3545;
    color: #fff;
    border: 2px solid #dc3545;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

#reset-btn:hover {
    background-color: #fff;
    color: #dc3545;
}

#reset-btn:active {
    background-color: #fff;
    color: #dc3545;
    box-shadow: 0 5px #666;
    transform: translateY(4px);
}

#reset-btn:disabled {
    background-color: #ccc;
    color: #fff;
    border: 2px solid #ccc;
    cursor: not-allowed;
}


/* General button styles */
.list-btn {
    font-family: 'Roboto', sans-serif;
    /* You can use any other font-family you prefer */
    color: #FFFFFF;
    background-color: #2bbe5c;
    border: none;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    /* Add spacing between the buttons */
    transition: background-color 0.3s, transform 0.3s;
}

/* Button hover styles */
.list-btn:hover {
    background-color: #136821;
    transform: scale(1.05);
}

/* Button active styles */
.list-btn:active {
    background-color: #1a993a;
    transform: scale(1);
}

#standard-listing-button {
    color: black;
    background-color: #E0E0E0;
}

#list-btn {
    color: white;
    background-color: #2bbe5c;

    /* display: none; */


}

#chat-listing-button {
    background-color: #99e7ac;
}

#seo-listing-button {

    color: #FFFFFF;
    background-color: rgb(238, 112, 112) !important;


}

#seo-listing-button:hover {
    background-color: rgb(243, 66, 66) !important;
    transform: scale(1.05);
}