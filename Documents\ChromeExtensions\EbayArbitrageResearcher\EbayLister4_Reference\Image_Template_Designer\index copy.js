const a0_0x459481=a0_0x5c42;(function(_0x57c2f1,_0x58b023){const _0x233598=a0_0x5c42,_0x3bfa7b=_0x57c2f1();while(!![]){try{const _0x3bd943=-parseInt(_0x233598(0x97))/0x1+parseInt(_0x233598(0xb3))/0x2*(-parseInt(_0x233598(0xd6))/0x3)+-parseInt(_0x233598(0xae))/0x4+parseInt(_0x233598(0xdf))/0x5*(-parseInt(_0x233598(0x112))/0x6)+parseInt(_0x233598(0xe4))/0x7*(parseInt(_0x233598(0x106))/0x8)+-parseInt(_0x233598(0x9c))/0x9*(-parseInt(_0x233598(0xc3))/0xa)+parseInt(_0x233598(0x10a))/0xb;if(_0x3bd943===_0x58b023)break;else _0x3bfa7b['push'](_0x3bfa7b['shift']());}catch(_0x5ebcc8){_0x3bfa7b['push'](_0x3bfa7b['shift']());}}}(a0_0x4308,0x6d0c0));let rectCount=0x0,copiedObject=null;const canvas=new fabric[(a0_0x459481(0xa5))]('c',{'backgroundColor':a0_0x459481(0x8e)}),testCanvas=new fabric[(a0_0x459481(0xa5))](a0_0x459481(0xc9),{'backgroundColor':a0_0x459481(0x8e)}),testImages=[a0_0x459481(0xf5),'https://m.media-amazon.com/images/I/61ExVdn4flL._AC_SY450_.jpg',a0_0x459481(0xaa),a0_0x459481(0xf6),a0_0x459481(0x95),a0_0x459481(0xe7)];document[a0_0x459481(0xf7)]('DOMContentLoaded',()=>{initializeEventListeners();}),document[a0_0x459481(0xf7)](a0_0x459481(0xc7),async _0x308de1=>{const _0x105349=a0_0x459481;if(document[_0x105349(0xd5)][_0x105349(0x90)]===_0x105349(0x103)||document[_0x105349(0xd5)]['tagName']==='TEXTAREA')return;if(_0x308de1[_0x105349(0xe5)]&&_0x308de1[_0x105349(0xeb)]==='KeyC'){const _0x175fb3=canvas[_0x105349(0x10c)]();_0x175fb3&&(copiedObject=_0x175fb3,navigator[_0x105349(0xff)][_0x105349(0xb0)](''),console[_0x105349(0xc1)](_0x105349(0x10e),copiedObject));}else{if(copiedObject&&_0x308de1[_0x105349(0xe5)]&&_0x308de1['code']===_0x105349(0xf0)){console[_0x105349(0xc1)](_0x105349(0x8d));try{const _0xd5e8bc=await((async()=>{const _0x4c932d=_0x105349,_0x4e0824=await navigator['permissions'][_0x4c932d(0xb4)]({'name':_0x4c932d(0x113)});if(_0x4e0824[_0x4c932d(0x8a)]==_0x4c932d(0xc8)||_0x4e0824['state']==_0x4c932d(0xef))return await navigator[_0x4c932d(0xff)]['read']();else throw new Error(_0x4c932d(0xa0));})()),_0x1c6d0e=_0xd5e8bc[_0x105349(0xe9)](_0x296736=>_0x296736['types'][_0x105349(0xcc)](_0x105349(0x96)));if(_0x1c6d0e)return;}catch(_0x9fa627){console[_0x105349(0x8c)](_0x105349(0x104),_0x9fa627);}let _0x4cb4ec;if(copiedObject['type']===_0x105349(0xa3)&&!isNaN(copiedObject[_0x105349(0xd2)](0x1)[_0x105349(0xf8)])){rectCount++;const _0xc69fb3=new fabric[(_0x105349(0xfc))]({'left':0x0,'top':0x0,'fill':copiedObject[_0x105349(0xd2)](0x0)['fill'],'width':copiedObject[_0x105349(0xd2)](0x0)[_0x105349(0xdc)]*copiedObject[_0x105349(0xbc)],'height':copiedObject['item'](0x0)[_0x105349(0x9f)]*copiedObject[_0x105349(0x122)],'stroke':copiedObject['item'](0x0)['stroke'],'strokeWidth':copiedObject[_0x105349(0xd2)](0x0)[_0x105349(0xac)]}),_0x2fb15d=new fabric[(_0x105349(0x11c))](rectCount['toString'](),{'left':_0xc69fb3['left']+_0xc69fb3[_0x105349(0xdc)]/0x2,'top':_0xc69fb3[_0x105349(0x94)]+_0xc69fb3[_0x105349(0x9f)]/0x2,'fontSize':copiedObject[_0x105349(0xd2)](0x1)[_0x105349(0xa7)],'originX':_0x105349(0xcf),'originY':_0x105349(0xcf)});_0x4cb4ec=new fabric['Group']([_0xc69fb3,_0x2fb15d],{'left':copiedObject[_0x105349(0x8f)]+0xa,'top':copiedObject[_0x105349(0x94)]+0xa}),canvas[_0x105349(0x93)](_0x4cb4ec);}else _0x4cb4ec=new fabric[(_0x105349(0x83))](copiedObject[_0x105349(0xbb)](),{'left':copiedObject[_0x105349(0x8f)]+0xa,'top':copiedObject['top']+0xa,'width':copiedObject[_0x105349(0xdc)],'height':copiedObject['height'],'scaleX':copiedObject[_0x105349(0xbc)],'scaleY':copiedObject[_0x105349(0x122)]}),canvas[_0x105349(0x93)](_0x4cb4ec);canvas[_0x105349(0xf3)]();}}}),document['addEventListener'](a0_0x459481(0x118),_0x3781db=>{const _0x4e9a18=a0_0x459481;_0x3781db[_0x4e9a18(0xe5)]&&_0x3781db[_0x4e9a18(0xeb)]===_0x4e9a18(0xf0)&&navigator[_0x4e9a18(0xff)][_0x4e9a18(0xc2)]()[_0x4e9a18(0xe1)](_0xd064d6=>{const _0x3d1cdb=_0x4e9a18;_0xd064d6[_0x3d1cdb(0xca)]>0x0&&_0xd064d6[0x0][_0x3d1cdb(0xcb)][_0x3d1cdb(0xcc)]('image/png')&&_0x3781db[_0x3d1cdb(0xfa)]();})['catch'](_0x3751d8=>{const _0x33a250=_0x4e9a18;console[_0x33a250(0x8c)](_0x3751d8);});}),document[a0_0x459481(0xf7)]('paste',async _0x3ad25b=>{const _0xf594f8=a0_0x459481;console['log'](_0xf594f8(0xee));if(_0x3ad25b[_0xf594f8(0x11e)]&&_0x3ad25b['clipboardData']['items']){const _0x4d9398=Array[_0xf594f8(0x115)](_0x3ad25b[_0xf594f8(0x11e)][_0xf594f8(0x121)])['find'](_0x35846d=>_0x35846d[_0xf594f8(0x100)][_0xf594f8(0xcc)](_0xf594f8(0x110)));console['log']('item',_0x4d9398);if(_0x4d9398){_0x3ad25b['preventDefault'](),console[_0xf594f8(0xc1)](_0xf594f8(0xfe));const _0x3dada7=_0x4d9398['getAsFile'](),_0x3d6748=new FileReader();_0x3d6748['onload']=function(_0x112139){const _0x59ff17=_0xf594f8,_0x4f18c1=_0x112139['target'][_0x59ff17(0xcd)];fabric['Image'][_0x59ff17(0xb8)](_0x4f18c1,_0xb48d03=>{const _0x31c270=_0x59ff17;_0xb48d03[_0x31c270(0xaf)]({'left':0x32,'top':0x32}),_0xb48d03[_0x31c270(0xc0)](0.5),canvas['add'](_0xb48d03),canvas[_0x31c270(0xf3)]();});},_0x3d6748[_0xf594f8(0x111)](_0x3dada7);}}});function initializeEventListeners(){const _0x4838d1=a0_0x459481;loadTemplate(),document[_0x4838d1(0xe8)](_0x4838d1(0xec))['addEventListener'](_0x4838d1(0xa4),addRectangle),document[_0x4838d1(0xe8)]('uploadBadge')[_0x4838d1(0xf7)](_0x4838d1(0x108),uploadBadge),document[_0x4838d1(0xe8)]('saveTemplate')['addEventListener'](_0x4838d1(0xa4),saveTemplate),document[_0x4838d1(0xe8)](_0x4838d1(0x119))['addEventListener'](_0x4838d1(0xa4),loadTemplate),document[_0x4838d1(0xe8)](_0x4838d1(0xf2))[_0x4838d1(0xf7)](_0x4838d1(0xa4),testTemplate),document['getElementById']('clearCanvas')['addEventListener'](_0x4838d1(0xa4),clearCanvas),document[_0x4838d1(0xe8)](_0x4838d1(0xf4))[_0x4838d1(0xf7)](_0x4838d1(0xa4),backToEditor),document['getElementById']('delete')[_0x4838d1(0xf7)](_0x4838d1(0xa4),deleteObject),document[_0x4838d1(0xe8)](_0x4838d1(0xa9))[_0x4838d1(0xf7)]('click',async()=>{const _0x575d85=_0x4838d1,_0x45fe3f=document[_0x575d85(0xe8)](_0x575d85(0xb5))[_0x575d85(0xc4)];if(_0x45fe3f){const _0x15dd7a=await loadImage(_0x45fe3f),_0x4884ad=await processImage(_0x15dd7a,_0x15dd7a[_0x575d85(0xdc)],_0x15dd7a['height']),_0x5955f9=new fabric[(_0x575d85(0x83))](_0x4884ad,{'left':0x32,'top':0x32});_0x5955f9[_0x575d85(0x105)](0xc8),_0x5955f9['scaleToHeight'](0xc8),canvas[_0x575d85(0x93)](_0x5955f9);}}),document[_0x4838d1(0xe8)](_0x4838d1(0x10f))['addEventListener'](_0x4838d1(0xa4),addBanner),window['addEventListener'](_0x4838d1(0xc7),_0x3c0ca0=>{const _0xd9868a=_0x4838d1;_0x3c0ca0[_0xd9868a(0xe3)]===_0xd9868a(0x11b)&&!(document[_0xd9868a(0xd5)]instanceof HTMLInputElement)&&deleteObject();}),document[_0x4838d1(0xb7)](_0x4838d1(0x114))[_0x4838d1(0x85)](_0x5c058f=>{const _0x8554df=_0x4838d1;_0x5c058f[_0x8554df(0xf7)](_0x8554df(0x9a),()=>{const _0x72ae7b=_0x8554df;canvas[_0x72ae7b(0x9d)]()[_0x72ae7b(0xf3)]();});});}function addRectangle(){const _0x1451b0=a0_0x459481;rectCount++;const _0x367387=new fabric[(_0x1451b0(0xfc))]({'left':0x32,'top':0x32,'fill':_0x1451b0(0xd1),'width':0xc8,'height':0xc8,'stroke':_0x1451b0(0x8b),'strokeWidth':0x5}),_0x314b87=new fabric[(_0x1451b0(0x11c))](rectCount[_0x1451b0(0xf1)](),{'left':_0x367387[_0x1451b0(0x8f)]+_0x367387[_0x1451b0(0xdc)]/0x2,'top':_0x367387[_0x1451b0(0x94)]+_0x367387[_0x1451b0(0x9f)]/0x2,'fontSize':0x14,'originX':_0x1451b0(0xcf),'originY':_0x1451b0(0xcf)}),_0x539741=new fabric[(_0x1451b0(0xb6))]([_0x367387,_0x314b87],{});canvas[_0x1451b0(0x93)](_0x539741);}function a0_0x5c42(_0x4a79a1,_0x251d69){const _0x4308ab=a0_0x4308();return a0_0x5c42=function(_0x5c4241,_0x1c3180){_0x5c4241=_0x5c4241-0x83;let _0x4931bb=_0x4308ab[_0x5c4241];return _0x4931bb;},a0_0x5c42(_0x4a79a1,_0x251d69);}function uploadBadge(_0xbffb0b){const _0x19a2b3=a0_0x459481;Array['from'](_0xbffb0b[_0x19a2b3(0xd7)][_0x19a2b3(0xdd)])['forEach'](_0x2ac9e7=>{const _0x200e07=_0x19a2b3,_0x2576ac=new FileReader();_0x2576ac[_0x200e07(0xce)]=_0x4289d=>{const _0x54d7b8=_0x200e07;fabric['Image']['fromURL'](_0x4289d['target'][_0x54d7b8(0xcd)],_0x4771da=>canvas[_0x54d7b8(0x93)](_0x4771da));},_0x2576ac[_0x200e07(0x111)](_0x2ac9e7);});}async function saveTemplate(){const _0x23524f=a0_0x459481,_0x16ecb3=JSON[_0x23524f(0xd9)](canvas[_0x23524f(0xf9)]([_0x23524f(0x92),_0x23524f(0x109)]));chrome['storage'][_0x23524f(0xe0)][_0x23524f(0xaf)]({'template':_0x16ecb3},async()=>{const _0x1af225=_0x23524f;alert('Template\x20saved!'),chrome[_0x1af225(0x11f)][_0x1af225(0xe0)]['get'](_0x1af225(0x116),_0x435c0e=>{const _0x3b3da1=_0x1af225;console[_0x3b3da1(0xc1)](_0x435c0e);});var {template:_0x232e76}=await chrome[_0x1af225(0x11f)][_0x1af225(0xe0)][_0x1af225(0x10b)](_0x1af225(0x116));console[_0x1af225(0xc1)]('test\x202',_0x232e76);});}function a0_0x4308(){const _0x9e0ff8=['getScaledWidth','focus','bannerText','711nWajqt','discardActiveObject','editor','height','Permission\x20to\x20read\x20clipboard\x20was\x20denied','jpeg','body','group','click','Canvas','hidden','fontSize','URL','fetchBadgeFromUrl','https://m.media-amazon.com/images/I/6149ETXCDHL._AC_SL1000_.jpg','getScaledHeight','strokeWidth','readAsText','818464FyISFC','set','writeText','bannerFont','remove','380252sFSlyZ','query','badgeUrl','Group','querySelectorAll','fromURL','push','test-image.jpeg','getElement','scaleX','getHeight','abs','tester','scale','log','read','65860ZkfFAc','value','href','line','keydown','granted','testCanvas','length','types','includes','result','onload','center','setActiveObject','transparent','item','src','#FF0000','activeElement','12ABtWZR','target','backgroundColor','stringify','apply','Arial','width','files','downloadImage','434195iTpXYz','local','then','isType','key','5167057aBMqzh','ctrlKey','setCoords','https://m.media-amazon.com/images/I/711A-vi410L._AC_SL1000_.jpg','getElementById','find','exportTemplate','code','addRect','removeChild','paste\x20event\x20for\x20image','prompt','KeyV','toString','testTemplate','renderAll','backToEditor','https://m.media-amazon.com/images/I/61NQHkhutyL._AC_SY450_.jpg','https://m.media-amazon.com/images/I/61P8yDGTczL._AC_SL1000_.jpg','addEventListener','text','toJSON','preventDefault','btnImportTemplate','Rect','red','item\x20is\x20image','clipboard','type','canvasBorderColor','sendToBack','INPUT','An\x20error\x20occurred\x20while\x20reading\x20the\x20clipboard:','scaleToWidth','8hOMpMh','bold','change','border','7741349fnzJtd','get','getActiveObject','getObjects','copiedObject','addBanner','image','readAsDataURL','30zXNpCw','clipboard-read','input','from','template','bannerPadding','paste','loadTemplate','getWidth','Backspace','Text','loadFromJSON','clipboardData','storage','addBorder','items','scaleY','revokeObjectURL','Image','Best\x20Seller','forEach','template.json','canvasBorderThickness','appendChild','clear','state','black','error','paste\x20event','white','left','tagName','object:moving','toDataURL','add','top','https://m.media-amazon.com/images/I/61SBYOJ0r6L._AC_SL1000_.jpg','image/png','116233pSEWyR','download'];a0_0x4308=function(){return _0x9e0ff8;};return a0_0x4308();}async function loadTemplate(){const _0x42f528=a0_0x459481;var {template:_0x157398}=await chrome['storage'][_0x42f528(0xe0)][_0x42f528(0x10b)]('template');_0x157398?canvas[_0x42f528(0x11d)](_0x157398,()=>{const _0x2067f3=_0x42f528;canvas[_0x2067f3(0xf3)](),updateRectCount();}):alert('No\x20saved\x20template\x20found!');}function updateRectCount(){const _0x7eaf46=a0_0x459481;let _0x476b32=0x0;canvas[_0x7eaf46(0x10d)]()[_0x7eaf46(0x85)](_0x1ee0b8=>{const _0x1121e8=_0x7eaf46;if(_0x1ee0b8[_0x1121e8(0xe2)]('group')){const _0xd326fa=parseInt(_0x1ee0b8[_0x1121e8(0xd2)](0x1)['text'],0xa);if(_0xd326fa>_0x476b32)_0x476b32=_0xd326fa;}}),rectCount=_0x476b32;}async function testTemplate(){const _0x4135cb=a0_0x459481;document[_0x4135cb(0xe8)](_0x4135cb(0x9e))[_0x4135cb(0xa6)]=!![],document[_0x4135cb(0xe8)]('tester')[_0x4135cb(0xa6)]=![],testCanvas[_0x4135cb(0x89)](),testCanvas['backgroundColor']=_0x4135cb(0x8e);var {template:_0x27659d}=await chrome[_0x4135cb(0x11f)][_0x4135cb(0xe0)]['get'](_0x4135cb(0x116));testCanvas[_0x4135cb(0x11d)](_0x27659d,async()=>{const _0x55ef24=_0x4135cb;await populateImages(),testCanvas[_0x55ef24(0xf3)]();});}async function populateImages(){const _0x23b1a8=a0_0x459481;let _0xbe837a=0x0;const _0x291a6a=testCanvas['getObjects']();for(let _0xa81b32 of _0x291a6a){if(_0xa81b32[_0x23b1a8(0xe2)](_0x23b1a8(0xa3))&&_0xbe837a<testImages['length']&&!isNaN(_0xa81b32[_0x23b1a8(0xd2)](0x1)[_0x23b1a8(0xf8)])){const _0x2a0a58=_0xa81b32[_0x23b1a8(0xdc)]*_0xa81b32[_0x23b1a8(0xbc)],_0x406a5c=_0xa81b32[_0x23b1a8(0x9f)]*_0xa81b32[_0x23b1a8(0x122)],_0x4c740d=await loadImage(testImages[_0xbe837a]),_0x5868e1=await processImage(_0x4c740d,_0x2a0a58,_0x406a5c),_0x2009f9=new fabric[(_0x23b1a8(0x83))](_0x5868e1,{'left':_0xa81b32[_0x23b1a8(0x8f)],'top':_0xa81b32[_0x23b1a8(0x94)]});_0x2009f9['scaleToWidth'](_0x2a0a58),_0x2009f9['scaleToHeight'](_0x406a5c),testCanvas['add'](_0x2009f9),testCanvas[_0x23b1a8(0xb2)](_0xa81b32),centerImage(_0x2009f9,_0x2a0a58,_0x406a5c,_0xa81b32),_0xbe837a++;}else _0xa81b32['isType']('group')&&!isNaN(_0xa81b32[_0x23b1a8(0xd2)](0x1)['text'])&&testCanvas[_0x23b1a8(0xb2)](_0xa81b32);}}function clearCanvas(){const _0x4616f2=a0_0x459481;canvas[_0x4616f2(0x89)](),rectCount=0x0;}function backToEditor(){const _0x55c0fa=a0_0x459481;document['getElementById'](_0x55c0fa(0x9e))['hidden']=![],document[_0x55c0fa(0xe8)](_0x55c0fa(0xbf))[_0x55c0fa(0xa6)]=!![];}function deleteObject(){const _0x12dd43=a0_0x459481,_0xcc5d97=canvas[_0x12dd43(0x10c)]();_0xcc5d97&&canvas[_0x12dd43(0xb2)](_0xcc5d97);}async function loadImage(_0x340ef2){return new Promise(_0x294ffd=>{const _0x49cf94=a0_0x5c42,_0x490023=new Image();_0x490023[_0x49cf94(0xd3)]=_0x340ef2,_0x490023[_0x49cf94(0xce)]=()=>_0x294ffd(_0x490023);});}async function processImage(_0x4e2af0,_0x3f1673,_0x1588b0){var _0x49d030=await resizeImage(_0x4e2af0,_0x3f1673,_0x1588b0);return _0x49d030=await makeImageTransparent(_0x49d030),await getRoundedImage(_0x49d030,0x5);}function centerImage(_0x20bfac,_0x483452,_0x4fa245,_0x194e80){const _0x30d91f=a0_0x459481,_0x149410=_0x20bfac[_0x30d91f(0x99)](),_0x13611d=_0x20bfac[_0x30d91f(0xab)]();_0x149410<_0x483452&&_0x20bfac['set']({'left':_0x194e80[_0x30d91f(0x8f)]+(_0x483452-_0x149410)/0x2}),_0x13611d<_0x4fa245&&_0x20bfac[_0x30d91f(0xaf)]({'top':_0x194e80['top']+(_0x4fa245-_0x13611d)/0x2}),_0x20bfac[_0x30d91f(0xe6)]();}function addBanner(){const _0x4c9ab7=a0_0x459481,_0x46ecfc=document[_0x4c9ab7(0xe8)](_0x4c9ab7(0x9b))[_0x4c9ab7(0xc4)]||_0x4c9ab7(0x84),_0x8cc91e=document[_0x4c9ab7(0xe8)]('bannerColor')[_0x4c9ab7(0xc4)]||_0x4c9ab7(0xd4),_0x441ffc=document[_0x4c9ab7(0xe8)](_0x4c9ab7(0xb1))[_0x4c9ab7(0xc4)]||_0x4c9ab7(0xdb),_0xd81c34=parseInt(document[_0x4c9ab7(0xe8)](_0x4c9ab7(0x117))[_0x4c9ab7(0xc4)])||0x14,_0x381874=new fabric[(_0x4c9ab7(0x11c))](_0x46ecfc,{'fontSize':0x64,'fill':_0x4c9ab7(0x8e),'fontFamily':_0x441ffc,'fontWeight':_0x4c9ab7(0x107),'originX':_0x4c9ab7(0xcf),'originY':_0x4c9ab7(0xcf)}),_0x23b642=new fabric[(_0x4c9ab7(0xfc))]({'rx':0xa,'ry':0xa,'fill':_0x8cc91e,'width':_0x381874[_0x4c9ab7(0xdc)]+_0xd81c34*0x2,'height':_0x381874['height']+_0xd81c34,'originX':_0x4c9ab7(0xcf),'originY':'center'}),_0x13f2aa=new fabric['Group']([_0x23b642,_0x381874],{'left':0x32,'top':0x32});canvas['add'](_0x13f2aa),canvas[_0x4c9ab7(0xd0)](_0x13f2aa),canvas[_0x4c9ab7(0xf3)]();}document[a0_0x459481(0xe8)](a0_0x459481(0xea))[a0_0x459481(0xf7)](a0_0x459481(0xa4),exportTemplate),document['getElementById'](a0_0x459481(0xfb))['addEventListener']('click',()=>{const _0x582499=a0_0x459481;document[_0x582499(0xe8)]('importTemplate')[_0x582499(0xa4)]();}),document[a0_0x459481(0xe8)]('importTemplate')[a0_0x459481(0xf7)](a0_0x459481(0x108),importTemplate);function exportTemplate(){const _0x44a79a=a0_0x459481,_0x56cfa8=JSON[_0x44a79a(0xd9)](canvas['toJSON']([_0x44a79a(0x92)])),_0x469b0d=new Blob([_0x56cfa8],{'type':'application/json'}),_0x5a6cc8=URL['createObjectURL'](_0x469b0d),_0x94e347=document['createElement']('a');_0x94e347[_0x44a79a(0xc5)]=_0x5a6cc8,_0x94e347[_0x44a79a(0x98)]=_0x44a79a(0x86),document[_0x44a79a(0xa2)][_0x44a79a(0x88)](_0x94e347),_0x94e347[_0x44a79a(0xa4)](),window[_0x44a79a(0xa8)][_0x44a79a(0x123)](_0x5a6cc8);}function importTemplate(_0x38cecc){const _0x33f829=a0_0x459481,_0x5b8b3a=_0x38cecc[_0x33f829(0xd7)]['files'][0x0];if(_0x5b8b3a){const _0x2feb44=new FileReader();_0x2feb44[_0x33f829(0xce)]=_0x28259d=>{const _0x1e3fed=_0x33f829,_0x4e4537=_0x28259d['target'][_0x1e3fed(0xcd)];canvas[_0x1e3fed(0x11d)](_0x4e4537,()=>{canvas['renderAll'](),updateRectCount();});},_0x2feb44[_0x33f829(0xad)](_0x5b8b3a);}}function downloadImage(){const _0x500dff=a0_0x459481,_0x4fd1b0=testCanvas[_0x500dff(0xd8)],_0x17dabc=new fabric[(_0x500dff(0xfc))]({'left':0x0,'top':0x0,'width':testCanvas['width'],'height':testCanvas[_0x500dff(0x9f)],'fill':'white'});testCanvas['add'](_0x17dabc),_0x17dabc[_0x500dff(0x102)]();const _0x1888be=testCanvas[_0x500dff(0x92)]({'format':_0x500dff(0xa1),'quality':0x1});testCanvas['remove'](_0x17dabc);const _0x4e9217=document['createElement']('a');_0x4e9217[_0x500dff(0x98)]=_0x500dff(0xba),_0x4e9217[_0x500dff(0xc5)]=_0x1888be,document['body'][_0x500dff(0x88)](_0x4e9217),_0x4e9217[_0x500dff(0xa4)](),document['body'][_0x500dff(0xed)](_0x4e9217),testCanvas['backgroundColor']=_0x4fd1b0,testCanvas[_0x500dff(0xf3)]();}document[a0_0x459481(0xe8)](a0_0x459481(0xde))[a0_0x459481(0xf7)](a0_0x459481(0xa4),downloadImage);function throttle(_0x4e8410,_0x1a4d27){let _0x11276d=![];return function(){const _0xe54930=a0_0x5c42;!_0x11276d&&(_0x4e8410[_0xe54930(0xda)](this,arguments),_0x11276d=!![],setTimeout(()=>{_0x11276d=![];},_0x1a4d27));};}let hGuideLine=null,vGuideLine=null;canvas['on'](a0_0x459481(0x91),throttle(_0x5b85c2=>{const _0x3f5601=a0_0x459481,_0x3e5d26=_0x5b85c2[_0x3f5601(0xd7)];clearGuidelines(),canvas[_0x3f5601(0x10d)]()['forEach'](_0x83626c=>{const _0x17469f=_0x3f5601;if(_0x83626c===_0x3e5d26||_0x83626c['type']===_0x17469f(0xc6))return;Math[_0x17469f(0xbe)](_0x3e5d26['left']-_0x83626c[_0x17469f(0x8f)])<0xa&&_0x3e5d26[_0x17469f(0xaf)]({'left':_0x83626c['left']}),Math[_0x17469f(0xbe)](_0x3e5d26[_0x17469f(0x94)]-_0x83626c[_0x17469f(0x94)])<0xa&&_0x3e5d26[_0x17469f(0xaf)]({'top':_0x83626c[_0x17469f(0x94)]});}),canvas[_0x3f5601(0xf3)]();},0x32));function drawGuideline(_0x4c5fa8,_0x2beeef,_0x2ed645,_0x1195c2){const _0x270551=a0_0x459481;!hGuideLine&&(hGuideLine=new fabric['Line']([0x0,0x0,0x0,0x0],{'stroke':_0x270551(0xfd),'selectable':![],'evented':![]}),canvas[_0x270551(0x93)](hGuideLine)),!vGuideLine&&(vGuideLine=new fabric['Line']([0x0,0x0,0x0,0x0],{'stroke':_0x270551(0xfd),'selectable':![],'evented':![]}),canvas[_0x270551(0x93)](vGuideLine)),_0x4c5fa8===_0x2ed645?(vGuideLine[_0x270551(0xaf)]({'x1':_0x4c5fa8,'y1':_0x2beeef,'x2':_0x2ed645,'y2':_0x1195c2}),vGuideLine[_0x270551(0xe6)]()):(hGuideLine[_0x270551(0xaf)]({'x1':_0x4c5fa8,'y1':_0x2beeef,'x2':_0x2ed645,'y2':_0x1195c2}),hGuideLine[_0x270551(0xe6)]());}function clearGuidelines(){const _0xa6cce0=a0_0x459481;hGuideLine&&(hGuideLine[_0xa6cce0(0xaf)]({'x1':0x0,'y1':0x0,'x2':0x0,'y2':0x0}),hGuideLine[_0xa6cce0(0xe6)]()),vGuideLine&&(vGuideLine[_0xa6cce0(0xaf)]({'x1':0x0,'y1':0x0,'x2':0x0,'y2':0x0}),vGuideLine[_0xa6cce0(0xe6)]());}var canvasBorders=[];function addBorderToCanvas(_0x1fa6bf,_0x4f979d){const _0x32c998=a0_0x459481;canvasBorders[_0x32c998(0x85)](_0x390f20=>canvas[_0x32c998(0xb2)](_0x390f20)),canvasBorders=[];const _0x13b3f0=new fabric[(_0x32c998(0xfc))]({'left':0x0,'top':0x0,'width':canvas[_0x32c998(0x11a)](),'height':_0x4f979d,'fill':_0x1fa6bf,'selectable':!![],'evented':!![]}),_0x299a3f=new fabric[(_0x32c998(0xfc))]({'left':0x0,'top':canvas['getHeight']()-_0x4f979d,'width':canvas['getWidth'](),'height':_0x4f979d,'fill':_0x1fa6bf,'selectable':!![],'evented':!![]}),_0x1a111a=new fabric[(_0x32c998(0xfc))]({'left':0x0,'top':0x0,'width':_0x4f979d,'height':canvas[_0x32c998(0xbd)](),'fill':_0x1fa6bf,'selectable':!![],'evented':!![]}),_0x4cbcb1=new fabric[(_0x32c998(0xfc))]({'left':canvas[_0x32c998(0x11a)]()-_0x4f979d,'top':0x0,'width':_0x4f979d,'height':canvas[_0x32c998(0xbd)](),'fill':_0x1fa6bf,'selectable':!![],'evented':!![]});canvasBorders[_0x32c998(0xb9)](_0x13b3f0,_0x299a3f,_0x1a111a,_0x4cbcb1),canvasBorders['forEach'](_0x3566a5=>canvas['add'](_0x3566a5)),canvas['renderAll']();}function handleAddCanvasBorder(){const _0x8149b2=a0_0x459481,_0x2d613f=document[_0x8149b2(0xe8)](_0x8149b2(0x101))['value'],_0x21d384=parseInt(document[_0x8149b2(0xe8)](_0x8149b2(0x87))[_0x8149b2(0xc4)]);addBorderToCanvas(_0x2d613f,_0x21d384);}document[a0_0x459481(0xe8)](a0_0x459481(0x120))[a0_0x459481(0xf7)]('click',handleAddCanvasBorder);