<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracker System</title>
</head>
<body>

    <!-- Main Controls -->
    <div id="main_controls">
        <button id="start_tracking">Start Tracking</button>
        
        <label class="switch">
            <input type="checkbox" id="tracker_run_status">
            <span class="slider round"></span>
        </label>
    </div>

    <!-- Filter Options -->
    <div id="tracker_filters">
        <h2>Tracker Filters</h2>

        <div>
            <input type="checkbox" id="enable_stock_monitor">
            <label for="enable_stock_monitor">Enable Stock Monitor</label>
        </div>

        <div>
            <input type="checkbox" id="enable_price_monitor">
            <label for="enable_price_monitor">Enable Price Monitor</label>
        </div>



        <h2>Pruning Options</h2>

        <div>
            <input type="checkbox" id="enable_delete_policy_violation_items">
            <label for="enable_delete_policy_violation_items">Delete Policy Violation Items</label>
        </div>

        <div>
            <input type="checkbox" id="enable_delete_items_with_no_sku">
            <label for="enable_delete_items_with_no_sku">Delete Items with No SKU (Otherwise will be skipped)</label>
        </div>

        <div>
            <input type="checkbox" id="enable_delete_items_not_found_on_amazon">
            <label for="enable_delete_items_not_found_on_amazon">Delete Items Not Found on Amazon</label>
        </div>

        <div>
            <input type="checkbox" id="enable_delete_items_sku_changed_on_amazon">
            <label for="enable_delete_items_sku_changed_on_amazon">Delete Items where SKU has been changed on Amazon</label>
        </div>

        <div id="enable-delete-items-with-no-sales" class="rule-based-setting">
            <input type="checkbox" class="enable-setting-checkbox">
            <label>
                <select class="item-action-select" style="margin-right:5px;">
                    <option value="delete">Delete</option>
                    <option value="out-of-stock">Make Out of Stock</option>
                </select>
                items with
                <input type="number" class="sales-threshold-input" min="0" value="0" style="width:50px; margin: 0 5px;">
                or fewer sales that are older than
                <input type="number" class="days-threshold-input" min="0" value="30" style="width:50px; margin: 0 5px;">
                days
            </label>
        </div>
        
        
        

         

    </div>

    

    <!-- Additional Settings -->
    <div id="additional_settings">
        <h2>Additional Settings</h2>

        <div>
            <label for="enable_continuous_tracking">Enable Continuous Tracking</label>
            <input type="checkbox" id="enable_continuous_tracking">
        </div>

        <div>
            <label for="tracking_timeout">Tracking Timeout (in seconds)</label>
            <input type="text" id="tracking_timeout" value="60">
        </div>

        <div>
            <label for="page_number">Page Number</label>
            <input type="text" id="page_number" value="1">
        </div>
    </div>

    <!-- Test Button (for development, ignore in production) -->
    <button id="test_end_item">Test End Item</button>

    <script src="script.js"></script>
</body>
</html>
