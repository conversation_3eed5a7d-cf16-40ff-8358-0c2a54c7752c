var a0_0x5bdc90=a0_0x460d;(function(_0x8d42bf,_0x1503e5){var _0x36b7a9=a0_0x460d,_0x57400e=_0x8d42bf();while(!![]){try{var _0x27055=parseInt(_0x36b7a9(0xd7))/0x1+-parseInt(_0x36b7a9(0xd4))/0x2*(parseInt(_0x36b7a9(0xc6))/0x3)+parseInt(_0x36b7a9(0xcf))/0x4*(-parseInt(_0x36b7a9(0xdf))/0x5)+parseInt(_0x36b7a9(0xca))/0x6+parseInt(_0x36b7a9(0xd9))/0x7+parseInt(_0x36b7a9(0xb5))/0x8+-parseInt(_0x36b7a9(0xb6))/0x9*(parseInt(_0x36b7a9(0xbf))/0xa);if(_0x27055===_0x1503e5)break;else _0x57400e['push'](_0x57400e['shift']());}catch(_0x65c4ee){_0x57400e['push'](_0x57400e['shift']());}}}(a0_0x4eab,0x68b88),console['log']('content.js\x20loaded'),onPageLoadAndStableNotifyBackground(),chrome[a0_0x5bdc90(0xd2)]['onMessage']['addListener']((_0xd340a6,_0x14e228,_0x533db0)=>{var _0x16d58b=a0_0x5bdc90;console[_0x16d58b(0xc9)](_0x14e228[_0x16d58b(0xcc)]?_0x16d58b(0xdb)+_0x14e228[_0x16d58b(0xcc)][_0x16d58b(0xe3)]:_0x16d58b(0xd5)+_0xd340a6[_0x16d58b(0xe0)]);if(_0xd340a6['type']==='list-item')return listTheItem()[_0x16d58b(0xd3)](function(_0x22db47){_0x533db0(_0x22db47);}),!![];if(_0xd340a6[_0x16d58b(0xe0)]===_0x16d58b(0xba))return listTheItem(_0xd340a6[_0x16d58b(0xc1)]['customLabel'])[_0x16d58b(0xd3)](function(_0x540c45){_0x533db0(_0x540c45);}),!![];if(_0xd340a6[_0x16d58b(0xe0)]==='get_seller_name_from_item_page'){console[_0x16d58b(0xc9)]('get_seller_name_from_item_page');var _0x4561e1=getSellerName();return console['log'](_0x16d58b(0xbb),_0x4561e1),_0x533db0(_0x4561e1),!![];}if(_0xd340a6[_0x16d58b(0xe0)]==='get_sku_from_ebay_item')return onPageLoadAndStable()[_0x16d58b(0xd3)](async function(_0x41e743){var _0x32a033=await getCustomLabel();_0x533db0(_0x32a033);}),!![];if(_0xd340a6['type']===_0x16d58b(0xbd))return getDescriptionFromIframe()[_0x16d58b(0xd3)](function(_0x2fe8e6){var _0x34e3af=_0x16d58b,_0x52be7d=new DOMParser(),_0xdb035a=_0x52be7d['parseFromString'](_0x2fe8e6,_0x34e3af(0xb9)),_0x68decb=_0xdb035a['body'][_0x34e3af(0xd8)];_0x68decb=_0x68decb['trim'](),_0x68decb=_0x68decb[_0x34e3af(0xc2)](/\s\s+/g,'\x20'),_0x68decb=_0x68decb[_0x34e3af(0xce)]()[_0x34e3af(0xc2)](/&amp;/g,'&')[_0x34e3af(0xc2)](/&lt;/g,'<')['replace'](/&gt;/g,'>')[_0x34e3af(0xc2)](/&quot;/g,'\x22')['replace'](/&#39;/g,'\x27')[_0x34e3af(0xc2)](/\t+/g,'\x20')[_0x34e3af(0xc2)](/[\r\n]+/g,'\x20')[_0x34e3af(0xc2)](/\s\s+/g,'\x20')['replace'](/[.?!]{2,}/g,_0x4e0011=>_0x4e0011[0x0])[_0x34e3af(0xc2)](/<\/?[a-z][^>]*>/gi,'');var _0x1579e8=getTitle();_0x68decb=_0x1579e8+'\x20'+_0x68decb,_0x533db0(_0x68decb);}),!![];return![];}));function a0_0x4eab(){var _0x20faea=['log','3946602sHorHA','click','tab','buyboxCta','trim','1832wsPcjq','Button\x20not\x20found\x20within\x20the\x20specified\x20time.','.ux-seller-section__item--seller','runtime','then','34MnTcsx','From\x20the\x20extension\x20request.type\x20ebay.js','price','569911CITmtq','textContent','3875648ILQVtX','innerText','From\x20a\x20content\x20script:','div.info-box','createElement','itemNumber','6420AUKBXv','type','getURL','style','url','6312000aDrucA','2194713CcWUSg','prepend','images','text/html','clone-item','sellerName','#mainContent\x20[class*=\x27item-title\x27]\x20span','get_description_from_ebay_item','display','30KjzZcv','querySelector','ebayData','replace','Favicons/Completed/right_arrow.png','skuToSave','appendChild','145017KaLPRj','div','none'];a0_0x4eab=function(){return _0x20faea;};return a0_0x4eab();}async function listTheItem(_0x44ee6c){var _0x2dfa2a=a0_0x5bdc90,_0x4ff7a5=await waitForButton('a.custom-button');if(_0x44ee6c){var _0x1f7005=document[_0x2dfa2a(0xdd)](_0x2dfa2a(0xc7));_0x1f7005['id']=_0x2dfa2a(0xc4),_0x1f7005[_0x2dfa2a(0xe2)][_0x2dfa2a(0xbe)]=_0x2dfa2a(0xc8),_0x1f7005[_0x2dfa2a(0xda)]=_0x44ee6c,_0x4ff7a5['appendChild'](_0x1f7005);}var _0x315342=chrome[_0x2dfa2a(0xd2)][_0x2dfa2a(0xe1)](_0x2dfa2a(0xc3));changeFaviconOfPage(_0x315342),_0x4ff7a5[_0x2dfa2a(0xcb)]();var _0x385a6e=await waitForButton(_0x2dfa2a(0xdc));return backgroundResponse;}function a0_0x460d(_0x58b47f,_0x3396e0){var _0x4eabe5=a0_0x4eab();return a0_0x460d=function(_0x460d84,_0x555435){_0x460d84=_0x460d84-0xb5;var _0x2651f9=_0x4eabe5[_0x460d84];return _0x2651f9;},a0_0x460d(_0x58b47f,_0x3396e0);}function waitForButton(_0x45e32e){return new Promise((_0x5c87cb,_0x586460)=>{const _0x13618b=setInterval(()=>{var _0x344779=a0_0x460d;const _0x40506f=document[_0x344779(0xc0)](_0x45e32e);_0x40506f&&(clearInterval(_0x13618b),_0x5c87cb(_0x40506f));},0x64),_0x2bf769=setTimeout(()=>{var _0xe8ea18=a0_0x460d;clearInterval(_0x13618b),_0x586460(new Error(_0xe8ea18(0xd0)));},0x1388);_0x13618b&&clearTimeout(_0x2bf769);});}function sleep(_0x314aa){return new Promise(_0xb2ead3=>setTimeout(_0xb2ead3,_0x314aa));}async function performTasks(){var _0x1fef02=a0_0x5bdc90;await onPageLoadAndIdle();var _0x4f41ea=document[_0x1fef02(0xc0)]('.x-buybox-cta');console[_0x1fef02(0xc9)](_0x1fef02(0xcd),_0x4f41ea);!_0x4f41ea&&(_0x4f41ea=document[_0x1fef02(0xc0)](_0x1fef02(0xd1)));var _0x591724=createButtonListToEbay();_0x4f41ea[_0x1fef02(0xc5)](_0x591724);var _0x225e22=getTitle(),_0x50a427=getSellerName(),_0x227c3c=getImages(),_0x4776fc=getItemNumber(),_0x45142e=getPrice();console[_0x1fef02(0xc9)]('title',_0x225e22),console[_0x1fef02(0xc9)](_0x1fef02(0xbb),_0x50a427),console[_0x1fef02(0xc9)](_0x1fef02(0xb8),_0x227c3c),console[_0x1fef02(0xc9)](_0x1fef02(0xde),_0x4776fc),console[_0x1fef02(0xc9)](_0x1fef02(0xd6),_0x45142e);var _0x5087ff=createEcommerceSearchButtonsPanel2(_0x225e22,_0x50a427,_0x227c3c[0x0],![],_0x4776fc,_0x45142e);_0x4f41ea[_0x1fef02(0xc5)](_0x5087ff);var _0x5dd5b4=await getCustomLabel();console[_0x1fef02(0xc9)]('customLabel',_0x5dd5b4);if(_0x5dd5b4){var _0x3376e7=createLookUpSkuButton(atob(_0x5dd5b4)),_0x1c277e=document[_0x1fef02(0xc0)](_0x1fef02(0xbc));_0x1c277e[_0x1fef02(0xb7)](_0x3376e7);}}performTasks();