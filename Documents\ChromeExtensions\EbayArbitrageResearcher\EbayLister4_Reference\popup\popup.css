

#logo {
  max-width: 228px;
  max-height: 228px;
  width: auto;
  height: auto;
}

/* User Features */
#user_features {
  background-color: #f5f5f5;
  padding: 10px;
}

h2 {
  color: #0064d2;
}

input[type="text"] {
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 14px;
}

#ultimate-buttons button{
  margin-top: 2px;
}

/* button {
  padding: 5px 10px;
  background-color: #0064d2;
  color: #fff;
  border: none;
  border-radius: 3px;
  font-size: 14px;
  cursor: pointer;
}

button:hover {
  background-color: #0051a8;
} */


/* .switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: #0064d2;
}

input:focus + .slider {
  box-shadow: 0 0 1px #0064d2;
}

.switch input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
  background-color: #2196F3;
} */

.switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 17px;
  margin: 0 10px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(13px);
  -ms-transform: translateX(13px);
  transform: translateX(13px);
}


#dev_tools button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  background-color: #2196F3;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}

#dev_tools button:hover {
  background-color: #0c7cd5;
}

#dev_tools button:active {
  background-color: #0a6cb8;
}



.save_features {
  display: flex;
  align-items: center;
}

.save_features input[type="text"] {
  flex-grow: 1;
  margin-right: 10px;
}

.save_features button#save_image_to_local_storage {
  margin-left: 10px;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  background-color: #2196F3;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}

.save_features button#save_image_to_local_storage:hover {
  background-color: #0c7cd5;
}

.save_features button#save_image_to_local_storage:active {
  background-color: #0a6cb8;
}

.watermark-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.watermark {
  position: relative;
  display: inline-block;
}

.watermark img {
  display: block;
  width: 200px;
  height: 200px;
  object-fit: cover;
}

.watermark .text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
}

.price-container {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.price-container label {
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
}

.price-container input {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  font-size: 16px;
  background-color: #f0fff0;
  box-shadow: 0 0 10px #00b300;
  transition: box-shadow 0.3s ease-in-out;
}

.price-container input:focus {
  outline: none;
  box-shadow: 0 0 10px #00b300, 0 0 10px #00b300 inset;
}

body {
  position: relative;
  height: 100%;
  min-height: 100vh;
  width: 95%;
  min-width: 90vw;
}

#login_container {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border: 1px solid black;
  z-index: 2;
}

#status_container {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: white;
  padding: 5px;
  border: 1px solid black;
  z-index: 1;
}

#status_text {
  font-weight: bold;
}

#logout_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url("https://cdn-icons-png.flaticon.com/128/3580/3580175.png");
  background-size: cover;
  cursor: pointer;
}


#user_info {
  display: flex;
  flex-direction: column;
}

#user_details {
  margin-top: 10px;
}



#bulk-list {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #f9f9f9;
}

#bulk-list h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

#bulk-list p {
  font-size: 16px;
  margin-bottom: 10px;
}

#amazon-links {
  width: 95%;
  height: 100px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  resize: none;
  margin-bottom: 10px;
}

#list-btn {
  background-color: #4CAF50;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
}

#list-btn:hover {
  background-color: #3e8e41;
}

#position-container {
  margin-top: 10px;
}

#position-label {
  margin-right: 10px;
}

#position-value {
  font-weight: bold;
  margin-right: 10px;
}



#ultimate-features {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: left;
  margin-top: 30px;
}

#ultimate-features button {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background-color: #59d8f8;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

#ultimate-features button:hover {
  background-color: #005fa3;
}





/* //vero brands */
#veroBrandContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.tasteful-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 10px;
}

.tasteful-button:hover {
  background-color: #2980b9;
}

.loader-container {
  width: 30px;
  height: 30px;
}

.loader {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 100%;
  height: 100%;
  animation: spin 2s linear infinite;
}

.status {
  margin: 0 10px;
}

.icon.success {
  color: green;
  font-size: 24px;
}

.icon.fail {
  color: red;
  font-size: 24px;
}

.counter {
  font-weight: bold;
}

.hidden {
  display: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



#agent_name_container
{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

#agent_name_container input[type="text"]
{
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 14px;
  width: 100%;
}



#image_template_container_options {
  display: flex;
  flex-direction: column;
}

#image_template_container_options label {
  display: flex;
  justify-content: space-between; /* Spreads content to align span to the left and switch to the right */
  align-items: center;
  margin-bottom: 3px; /* Adds spacing between rows */
}