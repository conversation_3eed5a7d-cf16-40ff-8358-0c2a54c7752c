<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="eta_options.css">
    <title>ETA Message Options</title>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <p>Edit the message below. You can use {{Customer_Name}} and {{Delivery_Date}} as placeholders which will be replaced in the actual message sent to customers.</p>
        </div>
        <textarea id="etaMessage" rows="10" cols="50"></textarea>
        <div class="button-options">
            <button id="resetMessage">Revert to Default</button>
            <button id="saveMessage">Save Message</button>
        </div>
     
    </div>
    <script src="eta_options.js"></script>
</body>
</html>
