#test_button_div {
    margin-top: 20px;
    display: inline-block;
    padding: 10px;
    border: 1px dashed grey;
    background-color: #f8f8f8;
    color: #888;
    font-size: 0.9em;
    opacity: 0.7;
    text-align: left;
    border-radius: 5px;
}

#test_input_container label {
    margin-bottom: 5px;
}

#test_input_container input,
#test_button {
    margin-top: 5px;
    padding: 5px;
    font-size: 0.9em;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #eaeaea;
    color: #555;
}

#test_button {
    margin-top: 10px;
}

#test_button:hover {
    background-color: #f0f0f0;
    cursor: pointer;
}

#test_button:active {
    background-color: #e0e0e0;
}



#additional_gspr_controls_container, #additional_controls_chinese_sellers_container {
   margin-top: 20px;
    display: flex;
}


.tracking-container {
    margin-top: 20px;
    max-width: 320px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-family: Arial, sans-serif;
    font-size: 14px;
    color: #333;
  }
  
  /* Each row */
  .tracking-info {
    display: flex;            /* Flex to control alignment */
    justify-content: flex-end;/* Push the content to the right */
    align-items: center;
    margin-bottom: 10px;
  }
  
  /* Right-align labels with a fixed width so they line up */
  .tracking-info label {
    width: 80px;       /* Adjust as desired */
    text-align: right; 
    margin-right: 10px;
  }
  
  /* Container for the numeric parts so they can flex together */
  .numbers {
    display: flex;
    align-items: center;
  }
  
  /* Inputs have a fixed width to keep them aligned vertically */
  .numbers input {
    width: 60px;
    padding: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: center;
  }
  
  /* Slash in between is spaced consistently */
  .numbers .slash {
    margin: 0 5px;
  }
  
  /* The total number/spans can also have a fixed or min-width 
     to ensure alignment across rows if needed */
  .numbers span {
    font-weight: bold;
    min-width: 30px;   /* ensures numbers line up if they vary in width */
    text-align: center;
  }
  

/* TWO-BAR PROGRESS CONTAINER FIXED AT TOP-RIGHT */
#progress_bars_container {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 9999;
    width: 300px;      /* Adjust as you see fit */
    min-height: 80px;
    padding: 10px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    font-family: Arial, sans-serif;
    border: 1px solid #eee;
  }
  
  /* If the screen is too narrow, place it at the bottom instead */
  @media (max-width: 450px) {
    #progress_bars_container {
      top: auto;
      bottom: 10px;
      right: 10px;
    }
  }
  
  .progress-section {
    margin-bottom: 12px;
  }
  
  .progress-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    text-align: left;
  }
  
  .progress-bar-bg {
    width: 100%;
    height: 10px;
    background-color: #f1f1f1;
    border-radius: 5px;
    overflow: hidden;
  }
  
  .progress-bar-fill {
    height: 100%;
    width: 0%;             /* updated via JS */
    transition: width 0.3s linear;
    /* The wave animation to convey constant movement */
    background-size: 300% 100%;
    animation: wave 2s linear infinite;
  }
  
  /* Top Bar: Vibrant green gradient */
  .item-bar-fill {
    background-image: linear-gradient(
      90deg, 
      #4caf50 0%,     /* Vibrant green */
      #66bb6a 50%,    /* Light green midpoint */
      #4caf50 100%
    );
  }
  
  /* Bottom Bar: Teal/Aqua gradient */
  .page-bar-fill {
    background-image: linear-gradient(
      90deg, 
      #009688 0%,     /* Teal */
      #4dd0e1 50%,     /* Lighter teal midpoint */
      #009688 100%
    );
  }
  
  /* Keyframes for the wave effect: background gradient shifts horizontally */
  @keyframes wave {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 0%; }
  }
  
  #multi_country_grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);  /* 4 columns of equal width */
    grid-auto-rows: auto;                   /* rows size to content */
    gap: 4px;                                /* small gutter */
    max-width: 360px;                        /* optional: cap total width (4×80px + gaps) */
  }
  
  #multi_country_grid label {
    display: flex;
    align-items: center;
    font-size: 0.9em;
    padding: 2px 4px;                        /* a little breathing room */
    border-radius: 4px;                      /* subtle rounding */
  }
  
  #multi_country_grid label:hover {
    background: rgba(0,0,0,0.05);            /* gentle hover highlight */
  }
  

  .pill-toggle {
    display: inline-block;
    cursor: pointer;
  }
  
  .pill-toggle input {
    display: none; /* hide the real checkbox */
  }
  
  .pill-toggle__label {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 999px;
    background: #e0e0e0;
    color: #555;
    font-family: sans-serif;
    font-weight: 500;
    transition: background 0.2s, color 0.2s;
  }
  
  /* show “Start Tracking” when unchecked */
  .pill-toggle__label::after {
    content: attr(data-off);
  }
  
  /* on hover, gently highlight */
  .pill-toggle__label:hover {
    background: #d5d5d5;
  }
  
  /* checked state: green and “Stop Tracking” */
  .pill-toggle input:checked + .pill-toggle__label {
    background: #4caf50;
    color: #fff;
  }
  .pill-toggle input:checked + .pill-toggle__label::after {
    content: attr(data-on);
  }
  