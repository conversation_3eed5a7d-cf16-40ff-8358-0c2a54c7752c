**Competitor's Title:**
{{Competitor_title}}

**Our Title:**
{{Our_title}}

**Instructions:**

We have evidence that the competitor's title generates high visibility on eBay, as their item has sold 30 times in the last 3 days. To enhance our item's visibility through eBay SEO:

- **Replicate the competitor's title as closely as possible, word for word, without including any false information.**
- **Ensure all information in the title accurately represents our product.**
- **Aim for a title length between 75-80 characters to maximize visibility.**
- **If there is remaining space within the 80-character limit after matching the competitor's title, add additional relevant keywords at the end of the title to reach the desired length.**
- **Include a character count check to confirm the final title is between 75-80 characters.**
- **Focus on matching the competitor's title first, as we know it is effective for search visibility.**

**Output Format:**

Please provide the restructured title and the reasoning behind it in JSON format.

**Important:**

- **Provide only the JSON object as the output, with no additional text or explanations outside the JSON.**
- **Ensure the JSON is properly formatted and contains no syntax errors so it can be directly parsed by a JSON parser.**
- **Do not include any comments or annotations outside the JSON object.**

**Example Output:**
{
  "restructured_title": "Your restructured title here",
  "reasoning": "Explanation of how and why the title was restructured, including a confirmation that the title length is between 75-80 characters."
}