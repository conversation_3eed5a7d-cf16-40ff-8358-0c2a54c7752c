{"model": "gpt-4o-mini", "system_message": "Aggressively generate a comprehensive list of item-specific keywords from the provided description, ensuring that no potential detail is overlooked.", "function_schema": {"name": "generate_custom_item_specifics", "description": "Generates a highly detailed list of item specifics from a provided item description. This function is designed to avoid missing any relevant details by aggressively interpreting and extracting both explicitly stated and contextually implied features and materials. It aims to capture every possible aspect that could be inferred from the item description to maximize keyword coverage.", "parameters": {"type": "object", "properties": {}, "required": []}}, "user_input": ""}