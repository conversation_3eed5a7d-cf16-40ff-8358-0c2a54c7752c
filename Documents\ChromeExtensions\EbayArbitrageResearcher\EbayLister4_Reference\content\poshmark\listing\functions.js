(function(_0x4fe291,_0x27128a){var _0x2cc9ac=a0_0x4b73,_0x3387e3=_0x4fe291();while(!![]){try{var _0x2e5b54=parseInt(_0x2cc9ac(0x14b))/0x1*(-parseInt(_0x2cc9ac(0x195))/0x2)+-parseInt(_0x2cc9ac(0x1b8))/0x3+-parseInt(_0x2cc9ac(0x101))/0x4+-parseInt(_0x2cc9ac(0x151))/0x5+-parseInt(_0x2cc9ac(0x130))/0x6*(parseInt(_0x2cc9ac(0x192))/0x7)+parseInt(_0x2cc9ac(0x105))/0x8*(-parseInt(_0x2cc9ac(0xd9))/0x9)+parseInt(_0x2cc9ac(0x16f))/0xa*(parseInt(_0x2cc9ac(0x13e))/0xb);if(_0x2e5b54===_0x27128a)break;else _0x3387e3['push'](_0x3387e3['shift']());}catch(_0x479095){_0x3387e3['push'](_0x3387e3['shift']());}}}(a0_0x5d7a,0xd9fc6));function simulateKeyboardEvent(_0x33265d){var _0x1e6d1d=a0_0x4b73;if(_0x33265d){_0x33265d['focus']();const _0x5a9a65={'bubbles':!![],'cancelable':!![]};[_0x1e6d1d(0x158),_0x1e6d1d(0xe3),_0x1e6d1d(0x146),'input',_0x1e6d1d(0x15b),_0x1e6d1d(0x126)][_0x1e6d1d(0x106)](_0x32397c=>{var _0x25bc56=_0x1e6d1d;const _0x18328e=new Event(_0x32397c,_0x5a9a65);_0x33265d[_0x25bc56(0x188)](_0x18328e);});}}function sleep(_0x3fc0f8){return new Promise(_0x4ba5c1=>setTimeout(_0x4ba5c1,_0x3fc0f8));}function waitForElement(_0x23d31d,_0x425959=0x7530){return new Promise((_0x7bff7,_0x4953e8)=>{let _0x48a1a5=0x0;const _0x341241=0x64,_0x694ef2=setInterval(()=>{var _0x331033=a0_0x4b73,_0x577315=document[_0x331033(0x12a)](_0x23d31d);_0x577315?(console[_0x331033(0x186)](_0x331033(0x17d)+_0x23d31d+_0x331033(0x15c)),clearInterval(_0x694ef2),_0x7bff7(_0x577315)):(console[_0x331033(0x186)]('Element\x20\x22'+_0x23d31d+_0x331033(0x167)),_0x48a1a5+=_0x341241,_0x48a1a5>=_0x425959&&(clearInterval(_0x694ef2),_0x4953e8(new Error(_0x331033(0x17d)+_0x23d31d+_0x331033(0x1a9)+_0x425959+_0x331033(0x119)))));},_0x341241);});}async function setTitle(_0x5dfcf5){var _0x58cf34=a0_0x4b73;const _0x3c6d4f=document[_0x58cf34(0x12a)](_0x58cf34(0x185));_0x5dfcf5[_0x58cf34(0x194)]>0x50&&(_0x5dfcf5=_0x5dfcf5[_0x58cf34(0x190)](0x0,0x50)),_0x3c6d4f&&(_0x3c6d4f[_0x58cf34(0x183)]=_0x5dfcf5,simulateKeyboardEvent(_0x3c6d4f));}async function setOriginalPrice(_0x2007c7){var _0xac3f8d=a0_0x4b73;const _0x14b7e9=document['querySelector'](_0xac3f8d(0x17f));_0x14b7e9&&(_0x14b7e9[_0xac3f8d(0x183)]=_0x2007c7,simulateKeyboardEvent(_0x14b7e9));}async function setListingPrice(_0x25dff1){var _0x1a332e=a0_0x4b73;const _0x1770a8=document[_0x1a332e(0x12a)](_0x1a332e(0x14a));_0x1770a8&&(_0x1770a8[_0x1a332e(0x183)]=_0x25dff1,simulateKeyboardEvent(_0x1770a8));}function a0_0x5d7a(){var _0x3493fb=['Images\x20Set','128vFgEpJ','forEach','Bags','[data-et-name=\x22next\x22]','local','[data-et-name]','Small\x20Pets','charCodeAt','Toys','autoSubmitEnabled','A\x20stylish\x20vintage\x20denim\x20jacket\x20perfect\x20for\x20any\x20occasion.','Pricing\x20section\x20not\x20found.','\x20\x20Amazon\x20words:\x20[','Vintage\x20Denim\x20Jacket','Fish','Category\x20Set','getURL','https://m.media-amazon.com/images/I/81+lQGoTwXL._AC_SY741_.jpg','No\x20match\x20found\x20for\x20subcategory,\x20returning\x20\x22Other\x22','submitButton\x20initiated','\x20ms','Office','Subcategory:','Art','split','.dropdown__menu--seller-shipping-discount','main_sd_images','title','Grooming','Hair','Accents','Pets','Bedding','blur','itemSpecifics','round','.dropdown__selector\x20span','querySelector','Discounted\x20shipping\x20set\x20to\x20free\x20successfully.','querySelectorAll','[aria-haspopup=\x22true\x22][data-test=\x22dropdown\x22]','map','\x20\x20\x20\x20Poshmark\x20words:\x20[','18GgUmfD','size','Video\x20Games\x20&\x20Consoles','parentElement','Accessories','descriptionAndFeatures','.listing-editor__section__title','Insert\x20Draft\x20Details','\x22\x20matches\x20\x22','images','Cat','classList','user_input','Women','35803196MgguKl','Cover\x20Shot\x20Selected','Jackets\x20&\x20Coats','Dresses','listingPrice','Attempt\x20','Listing\x20Price\x20Set','100','keyup','VR,\x20AR\x20&\x20Accessories','\x20\x20Against\x20Amazon\x20category:\x20\x22','Global\x20&\x20Traditional\x20Wear','[data-vv-name=\x22listingPrice\x22]','262555TVWyfW','Skincare','storage','Retrying\x20getMainCategory...','SKU\x20Set','required','235225NhwUtP','Pants','free','add','\x20seconds...','click','words','keydown','\x20\x20\x20\x20\x20\x20Match\x20found:\x20\x22','.dropdown__menu\x20.p--3\x20.d--fl\x20li','change','\x22\x20found.','Portable\x20Audio\x20&\x20Video','waiting_for_page_update','runtime','Underwear\x20&\x20Socks','No\x20match\x20found\x20for\x20main\x20category\x20in\x20title','now','Original\x20Price\x20Set','https://m.media-amazon.com/images/I/71HPPHSFGTL._AC_UL320_.jpg','innerText','\x22\x20not\x20found.','\x22\x20not\x20found.\x20Waiting...','\x20\x20\x20\x20Amazon\x20words:\x20[','includes','enum','jsonPrompt','.jpeg','Costumes','Error\x20in\x20populatePoshmarkWithDummyData:','10hDbjle','\x20failed:','Maximum\x20attempts\x20reached.\x20Unable\x20to\x20set\x20discounted\x20shipping\x20to\x20free.','Suits\x20&\x20Blazers','Skirts','trim','data','Holiday','Electronics','[data-vv-name=\x22sku\x22]','Best\x20word\x20match\x20found','Wearables','system_message','Kitchen','Element\x20\x22','Sample\x20Product\x20Title','[data-vv-name=\x22originalPrice\x22]','lastError','Attempting\x20to\x20map\x20to\x20poshmark\x20main\x20category\x20from\x20title','example_user_input','value','Wall\x20Decor','[data-vv-name=\x22title\x22]','log','main_hd_images','dispatchEvent','sizeNumbers','https://openai-function-call-djybcnnsgq-uc.a.run.app','[data-et-name=\x22listingEditorPricingSection\x22]','sku','files','[data-et-name=\x22list\x22]','Networking','substring','No\x20main\x20category\x20found','1862441vMrvyt','Form\x20populated\x20successfully.','length','2xUFEqj','\x20\x20\x20\x20\x20\x20Poshmark\x20words:\x20[','Intimates\x20&\x20Sleepwear','prompts_json/posh_mark_category_selector/get_main_category.json','Reptile','You\x20are\x20a\x20product\x20categorization\x20system.\x20Given\x20a\x20product\x20title\x20and\x20a\x20main\x20category,\x20determine\x20the\x20subcategory\x20from\x20the\x20provided\x20list\x20that\x20best\x20fits\x20the\x20item.\x0a\x0aMain\x20Category:\x20','Party\x20Supplies','Jeans','contains','Shirts\x20&\x20Tops','filter','items','categories','match','Discounted\x20Shipping\x20dropdown\x20not\x20found.','Tablets\x20&\x20Accessories','One\x20Pieces','firstChild','Main\x20category\x20\x22','Sweaters','\x22\x20not\x20found\x20within\x20','image/jpeg','toLowerCase','Cell\x20Phones\x20&\x20Accessories','.tc--dg','parse','element','sendMessage','Error','No\x20shipping\x20options\x20found.','Size\x20not\x20found,\x20selecting\x20first\x20option','Description\x20Set','Shoes','[data-vv-name=\x22description\x22]','Other','845472zYFXSk','button','Best\x20numeric\x20match\x20found','fetchData','Pants\x20&\x20Jumpsuits','Shorts','Free\x20shipping\x20option\x20not\x20found.','Tops','Subcategory\x20\x22','Kids','449280hqhgYs','then','Bird','abs','Men','Bottoms','slice','Retrying\x20getSubcategory...','Error\x20in\x20categorizing\x20product\x20after\x203\x20attempts:','Swim','keypress','\x22\x20matches\x20Poshmark\x20main\x20category\x20\x22','Bath,\x20Skin\x20&\x20Hair','Match\x20found:\x20\x22','Pajamas','Car\x20Audio,\x20Video\x20&\x20GPS','input[type=\x22file\x22]','.form__error-message','getAttribute','Jewelry','join','No\x20match\x20found\x20for\x20main\x20category','Checking\x20Poshmark\x20main\x20category:\x20\x22','li.dropdown__link','Matching\x20Sets','Dining','error','Design','sizeElement','Title\x20words:','output','Home','listing_result','numbers','Attempting\x20to\x20map\x20to\x20poshmark\x20subcategory','custom_title','Checking\x20Amazon\x20category:\x20\x22','Size\x20is\x20empty,\x20selecting\x20first\x20option','Sample\x20Subcategory','Does\x20not\x20contain\x20error\x20message','694532BRigcT','json','get'];a0_0x5d7a=function(){return _0x3493fb;};return a0_0x5d7a();}async function waitForDiscountedShippingToBeEnabled(_0x5dcb4e=0x1388){var _0x4aea84=a0_0x4b73;const _0x5ee0ec=Date['now'](),_0x563f31=document['querySelector'](_0x4aea84(0x18b));if(!_0x563f31)throw new Error(_0x4aea84(0x110));const _0x585994=_0x4aea84(0x12d),_0x543985=_0x563f31[_0x4aea84(0x12a)](_0x585994);if(!_0x543985)throw new Error(_0x4aea84(0x1a3));while(Date[_0x4aea84(0x162)]()-_0x5ee0ec<_0x5dcb4e){const _0x51a7c2=_0x543985[_0x4aea84(0xeb)](_0x4aea84(0x1a0));if(_0x51a7c2&&_0x51a7c2[_0x4aea84(0x174)]()!=='')return;await sleep(0xc8);}throw new Error('Discounted\x20shipping\x20dropdown\x20did\x20not\x20become\x20enabled\x20within\x20the\x20expected\x20time.');}async function setDiscountedShippingFree(){var _0x12f787=a0_0x4b73;const _0xb5a411=0x3,_0x3dfce0=0x7d0;let _0x3d77aa=0x0,_0x3dcb21=![];while(_0x3d77aa<_0xb5a411&&!_0x3dcb21){try{const _0x3d5665=document[_0x12f787(0x12a)](_0x12f787(0x18b));if(!_0x3d5665)throw new Error(_0x12f787(0x110));const _0x4d67fc=_0x12f787(0x12d),_0xcba8e6=_0x3d5665[_0x12f787(0x12a)](_0x4d67fc);if(!_0xcba8e6)throw new Error(_0x12f787(0x1a3));_0xcba8e6['firstChild']['firstChild'][_0x12f787(0x156)](),console['log'](_0x12f787(0x143)+(_0x3d77aa+0x1)+':\x20Clicked\x20on\x20the\x20discounted\x20shipping\x20dropdown.'),await waitForElement(_0x12f787(0x11e),0x1388);const _0x1a5b84=document[_0x12f787(0x12c)]('.dropdown__menu--seller-shipping-discount\x20li');if(_0x1a5b84[_0x12f787(0x194)]===0x0)throw new Error(_0x12f787(0x1b2));let _0x3c69c1=null;for(let _0x18fa3b of _0x1a5b84){const _0x2a665a=_0x18fa3b[_0x12f787(0x165)][_0x12f787(0x174)]()[_0x12f787(0x1ab)]();if(_0x2a665a[_0x12f787(0x169)](_0x12f787(0x153))){_0x3c69c1=_0x18fa3b;break;}}if(!_0x3c69c1)throw new Error(_0x12f787(0x1be));_0x3c69c1[_0x12f787(0x1a6)][_0x12f787(0x156)](),await sleep(0x1f4);var _0x47a0ac=_0x3d5665[_0x12f787(0x12a)](_0x12f787(0x1ad))[_0x12f787(0x165)][_0x12f787(0x174)]();if(_0x47a0ac[_0x12f787(0x1ab)]()[_0x12f787(0x169)]('free'))console[_0x12f787(0x186)](_0x12f787(0x12b)),_0x3dcb21=!![];else throw new Error('Failed\x20to\x20set\x20discounted\x20shipping\x20to\x20free.');}catch(_0x1d7c70){console[_0x12f787(0xf3)](_0x12f787(0x143)+(_0x3d77aa+0x1)+_0x12f787(0x170),_0x1d7c70),_0x3d77aa++,_0x3d77aa<_0xb5a411?(console['log']('Retrying\x20in\x20'+_0x3dfce0/0x3e8+_0x12f787(0x155)),await sleep(_0x3dfce0)):console[_0x12f787(0xf3)](_0x12f787(0x171));}}}async function setDescription(_0x24699e){var _0x35a8c7=a0_0x4b73;const _0x748a54=document[_0x35a8c7(0x12a)](_0x35a8c7(0x1b6));_0x24699e[_0x35a8c7(0x194)]>0x5dc&&(_0x24699e=_0x24699e[_0x35a8c7(0x190)](0x0,0x5dc)),_0x748a54&&(_0x748a54[_0x35a8c7(0x183)]=_0x24699e,simulateKeyboardEvent(_0x748a54));}async function setImages(_0x227d59){var _0x45d7c7=a0_0x4b73;const _0x25551a=document['querySelector'](_0x45d7c7(0xe9));if(_0x25551a&&_0x227d59[_0x45d7c7(0x194)]>0x0){const _0x584a3e=new DataTransfer();for(let _0x4b4991=0x0;_0x4b4991<_0x227d59[_0x45d7c7(0x194)]&&_0x4b4991<0x10;_0x4b4991++){var _0x245fcd=await urlToImage(_0x227d59[_0x4b4991]),_0x4e4da9=_0x245fcd['src'];const _0x479d3d=base64ToFile(_0x4e4da9,'image'+_0x4b4991+_0x45d7c7(0x16c));_0x584a3e[_0x45d7c7(0x1a0)][_0x45d7c7(0x154)](_0x479d3d);}_0x25551a[_0x45d7c7(0x18d)]=_0x584a3e['files'],_0x25551a[_0x45d7c7(0x188)](new Event('change',{'bubbles':!![]}));}}function base64ToFile(_0x3e0714,_0x56cc72){var _0x22a477=a0_0x4b73;const _0x30dae2=_0x3e0714[_0x22a477(0x11d)](','),_0x48df3=_0x30dae2[0x0]['match'](/:(.*?);/),_0x25ebb7=_0x48df3?_0x48df3[0x1]:_0x22a477(0x1aa),_0x375b25=atob(_0x30dae2[0x1]);let _0x18e19e=_0x375b25['length'];const _0x1c4651=new Uint8Array(_0x18e19e);while(_0x18e19e--){_0x1c4651[_0x18e19e]=_0x375b25[_0x22a477(0x10c)](_0x18e19e);}return new File([_0x1c4651],_0x56cc72,{'type':_0x25ebb7});}async function setCategory(_0x4b8803,_0x27ad04){var _0x382a2c=a0_0x4b73;const _0x3b7524=document[_0x382a2c(0x12a)](_0x382a2c(0x129));if(_0x3b7524){_0x3b7524['click'](),await sleep(0xc8);const _0x59a902=document[_0x382a2c(0x12c)](_0x382a2c(0x10a));let _0x1d705f=null;_0x59a902['forEach'](_0x34b89c=>{var _0x1e767a=_0x382a2c;_0x34b89c[_0x1e767a(0x165)]['trim']()[_0x1e767a(0x1ab)]()===_0x4b8803[_0x1e767a(0x174)]()['toLowerCase']()&&(_0x1d705f=_0x34b89c);});if(_0x1d705f){_0x1d705f[_0x382a2c(0x156)](),await sleep(0xc8);const _0x620f79=document['querySelectorAll'](_0x382a2c(0xf0));let _0x26c136=null;_0x620f79[_0x382a2c(0x106)](_0x147dd3=>{var _0x5f3533=_0x382a2c;_0x147dd3[_0x5f3533(0x165)][_0x5f3533(0x174)]()[_0x5f3533(0x1ab)]()===_0x27ad04[_0x5f3533(0x174)]()[_0x5f3533(0x1ab)]()&&(_0x26c136=_0x147dd3);}),_0x26c136?(_0x26c136[_0x382a2c(0x156)](),await sleep(0xc8)):console[_0x382a2c(0xf3)](_0x382a2c(0xd7)+_0x27ad04+_0x382a2c(0x166));}else console['error'](_0x382a2c(0x1a7)+_0x4b8803+_0x382a2c(0x166));}else console['error']('Category\x20dropdown\x20not\x20found.');}async function populatePoshmarkWithDummyData(){var _0x23f09a=a0_0x4b73;try{const _0x143b81={'name':_0x23f09a(0x112),'description':_0x23f09a(0x10f),'originalPrice':_0x23f09a(0x145),'listingPrice':'49','images':[_0x23f09a(0x164),'https://m.media-amazon.com/images/I/91kXaPqVK5L._AC_SY741_.jpg',_0x23f09a(0x116)]};await setTitle(_0x143b81['name']),await setOriginalPrice(_0x143b81['originalPrice']),await setListingPrice(_0x143b81[_0x23f09a(0x142)]),await setDescription(_0x143b81['description']),await setImages(_0x143b81[_0x23f09a(0x139)]),await setCategory(_0x23f09a(0x177),_0x23f09a(0x1b7)),console[_0x23f09a(0x186)](_0x23f09a(0x193));}catch(_0x140dd0){console[_0x23f09a(0xf3)](_0x23f09a(0x16e),_0x140dd0);}}async function getMainCategory(_0x176a1d){var _0x486ff9=a0_0x4b73,_0x4dd2fe=await fetch(chrome[_0x486ff9(0x15f)]['getURL'](_0x486ff9(0x198)))[_0x486ff9(0xda)](_0x1fbc1f=>_0x1fbc1f[_0x486ff9(0x102)]());_0x4dd2fe[_0x486ff9(0x13c)]=_0x176a1d,console['log'](_0x486ff9(0x16b),_0x4dd2fe);var _0x31c6c4=await new Promise((_0x1c7679,_0x60d93b)=>{var _0x5b9525=_0x486ff9;chrome['runtime'][_0x5b9525(0x1b0)]({'type':_0x5b9525(0x1bb),'url':_0x5b9525(0x18a),'data':_0x4dd2fe},function(_0x6d5e27){var _0x43d377=_0x5b9525;chrome['runtime'][_0x43d377(0x180)]?_0x60d93b(chrome[_0x43d377(0x15f)][_0x43d377(0x180)]):_0x1c7679(_0x6d5e27[_0x43d377(0x175)]);});});console[_0x486ff9(0x186)]('data',_0x31c6c4),_0x31c6c4=JSON[_0x486ff9(0x1ae)](_0x31c6c4);var _0x263f74=_0x31c6c4[_0x486ff9(0xf7)];const _0x57bf7e=[_0x486ff9(0x13d),'Men','Kids',_0x486ff9(0xf8),_0x486ff9(0x124),_0x486ff9(0x177)];return!_0x57bf7e[_0x486ff9(0x169)](_0x263f74)&&(_0x263f74=_0x486ff9(0x1b7)),_0x263f74;}async function getSubcategory(_0x201eb8,_0x364863){var _0x107286=a0_0x4b73,_0x8e68e8=await fetch(chrome['runtime'][_0x107286(0x115)]('prompts_json/posh_mark_category_selector/get_sub_category.json'))['then'](_0x4152a2=>_0x4152a2[_0x107286(0x102)]()),_0x531bc3=getSubcategoriesForMainCategory(_0x364863);_0x8e68e8[_0x107286(0x17b)]=_0x107286(0x19a)+_0x364863+'.\x0aSubcategories:\x20'+_0x531bc3['join'](',\x20')+'.',_0x8e68e8['function_schema']['parameters']['properties'][_0x107286(0xf7)][_0x107286(0x16a)]=_0x531bc3,_0x8e68e8[_0x107286(0x13c)]=_0x201eb8,_0x8e68e8[_0x107286(0x182)]=_0x107286(0x17e),_0x8e68e8['example_assistant_output_arguments']={'output':_0x107286(0xff)},console[_0x107286(0x186)](_0x107286(0x16b),_0x8e68e8);var _0x594220=await new Promise((_0x7cd94a,_0xdf1841)=>{var _0x45520a=_0x107286;chrome['runtime'][_0x45520a(0x1b0)]({'type':'fetchData','url':_0x45520a(0x18a),'data':_0x8e68e8},function(_0x1c5c83){var _0x106dda=_0x45520a;chrome[_0x106dda(0x15f)][_0x106dda(0x180)]?_0xdf1841(chrome[_0x106dda(0x15f)]['lastError']):_0x7cd94a(_0x1c5c83[_0x106dda(0x175)]);});});console[_0x107286(0x186)](_0x107286(0x175),_0x594220),_0x594220=JSON[_0x107286(0x1ae)](_0x594220);var _0x9fbc8b=_0x594220[_0x107286(0xf7)];return!_0x531bc3[_0x107286(0x169)](_0x9fbc8b)&&(_0x9fbc8b=_0x107286(0x1b7)),_0x9fbc8b;}function getSubcategoriesForMainCategory(_0x39c0b4){var _0x525eff=a0_0x4b73;const _0x59fb02={'Women':[_0x525eff(0x134),_0x525eff(0x107),_0x525eff(0x141),_0x525eff(0x197),_0x525eff(0x140),_0x525eff(0x19c),_0x525eff(0xec),'Makeup',_0x525eff(0x1bc),_0x525eff(0x1b5),'Shorts',_0x525eff(0x173),'Sweaters','Swim',_0x525eff(0x1bf),_0x525eff(0x14c),_0x525eff(0x122),'Bath\x20&\x20Body',_0x525eff(0x149),_0x525eff(0x1b7)],'Men':['Accessories','Bags',_0x525eff(0x140),_0x525eff(0x19c),_0x525eff(0x152),'Shirts',_0x525eff(0x1b5),_0x525eff(0x1bd),_0x525eff(0x172),_0x525eff(0x1a8),_0x525eff(0xe2),_0x525eff(0x160),_0x525eff(0x121),_0x525eff(0x149),_0x525eff(0x1b7)],'Kids':[_0x525eff(0x134),_0x525eff(0xde),'Dresses',_0x525eff(0x140),_0x525eff(0xf1),_0x525eff(0x1a5),_0x525eff(0xe7),_0x525eff(0x19e),_0x525eff(0x1b5),_0x525eff(0xe2),_0x525eff(0x16d),_0x525eff(0xe5),_0x525eff(0x10d),'Other'],'Home':[_0x525eff(0x123),_0x525eff(0x11c),'Bath',_0x525eff(0x125),_0x525eff(0xf4),_0x525eff(0xf2),'Games',_0x525eff(0x176),_0x525eff(0x17c),_0x525eff(0x11a),_0x525eff(0x19b),'Storage\x20&\x20Organization',_0x525eff(0x184),_0x525eff(0x1b7)],'Pets':['Dog',_0x525eff(0x13a),_0x525eff(0xdb),_0x525eff(0x113),_0x525eff(0x199),_0x525eff(0x10b),_0x525eff(0x1b7)],'Electronics':['Cameras,\x20Photo\x20&\x20Video','Computers,\x20Laptops\x20&\x20Parts',_0x525eff(0x1ac),_0x525eff(0xe8),_0x525eff(0x17a),_0x525eff(0x1a4),_0x525eff(0x132),_0x525eff(0x147),'Media',_0x525eff(0x18f),'Headphones',_0x525eff(0x15d),'Other']};return _0x59fb02[_0x39c0b4]||['Other'];}async function categorizeProduct(_0x336531){var _0x38a0fa=a0_0x4b73;let _0x285170=0x0;const _0x2b96cb=0x3;let _0x15a89b,_0xfec8d5;while(_0x285170<_0x2b96cb){try{return!_0x15a89b&&(_0x15a89b=await getMainCategory(_0x336531),console['log']('Main\x20Category:',_0x15a89b)),_0xfec8d5=await getSubcategory(_0x336531,_0x15a89b),console[_0x38a0fa(0x186)](_0x38a0fa(0x11b),_0xfec8d5),{'mainCategory':_0x15a89b,'subcategory':_0xfec8d5};}catch(_0x3a66ad){_0x285170++,console[_0x38a0fa(0xf3)]('Attempt\x20'+_0x285170+_0x38a0fa(0x170),_0x3a66ad);if(_0x285170>=_0x2b96cb){console[_0x38a0fa(0xf3)](_0x38a0fa(0xe1),_0x3a66ad);throw _0x3a66ad;}!_0x15a89b?console[_0x38a0fa(0x186)](_0x38a0fa(0x14e)):console[_0x38a0fa(0x186)](_0x38a0fa(0xe0));}}}function mapToPoshmarkMainCategoryFromTitle(_0x4c1415){var _0x174b59=a0_0x4b73,_0x353062=[_0x174b59(0x13d),_0x174b59(0xdd),_0x174b59(0xd8),_0x174b59(0xf8),_0x174b59(0x124),_0x174b59(0x177)],_0xf1a004=_0x353062[_0x174b59(0x12e)](_0x302b22=>_0x302b22['toLowerCase']()),_0x4a2f61=_0x4c1415[_0x174b59(0x11d)](/\W+/)[_0x174b59(0x19f)](Boolean)[_0x174b59(0x12e)](_0x2286c8=>_0x2286c8['toLowerCase']());console[_0x174b59(0x186)](_0x174b59(0xf6),_0x4a2f61);for(let _0x55fac8=0x0;_0x55fac8<_0x4a2f61['length'];_0x55fac8++){var _0x2fa502=_0x4a2f61[_0x55fac8],_0x390475=_0x2fa502['endsWith']('s')?_0x2fa502[_0x174b59(0xdf)](0x0,-0x1):_0x2fa502;for(let _0xa34062=0x0;_0xa34062<_0xf1a004[_0x174b59(0x194)];_0xa34062++){var _0x120052=_0xf1a004[_0xa34062];if(_0x2fa502===_0x120052||_0x390475===_0x120052)return console[_0x174b59(0x186)](_0x174b59(0xe6)+_0x4a2f61[_0x55fac8]+_0x174b59(0xe4)+_0x353062[_0xa34062]+'\x22'),_0x353062[_0xa34062];}}return console[_0x174b59(0x186)](_0x174b59(0x161)),null;}function mapToPoshmarkMainCategory(_0x550bcb){var _0x432e1e=a0_0x4b73,_0x24eea7=['Women',_0x432e1e(0xdd),'Kids',_0x432e1e(0xf8),'Pets',_0x432e1e(0x177)];for(let _0x10edb6=0x0;_0x10edb6<_0x24eea7['length'];_0x10edb6++){const _0x36be00=_0x24eea7[_0x10edb6][_0x432e1e(0x1ab)]();console[_0x432e1e(0x186)](_0x432e1e(0xef)+_0x24eea7[_0x10edb6]+'\x22');for(let _0x2b686=0x0;_0x2b686<_0x550bcb[_0x432e1e(0x194)];_0x2b686++){const _0x52f40a=_0x550bcb[_0x2b686][_0x432e1e(0x1ab)]();console[_0x432e1e(0x186)](_0x432e1e(0x148)+_0x550bcb[_0x2b686]+'\x22');const _0x504664=_0x52f40a[_0x432e1e(0x11d)](/\W+/)[_0x432e1e(0x19f)](Boolean),_0x731b23=_0x36be00['split'](/\W+/)[_0x432e1e(0x19f)](Boolean);console[_0x432e1e(0x186)](_0x432e1e(0x168)+_0x504664[_0x432e1e(0xed)](',\x20')+']'),console[_0x432e1e(0x186)](_0x432e1e(0x12f)+_0x731b23[_0x432e1e(0xed)](',\x20')+']');for(let _0x2ebba0=0x0;_0x2ebba0<_0x731b23[_0x432e1e(0x194)];_0x2ebba0++){const _0x3b6ed0=_0x731b23[_0x2ebba0];for(let _0x2216e8=0x0;_0x2216e8<_0x504664[_0x432e1e(0x194)];_0x2216e8++){const _0x59a607=_0x504664[_0x2216e8];if(_0x59a607===_0x3b6ed0)return console[_0x432e1e(0x186)](_0x432e1e(0x159)+_0x59a607+_0x432e1e(0x138)+_0x3b6ed0+'\x22'),_0x24eea7[_0x10edb6];}}}}return console[_0x432e1e(0x186)](_0x432e1e(0xee)),null;}function mapToPoshmarkSubCategory(_0x5a468a,_0xdeaadf){var _0x2f14a6=a0_0x4b73;for(let _0x30a1f2=_0x5a468a[_0x2f14a6(0x194)]-0x1;_0x30a1f2>=0x0;_0x30a1f2--){const _0xe80fba=_0x5a468a[_0x30a1f2][_0x2f14a6(0x1ab)]();console['log'](_0x2f14a6(0xfd)+_0x5a468a[_0x30a1f2]+'\x22');const _0x3f35c1=_0xe80fba[_0x2f14a6(0x11d)](/\W+/)[_0x2f14a6(0x19f)](Boolean);console[_0x2f14a6(0x186)](_0x2f14a6(0x111)+_0x3f35c1[_0x2f14a6(0xed)](',\x20')+']');for(let _0x3c6f72=0x0;_0x3c6f72<_0xdeaadf['length'];_0x3c6f72++){const _0x43a0e2=_0xdeaadf[_0x3c6f72];console[_0x2f14a6(0x186)]('\x20\x20\x20\x20Checking\x20Poshmark\x20subcategory:\x20\x22'+_0x43a0e2+'\x22');const _0x4d9803=_0x43a0e2['toLowerCase']()[_0x2f14a6(0x11d)](/\W+/)[_0x2f14a6(0x19f)](Boolean);console['log'](_0x2f14a6(0x196)+_0x4d9803[_0x2f14a6(0xed)](',\x20')+']');for(let _0x5b9163=0x0;_0x5b9163<_0x4d9803[_0x2f14a6(0x194)];_0x5b9163++){const _0x12fa24=_0x4d9803[_0x5b9163];for(let _0x4503b5=0x0;_0x4503b5<_0x3f35c1[_0x2f14a6(0x194)];_0x4503b5++){const _0x2951da=_0x3f35c1[_0x4503b5];if(_0x2951da===_0x12fa24)return console[_0x2f14a6(0x186)]('\x20\x20\x20\x20\x20\x20\x20\x20Match\x20found:\x20\x22'+_0x2951da+'\x22\x20matches\x20\x22'+_0x12fa24+'\x22'),_0xdeaadf[_0x3c6f72];}}}}return console[_0x2f14a6(0x186)](_0x2f14a6(0x117)),'Other';}async function attemptToMapToPoshmarkSubCategory(_0x415f7f){var _0x1c1359=mapToPoshmarkMainCategory(_0x415f7f),_0x376091=null;if(_0x1c1359){var _0x331cf2=getSubcategoriesForMainCategory(_0x1c1359);_0x376091=mapToPoshmarkSubCategory(product['categories'],_0x331cf2);}else return null;return{'mainCategory':_0x1c1359,'subcategory':_0x376091};}async function insertDraftDetails(_0x4e2c3e){var _0x1a6fda=a0_0x4b73;document[_0x1a6fda(0x120)]=_0x1a6fda(0x137);var _0x34ec6b,_0x534c4e;if(!_0x34ec6b){console[_0x1a6fda(0x186)](_0x1a6fda(0xfb));try{var {mainCategory:_0x34ec6b,subcategory:_0x534c4e}=await attemptToMapToPoshmarkSubCategory(_0x4e2c3e[_0x1a6fda(0x1a1)]);}catch(_0x434ebd){console[_0x1a6fda(0x186)](_0x1a6fda(0x1b1),_0x434ebd);}}if(!_0x34ec6b&&_0x4e2c3e['categories'][_0x1a6fda(0x194)]>0x0){console['log'](_0x1a6fda(0x181)),_0x34ec6b=mapToPoshmarkMainCategoryFromTitle(_0x4e2c3e['custom_title']);if(_0x34ec6b){var _0x4668e0=getSubcategoriesForMainCategory(_0x34ec6b);_0x534c4e=mapToPoshmarkSubCategory(_0x4e2c3e[_0x1a6fda(0x1a1)],_0x4668e0);}}!_0x34ec6b&&(_0x34ec6b=mapToPoshmarkMainCategoryFromTitle(_0x4e2c3e[_0x1a6fda(0xfc)]),_0x534c4e=_0x1a6fda(0x1b7));!_0x34ec6b&&(console[_0x1a6fda(0x186)](_0x1a6fda(0x191)),_0x34ec6b=_0x1a6fda(0xf8),_0x534c4e=_0x1a6fda(0x1b7));await setCategory(_0x34ec6b,_0x534c4e),document[_0x1a6fda(0x120)]=_0x1a6fda(0x114),await setTitle(_0x4e2c3e[_0x1a6fda(0xfc)]),document[_0x1a6fda(0x120)]='Title\x20Set',await setDescription(_0x4e2c3e[_0x1a6fda(0x135)]),document['title']=_0x1a6fda(0x1b4);var _0x200f17=[],_0x44d319=_0x4e2c3e[_0x1a6fda(0x187)],_0x535dca=_0x4e2c3e[_0x1a6fda(0x11f)];_0x200f17=_0x44d319;_0x44d319[_0x1a6fda(0x194)]<0x2&&_0x535dca[_0x1a6fda(0x194)]>0x2?_0x200f17=_0x535dca:_0x200f17=_0x44d319;await setImages(_0x200f17),document[_0x1a6fda(0x120)]=_0x1a6fda(0x104),await setSku(btoa(_0x4e2c3e[_0x1a6fda(0x18c)])),document[_0x1a6fda(0x120)]=_0x1a6fda(0x14f);var _0x5dcaf6=Number(_0x4e2c3e['custom_price']);_0x5dcaf6=Math[_0x1a6fda(0x128)](_0x5dcaf6),await setListingPrice(_0x5dcaf6),document['title']=_0x1a6fda(0x144),await setOriginalPrice(_0x5dcaf6*1.5),document['title']=_0x1a6fda(0x163),await selectCoverShot(0x1388),document[_0x1a6fda(0x120)]=_0x1a6fda(0x13f);var _0x4eaac5;try{for(let _0x4e5468=0x0;_0x4e5468<_0x4e2c3e[_0x1a6fda(0x127)]['length'];_0x4e5468++){const _0x513668=_0x4e2c3e[_0x1a6fda(0x127)][_0x4e5468];if(_0x513668['label'][_0x1a6fda(0x1ab)]()=='size'){_0x4eaac5=_0x513668[_0x1a6fda(0x183)];break;}}}catch(_0x39b698){}console[_0x1a6fda(0x186)](_0x1a6fda(0x131),_0x4eaac5),await selectSizeIfRequired(_0x4eaac5),addSubmitButtonListener(_0x4e2c3e);var {autoSubmitEnabled:_0x2faf25}=await chrome[_0x1a6fda(0x14d)][_0x1a6fda(0x109)][_0x1a6fda(0x103)](_0x1a6fda(0x10e));_0x2faf25&&(await listItem(),document[_0x1a6fda(0x120)]='Item\x20Listed');}async function selectCoverShot(_0x6f04a3=0x1388){var _0x336969=a0_0x4b73;const _0x4357ba=Date[_0x336969(0x162)](),_0x545c59=0x64;while(Date[_0x336969(0x162)]()-_0x4357ba<_0x6f04a3){var _0xf02c9=document[_0x336969(0x12a)]('.zoomout');_0xf02c9&&(_0xf02c9[_0x336969(0x156)](),await sleep(0x64),_0xf02c9[_0x336969(0x156)](),await sleep(0x64),_0xf02c9[_0x336969(0x156)](),await sleep(0x64));const _0x474cb4=document[_0x336969(0x12a)]('[data-et-on-name=\x22select_first_photo\x22]');if(_0x474cb4){_0x474cb4[_0x336969(0x156)]();return;}await sleep(_0x545c59);}console[_0x336969(0xf3)]('Cover\x20shot\x20button\x20not\x20found\x20within\x20the\x20timeout\x20period.');}async function setSku(_0x488cc7){var _0x13dda6=a0_0x4b73,_0x22df3c=document[_0x13dda6(0x12a)](_0x13dda6(0x178));_0x22df3c&&(_0x22df3c[_0x13dda6(0x183)]=_0x488cc7,simulateKeyboardEvent(_0x22df3c));}function a0_0x4b73(_0x543ee4,_0x45a9c8){var _0x5d7ab7=a0_0x5d7a();return a0_0x4b73=function(_0x4b7394,_0x44f25d){_0x4b7394=_0x4b7394-0xd7;var _0x13a5d4=_0x5d7ab7[_0x4b7394];return _0x13a5d4;},a0_0x4b73(_0x543ee4,_0x45a9c8);}function getSubmitButton(){var _0x27bd51=a0_0x4b73,_0x1772f3=document[_0x27bd51(0x12a)](_0x27bd51(0x108));return _0x1772f3;}async function listItem(){var _0x26ebd0=a0_0x4b73,_0x8e2d16=getSubmitButton();_0x8e2d16&&_0x8e2d16[_0x26ebd0(0x156)]();var _0x182743=await waitForElement(_0x26ebd0(0x18e),0x7530);_0x182743&&_0x182743[_0x26ebd0(0x156)]();}async function selectSizeIfRequired(_0x54fa42=null){var _0x429218=a0_0x4b73,_0xbbfbe6=document[_0x429218(0x12c)](_0x429218(0x136)),_0x1f8daa=null;for(let _0x13efb0=0x0;_0x13efb0<_0xbbfbe6['length'];_0x13efb0++){var _0x5a8b35=_0xbbfbe6[_0x13efb0];if(_0x5a8b35&&_0x5a8b35[_0x429218(0x165)][_0x429218(0x1ab)]()[_0x429218(0x169)](_0x429218(0x131))){_0x1f8daa=_0x5a8b35;break;}}console[_0x429218(0x186)](_0x429218(0xf5),_0x1f8daa);if(!_0x1f8daa)return;var _0x498947=_0x1f8daa[_0x429218(0x133)];while(_0x498947&&!_0x498947[_0x429218(0x13b)][_0x429218(0x19d)]('listing-editor__subsection')){_0x498947=_0x498947[_0x429218(0x133)];}var _0x202918=_0x498947[_0x429218(0x12a)](_0x429218(0xea));if(_0x202918&&_0x202918[_0x429218(0x165)]['toLowerCase']()[_0x429218(0x169)](_0x429218(0x150)))console[_0x429218(0x186)]('Contains\x20error\x20message');else{console['log'](_0x429218(0x100));return;}var _0x1ef732=_0x498947[_0x429218(0x12a)]('.dropdown__selector--select-tag');_0x1ef732[_0x429218(0x156)](),await sleep(0x1f4);var _0x45b99d=_0x498947[_0x429218(0x12c)](_0x429218(0x15a));if(!_0x54fa42){console[_0x429218(0x186)](_0x429218(0xfe)),_0x45b99d[0x0]['querySelector'](_0x429218(0x1b9))['click']();return;}function _0x4afb0c(_0x24281a){var _0x35487f=_0x429218,_0x233677=/\d+(\.\d+)?/g,_0x1209fc=_0x24281a[_0x35487f(0x1a2)](_0x233677);return _0x1209fc?_0x1209fc[_0x35487f(0x12e)](Number):[];}function _0x2a9007(_0x372f1c){var _0x468005=_0x429218,_0x3e5e16=/\b[A-Za-z]+\b/g,_0x598e32=_0x372f1c[_0x468005(0x1a2)](_0x3e5e16);return _0x598e32?_0x598e32[_0x468005(0x12e)](_0x1ade79=>_0x1ade79[_0x468005(0x1ab)]()):[];}var _0x192c71=_0x4afb0c(_0x54fa42),_0x2365db=_0x2a9007(_0x54fa42);console[_0x429218(0x186)](_0x429218(0x189),_0x192c71),console[_0x429218(0x186)]('sizeWords',_0x2365db);var _0x1c7692=[];for(let _0x224fb2=0x0;_0x224fb2<_0x45b99d[_0x429218(0x194)];_0x224fb2++){var _0x2c1b51=_0x45b99d[_0x224fb2],_0x215bea=_0x2c1b51['innerText'],_0x6e24c4=_0x4afb0c(_0x215bea),_0x5cf5b5=_0x2a9007(_0x215bea);_0x1c7692['push']({'element':_0x2c1b51,'numbers':_0x6e24c4,'words':_0x5cf5b5});}var _0x26f1a9=null,_0x457f72=Infinity;if(_0x192c71[_0x429218(0x194)]>0x0){for(let _0x2ad0bb=0x0;_0x2ad0bb<_0x1c7692[_0x429218(0x194)];_0x2ad0bb++){var _0x449d5b=_0x1c7692[_0x2ad0bb];if(_0x449d5b[_0x429218(0xfa)][_0x429218(0x194)]>0x0)for(let _0xa6c49b=0x0;_0xa6c49b<_0x449d5b[_0x429218(0xfa)][_0x429218(0x194)];_0xa6c49b++){var _0x157c4f=_0x449d5b[_0x429218(0xfa)][_0xa6c49b];for(let _0xf7de2=0x0;_0xf7de2<_0x192c71[_0x429218(0x194)];_0xf7de2++){var _0x1d8043=_0x192c71[_0xf7de2],_0x2b1179=Math[_0x429218(0xdc)](_0x157c4f-_0x1d8043);_0x2b1179<_0x457f72&&(_0x457f72=_0x2b1179,_0x26f1a9=_0x449d5b[_0x429218(0x1af)]);}}}if(_0x26f1a9){console['log'](_0x429218(0x1ba)),_0x26f1a9[_0x429218(0x12a)](_0x429218(0x1b9))[_0x429218(0x156)]();return;}}if(_0x2365db['length']>0x0){var _0x24ae8f=0x0;for(let _0x5be2d4=0x0;_0x5be2d4<_0x1c7692['length'];_0x5be2d4++){var _0x449d5b=_0x1c7692[_0x5be2d4],_0x23c61c=0x0;for(let _0x37e8d8=0x0;_0x37e8d8<_0x449d5b[_0x429218(0x157)]['length'];_0x37e8d8++){var _0x4c0327=_0x449d5b[_0x429218(0x157)][_0x37e8d8];_0x2365db[_0x429218(0x169)](_0x4c0327)&&_0x23c61c++;}_0x23c61c>_0x24ae8f&&(_0x24ae8f=_0x23c61c,_0x26f1a9=_0x449d5b[_0x429218(0x1af)]);}if(_0x26f1a9){console[_0x429218(0x186)](_0x429218(0x179)),_0x26f1a9[_0x429218(0x12a)](_0x429218(0x1b9))[_0x429218(0x156)]();return;}}console[_0x429218(0x186)](_0x429218(0x1b3)),_0x45b99d[0x0][_0x429218(0x12a)](_0x429218(0x1b9))['click']();}function addSubmitButtonListener(_0xa9a06d){var _0x37216d=a0_0x4b73,_0x4b1b47=getSubmitButton();console['log'](_0x37216d(0x118),_0x4b1b47),_0x4b1b47['addEventListener'](_0x37216d(0x156),async function(){var _0x499e47=_0x37216d;addAsinToStorage(_0xa9a06d[_0x499e47(0x18c)]),chrome[_0x499e47(0x15f)]['sendMessage']({'type':_0x499e47(0xf9),'status':_0x499e47(0x15e)});});}