

/* Quick Chat Button Settings */
.quick_chat_button_settings {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.quick_chat_button_settings label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.quick_chat_button_settings input[type="text"],
.quick_chat_button_settings input[type="color"],
.quick_chat_button_settings textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#quick_chat_button_color {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-bottom: 10px;
}

/* Buttons */
button {
    background-color: #6bb1fc;
    margin-right: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: 300;
}

button:hover {
    background-color: #0056b3;
}

#clear_quick_chat_button {
    background-color: #dc3545;
}

#clear_quick_chat_button:hover {
    background-color: #c82333;
}

#clear_all_quick_chat_buttons {
    background-color: #dc3545;
}

#create_quick_chat_button {
    background-color: #82e298;
}

#create_quick_chat_button:hover {
    background-color: #218838;
}

#save_quick_chat_buttons {
    background-color: #ffc107;
}

#save_quick_chat_buttons:hover {
    background-color: #e0a800;
}

#mass_import_quick_chat_messages {
    background-color: #e98afc;
}

#load_default_quick_chat_buttons {
    background-color: #ffc107;
}

/* Quick Chat Messages Container */
#quick_chat_messages_container {
    margin-bottom: 20px;
}

.quick_chat_message_container {
    margin-bottom: 10px;
}


/* Additional Styling */
.add_quick_chat_message_and_mass_import_quick_chat_messages_container,
.create_and_clear_quick_chat_button_container,
.save_and_export_quick_chat_buttons_container {
    margin-bottom: 20px;
}

/* Container for each quick chat button */
.quick_chat_button_container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    max-width: 35%;
}

/* Styling for each quick chat button */
.quick_chat_button {
    flex-grow: 1; /* Allows the button to grow and fill available space */
    margin-right: 10px; /* Adds some space between the button and the action buttons */
}

/* Styling for the remove and edit buttons */
.remove_quick_chat_button, .remove_quick_chat_message{
    /* Additional styling if needed */
    background-color: #dc3545;
}

/* Optional: Additional Responsive Adjustments */
@media (max-width: 600px) {
    .quick_chat_button_container {
        flex-direction: column;
        align-items: stretch;
    }

    .quick_chat_button,
    .remove_quick_chat_button,
    .edit_quick_chat_button {
        width: 100%;
        margin-bottom: 5px;
    }
}


.quick_chat_switch_container {
    display: flex;
    align-items: center;
  }
  
  .quick_chat_switch_container label {
    margin-right: 10px;
  }
  
  .quick_chat_switch_container input[type="checkbox"] {
    width: 20px;
    height: 20px;
  }
  