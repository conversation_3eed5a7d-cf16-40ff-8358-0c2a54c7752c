<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eBay Search Results</title>
    <link rel="stylesheet" href="index.css">
</head>

<body>
    <h1>eBay Search Results</h1>
    <form>
        <label for="item1">Item 1:</label>
        <input type="text" id="item1" name="item1" value="354870928185"><br><br>
        <label for="item2">Item 2:</label>
        <input type="text" id="item2" name="item2" value="354871416035"><br><br>
        <label for="item3">Item 3:</label>
        <input type="text" id="item3" name="item3" value="354872973924"><br><br>
        <button id="evaluate_title" >Evaluate Title</button>
    </form>

    
    <div id="explanation">
        <h3>Explanation of terms</h3>
        <p><strong>TF (Term Frequency):</strong> This represents how frequently a term occurs in a document. If a term occurs more frequently, then its TF value increases. For example, if the term "apple" appears 10 times in a document of 100 words, the TF for "apple" is 10/100 = 0.1.</p>
        <p><strong>IDF (Inverse Document Frequency):</strong> This represents how important a term is in the entire corpus of documents. If a term is common across many documents, its IDF value decreases. For example, if we have 1000 documents and the term "apple" appears in 100 of them, the IDF for "apple" is log(1000/100) = 1.</p>
        <p><strong>TF-IDF:</strong> This is the product of TF and IDF. A high TF-IDF score can indicate a term's importance in a single document relative to a set of documents or corpus. In our example, the TF-IDF for "apple" is 0.1 * 1 = 0.1.</p>
        <p><strong>Interpretation:</strong> A low TF value for a term means that the term appears less frequently in the document. A low IDF value means that the term is common across many documents. A low TF-IDF value could mean either that the term is common across all documents (low IDF), appears less frequently in the document in question (low TF), or both. Using our "apple" example, if "apple" appeared only 5 times in the document (TF = 5/100 = 0.05) or if it appeared in 200 out of 1000 documents (IDF = log(1000/200) = 0.7), the TF-IDF value would be lower (0.05 * 0.7 = 0.035).</p>
    </div>
    

    <div id="seo-tables">
        <table id="resultsTable">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Price</th>
                    <th>List Ranking</th>
                    <th>Sponsored</th>
                    <th>Item Number</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>

        <table id="tfidfTable">
            <thead>
                <tr>
                    <th id="termHeader">Term</th>
                    <th id="tfHeader">TF</th>
                    <th id="idfHeader">IDF</th>
                    <th id="tfIdfHeader">TF-IDF</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>




        

    </div>

    <table id="titleEvalTable">
        <thead>
            <tr>
                <th>Term</th>
                <th>TF-IDF</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
    


    <script src="index.js"></script>
</body>

</html>