[{"color": "rgb(255, 224, 102)", "messages": ["Hey {{Customer_First_Name}},\n\nHope this message finds you in great spirits!  I just wanted to express our heartfelt thanks for your recent purchase from our little family-run shop. It truly means a lot to us!\n\nI'm thrilled to share that your package is currently making its journey to you and should arrive by {{Delivery_Date}}. We're all hoping it gets to you smoothly and brings a smile to your face!\n\nShould you have any questions or need assistance once your order arrives, please don't hesitate to reach out. We're here to ensure you have a wonderful experience, and your feedback is always welcomed—it helps our family grow stronger!\n\nIn the rare case you find anything less than perfect with your order, please remember, we're just a message away. We're committed to making things right for you, no fuss, no stress.\n\nThank you once again for supporting our small family business. Your choice to shop with us is what keeps our dreams alive and kicking! Wishing you the best of days ahead!\n\nWarmest wishes,"], "name": "Thanks For Your Order - Shipped Date"}, {"color": "rgb(182, 251, 190)", "messages": ["Hello {{Customer_First_Name}},\n\nI hope this note finds you well. I'm reaching out with a heartfelt thank you for supporting our small family-run shop. Every order means so much to us, truly.\n\nToday, I have some disappointing news to share. There was an unexpected delay with our fulfillment company that has affected your order, and it's really saddened us. We strive for perfection in serving you, and it’s upsetting when things don’t go as planned. Please know that I've expressed our frustration to our fulfillment partners and they are prioritizing the re-shipment of your package.\n\nWe are doing everything possible to ensure your package arrives by {{Delivery_Date}}. If you have any questions or need further assistance once it arrives, please don't hesitate to reach out. Your satisfaction and feedback are crucial for our growth and improvement.\n\nIn case there’s anything about your order that doesn’t meet your expectations, please contact me directly. I’m here to resolve any issues quickly and with care—no stress, no hassle.\n\nThank you once again for your understanding and patience. Your continued support helps keep our dream alive and is something we don’t take for granted.\n\nSending warmest regards and apologies for the inconvenience"], "name": "Delay In Order - Reshipped"}, {"color": "rgb(255, 102, 0)", "messages": ["Hello {{Customer_First_Name}},\n\nI hope you’re doing great! Just touching base to see how things are going with you and if you have received your package. We really hope it’s fitting into your life beautifully!\n\nIf there’s anything you need or just want to chat about your purchase, I’m here. We’re not just here to send things your way; we’re here to make sure you love them, too!\n\nThanks so much for being a part of our community. Your support keeps us going and means more than we can say.\n\nTake care and don't hesitate to drop me a line anytime!\n\nWarmest regards,"], "name": "Follow Up Message"}, {"color": "rgb(255, 102, 102)", "messages": ["test1"], "name": "test1"}, {"color": "rgb(102, 255, 102)", "messages": ["test2"], "name": "test2"}, {"color": "rgb(255, 102, 102)", "messages": ["test3"], "name": "test3"}, {"color": "rgb(102, 255, 102)", "messages": ["test4"], "name": "test4"}, {"color": "rgb(255, 102, 102)", "messages": ["test5"], "name": "test5"}, {"color": "rgb(102, 255, 102)", "messages": ["test6"], "name": "test6"}, {"color": "rgb(255, 102, 102)", "messages": ["test7"], "name": "test7"}, {"color": "rgb(102, 255, 102)", "messages": ["test8"], "name": "test8"}, {"color": "rgb(255, 102, 102)", "messages": ["test9"], "name": "test9"}, {"color": "rgb(102, 255, 102)", "messages": ["test10"], "name": "test10"}, {"color": "rgb(255, 102, 102)", "messages": ["test11"], "name": "test11"}, {"color": "rgb(102, 255, 102)", "messages": ["test12"], "name": "test12"}, {"color": "rgb(255, 102, 102)", "messages": ["test13"], "name": "test13"}, {"color": "rgb(102, 255, 102)", "messages": ["test14"], "name": "test14"}, {"color": "rgb(255, 102, 102)", "messages": ["test15"], "name": "test15"}, {"color": "rgb(102, 255, 102)", "messages": ["test16"], "name": "test16"}, {"color": "rgb(255, 102, 102)", "messages": ["test17"], "name": "test17"}, {"color": "rgb(102, 255, 102)", "messages": ["test18"], "name": "test18"}, {"color": "rgb(255, 102, 102)", "messages": ["test19"], "name": "test19"}, {"color": "rgb(102, 255, 102)", "messages": ["test20"], "name": "test20"}, {"color": "rgb(255, 102, 102)", "messages": ["test21"], "name": "test21"}, {"color": "rgb(102, 255, 102)", "messages": ["test22"], "name": "test22"}, {"color": "rgb(255, 102, 102)", "messages": ["test23"], "name": "test23"}, {"color": "rgb(102, 255, 102)", "messages": ["test24"], "name": "test24"}, {"color": "rgb(255, 102, 102)", "messages": ["test25"], "name": "test25"}, {"color": "rgb(102, 255, 102)", "messages": ["test26"], "name": "test26"}, {"color": "rgb(255, 102, 102)", "messages": ["test27"], "name": "test27"}, {"color": "rgb(102, 255, 102)", "messages": ["test28"], "name": "test28"}, {"color": "rgb(255, 102, 102)", "messages": ["test29"], "name": "test29"}, {"color": "rgb(102, 255, 102)", "messages": ["test30"], "name": "test30"}]