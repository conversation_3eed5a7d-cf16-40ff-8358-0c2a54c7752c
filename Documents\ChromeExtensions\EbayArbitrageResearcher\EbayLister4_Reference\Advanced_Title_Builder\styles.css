body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.flex-container {
    display: flex;
}

.main-container {
    max-width: 75%;
    margin-right: auto; /* Adjust as needed */
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


.side-container {
    width: 25%;
    /* Add your styles here */
}



header {
    text-align: center;
    padding: 20px 0;
}

.instructions {
    border-top: 1px solid #ddd;
    padding: 20px;
}

.item-display {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    padding: 20px;
}

.title-container h2 {
    margin: 0;
    padding: 10px;
    background-color: #eaeaea;
    border-left: 4px solid #333;
}

.image-container {
    text-align: center;
}

.image-container img {
    max-width: 50%; /* Adjusted to make the image smaller */
    height: auto;
    border: 1px solid #ddd;
    padding: 5px;
    background-color: #fff;
    display: block; /* Ensures the image is centered in its container */
    margin: 0 auto; /* Center the image horizontally */
}
.description-container {
    grid-column: 1 / -1;
    background-color: #eaeaea;
    padding: 10px;
}

.operations {
    text-align: center;
    padding: 20px;
    background-color: #e8f4f8; /* Light blue background for a subtle look */
    border-top: 1px solid #ddd; /* Adds a subtle border to the top */
}

.operations button {
    padding: 10px 20px;
    background-color: #007bff; /* Bright blue color for the button */
    color: #ffffff;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.operations button:hover {
    background-color: #0056b3; /* Darker shade on hover for interaction feedback */
}


.operations {
    font-family: Arial, sans-serif;
}

#operationStatus {
    margin-top: 10px;
    padding: 10px;
    background-color: #f2f2f2;
    border: 1px solid #dcdcdc;
    color: #333;
    text-align: center;
}

#output {
    text-align: left;
    margin-top: 10px;
    padding: 10px;
    background-color: #eef;
    border: 1px solid #ccd;
    color: #036;
    height: 150px;
    overflow-y: auto; /* Allows scrolling if content overflows */
}

#output_titles{
    text-align: left;
    margin-top: 10px;
    padding: 10px;
    margin-bottom: 10px;

    background-color: #eef;
    border: 1px solid #ccd;
    color: #036;

}
.perfect_title {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
}

.title_type {
    font-size: 2.0em;
    font-weight: bold;
    color: rgb(47, 100, 170);
    margin-top: 30px;
    margin-bottom: 10px;
}

.highlight {
    background-color: yellow;
    cursor: pointer;

    /* Inherit all font properties */
    font: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    text-transform: inherit;
    color: inherit;
    text-decoration: inherit;
}


.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
    -webkit-animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { -webkit-transform: rotate(360deg); }
}

@-webkit-keyframes spin {
    to { -webkit-transform: rotate(360deg); }
}

#operationStatus {
    font-size: 18px; /* Larger font size */
    color: #69c733; /* A distinct font color */
    font-weight: bold; /* Bold font weight */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); /* Optional text shadow for depth */
    padding: 10px; /* Padding for better spacing */
    margin-top: 10px;
    background-color: #f2f2f2;
    border: 1px solid #dcdcdc;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { background-color: #f2f2f2; }
    50% { background-color: #e0e0e0; }
    100% { background-color: #f2f2f2; }
}

.output_container{
    text-align: left;
}



#tfIdfTableContainer {
    margin: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#tfIdfTableContainer h2 {
    margin-top: 0;
    color: #333;
    font-family: Arial, sans-serif;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    text-align: left;
    padding: 8px;
}

th {
    background-color: #f2f2f2;
    color: #333;
}

tr:nth-child(even) {
    background-color: #fff;
}

tr:nth-child(odd) {
    background-color: #f9f9f9;
}

#ebay_search_query {
    width: 100%;
    padding: 12px 20px;
    margin: 8px 0;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: large;
    font-weight: bold;
}