/**
 * eBay Search Scanner - Defensive Implementation with IIFE
 * Prevents race conditions and ensures atomic export
 */
(function() {
  'use strict';
  
  console.time('Scanner load');
  console.log('🔄 EbaySearchScanner: Starting defensive initialization...');

  // Defensive dependency checker
  function waitFor(testFn, timeout = 5000, step = 50) {
    return new Promise((resolve, reject) => {
      const t0 = performance.now();
      (function loop() {
        try {
          if (testFn()) return resolve(true);
          if (timeout && performance.now() - t0 > timeout) {
            return reject(new Error(`Timeout waiting for condition after ${timeout}ms`));
          }
          setTimeout(loop, step);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  class EbaySearchScanner {
    constructor() {
      // Don't access any globals in constructor - lazy initialize everything
      this.eventBus = null;
      this.errorHandler = null;
      this.rateLimiter = null;
      
      this.debug = true;
      this.isScanning = false;
      this.currentProgress = 0;
      this.dependenciesInitialized = false;
      
      // Comprehensive selector mappings for different eBay layouts
      this.selectors = {
        // Main listing containers
        listingContainers: [
          '.s-item',
          '.srp-results .s-item',
          '.srp-river-results .s-item',
          '.su-card-container'
        ],
        
        // Title selectors
        titles: [
          '.s-item__title',
          '.s-item__title--tag',
          '.s-item__title-label',
          '.s-item__link .s-item__title',
          'h3.s-item__title'
        ],
        
        // Price selectors
        prices: [
          '.s-item__price',
          '.s-item__price .notranslate',
          '.s-item__detail .s-item__price',
          '.s-item__price-range'
        ],
        
        // Shipping selectors
        shipping: [
          '.s-item__shipping',
          '.s-item__shipping .vi-price',
          '.s-item__logisticsCost',
          '.s-item__detail .s-item__shipping'
        ],
        
        // Condition selectors
        conditions: [
          '.s-item__condition-text',
          '.s-item__subtitle .SECONDARY_INFO',
          '.s-item__condition',
          '.s-item__detail .s-item__condition-text'
        ],
        
        // URL/Link selectors
        links: [
          '.s-item__link',
          '.s-item__title a',
          'a.s-item__link'
        ],
        
        // Image selectors
        images: [
          '.s-item__image img',
          '.s-item__wrapper img',
          '.s-item__image .s-item__image-img'
        ],
        
        // Sold date selectors
        soldDates: [
          '.s-item__title--tag .POSITIVE',
          '.s-item__ended-date',
          '.s-item__sold-date'
        ],
        
        // Watchers/Bids selectors
        watchers: [
          '.s-item__watchheart',
          '.s-item__bids',
          '.s-item__bidCount'
        ]
      };
    }

    /**
     * Initialize dependencies when they're available
     */
    async initializeDependencies() {
      if (this.dependenciesInitialized) return true;
      
      try {
        // Wait for all required dependencies
        await waitFor(() => window.ArbitrageEventBus, 3000);
        await waitFor(() => window.ArbitrageErrorHandler, 3000);
        await waitFor(() => window.ArbitrageRateLimiter, 3000);
        
        // ✅ CRITICAL FIX: Instantiate classes with 'new' keyword
        this.eventBus = new window.ArbitrageEventBus();
        this.errorHandler = new window.ArbitrageErrorHandler();
        this.rateLimiter = new window.ArbitrageRateLimiter();
        
        this.dependenciesInitialized = true;
        
        if (this.debug) {
          console.log('✅ EbaySearchScanner: Dependencies initialized successfully');
        }
        
        return true;
      } catch (error) {
        console.error('❌ EbaySearchScanner: Failed to initialize dependencies:', error);
        return false;
      }
    }

    /**
     * Main scanning function - extracts data from all listings on current page
     */
    async scanCurrentPage(options = {}) {
      // Initialize dependencies first
      const depsReady = await this.initializeDependencies();
      if (!depsReady) {
        throw new Error('Dependencies not available for scanning');
      }
      
      const {
        maxItems = 100,
        includeSponsored = false,
        onProgress = null
      } = options;

      if (this.isScanning) {
        throw new Error('Scan already in progress');
      }

      this.isScanning = true;
      this.currentProgress = 0;

      try {
        if (this.debug) {
          console.log('🔍 EbaySearchScanner: Starting page scan...');
        }

        // Emit scan started event
        if (this.eventBus) {
          this.eventBus.emit('scan:started', {
            timestamp: Date.now(),
            maxItems,
            includeSponsored
          });
        }

        // Find all listing containers
        const containers = this.findListingContainers();
        
        if (containers.length === 0) {
          throw new Error('No listing containers found on page');
        }

        if (this.debug) {
          console.log(`📋 Found ${containers.length} listing containers`);
        }

        const results = [];
        const itemsToProcess = Math.min(containers.length, maxItems);

        for (let i = 0; i < itemsToProcess; i++) {
          try {
            // Rate limiting
            if (this.rateLimiter) {
              await this.rateLimiter.waitForSlot();
            }

            const container = containers[i];
            
            // Skip sponsored listings if requested
            if (!includeSponsored && this.isSponsoredListing(container)) {
              continue;
            }

            const listingData = await this.extractListingData(container, i);
            
            if (listingData && this.isValidListing(listingData)) {
              results.push(listingData);
            }

            // Update progress
            this.currentProgress = ((i + 1) / itemsToProcess) * 100;
            
            if (onProgress) {
              onProgress({
                progress: this.currentProgress,
                processed: i + 1,
                total: itemsToProcess,
                found: results.length
              });
            }

            // Emit progress event
            if (this.eventBus) {
              this.eventBus.emit('scan:progress', {
                progress: this.currentProgress,
                processed: i + 1,
                total: itemsToProcess,
                found: results.length
              });
            }

          } catch (error) {
            console.warn(`⚠️ Failed to process listing ${i}:`, error);
            
            if (this.errorHandler) {
              this.errorHandler.handleError(error, 'listing_extraction', {
                listingIndex: i,
                containerId: containers[i]?.id || 'unknown'
              });
            }
          }
        }

        if (this.debug) {
          console.log(`✅ Scan complete: ${results.length} valid listings extracted`);
        }

        // Emit scan completed event
        if (this.eventBus) {
          this.eventBus.emit('scan:completed', {
            timestamp: Date.now(),
            totalFound: results.length,
            totalProcessed: itemsToProcess
          });
        }

        return results;

      } catch (error) {
        console.error('❌ EbaySearchScanner: Scan failed:', error);
        
        if (this.errorHandler) {
          this.errorHandler.handleError(error, 'page_scan', { options });
        }

        // Emit scan error event
        if (this.eventBus) {
          this.eventBus.emit('scan:error', {
            timestamp: Date.now(),
            error: error.message
          });
        }

        throw error;
      } finally {
        this.isScanning = false;
        this.currentProgress = 0;
      }
    }

    /**
     * Find all listing containers on the page
     */
    findListingContainers() {
      for (const selector of this.selectors.listingContainers) {
        const containers = document.querySelectorAll(selector);
        if (containers.length > 0) {
          if (this.debug) {
            console.log(`📦 Found ${containers.length} containers with selector: ${selector}`);
          }
          return Array.from(containers);
        }
      }
      return [];
    }

    /**
     * Check if a listing is sponsored
     */
    isSponsoredListing(container) {
      const sponsoredIndicators = [
        '.s-item__sponsored',
        '.s-item__title--tag .SPONSORED',
        '[data-viewport*="SPONSORED"]'
      ];
      
      return sponsoredIndicators.some(selector => 
        container.querySelector(selector) !== null
      );
    }

    /**
     * Extract data from a single listing container
     */
    async extractListingData(container, index) {
      try {
        const data = {
          index,
          title: this.extractText(container, this.selectors.titles),
          price: this.extractPrice(container),
          shipping: this.extractShipping(container),
          condition: this.extractText(container, this.selectors.conditions),
          url: this.extractUrl(container),
          imageUrl: this.extractImageUrl(container),
          soldDate: this.extractSoldDate(container),
          watchers: this.extractWatchers(container),
          extractedAt: new Date().toISOString()
        };

        return data;
      } catch (error) {
        console.warn(`⚠️ Failed to extract data from listing ${index}:`, error);
        return null;
      }
    }

    /**
     * Extract text using multiple selectors
     */
    extractText(container, selectors) {
      for (const selector of selectors) {
        const element = container.querySelector(selector);
        if (element && element.textContent) {
          return element.textContent.trim();
        }
      }
      return null;
    }

    /**
     * Extract and parse price
     */
    extractPrice(container) {
      const priceText = this.extractText(container, this.selectors.prices);
      if (!priceText) return null;

      // Extract numeric value from price text
      const match = priceText.match(/[\d,]+\.?\d*/);
      if (match) {
        return parseFloat(match[0].replace(/,/g, ''));
      }
      return null;
    }

    /**
     * Extract shipping information
     */
    extractShipping(container) {
      const shippingText = this.extractText(container, this.selectors.shipping);
      if (!shippingText) return null;

      // Check for free shipping
      if (shippingText.toLowerCase().includes('free')) {
        return 0;
      }

      // Extract shipping cost
      const match = shippingText.match(/[\d,]+\.?\d*/);
      if (match) {
        return parseFloat(match[0].replace(/,/g, ''));
      }
      return null;
    }

    /**
     * Extract listing URL
     */
    extractUrl(container) {
      for (const selector of this.selectors.links) {
        const link = container.querySelector(selector);
        if (link && link.href) {
          return link.href;
        }
      }
      return null;
    }

    /**
     * Extract image URL
     */
    extractImageUrl(container) {
      for (const selector of this.selectors.images) {
        const img = container.querySelector(selector);
        if (img && img.src) {
          return img.src;
        }
      }
      return null;
    }

    /**
     * Extract sold date
     */
    extractSoldDate(container) {
      const dateText = this.extractText(container, this.selectors.soldDates);
      if (!dateText) return null;

      // Try to parse various date formats
      const dateMatch = dateText.match(/\w{3}\s+\d{1,2}/);
      if (dateMatch) {
        return dateMatch[0];
      }
      return dateText;
    }

    /**
     * Extract watchers/bids count
     */
    extractWatchers(container) {
      const watchersText = this.extractText(container, this.selectors.watchers);
      if (!watchersText) return null;

      const match = watchersText.match(/\d+/);
      if (match) {
        return parseInt(match[0]);
      }
      return null;
    }

    /**
     * Validate extracted listing data
     */
    isValidListing(data) {
      return data && 
             data.title && 
             data.title.length > 0 && 
             data.price !== null && 
             data.price > 0;
    }

    /**
     * Stop current scan
     */
    stopScan() {
      this.isScanning = false;
      if (this.debug) {
        console.log('🛑 EbaySearchScanner: Scan stopped');
      }
    }

    /**
     * Get current scan progress
     */
    getProgress() {
      return {
        isScanning: this.isScanning,
        progress: this.currentProgress
      };
    }
  }

  // Export atomically at the bottom - no side effects above this line
  try {
    window.EbaySearchScanner = EbaySearchScanner;
    console.log('✅ EbaySearchScanner exported successfully');
    console.timeEnd('Scanner load');
    
    // Log timing for debugging
    console.log('🕐 EbaySearchScanner initialization timing:', {
      timestamp: performance.now(),
      readyState: document.readyState,
      dependencies: {
        eventBus: !!window.ArbitrageEventBus,
        errorHandler: !!window.ArbitrageErrorHandler,
        rateLimiter: !!window.ArbitrageRateLimiter
      }
    });
  } catch (error) {
    console.error('❌ Failed to export EbaySearchScanner:', error);
  }

})(); // End IIFE
