function a0_0x240a(){const _0x4a7301=['stop','css','986204tnPrvW','falsey','45228fPsaef','fixIOS','47200GMfNVX','953133YQUiDt','custom','other','indexOf','getType','number','border-right-width','-content','getFunctionRanges','length','border-bottom-width','$container','highlightWithinTextarea','padding-bottom','{{hwt-mark-stop}}','hwt','124965pzmyiv','slice','scrollLeft','toLowerCase','ios','prototype','bind','padding-left','-input\x20','update','type','html','-highlights\x20','$el','removeClass','getRegExpRanges','function','removeStaggeredRanges','start','<div>','val','&gt;','434uDsijt','windows\x20phone','translateX(','append','3097998FCGvBw','data','regexp','exec','navigator','&lt;','getRangeRanges','border-left-width','{{hwt-mark-start|','each','push','blockContainerScroll','unrecognized\x20highlight\x20type','error','map','insertAfter','renderMarks','</mark>','$backdrop','valid\x20config\x20object\x20not\x20provided','className','scroll.','padding-right','fixFirefox','range','getBoundaries','detectBrowser','sortBoundaries','forEach','replace','hasOwnProperty','40yKTbCq','browser','<mark\x20class=\x22','scrollTop','getStringRanges','destroy','handleScroll','unwrap','-input','getRanges','scroll','11237192KNOkNp','apply','remove','isGenerated','index','handleInput','-text\x20','highlight','2wIEWlw','+=3px','match','off','userAgent','string','padding-top','firefox','concat','$highlights'];a0_0x240a=function(){return _0x4a7301;};return a0_0x240a();}function a0_0x276c(_0x19d06d,_0x1879b4){const _0x240a2d=a0_0x240a();return a0_0x276c=function(_0x276c37,_0x44e100){_0x276c37=_0x276c37-0x1b3;let _0x11eff0=_0x240a2d[_0x276c37];return _0x11eff0;},a0_0x276c(_0x19d06d,_0x1879b4);}(function(_0xa03fa,_0x48d541){const _0x13bb19=a0_0x276c,_0x42bc10=_0xa03fa();while(!![]){try{const _0x27453a=parseInt(_0x13bb19(0x1b9))/0x1*(-parseInt(_0x13bb19(0x215))/0x2)+parseInt(_0x13bb19(0x1e3))/0x3+-parseInt(_0x13bb19(0x1b4))/0x4+parseInt(_0x13bb19(0x1b8))/0x5+-parseInt(_0x13bb19(0x1b6))/0x6*(parseInt(_0x13bb19(0x1df))/0x7)+parseInt(_0x13bb19(0x20d))/0x8+-parseInt(_0x13bb19(0x1c9))/0x9*(-parseInt(_0x13bb19(0x202))/0xa);if(_0x27453a===_0x48d541)break;else _0x42bc10['push'](_0x42bc10['shift']());}catch(_0x3f379f){_0x42bc10['push'](_0x42bc10['shift']());}}}(a0_0x240a,0xcbeb7),function(_0x465c50){const _0x28f399=a0_0x276c;let _0x2299f4=_0x28f399(0x1c8),_0x154da5=function(_0x5c23db,_0x42d770){this['init'](_0x5c23db,_0x42d770);};_0x154da5[_0x28f399(0x1ce)]={'init':function(_0x5c4f1d,_0xaf2c97){const _0x37e537=_0x28f399;this[_0x37e537(0x1d6)]=_0x5c4f1d,this[_0x37e537(0x1bd)](_0xaf2c97)==='function'&&(_0xaf2c97={'highlight':_0xaf2c97}),this['getType'](_0xaf2c97)==='custom'?(this[_0x37e537(0x214)]=_0xaf2c97,this['generate']()):console[_0x37e537(0x1f0)](_0x37e537(0x1f6));},'getType':function(_0x3c4df2){const _0x61e297=_0x28f399;let _0x348045=typeof _0x3c4df2;if(!_0x3c4df2)return _0x61e297(0x1b5);else{if(Array['isArray'](_0x3c4df2))return _0x3c4df2[_0x61e297(0x1c2)]===0x2&&typeof _0x3c4df2[0x0]===_0x61e297(0x1be)&&typeof _0x3c4df2[0x1]===_0x61e297(0x1be)?_0x61e297(0x1fb):'array';else{if(_0x348045==='object'){if(_0x3c4df2 instanceof RegExp)return _0x61e297(0x1e5);else{if(_0x3c4df2[_0x61e297(0x201)](_0x61e297(0x214)))return _0x61e297(0x1ba);}}else{if(_0x348045===_0x61e297(0x1d9)||_0x348045===_0x61e297(0x21a))return _0x348045;}}}return _0x61e297(0x1bb);},'generate':function(){const _0x426793=_0x28f399;this[_0x426793(0x1d6)]['addClass'](_0x2299f4+_0x426793(0x1d1)+_0x2299f4+_0x426793(0x1c0))['on']('input.'+_0x2299f4,this[_0x426793(0x212)]['bind'](this))['on'](_0x426793(0x1f8)+_0x2299f4,this[_0x426793(0x208)]['bind'](this)),this[_0x426793(0x21e)]=_0x465c50(_0x426793(0x1dc),{'class':_0x2299f4+_0x426793(0x1d5)+_0x2299f4+_0x426793(0x1c0)}),this[_0x426793(0x1f5)]=_0x465c50(_0x426793(0x1dc),{'class':_0x2299f4+'-backdrop'})['append'](this[_0x426793(0x21e)]),this[_0x426793(0x1c4)]=_0x465c50('<div>',{'class':_0x2299f4+'-container'})[_0x426793(0x1f2)](this[_0x426793(0x1d6)])[_0x426793(0x1e2)](this['$backdrop'],this[_0x426793(0x1d6)])['on'](_0x426793(0x20c),this[_0x426793(0x1ee)]['bind'](this)),this['browser']=this[_0x426793(0x1fd)]();switch(this['browser']){case _0x426793(0x21c):this[_0x426793(0x1fa)]();break;case _0x426793(0x1cd):this[_0x426793(0x1b7)]();break;}this['isGenerated']=!![],this['handleInput']();},'detectBrowser':function(){const _0x939f68=_0x28f399;let _0x4c63a4=window[_0x939f68(0x1e7)][_0x939f68(0x219)]['toLowerCase']();if(_0x4c63a4[_0x939f68(0x1bc)](_0x939f68(0x21c))!==-0x1)return _0x939f68(0x21c);else{if(!!_0x4c63a4['match'](/msie|trident\/7|edge/))return'ie';else return!!_0x4c63a4[_0x939f68(0x217)](/ipad|iphone|ipod/)&&_0x4c63a4[_0x939f68(0x1bc)](_0x939f68(0x1e0))===-0x1?'ios':'other';}},'fixFirefox':function(){const _0x228820=_0x28f399;let _0x118d9b=this[_0x228820(0x21e)][_0x228820(0x1b3)]([_0x228820(0x21b),_0x228820(0x1f9),'padding-bottom',_0x228820(0x1d0)]),_0x420ecb=this[_0x228820(0x21e)][_0x228820(0x1b3)](['border-top-width',_0x228820(0x1bf),_0x228820(0x1c3),_0x228820(0x1ea)]);this['$highlights'][_0x228820(0x1b3)]({'padding':'0','border-width':'0'}),this[_0x228820(0x1f5)][_0x228820(0x1b3)]({'margin-top':'+='+_0x118d9b[_0x228820(0x21b)],'margin-right':'+='+_0x118d9b[_0x228820(0x1f9)],'margin-bottom':'+='+_0x118d9b[_0x228820(0x1c6)],'margin-left':'+='+_0x118d9b[_0x228820(0x1d0)]})[_0x228820(0x1b3)]({'margin-top':'+='+_0x420ecb['border-top-width'],'margin-right':'+='+_0x420ecb[_0x228820(0x1bf)],'margin-bottom':'+='+_0x420ecb[_0x228820(0x1c3)],'margin-left':'+='+_0x420ecb['border-left-width']});},'fixIOS':function(){const _0x21ed90=_0x28f399;this[_0x21ed90(0x21e)][_0x21ed90(0x1b3)]({'padding-left':_0x21ed90(0x216),'padding-right':'+=3px'});},'handleInput':function(){const _0x2fd670=_0x28f399;let _0x4df23a=this['$el'][_0x2fd670(0x1dd)](),_0x25cb48=this[_0x2fd670(0x20b)](_0x4df23a,this[_0x2fd670(0x214)]),_0x2389fd=this[_0x2fd670(0x1da)](_0x25cb48),_0x48278f=this[_0x2fd670(0x1fc)](_0x2389fd);this[_0x2fd670(0x1f3)](_0x48278f);},'getRanges':function(_0x104a73,_0x1f2ffa){const _0x18f834=_0x28f399;let _0x36c841=this[_0x18f834(0x1bd)](_0x1f2ffa);switch(_0x36c841){case'array':return this['getArrayRanges'](_0x104a73,_0x1f2ffa);case'function':return this[_0x18f834(0x1c1)](_0x104a73,_0x1f2ffa);case _0x18f834(0x1e5):return this[_0x18f834(0x1d8)](_0x104a73,_0x1f2ffa);case'string':return this[_0x18f834(0x206)](_0x104a73,_0x1f2ffa);case'range':return this[_0x18f834(0x1e9)](_0x104a73,_0x1f2ffa);case'custom':return this['getCustomRanges'](_0x104a73,_0x1f2ffa);default:if(!_0x1f2ffa)return[];else console[_0x18f834(0x1f0)](_0x18f834(0x1ef));}},'getArrayRanges':function(_0x46e817,_0x27c972){const _0x243174=_0x28f399;let _0x5d8137=_0x27c972[_0x243174(0x1f1)](this['getRanges'][_0x243174(0x1cf)](this,_0x46e817));return Array['prototype'][_0x243174(0x21d)][_0x243174(0x20e)]([],_0x5d8137);},'getFunctionRanges':function(_0x26f2c8,_0x850aa1){const _0x15eeed=_0x28f399;return this[_0x15eeed(0x20b)](_0x26f2c8,_0x850aa1(_0x26f2c8));},'getRegExpRanges':function(_0x5b4c52,_0x54d574){const _0x338053=_0x28f399;let _0x26f33d=[],_0x517266;while(_0x517266=_0x54d574[_0x338053(0x1e6)](_0x5b4c52),_0x517266!==null){_0x26f33d[_0x338053(0x1ed)]([_0x517266['index'],_0x517266['index']+_0x517266[0x0][_0x338053(0x1c2)]]);if(!_0x54d574['global'])break;}return _0x26f33d;},'getStringRanges':function(_0x275324,_0x527e06){const _0x87c6c3=_0x28f399;let _0x4ad121=[],_0x3d95c2=_0x275324[_0x87c6c3(0x1cc)](),_0x2189de=_0x527e06[_0x87c6c3(0x1cc)](),_0x401d65=0x0;while(_0x401d65=_0x3d95c2['indexOf'](_0x2189de,_0x401d65),_0x401d65!==-0x1){_0x4ad121[_0x87c6c3(0x1ed)]([_0x401d65,_0x401d65+_0x2189de[_0x87c6c3(0x1c2)]]),_0x401d65+=_0x2189de[_0x87c6c3(0x1c2)];}return _0x4ad121;},'getRangeRanges':function(_0x557d3e,_0x1005dc){return[_0x1005dc];},'getCustomRanges':function(_0x2b3cb0,_0x3e7c4d){const _0x49338f=_0x28f399;let _0x2cbfc1=this[_0x49338f(0x20b)](_0x2b3cb0,_0x3e7c4d[_0x49338f(0x214)]);return _0x3e7c4d[_0x49338f(0x1f7)]&&_0x2cbfc1[_0x49338f(0x1ff)](function(_0x2ee7d4){const _0x38a742=_0x49338f;_0x2ee7d4[_0x38a742(0x1f7)]?_0x2ee7d4[_0x38a742(0x1f7)]=_0x3e7c4d[_0x38a742(0x1f7)]+'\x20'+_0x2ee7d4[_0x38a742(0x1f7)]:_0x2ee7d4['className']=_0x3e7c4d[_0x38a742(0x1f7)];}),_0x2cbfc1;},'removeStaggeredRanges':function(_0x37f45b){const _0x9272fb=_0x28f399;let _0x297b9a=[];return _0x37f45b[_0x9272fb(0x1ff)](function(_0x1b82aa){const _0x22044c=_0x9272fb;let _0xda978e=_0x297b9a['some'](function(_0x307fd3){let _0x28ffc2=_0x1b82aa[0x0]>_0x307fd3[0x0]&&_0x1b82aa[0x0]<_0x307fd3[0x1],_0x4ea281=_0x1b82aa[0x1]>_0x307fd3[0x0]&&_0x1b82aa[0x1]<_0x307fd3[0x1];return _0x28ffc2!==_0x4ea281;});!_0xda978e&&_0x297b9a[_0x22044c(0x1ed)](_0x1b82aa);}),_0x297b9a;},'getBoundaries':function(_0x578ea0){const _0x1620dc=_0x28f399;let _0x34c97e=[];return _0x578ea0[_0x1620dc(0x1ff)](function(_0x4e189a){const _0x1e6810=_0x1620dc;_0x34c97e[_0x1e6810(0x1ed)]({'type':'start','index':_0x4e189a[0x0],'className':_0x4e189a[_0x1e6810(0x1f7)]}),_0x34c97e[_0x1e6810(0x1ed)]({'type':'stop','index':_0x4e189a[0x1]});}),this[_0x1620dc(0x1fe)](_0x34c97e),_0x34c97e;},'sortBoundaries':function(_0x35d50c){_0x35d50c['sort'](function(_0x4f753c,_0x174ca9){const _0x5a8649=a0_0x276c;if(_0x4f753c['index']!==_0x174ca9[_0x5a8649(0x211)])return _0x174ca9[_0x5a8649(0x211)]-_0x4f753c[_0x5a8649(0x211)];else{if(_0x4f753c[_0x5a8649(0x1d3)]==='stop'&&_0x174ca9[_0x5a8649(0x1d3)]===_0x5a8649(0x1db))return 0x1;else return _0x4f753c['type']===_0x5a8649(0x1db)&&_0x174ca9[_0x5a8649(0x1d3)]===_0x5a8649(0x21f)?-0x1:0x0;}});},'renderMarks':function(_0x3c1b62){const _0x60186c=_0x28f399;let _0x3191bd=this[_0x60186c(0x1d6)][_0x60186c(0x1dd)]();_0x3c1b62[_0x60186c(0x1ff)](function(_0x1f3a29,_0x276daf){const _0x6cd4ec=_0x60186c;let _0x1780e5;_0x1f3a29[_0x6cd4ec(0x1d3)]===_0x6cd4ec(0x1db)?_0x1780e5=_0x6cd4ec(0x1eb)+_0x276daf+'}}':_0x1780e5=_0x6cd4ec(0x1c7),_0x3191bd=_0x3191bd[_0x6cd4ec(0x1ca)](0x0,_0x1f3a29['index'])+_0x1780e5+_0x3191bd[_0x6cd4ec(0x1ca)](_0x1f3a29['index']);}),_0x3191bd=_0x3191bd[_0x60186c(0x200)](/\n(\{\{hwt-mark-stop\}\})?$/,'\x0a\x0a$1'),_0x3191bd=_0x3191bd[_0x60186c(0x200)](/</g,_0x60186c(0x1e8))['replace'](/>/g,_0x60186c(0x1de)),this[_0x60186c(0x203)]==='ie'&&(_0x3191bd=_0x3191bd[_0x60186c(0x200)](/ /g,'\x20<wbr>')),_0x3191bd=_0x3191bd[_0x60186c(0x200)](/\{\{hwt-mark-start\|(\d+)\}\}/g,function(_0xc8c9be,_0x21dd5a){const _0x465bec=_0x60186c;var _0x9611c=_0x3c1b62[+_0x21dd5a]['className'];return _0x9611c?_0x465bec(0x204)+_0x9611c+'\x22>':'<mark>';}),_0x3191bd=_0x3191bd[_0x60186c(0x200)](/\{\{hwt-mark-stop\}\}/g,_0x60186c(0x1f4)),this['$highlights'][_0x60186c(0x1d4)](_0x3191bd);},'handleScroll':function(){const _0x27c2b0=_0x28f399;let _0x106cb3=this[_0x27c2b0(0x1d6)]['scrollTop']();this[_0x27c2b0(0x1f5)][_0x27c2b0(0x205)](_0x106cb3);let _0x47cda1=this[_0x27c2b0(0x1d6)][_0x27c2b0(0x1cb)]();this[_0x27c2b0(0x1f5)]['css']('transform',_0x47cda1>0x0?_0x27c2b0(0x1e1)+-_0x47cda1+'px)':'');},'blockContainerScroll':function(){const _0x58afb0=_0x28f399;this[_0x58afb0(0x1c4)][_0x58afb0(0x1cb)](0x0);},'destroy':function(){const _0x3d4b97=_0x28f399;this['$backdrop'][_0x3d4b97(0x20f)](),this['$el'][_0x3d4b97(0x209)]()[_0x3d4b97(0x1d7)](_0x2299f4+_0x3d4b97(0x213)+_0x2299f4+_0x3d4b97(0x20a))[_0x3d4b97(0x218)](_0x2299f4)['removeData'](_0x2299f4);}},_0x465c50['fn'][_0x28f399(0x1c5)]=function(_0x489250){const _0x516f9f=_0x28f399;return this[_0x516f9f(0x1ec)](function(){const _0x3f1b18=_0x516f9f;let _0x14a992=_0x465c50(this),_0x184758=_0x14a992[_0x3f1b18(0x1e4)](_0x2299f4);if(typeof _0x489250===_0x3f1b18(0x21a)){if(_0x184758)switch(_0x489250){case _0x3f1b18(0x1d2):_0x184758['handleInput']();break;case _0x3f1b18(0x207):_0x184758[_0x3f1b18(0x207)]();break;default:console['error']('unrecognized\x20method\x20string');}else console['error']('plugin\x20must\x20be\x20instantiated\x20first');}else _0x184758&&_0x184758['destroy'](),_0x184758=new _0x154da5(_0x14a992,_0x489250),_0x184758[_0x3f1b18(0x210)]&&_0x14a992[_0x3f1b18(0x1e4)](_0x2299f4,_0x184758);});};}(jQuery));