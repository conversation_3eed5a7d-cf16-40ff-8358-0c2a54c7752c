var a0_0x322f4e=a0_0x48b1;(function(_0x52b1a9,_0xc29b8b){var _0x545f34=a0_0x48b1,_0x298eef=_0x52b1a9();while(!![]){try{var _0x483d63=-parseInt(_0x545f34(0x200))/0x1*(parseInt(_0x545f34(0x2d1))/0x2)+parseInt(_0x545f34(0x29f))/0x3+parseInt(_0x545f34(0x289))/0x4+parseInt(_0x545f34(0x2c6))/0x5*(parseInt(_0x545f34(0x2a6))/0x6)+-parseInt(_0x545f34(0x261))/0x7+parseInt(_0x545f34(0x294))/0x8+-parseInt(_0x545f34(0x2cb))/0x9;if(_0x483d63===_0xc29b8b)break;else _0x298eef['push'](_0x298eef['shift']());}catch(_0x52e7ba){_0x298eef['push'](_0x298eef['shift']());}}}(a0_0x20e6,0xd121c));var shouldStopProcessing=![];console[a0_0x322f4e(0x213)](a0_0x322f4e(0x24b));async function getAllSKU(_0x301f6a){var _0x3e9b9b=a0_0x322f4e,_0x464496=!![],_0x24a158=getTotalPgNumber(),_0x3ee2a8,_0x307993;while(_0x464496){_0x3ee2a8=getCurrentPgNumber(),_0x307993=await getSkusOnPage(),skuList=skuList['concat'](_0x307993),await saveSkuListToLocalStorage(_0x307993),document[_0x3e9b9b(0x2b4)]=_0x3e9b9b(0x1fb)+_0x3ee2a8+'/'+_0x24a158+_0x3e9b9b(0x211)+skuList[_0x3e9b9b(0x26d)],_0x464496=await doesNextPageExistFunc(),_0x464496&&(await goToNextPage(),await waitSomeTime(0x3e8),await waitForNextPageToLoad(_0x3ee2a8+0x1),chrome[_0x3e9b9b(0x27f)][_0x3e9b9b(0x23b)]({'type':_0x3e9b9b(0x20a)}),chrome[_0x3e9b9b(0x2af)]['local'][_0x3e9b9b(0x24d)]({'page_number':_0x3ee2a8+0x1}));}document['title']=_0x3e9b9b(0x2d2)+document[_0x3e9b9b(0x2b4)],await saveSkuListToLocalStorage(_0x307993),document[_0x3e9b9b(0x2b4)]=_0x3e9b9b(0x283)+skuList['length'];}function saveSkuListToLocalStorage(_0x22ef91){return new Promise(_0x13ae68=>{var _0x244969=a0_0x48b1;chrome[_0x244969(0x2af)][_0x244969(0x2c2)][_0x244969(0x203)]('skuList',_0x3fdc90=>{var _0x5d5ae4=_0x244969,_0xe08348=_0x3fdc90[_0x5d5ae4(0x274)]?_0x3fdc90[_0x5d5ae4(0x274)][_0x5d5ae4(0x263)](_0x22ef91):_0x22ef91;chrome['storage'][_0x5d5ae4(0x2c2)][_0x5d5ae4(0x24d)]({'skuList':_0xe08348},_0x13ae68);});});}async function saveTheSkusOnPageManual(){return new Promise(_0x1476af=>{var _0x3b70ac=a0_0x48b1;chrome['storage'][_0x3b70ac(0x2c2)]['get'](_0x3b70ac(0x274),async _0x2ac611=>{var _0x4e6ef1=_0x3b70ac,_0x17c5b9=await getSkusOnPage(),_0x94583a=_0x2ac611['skuList'][_0x4e6ef1(0x263)](_0x17c5b9);chrome[_0x4e6ef1(0x2af)]['local'][_0x4e6ef1(0x24d)]({'skuList':_0x94583a},_0x1476af);});});}function waitSomeTime(_0x249cb7){return new Promise(_0x262d1a=>setTimeout(_0x262d1a,_0x249cb7));}async function waitForNextPageToLoad(_0x2aab6b){while(getCurrentPgNumber()!==_0x2aab6b){await waitSomeTime(0x3e8);}}function goToNextPage(){var _0x3d1ec7=a0_0x322f4e;document['getElementsByClassName']('pagination__next')[0x0][_0x3d1ec7(0x26f)]();}function doesNextPageExistFunc(){var _0x574200=a0_0x322f4e;return document[_0x574200(0x293)](_0x574200(0x2ca))[0x0][_0x574200(0x248)](_0x574200(0x2a3))!==_0x574200(0x282);}function getTotalPgNumber(){var _0x46ed7c=a0_0x322f4e;return parseInt(document['querySelector'](_0x46ed7c(0x254))[_0x46ed7c(0x2bd)][_0x46ed7c(0x210)](/\D/g,''));}function getCurrentPgNumber(){var _0xfa0815=a0_0x322f4e;return parseInt(document[_0xfa0815(0x260)]('.go-to-page\x20.textbox__control')[_0xfa0815(0x1fe)]);}function getSkusOnPage(){return new Promise(_0x15ca2f=>{var _0xa287ab=a0_0x48b1;const _0x1a1c88=[],_0x80549=getItemNodes();_0x80549[_0xa287ab(0x242)](_0x3975a8=>{var _0x4cccca=_0xa287ab;_0x3975a8[_0x4cccca(0x208)]('tr')[_0x4cccca(0x242)](_0xc0a309=>{var _0x3a240f=_0x4cccca;const _0x3bb044=_0xc0a309[_0x3a240f(0x260)](_0x3a240f(0x246));if(_0x3bb044){const _0x1e14ec=_0x3bb044[_0x3a240f(0x258)][_0x3a240f(0x217)]();if(_0x1e14ec)_0x1a1c88[_0x3a240f(0x28c)](_0x1e14ec);}});}),_0x15ca2f(_0x1a1c88);});}function getItemNodes(){var _0x36fc49=a0_0x322f4e;return document[_0x36fc49(0x208)](_0x36fc49(0x1f9));}function fetchEbayData(_0x33f372){var _0x4f85f0=a0_0x322f4e;let _0x22911a={};var _0xb53a7b=_0x33f372['querySelector']('div[class*=\x22title__text\x22]');_0xb53a7b&&(_0x22911a['title']=_0xb53a7b['textContent']['trim']());var _0x34f8e5=_0x33f372[_0x4f85f0(0x260)](_0x4f85f0(0x279));_0x34f8e5&&(_0x22911a[_0x4f85f0(0x285)]=_0x34f8e5[_0x4f85f0(0x248)](_0x4f85f0(0x2b5)));var _0x327805=_0x33f372[_0x4f85f0(0x260)]('td[class*=\x22listingSKU');_0x327805&&(_0x22911a[_0x4f85f0(0x21d)]=_0x327805[_0x4f85f0(0x258)][_0x4f85f0(0x217)]());var _0x215300=_0x33f372[_0x4f85f0(0x260)](_0x4f85f0(0x2d6));_0x215300&&(_0x22911a[_0x4f85f0(0x2ad)]=parseFloat(_0x215300['textContent']['trim']()['replace'](/[^0-9\.]+/g,'')));var _0x43274c=_0x33f372[_0x4f85f0(0x260)](_0x4f85f0(0x2bf));_0x43274c&&(_0x22911a[_0x4f85f0(0x23a)]=parseInt(_0x43274c[_0x4f85f0(0x258)][_0x4f85f0(0x217)]()['replace'](/[^0-9\.]+/g,'')));var _0x322d73=_0x33f372[_0x4f85f0(0x260)](_0x4f85f0(0x23f));_0x322d73&&(_0x22911a['itemNumber']=_0x322d73[_0x4f85f0(0x258)][_0x4f85f0(0x217)]());var _0x5777cc=_0x33f372['querySelector'](_0x4f85f0(0x2a4));_0x5777cc&&(_0x22911a[_0x4f85f0(0x21e)]=parseInt(_0x5777cc[_0x4f85f0(0x258)][_0x4f85f0(0x217)]()[_0x4f85f0(0x210)](/[^0-9\.]+/g,'')));var _0x13a5d3=_0x33f372['querySelector'](_0x4f85f0(0x204));_0x13a5d3&&(_0x22911a[_0x4f85f0(0x253)]=_0x13a5d3[_0x4f85f0(0x258)][_0x4f85f0(0x217)]());var _0x597e6e=_0x33f372[_0x4f85f0(0x260)]('td[class*=\x22timeRemaining\x22]\x20[class*=\x22text-column\x22]\x20div');_0x597e6e&&(_0x22911a['timeLeft']=_0x597e6e[_0x4f85f0(0x258)]['trim']());var _0x13b846=_0x33f372[_0x4f85f0(0x260)]('td[class*=\x22visitCount\x22]\x20.cell-wrapper\x20.fake-link');return _0x13b846&&(_0x22911a[_0x4f85f0(0x22a)]=parseInt(_0x13b846['textContent'][_0x4f85f0(0x217)]()[_0x4f85f0(0x210)](/[^0-9\.]+/g,''))),_0x22911a;}function createRelistButton(_0x10dd03){var _0x3f9d96=a0_0x322f4e,_0xbdb89b=document[_0x3f9d96(0x220)]('button'),_0x5444c7=_0x10dd03[_0x3f9d96(0x287)](0x0)[_0x3f9d96(0x264)]()+_0x10dd03[_0x3f9d96(0x24f)](0x1);return _0xbdb89b[_0x3f9d96(0x244)]=_0x5444c7+'\x20Relist',_0xbdb89b[_0x3f9d96(0x219)][_0x3f9d96(0x2c9)](_0x3f9d96(0x28e)),_0xbdb89b['classList'][_0x3f9d96(0x2c9)](_0x10dd03+'-relist-button'),_0xbdb89b['onclick']=async function(_0x4a30b9){var _0x51cbd8=_0x3f9d96;_0x4a30b9[_0x51cbd8(0x27e)](),console[_0x51cbd8(0x213)](_0x51cbd8(0x2b8)),this[_0x51cbd8(0x2b9)]=!![],this[_0x51cbd8(0x244)]=_0x51cbd8(0x207)+_0x5444c7+_0x51cbd8(0x218),chrome[_0x51cbd8(0x2af)][_0x51cbd8(0x2c2)][_0x51cbd8(0x24d)]({'bulkListType':_0x10dd03});var _0x5b2aa4=this;while(_0x5b2aa4['tagName']!=='TR'){_0x5b2aa4=_0x5b2aa4['parentElement'];}console['log'](_0x5b2aa4);var _0x14b1ca=fetchEbayData(_0x5b2aa4);console[_0x51cbd8(0x213)](_0x14b1ca);var _0x303040=await new Promise(_0x4589cc=>{var _0x56c454=_0x51cbd8;chrome['runtime'][_0x56c454(0x23b)]({'type':_0x56c454(0x225),'ebayData':_0x14b1ca},function(_0x21d319){var _0x27de29=_0x56c454;console['log'](_0x21d319),_0x4589cc(_0x21d319[_0x27de29(0x2d0)]);});});console[_0x51cbd8(0x213)](_0x51cbd8(0x1f7),_0x303040);var _0x3c7111=document[_0x51cbd8(0x220)](_0x51cbd8(0x286));_0x3c7111[_0x51cbd8(0x291)]=_0x51cbd8(0x290);if(_0x303040){var _0x17e616=_0x303040[_0x51cbd8(0x2bc)]?_0x303040['status']:_0x51cbd8(0x29e),_0x5e1c78=_0x303040[_0x51cbd8(0x299)]?_0x51cbd8(0x26e)+_0x303040[_0x51cbd8(0x299)]+'\x22\x20target=\x22_blank\x22>'+_0x303040['url']+_0x51cbd8(0x1f6):'Not\x20Available',_0x20523b=_0x303040[_0x51cbd8(0x21b)]?_0x303040[_0x51cbd8(0x21b)]:_0x51cbd8(0x29e),_0x386a15=_0x303040[_0x51cbd8(0x2d3)]?_0x51cbd8(0x26e)+_0x303040['ebayItemLink']+_0x51cbd8(0x275):'Not\x20Available';_0x3c7111['innerHTML']=_0x51cbd8(0x28f)+_0x17e616+'</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>URL:\x20'+_0x5e1c78+_0x51cbd8(0x278)+_0x20523b+_0x51cbd8(0x276)+_0x386a15+'</p>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20';}else _0x3c7111[_0x51cbd8(0x244)]=_0x51cbd8(0x1f5);const _0x16ddf5=_0x5b2aa4[_0x51cbd8(0x260)](_0x51cbd8(0x20f));_0x16ddf5[_0x51cbd8(0x29d)](_0x3c7111),await new Promise(_0x428b55=>setTimeout(_0x428b55,0x7d0)),this[_0x51cbd8(0x244)]=_0x5444c7+_0x51cbd8(0x2ce);},_0xbdb89b;}function createDisplayElement(_0x24d1ae){var _0x214312=a0_0x322f4e;console[_0x214312(0x213)](_0x214312(0x262),_0x24d1ae);var _0x3fbc7c=document[_0x214312(0x220)](_0x214312(0x286));_0x3fbc7c[_0x214312(0x291)]=_0x214312(0x290);var _0x550de8=_0x24d1ae['status']==_0x214312(0x215)?_0x214312(0x20d):'error',_0x466190=_0x24d1ae['ebayItemLink']?_0x214312(0x26e)+_0x24d1ae[_0x214312(0x2d3)]+_0x214312(0x275):'Not\x20Available',_0x2bf803=_0x24d1ae[_0x214312(0x299)]?'<a\x20href=\x22'+_0x24d1ae[_0x214312(0x299)]+_0x214312(0x26b):_0x214312(0x29e),_0x4aaecc=new Date()[_0x214312(0x201)](),_0x53620c='<span\x20class=\x22time-tracker\x22>['+_0x4aaecc+']</span>';return _0x3fbc7c[_0x214312(0x244)]=_0x214312(0x249)+_0x550de8+_0x214312(0x2a9)+_0x24d1ae[_0x214312(0x21b)]+_0x214312(0x251)+_0x2bf803+_0x214312(0x251)+_0x466190+_0x214312(0x251)+_0x53620c+_0x214312(0x240),_0x3fbc7c;}function createUpdateListButton(){var _0x10a6a9=a0_0x322f4e,_0x3ecbd1=document['createElement'](_0x10a6a9(0x2c3));return _0x3ecbd1[_0x10a6a9(0x244)]=_0x10a6a9(0x1f0),_0x3ecbd1['className']='update-listing-button',_0x3ecbd1[_0x10a6a9(0x29a)]=async function(_0x1ae8fa){var _0x5e94ea=_0x10a6a9;_0x1ae8fa[_0x5e94ea(0x27e)](),console[_0x5e94ea(0x213)]('Update\x20Listing\x20button\x20clicked'),this['disabled']=!![],this[_0x5e94ea(0x244)]=_0x5e94ea(0x2b7);var _0xc3b0a2=this;while(_0xc3b0a2[_0x5e94ea(0x235)]!=='TR'){_0xc3b0a2=_0xc3b0a2['parentElement'];}console[_0x5e94ea(0x213)](_0xc3b0a2);var _0x46ed64=fetchEbayData(_0xc3b0a2);console['log'](_0x46ed64);var _0x3b3e64=await deductCredits(0.2);if(!_0x3b3e64[_0x5e94ea(0x20d)]){console['log'](_0x5e94ea(0x1fa),_0x3b3e64),this[_0x5e94ea(0x244)]=_0x5e94ea(0x25f)+_0x3b3e64[_0x5e94ea(0x21b)];return;}var _0x333876=await new Promise(_0x4ca8c6=>{var _0x1d2099=_0x5e94ea;chrome[_0x1d2099(0x27f)][_0x1d2099(0x23b)]({'type':_0x1d2099(0x22f),'ebayData':_0x46ed64},function(_0x166762){var _0x54c4bd=_0x1d2099;console[_0x54c4bd(0x213)](_0x166762),_0x4ca8c6(_0x166762);});});console[_0x5e94ea(0x213)](_0x5e94ea(0x1f7),_0x333876),this[_0x5e94ea(0x244)]=_0x5e94ea(0x25e);var _0xb22d2=createDisplayElement(_0x333876[_0x5e94ea(0x2d0)]),_0x31d511=_0xc3b0a2[_0x5e94ea(0x260)]('td[class*=\x22title\x22]');_0x31d511[_0x5e94ea(0x29d)](_0xb22d2);},_0x3ecbd1;}function createSmartRelistButton(){var _0x4d532a=a0_0x322f4e,_0xa42d47=document[_0x4d532a(0x220)](_0x4d532a(0x2c3));return _0xa42d47[_0x4d532a(0x244)]=_0x4d532a(0x252),_0xa42d47[_0x4d532a(0x291)]=_0x4d532a(0x20c),_0xa42d47[_0x4d532a(0x29a)]=async function(_0x15174b){var _0x7f17d1=_0x4d532a;_0x15174b['preventDefault'](),console[_0x7f17d1(0x213)](_0x7f17d1(0x209));var _0x2da505=this;while(_0x2da505[_0x7f17d1(0x235)]!=='TR'){_0x2da505=_0x2da505[_0x7f17d1(0x273)];}console[_0x7f17d1(0x213)](_0x2da505);var _0x4a5d4b=await deductCredits(0x1);if(!_0x4a5d4b[_0x7f17d1(0x20d)]){console[_0x7f17d1(0x213)]('deductCreditsResponse',_0x4a5d4b),this[_0x7f17d1(0x244)]=_0x7f17d1(0x25f)+_0x4a5d4b[_0x7f17d1(0x21b)];return;}await smartRelistItem(_0x2da505);},_0xa42d47;}async function smartRelistItem(_0x1b70bd){var _0x6fe548=a0_0x322f4e;chrome['storage'][_0x6fe548(0x2c2)][_0x6fe548(0x24d)]({'run_status_bulk_lister':!![]});var _0x394152=_0x1b70bd['querySelector'](_0x6fe548(0x23c));_0x394152[_0x6fe548(0x2b9)]=!![],_0x394152[_0x6fe548(0x244)]='Smart\x20Relisting...\x20<span\x20class=\x27loader\x27></span>';var _0x400a07=fetchEbayData(_0x1b70bd);console[_0x6fe548(0x213)](_0x400a07);var _0x142d47=await new Promise(_0x1d9111=>{var _0x3ce5db=_0x6fe548;chrome['runtime'][_0x3ce5db(0x23b)]({'type':_0x3ce5db(0x29c),'ebayData':_0x400a07},function(_0x2eab4d){var _0x562fc8=_0x3ce5db;console[_0x562fc8(0x213)](_0x2eab4d),_0x1d9111(_0x2eab4d);});});console[_0x6fe548(0x213)](_0x6fe548(0x1f7),_0x142d47),_0x394152[_0x6fe548(0x244)]='Smart\x20Relisted';var _0x46e424=createDisplayElement(_0x142d47[_0x6fe548(0x2d0)]),_0x406f77=_0x1b70bd[_0x6fe548(0x260)](_0x6fe548(0x20f));return _0x406f77[_0x6fe548(0x29d)](_0x46e424),_0x142d47;}function createCloneListButton(){var _0x5052ae=a0_0x322f4e,_0x41578f=document[_0x5052ae(0x220)](_0x5052ae(0x2c3));return _0x41578f[_0x5052ae(0x244)]=_0x5052ae(0x1f1),_0x41578f[_0x5052ae(0x291)]=_0x5052ae(0x224),_0x41578f[_0x5052ae(0x29a)]=async function(_0x1aaacd){var _0x3042c6=_0x5052ae;_0x1aaacd[_0x3042c6(0x27e)](),console[_0x3042c6(0x213)](_0x3042c6(0x25a)),this[_0x3042c6(0x2b9)]=!![],this[_0x3042c6(0x244)]=_0x3042c6(0x28a);var _0x31e5b3=this;while(_0x31e5b3[_0x3042c6(0x235)]!=='TR'){_0x31e5b3=_0x31e5b3['parentElement'];}console[_0x3042c6(0x213)](_0x31e5b3);var _0x5c4b59=fetchEbayData(_0x31e5b3);console['log'](_0x5c4b59);var _0x1a0a9a=await deductCredits(0.2);if(!_0x1a0a9a['success']){console['log'](_0x3042c6(0x1fa),_0x1a0a9a),this[_0x3042c6(0x244)]=_0x3042c6(0x25f)+_0x1a0a9a['message'];return;}var _0x2ea6b3=await new Promise(_0x4f244f=>{var _0xbb9069=_0x3042c6;chrome[_0xbb9069(0x27f)][_0xbb9069(0x23b)]({'type':_0xbb9069(0x1f2),'ebayData':_0x5c4b59},function(_0x8456ce){var _0x13801d=_0xbb9069;console[_0x13801d(0x213)](_0x8456ce),_0x4f244f(_0x8456ce);});});console['log']('Response\x20received',_0x2ea6b3),this['innerHTML']='List\x20Cloned';var _0x3a8675=createDisplayElement(_0x2ea6b3[_0x3042c6(0x2d0)][_0x3042c6(0x2d0)][_0x3042c6(0x21b)]),_0x3ca081=_0x31e5b3[_0x3042c6(0x260)]('td[class*=\x22title\x22]');_0x3ca081['appendChild'](_0x3a8675);},_0x41578f;}function findItemNode(_0x5ac891){var _0x5c46c8=a0_0x322f4e,_0x5dcb50=getItemNodes();for(let _0xff8ce1=0x0;_0xff8ce1<_0x5dcb50[_0x5c46c8(0x26d)];_0xff8ce1++){var _0x110250=_0x5dcb50[_0xff8ce1],_0xf9c816=_0x110250[_0x5c46c8(0x260)](_0x5c46c8(0x2a0))[_0x5c46c8(0x248)](_0x5c46c8(0x238));if(_0xf9c816==_0x5ac891)return _0x110250;}}async function waitForElement(_0x94b376,_0x38239f){return new Promise(_0x2d74ba=>{var _0x6fe375=a0_0x48b1;const _0x5a19be=new MutationObserver((_0x2a0c2a,_0x4f7353)=>{var _0x27eaa5=a0_0x48b1;for(let _0x5eb50a of _0x2a0c2a){if(_0x5eb50a['type']==='childList'){const _0x5bcacf=Array[_0x27eaa5(0x28d)](document[_0x27eaa5(0x208)](_0x94b376));for(let _0x433b25 of _0x5bcacf){if(_0x38239f(_0x433b25)){console[_0x27eaa5(0x213)](_0x27eaa5(0x23d),_0x433b25),_0x4f7353['disconnect'](),_0x2d74ba(_0x433b25);break;}}}}});_0x5a19be['observe'](document[_0x6fe375(0x2b6)],{'childList':!![],'subtree':!![]});});}async function endItem(_0x5cbbdd){var _0xd82b7=a0_0x322f4e;console[_0xd82b7(0x213)](_0xd82b7(0x212),_0x5cbbdd);const _0x214de9=_0x5cbbdd[_0xd82b7(0x260)](_0xd82b7(0x2d4));_0x214de9[_0xd82b7(0x26f)]();const _0x2802c7=await waitForElement(_0xd82b7(0x243),_0x23b2f9=>_0x23b2f9[_0xd82b7(0x258)][_0xd82b7(0x217)]()===_0xd82b7(0x1fd));await new Promise(_0x5649ea=>setTimeout(_0x5649ea,0x7d0)),_0x2802c7[_0xd82b7(0x26f)]();const _0x33a410=await waitForElement(_0xd82b7(0x221),_0x3a693d=>_0x3a693d!=null);console[_0xd82b7(0x213)](_0xd82b7(0x1f4),_0x33a410);var _0xee0d8=_0x33a410[_0xd82b7(0x208)](_0xd82b7(0x2c3)),_0x511080;for(let _0x428e60=0x0;_0x428e60<_0xee0d8['length'];_0x428e60++){var _0x22f9f=_0xee0d8[_0x428e60];if(_0x22f9f['innerText']==_0xd82b7(0x1fd)){_0x511080=_0x22f9f;break;}}return console[_0xd82b7(0x213)](_0xd82b7(0x2c7),_0x511080),await new Promise(_0x32361b=>setTimeout(_0x32361b,0x7d0)),_0x511080[_0xd82b7(0x26f)](),'itemEnded';}function createEndItemButton(){var _0x262517=a0_0x322f4e,_0x459bc1=document[_0x262517(0x220)](_0x262517(0x2c3));return _0x459bc1[_0x262517(0x244)]=_0x262517(0x277),_0x459bc1[_0x262517(0x291)]=_0x262517(0x25c),_0x459bc1[_0x262517(0x29a)]=async function(_0x46f962){var _0x1f0e32=_0x262517;_0x46f962[_0x1f0e32(0x27e)](),console[_0x1f0e32(0x213)](_0x1f0e32(0x2ab)),this[_0x1f0e32(0x2b9)]=!![],this['innerHTML']=_0x1f0e32(0x271);var _0x2d19ba=this;while(_0x2d19ba[_0x1f0e32(0x235)]!=='TR'){_0x2d19ba=_0x2d19ba[_0x1f0e32(0x273)];}console[_0x1f0e32(0x213)](_0x2d19ba);var _0x239eae=fetchEbayData(_0x2d19ba);console[_0x1f0e32(0x213)](_0x239eae);var _0xbc4d7a=await new Promise(_0x1372d3=>{var _0x875820=_0x1f0e32;chrome['runtime']['sendMessage']({'type':_0x875820(0x212),'ebayData':_0x239eae},function(_0x2e4aeb){console['log'](_0x2e4aeb),_0x1372d3(_0x2e4aeb);});});console[_0x1f0e32(0x213)](_0x1f0e32(0x1f7),_0xbc4d7a),this[_0x1f0e32(0x244)]='Item\x20Ended';},_0x459bc1;}function a0_0x48b1(_0x3c891d,_0x5c6ae6){var _0x20e698=a0_0x20e6();return a0_0x48b1=function(_0x48b19f,_0x38ed35){_0x48b19f=_0x48b19f-0x1ee;var _0xc05c3d=_0x20e698[_0x48b19f];return _0xc05c3d;},a0_0x48b1(_0x3c891d,_0x5c6ae6);}function enableOpenTitleLinksInNewTab(_0x2fd73e,_0x58bccd){var _0x1f478b=a0_0x322f4e;const _0x642ef2=document[_0x1f478b(0x260)](_0x2fd73e);if(!_0x642ef2){console[_0x1f478b(0x266)]('Container\x20not\x20found.');return;}_0x642ef2[_0x1f478b(0x2c1)](_0x1f478b(0x26f),function(_0x10535f){var _0x36a3d4=_0x1f478b;let _0x3763b6=_0x10535f[_0x36a3d4(0x1f8)];while(_0x3763b6&&_0x3763b6!==_0x642ef2){if(_0x3763b6['matches'](_0x58bccd)){_0x10535f[_0x36a3d4(0x27e)]();const _0xc2a644=_0x3763b6[_0x36a3d4(0x259)];window[_0x36a3d4(0x26c)](_0xc2a644,_0x36a3d4(0x233)),console[_0x36a3d4(0x213)](_0x36a3d4(0x2b0)+_0xc2a644+_0x36a3d4(0x288));return;}_0x3763b6=_0x3763b6[_0x36a3d4(0x273)];}}),console[_0x1f478b(0x213)](_0x1f478b(0x216));}async function getLowPerformingItemNodes(_0x5e9588,_0x220361,_0x4abff2=0x2d0){var _0x4fab65=a0_0x322f4e;const _0x471265=[],_0x426cb7=getItemNodes();for(const _0x2e9485 of _0x426cb7){var _0x5e458a=fetchEbayData(_0x2e9485),_0x31ad96=_0x5e458a[_0x4fab65(0x21e)],_0x4d8bed=_0x5e458a[_0x4fab65(0x22a)];if(isNaN(_0x31ad96)||isNaN(_0x4d8bed)){console[_0x4fab65(0x223)](_0x4fab65(0x27c));continue;}if(!isTimeLessThan(_0x5e458a['timeLeft'],_0x4abff2)){console[_0x4fab65(0x213)](_0x4fab65(0x295),_0x4abff2);continue;}_0x31ad96<=_0x5e9588&&_0x4d8bed<=_0x220361&&_0x471265[_0x4fab65(0x28c)](_0x2e9485);}return _0x471265;}function createStopButton(){var _0x37aa6a=a0_0x322f4e;const _0x6f18bd=document[_0x37aa6a(0x220)]('button');return _0x6f18bd[_0x37aa6a(0x258)]='Stop',_0x6f18bd['className']='stop-button',_0x6f18bd[_0x37aa6a(0x2c1)](_0x37aa6a(0x26f),async _0x4acde6=>{var _0x1016e7=_0x37aa6a;_0x4acde6[_0x1016e7(0x27e)](),console[_0x1016e7(0x213)](_0x1016e7(0x214)),document[_0x1016e7(0x2b4)]=_0x1016e7(0x2cd)+document[_0x1016e7(0x2b4)],_0x6f18bd[_0x1016e7(0x2b9)]=!![],_0x6f18bd[_0x1016e7(0x244)]=_0x1016e7(0x25d),shouldStopProcessing=!![],_0x6f18bd[_0x1016e7(0x258)]=_0x1016e7(0x27d);}),_0x6f18bd;}function createBulkSmartRelistButton(){var _0x1e37c7=a0_0x322f4e;const _0x6da489=document[_0x1e37c7(0x220)](_0x1e37c7(0x2c3));_0x6da489[_0x1e37c7(0x258)]=_0x1e37c7(0x226),_0x6da489['className']=_0x1e37c7(0x21f),_0x6da489[_0x1e37c7(0x2c1)](_0x1e37c7(0x26f),async _0x2148de=>{var _0x5ec4ef=_0x1e37c7;_0x2148de['preventDefault'](),console[_0x5ec4ef(0x213)](_0x5ec4ef(0x2c8)),document[_0x5ec4ef(0x2b4)]=_0x5ec4ef(0x2a1)+document[_0x5ec4ef(0x2b4)],_0x6da489[_0x5ec4ef(0x2b9)]=!![],_0x6da489[_0x5ec4ef(0x244)]='Processing...\x20<span\x20class=\x27custom-loader\x27></span>';var _0x1910d8=document['getElementById'](_0x5ec4ef(0x20b))[_0x5ec4ef(0x1fe)],_0x5b24af=document[_0x5ec4ef(0x284)]('min-view-count-input')['value'],_0x26244c=document['getElementById'](_0x5ec4ef(0x26a))[_0x5ec4ef(0x1fe)],_0x39c743=await getLowPerformingItemNodes(_0x1910d8,_0x5b24af,_0x26244c);console['log'](_0x5ec4ef(0x298),_0x39c743);const _0x1e623d=document[_0x5ec4ef(0x284)](_0x5ec4ef(0x272));var _0x5adf54=_0x1e623d[_0x5ec4ef(0x1fe)];console['log'](_0x5ec4ef(0x241),_0x5adf54),_0x5adf54=parseInt(_0x5adf54,0xa);_0x5adf54>0xa&&(_0x5adf54=0xa);console[_0x5ec4ef(0x213)](_0x5ec4ef(0x29b),_0x39c743),await processLowPerformingItems(_0x39c743,_0x5adf54),_0x6da489[_0x5ec4ef(0x258)]=_0x5ec4ef(0x2a7);var _0x1032df=document['getElementById']('auto-refresh-checkbox');_0x1032df[_0x5ec4ef(0x267)]?(console[_0x5ec4ef(0x213)]('autoRefreshCheckbox\x20checked'),console[_0x5ec4ef(0x213)](_0x5ec4ef(0x231)),document['title']='Refreshing\x20Page\x20in\x205\x20minutes\x20-\x20'+document[_0x5ec4ef(0x2b4)],await waitSomeTime(0x1*0x3c*0x3e8),chrome['runtime']['sendMessage']({'type':_0x5ec4ef(0x2b1)})):(console[_0x5ec4ef(0x213)](_0x5ec4ef(0x1fc)),document['title']=_0x5ec4ef(0x2bb)+document['title']);});var _0x118c98=document['createElement'](_0x1e37c7(0x257));_0x118c98[_0x1e37c7(0x291)]='concurrent-tasks-select',_0x118c98['name']=_0x1e37c7(0x272),_0x118c98['id']=_0x1e37c7(0x272);for(var _0x13de82=0x1;_0x13de82<=0xa;_0x13de82++){var _0x2df6db=document[_0x1e37c7(0x220)](_0x1e37c7(0x20e));_0x2df6db[_0x1e37c7(0x1fe)]=_0x13de82,_0x2df6db[_0x1e37c7(0x2a8)]=_0x13de82,_0x118c98[_0x1e37c7(0x29d)](_0x2df6db);}_0x118c98['value']=0x4,_0x118c98['addEventListener'](_0x1e37c7(0x2ac),function(){var _0x58d6e6=_0x1e37c7;console['log'](_0x58d6e6(0x2a5)),console[_0x58d6e6(0x213)]('select.value',_0x118c98['value']),chrome[_0x58d6e6(0x2af)][_0x58d6e6(0x2c2)][_0x58d6e6(0x24d)]({'maxConcurrentTasks':_0x118c98[_0x58d6e6(0x1fe)]});}),chrome['storage'][_0x1e37c7(0x2c2)][_0x1e37c7(0x203)](_0x1e37c7(0x241),_0x381711=>{var _0x56c576=_0x1e37c7;console[_0x56c576(0x213)](_0x56c576(0x241),_0x381711[_0x56c576(0x241)]),_0x381711[_0x56c576(0x241)]?_0x118c98['value']=_0x381711[_0x56c576(0x241)]:_0x118c98['value']=0x4;});var _0x2ae2ee=document[_0x1e37c7(0x220)](_0x1e37c7(0x22c));_0x2ae2ee[_0x1e37c7(0x2aa)]=_0x1e37c7(0x272),_0x2ae2ee[_0x1e37c7(0x29d)](document['createTextNode'](_0x1e37c7(0x281)));var _0x32d95d=document[_0x1e37c7(0x220)](_0x1e37c7(0x286));_0x32d95d['className']='concurrent-tasks-select-div',_0x32d95d['appendChild'](_0x118c98),_0x32d95d[_0x1e37c7(0x29d)](_0x2ae2ee);var _0x3a21f0=document[_0x1e37c7(0x220)](_0x1e37c7(0x27a));_0x3a21f0['type']=_0x1e37c7(0x296),_0x3a21f0['id']=_0x1e37c7(0x222),_0x3a21f0[_0x1e37c7(0x255)]='auto-refresh-checkbox',_0x3a21f0['value']=_0x1e37c7(0x222),_0x3a21f0[_0x1e37c7(0x267)]=![];var _0x3e4e75=document[_0x1e37c7(0x220)]('label');_0x3e4e75[_0x1e37c7(0x2aa)]=_0x1e37c7(0x222),_0x3e4e75[_0x1e37c7(0x29d)](document[_0x1e37c7(0x292)](_0x1e37c7(0x227)));var _0x5b6527=document[_0x1e37c7(0x220)](_0x1e37c7(0x286));_0x5b6527['className']=_0x1e37c7(0x2ae),_0x5b6527['appendChild'](_0x3a21f0),_0x5b6527[_0x1e37c7(0x29d)](_0x3e4e75),_0x3a21f0[_0x1e37c7(0x2c1)]('change',function(){var _0xbc9988=_0x1e37c7;this[_0xbc9988(0x267)]?(console[_0xbc9988(0x213)](_0xbc9988(0x267)),chrome[_0xbc9988(0x2af)][_0xbc9988(0x2c2)][_0xbc9988(0x24d)]({'autoRefresh':!![]})):(console[_0xbc9988(0x213)](_0xbc9988(0x247)),chrome[_0xbc9988(0x2af)][_0xbc9988(0x2c2)]['set']({'autoRefresh':![]}));}),chrome[_0x1e37c7(0x2af)][_0x1e37c7(0x2c2)][_0x1e37c7(0x203)](_0x1e37c7(0x206),_0x38b54e=>{var _0x3faec3=_0x1e37c7;console['log'](_0x3faec3(0x206),_0x38b54e['autoRefresh']),_0x38b54e['autoRefresh']?_0x3a21f0[_0x3faec3(0x267)]=!![]:_0x3a21f0['checked']=![];});var _0x2cf4cb=document[_0x1e37c7(0x220)](_0x1e37c7(0x257));_0x2cf4cb[_0x1e37c7(0x291)]=_0x1e37c7(0x26a),_0x2cf4cb[_0x1e37c7(0x255)]='filter-by-hours-select',_0x2cf4cb['id']='filter-by-hours-select';var _0x3fde3c=document[_0x1e37c7(0x220)]('option');_0x3fde3c[_0x1e37c7(0x1fe)]='12',_0x3fde3c['text']=_0x1e37c7(0x2ba),_0x2cf4cb['appendChild'](_0x3fde3c);var _0x5b3730=document[_0x1e37c7(0x220)]('option');_0x5b3730[_0x1e37c7(0x1fe)]='24',_0x5b3730[_0x1e37c7(0x2a8)]='24h',_0x2cf4cb[_0x1e37c7(0x29d)](_0x5b3730);var _0x17e5cf=document[_0x1e37c7(0x220)](_0x1e37c7(0x20e));_0x17e5cf[_0x1e37c7(0x1fe)]='48',_0x17e5cf[_0x1e37c7(0x2a8)]='48h',_0x2cf4cb[_0x1e37c7(0x29d)](_0x17e5cf),_0x2cf4cb[_0x1e37c7(0x1fe)]='24',_0x2cf4cb[_0x1e37c7(0x2c1)](_0x1e37c7(0x2ac),function(){var _0xa1182a=_0x1e37c7;console['log'](_0xa1182a(0x202)),console['log'](_0xa1182a(0x232),_0x2cf4cb[_0xa1182a(0x1fe)]),chrome[_0xa1182a(0x2af)]['local'][_0xa1182a(0x24d)]({'filterByHours':_0x2cf4cb['value']});}),chrome['storage'][_0x1e37c7(0x2c2)][_0x1e37c7(0x203)](_0x1e37c7(0x2a2),_0x8d02bd=>{var _0x448a23=_0x1e37c7;console[_0x448a23(0x213)](_0x448a23(0x2a2),_0x8d02bd[_0x448a23(0x2a2)]),_0x8d02bd[_0x448a23(0x2a2)]?_0x2cf4cb[_0x448a23(0x1fe)]=_0x8d02bd['filterByHours']:_0x2cf4cb['value']='24';});var _0x289c62=document[_0x1e37c7(0x220)]('label');_0x289c62['htmlFor']=_0x1e37c7(0x26a),_0x289c62[_0x1e37c7(0x29d)](document['createTextNode'](_0x1e37c7(0x23e)));var _0x601cde=document[_0x1e37c7(0x220)]('div');_0x601cde['className']=_0x1e37c7(0x2c0),_0x601cde[_0x1e37c7(0x29d)](_0x2cf4cb),_0x601cde['appendChild'](_0x289c62);var _0x58fc37=document['createElement'](_0x1e37c7(0x27a));_0x58fc37[_0x1e37c7(0x2cf)]=_0x1e37c7(0x228),_0x58fc37['id']=_0x1e37c7(0x20b),_0x58fc37[_0x1e37c7(0x255)]='min-sold-quantity-input',_0x58fc37['value']='0',_0x58fc37[_0x1e37c7(0x256)]='0',_0x58fc37[_0x1e37c7(0x2b2)]=_0x1e37c7(0x1f3),_0x58fc37['step']='1';var _0x21d7b1=document[_0x1e37c7(0x220)]('label');_0x21d7b1[_0x1e37c7(0x2aa)]=_0x1e37c7(0x20b),_0x21d7b1[_0x1e37c7(0x29d)](document[_0x1e37c7(0x292)](_0x1e37c7(0x297)));var _0x58e3ed=document[_0x1e37c7(0x220)](_0x1e37c7(0x286));_0x58e3ed[_0x1e37c7(0x291)]=_0x1e37c7(0x22e),_0x58e3ed[_0x1e37c7(0x29d)](_0x58fc37),_0x58e3ed[_0x1e37c7(0x29d)](_0x21d7b1),_0x58fc37['addEventListener']('change',function(){var _0x35a0e9=_0x1e37c7;console[_0x35a0e9(0x213)](_0x35a0e9(0x205)),console['log']('minSoldQuantityInput.value',_0x58fc37['value']),chrome['storage']['local']['set']({'minSoldQuantity':_0x58fc37['value']});}),chrome[_0x1e37c7(0x2af)][_0x1e37c7(0x2c2)][_0x1e37c7(0x203)]('minSoldQuantity',_0x48abfe=>{var _0x1ad0d1=_0x1e37c7;console[_0x1ad0d1(0x213)](_0x1ad0d1(0x25b),_0x48abfe['minSoldQuantity']),_0x48abfe[_0x1ad0d1(0x25b)]?_0x58fc37[_0x1ad0d1(0x1fe)]=_0x48abfe[_0x1ad0d1(0x25b)]:_0x58fc37['value']='0';});var _0x285522=document[_0x1e37c7(0x220)](_0x1e37c7(0x27a));_0x285522[_0x1e37c7(0x2cf)]=_0x1e37c7(0x228),_0x285522['id']=_0x1e37c7(0x239),_0x285522[_0x1e37c7(0x255)]='min-view-count-input',_0x285522['value']='0',_0x285522[_0x1e37c7(0x256)]='0',_0x285522[_0x1e37c7(0x2b2)]=_0x1e37c7(0x1f3),_0x285522[_0x1e37c7(0x270)]='1';var _0x27ed12=document[_0x1e37c7(0x220)](_0x1e37c7(0x22c));_0x27ed12['htmlFor']=_0x1e37c7(0x239),_0x27ed12[_0x1e37c7(0x29d)](document[_0x1e37c7(0x292)](_0x1e37c7(0x229)));var _0x36f2b1=document['createElement']('div');_0x36f2b1[_0x1e37c7(0x291)]=_0x1e37c7(0x234),_0x36f2b1['appendChild'](_0x285522),_0x36f2b1[_0x1e37c7(0x29d)](_0x27ed12),_0x285522['addEventListener'](_0x1e37c7(0x2ac),function(){var _0x2699d4=_0x1e37c7;console[_0x2699d4(0x213)](_0x2699d4(0x22d)),console[_0x2699d4(0x213)](_0x2699d4(0x2b3),_0x285522[_0x2699d4(0x1fe)]),chrome[_0x2699d4(0x2af)][_0x2699d4(0x2c2)][_0x2699d4(0x24d)]({'minViewCount':_0x285522[_0x2699d4(0x1fe)]});}),chrome['storage'][_0x1e37c7(0x2c2)][_0x1e37c7(0x203)](_0x1e37c7(0x24a),_0x2aa87b=>{var _0x559ca2=_0x1e37c7;console[_0x559ca2(0x213)]('minViewCount',_0x2aa87b[_0x559ca2(0x24a)]),_0x2aa87b[_0x559ca2(0x24a)]?_0x285522['value']=_0x2aa87b[_0x559ca2(0x24a)]:_0x285522[_0x559ca2(0x1fe)]='0';});var _0x421ce8=document[_0x1e37c7(0x220)](_0x1e37c7(0x286));_0x421ce8[_0x1e37c7(0x291)]=_0x1e37c7(0x245);var _0x442b8c=document['createElement']('p');_0x442b8c['className']=_0x1e37c7(0x230),_0x442b8c[_0x1e37c7(0x258)]=_0x1e37c7(0x236);var _0x354c97=document[_0x1e37c7(0x220)]('p');_0x354c97[_0x1e37c7(0x280)]['fontWeight']=_0x1e37c7(0x268),_0x354c97[_0x1e37c7(0x258)]=_0x1e37c7(0x2c4),_0x421ce8[_0x1e37c7(0x29d)](_0x442b8c),_0x421ce8[_0x1e37c7(0x29d)](_0x354c97);var _0x2897e0=createStopButton(),_0x432275=document[_0x1e37c7(0x220)]('div');_0x432275[_0x1e37c7(0x291)]='bulk-smart-relist-button-container',_0x432275[_0x1e37c7(0x29d)](_0x5b6527),_0x432275['appendChild'](_0x32d95d),_0x432275['appendChild'](_0x601cde),_0x432275[_0x1e37c7(0x29d)](_0x58e3ed),_0x432275[_0x1e37c7(0x29d)](_0x36f2b1);var _0x481ffc=document['createElement'](_0x1e37c7(0x286));_0x481ffc['className']=_0x1e37c7(0x21c),_0x481ffc[_0x1e37c7(0x29d)](_0x6da489),_0x432275[_0x1e37c7(0x29d)](_0x481ffc);var _0x75021f=document[_0x1e37c7(0x220)](_0x1e37c7(0x286));return _0x75021f['className']=_0x1e37c7(0x269),_0x75021f[_0x1e37c7(0x29d)](_0x432275),_0x75021f[_0x1e37c7(0x29d)](_0x421ce8),_0x75021f;}function createFilterByHoursSelect(){var _0x1885d7=a0_0x322f4e,_0x3f1bb4=document[_0x1885d7(0x220)]('select');_0x3f1bb4['className']=_0x1885d7(0x26a),_0x3f1bb4[_0x1885d7(0x255)]=_0x1885d7(0x26a),_0x3f1bb4['id']='filter-by-hours-select';var _0x520a19=document[_0x1885d7(0x220)](_0x1885d7(0x20e));_0x520a19[_0x1885d7(0x1fe)]='12',_0x520a19[_0x1885d7(0x2a8)]=_0x1885d7(0x2ba),_0x3f1bb4['appendChild'](_0x520a19);var _0x54f2df=document[_0x1885d7(0x220)]('option');_0x54f2df[_0x1885d7(0x1fe)]='24',_0x54f2df['text']=_0x1885d7(0x1ff),_0x3f1bb4['appendChild'](_0x54f2df);var _0x985c0f=document[_0x1885d7(0x220)](_0x1885d7(0x20e));_0x985c0f[_0x1885d7(0x1fe)]='48',_0x985c0f[_0x1885d7(0x2a8)]='48h',_0x3f1bb4[_0x1885d7(0x29d)](_0x985c0f),_0x3f1bb4[_0x1885d7(0x1fe)]='24',_0x3f1bb4[_0x1885d7(0x2c1)](_0x1885d7(0x2ac),function(){var _0x383c7b=_0x1885d7;console[_0x383c7b(0x213)](_0x383c7b(0x202)),console[_0x383c7b(0x213)](_0x383c7b(0x232),_0x3f1bb4[_0x383c7b(0x1fe)]),chrome[_0x383c7b(0x2af)]['local'][_0x383c7b(0x24d)]({'filterByHours':_0x3f1bb4[_0x383c7b(0x1fe)]});}),chrome[_0x1885d7(0x2af)]['local'][_0x1885d7(0x203)](_0x1885d7(0x2a2),_0x4cfe9a=>{var _0x49d765=_0x1885d7;console[_0x49d765(0x213)](_0x49d765(0x2a2),_0x4cfe9a['filterByHours']),_0x4cfe9a[_0x49d765(0x2a2)]?_0x3f1bb4[_0x49d765(0x1fe)]=_0x4cfe9a['filterByHours']:_0x3f1bb4[_0x49d765(0x1fe)]='24';});var _0xaf7307=document[_0x1885d7(0x220)](_0x1885d7(0x22c));_0xaf7307[_0x1885d7(0x2aa)]=_0x1885d7(0x26a),_0xaf7307['appendChild'](document[_0x1885d7(0x292)]('Filter\x20By\x20Hours'));var _0x265672=document[_0x1885d7(0x220)](_0x1885d7(0x286));return _0x265672[_0x1885d7(0x291)]=_0x1885d7(0x2c0),_0x265672[_0x1885d7(0x29d)](_0x3f1bb4),_0x265672[_0x1885d7(0x29d)](_0xaf7307),_0x265672;}function a0_0x20e6(){var _0x13b790=['min','select','textContent','href','Clone\x20List\x20button\x20clicked','minSoldQuantity','end-item-button','Stopping...\x20<span\x20class=\x27custom-loader\x27></span>','Listing\x20Updated','Error:\x20','querySelector','4987500AfgodU','createDisplayElement\x20message','concat','toUpperCase','isFulfilled','warn','checked','bold','smart-relist-ui','filter-by-hours-select','\x22\x20target=\x22_blank\x22>Amazon</a>','open','length','<a\x20href=\x22','click','step','Ending\x20Item...\x20<span\x20class=\x27loader\x27></span>','concurrent-tasks-select','parentElement','skuList','\x22\x20target=\x22_blank\x22>View\x20Item</a>','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>','End\x20Item','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>Message:\x20','td[class*=\x22image\x22]\x20img','input','All\x20nodes\x20processed','Error\x20parsing\x20soldQuantity\x20or\x20viewCount','Stopped','preventDefault','runtime','style','Concurrent\x20Tasks','true','Saved\x20SKUS:\x20','getElementById','photos','div','charAt','\x20in\x20a\x20new\x20tab.','5920404xKqDMk','Cloning\x20List...\x20<span\x20class=\x27loader\x27></span>','all','push','from','relist-button','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>Status:\x20','info-box','className','createTextNode','getElementsByClassName','3204536xJlJEr','timeLeft\x20is\x20not\x20less\x20than','checkbox','Min\x20Sold\x20Quantity','Low\x20Performing\x20Nodes:','url','onclick','lowPerformingNodes\x20to\x20process','smartRelist','appendChild','Not\x20Available','3784596aKlOUk','.grid-row','Bulk\x20Smart\x20Relist\x20-\x20','filterByHours','aria-disabled','td[class*=\x22soldQuantity\x22]','select\x20changed','6gzZETV','Items\x20Bulk\x20Smartly\x20Relisted','text','</p>\x0a\x20\x20\x20\x20\x20\x20<p>Message:\x20','htmlFor','End\x20Item\x20button\x20clicked','change','price','auto-refresh-checkbox-div','storage','Opening\x20','refresh_page_and_relist','max','minViewCountInput.value','title','src','body','Updating\x20Listing...\x20<span\x20class=\x27loader\x27></span>','Relist\x20button\x20clicked','disabled','12h','Done\x20-\x20','status','innerText','match','td[class*=\x22availableQuantity\x22]\x20[class*=\x22cell-wrapper\x22]','filter-by-hours-select-div','addEventListener','local','button','Customizable\x20Table\x20on\x20eBay\x20must\x20include:\x20\x27Custom\x20Label\x27,\x20\x27Item\x20Number\x27,\x20\x27Available\x20Quantity\x27,\x20\x27Sold\x20Quantity\x27,\x20\x27Views\x27,\x20\x27Current\x20Price\x27,\x20\x27Start\x20Date\x27','Processing\x20node:\x20','4514060MlDuMc','finalEndListingButton','Bulk\x20Smart\x20Relist\x20button\x20clicked','add','pagination__next','13529565ScQpZv','Node\x20at\x20index\x20','Stop\x20-\x20','\x20Relisted','type','response','1945246DPfAUz','Saving\x20Skus\x20-\x20','ebayItemLink','.toggle-menu.btn','processed-node','td[class*=\x22price\x22]\x20[class*=\x22price__current\x22]','finally','Started\x20processing\x20node\x20at\x20index\x20','Update\x20List','Clone\x20List','cloneList','1000','.se-end-listing\x20element\x20found','<p>Error:\x20No\x20response\x20received</p>','</a>','Response\x20received','target','table[id*=gridData]\x20tbody','deductCreditsResponse','Page\x20','autoRefreshCheckbox\x20not\x20checked','End\x20listing','value','24h','1iUnoYe','toLocaleTimeString','filterByHoursSelect\x20changed','get','td[class*=\x22scheduledStartDate\x22]\x20[class*=\x22text-column\x22]\x20div','minSoldQuantityInput\x20changed','autoRefresh','Relisting\x20','querySelectorAll','Smart\x20Relist\x20button\x20clicked','updateData','min-sold-quantity-input','smart-relist-button','success','option','td[class*=\x22title\x22]','replace','\x20SKUS:\x20','endItem','log','Stop\x20button\x20clicked','itemListed','Event\x20delegation\x20setup\x20complete\x20for\x20opening\x20title\x20links\x20in\x20new\x20tab.','trim','...\x20<span\x20class=\x27loader\x27></span>','classList','Processing\x20stopped','message','action-button-container','customLabel','soldQuantity','bulk-smart-relist-button','createElement','.se-end-listing','auto-refresh-checkbox','error','clone-list-button','relistItem','Bulk\x20Smart\x20Relist\x20Low\x20Performing\x20Items','Auto\x20Refresh\x20Page','number','Min\x20View\x20Count','viewCount','.\x20Ongoing\x20tasks:\x20','label','minViewCountInput\x20changed','min-sold-quantity-input-div','updateListing','smart-relist-explanation','refreshing\x20page\x20in\x205\x20minutes','filterByHoursSelect.value','_blank','min-view-count-input-div','tagName','Welcome\x20to\x20the\x20Smart\x20Relist\x20feature!\x20Set\x20your\x20preferences\x20below\x20to\x20relist\x20items\x20on\x20your\x20eBay\x20account\x20based\x20on\x20ending\x20time,\x20minimum\x20sold\x20quantity,\x20and\x20view\x20count.\x20Choose\x20the\x20number\x20of\x20concurrent\x20listings\x20our\x20AI-enhanced\x20tool\x20will\x20process,\x20optimizing\x20item\x20titles\x20for\x20better\x20visibility.\x20Note:\x20Enabling\x20\x27Auto\x20Refresh\x20Page\x27\x20will\x20refresh\x20the\x20page\x20automatically\x20after\x20relisting.\x20Disable\x20this\x20feature\x20and\x20refresh\x20the\x20page\x20manually\x20to\x20turn\x20it\x20off.','Processing\x20node','data-id','min-view-count-input','availableQty','sendMessage','.smart-relist-button','Element\x20found','Filter\x20By\x20Hours','td[class*=\x22listingId\x22]','</p>\x0a\x20\x20\x20\x20\x20\x20','maxConcurrentTasks','forEach','.fake-menu__item','innerHTML','smart-relist-intro','td[class*=\x22listingSKU\x22]\x20.cell-wrapper','unchecked','getAttribute','\x0a\x20\x20\x20\x20\x20\x20<p>Status:\x20','minViewCount','ebay.active_listings_functions.js\x20loaded','Error\x20processing\x20node:\x20','set','\x20processed.\x20Total\x20processed:\x20','slice','then','</p>\x0a\x20\x20\x20\x20\x20\x20<p>','Smart\x20Relist','scheduledStartDate','.go-to-page\x20.label','name'];a0_0x20e6=function(){return _0x13b790;};return a0_0x20e6();}function isTimeLessThan(_0xe1788f,_0x421166){var _0x259be6=a0_0x322f4e;const _0x5610a4=_0xe1788f[_0x259be6(0x2be)](/(\d+d)?\s*(\d+h)?\s*(\d+m)?\s*(\d+s)?/),_0x3b9c1a=parseInt(_0x5610a4[0x1])||0x0,_0x67963=parseInt(_0x5610a4[0x2])||0x0,_0x1ea2d7=parseInt(_0x5610a4[0x3])||0x0,_0x4a85d8=parseInt(_0x5610a4[0x4])||0x0,_0x51b6b2=_0x3b9c1a*0x18+_0x67963+_0x1ea2d7/0x3c+_0x4a85d8/0xe10;return _0x51b6b2<_0x421166;}function updatePageOnStop(){var _0x1e4eaf=a0_0x322f4e;alert(_0x1e4eaf(0x21a));}async function processLowPerformingItems(_0x347487,_0x7044d5=0x4){var _0x55075a=a0_0x322f4e;let _0x242e97=[],_0x3bec5f=0x0,_0x8ee13=0x0,_0x25d697=![];const _0x63c557=()=>{var _0x2c029a=a0_0x48b1;if(_0x8ee13<_0x347487[_0x2c029a(0x26d)]){console['log']('Preparing\x20to\x20process\x20node\x20at\x20index\x20'+_0x8ee13);const _0x5e94fe=_0x347487[_0x8ee13++],_0x5925cc=processNode(_0x5e94fe)[_0x2c029a(0x250)](()=>{var _0x3404e=_0x2c029a;_0x3bec5f++,console[_0x3404e(0x213)](_0x3404e(0x2cc)+(_0x8ee13-0x1)+_0x3404e(0x24e)+_0x3bec5f);})[_0x2c029a(0x1ee)](()=>{_0x242e97=_0x242e97['filter'](_0x231d6f=>_0x231d6f!==_0x5925cc),_0x63c557();});_0x242e97[_0x2c029a(0x28c)](_0x5925cc),console[_0x2c029a(0x213)](_0x2c029a(0x1ef)+(_0x8ee13-0x1)+_0x2c029a(0x22b)+_0x242e97[_0x2c029a(0x26d)]);}else _0x25d697=!![];};for(let _0x153bd1=0x0;_0x153bd1<_0x7044d5&&_0x153bd1<_0x347487['length'];_0x153bd1++){_0x63c557();}const _0x18ae91=async()=>{var _0x44d134=a0_0x48b1;while(_0x242e97['length']>0x0||!_0x25d697){await Promise[_0x44d134(0x28b)](_0x242e97);}};await _0x18ae91(),console[_0x55075a(0x213)](_0x55075a(0x27b));}async function processNode(_0x24573d){var _0x19d6c3=a0_0x322f4e;console[_0x19d6c3(0x213)](_0x19d6c3(0x2c5)+_0x24573d);try{console['log'](_0x19d6c3(0x237),_0x24573d),await new Promise(_0x3496e2=>setTimeout(_0x3496e2,0x2710));}catch(_0x3c1e41){console[_0x19d6c3(0x223)](_0x19d6c3(0x24c)+_0x24573d,_0x3c1e41);}return _0x24573d[_0x19d6c3(0x219)][_0x19d6c3(0x2c9)](_0x19d6c3(0x2d5)),_0x24573d;}Promise['prototype'][a0_0x322f4e(0x265)]=function(){return Promise['race']([this,new Promise(_0x3b14bd=>setTimeout(_0x3b14bd,0x0))])===this;};