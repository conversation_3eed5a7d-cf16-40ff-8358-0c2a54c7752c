body {
    font-family: Arial, sans-serif;
    background-color: #f2f6fc;
    color: #333;
    padding: 20px;
  }
  
  .container {
    max-width: 650px;
    margin: auto;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  
  h1, h2 {
    text-align: center;
  }
  
  .red { color: #E53238; }
  .blue { color: #0064D2; }
  
  .step {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 15px;
  }
  
  textarea, input {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  
  .buttons {
    text-align: center;
    margin-top: 15px;
  }
  
  button {
    padding: 10px 12px;
    margin: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: white;
  }
  
  #generateBtn { background-color: #28a745; }
  #generateBtn:disabled { background-color: #bbb; cursor: not-allowed; }
  #exampleBtn { background-color: #007bff; }
  #clearBtn { background-color: #dc3545; }
  #downloadBtn { background-color: #F5AF02; color: black; }
  
  #titlesList li {
    background-color: #e9f5ff;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 5px;
  }
  

  .title-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .example-title {
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  
  .delete-btn {
    background-color: transparent;
    border: none;
    color: #dc3545;
    font-size: 18px;
    cursor: pointer;
    margin-left: 5px;
  }
  
  .delete-btn:hover {
    color: #b02a37;
  }
  