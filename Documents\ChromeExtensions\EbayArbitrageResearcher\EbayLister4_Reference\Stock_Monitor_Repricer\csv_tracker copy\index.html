<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<title>CSV GSPR Tool</title>
<link rel="stylesheet" href="styles.css" />
</head>
<body>
    <h1>CSV GSPR Tool</h1>
    <div>
        <label>
            <input type="checkbox" id="debugModeSwitch" /> Debug Mode
        </label>
    </div>
    

    <div>
        <h2>Upload CSV</h2>
        <input type="file" id="csvUpload" accept=".csv" />
        <button id="restoreFromStorageBtn">Restore From Saved Data</button>
        <button id="clearDataBtn">Clear Data</button>
    </div>

    <div id="headerSelectionContainer" style="display:none;">
        <h2>Select Headers</h2>
        <div id="headerCheckboxes"></div>
        <button id="confirmHeadersBtn">Confirm Headers</button>
    </div>

    <div id="mainSection" style="display:none;">
        <h2>Data Table</h2>
        <!-- Scrollable container -->
        <div class="table-container">
            <table id="dataTable"></table>
        </div>

        <div>
            <h3>Actions</h3>
            <div id="actionCheckboxes"></div>
            <label>Concurrency: <input type="number" id="concurrencyInput" value="1" min="1" /></label>
            <button id="runActionsBtn">Run Selected Actions</button>
            
            <button id="downloadCsvBtn">Download CSV</button>
            <button id="reUploadCsvBtn">Re-upload CSV</button>
        </div>

        <div>
            <label>Start at line: <input type="number" id="startLineInput" value="0" min="0"></label>
        </div>
        

        <div>
            <h3>Stats</h3>
            <div>Processed: <span id="processedCount">0</span> / <span id="totalCount">0</span></div>
            <div>Unprocessed: <span id="unprocessedCount">0</span></div>
            <div>
                <label>
                    <input type="checkbox" id="showUnprocessedOnly" /> Show Only Unprocessed
                </label>
            </div>
            <div>Last Action: <span id="statusMessage"></span></div>
        </div>
        

        <!-- Hidden input to re-upload CSV -->
        <input type="file" id="reUploadInput" accept=".csv" style="display:none;"/>
    </div>

<script src="../../libraries/papaparse.min.js"></script>
<script src="script.js"></script>
</body>
</html>
