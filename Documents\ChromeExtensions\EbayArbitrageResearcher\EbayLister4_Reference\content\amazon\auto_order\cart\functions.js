const a0_0xd94402=a0_0x2e1d;(function(_0x4a7f8e,_0xde99fc){const _0x12d356=a0_0x2e1d,_0x2648c4=_0x4a7f8e();while(!![]){try{const _0x5f5e64=parseInt(_0x12d356(0x1e4))/0x1*(-parseInt(_0x12d356(0x1f4))/0x2)+-parseInt(_0x12d356(0x1c9))/0x3*(parseInt(_0x12d356(0x1d8))/0x4)+parseInt(_0x12d356(0x1ca))/0x5+parseInt(_0x12d356(0x1cb))/0x6*(parseInt(_0x12d356(0x1f6))/0x7)+-parseInt(_0x12d356(0x1f5))/0x8*(parseInt(_0x12d356(0x1cf))/0x9)+-parseInt(_0x12d356(0x1ef))/0xa*(parseInt(_0x12d356(0x1c2))/0xb)+parseInt(_0x12d356(0x1ee))/0xc;if(_0x5f5e64===_0xde99fc)break;else _0x2648c4['push'](_0x2648c4['shift']());}catch(_0xdeef15){_0x2648c4['push'](_0x2648c4['shift']());}}}(a0_0x6af2,0x196b5),console[a0_0xd94402(0x1d3)](a0_0xd94402(0x1d5)));function getGoToCartButton(){const _0x1831b3=a0_0xd94402;var _0x1bf1d8=document[_0x1831b3(0x1f7)](_0x1831b3(0x1d9));return _0x1bf1d8;}function getProceedToCheckout(){const _0x56284b=a0_0xd94402;var _0x2ab6a5=document[_0x56284b(0x1f7)](_0x56284b(0x1ed));return _0x2ab6a5;}async function removeAllCartItems(_0x1ab296){const _0x15ca70=a0_0xd94402;while(!![]){var _0x23fe03=[...document[_0x15ca70(0x1c7)](_0x15ca70(0x1d0))];if(_0x23fe03['length']===0x0){console[_0x15ca70(0x1d3)](_0x15ca70(0x1df));break;}let _0x36c11b=![];for(const _0x282ce9 of _0x23fe03){const _0x18d7d1=_0x282ce9[_0x15ca70(0x1d7)]['parentElement']['parentElement'][_0x15ca70(0x1d7)],_0x3691fd=[..._0x18d7d1['querySelectorAll']('a')],_0x405cf1=_0x3691fd[_0x15ca70(0x1c8)](_0x181fa7=>{const _0x43e4da=_0x15ca70;if(!_0x181fa7[_0x43e4da(0x1f0)])return![];return _0x1ab296[_0x43e4da(0x1c8)](_0x2a1901=>_0x181fa7['href'][_0x43e4da(0x1c6)](_0x2a1901));});if(_0x405cf1){console[_0x15ca70(0x1d3)](_0x15ca70(0x1d1),_0x282ce9);continue;}console[_0x15ca70(0x1d3)]('Removing\x20Cart\x20Item:',_0x282ce9),_0x282ce9[_0x15ca70(0x1f7)](_0x15ca70(0x1f8))['click'](),await new Promise(_0x172b98=>setTimeout(_0x172b98,0x1f4)),_0x36c11b=!![];break;}if(!_0x36c11b){console[_0x15ca70(0x1d3)](_0x15ca70(0x1dd));break;}}}async function setGiftOptions(){const _0x6cbed5=a0_0xd94402;var _0x214691=document[_0x6cbed5(0x1f7)](_0x6cbed5(0x1cd));if(_0x214691){_0x214691[_0x6cbed5(0x1cc)](),await new Promise(_0x1b0466=>setTimeout(_0x1b0466,0x3e8));return;}var _0x214691=document[_0x6cbed5(0x1f7)](_0x6cbed5(0x1e9));if(_0x214691){_0x214691[_0x6cbed5(0x1cc)](),await new Promise(_0x2152dd=>setTimeout(_0x2152dd,0x3e8));return;}return;}async function setQuantityViaTextField(_0x2b6159,_0x5c50ab){const _0x295e69=a0_0xd94402,_0x54c1f9=document[_0x295e69(0x1f7)]('[data-asin=\x22'+_0x2b6159+'\x22]');if(!_0x54c1f9){console['warn']('Could\x20not\x20find\x20cart\x20item\x20with\x20data-asin=\x22'+_0x2b6159+'\x22');return;}const _0x41d939=getCurrentQty(_0x54c1f9);if(_0x41d939!==null&&_0x41d939===_0x5c50ab){console['log'](_0x295e69(0x1e5)+_0x2b6159+_0x295e69(0x1dc)+_0x5c50ab+_0x295e69(0x1db));return;}const _0xb04a0a=_0x54c1f9['querySelector']('.sc-quantity-textfield-input-group');if(!_0xb04a0a){console[_0x295e69(0x1de)]('Could\x20not\x20find\x20.sc-quantity-textfield-input-group\x20for\x20ASIN='+_0x2b6159);return;}_0xb04a0a[_0x295e69(0x1c3)][_0x295e69(0x1c4)](_0x295e69(0x1f3));const _0x3f048d=_0x54c1f9[_0x295e69(0x1f7)](_0x295e69(0x1e6));_0x3f048d&&_0x3f048d[_0x295e69(0x1c3)][_0x295e69(0x1c4)](_0x295e69(0x1f3));const _0x570fdc=_0xb04a0a[_0x295e69(0x1f7)](_0x295e69(0x1eb));if(!_0x570fdc){console[_0x295e69(0x1de)](_0x295e69(0x1c5)+_0x2b6159);return;}_0x570fdc[_0x295e69(0x1ea)]=_0x5c50ab,await waitForUpdateButtonAndClick(_0x54c1f9),await waitForQuantity(_0x2b6159,_0x5c50ab,0x1388),console[_0x295e69(0x1d3)](_0x295e69(0x1e5)+_0x2b6159+_0x295e69(0x1e7)+_0x5c50ab+'!');}function a0_0x2e1d(_0x49fabc,_0x2bc615){const _0x6af28a=a0_0x6af2();return a0_0x2e1d=function(_0x2e1db9,_0x341e43){_0x2e1db9=_0x2e1db9-0x1c2;let _0x26e122=_0x6af28a[_0x2e1db9];return _0x26e122;},a0_0x2e1d(_0x49fabc,_0x2bc615);}function getCurrentQty(_0x87a296){const _0x5286aa=a0_0xd94402,_0x16b9fe=_0x87a296[_0x5286aa(0x1f7)](_0x5286aa(0x1f2));if(_0x16b9fe){const _0x42e3ce=parseInt(_0x16b9fe[_0x5286aa(0x1ec)](_0x5286aa(0x1e1))||'',0xa);if(!isNaN(_0x42e3ce))return _0x42e3ce;}const _0x31c3ee=parseInt(_0x87a296[_0x5286aa(0x1ec)](_0x5286aa(0x1ce))||'',0xa);if(!isNaN(_0x31c3ee))return _0x31c3ee;const _0x2e4bc4=_0x87a296[_0x5286aa(0x1f7)](_0x5286aa(0x1eb));if(_0x2e4bc4){const _0x4a4395=parseInt(_0x2e4bc4[_0x5286aa(0x1ea)]||'',0xa);if(!isNaN(_0x4a4395))return _0x4a4395;}return null;}function a0_0x6af2(){const _0x14a811=['6222744ocfjUf','1380UmskOD','href','Update\x20button\x20is\x20still\x20present\x20after\x20several\x20clicks\x20—\x20continuing\x20anyway.','[name=\x22sc-quantity\x22]\x20[data-a-selector=\x22spinbutton\x22]','sc-hidden','204458AUfgRD','592624RxuIIX','1176VJZmab','querySelector','input','12507dgIjtU','classList','remove','Could\x20not\x20find\x20.sc-quantity-textfield\x20for\x20ASIN=','includes','querySelectorAll','some','494523KwGJjA','104725LtEnTv','2238MrmAIB','click','.sc-gift-option\x20input','data-quantity','9JHeqSs','#sc-active-cart\x20.sc-action-delete','Skipping\x20item\x20with\x20an\x20exception:\x20','.sc-quantity-update-button\x20a[data-action=\x22update\x22]','log','Clicking\x20Update\x20button\x20(attempt\x20#','Amazon\x20Auto\x20Order\x20Cart\x20functions\x20Script\x20Loaded','\x20on\x20ASIN=','parentElement','4xjqYVS','#sw-gtc\x20a','[data-asin=\x22','.\x20Skipping\x20update.',']\x20Quantity\x20already\x20','No\x20items\x20removed\x20in\x20this\x20pass.\x20Likely\x20all\x20items\x20have\x20exceptions.','warn','No\x20more\x20cart\x20items\x20found.','Update\x20button\x20disappeared\x20after\x20click\x20—\x20likely\x20successful.','aria-valuenow','Update\x20button\x20never\x20appeared\x20in\x20the\x20DOM.',')...','1DwotTJ','[ASIN=','.sc-update-link.sc-hidden',']\x20Successfully\x20set\x20quantity\x20to\x20','Item\x20[ASIN=','#sc-buy-box-gift-checkbox','value','.sc-quantity-textfield','getAttribute','input[name=\x22proceedToRetailCheckout\x22]'];a0_0x6af2=function(){return _0x14a811;};return a0_0x6af2();}async function waitForUpdateButtonAndClick(_0x45f2ce){const _0x25f760=a0_0xd94402,_0x5c241d=0x1388,_0x257d8d=0x190;let _0x364a58=0x0,_0xa35939=null;while(_0x364a58<_0x5c241d){_0xa35939=_0x45f2ce[_0x25f760(0x1f7)](_0x25f760(0x1d2));if(_0xa35939)break;await new Promise(_0x2e73b3=>setTimeout(_0x2e73b3,_0x257d8d)),_0x364a58+=_0x257d8d;}if(!_0xa35939)throw new Error(_0x25f760(0x1e2));for(let _0x58a9ea=0x1;_0x58a9ea<=0x3;_0x58a9ea++){console[_0x25f760(0x1d3)](_0x25f760(0x1d4)+_0x58a9ea+_0x25f760(0x1e3)),_0xa35939[_0x25f760(0x1cc)](),await new Promise(_0x29e6f1=>setTimeout(_0x29e6f1,0x320)),_0xa35939=_0x45f2ce[_0x25f760(0x1f7)](_0x25f760(0x1d2));if(!_0xa35939){console[_0x25f760(0x1d3)](_0x25f760(0x1e0));return;}}console['warn'](_0x25f760(0x1f1));}function waitForQuantity(_0x22bf06,_0x1cd872,_0x298e29=0x1388){const _0x9a195a=0x190;let _0x1ac334=0x0;return new Promise((_0x5478aa,_0x3a284b)=>{const _0x6eab5c=setInterval(()=>{const _0x30b2e3=a0_0x2e1d,_0x902219=document[_0x30b2e3(0x1f7)](_0x30b2e3(0x1da)+_0x22bf06+'\x22]');if(!_0x902219){_0x1ac334+=_0x9a195a;_0x1ac334>=_0x298e29&&(clearInterval(_0x6eab5c),_0x3a284b(new Error(_0x30b2e3(0x1e8)+_0x22bf06+']\x20not\x20found\x20in\x20DOM\x20after\x20re-render.')));return;}const _0x598a23=getCurrentQty(_0x902219);if(_0x598a23===_0x1cd872)return clearInterval(_0x6eab5c),_0x5478aa(!![]);_0x1ac334+=_0x9a195a,_0x1ac334>=_0x298e29&&(clearInterval(_0x6eab5c),_0x3a284b(new Error('Timed\x20out\x20waiting\x20for\x20quantity='+_0x1cd872+_0x30b2e3(0x1d6)+_0x22bf06+'.')));},_0x9a195a);});}