var a0_0x1fcb09=a0_0x37e6;(function(_0x4f22c7,_0xbc29d6){var _0x132f2e=a0_0x37e6,_0x3441fe=_0x4f22c7();while(!![]){try{var _0x5b30e9=-parseInt(_0x132f2e(0x161))/0x1*(parseInt(_0x132f2e(0x16a))/0x2)+-parseInt(_0x132f2e(0x14d))/0x3+-parseInt(_0x132f2e(0x15a))/0x4+-parseInt(_0x132f2e(0x177))/0x5+parseInt(_0x132f2e(0x175))/0x6*(parseInt(_0x132f2e(0x149))/0x7)+parseInt(_0x132f2e(0x14f))/0x8+parseInt(_0x132f2e(0x170))/0x9;if(_0x5b30e9===_0xbc29d6)break;else _0x3441fe['push'](_0x3441fe['shift']());}catch(_0x1cba4a){_0x3441fe['push'](_0x3441fe['shift']());}}}(a0_0xe639,0xe0d8a));function a0_0x37e6(_0x3faf65,_0x51b850){var _0xe63996=a0_0xe639();return a0_0x37e6=function(_0x37e601,_0x527e32){_0x37e601=_0x37e601-0x144;var _0x4533fe=_0xe63996[_0x37e601];return _0x4533fe;},a0_0x37e6(_0x3faf65,_0x51b850);}var amazonBrandToCheck=getProductBrand();function a0_0xe639(){var _0x3d3849=['location','https://cdn1.iconfinder.com/data/icons/color-bold-style/21/30-512.png','378982QScdgX','indexOf','title','\x20:\x0a','scanning\x20for\x20vero','\x0aTitle\x20Contains\x20','11892879TUuuwt','local','140','push','height','5445552CnwdGM','veroBrand','2687245nuoBbd','get','appendChild','\x0averoBrandWithoutInc:\x20\x0a','getURL','src','/dp/','7albAWX','VeroBrands:\x20\x0a\x0a','toLowerCase','veroBrandWithOutWhiteSpace','789483KyIBNh','getElementById','3335304lBahYm','replace','checkIfBrandIsVero','/VeroList.txt','set','log','createElement','img','length','storage','runtime','665568qlXOXx','filter','veroBrands','\x0averoBrand:\x20\x0a','includes','productTitle','\x0averoBrandWithOutWhiteSpace:\x20\x0a','4tYieAa','width','href','toString','https://upload.wikimedia.org/wikipedia/en/thumb/0/05/Vero_Insurance_logo.svg/1280px-Vero_Insurance_logo.svg.png','veroBrandDetectedList','split'];a0_0xe639=function(){return _0x3d3849;};return a0_0xe639();}amazonBrandToCheck=amazonBrandToCheck[a0_0x1fcb09(0x14b)]();var veroBrandDetectedList=[],amazonTitle=getProductTitle();amazonTitle=amazonTitle[a0_0x1fcb09(0x14b)]();window[a0_0x1fcb09(0x168)][a0_0x1fcb09(0x163)][a0_0x1fcb09(0x16b)](a0_0x1fcb09(0x148))>-0x1&&(console[a0_0x1fcb09(0x154)](a0_0x1fcb09(0x16e)),scanForVero());async function getVeroListText(){var _0x2b83e3=a0_0x1fcb09,_0x388f03=[],_0x1951c0=await fetch(chrome[_0x2b83e3(0x159)][_0x2b83e3(0x146)](_0x2b83e3(0x152)));_0x1951c0=await _0x1951c0['text'](),_0x388f03=_0x1951c0[_0x2b83e3(0x167)]('\x0a'),_0x388f03=_0x388f03[_0x2b83e3(0x15b)](function(_0x58cfbb){return _0x58cfbb!='';});for(let _0x56d283=0x0;_0x56d283<_0x388f03['length'];_0x56d283++){var _0x2af8dd=_0x388f03[_0x56d283];_0x2af8dd=_0x2af8dd['trim'](),_0x388f03[_0x56d283]=_0x2af8dd;}return _0x388f03;}async function scanForVero(){var _0x265c68=a0_0x1fcb09,{veroBrands:_0x16e3e9}=await chrome[_0x265c68(0x158)][_0x265c68(0x171)][_0x265c68(0x178)]([_0x265c68(0x15c)]);if(!_0x16e3e9){var _0x36deb4=await getVeroListText();_0x16e3e9=_0x36deb4,await chrome[_0x265c68(0x158)][_0x265c68(0x171)][_0x265c68(0x153)]({'veroBrands':_0x16e3e9});}await checkVeroBrandWithList(_0x16e3e9);try{await isBrandVero();}catch(_0x52b172){}}function checkIfBrandIsVero(){var _0x2dcc8b=a0_0x1fcb09;return console[_0x2dcc8b(0x154)](_0x2dcc8b(0x151)),new Promise(_0x5b5c50=>{var _0x3e6831=_0x2dcc8b;console[_0x3e6831(0x154)](_0x3e6831(0x166),veroBrandDetectedList),veroBrandDetectedList[_0x3e6831(0x157)]>0x0?_0x5b5c50(!![]):_0x5b5c50(![]);});}function isBrandVero(){return new Promise(_0x574fde=>{var _0x26e050=a0_0x37e6;if(veroBrandDetectedList['length']>0x0){var _0x151854=_0x26e050(0x14a)+veroBrandDetectedList[_0x26e050(0x164)]()['replace'](/,/g,'\x0a'),_0x3b1c15=_0x26e050(0x165),_0x4dbf3f=document[_0x26e050(0x155)](_0x26e050(0x156));_0x4dbf3f[_0x26e050(0x147)]=_0x3b1c15,_0x4dbf3f[_0x26e050(0x162)]=_0x26e050(0x172),_0x4dbf3f[_0x26e050(0x174)]='60',_0x4dbf3f[_0x26e050(0x16c)]=_0x151854;var _0x4130f3=document[_0x26e050(0x14e)](_0x26e050(0x15f));_0x4130f3[_0x26e050(0x144)](_0x4dbf3f),_0x574fde();}else{var _0xd813bc=_0x26e050(0x169),_0x4dbf3f=document[_0x26e050(0x155)](_0x26e050(0x156));_0x4dbf3f[_0x26e050(0x147)]=_0xd813bc,_0x4dbf3f['width']='50',_0x4dbf3f[_0x26e050(0x174)]='30';var _0x4130f3=document[_0x26e050(0x14e)](_0x26e050(0x15f));_0x4130f3['appendChild'](_0x4dbf3f),_0x574fde();}});}function checkVeroInAmazonTitle(_0x186809,_0x281817){var _0x3c5e82=a0_0x1fcb09,_0x4c2b97=amazonTitle[_0x3c5e82(0x167)]('\x20');for(let _0x4b6561=0x0;_0x4b6561<_0x4c2b97['length'];_0x4b6561++){var _0x4e8dfa=_0x4c2b97[_0x4b6561];if(_0x4e8dfa===_0x186809){var _0x4a7753='\x0aTitle\x20Contains\x20'+_0x281817+_0x3c5e82(0x16d)+_0x186809;veroBrandDetectedList['push'](_0x4a7753);}}if(_0x186809[_0x3c5e82(0x15e)]('\x20')){if(amazonTitle[_0x3c5e82(0x15e)](_0x186809)){var _0x4a7753=_0x3c5e82(0x16f)+_0x281817+_0x3c5e82(0x16d)+_0x186809;veroBrandDetectedList['push'](_0x4a7753);}}}function checkVeroBrandWithList(_0x3362e1){return new Promise(_0x54643d=>{var _0x5639f5=a0_0x37e6;for(var _0x15bcb6=0x0;_0x15bcb6<_0x3362e1[_0x5639f5(0x157)];++_0x15bcb6){var _0x1386a2=_0x3362e1[_0x15bcb6]['toLowerCase']();_0x1386a2['includes'](amazonBrandToCheck)&&veroBrandDetectedList[_0x5639f5(0x173)](_0x5639f5(0x15d)+_0x1386a2);amazonBrandToCheck[_0x5639f5(0x15e)](_0x1386a2)&&veroBrandDetectedList[_0x5639f5(0x173)](_0x5639f5(0x15d)+_0x1386a2);checkVeroInAmazonTitle(_0x1386a2,_0x5639f5(0x176));var _0x559326=_0x1386a2[_0x5639f5(0x150)](/\s/g,'');_0x559326[_0x5639f5(0x15e)](amazonBrandToCheck)&&veroBrandDetectedList[_0x5639f5(0x173)](_0x5639f5(0x160)+_0x559326);amazonBrandToCheck[_0x5639f5(0x15e)](_0x559326)&&veroBrandDetectedList[_0x5639f5(0x173)]('\x0averoBrandWithOutWhiteSpace:\x20\x0a'+_0x559326);checkVeroInAmazonTitle(_0x559326,_0x5639f5(0x14c));var _0x4af965=_0x1386a2;_0x4af965=_0x4af965[_0x5639f5(0x150)](',\x20inc','');var _0x4af965=_0x4af965[_0x5639f5(0x150)]('inc',''),_0x4af965=_0x4af965[_0x5639f5(0x150)](',','');checkVeroInAmazonTitle(_0x4af965,'veroBrandWithoutInc'),_0x4af965['includes'](amazonBrandToCheck)&&veroBrandDetectedList[_0x5639f5(0x173)](_0x5639f5(0x145)+_0x4af965),amazonBrandToCheck[_0x5639f5(0x15e)](_0x4af965)&&veroBrandDetectedList[_0x5639f5(0x173)](_0x5639f5(0x145)+_0x4af965),_0x54643d();}});}