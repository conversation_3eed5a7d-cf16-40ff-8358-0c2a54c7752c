function a0_0x4644(){var _0x2c404c=['continue','.se-field-card__container','sendMessage','waiting\x20for\x20element\x20with\x20name\x20condition','7372LTVvqh','1014462UJSZjv','innerText','toLowerCase','log','1330875FgCuTA','input\x20exists\x20chooseCategoryIfExists','found\x20new\x20condition\x20element','46712DbRPug','continue-btn','checked','click','error','eBay\x20identify\x20product\x20-\x20continue\x20without\x20matching\x20#5','forEach','button','1051322MVsJnq','title','includes','prelist-radix__next-action','1887NYxXXo','9tLCzja','300631JzMadL','disabled','clicked_continued_without_matching','className','querySelector','chooseCategoryIfExists','Continue\x20button\x20not\x20found.','.prelist-radix__next-action','Ebay\x20Pre\x20List\x20Identify\x20Functions\x20initialized','[name=\x22condition\x22]','.lightbox-dialog__main','querySelectorAll','7418590FFRkXP','Continue\x20button\x20not\x20found','10ZWIMnI'];a0_0x4644=function(){return _0x2c404c;};return a0_0x4644();}var a0_0x25c3dd=a0_0x549f;(function(_0x44dbe6,_0x12f879){var _0x2875cc=a0_0x549f,_0x2b8007=_0x44dbe6();while(!![]){try{var _0x963f02=parseInt(_0x2875cc(0x1ec))/0x1+parseInt(_0x2875cc(0x1e6))/0x2+parseInt(_0x2875cc(0x1ea))/0x3*(parseInt(_0x2875cc(0x1d6))/0x4)+-parseInt(_0x2875cc(0x1fa))/0x5*(parseInt(_0x2875cc(0x1d7))/0x6)+-parseInt(_0x2875cc(0x1db))/0x7+parseInt(_0x2875cc(0x1de))/0x8*(-parseInt(_0x2875cc(0x1eb))/0x9)+-parseInt(_0x2875cc(0x1f8))/0xa;if(_0x963f02===_0x12f879)break;else _0x2b8007['push'](_0x2b8007['shift']());}catch(_0x441e00){_0x2b8007['push'](_0x2b8007['shift']());}}}(a0_0x4644,0xad3ba),console[a0_0x25c3dd(0x1da)](a0_0x25c3dd(0x1f4)));function clickContinueWithoutMatch(){var _0x341b80=a0_0x25c3dd,_0xada902=document['querySelector'](_0x341b80(0x1f3));!_0xada902&&(_0xada902=document[_0x341b80(0x1f0)]('[class*=\x22radix__continue-btn\x22]')),_0xada902['click']();}function wait(_0x3cb699){return new Promise(_0x239d6c=>{setTimeout(_0x239d6c,_0x3cb699);});}async function selectNewCondition(){var _0x4e0341=a0_0x25c3dd;console[_0x4e0341(0x1da)](_0x4e0341(0x1fe));var _0x42e683;try{_0x42e683=await waitForElementVanillaJs(_0x4e0341(0x1f5),0x1388),document['title']=_0x4e0341(0x1dd),console[_0x4e0341(0x1da)](_0x4e0341(0x1dd));var _0x2d3717=document[_0x4e0341(0x1f0)](_0x4e0341(0x1f5));_0x2d3717[_0x4e0341(0x1e1)](),await new Promise(_0x43f454=>setTimeout(_0x43f454,0x3e8));!_0x2d3717[_0x4e0341(0x1e0)]&&await selectNewCondition();return;}catch(_0x3821e1){throw new Error('element\x20with\x20name\x20condition\x20not\x20found');}}async function ClickContinueToListing(){var _0x241d80=a0_0x25c3dd,_0x27e2c4=document[_0x241d80(0x1f0)]('.condition-dialog-radix__continue-btn');if(!_0x27e2c4){const _0x2dbcdc=document[_0x241d80(0x1f7)](_0x241d80(0x1e5));for(const _0x2377b3 of _0x2dbcdc){if(_0x2377b3[_0x241d80(0x1d8)][_0x241d80(0x1d9)]()['includes'](_0x241d80(0x1fb))){_0x27e2c4=_0x2377b3;break;}}}if(!_0x27e2c4){const _0x5cc529=document[_0x241d80(0x1f7)](_0x241d80(0x1e5));for(const _0x15c7f1 of _0x5cc529){if(_0x15c7f1[_0x241d80(0x1ef)]['toLowerCase']()[_0x241d80(0x1e8)](_0x241d80(0x1df))){_0x27e2c4=_0x15c7f1;break;}}}if(!_0x27e2c4){const _0xdea8f6=document[_0x241d80(0x1f7)]('button');for(const _0x333049 of _0xdea8f6){if(_0x333049['className'][_0x241d80(0x1d9)]()['includes'](_0x241d80(0x1e9))){_0x27e2c4=_0x333049;break;}}}_0x27e2c4[_0x241d80(0x1ed)]&&(await selectNewCondition(),await new Promise(_0x197ebd=>setTimeout(_0x197ebd,0x1f4)));if(_0x27e2c4)_0x27e2c4['click']();else{console[_0x241d80(0x1e2)](_0x241d80(0x1f2));throw new Error(_0x241d80(0x1f9));}}async function chooseCategoryIfExists(_0x3a6e08){var _0x199e97=a0_0x25c3dd;console['log'](_0x199e97(0x1f1));var _0x3db623=document['querySelector'](_0x199e97(0x1f6));if(_0x3db623){console[_0x199e97(0x1da)](_0x199e97(0x1dc));var _0x386cc6=document[_0x199e97(0x1f0)](_0x199e97(0x1fc));_0x386cc6&&(chrome['runtime'][_0x199e97(0x1fd)]({'type':_0x199e97(0x1ee),'productData':_0x3a6e08},function(_0x5cc31f){var _0x4ba048=_0x199e97;document[_0x4ba048(0x1e7)]=_0x4ba048(0x1e3),sentMessage=!![],_0x386cc6[_0x4ba048(0x1e1)]();}),await wait(0x1f4));}}function a0_0x549f(_0x56e519,_0x5059ad){var _0x4644ea=a0_0x4644();return a0_0x549f=function(_0x549f27,_0x3e9ea6){_0x549f27=_0x549f27-0x1d6;var _0x5718ed=_0x4644ea[_0x549f27];return _0x5718ed;},a0_0x549f(_0x56e519,_0x5059ad);}function continueToListing(){var _0x4b0ec4=a0_0x25c3dd,_0x228cd2=document[_0x4b0ec4(0x1f7)](_0x4b0ec4(0x1e5)),_0x378eef;_0x228cd2['forEach'](function(_0x1fa60b){var _0x2b4e2a=_0x4b0ec4;if(_0x1fa60b['textContent'][_0x2b4e2a(0x1d9)]()[_0x2b4e2a(0x1e8)](_0x2b4e2a(0x1fb))){_0x378eef=_0x1fa60b;return;}});!_0x378eef&&_0x228cd2[_0x4b0ec4(0x1e4)](function(_0x597c40){var _0x2e7cc6=_0x4b0ec4;if(_0x597c40[_0x2e7cc6(0x1ef)][_0x2e7cc6(0x1d9)]()[_0x2e7cc6(0x1e8)](_0x2e7cc6(0x1df))){_0x378eef=_0x597c40;return;}});if(_0x378eef)_0x378eef[_0x4b0ec4(0x1e1)]();else throw new Error(_0x4b0ec4(0x1f9));}