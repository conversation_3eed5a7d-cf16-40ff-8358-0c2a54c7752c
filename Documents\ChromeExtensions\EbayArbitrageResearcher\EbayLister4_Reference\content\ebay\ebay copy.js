var a0_0x5e1bbc=a0_0x426d;(function(_0x787907,_0x44491a){var _0x1c580e=a0_0x426d,_0x89d67=_0x787907();while(!![]){try{var _0x5af877=parseInt(_0x1c580e(0x2b4))/0x1+-parseInt(_0x1c580e(0x29c))/0x2*(-parseInt(_0x1c580e(0x23e))/0x3)+-parseInt(_0x1c580e(0x286))/0x4*(-parseInt(_0x1c580e(0x257))/0x5)+parseInt(_0x1c580e(0x214))/0x6+-parseInt(_0x1c580e(0x250))/0x7*(parseInt(_0x1c580e(0x21d))/0x8)+parseInt(_0x1c580e(0x206))/0x9*(-parseInt(_0x1c580e(0x1f8))/0xa)+-parseInt(_0x1c580e(0x28a))/0xb*(parseInt(_0x1c580e(0x242))/0xc);if(_0x5af877===_0x44491a)break;else _0x89d67['push'](_0x89d67['shift']());}catch(_0x2d2d5d){_0x89d67['push'](_0x89d67['shift']());}}}(a0_0x3fb9,0xc52c9),console[a0_0x5e1bbc(0x2b7)]('ebay.js'),onPageLoadAndStableNotifyBackground());var startedListing=![],totalNetworkResponse=0x0,previousTotalNetworkResponse=0x0,itemSpecificPrompt='',itemSpecificPromptResponse='',previousTabId,previousWindowId,receivedDescriptionNetworkResponse=![],documentTitle=a0_0x5e1bbc(0x276),isScriptCompleted=![],requestHeaders=[];async function testMain(){var _0x56b3ce=a0_0x5e1bbc;console[_0x56b3ce(0x2b7)]('testMain'),await onPageLoadAndStable(),await fillEmptyRequiredItemSpecificsV2(_0x56b3ce(0x28c));}function a0_0x426d(_0x18c493,_0xca01ab){var _0x3fb96e=a0_0x3fb9();return a0_0x426d=function(_0x426dae,_0x1fbfa5){_0x426dae=_0x426dae-0x1ee;var _0x1a1fe5=_0x3fb96e[_0x426dae];return _0x1a1fe5;},a0_0x426d(_0x18c493,_0xca01ab);}chrome[a0_0x5e1bbc(0x267)]['onMessage'][a0_0x5e1bbc(0x226)](async(_0x6b5e32,_0x10e5c9,_0x5fae20)=>{var _0x51a53b=a0_0x5e1bbc;console[_0x51a53b(0x2b7)](_0x10e5c9[_0x51a53b(0x26b)]?_0x51a53b(0x27a)+_0x10e5c9['tab'][_0x51a53b(0x260)]:_0x51a53b(0x1f3)+_0x6b5e32[_0x51a53b(0x22e)]);if((_0x6b5e32[_0x51a53b(0x22e)]===_0x51a53b(0x246)||_0x6b5e32[_0x51a53b(0x22e)]===_0x51a53b(0x217))&&startedListing===![]){console[_0x51a53b(0x2b7)](_0x51a53b(0x26f)),startedListing=!![],console[_0x51a53b(0x2b7)](_0x51a53b(0x275),_0x6b5e32),await waitForPageToLoad(),setInterval(function(){var _0x2e3f42=_0x51a53b;!isScriptCompleted?document[_0x2e3f42(0x2b3)]=documentTitle:clearInterval();},0x64);if(_0x6b5e32[_0x51a53b(0x263)][_0x51a53b(0x21f)]===_0x51a53b(0x1ee))postEbayDataForEbayItem(_0x6b5e32[_0x51a53b(0x263)]);else try{postEbayData(_0x6b5e32[_0x51a53b(0x263)]);}catch(_0x15e22a){console[_0x51a53b(0x2b7)](_0x51a53b(0x2a9)+_0x15e22a),chrome['runtime'][_0x51a53b(0x26c)]({'type':_0x51a53b(0x23d),'sku':_0x6b5e32[_0x51a53b(0x263)][_0x51a53b(0x25a)],'message':'Error\x20Posting\x20Item:\x20'+_0x15e22a});}}_0x6b5e32[_0x51a53b(0x22e)]===_0x51a53b(0x225)&&(console[_0x51a53b(0x2b7)]('networkResponse',_0x6b5e32[_0x51a53b(0x272)]),previousTotalNetworkResponse=totalNetworkResponse,totalNetworkResponse++),_0x6b5e32[_0x51a53b(0x22e)]==='ebay_description_network_response'&&(console[_0x51a53b(0x2b7)](_0x51a53b(0x228),_0x6b5e32[_0x51a53b(0x272)]),receivedDescriptionNetworkResponse=!![]),_0x6b5e32[_0x51a53b(0x22e)]===_0x51a53b(0x2a3)&&(console[_0x51a53b(0x2b7)](_0x51a53b(0x216),_0x6b5e32),requestHeaders=_0x6b5e32[_0x51a53b(0x27d)]);});function getHeader(_0x395b33,_0x412481){var _0x2b0382=a0_0x5e1bbc;for(var _0x2e090c=0x0;_0x2e090c<_0x412481[_0x2b0382(0x24f)];_0x2e090c++){if(_0x412481[_0x2e090c]['name']===_0x395b33)return _0x412481[_0x2e090c];}return null;}async function postViaNetworkRequest(_0x157569,_0x289736){var _0x260a8e=a0_0x5e1bbc,_0x2dab4c=new Headers();for(var _0x3562a1=0x0;_0x3562a1<requestHeaders[_0x260a8e(0x24f)];_0x3562a1++){var _0x31707e=requestHeaders[_0x3562a1];_0x2dab4c[_0x260a8e(0x20b)](_0x31707e[_0x260a8e(0x27c)],_0x31707e[_0x260a8e(0x2b1)]);}var _0x535fd9=new URLSearchParams(new URL(window[_0x260a8e(0x23f)][_0x260a8e(0x291)])['search']),_0x257fa6=_0x535fd9['get'](_0x260a8e(0x21a)),_0x3e2ca1=window[_0x260a8e(0x23f)][_0x260a8e(0x291)][_0x260a8e(0x204)]('/')[0x2],_0x298abd=_0x260a8e(0x241)+_0x3e2ca1+_0x260a8e(0x24a)+_0x257fa6+_0x260a8e(0x1fe),_0x485011='PUT',_0x2d40ac={[_0x157569]:_0x289736},_0x23e88a=new TextEncoder()[_0x260a8e(0x244)](JSON[_0x260a8e(0x26e)](_0x2d40ac));try{const _0x35e697=await fetch(_0x298abd,{'method':_0x485011,'headers':_0x2dab4c,'body':_0x23e88a});console['log'](_0x260a8e(0x297),_0x35e697);}catch(_0x3a05f4){console[_0x260a8e(0x295)](_0x260a8e(0x22f),_0x3a05f4);}}async function postObjectViaNetworkRequest(_0x3b5ab6){var _0x2a0638=a0_0x5e1bbc,_0x31cc9e=new Headers();for(var _0x3cada9=0x0;_0x3cada9<requestHeaders[_0x2a0638(0x24f)];_0x3cada9++){var _0x587692=requestHeaders[_0x3cada9];_0x31cc9e[_0x2a0638(0x20b)](_0x587692[_0x2a0638(0x27c)],_0x587692[_0x2a0638(0x2b1)]);}var _0x44c1b0=new URLSearchParams(new URL(window['location'][_0x2a0638(0x291)])[_0x2a0638(0x278)]),_0x461975=_0x44c1b0[_0x2a0638(0x210)]('draftId'),_0xd69a2e=window[_0x2a0638(0x23f)]['href'][_0x2a0638(0x204)]('/')[0x2],_0x7d1b40=_0x2a0638(0x241)+_0xd69a2e+_0x2a0638(0x24a)+_0x461975+_0x2a0638(0x1fe),_0x5363fc=_0x2a0638(0x22a),_0x1afbe8=new TextEncoder()['encode'](JSON['stringify'](_0x3b5ab6));try{const _0x30aa7d=await fetch(_0x7d1b40,{'method':_0x5363fc,'headers':_0x31cc9e,'body':_0x1afbe8});console[_0x2a0638(0x2b7)]('Response:',_0x30aa7d);}catch(_0x3117c0){console['error'](_0x2a0638(0x22f),_0x3117c0);}}function waitForRequestHeaders(){return new Promise((_0x1cc398,_0x5e052d)=>{var _0xfee290=setInterval(()=>{var _0x2c9bf1=a0_0x426d;requestHeaders[_0x2c9bf1(0x24f)]>0x0&&(clearInterval(_0xfee290),_0x1cc398());},0x3e8);});}function parseCookies(_0x2ac5a1){var _0x54178b=a0_0x5e1bbc,_0x2508cd=[],_0x24b803=_0x2ac5a1[_0x54178b(0x204)](';');for(var _0x3c4196=0x0;_0x3c4196<_0x24b803[_0x54178b(0x24f)];_0x3c4196++){var _0x2accf8=_0x24b803[_0x3c4196],_0x27df03=_0x2accf8['split']('='),_0x29f27c=_0x27df03[0x0],_0x160451=_0x27df03[0x1],_0x11485f={'name':_0x29f27c,'value':_0x160451};_0x2508cd[_0x54178b(0x1fa)](_0x11485f);}return _0x2508cd;}function waitForPageToLoad(){return new Promise((_0x40f7ff,_0x2221d1)=>{var _0x470b9b=setInterval(()=>{var _0x19ad0e=a0_0x426d;document['readyState']===_0x19ad0e(0x1f9)&&(clearInterval(_0x470b9b),_0x40f7ff());},0x3e8);});}async function testDraft(){var _0x45bd27=a0_0x5e1bbc;window[_0x45bd27(0x23f)][_0x45bd27(0x291)]['includes']('draftId')&&chrome[_0x45bd27(0x25b)]['local'][_0x45bd27(0x210)](_0x45bd27(0x207),async function(_0x563601){var _0x5744b4=_0x45bd27,_0x39c071=_0x563601[_0x5744b4(0x207)];console[_0x5744b4(0x2b7)](_0x5744b4(0x28f),_0x39c071),await waitForPageToLoad(),setInterval(function(){var _0x37b0ac=_0x5744b4;document[_0x37b0ac(0x2b3)]=documentTitle;},0x32),console['log']('0'),await pasteTitle(_0x39c071[_0x5744b4(0x2b0)]),documentTitle=_0x5744b4(0x232),await waitForNetworkResponseCountIncrease(),console[_0x5744b4(0x2b7)](_0x5744b4(0x212)),await waitForRequestHeaders(),console[_0x5744b4(0x2b7)]('waitForRequestHeaders\x20Done'),documentTitle='adding\x20gspr',await setGSPR(),documentTitle='gspr\x20added';});}function a0_0x3fb9(){var _0x3f33eb=['filteredItemSpecifics','close_tab','Item\x20Listed\x20Successfully!','shouldFillRequiredItemSpecifics','totalImagesUploaded','uploading\x20selected\x20image\x20done','3172DnYErC','free','error\x20filling\x20required\x20item\x20specifics','appendChild','7843737HqiQhu','receive\x20page\x20load\x20waitForPicUploadIFrameToLoad','Details\x20In\x20Description','Marke','style','DraftURL\x20product','Received\x20Description\x20Network\x20Response','href','Ready\x20To\x20Submit!','actionbar','shouldFillOptionalItemSpecifics\x20is\x20','error','main_sd_images','Response:','applyPromotedListingV2','receive\x20pageLoaded','Done\x20Uploading\x20Images','shouldFillRequiredItemSpecifics\x20is\x20true,\x20filling\x20required\x20item\x20specifics\x20with\x20AI','2hFLEjk','innerHTML','useSimpleDescription','paid','visible','querySelector','optional','requestVariables','pageErrorMessage','uploading\x20other\x20images','shouldGetGspr','waitForNetworkResponseCountIncrease\x20Done','co.uk','Error\x20Posting\x20Item:\x20','addEventListener','adRate','adding\x20gspr','pastePrice','europeanArticleNumber','.summary__price-fields\x20.se-textbox--input','custom_title','value','https://favicon-generator.org/favicon-generator/htdocs/favicons/2014-12-02/b14c9dab43e1876748e6262327d99884.ico','title','21902eBtqis','brand','input[name=\x22promotedListingSelection\x22]','log','itemSpecifics','visibility','EbayRelist','https://www.freeiconspng.com/uploads/error-icon-4.png','forceItemLocationSwitch','attributes','totalImagesUploaded\x20is\x20undefined\x20or\x20another\x20falsy\x20value\x20(but\x20not\x20zero)','From\x20the\x20extension\x20request.type\x20ebay.js','promotedListing_iframe','itemListed','waiting\x20for\x20network\x20response\x20count\x20increase','local','10uSrYff','complete','push','paid2','7.1','picUploadUpdate','?mode=AddItem&forceValidation=true','visibilityState','cmd','click','center','forceReturnPolicySwitch','split','response:\x20','1074564yvfjOY','amazon','Unbranded','pasteTitle','.summary__description','append','universalProductCode','main_hd_images','autoSubmitEnabled','round','get','error\x20pasting\x20sku','\x20waitForNetworkResponseCountIncrease\x20Done','getElementById','8766996jYLQJJ','receivedDescriptionNetworkResponse\x20received','received\x20message\x20requestVariables','identify_product','waitForRequestHeaders\x20Done','responsiblePersonEU','draftId','Adding\x20The\x20free\x20Description','price','56UWwuTG','promotedListingSelection','listingType','description','domain','postMessage','Adding\x20The\x20AI\x20Description','Adding\x20Item\x20Specifics','networkResponse','addListener','join','ebay_description_network_response','parse','PUT','Adding\x20The\x20descriptionHtml','mainHdImages','removeEventListener','type','Error:','mainSdImages','Does\x20Not\x20Apply','Title\x20Pasted','Failed\x20to\x20Submit\x20the\x20Listing!','7.3','promoted_listing_ad_rate\x20from\x20product','contentWindow','Adding\x20The\x20SKU','scheduleListingTime\x20is\x20true','imageName','promoted_listing_ad_rate\x20from\x20storage','Brand','submitButton\x20initiated','itemFailed','2390232nlqTJQ','location','suggestedQuery','https://','36gkcMBR','chatGpt','encode','Scheduling\x20the\x20Listing','insert_ebay_data','Promoting\x20Listing','promoted_listing_ad_rate','smooth','/lstng/api/listing_draft/','images','Uploading\x20Selected\x20Image','previousWindowId:\x20','#gh','length','213563KdmdUV','mainHdImages\x20type','b64Image','Does\x20not\x20apply','Adding\x20Price','postEbayData\x20begins','error\x20pasting\x20brand','6305kwJYoU','Adding\x20UPC','listing_result','sku','storage','.summary__title\x20.se-textbox--fluid','switchToWindowAndTab','BASE_64','useBrand','url','error\x20filling\x20optional\x20item\x20specifics','scheduleListingTime\x20is\x20false','productData','substring','manufacturerInfo','data','runtime','.smry.summary__photos','shouldFillOptionalItemSpecifics','selected_image','tab','sendMessage','scrollIntoView','stringify','insert_ebay_data\x20begins','button[innerHTML=\x27','send_message_to_promoted_listing_to_click_promoted_listing_button','details','src','#fehelix-uploader','request','Inserted\x20Script','required','search','textContent','From\x20a\x20content\x20script:','message','name','requestHeaders','.jpg','Adding\x20Title'];a0_0x3fb9=function(){return _0x3f33eb;};return a0_0x3fb9();}async function postEbayDataForEbayItem(_0x22f6ee){var _0xbc0502=a0_0x5e1bbc;changeFaviconToTaskBar(),console[_0xbc0502(0x2b7)]('1'),documentTitle='Started\x20inserting\x20product',await pasteOnlyImages(_0x22f6ee['images'],_0x22f6ee[_0xbc0502(0x2b3)]),await clickShowMoreButton(),documentTitle=_0xbc0502(0x27f),await pasteTitle(_0x22f6ee['title']),await waitForNetworkResponseCountIncrease(),console[_0xbc0502(0x2b7)](_0xbc0502(0x212)),await waitForRequestHeaders(),await new Promise((_0x12a878,_0x1a720c)=>{setTimeout(_0x12a878,0x7d0);}),documentTitle=_0xbc0502(0x224),await selectAllExtractedAspects(),await clickShowMoreButton();_0x22f6ee[_0xbc0502(0x25a)]&&await pasteSKU(_0x22f6ee['sku']);documentTitle=_0xbc0502(0x2b8);_0x22f6ee[_0xbc0502(0x2b8)]&&await fillEmptyItemSpecifics(_0x22f6ee[_0xbc0502(0x2b8)]);documentTitle=_0xbc0502(0x254),await fillEmptyRequiredItemSpecificsV2(_0xbc0502(0x28c)),await pastePrice(_0x22f6ee[_0xbc0502(0x21c)]),await postViaNetworkRequest(_0xbc0502(0x21c),_0x22f6ee[_0xbc0502(0x21c)]),await waitForNetworkResponseCountIncrease(),documentTitle=_0xbc0502(0x22b);var _0x10703f=await replaceCarouselImages(_0x22f6ee[_0xbc0502(0x220)]);await addHtmlToEbayDescription(_0x10703f),await postViaNetworkRequest(_0xbc0502(0x220),_0x10703f),await waitForNetworkResponseCountIncrease();var _0x3792b8=document[_0xbc0502(0x2a1)](_0xbc0502(0x24e));scrollIntoView(_0x3792b8),documentTitle='Promoting\x20Listing';var _0x5bd49b=document[_0xbc0502(0x2a1)]('input[name=\x22promotedListingSelection\x22]');if(_0x5bd49b){await postViaNetworkRequest(_0xbc0502(0x21e),!![]),await waitForNetworkResponseCountIncrease();var {promoted_listing_ad_rate:_0x378fff}=await chrome[_0xbc0502(0x25b)]['local'][_0xbc0502(0x210)](_0xbc0502(0x248));console['log']('promoted_listing_ad_rate\x20from\x20storage',_0x378fff);try{console[_0xbc0502(0x2b7)](_0xbc0502(0x235),_0x22f6ee['promoted_listing_ad_rate']),_0x22f6ee[_0xbc0502(0x248)]&&(_0x378fff=_0x22f6ee['promoted_listing_ad_rate']);}catch(_0x235154){console[_0xbc0502(0x2b7)]('error\x20setting\x20promoted_listing_ad_rate');}await postViaNetworkRequest(_0xbc0502(0x2ab),_0x378fff),await waitForNetworkResponseCountIncrease();}documentTitle=_0xbc0502(0x258),await clickShowMoreButton();var {domain:_0x33f900}=await chrome[_0xbc0502(0x25b)][_0xbc0502(0x1f7)]['get'](_0xbc0502(0x221));if(_0x33f900==_0xbc0502(0x2a8)||_0x33f900=='de'){await postViaNetworkRequest(_0xbc0502(0x2ae),'Does\x20Not\x20Apply'),await waitForNetworkResponseCountIncrease();var _0x2b7e22={'EAN':[_0xbc0502(0x253)]};await postViaNetworkRequest(_0xbc0502(0x1f1),_0x2b7e22),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest(_0xbc0502(0x20c),'Does\x20Not\x20Apply'),await waitForNetworkResponseCountIncrease();var _0x2b7e22={'UPC':[_0xbc0502(0x253)]};await postViaNetworkRequest(_0xbc0502(0x1f1),_0x2b7e22),await waitForNetworkResponseCountIncrease();}console[_0xbc0502(0x2b7)]('Ready\x20To\x20Submit!'),documentTitle=_0xbc0502(0x292),addSubmitButtonListener(_0x22f6ee);var {autoSubmitEnabled:_0x54de59}=await chrome['storage'][_0xbc0502(0x1f7)]['get'](_0xbc0502(0x20e));_0x54de59&&await submitTheListing();}async function postEbayData(_0x6bf0eb){var _0x3200be=a0_0x5e1bbc;console[_0x3200be(0x2b7)](_0x3200be(0x255));if(_0x6bf0eb[_0x3200be(0x240)]){if(_0x6bf0eb['suggestedQuery']['length']>0x41)do{var _0x2225ab=_0x6bf0eb[_0x3200be(0x240)][_0x3200be(0x204)]('\x20');_0x2225ab['pop'](),_0x6bf0eb['suggestedQuery']=_0x2225ab[_0x3200be(0x227)]('\x20');}while(_0x6bf0eb['suggestedQuery'][_0x3200be(0x24f)]>0x41);}await selectCondition(),changeFaviconToTaskBar(),console[_0x3200be(0x2b7)](_0x6bf0eb),await uploadImageConfig(_0x6bf0eb),console[_0x3200be(0x2b7)]('0'),documentTitle='Started\x20inserting\x20product',await clickShowMoreButton(),documentTitle='Adding\x20Title',await pasteTitle(_0x6bf0eb['custom_title']),console['log']('Paste\x20Title\x20Done'),await waitForNetworkResponseCountIncrease(),console['log'](_0x3200be(0x212)),await waitForRequestHeaders(),console[_0x3200be(0x2b7)](_0x3200be(0x218)),await selectAllExtractedAspects();var {shouldGetGspr:_0x2978c1}=await chrome[_0x3200be(0x25b)][_0x3200be(0x1f7)][_0x3200be(0x210)](_0x3200be(0x2a6)),_0x42a9bf=![];_0x2978c1&&(documentTitle=_0x3200be(0x2ac),_0x42a9bf=await setGSPR(_0x6bf0eb[_0x3200be(0x265)],_0x6bf0eb[_0x3200be(0x219)]),documentTitle='gspr\x20set'+_0x42a9bf);documentTitle=_0x3200be(0x224);var {shouldFillRequiredItemSpecifics:_0x17612d}=await chrome['storage'][_0x3200be(0x1f7)][_0x3200be(0x210)](_0x3200be(0x283)),{shouldFillOptionalItemSpecifics:_0x27c049}=await chrome['storage']['local'][_0x3200be(0x210)](_0x3200be(0x269));console[_0x3200be(0x2b7)](_0x3200be(0x283),_0x17612d),console[_0x3200be(0x2b7)](_0x3200be(0x269),_0x27c049),await clickShowMoreButton();_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x29f)&&_0x17612d?(console[_0x3200be(0x2b7)](_0x3200be(0x29b)),console[_0x3200be(0x2b7)](_0x3200be(0x294)+_0x27c049),await clickShowMoreButton(),await fillItemSpecificValuesWithAiV6(_0x6bf0eb)):(console[_0x3200be(0x2b7)]('shouldFillRequiredItemSpecifics\x20is\x20false,\x20not\x20filling\x20required\x20item\x20specifics\x20with\x20AI'),console[_0x3200be(0x2b7)](_0x3200be(0x294)+_0x27c049));(_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x243)||_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x1fb))&&(_0x17612d&&await fillItemSpecificValuesWithAiV5(_0x6bf0eb,_0x3200be(0x277),!![]),await clickShowMoreButton(),_0x27c049&&await fillItemSpecificValuesWithAiV5(_0x6bf0eb,_0x3200be(0x2a2),!![]));documentTitle='Adding\x20UPC';if(_0x6bf0eb[_0x3200be(0x280)]){var _0x5ceac2=capitalizeFirstLetterOfEachWordInItemSpecifics(_0x6bf0eb[_0x3200be(0x280)]);console[_0x3200be(0x2b7)](_0x3200be(0x280),_0x5ceac2),await fillEmptyItemSpecifics(_0x5ceac2);}if(_0x17612d&&_0x6bf0eb[_0x3200be(0x240)])try{await fillEmptyRequiredItemSpecificsV2(_0x6bf0eb[_0x3200be(0x240)]);}catch(_0x1cab95){console[_0x3200be(0x2b7)](_0x3200be(0x288));}else await fillEmptyRequiredItemSpecificsV2(_0x3200be(0x28c));if(_0x27c049&&_0x6bf0eb['listingType']==_0x3200be(0x29f))try{await fillEmptyOptionalItemSpecificsV2(_0x6bf0eb[_0x3200be(0x240)]);}catch(_0x2f429c){console['log'](_0x3200be(0x261));}console[_0x3200be(0x2b7)]('1'),console['log']('2'),documentTitle='Adding\x20Price';var _0x5301ce=_0x6bf0eb['custom_price'];await pastePrice(_0x5301ce),console[_0x3200be(0x2b7)]('4'),console['log']('5'),documentTitle=_0x3200be(0x237);if(_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x29f)||_0x6bf0eb['listingType']==_0x3200be(0x287)||_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x243))try{await pasteSKU(btoa(_0x6bf0eb[_0x3200be(0x25a)]));}catch(_0xbd1754){console[_0x3200be(0x2b7)](_0x3200be(0x211));}console[_0x3200be(0x2b7)]('6'),console['log']('7'),console[_0x3200be(0x2b7)](_0x3200be(0x1fc));if(!_0x6bf0eb[_0x3200be(0x25f)]){await pasteBrand(_0x3200be(0x208));try{await fillSingleSelectItemSpecific(_0x3200be(0x23b),'Unbranded'),await fillSingleSelectItemSpecific(_0x3200be(0x28d),_0x3200be(0x208));}catch(_0x76ccda){console[_0x3200be(0x2b7)](_0x3200be(0x256));}}if(_0x6bf0eb[_0x3200be(0x25f)]){await pasteBrand(_0x6bf0eb[_0x3200be(0x2b5)]);try{await fillSingleSelectItemSpecific(_0x3200be(0x23b),_0x6bf0eb[_0x3200be(0x2b5)]);}catch(_0x9d9a06){console['log'](_0x3200be(0x256));}}console[_0x3200be(0x2b7)]('7.2'),documentTitle=_0x3200be(0x223);if(_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x287)){documentTitle=_0x3200be(0x21b);var _0x31236a=await getBasicDescription(_0x6bf0eb);await addHtmlToEbayDescription(_0x31236a),await postViaNetworkRequest('description',_0x31236a),await waitForNetworkResponseCountIncrease();}else{if(_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x29f)){var _0x3a0b33=await getDescriptionHtmlFromOpenAi(_0x6bf0eb),{useSimpleDescription:_0x1e81cf}=await chrome[_0x3200be(0x25b)]['local'][_0x3200be(0x210)](_0x3200be(0x29e)),_0x3fb3aa;!_0x1e81cf?_0x3fb3aa=await createDescriptionFromTemplate2024(_0x3a0b33,_0x6bf0eb):_0x3fb3aa=_0x3a0b33;try{var _0x535e57=await createItemSpecificsHtmlTable();_0x3fb3aa=_0x3fb3aa+_0x535e57;}catch(_0x40f34b){}await addHtmlToEbayDescription(_0x3fb3aa),await postViaNetworkRequest('description',_0x3fb3aa),await waitForNetworkResponseCountIncrease();}else{if(_0x6bf0eb[_0x3200be(0x21f)]==_0x3200be(0x1fb)){var _0x3a0b33=await getDescriptionHtmlFromOpenAi(_0x6bf0eb);await addHtmlToEbayDescription(_0x3a0b33),await postViaNetworkRequest(_0x3200be(0x220),_0x3a0b33),await waitForNetworkResponseCountIncrease();}else{if(_0x6bf0eb[_0x3200be(0x21f)]=='chatGpt'){var _0x3a0b33=await getDescriptionHtmlFromChatGpt(_0x6bf0eb),_0x3fb3aa=await createDescriptionFromTemplate2024(_0x3a0b33,_0x6bf0eb);await addHtmlToEbayDescription(_0x3fb3aa),await postViaNetworkRequest(_0x3200be(0x220),_0x3fb3aa),await waitForNetworkResponseCountIncrease();}}}}console[_0x3200be(0x2b7)](_0x3200be(0x234));var _0x41f8e4=document[_0x3200be(0x2a1)](_0x3200be(0x24e));scrollIntoView(_0x41f8e4),documentTitle=_0x3200be(0x247);var _0x55222b=document[_0x3200be(0x2a1)](_0x3200be(0x2b6));if(_0x55222b){await postViaNetworkRequest(_0x3200be(0x21e),!![]),await waitForNetworkResponseCountIncrease();var {promoted_listing_ad_rate:_0x3ac533}=await chrome[_0x3200be(0x25b)][_0x3200be(0x1f7)][_0x3200be(0x210)](_0x3200be(0x248));console[_0x3200be(0x2b7)](_0x3200be(0x23a),_0x3ac533);try{console[_0x3200be(0x2b7)]('promoted_listing_ad_rate\x20from\x20product',_0x6bf0eb[_0x3200be(0x248)]),_0x6bf0eb['promoted_listing_ad_rate']&&(_0x3ac533=_0x6bf0eb[_0x3200be(0x248)]);}catch(_0x483af5){console[_0x3200be(0x2b7)]('error\x20setting\x20promoted_listing_ad_rate');}await postViaNetworkRequest(_0x3200be(0x2ab),_0x3ac533),await waitForNetworkResponseCountIncrease();}try{documentTitle=_0x3200be(0x245);var {scheduleListingTime:_0x44947b}=await chrome[_0x3200be(0x25b)][_0x3200be(0x1f7)][_0x3200be(0x210)]('scheduleListingTime');_0x44947b?(console['log'](_0x3200be(0x238),_0x44947b),await scheduleListing(Number(_0x44947b))):console['log'](_0x3200be(0x262),_0x44947b);}catch(_0x3e2950){console['log']('error\x20scheduling\x20the\x20listing');}var {forceItemLocationSwitch:_0x195e67}=await chrome[_0x3200be(0x25b)][_0x3200be(0x1f7)][_0x3200be(0x210)](_0x3200be(0x1f0));_0x195e67&&await setItemLocation();var {forceReturnPolicySwitch:_0x1decdc}=await chrome[_0x3200be(0x25b)][_0x3200be(0x1f7)]['get'](_0x3200be(0x203));_0x1decdc&&await setReturnPolicy();documentTitle=_0x3200be(0x258),await clickShowMoreButton();var {domain:_0x1ac936}=await chrome['storage'][_0x3200be(0x1f7)][_0x3200be(0x210)]('domain');if(_0x1ac936==_0x3200be(0x2a8)||_0x1ac936=='de'){await postViaNetworkRequest('europeanArticleNumber',_0x3200be(0x231)),await waitForNetworkResponseCountIncrease();var _0x29a6b3={'EAN':[_0x3200be(0x253)]};await postViaNetworkRequest(_0x3200be(0x1f1),_0x29a6b3),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest('universalProductCode','Does\x20Not\x20Apply'),await waitForNetworkResponseCountIncrease();var _0x29a6b3={'UPC':['Does\x20not\x20apply']};await postViaNetworkRequest(_0x3200be(0x1f1),_0x29a6b3),await waitForNetworkResponseCountIncrease();}console[_0x3200be(0x2b7)]('Ready\x20To\x20Submit!'),documentTitle=_0x3200be(0x292),addSubmitButtonListener(_0x6bf0eb);var {autoSubmitEnabled:_0x3dbbaf}=await chrome['storage'][_0x3200be(0x1f7)][_0x3200be(0x210)]('autoSubmitEnabled');_0x3dbbaf&&await submitTheListing();}function addSubmitButtonListener(_0x25b6e8){var _0x96f8a=a0_0x5e1bbc,_0x17218f=getSubmitButton();console[_0x96f8a(0x2b7)](_0x96f8a(0x23c),_0x17218f),_0x17218f[_0x96f8a(0x2aa)](_0x96f8a(0x201),async function(){var _0x1d5a9e=_0x96f8a;console['log']('submitButton\x20clicked');var _0x219df1=await checkIfItemWasSuccessfullyPosted(),_0x8d3b3f='';try{_0x8d3b3f=await getSuccessLink();}catch(_0x549e78){}if(_0x219df1)chrome[_0x1d5a9e(0x267)]['sendMessage']({'type':_0x1d5a9e(0x1f5),'sku':_0x25b6e8[_0x1d5a9e(0x25a)],'message':_0x1d5a9e(0x282),'ebayItemLink':_0x8d3b3f,'eventType':_0x1d5a9e(0x259)}),changeFaviconOfPage(_0x1d5a9e(0x2b2)),documentTitle='Submitted\x20the\x20Listing!',addSkuToSkuListAndSaveToLocalStorage(_0x25b6e8['sku']);else{var _0x137420=getPageErrorMessage();console[_0x1d5a9e(0x2b7)](_0x1d5a9e(0x2a4),_0x137420),!_0x137420&&(_0x137420='Item\x20Failed\x20to\x20List!\x20'+window['location'][_0x1d5a9e(0x291)]),chrome[_0x1d5a9e(0x267)]['sendMessage']({'type':_0x1d5a9e(0x23d),'sku':_0x25b6e8['sku'],'message':_0x137420,'eventType':_0x1d5a9e(0x259)}),changeFaviconOfPage(_0x1d5a9e(0x1ef)),documentTitle=_0x1d5a9e(0x233);}});}function getPageErrorMessage(){var _0x443ac0=a0_0x5e1bbc,_0x316a14=document['querySelector']('.page-notice__main');if(_0x316a14){var _0x5b33c9=_0x316a14[_0x443ac0(0x279)]+'\x20'+window[_0x443ac0(0x23f)][_0x443ac0(0x291)];return _0x5b33c9;}else return null;}async function uploadImageConfig(_0x2f2876){var _0x4cdd71=a0_0x5e1bbc,_0x4d353f=[],_0x2b5205=_0x2f2876[_0x4cdd71(0x20d)],_0x79a509=_0x2f2876[_0x4cdd71(0x296)];console[_0x4cdd71(0x2b7)](_0x4cdd71(0x22c),_0x2b5205[_0x4cdd71(0x24f)]),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x230),_0x79a509[_0x4cdd71(0x24f)]),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x251),typeof _0x2b5205),console['log']('mainSdImages\x20type',typeof _0x79a509),_0x4d353f=_0x2b5205;_0x2b5205[_0x4cdd71(0x24f)]<0x2&&_0x79a509['length']>0x2?_0x4d353f=_0x79a509:_0x4d353f=_0x2b5205;var _0x2bb285=_0x2f2876[_0x4cdd71(0x2b0)];documentTitle=_0x4cdd71(0x24c);var _0x336b04=_0x2f2876[_0x4cdd71(0x26a)];console[_0x4cdd71(0x2b7)]('uploading\x20selected\x20image'),await uploadImageAndWaitForCounterToUpload(_0x336b04,_0x2bb285[_0x4cdd71(0x264)](0x0,0x64)+'-'+0x1+_0x4cdd71(0x27e),IMAGES_TYPES[_0x4cdd71(0x25e)],0x0),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x285)),await waitForNetworkResponseCountIncrease(),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x2a7)),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x2a5)),console[_0x4cdd71(0x2b7)](_0x4cdd71(0x24b),_0x4d353f[_0x4cdd71(0x24f)]),documentTitle='Uploading\x20Images';var {onlyListOnePicture:_0x46fd1a}=await chrome[_0x4cdd71(0x25b)][_0x4cdd71(0x1f7)][_0x4cdd71(0x210)]('onlyListOnePicture');!_0x46fd1a&&await pasteImages(_0x4d353f,_0x2bb285,_0x2f2876),documentTitle=_0x4cdd71(0x29a);}async function waitForDescriptionNetworkResponse(){return new Promise((_0x27c472,_0x35eb09)=>{async function _0x305d4b(){var _0xd59eed=a0_0x426d;receivedDescriptionNetworkResponse===!![]?(documentTitle=_0xd59eed(0x290),console[_0xd59eed(0x2b7)](_0xd59eed(0x215)),_0x27c472()):(documentTitle='Waiting\x20For\x20Description\x20Network\x20Response',setTimeout(async()=>{await _0x305d4b();},0x64));}_0x305d4b();});}async function waitForNetworkResponseCountIncrease(){let _0x1cff71=totalNetworkResponse;return new Promise((_0x1eef6e,_0x219784)=>{async function _0x10b318(_0x109a53=0x0){var _0x13187e=a0_0x426d;totalNetworkResponse>_0x1cff71?(waitSomeTime(0x64),_0x1eef6e()):(_0x109a53++,_0x109a53%0x3c===0x0&&(console[_0x13187e(0x2b7)](_0x13187e(0x1f6),_0x109a53),console[_0x13187e(0x2b7)]('make\x20tab\x20active'),await makeTabActive()),setTimeout(async()=>{await _0x10b318(_0x109a53);},0x64)),_0x109a53>0x3c&&(totalNetworkResponse=_0x1cff71+0x1,_0x1eef6e());}_0x10b318();});}function makeTabActive(){}function waitUntilDocumentIsVisible(){return new Promise((_0x551067,_0x75a071)=>{var _0x253e81=setInterval(()=>{var _0x2330f5=a0_0x426d;document[_0x2330f5(0x1ff)]===_0x2330f5(0x2a0)&&(clearInterval(_0x253e81),_0x551067());},0x32);});}function uploadImageAndWaitForCounterToUpload(_0x4535c2,_0x250350,_0x213343,_0xdf8e94){return new Promise((_0x4a31fe,_0x30e6b9)=>{var _0x3cc364=a0_0x426d;console[_0x3cc364(0x2b7)](_0x3cc364(0x284),_0xdf8e94);!_0xdf8e94&&_0xdf8e94!==0x0&&(console[_0x3cc364(0x2b7)](_0x3cc364(0x1f2)),_0xdf8e94=getTotalImagesUploaded());console[_0x3cc364(0x2b7)](_0x3cc364(0x284),_0xdf8e94),uploadImage(_0x4535c2,'#fehelix-uploader',_0x250350,_0x213343);var _0x3ceffd=setInterval(()=>{var _0x42a438=getTotalImagesUploaded();_0x42a438>_0xdf8e94&&(clearInterval(_0x3ceffd),_0x4a31fe());},0x3e8);});}function uploadImageToIframeOnEbay(_0x5e59fc,_0xe0c756,_0x118cc6){return new Promise((_0x311d61,_0x40a7ca)=>{var _0x410d97=a0_0x426d,_0x2e6f64={'inputTagSelector':_0x118cc6,'imageName':_0xe0c756,'b64Image':_0x5e59fc};console['log']('uploadImageToIframeOnEbay\x20imageObject',_0x2e6f64),uploadImage(_0x2e6f64[_0x410d97(0x252)],_0x410d97(0x274),_0x2e6f64[_0x410d97(0x239)],IMAGES_TYPES[_0x410d97(0x25e)]);function _0x3c9204(_0x5c7997){var _0x42a66d=_0x410d97,_0x494253=JSON['parse'](_0x5c7997[_0x42a66d(0x266)]);_0x494253[_0x42a66d(0x200)]===_0x42a66d(0x1fd)&&(window['removeEventListener'](_0x42a66d(0x27b),_0x3c9204,![]),_0x311d61());}});}function waitForPicUploadIFrameToLoad(){return new Promise((_0x99ecb6,_0x2c2b85)=>{var _0x50bd9f=a0_0x426d;window[_0x50bd9f(0x2aa)](_0x50bd9f(0x27b),_0xc0c272,![]);function _0xc0c272(_0xdbf229){var _0x42846d=_0x50bd9f,_0xd5ae63=_0xdbf229[_0x42846d(0x266)];console[_0x42846d(0x2b7)](_0x42846d(0x28b),_0xd5ae63),(_0xd5ae63['pageLoaded']||_0xd5ae63['pageLoaded_for_promoted_listing'])&&(console[_0x42846d(0x2b7)](_0x42846d(0x299)),window['removeEventListener']('message',_0xc0c272,![]),_0x99ecb6());}});}function waitForPromotedListingIFrameToLoad(){return new Promise((_0x3c35b6,_0x57dd53)=>{var _0x48e78a=a0_0x426d;window[_0x48e78a(0x2aa)]('message',_0xba7744,![]);function _0xba7744(_0xe382c8){var _0x2aa205=_0x48e78a,_0x1d1adf=_0xe382c8['data'];_0x1d1adf['pageLoaded_for_promoted_listing']&&(window[_0x2aa205(0x22d)](_0x2aa205(0x27b),_0xba7744,![]),_0x3c35b6());}});}function promoteListing(_0x14a8a7){return new Promise((_0x523d1e,_0x51c30b)=>{var _0x265eca=a0_0x426d,_0x2f3447=document['getElementById'](_0x265eca(0x1f4));_0x2f3447[_0x265eca(0x236)][_0x265eca(0x222)]({'type':_0x265eca(0x271),'adRate':_0x14a8a7},_0x2f3447[_0x265eca(0x273)]),window[_0x265eca(0x2aa)](_0x265eca(0x27b),_0x2b9504,![]);function _0x2b9504(_0x592efc){var _0x4373e6=_0x265eca,_0x1b5d0f=JSON[_0x4373e6(0x229)](_0x592efc[_0x4373e6(0x266)]);_0x1b5d0f['cmd']==='PLDataUpdate'&&(window[_0x4373e6(0x22d)](_0x4373e6(0x27b),_0x2b9504,![]),_0x523d1e());}});}async function listItem(){return new Promise((_0x3158fe,_0x5c761e)=>{var _0x143696=a0_0x426d,_0x5c2e66=document['getElementById'](_0x143696(0x293)),_0x488f03=_0x5c2e66[_0x143696(0x2a1)]('[value=\x27List\x20item\x27]');_0x488f03[_0x143696(0x201)](),_0x488f03[_0x143696(0x201)](),_0x3158fe();});}function checkIfListingSucceeded(){return new Promise((_0x43a8f4,_0xe440b9)=>{var _0x1bfcda=a0_0x426d,_0x90db89=document[_0x1bfcda(0x213)]('success_msg');_0x90db89[_0x1bfcda(0x28e)][_0x1bfcda(0x2b9)]!==_0x1bfcda(0x2a0)&&_0x43a8f4(![]),_0x90db89[_0x1bfcda(0x28e)][_0x1bfcda(0x2b9)]===_0x1bfcda(0x2a0)&&_0x43a8f4(!![]);});}function waitSomeTime(_0x20d594){return new Promise((_0x22f196,_0x4c2a27)=>{setTimeout(()=>{_0x22f196();},_0x20d594);});}function tellChromeToCloseTab(){var _0x23f72e=a0_0x5e1bbc;chrome[_0x23f72e(0x267)][_0x23f72e(0x26c)]({'type':_0x23f72e(0x281)},function(_0x251081){});}function tellChromeToSwitchToPreviousTab(){return new Promise((_0x313cc4,_0x149736)=>{var _0x551186=a0_0x426d;console[_0x551186(0x2b7)]('previousTabId:\x20',previousTabId),console[_0x551186(0x2b7)](_0x551186(0x24d),previousWindowId),previousTabId&&previousWindowId?chrome['runtime']['sendMessage']({'type':_0x551186(0x25d),'windowId':previousWindowId,'tabId':previousTabId},function(_0x31429f){var _0x1d5ac5=_0x551186;console[_0x1d5ac5(0x2b7)](_0x1d5ac5(0x205),_0x31429f),_0x313cc4();}):_0x313cc4();});}function scrollIntoView(_0x3282c9){var _0x268e38=a0_0x5e1bbc;_0x3282c9[_0x268e38(0x26d)]({'behavior':_0x268e38(0x249),'block':'center','inline':_0x268e38(0x202)});}function createButtonToExecuteFunctionWithParameter(_0x281cf6,_0x725b56,_0x32e3c1){var _0x5c4457=a0_0x5e1bbc,_0x1b10f3=document['createElement']('button');_0x1b10f3[_0x5c4457(0x29d)]=_0x281cf6,_0x1b10f3['onclick']=function(_0x855bcc){_0x855bcc['stopPropagation'](),window[_0x281cf6](_0x32e3c1);},_0x725b56[_0x5c4457(0x2a1)](_0x5c4457(0x270)+_0x281cf6+'\x27]')===null&&_0x725b56[_0x5c4457(0x2a1)]('button[innerHTML=\x27'+_0x281cf6+'\x27]')===undefined&&_0x725b56[_0x5c4457(0x289)](_0x1b10f3);}function createManuelInputButtons(_0x1a734a){var _0x131786=a0_0x5e1bbc,_0x4f166b=document[_0x131786(0x2a1)](_0x131786(0x20a));createButtonToExecuteFunctionWithParameter('pasteDescriptionFromOpenAi',_0x4f166b,_0x1a734a);var _0x4f166b=document[_0x131786(0x2a1)](_0x131786(0x25c));createButtonToExecuteFunctionWithParameter(_0x131786(0x209),_0x4f166b,_0x1a734a[_0x131786(0x2b0)]);var _0x4f166b=document[_0x131786(0x2a1)](_0x131786(0x2af));createButtonToExecuteFunctionWithParameter(_0x131786(0x2ad),_0x4f166b,Math[_0x131786(0x20f)](_0x1a734a[_0x131786(0x21c)]*1.25));var _0x4f166b=document[_0x131786(0x2a1)]('.summary__promoted-listing');createButtonToExecuteFunctionWithParameter(_0x131786(0x298),_0x4f166b,null);var _0x4f166b=document['querySelector'](_0x131786(0x268));createButtonToExecuteFunctionWithParameter('uploadImageConfig',_0x4f166b,_0x1a734a);}function checkIfTotalNetworkResponseIncreased(_0x39527e,_0x476797){window[_0x39527e](_0x476797),setInterval(function(){totalNetworkResponse>previousTotalNetworkResponse&&(previousTotalNetworkResponse=totalNetworkResponse,window[_0x39527e](_0x476797));},0x3e8);}