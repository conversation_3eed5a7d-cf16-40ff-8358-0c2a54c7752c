.editor-container {
    display: flex;
    width: auto; /* Adjusted to auto */
}

#canvasContainer, #testCanvasContainer {
    width: 750px;
    height: 750px;
    transform: scale(0.5);
    transform-origin: 0 0;

}

#canvasWrapper {
    border: 2px solid #000;
    width: 750px; /* Adjust as per the transformed canvas size */
    height: 750px; /* Adjust as per the transformed canvas size */
}

#canvasWrapper {
    display: inline-block; /* Added this property */
}

#canvasContainer {
    transform: scale(0.5);
    transform-origin: 0 0;
}


#controls {
    flex-grow: 0; /* Adjusted from 1 to 0 to prevent it from taking extra space */
    width: auto; /* Added this property */
}

#controls {
    background-color: #f9f9f9;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.control-section {
    margin-bottom: 20px;
    border: #000 1px solid;
    padding: 10px;
}

.control-section h3 {
    color: #333;
    margin-bottom: 10px;
}

#controls button,
#controls input[type="file"],
#controls input[type="text"],
#controls select {
    margin-bottom: 10px;
}

#controls button {
    background-color: #4CAF50; /* Green */
    border: none;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    transition-duration: 0.4s;
    cursor: pointer;
}

#controls button:hover {
    background-color: white;
    color: black;
    border: 1px solid #4CAF50;
}

.danger {
    background-color: #f44336; /* Red */
    color: white;
}

.danger:hover {
    color: white;
    border: 1px solid #f44336;
    background-color: #da190b;
}
