Based on the aspect '{{label}}', please review and infer from the provided product description: '{{description}}'. Generate a comprehensive list of item specifics pertinent to the aspect '{{label}}' for this eBay listing. When direct information is not available, use inference to deduce related specifics. Values should be succinct, preferably single words or short phrases, suitable for eBay item specifics. Desired format:
{
    "values": ["<value1>", "<value2>", "<value3>", ...]
}

Strive for an extensive compilation of specifics, both explicit and implied, that are relevant to '{{label}}'. Do not restrict the list to only those mentioned in the description. If no specifics can be determined or inferred, respond with:
{
    "values": null
}
