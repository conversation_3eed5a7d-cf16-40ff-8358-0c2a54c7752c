var a0_0x499f93=a0_0x52a5;function a0_0x52a5(_0x485a41,_0x4cf2bf){var _0x4bc037=a0_0x4bc0();return a0_0x52a5=function(_0x52a561,_0x3527d8){_0x52a561=_0x52a561-0x116;var _0x5d1754=_0x4bc037[_0x52a561];return _0x5d1754;},a0_0x52a5(_0x485a41,_0x4cf2bf);}(function(_0x167a30,_0x4f4dff){var _0x24f6dd=a0_0x52a5,_0x3d3ad5=_0x167a30();while(!![]){try{var _0x3c2eb3=parseInt(_0x24f6dd(0x19e))/0x1+parseInt(_0x24f6dd(0x12c))/0x2+parseInt(_0x24f6dd(0x184))/0x3+-parseInt(_0x24f6dd(0x156))/0x4*(parseInt(_0x24f6dd(0x158))/0x5)+parseInt(_0x24f6dd(0x151))/0x6+parseInt(_0x24f6dd(0x132))/0x7+-parseInt(_0x24f6dd(0x1b3))/0x8;if(_0x3c2eb3===_0x4f4dff)break;else _0x3d3ad5['push'](_0x3d3ad5['shift']());}catch(_0x5ac090){_0x3d3ad5['push'](_0x3d3ad5['shift']());}}}(a0_0x4bc0,0x6ba64),console['log'](a0_0x499f93(0x18d)));var startedListing=![],totalNetworkResponse=0x0,previousTotalNetworkResponse=0x0,itemSpecificPrompt='',itemSpecificPromptResponse='',previousTabId,previousWindowId,receivedDescriptionNetworkResponse=![],documentTitle=a0_0x499f93(0x17d),isScriptCompleted=![],requestHeaders=[];chrome[a0_0x499f93(0x18b)]['onMessage']['addListener'](async(_0x104d84,_0x5c1f68,_0x39808f)=>{var _0x446fcb=a0_0x499f93;_0x104d84[_0x446fcb(0x1b5)]===_0x446fcb(0x1a9)&&startedListing===![]&&(console['log'](_0x446fcb(0x11d)),startedListing=!![],console['log'](_0x446fcb(0x19a),_0x104d84),await waitForPageToLoad(),setInterval(function(){var _0x5dae4a=_0x446fcb;!isScriptCompleted?document[_0x5dae4a(0x18c)]=documentTitle:clearInterval();},0x64),_0x104d84[_0x446fcb(0x133)]['listingType']===_0x446fcb(0x1a7)?postEbayDataForEbayItem(_0x104d84[_0x446fcb(0x133)]):postEbayData(_0x104d84[_0x446fcb(0x133)])),_0x104d84[_0x446fcb(0x1b5)]===_0x446fcb(0x160)&&(console['log'](_0x446fcb(0x160),_0x104d84[_0x446fcb(0x15a)]),previousTotalNetworkResponse=totalNetworkResponse,totalNetworkResponse++),_0x104d84['type']===_0x446fcb(0x17e)&&(console[_0x446fcb(0x181)]('ebay_description_network_response',_0x104d84[_0x446fcb(0x15a)]),receivedDescriptionNetworkResponse=!![]),_0x104d84[_0x446fcb(0x1b5)]==='requestVariables'&&(console[_0x446fcb(0x181)](_0x446fcb(0x173),_0x104d84),requestHeaders=_0x104d84[_0x446fcb(0x17b)]);});function getHeader(_0x4b4c61,_0x55b050){var _0x3f0427=a0_0x499f93;for(var _0x50ef5e=0x0;_0x50ef5e<_0x55b050[_0x3f0427(0x177)];_0x50ef5e++){if(_0x55b050[_0x50ef5e][_0x3f0427(0x199)]===_0x4b4c61)return _0x55b050[_0x50ef5e];}return null;}async function postViaNetworkRequest(_0xd880a7,_0x744d98){var _0x48549d=a0_0x499f93,_0x3cf2d1=new Headers();for(var _0x674bd6=0x0;_0x674bd6<requestHeaders[_0x48549d(0x177)];_0x674bd6++){var _0xe235c9=requestHeaders[_0x674bd6];_0x3cf2d1['append'](_0xe235c9[_0x48549d(0x199)],_0xe235c9[_0x48549d(0x141)]);}var _0xf1bc2b=new URLSearchParams(new URL(window['location'][_0x48549d(0x127)])[_0x48549d(0x1a1)]),_0x48ce52=_0xf1bc2b[_0x48549d(0x169)]('draftId'),_0x50be93=window[_0x48549d(0x148)][_0x48549d(0x127)]['split']('/')[0x2],_0x5df9d2=_0x48549d(0x17f)+_0x50be93+_0x48549d(0x11c)+_0x48ce52+_0x48549d(0x13b),_0x380233=_0x48549d(0x16c),_0x3cfe9c={[_0xd880a7]:_0x744d98},_0x29b9b7=new TextEncoder()[_0x48549d(0x183)](JSON['stringify'](_0x3cfe9c));try{const _0x204d40=await fetch(_0x5df9d2,{'method':_0x380233,'headers':_0x3cf2d1,'body':_0x29b9b7});console[_0x48549d(0x181)](_0x48549d(0x194),_0x204d40);}catch(_0x51b1b2){console[_0x48549d(0x126)](_0x48549d(0x16d),_0x51b1b2);}}function waitForRequestHeaders(){return new Promise((_0x4e3f7b,_0x5e68b5)=>{var _0x4d805b=setInterval(()=>{var _0x5985b7=a0_0x52a5;requestHeaders[_0x5985b7(0x177)]>0x0&&(clearInterval(_0x4d805b),_0x4e3f7b());},0x3e8);});}function parseCookies(_0x392130){var _0x3c1dfb=a0_0x499f93,_0x3c3c3c=[],_0x2676b4=_0x392130[_0x3c1dfb(0x121)](';');for(var _0x305fbe=0x0;_0x305fbe<_0x2676b4[_0x3c1dfb(0x177)];_0x305fbe++){var _0x2cf9c9=_0x2676b4[_0x305fbe],_0x2b44b9=_0x2cf9c9[_0x3c1dfb(0x121)]('='),_0x58f29f=_0x2b44b9[0x0],_0x11c714=_0x2b44b9[0x1],_0x583768={'name':_0x58f29f,'value':_0x11c714};_0x3c3c3c['push'](_0x583768);}return _0x3c3c3c;}function a0_0x4bc0(){var _0x3cc81e=['networkResponse','textContent','waiting\x20for\x20network\x20response\x20count\x20increase','make\x20tab\x20active','stopPropagation','readyState','local','https://favicon-generator.org/favicon-generator/htdocs/favicons/2014-12-02/b14c9dab43e1876748e6262327d99884.ico','button[innerHTML=\x27','get','substring','required','PUT','Error:','mainSdImages','Adding\x20The\x20AI\x20Description','close_tab','mainHdImages','error\x20pasting\x20sku','received\x20message\x20requestVariables','paid','uploading\x20selected\x20image','itemFailed','length','makeTabActive','Promoting\x20Listing','Does\x20not\x20apply','requestHeaders','.jpg','Inserted\x20Script','ebay_description_network_response','https://','custom_title','log','2.1','encode','740964eLSyEL','actionbar','DraftURL\x20product','amazon','visibilityState','paid2','Title\x20Pasted','runtime','title','ebay.js','querySelector','submitButton\x20clicked','adRate','sku','shouldFillRequiredItemSpecifics','7.3','Response:','.page-notice__main','selected_image','src','cmd','name','request','shouldFillOptionalItemSpecifics','totalImagesUploaded','receive\x20pageLoaded','93471pUJVhO','itemListed','Item\x20Failed\x20to\x20List!\x20','search','listingType','storage','message','scrollIntoView','center','EbayRelist','\x20waitForNetworkResponseCountIncrease\x20Done','insert_ebay_data','pageLoaded','chatGpt','appendChild','europeanArticleNumber','Adding\x20Item\x20Specifics','pasteTitle','visible','autoSubmitEnabled','itemSpecifics','1080928FcyOvn','https://www.freeiconspng.com/uploads/error-icon-4.png','type','attributes','Adding\x20Price','price','Ready\x20To\x20Submit!','main_sd_images','Adding\x20Title','mainHdImages\x20type','filteredItemSpecifics','/lstng/api/listing_draft/','insert_ebay_data\x20begins','data','listing_result','waitForRequestHeaders\x20Done','split','picUploadUpdate','Done\x20Uploading\x20Images','previousWindowId:\x20','smooth','error','href','pageErrorMessage','createElement','submitButton\x20initiated','BASE_64','143206ZZSMBC','pageLoaded_for_promoted_listing','input[name=\x22promotedListingSelection\x22]','draftId','innerHTML','parse','640647TNtjex','productData','universalProductCode','#fehelix-uploader','Waiting\x20For\x20Description\x20Network\x20Response','pasteDescriptionFromOpenAi','optional','Unbranded','receive\x20page\x20load\x20waitForPicUploadIFrameToLoad','?mode=AddItem&forceValidation=true','style','applyPromotedListingV2','contentWindow','Paste\x20Title\x20Done','button','value','receivedDescriptionNetworkResponse\x20received','images','getElementById','Received\x20Description\x20Network\x20Response','Does\x20Not\x20Apply','mainSdImages\x20type','location','Started\x20inserting\x20product','Details\x20In\x20Description','previousWindowId','send_message_to_promoted_listing_to_click_promoted_listing_button','promotedListingSelection','includes','PLDataUpdate','removeEventListener','5185662npEMoh','sendMessage','addEventListener','visibility','.summary__price-fields\x20.se-textbox--input','226232bvyJko','click','70GzfcOX','round','details','co.uk','7.1','Adding\x20UPC','.summary__description','description'];a0_0x4bc0=function(){return _0x3cc81e;};return a0_0x4bc0();}function waitForPageToLoad(){return new Promise((_0x25ce85,_0x5b59b7)=>{var _0x5cc05c=setInterval(()=>{var _0x1abd17=a0_0x52a5;document[_0x1abd17(0x165)]==='complete'&&(clearInterval(_0x5cc05c),_0x25ce85());},0x3e8);});}async function testDraft(){var _0x585aa8=a0_0x499f93;window[_0x585aa8(0x148)]['href'][_0x585aa8(0x14e)](_0x585aa8(0x12f))&&chrome[_0x585aa8(0x1a3)][_0x585aa8(0x166)][_0x585aa8(0x169)](_0x585aa8(0x187),async function(_0x201ed9){var _0x4ffd2e=_0x585aa8,_0x1c7798=_0x201ed9[_0x4ffd2e(0x187)];console[_0x4ffd2e(0x181)](_0x4ffd2e(0x186),_0x1c7798),await waitForPageToLoad(),setInterval(function(){var _0x31801d=_0x4ffd2e;document[_0x31801d(0x18c)]=documentTitle;},0x32),console[_0x4ffd2e(0x181)]('0'),await pasteTitle(_0x1c7798[_0x4ffd2e(0x180)]),documentTitle=_0x4ffd2e(0x18a),await waitForNetworkResponseCountIncrease(),console[_0x4ffd2e(0x181)](_0x4ffd2e(0x1a8)),await waitForRequestHeaders(),console[_0x4ffd2e(0x181)]('waitForRequestHeaders\x20Done'),await selectAllExtractedAspects(),await clickShowMoreButton();});}async function postEbayDataForEbayItem(_0x22f65f){var _0x55410e=a0_0x499f93;changeFaviconToTaskBar(),console[_0x55410e(0x181)]('1'),documentTitle=_0x55410e(0x149),await pasteOnlyImages(_0x22f65f[_0x55410e(0x143)],_0x22f65f[_0x55410e(0x18c)]),await clickShowMoreButton(),documentTitle=_0x55410e(0x119),await pasteTitle(_0x22f65f[_0x55410e(0x18c)]),await waitForNetworkResponseCountIncrease(),console[_0x55410e(0x181)](_0x55410e(0x1a8)),await waitForRequestHeaders(),await new Promise((_0x2a6fad,_0x4ec6bd)=>{setTimeout(_0x2a6fad,0x7d0);}),documentTitle=_0x55410e(0x1ae),await selectAllExtractedAspects(),await clickShowMoreButton();_0x22f65f['sku']&&await pasteSKU(_0x22f65f[_0x55410e(0x191)]);documentTitle='itemSpecifics';_0x22f65f['itemSpecifics']&&await fillEmptyItemSpecifics(_0x22f65f[_0x55410e(0x1b2)]);documentTitle=_0x55410e(0x1b7),await fillEmptyRequiredItemSpecificsV2(_0x55410e(0x14a)),await pastePrice(_0x22f65f['price']),await postViaNetworkRequest('price',_0x22f65f[_0x55410e(0x116)]),await waitForNetworkResponseCountIncrease(),documentTitle='Adding\x20The\x20descriptionHtml';var _0xcd60a1=await replaceCarouselImages(_0x22f65f[_0x55410e(0x15f)]);await addHtmlToEbayDescription(_0xcd60a1),await postViaNetworkRequest('description',_0xcd60a1),await waitForNetworkResponseCountIncrease();var _0x36d67f=document[_0x55410e(0x18e)]('#gh');scrollIntoView(_0x36d67f),documentTitle=_0x55410e(0x179);var _0x5367e3=document['querySelector'](_0x55410e(0x12e));_0x5367e3&&(await postViaNetworkRequest(_0x55410e(0x14d),!![]),await waitForNetworkResponseCountIncrease(),await postViaNetworkRequest(_0x55410e(0x190),_0x55410e(0x182)),await waitForNetworkResponseCountIncrease());documentTitle=_0x55410e(0x15d),await clickShowMoreButton();var {domain:_0x457e8b}=await chrome['storage'][_0x55410e(0x166)][_0x55410e(0x169)]('domain');if(_0x457e8b=='co.uk'){await postViaNetworkRequest(_0x55410e(0x1ad),_0x55410e(0x146)),await waitForNetworkResponseCountIncrease();var _0x13f340={'EAN':[_0x55410e(0x17a)]};await postViaNetworkRequest('attributes',_0x13f340),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest(_0x55410e(0x134),_0x55410e(0x146)),await waitForNetworkResponseCountIncrease();var _0x13f340={'UPC':[_0x55410e(0x17a)]};await postViaNetworkRequest('attributes',_0x13f340),await waitForNetworkResponseCountIncrease();}console[_0x55410e(0x181)](_0x55410e(0x117)),documentTitle='Ready\x20To\x20Submit!',addSubmitButtonListener(_0x22f65f);var {autoSubmitEnabled:_0x3704ac}=await chrome[_0x55410e(0x1a3)][_0x55410e(0x166)]['get']('autoSubmitEnabled');_0x3704ac&&await submitTheListing();}async function postEbayData(_0x1700a6){var _0x12d2ff=a0_0x499f93;changeFaviconToTaskBar(),console[_0x12d2ff(0x181)](_0x1700a6),await uploadImageConfig(_0x1700a6),console[_0x12d2ff(0x181)]('0'),documentTitle=_0x12d2ff(0x149),await clickShowMoreButton(),documentTitle=_0x12d2ff(0x119),await pasteTitle(_0x1700a6[_0x12d2ff(0x180)]),console[_0x12d2ff(0x181)](_0x12d2ff(0x13f)),await waitForNetworkResponseCountIncrease(),console[_0x12d2ff(0x181)]('\x20waitForNetworkResponseCountIncrease\x20Done'),await waitForRequestHeaders(),console[_0x12d2ff(0x181)](_0x12d2ff(0x120)),await selectAllExtractedAspects(),documentTitle=_0x12d2ff(0x1ae),await clickShowMoreButton();_0x1700a6[_0x12d2ff(0x1a2)]=='paid'&&(await fillItemSpecificValuesWithAiV5(_0x1700a6,_0x12d2ff(0x16b)),await clickShowMoreButton(),await fillItemSpecificValuesWithAiV5(_0x1700a6,_0x12d2ff(0x138)));var {shouldFillRequiredItemSpecifics:_0x4371e6}=await chrome[_0x12d2ff(0x1a3)][_0x12d2ff(0x166)][_0x12d2ff(0x169)](_0x12d2ff(0x192)),{shouldFillOptionalItemSpecifics:_0x1b8c13}=await chrome[_0x12d2ff(0x1a3)][_0x12d2ff(0x166)]['get'](_0x12d2ff(0x19b));(_0x1700a6['listingType']==_0x12d2ff(0x1ab)||_0x1700a6['listingType']==_0x12d2ff(0x189))&&(_0x4371e6&&await fillItemSpecificValuesWithAiV5(_0x1700a6,'required',!![]),await clickShowMoreButton(),_0x1b8c13&&await fillItemSpecificValuesWithAiV5(_0x1700a6,_0x12d2ff(0x138),!![]));documentTitle=_0x12d2ff(0x15d);if(_0x1700a6[_0x12d2ff(0x11b)]){var _0x2cc11f=capitalizeFirstLetterOfEachWordInItemSpecifics(_0x1700a6[_0x12d2ff(0x11b)]);console['log']('filteredItemSpecifics',_0x2cc11f),await fillEmptyItemSpecifics(_0x2cc11f);}await fillEmptyRequiredItemSpecificsV2(_0x12d2ff(0x14a)),console[_0x12d2ff(0x181)]('1'),console[_0x12d2ff(0x181)]('2'),documentTitle='Adding\x20Price';var _0x299c16=_0x1700a6['custom_price'];await pastePrice(_0x299c16),console[_0x12d2ff(0x181)]('4'),console[_0x12d2ff(0x181)]('5'),documentTitle='Adding\x20The\x20SKU';if(_0x1700a6[_0x12d2ff(0x1a2)]==_0x12d2ff(0x174)||_0x1700a6[_0x12d2ff(0x1a2)]=='free'||_0x1700a6['listingType']==_0x12d2ff(0x1ab))try{await pasteSKU(btoa(_0x1700a6[_0x12d2ff(0x191)]));}catch(_0x54cf0e){console[_0x12d2ff(0x181)](_0x12d2ff(0x172));}console[_0x12d2ff(0x181)]('6'),console[_0x12d2ff(0x181)]('7'),console['log'](_0x12d2ff(0x15c)),await pasteBrand(_0x12d2ff(0x139));try{await fillSingleSelectItemSpecific('Brand','Unbranded');}catch(_0x283d56){console['log']('error\x20pasting\x20brand');}console['log']('7.2'),documentTitle=_0x12d2ff(0x16f);if(_0x1700a6[_0x12d2ff(0x1a2)]=='free'){await pasteDescription(_0x1700a6);var _0x8f7268=await getBasicDescription(_0x1700a6);await addHtmlToEbayDescription(_0x8f7268),await postViaNetworkRequest(_0x12d2ff(0x15f),_0x8f7268),await waitForNetworkResponseCountIncrease();}else{if(_0x1700a6[_0x12d2ff(0x1a2)]==_0x12d2ff(0x174)){var _0x4d5028=await getDescriptionHtmlFromOpenAi(_0x1700a6),_0x3067b7=await createDescriptionFromTemplate2023(_0x4d5028,_0x1700a6);await addHtmlToEbayDescription(_0x3067b7),await postViaNetworkRequest('description',_0x3067b7),await waitForNetworkResponseCountIncrease();}else{if(_0x1700a6[_0x12d2ff(0x1a2)]==_0x12d2ff(0x189)){var _0x4d5028=await getDescriptionHtmlFromOpenAi(_0x1700a6);await addHtmlToEbayDescription(_0x4d5028),await postViaNetworkRequest('description',_0x4d5028),await waitForNetworkResponseCountIncrease();}else{if(_0x1700a6[_0x12d2ff(0x1a2)]==_0x12d2ff(0x1ab)){var _0x4d5028=await getDescriptionHtmlFromChatGpt(_0x1700a6),_0x3067b7=await createDescriptionFromTemplate2023(_0x4d5028,_0x1700a6);await addHtmlToEbayDescription(_0x3067b7),await postViaNetworkRequest(_0x12d2ff(0x15f),_0x3067b7),await waitForNetworkResponseCountIncrease();}}}}console[_0x12d2ff(0x181)](_0x12d2ff(0x193));var _0x1a9b1a=document[_0x12d2ff(0x18e)]('#gh');scrollIntoView(_0x1a9b1a),documentTitle=_0x12d2ff(0x179);var _0x266dec=document['querySelector'](_0x12d2ff(0x12e));_0x266dec&&(await postViaNetworkRequest('promotedListingSelection',!![]),await waitForNetworkResponseCountIncrease(),await postViaNetworkRequest('adRate',_0x12d2ff(0x182)),await waitForNetworkResponseCountIncrease());documentTitle='Adding\x20UPC',await clickShowMoreButton();var {domain:_0x368ce3}=await chrome[_0x12d2ff(0x1a3)][_0x12d2ff(0x166)]['get']('domain');if(_0x368ce3==_0x12d2ff(0x15b)){await postViaNetworkRequest(_0x12d2ff(0x1ad),_0x12d2ff(0x146)),await waitForNetworkResponseCountIncrease();var _0x55ac8e={'EAN':[_0x12d2ff(0x17a)]};await postViaNetworkRequest(_0x12d2ff(0x1b6),_0x55ac8e),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest(_0x12d2ff(0x134),_0x12d2ff(0x146)),await waitForNetworkResponseCountIncrease();var _0x55ac8e={'UPC':[_0x12d2ff(0x17a)]};await postViaNetworkRequest(_0x12d2ff(0x1b6),_0x55ac8e),await waitForNetworkResponseCountIncrease();}console['log']('Ready\x20To\x20Submit!'),documentTitle='Ready\x20To\x20Submit!',addSubmitButtonListener(_0x1700a6);var {autoSubmitEnabled:_0xcd94d5}=await chrome[_0x12d2ff(0x1a3)][_0x12d2ff(0x166)][_0x12d2ff(0x169)](_0x12d2ff(0x1b1));_0xcd94d5&&await submitTheListing();}function addSubmitButtonListener(_0x414e39){var _0x30453d=a0_0x499f93,_0xf3f5f=getSubmitButton();console['log'](_0x30453d(0x12a),_0xf3f5f),_0xf3f5f[_0x30453d(0x153)](_0x30453d(0x157),async function(){var _0x59682c=_0x30453d;console[_0x59682c(0x181)](_0x59682c(0x18f));var _0x56c20b=await checkIfItemWasSuccessfullyPosted(),_0xd24fd0='';try{_0xd24fd0=await getSuccessLink();}catch(_0x262936){}if(_0x56c20b)chrome['runtime'][_0x59682c(0x152)]({'type':_0x59682c(0x19f),'sku':_0x414e39[_0x59682c(0x191)],'message':'Item\x20Listed\x20Successfully!','ebayItemLink':_0xd24fd0,'eventType':_0x59682c(0x11f)}),changeFaviconOfPage(_0x59682c(0x167)),documentTitle='Submitted\x20the\x20Listing!',addSkuToSkuListAndSaveToLocalStorage(_0x414e39['sku']);else{var _0x20356f=getPageErrorMessage();console[_0x59682c(0x181)](_0x59682c(0x128),_0x20356f),!_0x20356f&&(_0x20356f=_0x59682c(0x1a0)+window[_0x59682c(0x148)][_0x59682c(0x127)]),chrome[_0x59682c(0x18b)]['sendMessage']({'type':_0x59682c(0x176),'sku':_0x414e39['sku'],'message':_0x20356f,'eventType':_0x59682c(0x11f)}),changeFaviconOfPage(_0x59682c(0x1b4)),documentTitle='Failed\x20to\x20Submit\x20the\x20Listing!';}});}function getPageErrorMessage(){var _0x25e050=a0_0x499f93,_0x567a77=document[_0x25e050(0x18e)](_0x25e050(0x195));if(_0x567a77){var _0x570b94=_0x567a77[_0x25e050(0x161)]+'\x20'+window[_0x25e050(0x148)][_0x25e050(0x127)];return _0x570b94;}else return null;}async function uploadImageConfig(_0x38d3e3){var _0x597d78=a0_0x499f93,_0x121640=[],_0x4c9043=_0x38d3e3['main_hd_images'],_0x4712e0=_0x38d3e3[_0x597d78(0x118)];console[_0x597d78(0x181)](_0x597d78(0x171),_0x4c9043[_0x597d78(0x177)]),console[_0x597d78(0x181)](_0x597d78(0x16e),_0x4712e0['length']),console[_0x597d78(0x181)](_0x597d78(0x11a),typeof _0x4c9043),console[_0x597d78(0x181)](_0x597d78(0x147),typeof _0x4712e0),_0x121640=_0x4c9043;_0x4c9043[_0x597d78(0x177)]<0x2&&_0x4712e0['length']>0x2?_0x121640=_0x4712e0:_0x121640=_0x4c9043;var _0x8f16af=_0x38d3e3[_0x597d78(0x180)];documentTitle='Uploading\x20Selected\x20Image';var _0x247667=_0x38d3e3[_0x597d78(0x196)];console['log'](_0x597d78(0x175)),await uploadImageAndWaitForCounterToUpload(_0x247667,_0x8f16af[_0x597d78(0x16a)](0x0,0x64)+'-'+0x1+_0x597d78(0x17c),IMAGES_TYPES[_0x597d78(0x12b)]),await waitForNetworkResponseCountIncrease(),console[_0x597d78(0x181)]('uploading\x20other\x20images'),console[_0x597d78(0x181)](_0x597d78(0x143),_0x121640['length']),documentTitle='Uploading\x20Images',await pasteImages(_0x121640,_0x8f16af,_0x38d3e3),documentTitle=_0x597d78(0x123);}async function waitForDescriptionNetworkResponse(){return new Promise((_0x167279,_0x591428)=>{async function _0xb5c7aa(){var _0x12d0aa=a0_0x52a5;receivedDescriptionNetworkResponse===!![]?(documentTitle=_0x12d0aa(0x145),console[_0x12d0aa(0x181)](_0x12d0aa(0x142)),_0x167279()):(documentTitle=_0x12d0aa(0x136),setTimeout(async()=>{await _0xb5c7aa();},0x64));}_0xb5c7aa();});}async function waitForNetworkResponseCountIncrease(){let _0x13c12c=totalNetworkResponse;return new Promise((_0x27b206,_0x3ef756)=>{async function _0xba5599(_0x5898ca=0x0){var _0x59cff9=a0_0x52a5;totalNetworkResponse>_0x13c12c?(waitSomeTime(0x64),_0x27b206()):(_0x5898ca++,_0x5898ca%0x3c===0x0&&(console[_0x59cff9(0x181)](_0x59cff9(0x162),_0x5898ca),console[_0x59cff9(0x181)](_0x59cff9(0x163)),await makeTabActive()),setTimeout(async()=>{await _0xba5599(_0x5898ca);},0x64)),_0x5898ca>0x3c&&(totalNetworkResponse=_0x13c12c+0x1,_0x27b206());}_0xba5599();});}function makeTabActive(){return new Promise((_0x3f3ebe,_0x542dd1)=>{var _0x4bd670=a0_0x52a5;chrome[_0x4bd670(0x18b)][_0x4bd670(0x152)]({'type':_0x4bd670(0x178)},async function(_0xfbe7e2){var _0x240371=_0x4bd670;previousTabId=_0xfbe7e2['previousTabId'],previousWindowId=_0xfbe7e2[_0x240371(0x14b)];var _0x1d4868='.smry.summary__title\x20input[name=\x27title\x27]',_0x31e7dc=document[_0x240371(0x18e)](_0x1d4868);await waitSomeTime(0xc8),_0x3f3ebe();});});}function waitUntilDocumentIsVisible(){return new Promise((_0x5530fd,_0x187169)=>{var _0x2411b6=setInterval(()=>{var _0xe85ba5=a0_0x52a5;document[_0xe85ba5(0x188)]===_0xe85ba5(0x1b0)&&(clearInterval(_0x2411b6),_0x5530fd());},0x32);});}function uploadImageAndWaitForCounterToUpload(_0x12fad7,_0x4f7d6a,_0x27777f){return new Promise((_0x323878,_0x50b9e1)=>{var _0x4f4aa4=a0_0x52a5,_0x495a27=getTotalImagesUploaded();console[_0x4f4aa4(0x181)](_0x4f4aa4(0x19c),_0x495a27),uploadImage(_0x12fad7,_0x4f4aa4(0x135),_0x4f7d6a,_0x27777f);var _0x3380ee=setInterval(()=>{var _0x3806a5=getTotalImagesUploaded();_0x3806a5>_0x495a27&&(clearInterval(_0x3380ee),_0x323878());},0x3e8);});}function uploadImageToIframeOnEbay(_0x48591b,_0x19a096,_0x27f19e){return new Promise((_0x2c1872,_0x12f65c)=>{var _0x2d5023=a0_0x52a5,_0x26ca63={'inputTagSelector':_0x27f19e,'imageName':_0x19a096,'b64Image':_0x48591b};console[_0x2d5023(0x181)]('uploadImageToIframeOnEbay\x20imageObject',_0x26ca63),uploadImage(_0x26ca63['b64Image'],_0x2d5023(0x135),_0x26ca63['imageName'],IMAGES_TYPES[_0x2d5023(0x12b)]);function _0x16967c(_0x30c02c){var _0x8cd639=_0x2d5023,_0xc12fe5=JSON['parse'](_0x30c02c['data']);_0xc12fe5[_0x8cd639(0x198)]===_0x8cd639(0x122)&&(window['removeEventListener'](_0x8cd639(0x1a4),_0x16967c,![]),_0x2c1872());}});}function waitForPicUploadIFrameToLoad(){return new Promise((_0x105c9b,_0x5e6fd3)=>{var _0xfdc94d=a0_0x52a5;window[_0xfdc94d(0x153)](_0xfdc94d(0x1a4),_0x35aef3,![]);function _0x35aef3(_0x15cd63){var _0x26772b=_0xfdc94d,_0x30a4a3=_0x15cd63[_0x26772b(0x11e)];console['log'](_0x26772b(0x13a),_0x30a4a3),(_0x30a4a3[_0x26772b(0x1aa)]||_0x30a4a3[_0x26772b(0x12d)])&&(console[_0x26772b(0x181)](_0x26772b(0x19d)),window[_0x26772b(0x150)](_0x26772b(0x1a4),_0x35aef3,![]),_0x105c9b());}});}function waitForPromotedListingIFrameToLoad(){return new Promise((_0x555c04,_0x23c130)=>{var _0x4b4bdb=a0_0x52a5;window[_0x4b4bdb(0x153)](_0x4b4bdb(0x1a4),_0x422c6d,![]);function _0x422c6d(_0xb94019){var _0x1d4b24=_0x4b4bdb,_0x515ebb=_0xb94019['data'];_0x515ebb[_0x1d4b24(0x12d)]&&(window[_0x1d4b24(0x150)](_0x1d4b24(0x1a4),_0x422c6d,![]),_0x555c04());}});}function promoteListing(_0x381f57){return new Promise((_0x3b6504,_0x263ce3)=>{var _0x5cb050=a0_0x52a5,_0xf4e6c4=document[_0x5cb050(0x144)]('promotedListing_iframe');_0xf4e6c4[_0x5cb050(0x13e)]['postMessage']({'type':_0x5cb050(0x14c),'adRate':_0x381f57},_0xf4e6c4[_0x5cb050(0x197)]),window[_0x5cb050(0x153)](_0x5cb050(0x1a4),_0x2dbd48,![]);function _0x2dbd48(_0x9749ff){var _0x57271b=_0x5cb050,_0x1a5249=JSON[_0x57271b(0x131)](_0x9749ff[_0x57271b(0x11e)]);_0x1a5249[_0x57271b(0x198)]===_0x57271b(0x14f)&&(window[_0x57271b(0x150)](_0x57271b(0x1a4),_0x2dbd48,![]),_0x3b6504());}});}async function listItem(){return new Promise((_0x421c43,_0x41e38c)=>{var _0x22958c=a0_0x52a5,_0x346a63=document[_0x22958c(0x144)](_0x22958c(0x185)),_0xee352c=_0x346a63[_0x22958c(0x18e)]('[value=\x27List\x20item\x27]');_0xee352c['click'](),_0xee352c['click'](),_0x421c43();});}function checkIfListingSucceeded(){return new Promise((_0x25aa0c,_0x44a0f9)=>{var _0x7096c1=a0_0x52a5,_0x4047af=document[_0x7096c1(0x144)]('success_msg');_0x4047af[_0x7096c1(0x13c)][_0x7096c1(0x154)]!==_0x7096c1(0x1b0)&&_0x25aa0c(![]),_0x4047af[_0x7096c1(0x13c)][_0x7096c1(0x154)]===_0x7096c1(0x1b0)&&_0x25aa0c(!![]);});}function waitSomeTime(_0xe6697f){return new Promise((_0x192d6c,_0x5380b2)=>{setTimeout(()=>{_0x192d6c();},_0xe6697f);});}function tellChromeToCloseTab(){var _0x294a7d=a0_0x499f93;chrome[_0x294a7d(0x18b)][_0x294a7d(0x152)]({'type':_0x294a7d(0x170)},function(_0x158ddd){});}function tellChromeToSwitchToPreviousTab(){return new Promise((_0x1d4dc6,_0x1e4751)=>{var _0x13cbf2=a0_0x52a5;console[_0x13cbf2(0x181)]('previousTabId:\x20',previousTabId),console[_0x13cbf2(0x181)](_0x13cbf2(0x124),previousWindowId),previousTabId&&previousWindowId?chrome[_0x13cbf2(0x18b)][_0x13cbf2(0x152)]({'type':'switchToWindowAndTab','windowId':previousWindowId,'tabId':previousTabId},function(_0x252938){var _0x1d18a3=_0x13cbf2;console[_0x1d18a3(0x181)]('response:\x20',_0x252938),_0x1d4dc6();}):_0x1d4dc6();});}function scrollIntoView(_0xd4bc65){var _0x345e7e=a0_0x499f93;_0xd4bc65[_0x345e7e(0x1a5)]({'behavior':_0x345e7e(0x125),'block':_0x345e7e(0x1a6),'inline':_0x345e7e(0x1a6)});}function createButtonToExecuteFunctionWithParameter(_0x2c91a7,_0x9d55ce,_0xcfbf7a){var _0x2c5d93=a0_0x499f93,_0x2e95a6=document[_0x2c5d93(0x129)](_0x2c5d93(0x140));_0x2e95a6[_0x2c5d93(0x130)]=_0x2c91a7,_0x2e95a6['onclick']=function(_0x239499){var _0x58c8c0=_0x2c5d93;_0x239499[_0x58c8c0(0x164)](),window[_0x2c91a7](_0xcfbf7a);},_0x9d55ce['querySelector'](_0x2c5d93(0x168)+_0x2c91a7+'\x27]')===null&&_0x9d55ce[_0x2c5d93(0x18e)]('button[innerHTML=\x27'+_0x2c91a7+'\x27]')===undefined&&_0x9d55ce[_0x2c5d93(0x1ac)](_0x2e95a6);}function createManuelInputButtons(_0x118911){var _0x193b25=a0_0x499f93,_0x884bb0=document[_0x193b25(0x18e)](_0x193b25(0x15e));createButtonToExecuteFunctionWithParameter(_0x193b25(0x137),_0x884bb0,_0x118911);var _0x884bb0=document[_0x193b25(0x18e)]('.summary__title\x20.se-textbox--fluid');createButtonToExecuteFunctionWithParameter(_0x193b25(0x1af),_0x884bb0,_0x118911[_0x193b25(0x180)]);var _0x884bb0=document[_0x193b25(0x18e)](_0x193b25(0x155));createButtonToExecuteFunctionWithParameter('pastePrice',_0x884bb0,Math[_0x193b25(0x159)](_0x118911[_0x193b25(0x116)]*1.25));var _0x884bb0=document['querySelector']('.summary__promoted-listing');createButtonToExecuteFunctionWithParameter(_0x193b25(0x13d),_0x884bb0,null);var _0x884bb0=document[_0x193b25(0x18e)]('.smry.summary__photos');createButtonToExecuteFunctionWithParameter('uploadImageConfig',_0x884bb0,_0x118911);}function checkIfTotalNetworkResponseIncreased(_0x49360b,_0x1fc528){window[_0x49360b](_0x1fc528),setInterval(function(){totalNetworkResponse>previousTotalNetworkResponse&&(previousTotalNetworkResponse=totalNetworkResponse,window[_0x49360b](_0x1fc528));},0x3e8);}