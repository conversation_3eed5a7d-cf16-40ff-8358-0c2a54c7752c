const a0_0x2f0380=a0_0x4e3f;(function(_0x172dcb,_0x175acb){const _0x1ad615=a0_0x4e3f,_0x340be6=_0x172dcb();while(!![]){try{const _0x2e0d5b=-parseInt(_0x1ad615(0x1f9))/0x1*(-parseInt(_0x1ad615(0x1e6))/0x2)+-parseInt(_0x1ad615(0x188))/0x3*(parseInt(_0x1ad615(0x1c0))/0x4)+parseInt(_0x1ad615(0x1df))/0x5+-parseInt(_0x1ad615(0x19e))/0x6+parseInt(_0x1ad615(0x196))/0x7*(parseInt(_0x1ad615(0x1dc))/0x8)+parseInt(_0x1ad615(0x1dd))/0x9*(parseInt(_0x1ad615(0x1fc))/0xa)+parseInt(_0x1ad615(0x18b))/0xb*(-parseInt(_0x1ad615(0x1b6))/0xc);if(_0x2e0d5b===_0x175acb)break;else _0x340be6['push'](_0x340be6['shift']());}catch(_0x1b8950){_0x340be6['push'](_0x340be6['shift']());}}}(a0_0x3ed1,0x1ea80));let rectCount=0x0,copiedObject=null;var customBannerText=a0_0x2f0380(0x1ed);const canvas=new fabric[(a0_0x2f0380(0x226))]('c',{'backgroundColor':a0_0x2f0380(0x1bb)}),testCanvas=new fabric[(a0_0x2f0380(0x226))]('testCanvas',{'backgroundColor':a0_0x2f0380(0x1bb)}),testImages=[a0_0x2f0380(0x19d),a0_0x2f0380(0x232),a0_0x2f0380(0x21c),a0_0x2f0380(0x1ef),a0_0x2f0380(0x1e5),a0_0x2f0380(0x1c6)];document[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x20f),()=>{initializeEventListeners();}),document['addEventListener']('keydown',async _0x5762e0=>{const _0x182672=a0_0x2f0380;if(document[_0x182672(0x1b9)][_0x182672(0x227)]===_0x182672(0x1a1)||document[_0x182672(0x1b9)][_0x182672(0x227)]===_0x182672(0x1fe))return;if(_0x5762e0[_0x182672(0x202)]&&_0x5762e0[_0x182672(0x185)]===_0x182672(0x206)){const _0x19214a=canvas[_0x182672(0x18c)]();_0x19214a&&(copiedObject=_0x19214a,navigator[_0x182672(0x1cd)][_0x182672(0x18d)](''),console[_0x182672(0x1f7)](_0x182672(0x20e),copiedObject));}else{if(copiedObject&&_0x5762e0[_0x182672(0x202)]&&_0x5762e0[_0x182672(0x185)]===_0x182672(0x217)){console[_0x182672(0x1f7)]('paste\x20event');try{const _0x2e4be6=await((async()=>{const _0x439c98=_0x182672,_0x2e648f=await navigator[_0x439c98(0x207)]['query']({'name':_0x439c98(0x1d1)});if(_0x2e648f[_0x439c98(0x224)]=='granted'||_0x2e648f['state']==_0x439c98(0x18a))return await navigator[_0x439c98(0x1cd)][_0x439c98(0x197)]();else throw new Error('Permission\x20to\x20read\x20clipboard\x20was\x20denied');})()),_0x1d5216=_0x2e4be6[_0x182672(0x1ac)](_0x446ce6=>_0x446ce6['types'][_0x182672(0x234)]('image/png'));if(_0x1d5216)return;}catch(_0x551e70){console[_0x182672(0x1a8)](_0x182672(0x199),_0x551e70);}let _0x323636;if(copiedObject[_0x182672(0x1e4)]==='group'&&!isNaN(copiedObject[_0x182672(0x1f8)](0x1)[_0x182672(0x1bf)])){rectCount++;const _0x46fe1c=new fabric[(_0x182672(0x1b0))]({'left':0x0,'top':0x0,'fill':copiedObject[_0x182672(0x1f8)](0x0)[_0x182672(0x1e8)],'width':copiedObject[_0x182672(0x1f8)](0x0)[_0x182672(0x236)]*copiedObject[_0x182672(0x1cb)],'height':copiedObject[_0x182672(0x1f8)](0x0)[_0x182672(0x22d)]*copiedObject['scaleY'],'stroke':copiedObject[_0x182672(0x1f8)](0x0)[_0x182672(0x1c3)],'strokeWidth':copiedObject[_0x182672(0x1f8)](0x0)[_0x182672(0x1d2)]}),_0x4622bd=new fabric[(_0x182672(0x19a))](rectCount[_0x182672(0x20c)](),{'left':_0x46fe1c['left']+_0x46fe1c[_0x182672(0x236)]/0x2,'top':_0x46fe1c[_0x182672(0x1e9)]+_0x46fe1c['height']/0x2,'fontSize':copiedObject[_0x182672(0x1f8)](0x1)[_0x182672(0x186)],'originX':_0x182672(0x203),'originY':_0x182672(0x203)});_0x323636=new fabric[(_0x182672(0x221))]([_0x46fe1c,_0x4622bd],{'left':copiedObject[_0x182672(0x1b3)]+0xa,'top':copiedObject[_0x182672(0x1e9)]+0xa}),canvas['add'](_0x323636);}else _0x323636=new fabric['Image'](copiedObject[_0x182672(0x22e)](),{'left':copiedObject[_0x182672(0x1b3)]+0xa,'top':copiedObject[_0x182672(0x1e9)]+0xa,'width':copiedObject['width'],'height':copiedObject[_0x182672(0x22d)],'scaleX':copiedObject[_0x182672(0x1cb)],'scaleY':copiedObject[_0x182672(0x22c)]}),canvas[_0x182672(0x1b2)](_0x323636);canvas[_0x182672(0x20b)]();}}}),document[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x22b),_0x3c21f2=>{const _0x50ed87=a0_0x2f0380;_0x3c21f2[_0x50ed87(0x202)]&&_0x3c21f2['code']==='KeyV'&&navigator[_0x50ed87(0x1cd)][_0x50ed87(0x197)]()[_0x50ed87(0x1d5)](_0x3930f5=>{const _0x4c7235=_0x50ed87;_0x3930f5[_0x4c7235(0x200)]>0x0&&_0x3930f5[0x0][_0x4c7235(0x1a0)][_0x4c7235(0x234)](_0x4c7235(0x1f1))&&_0x3c21f2['preventDefault']();})['catch'](_0x14ae64=>{console['error'](_0x14ae64);});}),document[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x22b),async _0x16af2f=>{const _0xd6d55c=a0_0x2f0380;console[_0xd6d55c(0x1f7)](_0xd6d55c(0x198));if(_0x16af2f[_0xd6d55c(0x1d6)]&&_0x16af2f[_0xd6d55c(0x1d6)]['items']){const _0x4a9028=Array['from'](_0x16af2f['clipboardData'][_0xd6d55c(0x1a4)])[_0xd6d55c(0x1ac)](_0x5b9795=>_0x5b9795[_0xd6d55c(0x1e4)]['includes'](_0xd6d55c(0x1f4)));console[_0xd6d55c(0x1f7)]('item',_0x4a9028);if(_0x4a9028){_0x16af2f[_0xd6d55c(0x210)](),console[_0xd6d55c(0x1f7)](_0xd6d55c(0x237));const _0x5831d2=_0x4a9028['getAsFile'](),_0x54245d=new FileReader();_0x54245d[_0xd6d55c(0x190)]=function(_0x30c043){const _0x45dc48=_0xd6d55c,_0xf4ffe7=_0x30c043[_0x45dc48(0x1b7)][_0x45dc48(0x235)];fabric[_0x45dc48(0x18e)][_0x45dc48(0x18f)](_0xf4ffe7,_0x26dbef=>{const _0x5366c8=_0x45dc48;_0x26dbef['set']({'left':0x32,'top':0x32}),_0x26dbef[_0x5366c8(0x1d4)](0.5),canvas[_0x5366c8(0x1b2)](_0x26dbef),canvas[_0x5366c8(0x20b)]();});},_0x54245d[_0xd6d55c(0x1cc)](_0x5831d2);}}});function initializeEventListeners(){const _0x2d2eaf=a0_0x2f0380;loadTemplate(),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x1a9))[_0x2d2eaf(0x1c2)]('click',addRectangle),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x1a2))[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x1c1),uploadBadge),document[_0x2d2eaf(0x201)]('saveTemplate')[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x230),saveTemplate),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x189))[_0x2d2eaf(0x1c2)]('click',loadTemplate),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x219))[_0x2d2eaf(0x1c2)]('click',testTemplate),document['getElementById'](_0x2d2eaf(0x22f))['addEventListener'](_0x2d2eaf(0x230),clearCanvas),document['getElementById']('backToEditor')[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x230),backToEditor),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x1a5))[_0x2d2eaf(0x1c2)]('click',deleteObject),document[_0x2d2eaf(0x201)]('fetchBadgeFromUrl')['addEventListener'](_0x2d2eaf(0x230),async()=>{const _0x4f4f50=_0x2d2eaf,_0x249dbb=document['getElementById'](_0x4f4f50(0x1d9))[_0x4f4f50(0x1f3)];if(_0x249dbb){const _0x35e1d8=await loadImage(_0x249dbb),_0x3c6ef1=await processImage(_0x35e1d8,_0x35e1d8[_0x4f4f50(0x236)],_0x35e1d8[_0x4f4f50(0x22d)]),_0x4464a3=new fabric['Image'](_0x3c6ef1,{'left':0x32,'top':0x32});_0x4464a3[_0x4f4f50(0x1a7)](0xc8),_0x4464a3['scaleToHeight'](0xc8),canvas[_0x4f4f50(0x1b2)](_0x4464a3);}}),document[_0x2d2eaf(0x201)](_0x2d2eaf(0x1be))[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x230),addBanner),window[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x1d3),_0x4fccf5=>{const _0x1caef2=_0x2d2eaf;_0x4fccf5[_0x1caef2(0x191)]===_0x1caef2(0x1aa)&&!(document[_0x1caef2(0x1b9)]instanceof HTMLInputElement)&&deleteObject();}),document['querySelectorAll'](_0x2d2eaf(0x1ec))[_0x2d2eaf(0x1b8)](_0x411a1a=>{const _0x2a7b01=_0x2d2eaf;_0x411a1a[_0x2a7b01(0x1c2)]('focus',()=>{const _0x2c287e=_0x2a7b01;canvas[_0x2c287e(0x216)]()[_0x2c287e(0x20b)]();});}),document[_0x2d2eaf(0x201)]('predefinedBannerText')[_0x2d2eaf(0x1c2)](_0x2d2eaf(0x1c1),async()=>{const _0x1953f5=_0x2d2eaf,_0x83c08c=document[_0x1953f5(0x201)](_0x1953f5(0x1f5))['value'];console['log'](_0x1953f5(0x1ff),_0x83c08c),document[_0x1953f5(0x201)](_0x1953f5(0x1ff))['value']=_0x83c08c;});}function addRectangle(){const _0x5268eb=a0_0x2f0380;rectCount++;const _0x1787ed=parseFloat(document['getElementById'](_0x5268eb(0x1fd))[_0x5268eb(0x1f3)])||0x1,_0x41b033=new fabric['Rect']({'left':0x32,'top':0x32,'fill':'transparent','width':0xc8,'height':0xc8,'stroke':_0x5268eb(0x1c7),'strokeWidth':0x5,'opacity':_0x1787ed}),_0x3b125a=new fabric['Text'](rectCount[_0x5268eb(0x20c)](),{'left':_0x41b033[_0x5268eb(0x1b3)]+_0x41b033[_0x5268eb(0x236)]/0x2,'top':_0x41b033[_0x5268eb(0x1e9)]+_0x41b033[_0x5268eb(0x22d)]/0x2,'fontSize':0x14,'originX':_0x5268eb(0x203),'originY':_0x5268eb(0x203)}),_0x108af6=new fabric[(_0x5268eb(0x221))]([_0x41b033,_0x3b125a],{});canvas[_0x5268eb(0x1b2)](_0x108af6);}function uploadBadge(_0x440bec){const _0x3a431c=a0_0x2f0380;Array[_0x3a431c(0x1da)](_0x440bec[_0x3a431c(0x1b7)]['files'])[_0x3a431c(0x1b8)](_0x5cd12c=>{const _0x4b5c2c=_0x3a431c,_0xe9e007=new FileReader();_0xe9e007[_0x4b5c2c(0x190)]=_0x304add=>{const _0x1fc1ed=_0x4b5c2c;fabric[_0x1fc1ed(0x18e)][_0x1fc1ed(0x18f)](_0x304add[_0x1fc1ed(0x1b7)][_0x1fc1ed(0x235)],_0x219c68=>canvas[_0x1fc1ed(0x1b2)](_0x219c68));},_0xe9e007[_0x4b5c2c(0x1cc)](_0x5cd12c);});}async function saveTemplate(){const _0x32da12=a0_0x2f0380,_0x2bf32e=JSON['stringify'](canvas['toJSON']([_0x32da12(0x193),'border']));chrome[_0x32da12(0x1a3)][_0x32da12(0x1b5)][_0x32da12(0x1db)]({'template':_0x2bf32e},async()=>{const _0x6927d9=_0x32da12;alert('Template\x20saved!'),chrome[_0x6927d9(0x1a3)][_0x6927d9(0x1b5)]['get'](_0x6927d9(0x223),_0x532586=>{const _0x41ea48=_0x6927d9;console[_0x41ea48(0x1f7)](_0x532586);});var {template:_0x1f2654}=await chrome[_0x6927d9(0x1a3)][_0x6927d9(0x1b5)]['get'](_0x6927d9(0x223));console[_0x6927d9(0x1f7)](_0x6927d9(0x1cf),_0x1f2654);});}async function loadTemplate(){const _0x32c789=a0_0x2f0380;var {template:_0x11af4f}=await chrome[_0x32c789(0x1a3)][_0x32c789(0x1b5)][_0x32c789(0x1ca)](_0x32c789(0x223));_0x11af4f?canvas[_0x32c789(0x195)](_0x11af4f,()=>{const _0x176f5f=_0x32c789;canvas[_0x176f5f(0x20b)](),updateRectCount();}):alert(_0x32c789(0x1c8));}function updateRectCount(){const _0x23cd38=a0_0x2f0380;let _0x313e5f=0x0;canvas[_0x23cd38(0x233)]()[_0x23cd38(0x1b8)](_0x34b413=>{const _0x2ea7a3=_0x23cd38;if(_0x34b413[_0x2ea7a3(0x22a)]('group')){const _0x490fde=parseInt(_0x34b413[_0x2ea7a3(0x1f8)](0x1)[_0x2ea7a3(0x1bf)],0xa);if(_0x490fde>_0x313e5f)_0x313e5f=_0x490fde;}}),rectCount=_0x313e5f;}async function testTemplate(){const _0x3c6e15=a0_0x2f0380;document[_0x3c6e15(0x201)](_0x3c6e15(0x222))['hidden']=!![],document[_0x3c6e15(0x201)](_0x3c6e15(0x1c4))[_0x3c6e15(0x1fb)]=![];var _0x57aaee={'title':_0x3c6e15(0x231),'descriptionAndFeatures':_0x3c6e15(0x214)};await populateTemplate(testCanvas,testImages,_0x57aaee);}function clearCanvas(){const _0x28eb7c=a0_0x2f0380;canvas[_0x28eb7c(0x1b1)](),rectCount=0x0;}function backToEditor(){const _0x143bd9=a0_0x2f0380;document[_0x143bd9(0x201)](_0x143bd9(0x222))[_0x143bd9(0x1fb)]=![],document[_0x143bd9(0x201)]('tester')[_0x143bd9(0x1fb)]=!![];}function a0_0x4e3f(_0x58be2a,_0x2075d9){const _0x3ed1b9=a0_0x3ed1();return a0_0x4e3f=function(_0x4e3f80,_0x313c2f){_0x4e3f80=_0x4e3f80-0x185;let _0x167193=_0x3ed1b9[_0x4e3f80];return _0x167193;},a0_0x4e3f(_0x58be2a,_0x2075d9);}function deleteObject(){const _0x4d88a3=a0_0x2f0380,_0x479d90=canvas[_0x4d88a3(0x18c)]();_0x479d90&&canvas[_0x4d88a3(0x1bc)](_0x479d90);}async function loadImage(_0x2ece26){return new Promise(_0x38c433=>{const _0x3dc01d=new Image();_0x3dc01d['src']=_0x2ece26,_0x3dc01d['onload']=()=>_0x38c433(_0x3dc01d);});}async function processImage(_0x20b7c8,_0x1d7c8c,_0x71be48){var _0x524321=await resizeImage(_0x20b7c8,_0x1d7c8c,_0x71be48);return _0x524321=await makeImageTransparent(_0x524321),await getRoundedImage(_0x524321,0x5);}document['getElementById'](a0_0x2f0380(0x1e2))[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),exportTemplate),document['getElementById']('btnImportTemplate')[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),()=>{const _0x261a84=a0_0x2f0380;document[_0x261a84(0x201)](_0x261a84(0x1e3))[_0x261a84(0x230)]();}),document[a0_0x2f0380(0x201)]('importTemplate')[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x1c1),importTemplate);function exportTemplate(){const _0x53e0fb=a0_0x2f0380,_0x5c7e34=JSON[_0x53e0fb(0x229)](canvas[_0x53e0fb(0x213)]([_0x53e0fb(0x193)])),_0x1d3473=new Blob([_0x5c7e34],{'type':_0x53e0fb(0x1eb)}),_0x2caaf6=URL[_0x53e0fb(0x19c)](_0x1d3473),_0x5d3348=document[_0x53e0fb(0x225)]('a');_0x5d3348[_0x53e0fb(0x1ba)]=_0x2caaf6,_0x5d3348[_0x53e0fb(0x1d0)]=_0x53e0fb(0x212),document['body'][_0x53e0fb(0x205)](_0x5d3348),_0x5d3348[_0x53e0fb(0x230)](),window[_0x53e0fb(0x1e0)][_0x53e0fb(0x20d)](_0x2caaf6);}function importTemplate(_0xe0a71){const _0x3ce378=a0_0x2f0380,_0x197823=_0xe0a71[_0x3ce378(0x1b7)][_0x3ce378(0x19b)][0x0];if(_0x197823){const _0x2993fb=new FileReader();_0x2993fb['onload']=_0x58bb0c=>{const _0x322449=_0x3ce378,_0x1cab22=_0x58bb0c[_0x322449(0x1b7)][_0x322449(0x235)];canvas[_0x322449(0x195)](_0x1cab22,()=>{canvas['renderAll'](),updateRectCount();});},_0x2993fb[_0x3ce378(0x187)](_0x197823);}}function downloadImage(){const _0x5a42c1=a0_0x2f0380,_0x27600f=testCanvas[_0x5a42c1(0x20a)],_0xcfa3d5=new fabric['Rect']({'left':0x0,'top':0x0,'width':testCanvas[_0x5a42c1(0x236)],'height':testCanvas[_0x5a42c1(0x22d)],'fill':_0x5a42c1(0x1bb)});testCanvas[_0x5a42c1(0x1b2)](_0xcfa3d5),_0xcfa3d5[_0x5a42c1(0x21f)]();const _0x42f3bb=testCanvas[_0x5a42c1(0x193)]({'format':_0x5a42c1(0x215),'quality':0x1});testCanvas[_0x5a42c1(0x1bc)](_0xcfa3d5);const _0x3f9904=document[_0x5a42c1(0x225)]('a');_0x3f9904['download']='test-image.jpeg',_0x3f9904['href']=_0x42f3bb,document[_0x5a42c1(0x21e)]['appendChild'](_0x3f9904),_0x3f9904[_0x5a42c1(0x230)](),document[_0x5a42c1(0x21e)]['removeChild'](_0x3f9904),testCanvas[_0x5a42c1(0x20a)]=_0x27600f,testCanvas[_0x5a42c1(0x20b)]();}document[a0_0x2f0380(0x201)]('downloadImage')[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),downloadImage);function throttle(_0xb66bd6,_0xc8cde4){let _0x54a7c4=![];return function(){const _0x47db4b=a0_0x4e3f;!_0x54a7c4&&(_0xb66bd6[_0x47db4b(0x1e7)](this,arguments),_0x54a7c4=!![],setTimeout(()=>{_0x54a7c4=![];},_0xc8cde4));};}let hGuideLine=null,vGuideLine=null;canvas['on'](a0_0x2f0380(0x19f),throttle(_0x4787d5=>{const _0x17d8ad=a0_0x2f0380,_0x598db4=_0x4787d5[_0x17d8ad(0x1b7)];clearGuidelines(),canvas['getObjects']()['forEach'](_0x29726b=>{const _0x3b5441=_0x17d8ad;if(_0x29726b===_0x598db4||_0x29726b[_0x3b5441(0x1e4)]===_0x3b5441(0x204))return;Math[_0x3b5441(0x1de)](_0x598db4[_0x3b5441(0x1b3)]-_0x29726b['left'])<0xa&&_0x598db4['set']({'left':_0x29726b['left']}),Math['abs'](_0x598db4['top']-_0x29726b[_0x3b5441(0x1e9)])<0xa&&_0x598db4[_0x3b5441(0x1db)]({'top':_0x29726b[_0x3b5441(0x1e9)]});}),canvas[_0x17d8ad(0x20b)]();},0x32));function drawGuideline(_0x3a0c60,_0x3c57c8,_0x5da7d2,_0x39087c){const _0x1d81de=a0_0x2f0380;!hGuideLine&&(hGuideLine=new fabric[(_0x1d81de(0x220))]([0x0,0x0,0x0,0x0],{'stroke':_0x1d81de(0x1bd),'selectable':![],'evented':![]}),canvas['add'](hGuideLine)),!vGuideLine&&(vGuideLine=new fabric['Line']([0x0,0x0,0x0,0x0],{'stroke':_0x1d81de(0x1bd),'selectable':![],'evented':![]}),canvas['add'](vGuideLine)),_0x3a0c60===_0x5da7d2?(vGuideLine['set']({'x1':_0x3a0c60,'y1':_0x3c57c8,'x2':_0x5da7d2,'y2':_0x39087c}),vGuideLine['setCoords']()):(hGuideLine[_0x1d81de(0x1db)]({'x1':_0x3a0c60,'y1':_0x3c57c8,'x2':_0x5da7d2,'y2':_0x39087c}),hGuideLine[_0x1d81de(0x1a6)]());}function a0_0x3ed1(){const _0x271ad2=['change','addEventListener','stroke','tester','selection:cleared','https://m.media-amazon.com/images/I/711A-vi410L._AC_SL1000_.jpg','black','No\x20saved\x20template\x20found!','Arial','get','scaleX','readAsDataURL','clipboard','\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20L\x20','test\x202','download','clipboard-read','strokeWidth','keydown','scale','then','clipboardData','\x200\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20L\x20','setActiveObject','badgeUrl','from','set','944CnSIJp','63tvrcIC','abs','71695wICOXQ','URL','getWidth','exportTemplate','importTemplate','type','https://m.media-amazon.com/images/I/61SBYOJ0r6L._AC_SL1000_.jpg','286nlfcrB','apply','fill','top','Path','application/json','input','Plastic\x20Expert','M\x20-','https://m.media-amazon.com/images/I/61P8yDGTczL._AC_SL1000_.jpg','Best\x20Seller','image/png','bold','value','image','predefinedBannerText','activeObject','log','item','1549NqEsXZ','selection:created','hidden','51780ZcZvdy','opacitySlider','TEXTAREA','bannerText','length','getElementById','ctrlKey','center','line','appendChild','KeyC','permissions','#FFA500','getHeight','backgroundColor','renderAll','toString','revokeObjectURL','copiedObject','DOMContentLoaded','preventDefault','bannerColor','template.json','toJSON','7\x22\x20display,\x2016\x20or\x2032\x20GB\x20of\x20internal\x20storage\x20(up\x20to\x20512\x20GB\x20of\x20expandable\x20storage\x20with\x20microSD).\x20Faster\x201.3\x20GHz\x20quad-core\x20processor\x20and\x201\x20GB\x20of\x20RAM.\x20Up\x20to\x207\x20hours\x20of\x20reading,\x20browsing\x20the\x20web,\x20watching\x20video,\x20and\x20listening\x20to\x20music.\x20Hands-free\x20with\x20Alexa,\x20including\x20on/off\x20toggle.\x201-year\x20limited\x20warranty.\x2090-day\x20limited\x20warranty\x20for\x20certified\x20refurbished\x20products.','jpeg','discardActiveObject','KeyV','group','testTemplate','rect','updateSliderWithGroupRect','https://m.media-amazon.com/images/I/6149ETXCDHL._AC_SL1000_.jpg','\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20z','body','sendToBack','Line','Group','editor','template','state','createElement','Canvas','tagName','\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20L\x20-','stringify','isType','paste','scaleY','height','getElement','clearCanvas','click','Kindle\x20Fire\x20Tablet\x20|\x207\x22\x20display,\x2016\x20GB,\x20Black\x20-\x20with\x20Ads','https://m.media-amazon.com/images/I/61ExVdn4flL._AC_SY450_.jpg','getObjects','includes','result','width','item\x20is\x20image','code','fontSize','readAsText','36747DKHtbh','loadTemplate','prompt','1568743ZRxqwO','getActiveObject','writeText','Image','fromURL','onload','key','opacity','toDataURL','selection:updated','loadFromJSON','3661pVYhon','read','paste\x20event\x20for\x20image','An\x20error\x20occurred\x20while\x20reading\x20the\x20clipboard:','Text','files','createObjectURL','https://m.media-amazon.com/images/I/61NQHkhutyL._AC_SY450_.jpg','320256SqMgJB','object:moving','types','INPUT','uploadBadge','storage','items','delete','setCoords','scaleToWidth','error','addRect','Backspace','canvasBorderColor','find','bannerFontColor','selected','bannerFont','Rect','clear','add','left','push','local','12ESxRrB','target','forEach','activeElement','href','white','remove','red','addBanner','text','4JJAhXh'];a0_0x3ed1=function(){return _0x271ad2;};return a0_0x3ed1();}function clearGuidelines(){const _0x3abf01=a0_0x2f0380;hGuideLine&&(hGuideLine[_0x3abf01(0x1db)]({'x1':0x0,'y1':0x0,'x2':0x0,'y2':0x0}),hGuideLine[_0x3abf01(0x1a6)]()),vGuideLine&&(vGuideLine[_0x3abf01(0x1db)]({'x1':0x0,'y1':0x0,'x2':0x0,'y2':0x0}),vGuideLine[_0x3abf01(0x1a6)]());}var canvasBorders=[];function addBorderToCanvas(_0x3abd23,_0x55417f){const _0x4876fc=a0_0x2f0380;canvasBorders[_0x4876fc(0x1b8)](_0x103044=>canvas[_0x4876fc(0x1bc)](_0x103044)),canvasBorders=[];const _0x259aac=new fabric[(_0x4876fc(0x1b0))]({'left':0x0,'top':0x0,'width':canvas['getWidth'](),'height':_0x55417f,'fill':_0x3abd23,'selectable':!![],'evented':!![]}),_0x350fa7=new fabric[(_0x4876fc(0x1b0))]({'left':0x0,'top':canvas[_0x4876fc(0x209)]()-_0x55417f,'width':canvas[_0x4876fc(0x1e1)](),'height':_0x55417f,'fill':_0x3abd23,'selectable':!![],'evented':!![]}),_0x612e7d=new fabric['Rect']({'left':0x0,'top':0x0,'width':_0x55417f,'height':canvas[_0x4876fc(0x209)](),'fill':_0x3abd23,'selectable':!![],'evented':!![]}),_0x15c5ef=new fabric[(_0x4876fc(0x1b0))]({'left':canvas[_0x4876fc(0x1e1)]()-_0x55417f,'top':0x0,'width':_0x55417f,'height':canvas['getHeight'](),'fill':_0x3abd23,'selectable':!![],'evented':!![]});canvasBorders[_0x4876fc(0x1b4)](_0x259aac,_0x350fa7,_0x612e7d,_0x15c5ef),canvasBorders[_0x4876fc(0x1b8)](_0x64ad6b=>canvas[_0x4876fc(0x1b2)](_0x64ad6b)),canvas['renderAll']();}function handleAddCanvasBorder(){const _0x125394=a0_0x2f0380,_0x5647bc=document['getElementById'](_0x125394(0x1ab))[_0x125394(0x1f3)],_0x315e04=parseInt(document[_0x125394(0x201)]('canvasBorderThickness')['value']);addBorderToCanvas(_0x5647bc,_0x315e04);}document[a0_0x2f0380(0x201)]('addBorder')[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),handleAddCanvasBorder);function sendToBack(){const _0x597da8=a0_0x2f0380,_0x29dd7e=canvas[_0x597da8(0x18c)]();_0x29dd7e&&(canvas['sendToBack'](_0x29dd7e),canvas[_0x597da8(0x20b)]());}document[a0_0x2f0380(0x201)](a0_0x2f0380(0x21f))[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),sendToBack);const opacitySlider=document[a0_0x2f0380(0x201)]('opacitySlider');opacitySlider['addEventListener']('input',function(){const _0x14d35e=a0_0x2f0380,_0x476d0b=canvas['getActiveObject']();if(_0x476d0b&&_0x476d0b[_0x14d35e(0x1e4)]===_0x14d35e(0x218)){const _0x3829ad=_0x476d0b[_0x14d35e(0x233)]()[_0x14d35e(0x1ac)](_0x3ceef1=>_0x3ceef1[_0x14d35e(0x1e4)]===_0x14d35e(0x21a));_0x3829ad&&(console[_0x14d35e(0x1f7)](_0x3829ad[_0x14d35e(0x192)][_0x14d35e(0x20c)]()),_0x3829ad[_0x14d35e(0x1db)](_0x14d35e(0x192),parseFloat(this['value'])),canvas[_0x14d35e(0x20b)]());}});let lastSelectedObject=null;function updateSliderWithGroupRect(_0x249809){const _0x3eb575=a0_0x2f0380;console['log'](_0x3eb575(0x21b));const _0x5615f8=_0x249809[_0x3eb575(0x1ae)][0x0]||canvas[_0x3eb575(0x18c)]();console[_0x3eb575(0x1f7)](_0x5615f8['type']);if(_0x5615f8&&_0x5615f8['type']==='group'){console[_0x3eb575(0x1f7)](_0x3eb575(0x1f6),_0x5615f8);const _0x2cd304=_0x5615f8[_0x3eb575(0x233)]()[_0x3eb575(0x1ac)](_0xeac3e7=>_0xeac3e7[_0x3eb575(0x1e4)]===_0x3eb575(0x21a));_0x2cd304&&(console[_0x3eb575(0x1f7)](_0x2cd304[_0x3eb575(0x192)]['toString']()),opacitySlider['value']=_0x2cd304[_0x3eb575(0x192)]['toString'](),lastSelectedObject=_0x2cd304);}else opacitySlider[_0x3eb575(0x1f3)]='1',lastSelectedObject=null;}opacitySlider[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x1ec),function(){const _0x4c3e6a=a0_0x2f0380;lastSelectedObject&&(lastSelectedObject['set'](_0x4c3e6a(0x192),parseFloat(this[_0x4c3e6a(0x1f3)])),canvas[_0x4c3e6a(0x20b)]());}),canvas['on'](a0_0x2f0380(0x1fa),updateSliderWithGroupRect),canvas['on'](a0_0x2f0380(0x194),updateSliderWithGroupRect),canvas['on'](a0_0x2f0380(0x1c5),function(){const _0x5f3860=a0_0x2f0380;console[_0x5f3860(0x1f7)]('selection:cleared'),opacitySlider[_0x5f3860(0x1f3)]='1';});function createBestSellerRibbon(){const _0x15b732=a0_0x2f0380,_0x1a271b=document[_0x15b732(0x201)]('bannerText')[_0x15b732(0x1f3)]||_0x15b732(0x1f0),_0x2a7662=document[_0x15b732(0x201)](_0x15b732(0x211))[_0x15b732(0x1f3)]||_0x15b732(0x208),_0x169240=document[_0x15b732(0x201)](_0x15b732(0x1af))[_0x15b732(0x1f3)]||_0x15b732(0x1c9),_0x13f71b=document['getElementById'](_0x15b732(0x1ad))['value']||_0x15b732(0x1bb),_0x47672a=0x14,_0x5333f0=new fabric['Text'](_0x1a271b,{'fontSize':0x23,'fontFamily':_0x169240,'fontWeight':_0x15b732(0x1f2),'fill':_0x13f71b,'originX':_0x15b732(0x203),'originY':'center'}),_0x44ce28=_0x5333f0[_0x15b732(0x236)]+_0x47672a*0x2,_0x1920e2=_0x5333f0[_0x15b732(0x22d)]+_0x47672a,_0x217ee5=0x1e,_0xf08798=_0x15b732(0x1ee)+_0x44ce28/0x2+'\x20'+_0x1920e2/0x2+_0x15b732(0x228)+_0x44ce28/0x2+'\x20-'+_0x1920e2/0x2+_0x15b732(0x1ce)+_0x44ce28/0x2+'\x20-'+_0x1920e2/0x2+'\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20L\x20'+(_0x44ce28/0x2+_0x217ee5)+_0x15b732(0x1d7)+_0x44ce28/0x2+'\x20'+_0x1920e2/0x2+_0x15b732(0x21d),_0x32cba1=new fabric[(_0x15b732(0x1ea))](_0xf08798,{'fill':_0x2a7662,'stroke':'black','strokeWidth':0x1,'originX':_0x15b732(0x203),'originY':'center'}),_0xdc0d0e=new fabric[(_0x15b732(0x221))]([_0x32cba1,_0x5333f0],{'left':0x1f4,'top':0x1f4,'originX':_0x15b732(0x203),'originY':'center'});canvas['add'](_0xdc0d0e),canvas[_0x15b732(0x1d8)](_0xdc0d0e),canvas[_0x15b732(0x20b)]();}document[a0_0x2f0380(0x201)]('createRibbon')[a0_0x2f0380(0x1c2)](a0_0x2f0380(0x230),createBestSellerRibbon);