var a0_0x23dad8=a0_0xf659;(function(_0x3a7ae9,_0x595360){var _0x1d70fa=a0_0xf659,_0x5b7b36=_0x3a7ae9();while(!![]){try{var _0x3a6af4=-parseInt(_0x1d70fa(0x1bd))/0x1*(-parseInt(_0x1d70fa(0x1cd))/0x2)+parseInt(_0x1d70fa(0x17e))/0x3+parseInt(_0x1d70fa(0x187))/0x4+parseInt(_0x1d70fa(0x1a5))/0x5*(parseInt(_0x1d70fa(0x184))/0x6)+parseInt(_0x1d70fa(0x190))/0x7*(parseInt(_0x1d70fa(0x1a2))/0x8)+parseInt(_0x1d70fa(0x195))/0x9+parseInt(_0x1d70fa(0x1ad))/0xa*(-parseInt(_0x1d70fa(0x1cc))/0xb);if(_0x3a6af4===_0x595360)break;else _0x5b7b36['push'](_0x5b7b36['shift']());}catch(_0x25ad3a){_0x5b7b36['push'](_0x5b7b36['shift']());}}}(a0_0x5257,0x75064),console[a0_0x23dad8(0x1ca)](a0_0x23dad8(0x1d2)),onPageLoadAndStableNotifyBackground());var requestParams,skuList=[],table_rows_pattern=a0_0x23dad8(0x1d4);function a0_0x5257(){var _0x49211e=['addEventListener','18FInzsU','then','minSold','3778404nKRMqW','Error\x20highlighting\x20low\x20performing\x20nodes:','isWorkingPage','start_relisting','.filter-group--actions','storage','From\x20the\x20extension\x20request.type\x20ebay.js','Available\x20quantity','data','7FqdBTo','Custom\x20label\x20(SKU)','Verkaufte\x20Stückzahl','skuList','add','7978680fWyAip','trim','minSoldQuantity','added\x20low\x20performing\x20class\x20to\x20nodes','Aufrufe\x20(30\x20Tage)','onMessage','Displaying\x20options','itemTitleCell','error','Verfügbare\x20Anzahl','runtime','forEach','Views\x20(30\x20days)','1288168jgLAEw','length','end_low_performing_items','857245UvDeJT','ebayData','minViewCount','end_low_performing_items_to_sell_similar','click_bulk_revise_menu_option','firstElementChild','itemNumber','minViews','579470hggpio','local','attempting_to_ending_low_performing_items','remove','response','Adding\x20relist\x20buttons\x20to\x20items','.column-title__text\x20a','Sold','catch','.smart-relist-button','reload','low-performing','availableQuantity','td[class*=\x22listingSKU\x22]\x20.cell-wrapper','save_sku_on_page','increase_quantity','7xuOWQU','no_items_to_end','get','totalMenuOptions','.inline-notice.inline-notice--confirmation\x20.inline-notice__main\x20p','addListener','.bulk-smart-relist-button','count_total_menu_options','lastError','classList','optionIndex','type','ending_low_performing_items_wait_for_tab_refresh','log','Sold\x20quantity','440OBmjbF','49822JSZODe','appendChild','querySelector','sendMessage','body','ebay.active_listings.js','relistButton\x20clicked','tbody[id*=grid-row]','resend_message_to_tab_on_update_and_notified','Preparing\x20sell\x20similar\x20workspace','click','349038ovJJjo','end-item','tab','textContent','Page\x20not\x20loaded\x20properly'];a0_0x5257=function(){return _0x49211e;};return a0_0x5257();}chrome[a0_0x23dad8(0x19f)][a0_0x23dad8(0x19a)][a0_0x23dad8(0x1c2)]((_0x1690c4,_0x4a67c3,_0x279e3e)=>{var _0x5797aa=a0_0x23dad8;console[_0x5797aa(0x1ca)](_0x4a67c3['tab']?'From\x20a\x20content\x20script:'+_0x4a67c3[_0x5797aa(0x180)]['url']:_0x5797aa(0x18d)+_0x1690c4['type']);_0x1690c4[_0x5797aa(0x1c8)]===_0x5797aa(0x189)&&(console['log'](_0x5797aa(0x189)),getAllSKU(_0x1690c4[_0x5797aa(0x18f)]));_0x1690c4[_0x5797aa(0x1c8)]===_0x5797aa(0x1bb)&&(console['log']('save_sku_on_page'),chrome[_0x5797aa(0x18c)]['local'][_0x5797aa(0x1bf)](_0x5797aa(0x193),_0x5484f4=>{var _0x4b794c=_0x5797aa;console[_0x4b794c(0x1ca)]('skuList',_0x5484f4[_0x4b794c(0x193)][_0x4b794c(0x1a3)],_0x5484f4['skuList']),saveTheSkusOnPageManual();}));if(_0x1690c4['type']==='end-item'){var _0x5718d3=_0x1690c4[_0x5797aa(0x1a6)][_0x5797aa(0x1ab)],_0x19c1ee=findItemNode(_0x5718d3);console['log'](_0x5797aa(0x17f),_0x5718d3,_0x19c1ee);var _0x5239bc=checkIfPageLoadedProperly();if(!_0x5239bc)return _0x279e3e({'response':_0x5797aa(0x182)}),!![];return endItem(_0x19c1ee)[_0x5797aa(0x185)](_0x5ef125=>{var _0x261b32=_0x5797aa;console[_0x261b32(0x1ca)]('response',_0x5ef125),_0x279e3e({'response':_0x5ef125});}),!![];}if(_0x1690c4[_0x5797aa(0x1c8)]==='check-if-item-ended'){var _0x528e7c=document[_0x5797aa(0x1cf)](_0x5797aa(0x1c1));return _0x528e7c?_0x279e3e({'itemEnded':!![]}):_0x279e3e({'itemEnded':![]}),!![];}if(_0x1690c4['type']===_0x5797aa(0x18a))return console['log'](_0x5797aa(0x18a)),startRelisting()[_0x5797aa(0x185)](_0x5c962f=>{var _0x6b249b=_0x5797aa;console[_0x6b249b(0x1ca)](_0x6b249b(0x1b1),_0x5c962f),_0x279e3e({'response':_0x5c962f});}),!![];if(_0x1690c4[_0x5797aa(0x1c8)]===_0x5797aa(0x1bc)){console[_0x5797aa(0x1ca)](_0x5797aa(0x1bc));var _0x5718d3=_0x1690c4[_0x5797aa(0x1ab)],_0x19c1ee=findItemNode(_0x5718d3),_0x7871bf=_0x1690c4['quantity'];return console[_0x5797aa(0x1ca)]('increase_quantity',_0x5718d3,_0x19c1ee),setRowCellValue(_0x19c1ee,_0x5797aa(0x1b9),_0x7871bf)['then'](_0xcc6c7c=>{var _0x4b6f49=_0x5797aa;console[_0x4b6f49(0x1ca)](_0x4b6f49(0x1b1),_0xcc6c7c),_0x279e3e({'response':_0xcc6c7c});}),!![];}if(_0x1690c4[_0x5797aa(0x1c8)]==='end_low_performing_items'){var _0x2a2757=_0x1690c4[_0x5797aa(0x186)],_0x47d84b=_0x1690c4[_0x5797aa(0x1ac)],_0xed2487=_0x1690c4['filterByHours'],_0x4542eb=getLowPerformingItemNodes(_0x2a2757,_0x47d84b,_0xed2487);console[_0x5797aa(0x1ca)](_0x5797aa(0x1a4),_0x4542eb),_0x4542eb[_0x5797aa(0x1a3)]<0x2?_0x279e3e({'response':_0x5797aa(0x1be)}):(endLowPerformingItems(_0x4542eb),_0x279e3e({'response':_0x5797aa(0x1c9)}));}if(_0x1690c4[_0x5797aa(0x1c8)]===_0x5797aa(0x1a8)){requestParams=_0x1690c4;var _0x2a2757=_0x1690c4[_0x5797aa(0x186)],_0x47d84b=_0x1690c4[_0x5797aa(0x1ac)],_0xed2487=_0x1690c4['filterByHours'];console[_0x5797aa(0x1ca)]('end_low_performing_items_to_sell_similar',_0x2a2757,_0x47d84b,_0xed2487);var _0x4fd7e9=[['Item\x20number','Artikelnummer'],[_0x5797aa(0x1cb),_0x5797aa(0x1b4),_0x5797aa(0x192)],[_0x5797aa(0x191),'Bestandseinheit\x20(SKU)'],[_0x5797aa(0x1a1),_0x5797aa(0x199)],[_0x5797aa(0x18e),_0x5797aa(0x19e)]],_0x508e22=areToggleOptionsDisplayed(_0x4fd7e9);if(!_0x508e22)console['log'](_0x5797aa(0x19b)),chrome['runtime'][_0x5797aa(0x1d0)]({'type':_0x5797aa(0x17b),'message':_0x1690c4}),withTimeout(0x1d4c0,applyCustomViewSettings(_0x4fd7e9))[_0x5797aa(0x185)](()=>{})[_0x5797aa(0x1b5)](_0x1af890=>{var _0x4f59ac=_0x5797aa;console['log']('Error\x20applying\x20custom\x20view\x20settings',_0x1af890),location[_0x4f59ac(0x1b7)]();});else{console[_0x5797aa(0x1ca)](_0x5797aa(0x17c));var _0x4542eb=getLowPerformingItemNodes(_0x2a2757,_0x47d84b,_0xed2487);console[_0x5797aa(0x1ca)]('Preparing\x20sell\x20similar\x20workspace\x20lowPerformingItems',_0x4542eb),_0x4542eb[_0x5797aa(0x1a3)]<0x2?chrome[_0x5797aa(0x19f)][_0x5797aa(0x1d0)]({'type':_0x5797aa(0x1af),'endResponse':_0x5797aa(0x1be)}):(chrome[_0x5797aa(0x19f)][_0x5797aa(0x1d0)]({'type':_0x5797aa(0x1af),'endResponse':'attempting_to_ending_low_performing_items'}),endLowPerformingItems(_0x4542eb)),setTimeout(()=>{var _0x20b9de=_0x5797aa;chrome['runtime'][_0x20b9de(0x1d0)]({'type':_0x20b9de(0x17b),'message':_0x1690c4}),location[_0x20b9de(0x1b7)]();},0x1d4c0);}}if(_0x1690c4[_0x5797aa(0x1c8)]===_0x5797aa(0x1a9)){var _0x220005=_0x1690c4[_0x5797aa(0x1c7)];reviseListingsByMenuOption(_0x220005);}if(_0x1690c4['type']===_0x5797aa(0x1c4))return countEditMenuOptions()[_0x5797aa(0x185)](_0x1c0f35=>{var _0x36bf5b=_0x5797aa;console[_0x36bf5b(0x1ca)](_0x36bf5b(0x1c0),_0x1c0f35),_0x279e3e({'totalMenuOptions':_0x1c0f35});}),!![];return!![];});async function startRelisting(){var _0x90b067=a0_0x23dad8;console[_0x90b067(0x1ca)]('startRelisting\x20-\x20after\x20onPageIdle');var _0x3f6dd3=await waitForElement(_0x90b067(0x1c3),_0x2a917b=>_0x2a917b!=null);return console['log']('relistButton',_0x3f6dd3),_0x3f6dd3[_0x90b067(0x17d)](),console['log'](_0x90b067(0x1d3)),_0x90b067(0x1d3);}async function addButtonsToItems(){var _0x23687e=a0_0x23dad8;console[_0x23687e(0x1ca)](_0x23687e(0x1b2));const _0x46e8c4=getItemNodes();_0x46e8c4[_0x23687e(0x1a0)](_0x198294=>{var _0x48988e=_0x23687e;const _0x25630c=_0x198294[_0x48988e(0x1aa)][_0x48988e(0x1cf)]('td[class*=\x22title\x22]');if(!_0x25630c[_0x48988e(0x1cf)](_0x48988e(0x1b6))){var _0x445db0=createSmartRelistButton();const _0xaffc8f=createEndItemButton();_0x25630c[_0x48988e(0x1ce)](_0x445db0),_0x25630c[_0x48988e(0x1ce)](_0xaffc8f);var _0x56d5d9=_0x25630c[_0x48988e(0x181)][_0x48988e(0x196)](),_0x366648=createEbaySearchButton(_0x56d5d9);_0x25630c[_0x48988e(0x1ce)](_0x366648);var _0x20b41d=createEbaySearchButton(_0x56d5d9,{'soldItems':!![],'endedRecently':!![]},'icons/sold.png');_0x25630c[_0x48988e(0x1ce)](_0x20b41d),console[_0x48988e(0x1ca)](_0x48988e(0x19c),_0x25630c);var _0x47fff0=_0x198294[_0x48988e(0x1cf)](_0x48988e(0x1ba))[_0x48988e(0x181)][_0x48988e(0x196)]();_0x47fff0=atob(_0x47fff0);var _0x13907e=openAmazonSkuButton(_0x47fff0);_0x25630c['appendChild'](_0x13907e);}});}document[a0_0x23dad8(0x183)]('DOMContentLoaded',async()=>{var _0xe3a5e0=a0_0x23dad8;await onPageLoadAndIdle(),enableOpenTitleLinksInNewTab(_0xe3a5e0(0x1d1),_0xe3a5e0(0x1b3)),highlightLowPerformingNodes(0x0,0x270f,0x2d0);});function a0_0xf659(_0x29c2f9,_0x3bc7c4){var _0x5257ff=a0_0x5257();return a0_0xf659=function(_0xf65996,_0x2a445b){_0xf65996=_0xf65996-0x17b;var _0x2136f2=_0x5257ff[_0xf65996];return _0x2136f2;},a0_0xf659(_0x29c2f9,_0x3bc7c4);}async function getLowPerformingItemThresholds(){return new Promise((_0x3a3658,_0x2e1e18)=>{var _0x3ae783=a0_0xf659;chrome[_0x3ae783(0x18c)][_0x3ae783(0x1ae)][_0x3ae783(0x1bf)]([_0x3ae783(0x197),_0x3ae783(0x1a7)],_0x8da071=>{var _0x53367d=_0x3ae783;if(chrome['runtime'][_0x53367d(0x1c5)])_0x2e1e18(chrome[_0x53367d(0x19f)][_0x53367d(0x1c5)]);else{const _0x2f56dd=_0x8da071[_0x53367d(0x197)]||0x0,_0x100a3e=_0x8da071[_0x53367d(0x1a7)]||0x0;_0x3a3658({'minSoldQuantity':_0x2f56dd,'minViewCount':_0x100a3e});}});});}async function highlightLowPerformingNodes(_0x3f1486=0x0,_0x36c8a1=0x0,_0x5076bd=0x2d0){var _0x4f24e5=a0_0x23dad8,_0x11baa8=getItemNodes();for(var _0x3f2acc of _0x11baa8){_0x3f2acc['classList'][_0x4f24e5(0x1b0)]('low-performing');}try{const _0x47b8ac=await getLowPerformingItemNodes(_0x3f1486,_0x36c8a1,_0x5076bd);_0x47b8ac[_0x4f24e5(0x1a0)](_0x1c925c=>{var _0x4e8e07=_0x4f24e5;_0x1c925c[_0x4e8e07(0x1c6)][_0x4e8e07(0x194)](_0x4e8e07(0x1b8));}),console[_0x4f24e5(0x1ca)](_0x4f24e5(0x198));}catch(_0x108af9){console[_0x4f24e5(0x19d)](_0x4f24e5(0x188),_0x108af9);}}async function addBulkSmartRelistButton(){var _0x2290d1=a0_0x23dad8,_0x3d1283=await createBulkSmartRelistButton(),_0x2e1f7f=document['querySelector'](_0x2290d1(0x18b));_0x2e1f7f[_0x2290d1(0x1ce)](_0x3d1283);}