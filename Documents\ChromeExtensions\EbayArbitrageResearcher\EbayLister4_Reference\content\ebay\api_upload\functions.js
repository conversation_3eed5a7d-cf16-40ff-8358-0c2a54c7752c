(function(_0x52d269,_0x43d7e5){const _0x55ed6d=a0_0x20fa,_0x32e0bd=_0x52d269();while(!![]){try{const _0x79889d=parseInt(_0x55ed6d(0x191))/0x1*(-parseInt(_0x55ed6d(0x179))/0x2)+-parseInt(_0x55ed6d(0x18e))/0x3*(parseInt(_0x55ed6d(0x173))/0x4)+parseInt(_0x55ed6d(0x188))/0x5+parseInt(_0x55ed6d(0x192))/0x6*(parseInt(_0x55ed6d(0x193))/0x7)+parseInt(_0x55ed6d(0x189))/0x8*(parseInt(_0x55ed6d(0x18c))/0x9)+parseInt(_0x55ed6d(0x184))/0xa+-parseInt(_0x55ed6d(0x195))/0xb*(parseInt(_0x55ed6d(0x183))/0xc);if(_0x79889d===_0x43d7e5)break;else _0x32e0bd['push'](_0x32e0bd['shift']());}catch(_0x2c5f36){_0x32e0bd['push'](_0x32e0bd['shift']());}}}(a0_0xd719,0x6e75e));async function refineProduct(_0x574564,_0x30fd07){const _0x1f1be2=a0_0x20fa,{shippingCost:shippingCost=0x0}=_0x30fd07;return _0x574564[_0x1f1be2(0x176)]&&_0x574564[_0x1f1be2(0x176)][_0x1f1be2(0x17b)](_0x3af4ac=>{const _0x5c9d63=_0x1f1be2;_0x3af4ac[_0x5c9d63(0x175)]=(_0x3af4ac['price']||0x1)+shippingCost;}),_0x574564;}async function getEbayCategory(_0x10c61f){const _0x3af9dd=a0_0x20fa;try{const _0x328c58=await EbayApi['getCategoryRecommendations'](_0x10c61f),_0x397770=_0x328c58[_0x3af9dd(0x180)];if(!_0x397770||!_0x397770[_0x3af9dd(0x18f)])throw new Error(_0x3af9dd(0x17f));for(const _0x569077 of _0x397770){if(_0x569077[_0x3af9dd(0x17c)])return _0x569077[_0x3af9dd(0x181)];}return _0x397770[0x0]['value'];}catch(_0xc0c3ff){return console[_0x3af9dd(0x17d)](_0x3af9dd(0x190),_0xc0c3ff),_0x3af9dd(0x185);}}async function addVariations(_0x38aa39,_0x51a5a7){const _0x20db2c=a0_0x20fa;console[_0x20db2c(0x17e)](_0x20db2c(0x18a),_0x38aa39),console[_0x20db2c(0x17e)](_0x20db2c(0x186),_0x51a5a7);}async function updateSingleVariation(_0x41b1a1,_0x1038b4){const _0xe93f4a=a0_0x20fa;console[_0xe93f4a(0x17e)]('updateSingleVariation\x20=>\x20single\x20variation\x20update\x20for\x20draft:',_0x41b1a1);if(_0x1038b4[_0xe93f4a(0x176)]&&_0x1038b4[_0xe93f4a(0x176)][_0xe93f4a(0x18f)]===0x1){const _0x2724d8=_0x1038b4[_0xe93f4a(0x176)][0x0];console[_0xe93f4a(0x17e)](_0xe93f4a(0x196),_0x2724d8[_0xe93f4a(0x175)],_0xe93f4a(0x182),_0x2724d8['quantity']||0x1);}}function a0_0x20fa(_0x134143,_0x447a99){const _0xd719d2=a0_0xd719();return a0_0x20fa=function(_0x20faea,_0x32d26e){_0x20faea=_0x20faea-0x173;let _0x1342a1=_0xd719d2[_0x20faea];return _0x1342a1;},a0_0x20fa(_0x134143,_0x447a99);}async function uploadPhotoToEbay(_0xd984ba,_0x553693){const _0x8a1fd2=a0_0x20fa;return console['log']('uploadPhotoToEbay\x20=>\x20uploading:',_0x553693,_0x8a1fd2(0x18d),_0xd984ba),!![];}function a0_0xd719(){const _0x344d2f=['4NkApUY','29238vNHXVs','847gmycyk','uploadVideoToEbay\x20=>\x20injecting\x20video\x20into\x20eBay:','2065789zLNiEz','Price:','364ZORwSu','com','price','variations','location','/lstng?draftId=','110332GWBKSR','&mode=AddItem','forEach','leaf','error','log','No\x20category\x20suggestions\x20found\x20from\x20eBay','searchCategories','value','Qty:','72ClgRxm','8302550MxLzgn','12345','Variations\x20=>','https://www.ebay.','379210KcrOYX','1328KcJCHT','addVariations\x20=>\x20Bulk\x20edit\x20route\x20with\x20draftId:','downloadVideo\x20=>\x20storing\x20locally:','38259zYyYMC','with\x20csrf:','13236FLwtEa','length','Failed\x20to\x20get\x20eBay\x20category\x20recommendations:'];a0_0xd719=function(){return _0x344d2f;};return a0_0xd719();}async function downloadVideo(_0x5019c8){const _0xd1979f=a0_0x20fa;console[_0xd1979f(0x17e)](_0xd1979f(0x18b),_0x5019c8);}async function uploadVideoToEbay(_0x1e941c){const _0x5db4c1=a0_0x20fa;console[_0x5db4c1(0x17e)](_0x5db4c1(0x194),_0x1e941c);}function showSuccessMessage(_0x58fb52){console['log']('Success:',_0x58fb52);}function showErrorMessage(_0x4c8407){const _0x488885=a0_0x20fa;console[_0x488885(0x17d)]('Error:',_0x4c8407);}function navigateToEbayListingEditor(_0x19e012){const _0x309153=a0_0x20fa,_0x2277a3=_0x309153(0x174);window[_0x309153(0x177)]['href']=_0x309153(0x187)+_0x2277a3+_0x309153(0x178)+_0x19e012+_0x309153(0x17a);}