var a0_0x1f312b=a0_0x52e1;(function(_0x4a1a87,_0x104033){var _0x26a72a=a0_0x52e1,_0x42104c=_0x4a1a87();while(!![]){try{var _0x31ba28=parseInt(_0x26a72a(0x1e4))/0x1+parseInt(_0x26a72a(0x202))/0x2*(-parseInt(_0x26a72a(0x1cd))/0x3)+parseInt(_0x26a72a(0x216))/0x4*(parseInt(_0x26a72a(0x20c))/0x5)+parseInt(_0x26a72a(0x1fa))/0x6*(parseInt(_0x26a72a(0x1cb))/0x7)+-parseInt(_0x26a72a(0x201))/0x8*(-parseInt(_0x26a72a(0x1fb))/0x9)+parseInt(_0x26a72a(0x217))/0xa*(parseInt(_0x26a72a(0x1c7))/0xb)+-parseInt(_0x26a72a(0x20f))/0xc;if(_0x31ba28===_0x104033)break;else _0x42104c['push'](_0x42104c['shift']());}catch(_0xdb86e3){_0x42104c['push'](_0x42104c['shift']());}}}(a0_0x36f4,0xdfcf4),console[a0_0x1f312b(0x1e9)](a0_0x1f312b(0x1f6)));async function setDeliveryAddress(_0x3c38c9){var _0x430d60=a0_0x1f312b;document[_0x430d60(0x20e)]=_0x430d60(0x1ce),console['log'](_0x430d60(0x1f7),_0x3c38c9);var _0x149954=document['querySelector'](_0x430d60(0x1d2));if(_0x149954){var _0x149350=_0x149954[_0x430d60(0x210)],_0x12a72a=_0x3c38c9['customer'][_0x430d60(0x1f2)];_0x12a72a=_0x12a72a[_0x430d60(0x1da)](/[^a-zA-ZäöüÄÖÜß ]/g,'');if(_0x149350==_0x12a72a){console[_0x430d60(0x1e9)](_0x430d60(0x1ee));return;}}await showAddressForm(),console[_0x430d60(0x1e9)](_0x430d60(0x1c6));var _0x1cf3e6=[_0x430d60(0x1cf),_0x430d60(0x1d6),'.a-popover-wrapper\x20[data-csa-c-slot-id=\x22address-ui-widgets-continue-address-btn-bottom\x22]',_0x430d60(0x1eb)];document[_0x430d60(0x20e)]=_0x430d60(0x1f1),await new Promise(_0x373c99=>setTimeout(_0x373c99,0x3e8));var _0x3cd5d9=await waitForAnyOfTheseElements(_0x1cf3e6,0x61a8,0x64);console['log'](_0x430d60(0x1fc),_0x3cd5d9),document[_0x430d60(0x20e)]=_0x430d60(0x1ed),await setDeliveryAddressAmazon(_0x3c38c9['customer']),document[_0x430d60(0x20e)]=_0x430d60(0x1f0),console['log'](_0x430d60(0x1fd)),console[_0x430d60(0x1e9)](_0x430d60(0x1d8),_0x3cd5d9);var _0x412d3b=getSubmitButton();console['log'](_0x430d60(0x1d5),_0x412d3b),_0x412d3b&&_0x412d3b[_0x430d60(0x20b)]();}function getSubmitButton(){var _0x3a1308=a0_0x1f312b,_0x500b1f=['.a-popover-wrapper\x20#address-ui-widgets-form-submit-button',_0x3a1308(0x1d6),_0x3a1308(0x21b),_0x3a1308(0x1eb)],_0x370648=null;for(var _0x46994e=0x0;_0x500b1f['length'];_0x46994e++){console['log']('updateAddressButtonSelectors[i]',_0x500b1f[_0x46994e]);var _0xd1d20a=document[_0x3a1308(0x1d1)](_0x500b1f[_0x46994e]);if(_0xd1d20a){console[_0x3a1308(0x1e9)](_0x3a1308(0x1e3),_0xd1d20a);if(_0xd1d20a[_0x3a1308(0x218)]==_0x3a1308(0x1e2)&&_0xd1d20a[_0x3a1308(0x205)]==_0x3a1308(0x1ca))return _0x370648=_0xd1d20a,_0x370648;return _0x370648=_0xd1d20a[_0x3a1308(0x1d1)](_0x3a1308(0x1c9)),_0x370648;}}return _0x370648;}async function showAddressForm(){var _0x1d2c64=a0_0x1f312b;document[_0x1d2c64(0x20e)]='#2\x20Showing\x20Address\x20Form';var _0xba6699=document['querySelector']('a[id*=\x22add-new-address\x22]');if(!_0xba6699){document[_0x1d2c64(0x20e)]=_0x1d2c64(0x1d3);var _0x2f56be=document[_0x1d2c64(0x1d1)]('a[id*=\x22addressChangeLinkId\x22],\x20[data-args=\x22redirectReason=shipaddressselectChangeClicked\x22]');_0x2f56be['click']();try{document[_0x1d2c64(0x20e)]=_0x1d2c64(0x1f8),await waitForSelectorToAppear('a[id*=\x22add-new-address\x22]',0x4e20);}catch(_0x2cd512){document[_0x1d2c64(0x20e)]='#5\x20Error\x20Waiting\x20for\x20Address\x20Form',await showAddressForm();}}document[_0x1d2c64(0x20e)]=_0x1d2c64(0x1ff);var _0x4140ce=document[_0x1d2c64(0x1d4)]('a[id*=\x22edit-address\x22]');if(_0x4140ce[_0x1d2c64(0x215)]>0x2){_0x4140ce[0x2][_0x1d2c64(0x20b)]();return;}var _0x5032dc=document[_0x1d2c64(0x1d4)](_0x1d2c64(0x1ef));if(_0x5032dc['length']>0x2){_0x5032dc[0x2][_0x1d2c64(0x1d1)]('a')['click']();return;}var _0xba6699=document['querySelector']('a[id*=\x22add-new-address\x22]');if(_0xba6699){_0xba6699[_0x1d2c64(0x20b)]();return;}return document[_0x1d2c64(0x20e)]=_0x1d2c64(0x1df),null;}function a0_0x52e1(_0x3a813d,_0x3d7df6){var _0x36f4ac=a0_0x36f4();return a0_0x52e1=function(_0x52e14a,_0x499257){_0x52e14a=_0x52e14a-0x1c6;var _0x4cb3e6=_0x36f4ac[_0x52e14a];return _0x4cb3e6;},a0_0x52e1(_0x3a813d,_0x3d7df6);}async function setDeliveryAddressAmazon(_0x2c0cf9){var _0x5471b5=a0_0x1f312b,_0x384adb=document['querySelector']('.a-popover\x20select[name=\x22address-ui-widgets-countryCode\x22]');if(_0x384adb){const _0x317f63=_0x384adb['value'],_0x2734d1=convertCountryNameToCode(_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x203)])||'US';_0x317f63!==_0x2734d1&&(_0x384adb[_0x5471b5(0x1ec)]=_0x2734d1,_0x384adb[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1e7),{'bubbles':!![]})),await new Promise(_0x2d13ea=>setTimeout(_0x2d13ea,0x7d0)));}var _0x3d3ed0=document[_0x5471b5(0x1d1)](_0x5471b5(0x1f4));if(_0x3d3ed0){var _0x3111f3=_0x2c0cf9[_0x5471b5(0x1f2)][_0x5471b5(0x1da)](/[^a-zA-ZäöüÄÖÜß ]/g,'');document[_0x5471b5(0x20e)]=_0x5471b5(0x21a)+_0x3111f3,_0x3d3ed0['value']=_0x3111f3||'',_0x3d3ed0['dispatchEvent'](new Event(_0x5471b5(0x1cc),{'bubbles':!![]}));}if(_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x203)]==_0x5471b5(0x1e8)||_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x203)]==_0x5471b5(0x1e0)){var _0x31e5fe=_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x1dc)];_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x1dc)]=_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x1f5)],_0x2c0cf9['address'][_0x5471b5(0x1f5)]=_0x31e5fe;}var _0x3f0a80=document[_0x5471b5(0x1d1)]('.a-popover\x20#address-ui-widgets-enterAddressLine1');_0x3f0a80&&(_0x3f0a80[_0x5471b5(0x1ec)]=_0x2c0cf9[_0x5471b5(0x1dd)]['line_1']||'',_0x3f0a80[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1cc),{'bubbles':!![]})),_0x3f0a80[_0x5471b5(0x20d)](new Event('change',{'bubbles':!![]})));var _0x26e6ed=document[_0x5471b5(0x1d1)](_0x5471b5(0x1db));_0x26e6ed&&(_0x26e6ed[_0x5471b5(0x1ec)]=_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x1f5)]||'',_0x26e6ed[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1cc),{'bubbles':!![]})));var _0x56a074=document['querySelector'](_0x5471b5(0x1de));_0x56a074&&(_0x56a074[_0x5471b5(0x1ec)]=_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x1fe)]||'',_0x56a074['dispatchEvent'](new Event('input',{'bubbles':!![]})));var _0x2983bb=document[_0x5471b5(0x1d1)]('.a-popover\x20select[name=\x22address-ui-widgets-enterAddressStateOrRegion\x22]');if(_0x2983bb){var _0x583aa1=_0x2c0cf9[_0x5471b5(0x1dd)][_0x5471b5(0x219)];_0x2c0cf9['address'][_0x5471b5(0x203)]=='Canada'&&(_0x583aa1=convertStateCodeToName(_0x2c0cf9['address'][_0x5471b5(0x219)])),console[_0x5471b5(0x1e9)](_0x5471b5(0x1d7),_0x583aa1),_0x2983bb[_0x5471b5(0x1ec)]=_0x583aa1,_0x2983bb[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1e7),{'bubbles':!![]}));}var _0x15b7c7=document[_0x5471b5(0x1d1)](_0x5471b5(0x1e6));_0x15b7c7&&(_0x15b7c7['value']=_0x2c0cf9['address']['zip']||'',_0x15b7c7[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1cc),{'bubbles':!![]})));var _0x24b63c=document['querySelector'](_0x5471b5(0x1e5));_0x24b63c&&(_0x24b63c[_0x5471b5(0x1ec)]=_0x2c0cf9[_0x5471b5(0x1c8)]||'',_0x24b63c[_0x5471b5(0x20d)](new Event(_0x5471b5(0x1cc),{'bubbles':!![]})));}function waitForSelectorToAppear(_0x12fb6c,_0x59f492=0x1388){return new Promise((_0x1da587,_0x3872fa)=>{var _0x1a9462=a0_0x52e1;const _0x266956=document[_0x1a9462(0x1d1)](_0x12fb6c);if(_0x266956){_0x1da587(_0x266956);return;}const _0x10fc49=new MutationObserver((_0x5ad5c6,_0x1a4388)=>{var _0x6e7316=_0x1a9462;const _0x4a2454=document[_0x6e7316(0x1d1)](_0x12fb6c);_0x4a2454&&(_0x1a4388['disconnect'](),clearTimeout(_0x4ac9f7),_0x1da587(_0x4a2454));});_0x10fc49[_0x1a9462(0x1f3)](document[_0x1a9462(0x1e1)],{'childList':!![],'subtree':!![]});const _0x4ac9f7=setTimeout(()=>{var _0x514783=_0x1a9462;_0x10fc49['disconnect'](),_0x3872fa(new Error('Timeout:\x20Selector\x20\x22'+_0x12fb6c+_0x514783(0x206)+_0x59f492+'ms'));},_0x59f492);});}function a0_0x36f4(){var _0x51a8f2=['giftMessage','Gift\x20Message\x20Sender\x20is\x20undefined','#11\x20Setting\x20Gift\x20Message\x20Sender','Gift\x20Message\x20is\x20undefined','click','5lZHkTo','dispatchEvent','title','16734540elfQXP','innerText','giftMessageSender','Clicking\x20Save\x20Button\x20from\x20Gift\x20Message','[data-csa-c-slot-id=\x22checkout-primary-continue-giftselect\x22]','[data-pipeline-link-args=\x22redirectReason=ChooseGiftOptions\x22]','length','4322268ZMLcrz','12630xylmFm','tagName','state','#9.1\x20Setting\x20Full\x20Name:\x20','.a-popover-wrapper\x20[data-csa-c-slot-id=\x22address-ui-widgets-continue-address-btn-bottom\x22]','showAddressForm\x20function\x20done','3674jFExNz','phone','input[type=\x22submit\x22]','submit','203dlYwTA','input','1111641UrDIZi','#1\x20Setting\x20Delivery\x20Address','.a-popover-wrapper\x20#address-ui-widgets-form-submit-button','Setting\x20Gift\x20Message','querySelector','.displayAddressLI.displayAddressFullName','#3\x20Clicking\x20Address\x20Change\x20Link','querySelectorAll','submitButton','.a-popover-wrapper\x20[data-csa-c-slot-id=\x22address-ui-widgets-continue-edit-address-btn-bottom\x22]','State','updateAddressButton','Gift\x20Message\x20Sender\x20Input\x20not\x20found','replace','.a-popover\x20#address-ui-widgets-enterAddressLine2','line_1','address','.a-popover\x20#address-ui-widgets-enterAddressCity','#7\x20Waiting\x20for\x20Add\x20New\x20Address\x20Button','Germany','body','INPUT','updateAddressButton\x20found','662081ITnZqH','.a-popover\x20#address-ui-widgets-enterAddressPhoneNumber','.a-popover\x20#address-ui-widgets-enterAddressPostalCode','change','Deutschland','log','Clicking\x20Continue\x20Button\x20from\x20Gift\x20Message','.a-popover-wrapper\x20[address-ui-widgets-form-submit-button-announce]','value','#9\x20Clicking\x20Update\x20Address\x20Button','Address\x20already\x20set','span[class*=\x22address-edit-link\x22]','#9.9\x20address\x20set,\x20attempting\x20to\x20click\x20update\x20address\x20button','#8\x20Waiting\x20for\x20Update\x20Address\x20Button','name','observe','.a-popover\x20#address-ui-widgets-enterAddressFullName','line_2','content/amazon/auto_order/checkout/functions.js\x20loaded','Setting\x20delivery\x20address','#4\x20Waiting\x20for\x20Address\x20Form','[data-a-modal*=\x22changeGiftOptionsPopover\x22]','132834etNrYr','5547501gcnjkj','updateAddressButton\x20Element','address\x20set','city','#6\x20Clicking\x20Add\x20New\x20Address\x20Button','#message-area-0','8ZdMqKj','6oBOTCq','country','#gift-message-sender-input-0','type','\x22\x20did\x20not\x20appear\x20within\x20'];a0_0x36f4=function(){return _0x51a8f2;};return a0_0x36f4();}async function setGiftMessage(_0x182ad5){var _0x29c934=a0_0x1f312b,_0x37e025=document[_0x29c934(0x1d1)](_0x29c934(0x204));for(var _0x3aecc1=0x0;_0x3aecc1<0xa;_0x3aecc1++){if(_0x37e025)break;await new Promise(_0x91cba4=>setTimeout(_0x91cba4,0x3e8)),_0x37e025=document['querySelector'](_0x29c934(0x204));}var _0x3f5fff;try{if(_0x182ad5[_0x29c934(0x207)]==undefined)throw new Error(_0x29c934(0x20a));_0x3f5fff=_0x182ad5[_0x29c934(0x207)];}catch(_0x29f89f){console[_0x29c934(0x1e9)]('Gift\x20Message\x20is\x20undefined'),_0x3f5fff='-';}var _0x86f1a2;try{if(_0x182ad5[_0x29c934(0x211)]==undefined)throw new Error('Gift\x20Message\x20Sender\x20is\x20undefined');_0x86f1a2=_0x182ad5['giftMessageSender'];}catch(_0x471ebe){console[_0x29c934(0x1e9)](_0x29c934(0x208)),_0x86f1a2='-';}if(_0x37e025)_0x37e025[_0x29c934(0x1ec)]=_0x86f1a2,_0x37e025[_0x29c934(0x20d)](new Event(_0x29c934(0x1cc),{'bubbles':!![]})),document[_0x29c934(0x20e)]=_0x29c934(0x209),await new Promise(_0x31e346=>setTimeout(_0x31e346,0x3e8));else{var _0x44cc50=document[_0x29c934(0x1d1)](_0x29c934(0x214));if(_0x44cc50)return _0x44cc50[_0x29c934(0x20b)](),await new Promise(_0x1b54cd=>setTimeout(_0x1b54cd,0x3e8)),setGiftMessage(_0x182ad5);console[_0x29c934(0x1e9)](_0x29c934(0x1d9));return;}var _0x1625cc=document['querySelector'](_0x29c934(0x200));_0x1625cc&&(console[_0x29c934(0x1e9)](_0x29c934(0x1d0)),_0x1625cc[_0x29c934(0x1ec)]=_0x3f5fff,_0x1625cc[_0x29c934(0x20d)](new Event('input',{'bubbles':!![]})),document['title']='#12\x20Setting\x20Gift\x20Message',await new Promise(_0x242077=>setTimeout(_0x242077,0x3e8)));var _0x13b7ca=document[_0x29c934(0x1d1)](_0x29c934(0x213));if(_0x13b7ca)console['log'](_0x29c934(0x1ea)),_0x13b7ca[_0x29c934(0x20b)](),await new Promise(_0x31452c=>setTimeout(_0x31452c,0x3e8));else{var _0x378164=document[_0x29c934(0x1d1)](_0x29c934(0x1f9));if(_0x378164){console[_0x29c934(0x1e9)](_0x29c934(0x1ea)),_0x378164['click'](),await new Promise(_0x5da611=>setTimeout(_0x5da611,0x3e8));var _0x41fdef=document[_0x29c934(0x1d1)]('[data-testid=\x22GiftOptions_saveButton-0\x22]');_0x41fdef&&(console[_0x29c934(0x1e9)](_0x29c934(0x212)),_0x41fdef[_0x29c934(0x20b)](),await new Promise(_0x492875=>setTimeout(_0x492875,0x3e8)));}}}