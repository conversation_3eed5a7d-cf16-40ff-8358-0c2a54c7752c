.vim a.custom-button.fake-btn--primary.ux-call-to-action {
    background-color: #90EE90 !important; /* Light green background */
    border-color: #90EE90 !important; /* Light green border */
    color: #fff !important; /* White text for contrast */
    margin-top: 5px !important; /* Add space above the button */

}

.vim a.custom-button.fake-btn--primary.ux-call-to-action:hover {
    background-color: #76C476 !important; /* Slightly darker green for hover */
    border-color: #76C476 !important; /* Slightly darker green border for hover */
    color: #fff !important; /* Keeping text white during hover */
}

/* If you want to change the color of text during hover, add this */
.vim a.custom-button.fake-btn--primary.ux-call-to-action:hover .ux-call-to-action__text {
    color: #fff !important; /* Keeping text white during hover */
}


.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled {
    background-color: #ccc !important; /* Grey background to indicate disabled state */
    border-color: #ccc !important; /* Grey border to indicate disabled state */
    color: #888 !important; /* Grey text to indicate disabled state */
    cursor: not-allowed !important; /* Change cursor to indicate it's not clickable */
    pointer-events: none; 
}

/* Ensure the disabled button does not have hover or active styles */
.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled:hover,
.vim a.custom-button.fake-btn--primary.ux-call-to-action.disabled:active {
    background-color: #ccc !important;
    border-color: #ccc !important;
    color: #888 !important;
    cursor: not-allowed !important;
}



/* Style for the info-box */
.info-box {
    max-width: 300px;
    background-color: #f0f0f0;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-size: 0.9em;
}

.info-box p {
    margin: 5px 0;
}

/* Adding a bit of style to the URL for aesthetics */
.info-box a {
    color: #3498db;
    text-decoration: none;
}

.info-box a:hover {
    text-decoration: underline;
}
