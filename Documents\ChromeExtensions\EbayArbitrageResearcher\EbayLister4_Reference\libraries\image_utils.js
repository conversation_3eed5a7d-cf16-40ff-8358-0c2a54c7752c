const a0_0x3d36d8=a0_0xe24f;(function(_0x230e2f,_0x7b94e9){const _0x5cc19f=a0_0xe24f,_0x2c02cd=_0x230e2f();while(!![]){try{const _0x17a938=parseInt(_0x5cc19f(0x120))/0x1+-parseInt(_0x5cc19f(0x126))/0x2+-parseInt(_0x5cc19f(0x127))/0x3+-parseInt(_0x5cc19f(0x132))/0x4+parseInt(_0x5cc19f(0x134))/0x5+parseInt(_0x5cc19f(0x12b))/0x6+parseInt(_0x5cc19f(0x130))/0x7*(-parseInt(_0x5cc19f(0x122))/0x8);if(_0x17a938===_0x7b94e9)break;else _0x2c02cd['push'](_0x2c02cd['shift']());}catch(_0x228215){_0x2c02cd['push'](_0x2c02cd['shift']());}}}(a0_0x50d2,0xcc50b));const IMAGES_TYPES={'URL':a0_0x3d36d8(0x128),'BASE_64':'BASE_64'};function a0_0x50d2(){const _0x1665d4=['URL','querySelector','log','6117558qjMyow','BASE_64','error','split','url-to-b64','369383ZMVTEi','getTime','930576NtCGcl','runtime','3789470OdcCBL','files','add','items','image_utils.js','image/jpeg','632236kBnpWV','sendMessage','8qdeiGd','length','dispatchEvent','charCodeAt','285542xJIKQR','3433992nNttUq'];a0_0x50d2=function(){return _0x1665d4;};return a0_0x50d2();}console[a0_0x3d36d8(0x12a)](a0_0x3d36d8(0x138));async function urlToImage(_0x2c3ec9){var _0x5a5236=await sendMessageToFetchUrlToB64(_0x2c3ec9),_0x5a5236=_0x5a5236['b64Image'],_0x39c8da=await b64ToCanvas(_0x5a5236);return _0x39c8da;}function sendMessageToFetchUrlToB64(_0x4c1886){return new Promise((_0x20016a,_0x49e1a8)=>{const _0x232e14=a0_0xe24f;chrome[_0x232e14(0x133)][_0x232e14(0x121)]({'type':_0x232e14(0x12f),'url':_0x4c1886},_0x56ab65=>{const _0x1c357e=_0x232e14;_0x56ab65&&_0x56ab65['error']?_0x49e1a8(new Error(_0x56ab65[_0x1c357e(0x12d)])):_0x20016a(_0x56ab65);});});}function a0_0xe24f(_0x2571d7,_0x34e3d9){const _0x50d2fa=a0_0x50d2();return a0_0xe24f=function(_0xe24f53,_0x352a64){_0xe24f53=_0xe24f53-0x120;let _0x5859fc=_0x50d2fa[_0xe24f53];return _0x5859fc;},a0_0xe24f(_0x2571d7,_0x34e3d9);}const b64StringToFile=(_0x576746,_0x4d4d57)=>{const _0x3cdb81=a0_0x3d36d8;trimmedString=_0x576746;_0x576746['startsWith']('data')&&(trimmedString=_0x576746[_0x3cdb81(0x12e)](',')[0x1]);const _0x25e9f6=atob(trimmedString),_0x1d6a38=new ArrayBuffer(_0x25e9f6['length']),_0xa13a8e=new Uint8Array(_0x1d6a38);for(let _0x305dac=0x0;_0x305dac<_0x25e9f6[_0x3cdb81(0x123)];_0x305dac++){_0xa13a8e[_0x305dac]=_0x25e9f6[_0x3cdb81(0x125)](_0x305dac);}const _0x8f9488=_0x3cdb81(0x139),_0x477ce7=new Blob([_0x1d6a38],{'type':_0x8f9488});return new File([_0x477ce7],_0x4d4d57,{'lastModified':new Date()[_0x3cdb81(0x131)](),'type':_0x8f9488});},uploadFileImageToInputTag=(_0x37ccd0,_0x2b2b68)=>{const _0x4365d4=a0_0x3d36d8,_0x5e4600=new DataTransfer();_0x5e4600[_0x4365d4(0x137)][_0x4365d4(0x136)](_0x37ccd0),_0x2b2b68[_0x4365d4(0x135)]=_0x5e4600[_0x4365d4(0x135)];const _0x4f6ede=new Event('change',{'bubbles':!0x0});_0x2b2b68[_0x4365d4(0x124)](_0x4f6ede);};var uploadImage=async(_0x458c46,_0x475fe4,_0x14447f,_0x36efe0)=>{const _0x582d17=a0_0x3d36d8;let _0xdce23b;if(_0x36efe0==IMAGES_TYPES[_0x582d17(0x128)]){const {b64Image:_0xb4c861}=await sendMessageToFetchUrlToB64(_0x458c46);_0xdce23b=b64StringToFile(_0xb4c861,_0x14447f);}_0x36efe0==IMAGES_TYPES[_0x582d17(0x12c)]&&(_0xdce23b=b64StringToFile(_0x458c46,_0x14447f));var _0x596379=document[_0x582d17(0x129)](_0x475fe4);uploadFileImageToInputTag(_0xdce23b,_0x596379);};