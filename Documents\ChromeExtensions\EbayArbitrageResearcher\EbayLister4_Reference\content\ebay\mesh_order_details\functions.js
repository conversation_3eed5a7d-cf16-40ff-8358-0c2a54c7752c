var a0_0x38e749=a0_0x22b1;(function(_0x23c13a,_0x2f4b5f){var _0x4089d9=a0_0x22b1,_0x4020b4=_0x23c13a();while(!![]){try{var _0x4aec95=parseInt(_0x4089d9(0x1a8))/0x1*(parseInt(_0x4089d9(0x16c))/0x2)+-parseInt(_0x4089d9(0x284))/0x3*(parseInt(_0x4089d9(0x283))/0x4)+parseInt(_0x4089d9(0x1b5))/0x5+parseInt(_0x4089d9(0x271))/0x6+parseInt(_0x4089d9(0x274))/0x7+-parseInt(_0x4089d9(0x246))/0x8+-parseInt(_0x4089d9(0x247))/0x9;if(_0x4aec95===_0x2f4b5f)break;else _0x4020b4['push'](_0x4020b4['shift']());}catch(_0x54a4ca){_0x4020b4['push'](_0x4020b4['shift']());}}}(a0_0x1664,0xd383c),console[a0_0x38e749(0x203)](a0_0x38e749(0x223)));function getElementToPlaceButtton(){var _0x1b398e=a0_0x38e749,_0x56c198=document[_0x1b398e(0x1d1)](_0x1b398e(0x165));return _0x56c198;}function createSkuElementButton(){var _0xbf3a04=a0_0x38e749;console[_0xbf3a04(0x203)](_0xbf3a04(0x182));var _0x4460f7=getSku();if(_0x4460f7&&_0x4460f7[_0xbf3a04(0x241)]('==')){console[_0xbf3a04(0x203)](_0xbf3a04(0x239),_0x4460f7),console[_0xbf3a04(0x203)](_0xbf3a04(0x168));return;}var _0x51ffbb=document['querySelector'](_0xbf3a04(0x22c));!_0x51ffbb&&(_0x51ffbb=document[_0xbf3a04(0x233)]);var _0xef9c0d=document['createElement'](_0xbf3a04(0x1b6));_0xef9c0d['textContent']=_0xbf3a04(0x256),_0x51ffbb[_0xbf3a04(0x288)](_0xef9c0d,_0x51ffbb[_0xbf3a04(0x22f)][0x1]),_0xef9c0d['addEventListener'](_0xbf3a04(0x1db),async function(){var _0x2ce695=_0xbf3a04,_0x4bbcb2=document[_0x2ce695(0x230)](_0x2ce695(0x16a));_0x4bbcb2[_0x2ce695(0x268)][_0x2ce695(0x20b)]('lineItemCardInfo__sku','spaceTop');var _0x826c89=document[_0x2ce695(0x230)](_0x2ce695(0x192));_0x826c89[_0x2ce695(0x268)][_0x2ce695(0x20b)](_0x2ce695(0x21f)),_0x826c89[_0x2ce695(0x21b)]=_0x2ce695(0x207);var _0x8ce63f=document[_0x2ce695(0x230)](_0x2ce695(0x192));_0x8ce63f[_0x2ce695(0x268)]['add']('sh-secondary');var _0x3871a1=getItemNumber(),_0x400464=await getSkuFromDescription(_0x3871a1);_0x8ce63f[_0x2ce695(0x21b)]=_0x400464,_0x4bbcb2['appendChild'](_0x826c89),_0x4bbcb2[_0x2ce695(0x226)](_0x8ce63f),_0x51ffbb[_0x2ce695(0x1a4)](_0x4bbcb2,_0xef9c0d);});}async function createButtonToCopyAmazonLink(){var _0x1695eb=a0_0x38e749,_0x421b91=document[_0x1695eb(0x230)]('button');return _0x421b91[_0x1695eb(0x234)]='Copy',_0x421b91[_0x1695eb(0x268)][_0x1695eb(0x20b)](_0x1695eb(0x1c7)),_0x421b91[_0x1695eb(0x236)](_0x1695eb(0x1db),async function(){var _0x5e1916=_0x1695eb,_0x435082=await getSku();if(!_0x435082)return null;console[_0x5e1916(0x203)]('sku',_0x435082);var _0x26ae21=await convertSkuToLink(_0x435082);navigator[_0x5e1916(0x1c4)][_0x5e1916(0x1dd)](_0x26ae21),_0x421b91[_0x5e1916(0x268)][_0x5e1916(0x20b)](_0x5e1916(0x25a)),_0x421b91[_0x5e1916(0x234)]=_0x5e1916(0x224);}),_0x421b91;}async function copyOrderDetails(_0x5b6e4c=![],_0x2d346c){var _0x4f9cce=a0_0x38e749;console[_0x4f9cce(0x203)](_0x4f9cce(0x199));var _0x3827b4=await getOrderDetails();console['log'](_0x4f9cce(0x190),_0x3827b4),await chrome[_0x4f9cce(0x225)][_0x4f9cce(0x196)]['set']({'orderDetails':_0x3827b4});var _0x2c7255=JSON[_0x4f9cce(0x259)](_0x3827b4);console['log'](_0x4f9cce(0x1f0),_0x2c7255);if(!_0x5b6e4c){navigator[_0x4f9cce(0x1c4)]['writeText'](_0x2c7255);return;}console[_0x4f9cce(0x203)]('orderDetailsString',_0x2c7255);var _0x4354a4={'clipKey':_0x2d346c,'content':JSON[_0x4f9cce(0x259)](_0x3827b4)};console['log']('data',_0x4354a4);var _0x3deb4c='https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/virtualClipboard/';try{console[_0x4f9cce(0x203)](_0x4f9cce(0x1a5));var _0x8daf93=await fetch(_0x3deb4c,{'method':_0x4f9cce(0x220),'headers':{'Content-Type':_0x4f9cce(0x1eb)},'body':JSON[_0x4f9cce(0x259)](_0x4354a4)});console[_0x4f9cce(0x203)](_0x4f9cce(0x1be),_0x8daf93);if(!_0x8daf93['ok'])throw new Error('Failed\x20to\x20save\x20order\x20details');var _0x546c24=await _0x8daf93[_0x4f9cce(0x213)]();return console[_0x4f9cce(0x203)]('Order\x20details\x20saved\x20successfully:',_0x546c24),_0x546c24;}catch(_0x282f80){console['error'](_0x4f9cce(0x280),_0x282f80);throw _0x282f80;}}async function createShouldCoppyAddressField(){var _0x40d8af=a0_0x38e749,{shouldCopyAddress:_0x3bea0a}=await chrome['storage']['local']['get'](_0x40d8af(0x269));_0x3bea0a===undefined&&(_0x3bea0a=!![],await chrome['storage'][_0x40d8af(0x196)][_0x40d8af(0x18b)]({'shouldCopyAddress':_0x3bea0a}));var _0x25b665=document[_0x40d8af(0x230)](_0x40d8af(0x217));_0x25b665[_0x40d8af(0x1da)](_0x40d8af(0x227),_0x40d8af(0x243)),_0x25b665[_0x40d8af(0x268)][_0x40d8af(0x20b)]('amazon-copy-address-field'),_0x25b665[_0x40d8af(0x1da)]('id',_0x40d8af(0x1e1));var _0x597e7c=document[_0x40d8af(0x230)](_0x40d8af(0x24e));_0x597e7c['innerText']=_0x40d8af(0x1ee),_0x597e7c[_0x40d8af(0x1da)](_0x40d8af(0x1d2),_0x40d8af(0x1e1)),_0x597e7c[_0x40d8af(0x268)]['add'](_0x40d8af(0x186));var _0xd3bdad=document[_0x40d8af(0x230)](_0x40d8af(0x16a));return _0xd3bdad['id']='amazon-copy-address-div',_0xd3bdad[_0x40d8af(0x226)](_0x597e7c),_0xd3bdad[_0x40d8af(0x226)](_0x25b665),_0x25b665[_0x40d8af(0x281)]=_0x3bea0a,_0x25b665[_0x40d8af(0x236)](_0x40d8af(0x289),async function(){var _0x5b41e2=_0x40d8af;await chrome[_0x5b41e2(0x225)][_0x5b41e2(0x196)][_0x5b41e2(0x18b)]({'shouldCopyAddress':_0x25b665[_0x5b41e2(0x281)]});}),_0xd3bdad;}function createButtonToIncreaseQuantity(){var _0x225b9a=a0_0x38e749,_0x41b02a=document[_0x225b9a(0x230)](_0x225b9a(0x16a));_0x41b02a[_0x225b9a(0x268)][_0x225b9a(0x20b)]('amazon-quantity-container');var _0x295cb5=document[_0x225b9a(0x230)]('label');_0x295cb5['innerText']='Increase\x20Quantity',_0x295cb5[_0x225b9a(0x268)][_0x225b9a(0x20b)](_0x225b9a(0x1a0)),_0x41b02a['appendChild'](_0x295cb5);var _0x1a4d58=document[_0x225b9a(0x230)]('button');_0x1a4d58['innerText']=_0x225b9a(0x174),_0x1a4d58[_0x225b9a(0x268)]['add'](_0x225b9a(0x20c)),_0x41b02a['appendChild'](_0x1a4d58),_0x1a4d58[_0x225b9a(0x236)](_0x225b9a(0x1db),async function(){var _0x515f6a=_0x225b9a;_0x1a4d58[_0x515f6a(0x268)][_0x515f6a(0x20b)](_0x515f6a(0x26b));var _0x138558=document[_0x515f6a(0x1d1)](_0x515f6a(0x165)),_0x450a35=_0x138558['querySelectorAll'](_0x515f6a(0x181))[0x1][_0x515f6a(0x234)];_0x1a4d58[_0x515f6a(0x234)]='In\x20Progress';try{var _0x537480=await new Promise(_0x1c33ae=>{var _0x404a1f=_0x515f6a;chrome[_0x404a1f(0x23f)][_0x404a1f(0x1fa)]({'type':_0x404a1f(0x1c1),'itemNumber':_0x450a35},function(_0x517e9a){var _0x4f1d78=_0x404a1f;console[_0x4f1d78(0x203)](_0x517e9a),_0x1c33ae(_0x517e9a);});});_0x1a4d58[_0x515f6a(0x268)][_0x515f6a(0x1c3)](_0x515f6a(0x26b));if(_0x537480&&_0x537480[_0x515f6a(0x1be)])_0x1a4d58[_0x515f6a(0x268)][_0x515f6a(0x20b)]('amazon-update-quantity-button-success'),_0x1a4d58[_0x515f6a(0x234)]=_0x515f6a(0x1d8);else throw new Error(_0x515f6a(0x27e));}catch(_0x8828d){_0x1a4d58['classList'][_0x515f6a(0x1c3)](_0x515f6a(0x26b)),_0x1a4d58[_0x515f6a(0x268)][_0x515f6a(0x20b)](_0x515f6a(0x19e)),_0x1a4d58[_0x515f6a(0x234)]=_0x515f6a(0x16e),console[_0x515f6a(0x203)]('error',_0x8828d);}});var _0xffbb82=document[_0x225b9a(0x230)](_0x225b9a(0x24b));_0xffbb82[_0x225b9a(0x268)]['add']('amazon-quantity-select');for(var _0x344295=0x0;_0x344295<=0xa;_0x344295++){var _0x107aff=document['createElement']('option');_0x107aff['value']=_0x344295,_0x107aff[_0x225b9a(0x189)]=_0x344295,_0xffbb82['appendChild'](_0x107aff);}return chrome[_0x225b9a(0x225)][_0x225b9a(0x196)]['get'](_0x225b9a(0x23d),function(_0x38e7a9){var _0x17a827=_0x225b9a;_0x38e7a9[_0x17a827(0x23d)]?_0xffbb82[_0x17a827(0x252)]=_0x38e7a9[_0x17a827(0x23d)]:(_0xffbb82[_0x17a827(0x252)]=0x1,chrome[_0x17a827(0x225)][_0x17a827(0x196)]['set']({'quantityToUpdate':0x1}));}),_0xffbb82[_0x225b9a(0x236)](_0x225b9a(0x289),function(){var _0x4ecfc3=_0x225b9a,_0x3b6365=_0xffbb82[_0x4ecfc3(0x252)];chrome[_0x4ecfc3(0x225)][_0x4ecfc3(0x196)][_0x4ecfc3(0x18b)]({'quantityToUpdate':_0x3b6365});}),_0x41b02a[_0x225b9a(0x226)](_0xffbb82),_0x41b02a;}function createButtonToCopyETAMessageToClipboard(){var _0x2c8e92=a0_0x38e749,_0x458892=document[_0x2c8e92(0x230)](_0x2c8e92(0x1b6));return _0x458892[_0x2c8e92(0x234)]=_0x2c8e92(0x206),_0x458892[_0x2c8e92(0x268)][_0x2c8e92(0x20b)](_0x2c8e92(0x1fd)),_0x458892[_0x2c8e92(0x236)](_0x2c8e92(0x1db),async function(){var _0x5b3890=_0x2c8e92,_0x13b671=getETA(),{etaMessage:_0x288d15}=await chrome[_0x5b3890(0x225)][_0x5b3890(0x196)]['get'](_0x5b3890(0x214));!_0x288d15&&(_0x288d15=await fetch(chrome[_0x5b3890(0x23f)][_0x5b3890(0x169)](_0x5b3890(0x194))),_0x288d15=await _0x288d15[_0x5b3890(0x189)]());var _0x1036a3=document['querySelectorAll'](_0x5b3890(0x26d)),_0x42c90b=_0x1036a3[0x1]['innerText'];_0x42c90b=_0x42c90b[_0x5b3890(0x1f7)]('\x20')[0x0],_0x42c90b=_0x42c90b['charAt'](0x0)[_0x5b3890(0x17f)]()+_0x42c90b[_0x5b3890(0x25f)](0x1)[_0x5b3890(0x27d)](),_0x288d15=_0x288d15[_0x5b3890(0x287)](_0x5b3890(0x1f3),_0x42c90b),_0x288d15=_0x288d15[_0x5b3890(0x287)](_0x5b3890(0x27b),_0x13b671),navigator[_0x5b3890(0x1c4)][_0x5b3890(0x1dd)](_0x288d15),_0x458892[_0x5b3890(0x268)][_0x5b3890(0x20b)](_0x5b3890(0x188)),_0x458892[_0x5b3890(0x234)]='Copied';}),_0x458892;}function createButtonToCopyFeedbackMessageToClipboard(){var _0x181943=a0_0x38e749,_0x129115=document[_0x181943(0x230)](_0x181943(0x1b6));return _0x129115[_0x181943(0x234)]=_0x181943(0x20f),_0x129115[_0x181943(0x268)]['add'](_0x181943(0x278)),_0x129115[_0x181943(0x236)](_0x181943(0x1db),async function(){var _0x5e1820=_0x181943,{feedbackMessage:_0x1d6b5f}=await chrome[_0x5e1820(0x225)][_0x5e1820(0x196)][_0x5e1820(0x218)](_0x5e1820(0x26a));!_0x1d6b5f&&(_0x1d6b5f=await fetch(chrome[_0x5e1820(0x23f)][_0x5e1820(0x169)](_0x5e1820(0x1e3))),_0x1d6b5f=await _0x1d6b5f[_0x5e1820(0x189)]());var _0x57127d=await getOrderDetails(),_0xfac580=_0x57127d['customer'][_0x5e1820(0x212)],_0x8c66b5=_0xfac580[_0x5e1820(0x1f7)]('\x20')[0x0],_0x5c2117=_0x1d6b5f[_0x5e1820(0x287)](_0x5e1820(0x1f3),_0x8c66b5);navigator[_0x5e1820(0x1c4)][_0x5e1820(0x1dd)](_0x5c2117);}),_0x129115;}function a0_0x1664(){var _0x17d3aa=['readText','updateEta:\x20Invalid\x20date\x20provided','display','numeric','amazonOrderNumber','en-US','autoOrderButton\x20clicked\x20SKU:','success','5158825VmNGmP','button','innerHTML','Raw\x20Order\x20Earnings:','Monday','line_2','toFixed','.shipping-address\x20.address','giftMessageSender','response','dispatchEvent','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Estimated\x20Delivery\x20Date:</strong>\x20<span\x20id=\x22estimatedDeliveryDate\x22>','increaseQuantityOfItem','fontWeight','remove','clipboard','fulfillmentDate','length','amazon-copy-link-button','ebay-submit-order-details-button','Import\x20Fulfillment\x20Details','Conversion\x20Rate:','join','_blank','Nov','POST','order_item_same_browser','prepend','querySelector','for','orderDetails\x20to\x20be\x20saved','readOnly','Order\x20Earnings:','Total\x20Cost:','getDate','Updated','updateEta:\x20.amazon-eta-field\x20element\x20not\x20found','setAttribute','click','amazonEmail','writeText','rowNumber','Get\x20link\x20instead\x20of\x20auto\x20order','href','amazon-copy-address-field','createSheetRow','content/ebay/mesh_order_details/feedback_options/feedback_message_default.txt','address','phoneNumberElement:','https://script.google.com/macros/s/AKfycbwTOktup4oRvsKGNPtMSwUTNX41Z93HZt_PqFDzzgu0jPysi9Tmmrcs-LPf86z2nc8tnQ/exec','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Before\x20Tax:</strong>\x20<span\x20id=\x22totalBeforeTax\x22>','www.amazon.ca','Date\x20found:','Conversion\x20Key:','application/json','settings-modal','customer','Copy\x20Address','shippingAddressElement:','stringified\x20orderDetails','onclick','WARNING:\x20SKU\x20does\x20not\x20match!\x20Please\x20verify\x20the\x20fulfillment\x20details.','{{Customer_Name}}','7999','estimatedDeliveryDate','disabled','split','www.amazon.com','#totalBeforeTax','sendMessage','content/ebay/mesh_order_details/eta_options/eta_options.html','Thursday','amazon-copy-eta-button','Save','itemName','add-note-button','error','city:','log','.copy-to-clipboard','.phone.ship-itm','Copy\x20ETA\x20Message','Custom\x20label\x20(SKU):\x20','googleSheetRowNumber','Unsupported\x20domain','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20Email:</strong>\x20<span\x20id=\x22amazonEmail\x22>','add','amazon-update-quantity-button','name:','state:','Copy\x20Feedback\x20Message','.info-value','#amazonOrderNumber','name','json','etaMessage','Domain\x20Currency:','city','input','get','eBay\x20order\x20submitted\x20successfully:','parse','textContent','SS/','ebay-submit-order-details-button-success','dateOfSale','sh-secondary','PATCH','https://www.amazon.','setHours','ebay/mesh_order_details/functions.js','Copied','storage','appendChild','type','autoOrderButton\x20clicked\x20link:','Jan','Apr','#amazonEmail','.lineItemCardInfo__content\x20.details','textArea','content/ebay/mesh_order_details/feedback_options/feedback_options.html','children','createElement','getFullYear','Mar','body','innerText','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>fulfillment\x20Date:</strong>\x20<span\x20id=\x22fulfillmentDate\x22>','addEventListener','content','www.amazon.com.au','skuTest','amazonDomain','Amazon\x20Domain:','settings-modal-close','quantityToUpdate','amazon-feedback-link','runtime','date','endsWith','parentElement','checkbox','textarea[name=\x22note-content\x22]','Wednesday','11140352UHJXaD','16551693hyoQrg','state','#amazonSku','ebaySku','select','_TO_','Add\x20Note','label','Gift\x20Message\x20Sender:\x20','Update\x20result:','Saturday','value','amazon-eta-link','auto-order-button','line_1','Get\x20SKU','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22fulfillmentDetails\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<h2>Fulfillment\x20Details</h2>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Customer\x20Address:</strong>\x20<span\x20id=\x22customerAddress\x22>','querySelectorAll','stringify','amazon-copy-link-button-clicked','No\x20date\x20found','result','cols','Sep','slice','Exported!','line_1:','auto-order-container','indexOf','ebayOrderNumber','data','Failed\x20to\x20submit\x20eBay\x20order','target','classList','shouldCopyAddress','feedbackMessage','amazon-update-quantity-button-working','setDate','.shipping-address\x20.tooltip','ebay-submit-order-details-button-error','settings-modal-content','getDay','5517342HseWfm','amazon-eta-label','Change\x20ETA\x20Message','10409630UdvKBX','AUD','phone:','USD','amazon-copy-feedback-button','Copy\x20Auto\x20Link','Use\x20Gift\x20Option:\x20','{{Delivery_Date}}','totalTax','toLowerCase','Response\x20Error','rowValues','Error\x20saving\x20order\x20details:','checked','applyAmazonAffiliateTag','77884GKenJx','57JzryxH','getMonth','customerAddress','replace','insertBefore','change','www.amazon.co.uk','.note-content','orderEarnings','.lineItemCardInfo__itemId.spaceTop','ebay-submit-order-details-button-working','country:','No\x20need\x20to\x20create\x20SKU\x20button.\x20SKU\x20ends\x20with\x20\x22==\x22.','getURL','div','shouldUseGiftOption','79194SMVaQb','Error\x20submitting\x20eBay\x20order:','Error','CAD','phone','Export\x20to\x20EcomSniper','Currency\x20Symbol:','none','Increase\x20Quantity','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Order\x20Number:</strong>\x20<span\x20id=\x22amazonOrderNumber\x22>','match','Auto\x20Order','Add\x20note','amazonSku','className','.amazon-eta-field','EUR','get_sku_from_description','updateEta:\x20Formatted\x20date','toUpperCase','zip:','.sh-secondary','createSkuElementButton','Unsupported\x20Amazon\x20domain','Total\x20Before\x20Tax:','amazon-eta-field','amazon-copy-address-label','long','amazon-copy-eta-button-clicked','text','&tag=bestbatteri00-20','set','giftMessage','SKU\x20not\x20found.\x20Please\x20enter\x20the\x20SKU\x20manually.','#estimatedDeliveryDate','Profit:','orderDetails','Earnings\x20Currency:','span','totalBeforeTax','content/ebay/mesh_order_details/eta_options/eta_message_default.txt','https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/virtualClipboard/','local','#fulfillmentDate','importButton','copyOrderDetails','toLocaleDateString','style','customer:','Jun','amazon-update-quantity-button-error','Error\x20Exporting\x20Data','amazon-quantity-label','/dp/','UNKNOWN','children:','replaceChild','making\x20request','</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20SKU:</strong>\x20<span\x20id=\x22amazonSku\x22>','rows','26VuQVig','Error\x20updating\x20sheet:','ebayFees','<br>','addFee'];a0_0x1664=function(){return _0x17d3aa;};return a0_0x1664();}function createFieldToAddETA(){var _0xb4a7a5=a0_0x38e749,_0x49a203=document['createElement'](_0xb4a7a5(0x217));_0x49a203[_0xb4a7a5(0x1da)](_0xb4a7a5(0x227),_0xb4a7a5(0x240)),_0x49a203[_0xb4a7a5(0x268)][_0xb4a7a5(0x20b)](_0xb4a7a5(0x185));var _0x57ae0d=document[_0xb4a7a5(0x230)](_0xb4a7a5(0x24e));_0x57ae0d[_0xb4a7a5(0x234)]='ETA',_0x57ae0d[_0xb4a7a5(0x1da)]('for',_0xb4a7a5(0x185)),_0x57ae0d[_0xb4a7a5(0x268)]['add'](_0xb4a7a5(0x272));var _0x456648=document[_0xb4a7a5(0x230)](_0xb4a7a5(0x16a));_0x456648[_0xb4a7a5(0x226)](_0x57ae0d),_0x456648[_0xb4a7a5(0x226)](_0x49a203);var _0x2c8f21=document[_0xb4a7a5(0x230)]('a');return _0x2c8f21[_0xb4a7a5(0x234)]=_0xb4a7a5(0x273),_0x2c8f21[_0xb4a7a5(0x268)]['add'](_0xb4a7a5(0x253)),_0x2c8f21['setAttribute'](_0xb4a7a5(0x267),_0xb4a7a5(0x1cc)),_0x2c8f21[_0xb4a7a5(0x1da)](_0xb4a7a5(0x1e0),chrome['runtime'][_0xb4a7a5(0x169)](_0xb4a7a5(0x1fb))),_0x456648[_0xb4a7a5(0x226)](_0x2c8f21),_0x456648;}function createFieldToAddFeedbackMessage(){var _0x5af681=a0_0x38e749,_0x1a196b=document[_0x5af681(0x230)](_0x5af681(0x16a)),_0xcde3e=document[_0x5af681(0x230)]('a');return _0xcde3e[_0x5af681(0x234)]='Change\x20Feedback\x20Message',_0xcde3e[_0x5af681(0x268)][_0x5af681(0x20b)](_0x5af681(0x23e)),_0xcde3e[_0x5af681(0x1da)](_0x5af681(0x267),_0x5af681(0x1cc)),_0xcde3e[_0x5af681(0x1da)](_0x5af681(0x1e0),chrome[_0x5af681(0x23f)][_0x5af681(0x169)](_0x5af681(0x22e))),_0x1a196b[_0x5af681(0x226)](_0xcde3e),_0x1a196b;}function getETA(){var _0x2f8352=a0_0x38e749,_0x3777eb=document[_0x2f8352(0x1d1)]('.amazon-eta-field'),_0x40b51d=new Date(_0x3777eb[_0x2f8352(0x252)]);_0x40b51d[_0x2f8352(0x26c)](_0x40b51d[_0x2f8352(0x1d7)]()+0x1);var _0x6beb0c={'year':_0x2f8352(0x1b0),'month':_0x2f8352(0x187),'day':_0x2f8352(0x1b0)};return _0x40b51d=_0x40b51d[_0x2f8352(0x19a)](_0x2f8352(0x1b2),_0x6beb0c),_0x40b51d;}async function convertSkuToLink(_0x29f3a7){var _0x183693=a0_0x38e749,{domain:_0x2595dd}=await chrome[_0x183693(0x225)][_0x183693(0x196)][_0x183693(0x218)]('domain'),_0x53b989=_0x183693(0x221)+_0x2595dd+_0x183693(0x1a1)+atob(_0x29f3a7)+'?th=1&psc=1',{applyAmazonAffiliateTag:_0x153c92}=await chrome[_0x183693(0x225)][_0x183693(0x196)][_0x183693(0x218)](_0x183693(0x282));if(_0x153c92){var _0x323e0d=_0x183693(0x18a);_0x53b989=_0x53b989+_0x323e0d;}return _0x53b989;}async function submitEbayOrder(_0x5163b8){var _0xdd2168=a0_0x38e749,_0x56bbb4='https://us-central1-ecomsniper-cb046.cloudfunctions.net/app/api/v1/ebayOrders';try{var _0x3b83fe=await fetch(_0x56bbb4,{'method':'POST','headers':{'Content-Type':_0xdd2168(0x1eb)},'body':JSON[_0xdd2168(0x259)](_0x5163b8)});if(!_0x3b83fe['ok'])throw new Error(_0xdd2168(0x266));var _0x227a19=await _0x3b83fe['json']();return console['log'](_0xdd2168(0x219),_0x227a19),_0x227a19;}catch(_0x4d31ba){console[_0xdd2168(0x201)](_0xdd2168(0x16d),_0x4d31ba);throw _0x4d31ba;}}function getCustomer(){var _0x387f8f=a0_0x38e749,_0x33a402=document[_0x387f8f(0x1d1)](_0x387f8f(0x1bc));console[_0x387f8f(0x203)](_0x387f8f(0x1ef),_0x33a402);var _0x101200=_0x33a402['childNodes'];console[_0x387f8f(0x203)](_0x387f8f(0x1a3),_0x101200);var _0x2d1bc0=_0x101200[0x0]['innerText'];console[_0x387f8f(0x203)](_0x387f8f(0x20d),_0x2d1bc0);var _0x4949df,_0x3c87ea,_0x2d2ab8,_0xf8745b,_0x3ae32c,_0x31b8aa;if(_0x101200[_0x387f8f(0x1c6)]==0x5){_0x4949df=_0x101200[0x1][_0x387f8f(0x234)],console[_0x387f8f(0x203)]('line_1:',_0x4949df),_0x3c87ea=_0x101200[0x2][_0x387f8f(0x234)],console[_0x387f8f(0x203)]('line_2:',_0x3c87ea);var _0x299960=_0x101200[0x3]['querySelectorAll'](_0x387f8f(0x204));_0x2d2ab8=_0x299960[0x0][_0x387f8f(0x234)],console['log'](_0x387f8f(0x202),_0x2d2ab8),_0xf8745b=_0x299960[0x1]['innerText'],console[_0x387f8f(0x203)]('state:',_0xf8745b),_0x3ae32c=_0x299960[0x2][_0x387f8f(0x234)],console[_0x387f8f(0x203)]('zip:',_0x3ae32c),_0x31b8aa=_0x101200[0x4][_0x387f8f(0x234)],console[_0x387f8f(0x203)](_0x387f8f(0x167),_0x31b8aa);}else{if(_0x101200[_0x387f8f(0x1c6)]==0x4){_0x4949df=_0x101200[0x1]['innerText'],console[_0x387f8f(0x203)](_0x387f8f(0x261),_0x4949df);var _0x299960=_0x101200[0x2][_0x387f8f(0x258)]('.copy-to-clipboard');_0x2d2ab8=_0x299960[0x0]['innerText'],console[_0x387f8f(0x203)](_0x387f8f(0x202),_0x2d2ab8),_0xf8745b=_0x299960[0x1][_0x387f8f(0x234)],console[_0x387f8f(0x203)](_0x387f8f(0x20e),_0xf8745b),_0x3ae32c=_0x299960[0x2][_0x387f8f(0x234)],console[_0x387f8f(0x203)](_0x387f8f(0x180),_0x3ae32c),_0x31b8aa=_0x101200[0x3][_0x387f8f(0x234)],console[_0x387f8f(0x203)](_0x387f8f(0x167),_0x31b8aa);}}var _0x4f5948=document['querySelector'](_0x387f8f(0x205));console[_0x387f8f(0x203)](_0x387f8f(0x1e5),_0x4f5948);var _0x229f2a='';_0x4f5948&&(_0x229f2a=_0x4f5948[_0x387f8f(0x1d1)](_0x387f8f(0x210))[_0x387f8f(0x234)],console[_0x387f8f(0x203)](_0x387f8f(0x276),_0x229f2a));var _0x2d0c08={'name':_0x2d1bc0,'phone':_0x229f2a,'address':{'line_1':_0x4949df,'line_2':_0x3c87ea,'city':_0x2d2ab8,'state':_0xf8745b,'zip':_0x3ae32c,'country':_0x31b8aa}};return console['log'](_0x387f8f(0x19c),_0x2d0c08),_0x2d0c08;}function getRemark(){var _0x6cc771=a0_0x38e749,_0x26555e=document[_0x6cc771(0x1d1)]('.note-content');if(_0x26555e)return _0x26555e['innerText'];return null;}function getFulfillmentDetails(){var _0x358b8c=a0_0x38e749,_0x166d47=document['querySelector'](_0x358b8c(0x211))?.['innerText'],_0x5c976f=document[_0x358b8c(0x1d1)](_0x358b8c(0x1f9))?.[_0x358b8c(0x234)],_0x1174b6=document[_0x358b8c(0x1d1)]('#totalTax')?.[_0x358b8c(0x234)],_0x574bb7=document[_0x358b8c(0x1d1)](_0x358b8c(0x18e))?.['innerText'],_0x16c353=document['querySelector'](_0x358b8c(0x22b))?.[_0x358b8c(0x234)],_0x10ff74=document[_0x358b8c(0x1d1)](_0x358b8c(0x197))?.[_0x358b8c(0x234)],_0x759498=document[_0x358b8c(0x1d1)](_0x358b8c(0x249))?.[_0x358b8c(0x234)],_0x253029=document[_0x358b8c(0x1d1)]('#amazonDomain')?.[_0x358b8c(0x234)];if(!_0x166d47||!_0x5c976f||!_0x1174b6||!_0x574bb7||!_0x16c353||!_0x10ff74)return;return{'amazonOrderNumber':_0x166d47,'totalBeforeTax':_0x5c976f,'totalTax':_0x1174b6,'estimatedDeliveryDate':_0x574bb7,'amazonEmail':_0x16c353,'fulfillmentDate':_0x10ff74,'amazonSku':_0x759498,'amazonDomain':_0x253029};}function createButtonToSubmitOrderDetailsToEcomSniper(){var _0x56f979=a0_0x38e749,_0x23cf5e=document[_0x56f979(0x230)](_0x56f979(0x1b6));return _0x23cf5e[_0x56f979(0x234)]=_0x56f979(0x171),_0x23cf5e['classList'][_0x56f979(0x20b)](_0x56f979(0x1c8)),_0x23cf5e[_0x56f979(0x236)](_0x56f979(0x1db),async function(){var _0x3f5484=_0x56f979;_0x23cf5e[_0x3f5484(0x268)][_0x3f5484(0x20b)](_0x3f5484(0x166));var _0x28d277=await getOrderDetails();console[_0x3f5484(0x203)]('orderDetails',_0x28d277);try{var _0x24d778=await submitEbayOrder(_0x28d277);console[_0x3f5484(0x203)](_0x3f5484(0x1be),_0x24d778),_0x23cf5e['classList']['remove']('ebay-submit-order-details-button-working'),_0x23cf5e['classList'][_0x3f5484(0x20b)](_0x3f5484(0x21d)),_0x23cf5e[_0x3f5484(0x234)]=_0x3f5484(0x260);}catch(_0x1b25ba){_0x23cf5e['classList'][_0x3f5484(0x1c3)](_0x3f5484(0x166)),_0x23cf5e[_0x3f5484(0x268)][_0x3f5484(0x20b)](_0x3f5484(0x26e)),_0x23cf5e['innerText']=_0x3f5484(0x19f),console['log'](_0x3f5484(0x201),_0x1b25ba);}}),_0x23cf5e;}async function exportToEcomSniper(){var _0x1e33ef=a0_0x38e749,_0x26511c=await getOrderDetails();console[_0x1e33ef(0x203)](_0x1e33ef(0x190),_0x26511c);try{var _0x26f9a9=await submitEbayOrder(_0x26511c);console['log'](_0x1e33ef(0x1be),_0x26f9a9);}catch(_0x52c1a1){console[_0x1e33ef(0x203)](_0x1e33ef(0x201),_0x52c1a1);}}async function submitToGoogleSheets(){var _0x4064d3=a0_0x38e749,_0x4df708=await getOrderDetails();console[_0x4064d3(0x203)](_0x4064d3(0x190),_0x4df708);var _0x59bf40=[null,null,_0x4064d3(0x1f4),_0x4df708[_0x4064d3(0x264)],_0x4df708[_0x4064d3(0x24a)],_0x4df708[_0x4064d3(0x1ed)][_0x4064d3(0x212)],_0x4df708[_0x4064d3(0x1ed)]['address'][_0x4064d3(0x255)],_0x4df708[_0x4064d3(0x1ed)]['address'][_0x4064d3(0x1ba)],_0x4df708[_0x4064d3(0x1ed)]['address'][_0x4064d3(0x216)],_0x4df708[_0x4064d3(0x1ed)][_0x4064d3(0x1e4)][_0x4064d3(0x248)],_0x4df708[_0x4064d3(0x1ed)][_0x4064d3(0x1e4)]['zip'],_0x4df708[_0x4064d3(0x1ed)][_0x4064d3(0x170)],_0x4df708['quantitySold'],_0x4df708[_0x4064d3(0x21e)],_0x4df708[_0x4064d3(0x1ff)],_0x4df708['soldPrice'],_0x4df708[_0x4064d3(0x1aa)],_0x4df708[_0x4064d3(0x1ac)]];console[_0x4064d3(0x203)](_0x4064d3(0x27f),_0x59bf40);var _0x316391=await new Promise(_0x493ac4=>{var _0x4a2b42=_0x4064d3;chrome[_0x4a2b42(0x23f)]['sendMessage']({'type':_0x4a2b42(0x1e2),'rowValues':_0x59bf40},function(_0x4adf3a){var _0x3d642b=_0x4a2b42;console[_0x3d642b(0x203)](_0x4adf3a),_0x493ac4(_0x4adf3a);});});console['log']('response',_0x316391);if(_0x316391&&_0x316391[_0x4064d3(0x25c)]==_0x4064d3(0x1b4)){var _0x53f427=_0x316391[_0x4064d3(0x1de)],_0x26f2b0=document[_0x4064d3(0x230)](_0x4064d3(0x16a));_0x26f2b0['id']=_0x4064d3(0x208),_0x26f2b0['value']=_0x53f427,_0x26f2b0[_0x4064d3(0x1b7)]='Row\x20Number:\x20'+_0x53f427,_0x26f2b0[_0x4064d3(0x19b)]['color']='green',_0x26f2b0[_0x4064d3(0x19b)][_0x4064d3(0x1c2)]='bold';var _0x1fcf0d=getElementToPlaceButtton();_0x1fcf0d[_0x4064d3(0x226)](_0x26f2b0);}return _0x316391;}async function changeActionsLinkToOpenInNewTab(){var _0x3a9a90=document['querySelectorAll']('.actions\x20a');_0x3a9a90['forEach'](_0x4a4616=>{var _0x5a1281=a0_0x22b1;_0x4a4616[_0x5a1281(0x1da)](_0x5a1281(0x267),_0x5a1281(0x1cc));});}function createButtonToImportFulfillmentDetails(){var _0x2566cc=a0_0x38e749,_0x43c5d7=document[_0x2566cc(0x230)](_0x2566cc(0x1b6));return _0x43c5d7[_0x2566cc(0x1b7)]=_0x2566cc(0x1c9),_0x43c5d7[_0x2566cc(0x17a)]=_0x2566cc(0x198),_0x43c5d7[_0x2566cc(0x1f1)]=async function(){importFulfillmentDetails();},_0x43c5d7;}async function importFulfillmentDetails(_0x20a6f4=![],_0x80da5a){var _0x569a3e=a0_0x38e749;try{if(_0x20a6f4){var _0x306dc2=_0x569a3e(0x195),_0x407823=await fetch(_0x306dc2+_0x80da5a);_0x407823=await _0x407823['json'](),console[_0x569a3e(0x203)]('response',_0x407823),console['log'](_0x407823[_0x569a3e(0x265)][_0x569a3e(0x237)]),fullfilmentData=JSON[_0x569a3e(0x21a)](_0x407823[_0x569a3e(0x265)][_0x569a3e(0x237)]),console[_0x569a3e(0x203)]('fullfilmentData',fullfilmentData);}else{const _0x4e4eb9=await navigator['clipboard'][_0x569a3e(0x1ad)]();fullfilmentData=JSON['parse'](_0x4e4eb9);}await appendFulfillmentDetails(fullfilmentData);}catch(_0x985431){console[_0x569a3e(0x201)]('Error\x20importing\x20fulfillment\x20details:',_0x985431);throw _0x985431;}}async function appendFulfillmentDetails(_0x3dd86c){var _0x5b9d2f=a0_0x38e749;const _0x4281a0=_0x5b9d2f(0x257)+_0x3dd86c[_0x5b9d2f(0x286)]['replace'](/\n/g,_0x5b9d2f(0x1ab))+_0x5b9d2f(0x175)+_0x3dd86c[_0x5b9d2f(0x1b1)]+_0x5b9d2f(0x1e7)+_0x3dd86c[_0x5b9d2f(0x193)]+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Total\x20Tax:</strong>\x20<span\x20id=\x22totalTax\x22>'+_0x3dd86c[_0x5b9d2f(0x27c)]+_0x5b9d2f(0x1c0)+_0x3dd86c[_0x5b9d2f(0x1f5)]+_0x5b9d2f(0x20a)+_0x3dd86c[_0x5b9d2f(0x1dc)]+_0x5b9d2f(0x235)+_0x3dd86c[_0x5b9d2f(0x1c5)]+_0x5b9d2f(0x1a6)+_0x3dd86c['amazonSku']+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p><strong>Amazon\x20Domain:</strong>\x20<span\x20id=\x22amazonDomain\x22>'+_0x3dd86c[_0x5b9d2f(0x23a)]+'</span></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20',_0x3c6642=document[_0x5b9d2f(0x230)](_0x5b9d2f(0x16a));_0x3c6642['innerHTML']=_0x4281a0;var _0x1b0085=document[_0x5b9d2f(0x1d1)]('.soldPrice');_0x1b0085[_0x5b9d2f(0x242)][_0x5b9d2f(0x242)][_0x5b9d2f(0x226)](_0x3c6642);var _0x305980=await getOrderDetails(),_0x9d170f=_0x305980[_0x5b9d2f(0x1ed)]['name'],_0x36f3df=_0x3dd86c[_0x5b9d2f(0x286)][_0x5b9d2f(0x1f7)]('\x0a')[0x0],_0x2816e2=atob(_0x305980[_0x5b9d2f(0x24a)]),_0xc5871d=_0x3dd86c[_0x5b9d2f(0x179)];checkIffulfillmentDetailsAreCorrect(_0x9d170f,_0x36f3df,_0x2816e2,_0xc5871d);var _0x3b4dd7=document[_0x5b9d2f(0x1d1)](_0x5b9d2f(0x18e))['innerText'],_0x39beb4=extractDateFromString(_0x3b4dd7);_0x39beb4?(console[_0x5b9d2f(0x203)](_0x5b9d2f(0x1e9),_0x39beb4),updateEta(_0x39beb4)):console['log'](_0x5b9d2f(0x25b));var _0x2da16e=await calculateProfit(_0x305980[_0x5b9d2f(0x164)],_0x3dd86c[_0x5b9d2f(0x193)],_0x3dd86c[_0x5b9d2f(0x27c)],_0x3dd86c['amazonDomain']);console[_0x5b9d2f(0x203)](_0x5b9d2f(0x18f),_0x2da16e);}function a0_0x22b1(_0xf789a,_0x50eb5){var _0x16646a=a0_0x1664();return a0_0x22b1=function(_0x22b145,_0xa4c7){_0x22b145=_0x22b145-0x164;var _0x2e724b=_0x16646a[_0x22b145];return _0x2e724b;},a0_0x22b1(_0xf789a,_0x50eb5);}function checkIffulfillmentDetailsAreCorrect(_0x11c2a3,_0x1c4d23,_0x46e262,_0x4fd319){var _0x1c01b7=a0_0x38e749;_0x1c4d23!==_0x11c2a3&&alert('WARNING:\x20Customer\x20name\x20does\x20not\x20match!\x20Please\x20verify\x20the\x20fulfillment\x20details.'),_0x46e262!==_0x4fd319&&alert(_0x1c01b7(0x1f2));}function extractDateFromString(_0x3529a5){var _0x264cb1=a0_0x38e749;const _0x17ac16=new Date(),_0x2bc8e7=['Sunday',_0x264cb1(0x1b9),'Tuesday',_0x264cb1(0x245),_0x264cb1(0x1fc),'Friday',_0x264cb1(0x251)];function _0x3a8961(_0x4c7942,_0x4f2f19){const _0x1ce142=new Date(_0x4c7942);return _0x1ce142['setDate'](_0x1ce142['getDate']()+_0x4f2f19),_0x1ce142;}function _0x269149(_0x2e8c23,_0x3913e9){var _0x932a5b=_0x264cb1;const _0x1a3050=_0x2bc8e7['indexOf'](_0x3913e9),_0x514ac5=new Date(_0x2e8c23);return _0x514ac5[_0x932a5b(0x26c)](_0x514ac5['getDate']()+(0x7+_0x1a3050-_0x2e8c23[_0x932a5b(0x270)]())%0x7),_0x514ac5;}function _0x3ef7d8(_0x806161,_0x1d647b){var _0x925c0b=_0x264cb1;let [_0x40af9c,_0x593403]=_0x1d647b['split']('\x20'),[_0x3648dc,_0x119fdb]=_0x40af9c[_0x925c0b(0x1f7)](':');if(_0x593403==='PM'&&_0x3648dc<0xc)_0x3648dc=parseInt(_0x3648dc)+0xc;return _0x806161[_0x925c0b(0x222)](_0x3648dc,_0x119fdb||0x0,0x0,0x0),_0x806161;}function _0x4bddd6(_0x1756d3){var _0x21e610=_0x264cb1;const [_0x5dae05,_0x3ec859]=_0x1756d3['split'](',\x20'),[_0x14a38b,_0xada413]=_0x3ec859[_0x21e610(0x1f7)]('\x20'),_0x575867=[_0x21e610(0x229),'Feb',_0x21e610(0x232),_0x21e610(0x22a),'May',_0x21e610(0x19d),'Jul','Aug',_0x21e610(0x25e),'Oct',_0x21e610(0x1cd),'Dec'],_0x1f44bc=_0x575867[_0x21e610(0x263)](_0xada413);if(_0x1f44bc===-0x1)return null;const _0x1c318e=_0x17ac16[_0x21e610(0x285)]()>_0x1f44bc?_0x17ac16[_0x21e610(0x231)]()+0x1:_0x17ac16[_0x21e610(0x231)]();return new Date(_0x1c318e,_0x1f44bc,_0x14a38b);}const _0x352bc0=[{'regex':/Delivered today/,'date':new Date(_0x17ac16)},{'regex':/Arriving today/,'date':new Date(_0x17ac16)},{'regex':/Arriving tomorrow/,'date':_0x3a8961(new Date(_0x17ac16),0x1)},{'regex':/Delivered (\w+ \d+)/,'dateFunction':_0xee579=>new Date(_0xee579[0x1]+',\x20'+_0x17ac16[_0x264cb1(0x231)]())},{'regex':/Arriving (\w+)$/,'dateFunction':_0x4e02c9=>_0x269149(_0x17ac16,_0x4e02c9[0x1])},{'regex':/Now expected by (\w+ \d+)/,'dateFunction':_0x5e0839=>new Date(_0x5e0839[0x1]+',\x20'+_0x17ac16[_0x264cb1(0x231)]())},{'regex':/Arriving tomorrow by (\d+ [AP]M)/,'dateFunction':_0x2dea12=>_0x3ef7d8(_0x3a8961(new Date(_0x17ac16),0x1),_0x2dea12[0x1])},{'regex':/Arriving today by (\d+ [AP]M)/,'dateFunction':_0x437fa4=>_0x3ef7d8(new Date(_0x17ac16),_0x437fa4[0x1])},{'regex':/Arriving by (\w+ \d+)/,'dateFunction':_0x48d423=>new Date(_0x48d423[0x1]+',\x20'+_0x17ac16['getFullYear']())},{'regex':/Delivered (\w+ \d+),/,'dateFunction':_0x430b23=>new Date(_0x430b23[0x1]+',\x20'+_0x17ac16[_0x264cb1(0x231)]())},{'regex':/Expected by (\w+ \d+)/,'dateFunction':_0x1c2006=>new Date(_0x1c2006[0x1]+',\x20'+_0x17ac16[_0x264cb1(0x231)]())},{'regex':/Arriving (\w+, \d+ \w+)/,'dateFunction':_0x508ee2=>_0x4bddd6(_0x508ee2[0x1])}];for(let _0x367fb5 of _0x352bc0){const {regex:_0x464267,date:_0x1444e9,dateFunction:_0x36f5a6}=_0x367fb5,_0x1c8c86=_0x3529a5[_0x264cb1(0x176)](_0x464267);if(_0x1c8c86)return _0x36f5a6?_0x36f5a6(_0x1c8c86):_0x1444e9;}return null;}function updateEta(_0x1ebe97){var _0x2d51af=a0_0x38e749;if(!(_0x1ebe97 instanceof Date)||isNaN(_0x1ebe97)){console[_0x2d51af(0x201)](_0x2d51af(0x1ae),_0x1ebe97);return;}var _0x3ed8c4=[_0x1ebe97[_0x2d51af(0x231)](),('0'+(_0x1ebe97[_0x2d51af(0x285)]()+0x1))[_0x2d51af(0x25f)](-0x2),('0'+_0x1ebe97[_0x2d51af(0x1d7)]())['slice'](-0x2)][_0x2d51af(0x1cb)]('-');console[_0x2d51af(0x203)](_0x2d51af(0x17e),_0x3ed8c4);var _0x32550f=document['querySelector'](_0x2d51af(0x17b));if(!_0x32550f){console[_0x2d51af(0x201)](_0x2d51af(0x1d9));return;}if(_0x32550f[_0x2d51af(0x1f6)]||_0x32550f[_0x2d51af(0x1d4)]){console['error']('updateEta:\x20.amazon-eta-field\x20is\x20disabled\x20or\x20read-only');return;}_0x32550f['value']=_0x3ed8c4,console[_0x2d51af(0x203)]('updateEta:\x20ETA\x20updated\x20successfully');}const exchangeRates={'CAD_TO_USD':0x1/1.3507,'USD_TO_CAD':1.3507,'GBP_TO_USD':0x1/0.789173,'USD_TO_GBP':0.789173,'AUD_TO_USD':0x1/1.64866,'USD_TO_AUD':1.64866,'EUR_TO_USD':0x1/1.0821,'USD_TO_EUR':1.0821};function extractCurrencyAndAmount(_0x570af1){var _0x5a0632=a0_0x38e749;let _0x13a282=_0x570af1[_0x5a0632(0x176)](/(C \$|\$|£|A \$|€)/),_0x2f21f8=_0x13a282?_0x13a282[0x0]:null,_0x4714ea=parseFloat(_0x570af1[_0x5a0632(0x287)](/[^\d.-]/g,''));return{'currencySymbol':_0x2f21f8,'amount':_0x4714ea};}function getDomainCurrency(_0x3323f5){var _0x23f52b=a0_0x38e749;switch(_0x3323f5){case _0x23f52b(0x1e8):return _0x23f52b(0x16f);case _0x23f52b(0x1f8):return'USD';case _0x23f52b(0x28a):return'GBP';case _0x23f52b(0x238):return'AUD';case'www.amazon.de':return _0x23f52b(0x17c);default:return'UNKNOWN';}}function sanitizeCurrency(_0x10c8ff){var _0x51d435=a0_0x38e749;return parseFloat(_0x10c8ff[_0x51d435(0x287)](/[^\d.-]/g,''));}async function calculateProfit(_0x3b6a24,_0x456bc3,_0x379811,_0x15ae82){var _0x50dde5=a0_0x38e749;console[_0x50dde5(0x203)](_0x50dde5(0x1b8),_0x3b6a24);var {currencySymbol:_0x440001,amount:_0x8d8be6}=extractCurrencyAndAmount(_0x3b6a24);console[_0x50dde5(0x203)](_0x50dde5(0x172),_0x440001),console[_0x50dde5(0x203)](_0x50dde5(0x1d5),_0x8d8be6),console[_0x50dde5(0x203)](_0x50dde5(0x184),_0x456bc3),console[_0x50dde5(0x203)]('Total\x20Tax:',_0x379811),console[_0x50dde5(0x203)](_0x50dde5(0x23b),_0x15ae82);var _0x363536=getDomainCurrency(_0x15ae82);console['log'](_0x50dde5(0x215),_0x363536);if(_0x363536===_0x50dde5(0x1a2))return console[_0x50dde5(0x201)](_0x50dde5(0x183)),_0x50dde5(0x209);_0x456bc3=sanitizeCurrency(_0x456bc3),_0x379811=sanitizeCurrency(_0x379811);var _0x1ad6ca=_0x456bc3+_0x379811;console['log'](_0x50dde5(0x1d6),_0x1ad6ca);var _0x29a9aa=_0x1ad6ca;const _0x3339a3={'C\x20$':_0x50dde5(0x16f),'$':_0x50dde5(0x277),'£':'GBP','A\x20$':_0x50dde5(0x275),'€':_0x50dde5(0x17c)};let _0x28c295=_0x3339a3[_0x440001]||_0x50dde5(0x277);console[_0x50dde5(0x203)](_0x50dde5(0x191),_0x28c295);if(_0x28c295!==_0x363536){let _0x4de5f0=_0x363536+_0x50dde5(0x24c)+_0x28c295,_0x39630b=exchangeRates[_0x4de5f0]||0x1;_0x29a9aa=_0x1ad6ca*_0x39630b,console['log'](_0x50dde5(0x1ea),_0x4de5f0),console[_0x50dde5(0x203)](_0x50dde5(0x1ca),_0x39630b),console[_0x50dde5(0x203)]('Converted\x20Cost:',_0x29a9aa);}let _0x559bbd=_0x8d8be6-_0x29a9aa;console[_0x50dde5(0x203)](_0x50dde5(0x18f),_0x559bbd);let _0x4dcf30=''+_0x440001+_0x559bbd[_0x50dde5(0x1bb)](0x2);return console['log']('Formatted\x20Profit:',_0x4dcf30),_0x4dcf30;}async function createNote(_0xc4ba54){var _0x86b2a5=a0_0x38e749,_0x528a47=document[_0x86b2a5(0x1d1)]('.edit-note');if(_0x528a47)_0x528a47[_0x86b2a5(0x1db)]();else{var _0x3ca552=selectElementByText(_0x86b2a5(0x178));_0x3ca552['click']();}var _0x3b0d44=await waitForElementVanillaJs(_0x86b2a5(0x244));_0x3b0d44[_0x86b2a5(0x252)]=_0xc4ba54,_0x3b0d44['dispatchEvent'](new Event('input',{'bubbles':!![]})),_0x3b0d44[_0x86b2a5(0x1bf)](new Event(_0x86b2a5(0x289),{'bubbles':!![]}));var _0x1f416a=selectElementByText(_0x86b2a5(0x1fe));_0x1f416a[_0x86b2a5(0x1db)]();}function createShippedNoteButton(){var _0x29f76c=a0_0x38e749,_0x558246=document[_0x29f76c(0x230)]('button');return _0x558246[_0x29f76c(0x234)]=_0x29f76c(0x24d),_0x558246[_0x29f76c(0x268)][_0x29f76c(0x20b)](_0x29f76c(0x200)),_0x558246[_0x29f76c(0x236)]('click',async function(){await createAndAppendNote();}),_0x558246;}async function addShippedNote(){var _0x5c895d=getPreviousNoteText(),_0x269236=await createShippedNoteText(),_0x2d6152=_0x5c895d?_0x5c895d+'\x0a'+_0x269236:_0x269236;await createNote(_0x2d6152);}function getPreviousNoteText(){var _0x527aff=a0_0x38e749,_0x5d0bc1=document[_0x527aff(0x1d1)](_0x527aff(0x28b));if(_0x5d0bc1)return _0x5d0bc1[_0x527aff(0x234)];return null;}async function createShippedNoteText(){var _0x6bdaa3=a0_0x38e749,_0x226044=document['querySelector'](_0x6bdaa3(0x22b))['innerText'],_0x5a63d3=document[_0x6bdaa3(0x1d1)](_0x6bdaa3(0x18e))[_0x6bdaa3(0x234)],_0x194d5e=document[_0x6bdaa3(0x1d1)](_0x6bdaa3(0x197))[_0x6bdaa3(0x234)],{agentName:_0x36c0ef}=await chrome[_0x6bdaa3(0x225)]['local'][_0x6bdaa3(0x218)]('agentName'),_0x4486ac=_0x6bdaa3(0x21c)+_0x226044+'/'+_0x5a63d3+'/'+_0x194d5e+'-'+_0x36c0ef;return _0x4486ac;}async function createAutoOrderContainer(){var _0x2b7006=a0_0x38e749;const {shouldGetLinkInstead:shouldGetLinkInstead=![],giftMessage:giftMessage='-',giftMessageSender:giftMessageSender='-',shouldUseGiftOption:shouldUseGiftOption=![]}=await chrome[_0x2b7006(0x225)]['local'][_0x2b7006(0x218)]({'shouldGetLinkInstead':![],'giftMessage':'-','giftMessageSender':'-','shouldUseGiftOption':![]}),_0x22d3d9=document[_0x2b7006(0x230)](_0x2b7006(0x16a));_0x22d3d9[_0x2b7006(0x268)][_0x2b7006(0x20b)](_0x2b7006(0x262));const _0x3b4a22=document[_0x2b7006(0x230)](_0x2b7006(0x1b6));shouldGetLinkInstead?_0x3b4a22[_0x2b7006(0x234)]=_0x2b7006(0x279):_0x3b4a22[_0x2b7006(0x234)]=_0x2b7006(0x177);_0x3b4a22[_0x2b7006(0x268)]['add'](_0x2b7006(0x254)),_0x3b4a22['addEventListener'](_0x2b7006(0x1db),async _0x5a1010=>{var _0x19ece8=_0x2b7006;_0x5a1010['preventDefault']();const {shouldGetLinkInstead:_0x3341eb,giftMessage:giftMessage='-',giftMessageSender:giftMessageSender='-'}=await chrome[_0x19ece8(0x225)]['local'][_0x19ece8(0x218)]({'shouldGetLinkInstead':![],'giftMessage':'-','giftMessageSender':'-'});if(_0x3341eb){let _0x27d26d=await getSku();if(!_0x27d26d){alert(_0x19ece8(0x18d));return;}console[_0x19ece8(0x203)](_0x19ece8(0x1b3),_0x27d26d);let _0xded805=await convertSkuToLink(_0x27d26d);console[_0x19ece8(0x203)](_0x19ece8(0x228),_0xded805);const _0x4b4b81=await getOrderDetails();_0x4b4b81[_0x19ece8(0x18c)]=giftMessage,_0x4b4b81[_0x19ece8(0x1bd)]=giftMessageSender,_0x4b4b81[_0x19ece8(0x16b)]=shouldUseGiftOption,console[_0x19ece8(0x203)](_0x19ece8(0x1d3),_0x4b4b81),_0xded805=_0xded805+('?autoOrder=true&orderDetails='+encodeURIComponent(JSON[_0x19ece8(0x259)](_0x4b4b81))),navigator['clipboard'][_0x19ece8(0x1dd)](_0xded805),_0x3b4a22[_0x19ece8(0x268)][_0x19ece8(0x20b)](_0x19ece8(0x25a)),setTimeout(()=>{var _0x56d321=_0x19ece8;_0x3b4a22[_0x56d321(0x268)][_0x56d321(0x1c3)](_0x56d321(0x25a));},0x1388);}else console['log']('Auto\x20order\x20logic\x20triggered.'),_0x3b4a22[_0x19ece8(0x1f6)]=!![],await autoOrder();});const _0x3c615a=document[_0x2b7006(0x230)](_0x2b7006(0x192));_0x3c615a[_0x2b7006(0x1b7)]='⚙',_0x3c615a[_0x2b7006(0x268)][_0x2b7006(0x20b)]('settings-icon');const _0x333693=document[_0x2b7006(0x230)](_0x2b7006(0x16a));_0x333693[_0x2b7006(0x268)][_0x2b7006(0x20b)](_0x2b7006(0x1ec)),_0x333693[_0x2b7006(0x19b)]['display']=_0x2b7006(0x173);const _0x57561c=document['createElement'](_0x2b7006(0x16a));_0x57561c[_0x2b7006(0x268)]['add'](_0x2b7006(0x26f));const _0x592c70=document[_0x2b7006(0x230)](_0x2b7006(0x192));_0x592c70[_0x2b7006(0x234)]='x',_0x592c70['classList'][_0x2b7006(0x20b)](_0x2b7006(0x23c)),_0x592c70[_0x2b7006(0x236)]('click',()=>{var _0x5b6308=_0x2b7006;_0x333693['style'][_0x5b6308(0x1af)]='none';});const _0x430d75=document[_0x2b7006(0x230)]('label');_0x430d75['innerText']=_0x2b7006(0x1df);const _0x41d6ea=document[_0x2b7006(0x230)](_0x2b7006(0x217));_0x41d6ea['type']=_0x2b7006(0x243),_0x41d6ea[_0x2b7006(0x281)]=shouldGetLinkInstead,_0x41d6ea[_0x2b7006(0x236)](_0x2b7006(0x289),async()=>{var _0x569d3c=_0x2b7006;const _0xd2b9d3=_0x41d6ea[_0x569d3c(0x281)];await chrome[_0x569d3c(0x225)]['local'][_0x569d3c(0x18b)]({'shouldGetLinkInstead':_0xd2b9d3}),_0x3b4a22[_0x569d3c(0x234)]=_0xd2b9d3?_0x569d3c(0x279):_0x569d3c(0x177);}),_0x430d75[_0x2b7006(0x1d0)](_0x41d6ea);const _0x980500=document['createElement'](_0x2b7006(0x24e));_0x980500[_0x2b7006(0x234)]=_0x2b7006(0x27a);const _0x37717b=document[_0x2b7006(0x230)](_0x2b7006(0x217));_0x37717b['type']=_0x2b7006(0x243),_0x37717b[_0x2b7006(0x281)]=![],_0x37717b[_0x2b7006(0x281)]=shouldUseGiftOption,_0x37717b[_0x2b7006(0x236)](_0x2b7006(0x289),async()=>{var _0x8b2630=_0x2b7006;const _0x4d05a7=_0x37717b[_0x8b2630(0x281)];await chrome[_0x8b2630(0x225)]['local']['set']({'shouldUseGiftOption':_0x4d05a7});}),_0x980500[_0x2b7006(0x1d0)](_0x37717b);const _0x1d7db9=document[_0x2b7006(0x230)](_0x2b7006(0x24e));_0x1d7db9[_0x2b7006(0x234)]='Gift\x20Message:\x20';const _0x40bdb4=document['createElement'](_0x2b7006(0x22d));_0x40bdb4[_0x2b7006(0x1a7)]=0x3,_0x40bdb4[_0x2b7006(0x25d)]=0x19,_0x40bdb4[_0x2b7006(0x252)]=giftMessage,_0x40bdb4[_0x2b7006(0x236)](_0x2b7006(0x217),async _0x3340f4=>{var _0x3ca35c=_0x2b7006;await chrome['storage'][_0x3ca35c(0x196)][_0x3ca35c(0x18b)]({'giftMessage':_0x3340f4[_0x3ca35c(0x267)][_0x3ca35c(0x252)]});}),_0x1d7db9[_0x2b7006(0x226)](_0x40bdb4);const _0x23d5ef=document[_0x2b7006(0x230)](_0x2b7006(0x24e));_0x23d5ef[_0x2b7006(0x234)]=_0x2b7006(0x24f);const _0x4ee62e=document[_0x2b7006(0x230)](_0x2b7006(0x217));return _0x4ee62e[_0x2b7006(0x227)]=_0x2b7006(0x189),_0x4ee62e[_0x2b7006(0x252)]=giftMessageSender,_0x4ee62e['addEventListener'](_0x2b7006(0x217),async _0x196c2f=>{var _0x54e2ac=_0x2b7006;await chrome[_0x54e2ac(0x225)][_0x54e2ac(0x196)][_0x54e2ac(0x18b)]({'giftMessageSender':_0x196c2f[_0x54e2ac(0x267)][_0x54e2ac(0x252)]});}),_0x23d5ef[_0x2b7006(0x226)](_0x4ee62e),_0x57561c[_0x2b7006(0x226)](_0x592c70),_0x57561c[_0x2b7006(0x226)](_0x430d75),_0x57561c[_0x2b7006(0x226)](document[_0x2b7006(0x230)]('br')),_0x57561c[_0x2b7006(0x226)](_0x980500),_0x57561c[_0x2b7006(0x226)](document[_0x2b7006(0x230)]('br')),_0x57561c[_0x2b7006(0x226)](_0x1d7db9),_0x57561c[_0x2b7006(0x226)](document[_0x2b7006(0x230)]('br')),_0x57561c[_0x2b7006(0x226)](_0x23d5ef),_0x333693['appendChild'](_0x57561c),_0x3c615a[_0x2b7006(0x236)]('click',_0x10d215=>{var _0x44d290=_0x2b7006;_0x10d215['preventDefault'](),_0x333693[_0x44d290(0x19b)][_0x44d290(0x1af)]='block';}),_0x22d3d9['appendChild'](_0x3b4a22),_0x22d3d9[_0x2b7006(0x226)](_0x3c615a),_0x22d3d9[_0x2b7006(0x226)](_0x333693),_0x22d3d9;}async function autoOrder(){var _0x317917=a0_0x38e749,_0x3401b4=await getOrderDetails();console['log'](_0x317917(0x190),_0x3401b4);var _0x3fd142=await chrome['runtime'][_0x317917(0x1fa)]({'type':_0x317917(0x1cf),'orderDetails':_0x3401b4},function(_0x5a3fa4){var _0x14238f=_0x317917;console['log'](_0x14238f(0x1be),_0x5a3fa4);});console['log'](_0x317917(0x1be),_0x3fd142);}function getItemNumber(){var _0x1d0e81=a0_0x38e749,_0x25c23d=document['querySelector']('.lineItemCardInfo__itemId'),_0x5530ba=_0x25c23d[_0x1d0e81(0x258)](_0x1d0e81(0x181))[0x1][_0x1d0e81(0x234)];return _0x5530ba;}async function getSkuFromDescription(_0x1b84e1){var _0x48cbd4=await new Promise(_0xf818e3=>{var _0x31243d=a0_0x22b1;chrome['runtime'][_0x31243d(0x1fa)]({'type':_0x31243d(0x17d),'itemNumber':_0x1b84e1},function(_0x28b133){_0xf818e3(_0x28b133);});});return _0x48cbd4?.['sku'];}async function updateSheet(_0x1843d8,_0x1b5bf1,_0x3501e0){var _0x4b8c19=a0_0x38e749;const _0x4e7fb7=_0x4b8c19(0x1e6),_0x2fae67={'spreadsheetId':_0x1843d8,'range':_0x1b5bf1,'values':_0x3501e0};try{const _0x43f42d=await fetch(_0x4e7fb7,{'method':_0x4b8c19(0x1ce),'headers':{'Content-Type':_0x4b8c19(0x1eb)},'body':JSON[_0x4b8c19(0x259)](_0x2fae67)}),_0x3794ef=await _0x43f42d[_0x4b8c19(0x213)]();console[_0x4b8c19(0x203)](_0x4b8c19(0x250),_0x3794ef);}catch(_0x2bf8be){console[_0x4b8c19(0x201)](_0x4b8c19(0x1a9),_0x2bf8be);}}