body {
    font-family: Aria<PERSON>, sans-serif;
    padding: 20px;
}

textarea {
    width: 300px;
    height: 100px;
    margin-bottom: 10px;
}

button {
    margin-right: 10px;
}

#currentPosition, #currentUser {
    font-weight: bold;
}

.container{
    margin: 10px;
}


#usernames-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}




/* Reset Animation Styles */
#resetAnimationContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.5s ease;
  }
  
  #resetAnimationContainer.show {
    opacity: 1;
    pointer-events: auto;
  }
  
  #resetAnimationContent {
    text-align: center;
    animation: scaleUp 0.5s ease forwards;
  }
  
  @keyframes scaleUp {
    0% {
      transform: scale(0.5);
      opacity: 0;
    }
    100% {
      transform: scale(1.0);
      opacity: 1;
    }
  }
  
  #resetIcon {
    animation: shake 0.5s ease-in-out;
  }
  
  @keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-1deg); }
    20% { transform: translate(-3px, 0px) rotate(1deg); }
    30% { transform: translate(3px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(1deg); }
    50% { transform: translate(-1px, 2px) rotate(-1deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(3px, 1px) rotate(-1deg); }
    80% { transform: translate(-1px, -1px) rotate(1deg); }
    90% { transform: translate(1px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-1deg); }
  }
  