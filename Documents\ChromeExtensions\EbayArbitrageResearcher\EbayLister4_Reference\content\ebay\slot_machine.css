body {
          width: 100vw;
          height: 100vh;
          margin: 0;
          padding: 0;
        }
        
        #app {
          width: 100%;
          height: 100%;
          background: #1a2b45;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
        
        .doors {
          display: flex;
        }
        
        .door {
          background: #fafafa;
          width: 100px;
          height: 100px;
          overflow: hidden;
          border-radius: 5px;
          margin: 5px;
        }
        
        .boxes {
          /* transform: translateY(0); */
          transition: transform 1s ease-in-out;
        }
        
        .box {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 3rem;
        }
        
      
        
        .info {
          position: fixed;
          bottom: 0;
          width: 100%;
          text-align: center;
        }
        