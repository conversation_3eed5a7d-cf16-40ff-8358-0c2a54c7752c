var a0_0x3fd07d=a0_0x2ced;function a0_0x2ced(_0x91af4b,_0x20fe60){var _0x3bd50a=a0_0x3bd5();return a0_0x2ced=function(_0x2ced55,_0x6de3f6){_0x2ced55=_0x2ced55-0x72;var _0x53f491=_0x3bd50a[_0x2ced55];return _0x53f491;},a0_0x2ced(_0x91af4b,_0x20fe60);}(function(_0x16c9d9,_0x2dd746){var _0x21e061=a0_0x2ced,_0x294c38=_0x16c9d9();while(!![]){try{var _0x5657ae=-parseInt(_0x21e061(0xa2))/0x1*(parseInt(_0x21e061(0xce))/0x2)+-parseInt(_0x21e061(0x118))/0x3+-parseInt(_0x21e061(0xc4))/0x4+parseInt(_0x21e061(0x8d))/0x5+parseInt(_0x21e061(0x75))/0x6+-parseInt(_0x21e061(0x77))/0x7+-parseInt(_0x21e061(0x104))/0x8*(-parseInt(_0x21e061(0xbb))/0x9);if(_0x5657ae===_0x2dd746)break;else _0x294c38['push'](_0x294c38['shift']());}catch(_0x53e2a5){_0x294c38['push'](_0x294c38['shift']());}}}(a0_0x3bd5,0x38a4a),console[a0_0x3fd07d(0xe2)](a0_0x3fd07d(0x10b)));function scrollToIndex(_0x5ee8e8){var _0x2c5fd7=a0_0x3fd07d;_0x5ee8e8[_0x2c5fd7(0x8e)][_0x2c5fd7(0x9d)](_0x2c5fd7(0x78));var _0x4c2be5=Array[_0x2c5fd7(0xfe)][_0x2c5fd7(0x99)][_0x2c5fd7(0x111)](_0x5ee8e8[_0x2c5fd7(0xd5)][_0x2c5fd7(0x102)],function(_0x50644f){return _0x50644f!==_0x5ee8e8;});_0x4c2be5[_0x2c5fd7(0xd6)](function(_0x38ecb2){var _0x5a9556=_0x2c5fd7;_0x38ecb2[_0x5a9556(0x8e)][_0x5a9556(0x114)](_0x5a9556(0x78));}),_0x5ee8e8[_0x2c5fd7(0x7e)]({'block':_0x2c5fd7(0x7c)});}function checkIfViolationExists(_0x47c4){var _0x32f39a=a0_0x3fd07d,_0x20e6bc=![],_0x1670eb=_0x47c4['querySelectorAll'](_0x32f39a(0x108));return _0x1670eb[_0x32f39a(0x73)]>0x0&&(_0x20e6bc=!![]),_0x20e6bc;}async function requestBackgroundToEndItem(_0x214abc){var _0x5f48f0=a0_0x3fd07d;_0x214abc['style'][_0x5f48f0(0xf9)]='#fc95ab';var _0x855d09=fetchEbayData(_0x214abc),_0xed2ca6=await new Promise(_0xa243b5=>{var _0x3f0afd=_0x5f48f0;chrome[_0x3f0afd(0x11b)]['sendMessage']({'type':_0x3f0afd(0xda),'ebayData':_0x855d09},function(_0x5985e1){var _0x112b1e=_0x3f0afd;console[_0x112b1e(0xe2)](_0x5985e1),_0xa243b5(_0x5985e1);});});return console[_0x5f48f0(0xe2)]('Response\x20received',_0xed2ca6),_0xed2ca6;}async function identifySupplierFromSku(_0x28e782){return'amazon';}async function fetchAmazonData(_0x3117ad){var _0x48d48d=a0_0x3fd07d,{domain:_0x30df74}=await chrome[_0x48d48d(0x110)]['local']['get']('domain'),_0x561688=_0x48d48d(0xe5)+_0x30df74+_0x48d48d(0x121)+_0x3117ad+_0x48d48d(0xf2),{amazonData:_0x3e47b6}=await new Promise(_0x58f6b0=>{var _0x1e7049=_0x48d48d;chrome[_0x1e7049(0x11b)][_0x1e7049(0xab)]({'type':_0x1e7049(0xc3),'url':_0x561688},function(_0x204391){_0x58f6b0(_0x204391);});});return console['log']('amazonData',_0x3e47b6),_0x3e47b6;}async function decodeSku(_0x269990,_0x121daf){var _0x10cb8c=a0_0x3fd07d,_0x54716c;if(_0x121daf===_0x10cb8c(0x8f))try{_0x54716c=atob(_0x269990);}catch(_0x221882){return console[_0x10cb8c(0xb0)](_0x221882),null;}return _0x54716c;}async function fetchSupplierItemData(_0xf1f25,_0x690bc2){var _0xdc00e2=a0_0x3fd07d;if(_0x690bc2===_0xdc00e2(0x8f)){var _0x5d35ac=await fetchAmazonData(_0xf1f25);return _0x5d35ac;}}async function getSettings(){var _0xc1c81d=a0_0x3fd07d;const {priceVariation:_0x382896,desiredPriceEnding:_0x4b299a,markupPrice:_0x5c2c80}=await chrome[_0xc1c81d(0x110)][_0xc1c81d(0xb9)]['get']([_0xc1c81d(0xaa),_0xc1c81d(0xb6),_0xc1c81d(0x86)]);return{'priceVariation':Number(_0x382896),'desiredPriceEnding':Number(_0x4b299a),'markupPercentage':Number(_0x5c2c80)};}function calculateMarkedUpPrice(_0x3ab668,_0x51dde3){var _0x2b06c4=a0_0x3fd07d;return parseFloat((_0x3ab668*(0x1+_0x51dde3/0x64))[_0x2b06c4(0xbd)](0x2));}async function generateListItem(_0x3937bd,_0x5a00ac){var _0x30ae98=a0_0x3fd07d,_0x1b2b82=document['createElement']('li');if(_0x3937bd===_0x30ae98(0xfc))_0x1b2b82[_0x30ae98(0xe0)]=_0x3937bd+_0x30ae98(0xa5)+_0x5a00ac[_0x3937bd]+'\x22\x20target=\x22_blank\x22>'+_0x5a00ac[_0x3937bd]+_0x30ae98(0x117);else{if(_0x3937bd===_0x30ae98(0xc9)||_0x3937bd===_0x30ae98(0x7b))_0x1b2b82[_0x30ae98(0xe0)]=_0x3937bd+':\x20'+_0x5a00ac[_0x3937bd],_0x1b2b82['style'][_0x30ae98(0x7f)]=_0x5a00ac[_0x3937bd]?'green':_0x30ae98(0x9c);else{if(_0x3937bd===_0x30ae98(0xb4)){const _0x2877c1=await getSettings();var _0x124353=parseFloat(_0x5a00ac[_0x3937bd]);console[_0x30ae98(0xe2)](_0x30ae98(0x94),_0x124353);var _0x5ecaf0=calculateMarkedUpPrice(_0x124353,_0x2877c1[_0x30ae98(0x113)]);_0x1b2b82[_0x30ae98(0xe0)]=_0x3937bd+':\x20'+_0x5a00ac[_0x3937bd]+_0x30ae98(0x98)+_0x2877c1[_0x30ae98(0x113)]+_0x30ae98(0x9e)+_0x5ecaf0;}else _0x1b2b82['innerHTML']=_0x3937bd+':\x20'+_0x5a00ac[_0x3937bd];}}return _0x1b2b82;}async function displayAmazonItemInfo(_0x394339,_0xba50dc){var _0x2baacd=a0_0x3fd07d,_0xe033c=_0x394339[_0x2baacd(0xf4)][_0x2baacd(0x10f)](_0x2baacd(0x122)),_0xe3aadc=document[_0x2baacd(0xea)](_0x2baacd(0xbf));_0xe3aadc[_0x2baacd(0x8e)][_0x2baacd(0x9d)](_0x2baacd(0x115));var _0x22d819=document['createElement']('h3');_0x22d819['innerHTML']=_0x2baacd(0xdc),_0xe3aadc[_0x2baacd(0xa3)](_0x22d819);var _0x4b3729=document['createElement']('ul');for(var _0x4e2ff2 in _0xba50dc){if(!['deliveryTimeMessage',_0x2baacd(0xcf),_0x2baacd(0x72)][_0x2baacd(0xad)](_0x4e2ff2)){var _0x5d0e0c=await generateListItem(_0x4e2ff2,_0xba50dc);_0x4b3729['appendChild'](_0x5d0e0c);}}_0xe3aadc[_0x2baacd(0xa3)](_0x4b3729),_0xe033c[_0x2baacd(0xa3)](_0xe3aadc);}async function trackItem(_0x2827a9){var _0x1a364d=a0_0x3fd07d;if(!_0x2827a9)return;var _0x1e0ba1=_0x2827a9[_0x1a364d(0x10f)]('.grid-row')[_0x1a364d(0xac)](_0x1a364d(0xd2));console[_0x1a364d(0xe2)](_0x1a364d(0x10a),_0x2827a9),_0x2827a9[_0x1a364d(0xb5)][_0x1a364d(0xf9)]=_0x1a364d(0xd3),scrollToIndex(_0x2827a9);var _0x25304d=fetchEbayData(_0x2827a9);console[_0x1a364d(0xe2)]('ebayItemData',_0x25304d);var {enable_set_gspr:_0xd41489}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)](_0x1a364d(0xc7));if(_0xd41489&&_0xd41489['checkbox']){var {failedGsprItems:_0x7d5ec5}=await chrome['storage']['local'][_0x1a364d(0x90)](_0x1a364d(0x129));!_0x7d5ec5&&(_0x7d5ec5=[]);if(!_0x25304d[_0x1a364d(0xc6)]){console['log']('custom\x20label\x20not\x20found'),await trackLog(_0x25304d,_0x1a364d(0x8a)),_0x7d5ec5[_0x1a364d(0xd4)](_0x25304d[_0x1a364d(0xa4)]),await chrome[_0x1a364d(0x110)]['local'][_0x1a364d(0xa8)]({'failedGsprItems':_0x7d5ec5});return;}var _0x27746b=checkIfViolationExists(_0x2827a9);if(_0x27746b){console[_0x1a364d(0xe2)](_0x1a364d(0x8c)),await trackLog(_0x25304d,_0x1a364d(0x8c)),_0x7d5ec5[_0x1a364d(0xd4)](_0x25304d['itemNumber']),await chrome[_0x1a364d(0x110)]['local']['set']({'failedGsprItems':_0x7d5ec5});return;}var _0x3602fe=await decodeSku(_0x25304d['customLabel'],'amazon');if(!_0x3602fe){console[_0x1a364d(0xe2)]('SKU\x20not\x20decoded'),await trackLog(_0x25304d,_0x1a364d(0x8b)),_0x7d5ec5['push'](_0x25304d[_0x1a364d(0xa4)]),await chrome[_0x1a364d(0x110)]['local'][_0x1a364d(0xa8)]({'failedGsprItems':_0x7d5ec5});return;}if(!_0x3602fe[_0x1a364d(0xc0)]()[_0x1a364d(0xa0)]('B')){console['log'](_0x1a364d(0xb3)),await trackLog(_0x25304d,'SKU\x20does\x20not\x20start\x20with\x20B'),_0x7d5ec5[_0x1a364d(0xd4)](_0x25304d[_0x1a364d(0xa4)]),await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0xa8)]({'failedGsprItems':_0x7d5ec5});return;}var {domain:_0x527f42}=await chrome['storage'][_0x1a364d(0xb9)][_0x1a364d(0x90)](_0x1a364d(0xd9)),_0x30a7bb=_0x1a364d(0xe5)+_0x527f42+_0x1a364d(0x121)+_0x3602fe+'/?th=1&psc=1',_0x22e140=await new Promise(_0x2b14be=>{var _0x3931fb=_0x1a364d;chrome[_0x3931fb(0x11b)]['sendMessage']({'type':_0x3931fb(0x127),'url':_0x30a7bb},function(_0x3b9ac9){var _0x112bee=_0x3931fb;console[_0x112bee(0xe2)](_0x112bee(0x101),_0x3b9ac9),_0x2b14be(_0x3b9ac9);});});if(!_0x22e140||!_0x22e140[_0x1a364d(0x101)]||!_0x22e140['gspr'][_0x1a364d(0xec)]||!_0x22e140['gspr'][_0x1a364d(0xec)][_0x1a364d(0x87)]){await trackLog(_0x25304d,_0x1a364d(0xb1)),_0x7d5ec5[_0x1a364d(0xd4)](_0x25304d[_0x1a364d(0xa4)]),await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)]['set']({'failedGsprItems':_0x7d5ec5});return;}var {gspr:_0x4a6cf7}=_0x22e140,_0x3fb89c=await new Promise(_0x504605=>{var _0x3d2b77=_0x1a364d;chrome[_0x3d2b77(0x11b)]['sendMessage']({'type':'update_gspr_on_listing','gspr':_0x4a6cf7,'itemNumber':_0x25304d['itemNumber']},function(_0x314da1){var _0x39a659=_0x3d2b77;console[_0x39a659(0xe2)](_0x314da1),_0x504605(_0x314da1);});});console[_0x1a364d(0xe2)](_0x1a364d(0xed),_0x3fb89c);(!_0x3fb89c||!_0x3fb89c[_0x1a364d(0x106)])&&(_0x7d5ec5[_0x1a364d(0xd4)](_0x25304d[_0x1a364d(0xa4)]),await chrome['storage'][_0x1a364d(0xb9)][_0x1a364d(0xa8)]({'failedGsprItems':_0x7d5ec5}),await trackLog(_0x25304d,_0x1a364d(0xf6)));return;}var {enable_set_skus_to_items:_0x3fe3fb}=await chrome['storage'][_0x1a364d(0xb9)][_0x1a364d(0x90)]('enable_set_skus_to_items');if(_0x3fe3fb&&_0x3fe3fb[_0x1a364d(0xba)]){if(_0x25304d[_0x1a364d(0xc6)]){console[_0x1a364d(0xe2)](_0x1a364d(0xde));return;}console[_0x1a364d(0xe2)](_0x1a364d(0xff),_0x25304d['itemNumber']);var _0x22e140=await new Promise(_0x466dec=>{var _0x45277d=_0x1a364d;chrome[_0x45277d(0x11b)][_0x45277d(0xab)]({'type':_0x45277d(0xef),'itemNumber':_0x25304d[_0x45277d(0xa4)]},function(_0x272103){_0x466dec(_0x272103);});}),_0x5bc306=_0x22e140?.[_0x1a364d(0xc5)];console[_0x1a364d(0xe2)](_0x1a364d(0xc5),_0x5bc306);if(_0x5bc306){console['log']('sku\x20found',_0x5bc306);var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0x80),_0x5bc306);console[_0x1a364d(0xe2)]('succssfullyUpdated',_0x4052b9);}return;}var {enable_scan_for_restricted_words:_0x3e84c7}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)](_0x1a364d(0xc1));if(_0x3e84c7&&_0x3e84c7[_0x1a364d(0xba)]){console[_0x1a364d(0xe2)](_0x1a364d(0xf0));var _0x22e140=await new Promise(_0xa215e8=>{var _0x2732c9=_0x1a364d;chrome[_0x2732c9(0x11b)][_0x2732c9(0xab)]({'type':_0x2732c9(0xe3),'itemNumber':_0x25304d['itemNumber']},function(_0x466351){_0xa215e8(_0x466351);});});return;}var _0x27746b=checkIfViolationExists(_0x2827a9);if(_0x27746b){var {enable_delete_policy_violation_items:_0x480c0a}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)]('enable_delete_policy_violation_items');if(_0x480c0a&&_0x480c0a[_0x1a364d(0xba)]){var _0x22e140=await requestBackgroundToEndItem(_0x2827a9);console[_0x1a364d(0xe2)]('response',_0x22e140),console[_0x1a364d(0xe2)](_0x1a364d(0x9f)),await trackLog(_0x25304d,_0x1a364d(0x9b));return;}await trackLog(_0x25304d,'Policy\x20violation\x20exists');return;}var {enable_delete_items_with_no_sku:_0x5d93fa}=await chrome[_0x1a364d(0x110)]['local'][_0x1a364d(0x90)](_0x1a364d(0xf3));if(_0x5d93fa&&_0x5d93fa[_0x1a364d(0xba)]&&!_0x25304d[_0x1a364d(0xc6)]){console[_0x1a364d(0xe2)](_0x1a364d(0x92));var _0x2a1ae0=_0x5d93fa[_0x1a364d(0x10d)];if(_0x2a1ae0===_0x1a364d(0x11a)){var _0x22e140=await requestBackgroundToEndItem(_0x2827a9);console['log'](_0x1a364d(0xf1),_0x22e140),console[_0x1a364d(0xe2)]('item\x20deleted'),await trackLog(_0x25304d,'Custom\x20label\x20not\x20found,\x20item\x20deleted');}if(_0x2a1ae0==='out_of_stock'){if(_0x25304d['availableQty']>0x0){var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0xd0),0x0);!_0x4052b9?await trackLog(_0x25304d,_0x1a364d(0x11f)):await trackLog(_0x25304d,_0x1a364d(0xae));}}return;}if(!_0x25304d[_0x1a364d(0xc6)]){console[_0x1a364d(0xe2)](_0x1a364d(0x85));return;}var _0x1e96a8=await identifySupplierFromSku(_0x25304d[_0x1a364d(0xc6)]),_0x3602fe=await decodeSku(_0x25304d[_0x1a364d(0xc6)],_0x1e96a8),{enable_delete_items_with_broken_sku:_0xfaf133}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)]['get'](_0x1a364d(0xe7));if(!_0x3602fe){if(_0xfaf133&&_0xfaf133[_0x1a364d(0xba)]){console[_0x1a364d(0xe2)](_0x1a364d(0xb2));var _0x2a1ae0=_0xfaf133['action'];if(_0x2a1ae0==='delete'){var _0x22e140=await requestBackgroundToEndItem(_0x2827a9);console['log'](_0x1a364d(0xf1),_0x22e140),console[_0x1a364d(0xe2)](_0x1a364d(0x9f)),await trackLog(_0x25304d,_0x1a364d(0x100));}if(_0x2a1ae0===_0x1a364d(0xe6)){if(_0x25304d['availableQty']>0x0){var _0x4052b9=await setRowCellValue(_0x2827a9,'availableQuantity',0x0);!_0x4052b9?await trackLog(_0x25304d,_0x1a364d(0x124)):await trackLog(_0x25304d,_0x1a364d(0xdd));}}}return;}var _0x49fbf2=await fetchSupplierItemData(_0x3602fe,_0x1e96a8);await displayAmazonItemInfo(_0x2827a9,_0x49fbf2);if(_0x49fbf2['hasNetworkError']){await trackLog(_0x25304d,_0x1a364d(0x7a)),_0x2827a9[_0x1a364d(0xb5)][_0x1a364d(0xf9)]=_0x1a364d(0xa9);return;}var {enable_delete_items_by_non_chinese_sellers:_0x883440}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)]['get'](_0x1a364d(0x103));if(_0x883440&&_0x883440[_0x1a364d(0xba)])try{var {isChineseSeller:_0x52aaea}=await new Promise(_0x537091=>{var _0xa69892=_0x1a364d;chrome[_0xa69892(0x11b)][_0xa69892(0xab)]({'type':_0xa69892(0x11c)},function(_0x4d3d23){_0x537091(_0x4d3d23);});});console['log'](_0x1a364d(0xaf),_0x52aaea);if(_0x52aaea===![]){console[_0x1a364d(0xe2)](_0x1a364d(0x97));if(_0x883440['action']===_0x1a364d(0xf5)){trackLog(_0x25304d,'Non\x20chinese\x20seller,\x20item\x20not\x20deleted,\x20item\x20id\x20saved');var {nonChineseSellerItemIds:_0x111c3e}=await chrome[_0x1a364d(0x110)]['local'][_0x1a364d(0x90)]('nonChineseSellerItemIds');!_0x111c3e&&(_0x111c3e=[]),_0x111c3e[_0x1a364d(0xd4)](_0x25304d[_0x1a364d(0xa4)]),await chrome['storage'][_0x1a364d(0xb9)]['set']({'nonChineseSellerItemIds':_0x111c3e});}return;}else console[_0x1a364d(0xe2)](_0x1a364d(0xd7)),trackLog(_0x25304d,'Chinese\x20seller\x20[✅]');}catch(_0x52fe8e){console[_0x1a364d(0xb0)](_0x52fe8e),await trackLog(_0x25304d,'Error\x20checking\x20seller\x20location');return;}var {enable_delete_items_not_found_on_amazon:_0x5c2a40}=await chrome['storage']['local'][_0x1a364d(0x90)](_0x1a364d(0x91));if(_0x5c2a40&&_0x5c2a40['checkbox']&&!_0x49fbf2[_0x1a364d(0xd8)]&&!_0x49fbf2[_0x1a364d(0x74)]){console['log'](_0x1a364d(0xe1));var _0x2a1ae0=_0x5c2a40['action'];if(_0x2a1ae0===_0x1a364d(0x11a)){var _0x22e140=await requestBackgroundToEndItem(_0x2827a9);console[_0x1a364d(0xe2)](_0x1a364d(0xf1),_0x22e140),console['log'](_0x1a364d(0x9f)),await trackLog(_0x25304d,'<span\x20style=\x27color:\x20red;\x27>Item\x20not\x20found\x20on\x20supplier\x20site,\x20item\x20deleted</span>');}if(_0x2a1ae0===_0x1a364d(0xe6)){if(_0x25304d[_0x1a364d(0xbe)]>0x0){var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0xd0),0x0);!_0x4052b9&&await trackLog(_0x25304d,_0x1a364d(0xfd));}await trackLog(_0x25304d,_0x1a364d(0x96));}return;}var {enable_delete_items_sku_changed_on_amazon:_0x47096e}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)](_0x1a364d(0x11e));if(_0x47096e&&_0x47096e[_0x1a364d(0xba)]&&_0x49fbf2[_0x1a364d(0xc5)]!==_0x3602fe){console[_0x1a364d(0xe2)](_0x1a364d(0xa1));var _0x2a1ae0=_0x47096e[_0x1a364d(0x10d)];if(_0x2a1ae0===_0x1a364d(0x11a)){var _0x22e140=await requestBackgroundToEndItem(_0x2827a9);console[_0x1a364d(0xe2)]('response',_0x22e140),console[_0x1a364d(0xe2)](_0x1a364d(0x9f));var _0x5953fd='\x0a\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20red;\x22>SKU\x20changed\x20on\x20supplier\x20site,\x20item\x20deleted</span>,\x0a\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20blue;\x22>decodedSku:\x20['+_0x3602fe+']</span>,\x0a\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20green;\x22>supplierSku:\x20['+_0x49fbf2[_0x1a364d(0xc5)]+_0x1a364d(0x123);await trackLog(_0x25304d,_0x5953fd);}if(_0x2a1ae0==='out_of_stock'){if(_0x25304d['availableQty']>0x0){var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0xd0),0x0);!_0x4052b9&&await trackLog(_0x25304d,_0x1a364d(0xcd));}var _0x5953fd=_0x1a364d(0x84)+_0x3602fe+_0x1a364d(0x120)+_0x49fbf2['sku']+_0x1a364d(0x9a);await trackLog(_0x25304d,_0x5953fd);}return;}if(!_0x49fbf2){await trackLog(_0x25304d,_0x1a364d(0x107));return;}var {enable_stock_monitor:_0x7edad2}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)]('enable_stock_monitor');console['log'](_0x1a364d(0xdf),_0x7edad2);var _0x5bb0ee=await checkIsInStock(_0x49fbf2);if(_0x7edad2&&_0x7edad2[_0x1a364d(0xba)]){var _0x1ea3c7=_0x7edad2[_0x1a364d(0xf8)];console[_0x1a364d(0xe2)]('isInStock:\x20'+_0x5bb0ee);if(_0x5bb0ee){console[_0x1a364d(0xe2)](_0x1a364d(0xb8)),_0x2827a9[_0x1a364d(0xb5)][_0x1a364d(0xf9)]=_0x1a364d(0x119);if(_0x25304d[_0x1a364d(0xbe)]<0x1){var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0xd0),_0x1ea3c7);!_0x4052b9&&await trackLog(_0x25304d,_0x1a364d(0xe4));}}else{console[_0x1a364d(0xe2)](_0x1a364d(0xe8));var _0x4e0efd=await whyIsItemNotInStock(_0x49fbf2),_0x32e660=_0x49fbf2[_0x1a364d(0xcc)],_0x5953fd=_0x1a364d(0xfb)+_0x4e0efd[_0x1a364d(0x11d)](',\x20')+_0x1a364d(0xf7)+_0x32e660+'</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20';_0x2827a9[_0x1a364d(0xb5)]['backgroundColor']=_0x1a364d(0xa9),await trackLog(_0x25304d,_0x5953fd);if(_0x25304d[_0x1a364d(0xbe)]>0x0){var _0x4052b9=await setRowCellValue(_0x2827a9,_0x1a364d(0xd0),0x0);!_0x4052b9&&await trackLog(_0x25304d,'Update\x20failed,\x20Item\x20is\x20not\x20in\x20stock,\x20quantity\x20not\x20updated');}}}var {enable_price_ending_filter:_0x2f2481}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)][_0x1a364d(0x90)]('enable_price_ending_filter'),{enable_price_monitor:_0x117fb2}=await chrome[_0x1a364d(0x110)]['local'][_0x1a364d(0x90)](_0x1a364d(0x126));if(_0x117fb2&&_0x117fb2[_0x1a364d(0xba)]&&_0x2f2481&&_0x2f2481['checkbox']){var _0x331920=_0x25304d[_0x1a364d(0xb4)];console[_0x1a364d(0xe2)](_0x1a364d(0x109)+_0x331920);var _0x4683cd=_0x2f2481['price_endings'];console[_0x1a364d(0xe2)](_0x1a364d(0xca)+_0x4683cd),_0x4683cd=_0x4683cd[_0x1a364d(0xa7)](','),_0x4683cd=_0x4683cd[_0x1a364d(0x88)](_0x2143e2=>{var _0x18c010=_0x1a364d;return parseFloat(_0x2143e2[_0x18c010(0x79)]());}),console['log']('allowedPriceEndings:\x20'+_0x4683cd);var _0x54874d=_0x331920[_0x1a364d(0x116)]()[_0x1a364d(0xa7)]('.')[0x1]||_0x331920[_0x1a364d(0x116)]()[_0x1a364d(0xa7)](',')[0x1]||_0x331920[_0x1a364d(0x116)]()[_0x1a364d(0xa7)]('€')[0x1];_0x54874d=parseFloat(_0x54874d),console[_0x1a364d(0xe2)](_0x1a364d(0xe9)+_0x54874d);if(!_0x4683cd[_0x1a364d(0xad)](_0x54874d)){await trackLog(_0x25304d,_0x1a364d(0xfa));return;}}if(_0x117fb2&&_0x117fb2[_0x1a364d(0xba)]&&_0x5bb0ee){var _0x5b1d5b=_0x117fb2[_0x1a364d(0xc2)];if(_0x5b1d5b===_0x1a364d(0x76)){var _0x5ebb2f=parseFloat(_0x117fb2[_0x1a364d(0x83)]),_0x58ecc0=parseFloat(_0x117fb2['price_trigger_threshold']),_0x331920=parseFloat(_0x25304d[_0x1a364d(0xb4)]),_0x3218d8=parseFloat(_0x49fbf2['price']),_0x11c707=_0x5ebb2f/0x64*_0x3218d8,_0x2e1134=_0x3218d8+_0x11c707;_0x2e1134=parseFloat(_0x2e1134[_0x1a364d(0xbd)](0x2));var _0x1446db=_0x2e1134-_0x58ecc0,_0x1fa5d2=_0x2e1134+_0x58ecc0;if(_0x331920<_0x1446db||_0x331920>_0x1fa5d2){var {end_price:_0x1cd7f5}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)]['get']('end_price');_0x1cd7f5=parseFloat(_0x1cd7f5);if(isNaN(_0x1cd7f5))throw new Error(_0x1a364d(0xdb));var _0x220c4a=adjustPriceToEndWith(_0x2e1134,_0x1cd7f5),{domain:_0x527f42}=await chrome[_0x1a364d(0x110)][_0x1a364d(0xb9)]['get'](_0x1a364d(0xd9));(_0x527f42==='de'||_0x527f42==='fr'||_0x527f42==='it'||_0x527f42==='es')&&(_0x220c4a=_0x220c4a[_0x1a364d(0x116)]()['replace']('.',','));console[_0x1a364d(0xe2)](_0x1a364d(0xee)+_0x220c4a);var _0x4052b9=await setRowCellValue(_0x2827a9,'price',_0x220c4a);!_0x4052b9&&await trackLog(_0x25304d,_0x1a364d(0xb7));}else{}}}}function a0_0x3bd5(){var _0x5c1e90=['prime_option','Page\x20did\x20not\x20open\x20correctly','markup_percentage','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20red;\x22>SKU\x20changed\x20on\x20supplier\x20site,\x20quantity\x20set\x20to\x200</span>,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20blue;\x22>decodedSku:\x20[','custom\x20label\x20not\x20found','markupPrice','country','map','should_track_log','Custom\x20label\x20not\x20found','SKU\x20not\x20decoded','Policy\x20violation\x20exists','1431240OVXCAg','classList','amazon','get','enable_delete_items_not_found_on_amazon','custom\x20label\x20not\x20found,\x20deleting\x20item','Item\x20is\x20not\x20eligible\x20for\x20Prime','\x20generateListItem\x20azmazonPrice','Item\x20does\x20not\x20have\x20free\x20shipping','<span\x20style=\x27color:\x20red;\x27>Item\x20not\x20found\x20on\x20supplier\x20site,\x20quantity\x20set\x20to\x200</span>','Non\x20chinese\x20seller,\x20deleting\x20item','\x20<br>\x20Markup\x20Percentage:\x20','filter',']</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','Policy\x20violation\x20exists,\x20item\x20deleted','red','add','\x20<br>\x20Markup\x20Price:\x20','item\x20deleted','startsWith','SKU\x20changed\x20on\x20supplier\x20site,\x20deleting\x20item','2qHDDLH','appendChild','itemNumber',':\x20<a\x20href=\x22','tracking_log','split','set','#fc95ab','priceVariation','sendMessage','getAttribute','includes','Custom\x20label\x20not\x20found,\x20quantity\x20set\x20to\x200','isChineseSeller','error','GSPR\x20not\x20found','SKU\x20is\x20not\x20decoded,\x20deleting\x20item','SKU\x20does\x20not\x20start\x20with\x20B','price','style','desiredPriceEnding','Update\x20failed,\x20Price\x20mismatch\x20detected,\x20price\x20not\x20updated','item\x20is\x20in\x20stock','local','checkbox','207XUFppj','hasFreeShipping','toFixed','availableQty','div','toUpperCase','enable_scan_for_restricted_words','pricing_option','fetch_amazon_data','1626868yNyHdm','sku','customLabel','enable_set_gspr','toLocaleString','isItemAvailable','(pre)\x20allowedPriceEndings:\x20','Invalid\x20end_price\x20value','availabilityMessage','Update\x20failed,\x20SKU\x20changed\x20on\x20supplier\x20site,\x20quantity\x20not\x20updated','290982SLnegy','isItemDeliveryExtended','availableQuantity','Item\x20is\x20used','data-id','#FFE15175','push','parentNode','forEach','Chinese\x20seller,\x20item\x20not\x20deleted','hasNetworkError','domain','endItem','Invalid\x20end_price\x20value\x20from\x20storage','Amazon\x20Item','SKU\x20not\x20decoded,\x20quantity\x20set\x20to\x200','custom\x20label\x20found,\x20sku\x20already\x20set','enable_stock_monitor','innerHTML','Item\x20not\x20found\x20on\x20supplier\x20site,\x20deleting\x20item','log','check_if_item_is_restricted','Update\x20failed,\x20Item\x20is\x20in\x20stock,\x20quantity\x20not\x20updated','https://www.amazon.','out_of_stock','enable_delete_items_with_broken_sku','item\x20is\x20not\x20in\x20stock','ebayPriceEnding:\x20','createElement','reduce','manufacturerInfo','gsprSetResponse','adjusting\x20the\x20price\x20to:\x20','get_sku_from_description','Scanning\x20for\x20restricted\x20words','response','/?th=1&psc=1','enable_delete_items_with_no_sku','firstElementChild','save_item_id','GSPR\x20set\x20failed','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22status-availability\x22><em>Availability\x20Message:</em>\x20','restock_quantity','backgroundColor','eBay\x20price\x20ending\x20is\x20not\x20allowed.\x20Please\x20change\x20the\x20price\x20ending\x20to\x20one\x20of\x20the\x20allowed\x20price\x20endings','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22tracking-message\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22status-error\x22><strong>Item\x20is\x20not\x20in\x20stock</strong></p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22status-reasons\x22><em>Reasons:</em>\x20','amazonItemUrl','Update\x20failed,\x20Item\x20not\x20found\x20on\x20supplier\x20site,\x20quantity\x20not\x20updated','prototype','ebay\x20Item\x20Number','SKU\x20not\x20decoded,\x20item\x20deleted','gspr','children','enable_delete_items_by_non_chinese_sellers','191752IolKVh','itemCondition','gsprSet','Supplier\x20item\x20info\x20not\x20found','.inline-notice__header','ebayPrice:\x20','itemNode','ebay_tracking_functions.js\x20loaded','Item\x20is\x20not\x20available','action','used','querySelector','storage','call','ceil','markupPercentage','remove','amazon-item-box','toString','</a>','87963IOOdgF','#d1f2b8','delete','runtime','check_amazon_tab_if_chinese_seller','join','enable_delete_items_sku_changed_on_amazon','Update\x20failed,\x20Custom\x20label\x20not\x20found,\x20quantity\x20not\x20updated',']</span>,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20green;\x22>supplierSku:\x20[','/dp/','td[class*=\x22title\x22]',']</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20','Update\x20failed,\x20SKU\x20not\x20decoded,\x20quantity\x20not\x20updated','prime_only','enable_price_monitor','get_gspr','toLowerCase','failedGsprItems','isItemCorrectUrl','length','isPageCorrectlyOpened','1815390dkTXcH','markup_pricing','1267490LfRzYT','highlighted','trim','Network\x20error,\x20item\x20not\x20found\x20on\x20supplier\x20site','isEligibleForPrime','center','abs','scrollIntoView','color','listingSKU'];a0_0x3bd5=function(){return _0x5c1e90;};return a0_0x3bd5();}function adjustPriceToEndWith(_0x51f713,_0x3ceb20){var _0x55d479=a0_0x3fd07d;_0x3ceb20=parseFloat(_0x3ceb20);if(_0x3ceb20>=0x1&&_0x3ceb20<0x64)_0x3ceb20=_0x3ceb20/0x64;else{if(_0x3ceb20>=0x64||_0x3ceb20<0x0)throw new Error(_0x55d479(0xcb));}var _0x3636ad=Math['floor'](_0x51f713),_0x36c46a=Math[_0x55d479(0x112)](_0x51f713),_0x41cdc2=[];for(var _0x465e72=_0x3636ad-0x1;_0x465e72<=_0x36c46a+0x1;_0x465e72++){var _0x33a627=_0x465e72+_0x3ceb20;_0x33a627>=0x0&&_0x41cdc2[_0x55d479(0xd4)](_0x33a627);}var _0x28cf90=_0x41cdc2[_0x55d479(0xeb)](function(_0x10afaf,_0x25a6f1){var _0x4c2417=_0x55d479;return Math['abs'](_0x25a6f1-_0x51f713)<Math[_0x4c2417(0x7d)](_0x10afaf-_0x51f713)?_0x25a6f1:_0x10afaf;});return parseFloat(_0x28cf90[_0x55d479(0xbd)](0x2));}async function checkIsInStock(_0x2f7701){var _0x4709e5=a0_0x3fd07d;if(!_0x2f7701[_0x4709e5(0xbc)])return![];if(!_0x2f7701[_0x4709e5(0xc9)])return![];if(!_0x2f7701[_0x4709e5(0x74)])return![];if(_0x2f7701[_0x4709e5(0xcf)])return![];if(_0x2f7701['itemCondition']){if(_0x2f7701[_0x4709e5(0x105)][_0x4709e5(0x128)]()==_0x4709e5(0x10e))return![];}var {enable_stock_monitor:_0x3fdc7b}=await chrome[_0x4709e5(0x110)][_0x4709e5(0xb9)][_0x4709e5(0x90)](_0x4709e5(0xdf)),_0x413eeb=_0x3fdc7b[_0x4709e5(0x81)];if(_0x413eeb==='prime_only'){if(!_0x2f7701[_0x4709e5(0x7b)])return![];}return!![];}async function whyIsItemNotInStock(_0xda5312){var _0x518390=a0_0x3fd07d,_0x107661=[];!_0xda5312[_0x518390(0xbc)]&&_0x107661[_0x518390(0xd4)](_0x518390(0x95));!_0xda5312[_0x518390(0xc9)]&&_0x107661['push'](_0x518390(0x10c));!_0xda5312[_0x518390(0x74)]&&_0x107661[_0x518390(0xd4)](_0x518390(0x82));_0xda5312[_0x518390(0xcf)]&&_0x107661['push']('Item\x20delivery\x20is\x20extended');_0xda5312[_0x518390(0x105)]&&(_0xda5312[_0x518390(0x105)][_0x518390(0x128)]()==_0x518390(0x10e)&&_0x107661['push'](_0x518390(0xd1)));var {enable_stock_monitor:_0x167457}=await chrome[_0x518390(0x110)][_0x518390(0xb9)][_0x518390(0x90)](_0x518390(0xdf)),_0x182175=_0x167457[_0x518390(0x81)];return _0x182175===_0x518390(0x125)&&(!_0xda5312[_0x518390(0x7b)]&&_0x107661[_0x518390(0xd4)](_0x518390(0x93))),_0x107661;}async function trackLog(_0x35b592,_0x2883f8){var _0x591d7d=a0_0x3fd07d,{should_track_log:_0x28fa69}=await chrome[_0x591d7d(0x110)][_0x591d7d(0xb9)][_0x591d7d(0x90)](_0x591d7d(0x89));if(!_0x28fa69)return;var {tracking_log:_0x2a8699}=await chrome['storage'][_0x591d7d(0xb9)][_0x591d7d(0x90)](_0x591d7d(0xa6));!_0x2a8699&&(_0x2a8699=[]);var _0x28cc5e={'entry_time':new Date()[_0x591d7d(0xc8)](),'ebayItemData':_0x35b592,'statusMessage':_0x2883f8};_0x2a8699[_0x591d7d(0xd4)](_0x28cc5e),await chrome[_0x591d7d(0x110)]['local']['set']({'tracking_log':_0x2a8699});}