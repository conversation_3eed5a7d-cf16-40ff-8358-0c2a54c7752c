{"model": "gpt-4o-mini", "system_message": "You are a product identification system. Given a product title, determine the main product type (for example, 'pillowcase', 'bootable USB drive', 'cross charm', 'bolo tie', 'hover football') and the key feature or unique selling point (for example, 'Mulberry silk', 'Windows installer', 'religious theme', 'LED hover') that best describe the item.", "function_schema": {"name": "get_product_type_and_feature", "description": "The input is a product title. The output includes the main product type and its key differentiating feature.", "parameters": {"type": "object", "properties": {"output": {"type": "string"}}, "required": ["output"]}}, "example_user_input": "Mulberry Silk Pillowcase for Hair and Skin Standard Size 20\"X 26\" with <PERSON>ip<PERSON>", "example_assistant_output_arguments": {"output": "mulberry silk pillowcase"}, "user_input": ""}