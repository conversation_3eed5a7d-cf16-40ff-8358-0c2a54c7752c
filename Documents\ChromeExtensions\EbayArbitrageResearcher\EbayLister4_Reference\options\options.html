<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Extension Options</title>
  <link rel="stylesheet" href="options.css" />
</head>
<body>
  <h1>Extension Options</h1>

  <!-- General Settings -->
  <section id="general-settings">
    <fieldset>
      <legend>General Settings</legend>
      <div>
        <input type="checkbox" id="is-international" />
        <label for="is-international">Is International</label>
      </div>
      <div>
        <input type="checkbox" id="doesUserHaveEbaySubscription" checked />
        <label for="doesUserHaveEbaySubscription">Does User Have eBay Subscription</label>
      </div>
      <div>
        <input type="checkbox" id="isUserTaxExempt" checked />
        <label for="isUserTaxExempt">Is User Tax Exempt</label>
      </div>
    </fieldset>
  </section>

  <!-- Item Location Settings -->
  <section id="item-location-settings">
    <fieldset>
      <legend>Item Location Settings</legend>
      <div>
        <input type="checkbox" id="forceItemLocationSwitch" checked />
        <label for="forceItemLocationSwitch">Force Item Location</label>
      </div>
      <div>
        <label for="itemLocation">Item Location</label>
        <input type="text" id="itemLocation" placeholder="49132" />
      </div>
      <div>
        <label for="itemLocationCountry">Item Location Country</label>
        <select id="itemLocationCountry">
          <option value="" disabled selected>Select a country</option>
          <option value="US">US</option>
          <option value="UK">UK</option>
          <option value="CA">CA</option>
          <option value="AU">AU</option>
          <option value="DE">DE</option>
          <option value="FR">FR</option>
          <option value="IT">IT</option>
          <option value="ES">ES</option>
          <option value="NL">NL</option>
          <option value="BE">BE</option>
          <option value="CH">CH</option>
        </select>
      </div>
      <div>
        <label for="itemLocationCityState">Item Location City, State</label>
        <input type="text" id="itemLocationCityState" placeholder="Enter state: Kentwood, MI" />
      </div>
    </fieldset>
  </section>

  <!-- Return Policy Settings -->
  <section id="return-policy-settings">
    <fieldset>
      <legend>Return Policy Settings</legend>
      <div>
        <input type="checkbox" id="forceReturnPolicySwitch" checked />
        <label for="forceReturnPolicySwitch">Force Return Policy</label>
      </div>
      <div>
        <label for="returnPolicyId">Return Policy ID</label>
        <input type="number" id="returnPolicyId" placeholder="Enter return policy ID" />
      </div>
    </fieldset>
  </section>

  <!-- Listing Settings -->
  <section id="listing-settings">
    <fieldset>
      <legend>Image Listing Settings</legend>

      <div>
        <input type="checkbox" id="onlyListOnePicture" />
        <label for="onlyListOnePicture">Only List One Picture</label>
      </div>
    </fieldset>

    <br>
    
    <fieldset style="display: none;">
      <legend>Custom Image Prompt</legend>
      <div>
        <input type="checkbox" id="useCustomImagePrompt" />
        <label for="useCustomImagePrompt">Show Custom Image Settings</label>
      </div>
      <div>
        <label for="custom_image_prompt">Image Prompt</label>
        <textarea id="custom_image_prompt" placeholder="Enter custom image prompt">Please generate an image ad for my eBay store using this exact product (do not use a generic or alternate version). The design should be slightly sophisticated and aesthetically pleasing. Use a font that matches the style of the image to highlight 2–5 key benefits. These should be concise but still detailed enough to inform and persuade buyers — not overly simplified. The goal is to provide just enough information to spark interest and encourage a purchase without overwhelming the viewer. </textarea>
      </div>
    </fieldset>
  </section>

  <!-- Gmail Monitor Options -->
  <section id="gmail-monitor-options" style="display: none;">
    <fieldset>
      <legend>Gmail Monitor Options</legend>
      <div>
        <button id="sign-in-button">Sign In with Gmail</button>
      </div>
      <div>
        <input type="checkbox" id="monitor-switch" />
        <label for="monitor-switch">Monitor Gmail</label>
      </div>
    </fieldset>
  </section>

  <!-- Remove Words From Listing -->
  <section id="remove-words" style="display: none;">
    <fieldset>
      <legend>Remove Words From Listing</legend>
      <div>
        <input type="checkbox" id="removeWordsFromListingSwitch" />
        <label for="removeWordsFromListingSwitch">Remove Words From Listing</label>
      </div>
      <div>
        <label for="wordsToRemove">Words to Remove</label>
        <textarea id="wordsToRemove" placeholder="Enter words to remove from listing"></textarea>
        <button id="resetWords" type="button">Reset to Default</button>
      </div>
    </fieldset>
  </section>


  <!-- //description prompt text area + check box to use custom description prompt -->
  <section id="description-settings">
    <fieldset>
      <legend>Custom Description Prompt</legend>
      <div>
        <input type="checkbox" id="useCustomDescriptionPrompt" />
        <label for="useCustomDescriptionPrompt">Use Custom Description Prompt</label>
      </div>
      <div>
        <label for="descriptionPrompt">Description Prompt</label>
        <textarea id="descriptionPrompt" placeholder="Enter custom description prompt">You are an expert copywriter. Turn the user’s text into an appealing product description. Keep it concise and professional.</textarea>
      </div>
    </fieldset>

    <fieldset>
      <legend>Default Description Settings</legend>
      <div>
        <input type="checkbox" id="removeSectionsFromDescriptionTemplate" />
        <label for="removeSectionsFromDescriptionTemplate">Remove Shipping/Return/Payment/Feedback /Contact Us Sections</label>
      </div>

      <div>
        <input type="checkbox" id="disableThankYouMessage" />
        <label for="disableThankYouMessage">Disable Thank You Message</label>
      </div>


    </fieldset>


    <br>

    <!-- //add field to use "custom_description" and then a button that opens the description template builder -->
    <fieldset style="display: none;">
      <legend>Custom Description Template</legend>
      <div class="description-template">
        <input type="checkbox" id="useCustomDescriptionTemplate" />
        <label for="useCustomDescriptionTemplate">Use Custom Description Template</label>
        <button id="openDescriptionTemplateBuilder">Open Description Template Builder</button>
      </div>
  
    </fieldset>


   
  </section>


  <section id="title-settings">
    <fieldset>
      <legend>Custom Title Generator</legend>
      <div>
        <input type="checkbox" id="useCustomTitleGenerator" />
        <label for="useCustomTitleGenerator">Use Custom Title Generator</label>
      </div>
      <div>
        <label for="custom_title_prompt">Title Prompt</label>
        <textarea id="custom_title_prompt" placeholder="Enter custom title prompt">You are an expert copywriter. Turn the user’s text into an appealing product title. Keep it concise and professional.</textarea>
      </div>
    </fieldset>

    </section>

  <section id="order-settings">
    <fieldset>
      <legend>Order Settings</legend>
      <div>
        <input type="checkbox" id="autoDisplayAddress" />
        <label for="autoDisplayAddress">Automatically Display Address Beside Orders</label>
      </div>
    </fieldset>
  </section>

  <button id="save">Save</button>

  <script src="options.js"></script>
</body>
</html>
