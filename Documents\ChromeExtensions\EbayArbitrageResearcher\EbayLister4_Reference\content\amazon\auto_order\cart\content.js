var a0_0x29354b=a0_0x44b3;(function(_0x2c46ae,_0xb68811){var _0x47fe87=a0_0x44b3,_0x48c4c0=_0x2c46ae();while(!![]){try{var _0xf39277=parseInt(_0x47fe87(0x7f))/0x1+parseInt(_0x47fe87(0x70))/0x2*(parseInt(_0x47fe87(0x6b))/0x3)+parseInt(_0x47fe87(0x72))/0x4+parseInt(_0x47fe87(0x7b))/0x5+-parseInt(_0x47fe87(0x81))/0x6*(parseInt(_0x47fe87(0x7d))/0x7)+parseInt(_0x47fe87(0x6c))/0x8+-parseInt(_0x47fe87(0x75))/0x9*(parseInt(_0x47fe87(0x73))/0xa);if(_0xf39277===_0xb68811)break;else _0x48c4c0['push'](_0x48c4c0['shift']());}catch(_0x3c249e){_0x48c4c0['push'](_0x48c4c0['shift']());}}}(a0_0x5e53,0x55d3b),console[a0_0x29354b(0x6d)](a0_0x29354b(0x78)));function a0_0x44b3(_0x5a8cd4,_0x22025c){var _0x5e5302=a0_0x5e53();return a0_0x44b3=function(_0x44b34d,_0x272eec){_0x44b34d=_0x44b34d-0x6a;var _0x3ed7c9=_0x5e5302[_0x44b34d];return _0x3ed7c9;},a0_0x44b3(_0x5a8cd4,_0x22025c);}var executed=![];chrome['runtime'][a0_0x29354b(0x7e)][a0_0x29354b(0x77)]((_0x122c24,_0x454f94,_0x119c27)=>{var _0x32ddec=a0_0x29354b;console[_0x32ddec(0x6d)]('Message\x20received',_0x122c24),_0x122c24[_0x32ddec(0x7a)]===_0x32ddec(0x7c)&&_0x122c24['action']===_0x32ddec(0x76)&&!executed&&(executed=!![],proceedToCheckout(_0x122c24[_0x32ddec(0x71)]));});function a0_0x5e53(){var _0x43de27=['414001iZubbo','Proceeding\x20to\x20checkout','507126zurgmU','proceedButton','15261BmpZtc','2435056wuZRXz','log','shouldUseGiftOption','ebaySku','138pRhYvI','orderDetails','2280808mZKcmR','1373210hxKryb','Clicking\x20on\x20cart\x20button','99zNyLGD','proceed_to_checkout','addListener','Amazon\x20Auto\x20Order\x20Cart\x20Content\x20Script\x20Loaded','click','type','1957660vOlCmP','autoOrder','14aZzXRO','onMessage'];a0_0x5e53=function(){return _0x43de27;};return a0_0x5e53();}async function proceedToCheckout(_0x260905){var _0x382d74=a0_0x29354b;console[_0x382d74(0x6d)](_0x382d74(0x80));var _0x4fd0ef=getGoToCartButton();console[_0x382d74(0x6d)](_0x4fd0ef);if(_0x4fd0ef){console[_0x382d74(0x6d)](_0x382d74(0x74)),_0x4fd0ef[_0x382d74(0x79)]();return;}var _0x44c958=getProceedToCheckout();console[_0x382d74(0x6d)](_0x382d74(0x6a),_0x44c958);if(_0x44c958){var _0x587606=atob(_0x260905[_0x382d74(0x6f)]);await removeAllCartItems([_0x587606]);var _0x346076=_0x260905['quantitySold'];_0x346076=parseInt(_0x346076),await setQuantityViaTextField(_0x587606,_0x346076),_0x260905[_0x382d74(0x6e)]&&await setGiftOptions(),_0x44c958=getProceedToCheckout(),_0x44c958[_0x382d74(0x79)]();}}