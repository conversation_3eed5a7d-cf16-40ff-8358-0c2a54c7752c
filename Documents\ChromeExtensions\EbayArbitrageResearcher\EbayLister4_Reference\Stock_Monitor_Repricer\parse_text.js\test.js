const a0_0x67428c=a0_0x4c8c;function a0_0x4c8c(_0x3285cc,_0x44dd42){const _0x26911e=a0_0x2691();return a0_0x4c8c=function(_0x4c8c58,_0x2f2a37){_0x4c8c58=_0x4c8c58-0x97;let _0x498770=_0x26911e[_0x4c8c58];return _0x498770;},a0_0x4c8c(_0x3285cc,_0x44dd42);}function a0_0x2691(){const _0x31b02e=['XMLHttpRequest','json','8litJyv','status','3UptLEu','428364wCcxnl','no-cache','Detail','568298mZmNXN','then','documentElement','toString','entries','text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8','en-US,en;q=0.5','16875meFbUs','Failed\x20to\x20fetch\x20main\x20page:\x20','web','error','4056010lNicpN','textContent','Failed\x20to\x20fetch\x20manufacturer\x20info:\x20','Error\x20fetching\x20manufacturer\x20info:','buffet-sidesheet-ingress','text/html','join','parseFromString','Missing\x20required\x20fields:\x20','541137oKLKys','length','trim','116780eXIdlP','2110120cRCuHb','https://www.amazon.de/dp/B08QHVCWLN?th=1','GET'];a0_0x2691=function(){return _0x31b02e;};return a0_0x2691();}(function(_0x44fe3d,_0x69c1b6){const _0x390694=a0_0x4c8c,_0x310771=_0x44fe3d();while(!![]){try{const _0xb3d570=parseInt(_0x390694(0x9c))/0x1+-parseInt(_0x390694(0xab))/0x2*(parseInt(_0x390694(0xa7))/0x3)+parseInt(_0x390694(0xa0))/0x4+parseInt(_0x390694(0x9f))/0x5+parseInt(_0x390694(0xa8))/0x6+-parseInt(_0x390694(0xb6))/0x7+parseInt(_0x390694(0xa5))/0x8*(-parseInt(_0x390694(0xb2))/0x9);if(_0xb3d570===_0x69c1b6)break;else _0x310771['push'](_0x310771['shift']());}catch(_0x3f66ac){_0x310771['push'](_0x310771['shift']());}}}(a0_0x2691,0x48beb));async function getManufacturerInfo(_0x47485e){const _0xffaf20=a0_0x4c8c;try{const _0x433100='https://www.amazon.de/dp/'+_0x47485e,_0x2cfc36=await fetch(_0x433100,{'method':_0xffaf20(0xa2),'headers':{'Accept':_0xffaf20(0xb0),'Accept-Language':_0xffaf20(0xb1),'Cache-Control':_0xffaf20(0xa9),'Pragma':'no-cache'}});if(!_0x2cfc36['ok'])throw new Error(_0xffaf20(0xb3)+_0x2cfc36[_0xffaf20(0xa6)]);const _0x37b481='https://www.amazon.de/gp/product/ajax/buffet-sidesheet-content',_0x882f19=new URLSearchParams({'asin':_0x47485e,'deviceType':_0xffaf20(0xb4),'pageType':_0xffaf20(0xaa),'ingress':_0xffaf20(0x97)}),_0x208561=await fetch(_0x37b481+'?'+_0x882f19[_0xffaf20(0xae)](),{'method':_0xffaf20(0xa2),'headers':{'Accept':'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8','Accept-Language':_0xffaf20(0xb1),'X-Requested-With':_0xffaf20(0xa3),'Referer':_0xffaf20(0xa1)}});if(!_0x208561['ok'])throw new Error(_0xffaf20(0xb8)+_0x208561[_0xffaf20(0xa6)]);const _0x5748ba=await _0x208561[_0xffaf20(0xa4)](),_0x57da2a=new DOMParser(),_0x20fd3e=_0x57da2a[_0xffaf20(0x9a)](_0x5748ba['html'],_0xffaf20(0x98)),_0x2f2abd=_0x20fd3e[_0xffaf20(0xad)][_0xffaf20(0xb7)],_0x83ba41={'manufacturer':extractMatch(_0x2f2abd,/Shenzhen USEER Robotics Co\.,Ltd\./),'address':extractMatch(_0x2f2abd,/Building 2, Fashion Brand Industrial Park, E'Bu Town, Shenzhen-Shanwei Special Cooperation Zone/),'location':extractMatch(_0x2f2abd,/Shenzhen, Guangdong, CN/),'email':extractMatch(_0x2f2abd,/Yunshirobotics@outlook\.com/)},_0x283d89=Object[_0xffaf20(0xaf)](_0x83ba41)['filter'](([_0x146d36,_0x358dfd])=>!_0x358dfd)['map'](([_0x282195])=>_0x282195);if(_0x283d89[_0xffaf20(0x9d)]>0x0)throw new Error(_0xffaf20(0x9b)+_0x283d89[_0xffaf20(0x99)](',\x20'));return _0x83ba41;}catch(_0x3a1d52){console[_0xffaf20(0xb5)](_0xffaf20(0xb9),_0x3a1d52);throw _0x3a1d52;}}function extractMatch(_0x1b7341,_0x27fdef){const _0x44d71a=a0_0x4c8c,_0x1ec197=_0x1b7341['match'](_0x27fdef);return _0x1ec197?_0x1ec197[0x0][_0x44d71a(0x9e)]():'';}const asin='B08QHVCWLN';getManufacturerInfo(asin)[a0_0x67428c(0xac)](_0x319aaf=>console['log']('Manufacturer\x20Info:',_0x319aaf))['catch'](_0x4f2a30=>console[a0_0x67428c(0xb5)]('Error:',_0x4f2a30));