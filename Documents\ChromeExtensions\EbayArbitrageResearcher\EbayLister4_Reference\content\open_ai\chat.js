var a0_0x50b808=a0_0x5302;(function(_0x4fcc06,_0x902310){var _0x4a2cb6=a0_0x5302,_0x3034dd=_0x4fcc06();while(!![]){try{var _0x5ab980=-parseInt(_0x4a2cb6(0x13f))/0x1*(parseInt(_0x4a2cb6(0xf2))/0x2)+-parseInt(_0x4a2cb6(0x135))/0x3+parseInt(_0x4a2cb6(0x12a))/0x4*(parseInt(_0x4a2cb6(0xf5))/0x5)+parseInt(_0x4a2cb6(0xc6))/0x6+-parseInt(_0x4a2cb6(0xb0))/0x7*(parseInt(_0x4a2cb6(0x133))/0x8)+-parseInt(_0x4a2cb6(0x8f))/0x9*(parseInt(_0x4a2cb6(0x124))/0xa)+parseInt(_0x4a2cb6(0xe1))/0xb;if(_0x5ab980===_0x902310)break;else _0x3034dd['push'](_0x3034dd['shift']());}catch(_0xa05bda){_0x3034dd['push'](_0x3034dd['shift']());}}}(a0_0x537a,0x33cba),console['log'](a0_0x50b808(0x104)));let messageQueue=[],isProcessing=![];var processedMessagesCount=0x0,webVersion=0x1,documentTitle=a0_0x50b808(0xee),checkLimitReachedInterval;setInterval(function(){var _0x12637e=a0_0x50b808;document[_0x12637e(0x136)]=documentTitle;},0x12c);var chatGptCompletedFavicon=a0_0x50b808(0x90),chatGptProcessingFavicon=a0_0x50b808(0xe8);async function checkVersion(){var _0x1d7e6d=a0_0x50b808,_0x344b59=document[_0x1d7e6d(0x119)](_0x1d7e6d(0xd8));for(var _0x252b2a=0x0;_0x252b2a<_0x344b59[_0x1d7e6d(0xa2)];_0x252b2a++){if(_0x344b59[_0x252b2a][_0x1d7e6d(0xd6)]===_0x1d7e6d(0xf0))return sendLogMessage(_0x1d7e6d(0x151)),0x1;}return sendLogMessage(_0x1d7e6d(0x147)),0x2;}function activateTab(){var _0x3b9e5e=a0_0x50b808;console[_0x3b9e5e(0x137)](_0x3b9e5e(0x117)),Object[_0x3b9e5e(0xbe)](document,_0x3b9e5e(0x14f),{'value':_0x3b9e5e(0xf4),'writable':!![]}),console[_0x3b9e5e(0x137)](_0x3b9e5e(0x99),document['visibilityState']),Object['defineProperty'](document,_0x3b9e5e(0x14e),{'value':![],'writable':!![]}),console[_0x3b9e5e(0x137)](_0x3b9e5e(0xdf),document[_0x3b9e5e(0x14e)]),window['dispatchEvent'](new Event('focus')),console[_0x3b9e5e(0x137)]('Focus\x20event\x20dispatched');}async function main(){var _0x14de4e=a0_0x50b808;await onPageLoadAndStable();var _0x2b7d9a=await waitForElement(_0x14de4e(0x9f),0x186a0),_0x3c52e6=_0x2b7d9a[_0x14de4e(0xd4)],_0x3af742=createConnectToBackEndButton();webVersion=await checkVersion(),console['log'](_0x14de4e(0xce),webVersion);var _0x13274f=null;webVersion===0x1&&(_0x13274f=await waitForElementByText(_0x14de4e(0xd8),'New\x20Chat',0x186a0));webVersion===0x2&&(_0x13274f=await waitForElementByText(_0x14de4e(0xca),_0x14de4e(0xc3),0x186a0));_0x13274f['parentElement'][_0x14de4e(0x113)](_0x3af742);var _0x3af742=await waitForElementByText('Button','Connect',0x186a0);_0x3af742[_0x14de4e(0x11c)]();}function a0_0x537a(){var _0x2a9e32=['hidden','visibilityState','initialize-btn','Version\x201\x20detected','No\x20differences\x20found.','chat.js:\x20startNewChat:\x20textArea:\x20','Send\x20Prompt','check_alive\x20received','chat.js:\x20waitForStopGeneratingButton:\x20','18ZxUvrX','Favicons/Completed/chatGptReceived.png','Regenerate\x20button\x20not\x20found,\x20checking\x20again...','Last\x20Message:\x20','Connecting..','innerHTML','.group\x20.markdown','Stop\x20Generating\x20Button\x20Found','hasFocus','querySelector','visibilityState\x20set\x20to:','shift','Last\x20Message\x20Received','getURL','chat.js:\x20Limit\x20Not\x20Reached','Regenerate','#prompt-textarea','Error\x20Detected','absolute','length','chat.js:\x20prompt\x20and\x20secondLastMessage\x20are\x20same\x20after\x20trimming','prompt-textarea','Document\x20Has\x20Focus:\x20','TEXT_NODE','make_tab_active','Waiting\x2010\x20seconds\x20for\x20last\x20message','response','trim','\x20ms\x20for\x20selector\x20','chat.js:\x20makeTabActive:\x20error:\x20','chat.js:\x20postStatus\x20done','response:\x20','Web\x20Version\x201:\x20Regenerate\x20Button\x20Found','553CZJBUn','classList','chat.js:\x20waitForRegenarteButton\x20done','createElement','connected','button[aria-label=\x22Stop\x20generating\x22]','get_last_message','Disabled\x20Send\x20Button\x20Found','removeListener','Asking\x20Chat\x20GPT\x20Web\x20with\x20prompt:\x20','Prompt\x20and\x20Second\x20Last\x20Message\x20are\x20not\x20same','childNodes','Waiting\x20for\x20Regenerate\x20Button','disconnect','defineProperty','limit','performance','sendMessage','result:\x20','ChatGPT','chat.js:\x20waitForRegenarteButton:\x20','ELEMENT_NODE','2017566rIlrkV','.markdown','post-request-detected','chat.js:\x20Limit\x20Reached','div','chat.js:\x20button.onclick','chat.js\x20getLastMessage:\x20ask_chat_gpt_web:\x20error:\x20','prompt','chat.js:\x20webVersion:\x20','Cleared\x20all\x20intervals','now','sendResponse','onclick','input','parentElement','forEach','innerText','getElementById','span','nodeValue','Network\x20State:\x20Online','\x20with\x20innerText\x20','runtime','div[role=\x27presentation\x27]\x20.flex.flex-col.text-sm','max','hidden\x20set\x20to:','mousemove','4327565XYleUz','Regenerate\x20Button\x20Found','initialize_chat_gpt_web','button.text-token-text-primary','Waiting\x2010\x20seconds','Web\x20Version\x202:\x20Regenerate\x20Button\x20Found','Timed\x20out\x20after\x20','Favicons/OpenAi/chat_gpt_connected.png','reached','error','Second\x20Last\x20Message:\x20','button','replace','Chat\x20GPT\x20Web','chat.js:\x20Entered\x20Prompt','New\x20Chat','blur','8rcJeNy','chat.js:\x20waitForDisabledSendButton:\x20','visible','5oEEvQt','addListener','Error\x20getting\x20last\x20message:\x20','includes','check_alive','Response\x20Received','Too\x20Many\x20Requests','None','width','prompt:\x20','change','ask_chat_gpt_web','secondLastMessage:\x20','preventDefault','Web\x20Version\x202:\x20Disabled\x20Send\x20Button\x20Found','chat.js\x20loaded','type','\x27\x20vs\x20\x27','button.m-auto','.group','request:\x20','nodeType','div[role=\x27presentation\x27]\x20.text-red-500','Processing\x20Message','Network\x20State:\x20Offline','chat.js:\x20prompt\x20and\x20secondLastMessage\x20are\x20not\x20same\x20even\x20after\x20trimming',':\x20\x27','Second\x20Last\x20Message\x20Length:\x20','Performance\x20Timing:\x20','Found\x20regenerateButton','appendChild','add','Last\x20Message\x20Length:\x20','div[data-testid*=\x27conversation-turn-\x27]','Activating\x20alwaysActive\x20function','body','querySelectorAll','button[data-testid=\x22send-button\x22]','chat.js:\x20last_message:\x20','click','push','Prompt\x20and\x20Second\x20Last\x20Message\x20are\x20not\x20same\x20even\x20after\x20trimming','Connected','button.md\x5c:group-hover\x5c:visible','ask_button.click()\x20done','Prompt\x20Sent','stopPropagation','740670vHdyhZ','message','get_last_message\x20received','button[data-testid=\x27send-button\x27]','Waiting\x20for\x20Chat\x20Box\x20to\x20be\x20Idle','style','977596IOEJrA','tagName','Asking\x20Chat\x20GPT\x20Web','observe','Entered\x20Prompt','attributes','focus','disabled','POST\x20request\x20to\x20lat/r\x20detected!','22728oTjHQo','request','1099200NNRWHk','title','log','Timed\x20out\x20waiting\x20for\x20the\x20regenerate\x20button','Waiting\x20for\x20Last\x20Message','Window\x20Blur\x20State:\x20','Active\x20Element:\x20','onMessage','Connect','Use\x20default\x20model','5737GuMiuw','Response\x201','dispatchEvent','Prompt\x20and\x20Second\x20Last\x20Message\x20are\x20same\x20after\x20trimming','chat.js:\x20Error\x20Detected','chat.js:\x20Too\x20Many\x20Requests:\x20','send_prompt','removeChild','Version\x202\x20detected','chat.js\x20onMessage\x20received:\x20','Stop\x20generating','div[data-message-author-role=\x27user\x27]','onLine','value','Total\x20differences:\x20'];a0_0x537a=function(){return _0x2a9e32;};return a0_0x537a();}main();function createButtonToSendPrompt(){var _0xb50752=a0_0x50b808,_0x39028d=document['createElement'](_0xb50752(0xec));return _0x39028d['id']=_0xb50752(0x145),_0x39028d[_0xb50752(0x94)]=_0xb50752(0x8c),_0x39028d[_0xb50752(0xd2)]=async function(_0x4d5226){var _0x2e5ab5=_0xb50752;_0x4d5226[_0x2e5ab5(0x102)](),_0x4d5226[_0x2e5ab5(0x123)]();var _0xe210a3=document[_0x2e5ab5(0xd7)]('prompt-textarea')[_0x2e5ab5(0x14c)],_0x3998f6=await ask_chat_gpt_web(_0xe210a3);console['log'](_0x2e5ab5(0xae),_0x3998f6);},_0x39028d;}function createConnectToBackEndButton(){var _0x2718de=a0_0x50b808,_0xb35e41=document[_0x2718de(0xb3)](_0x2718de(0xec));return _0xb35e41['id']=_0x2718de(0xe3),_0xb35e41[_0x2718de(0x94)]=_0x2718de(0x13d),_0xb35e41['classList'][_0x2718de(0x114)](_0x2718de(0x150)),_0xb35e41[_0x2718de(0xd2)]=async function(_0x59459d){var _0x186a47=_0x2718de;_0x59459d[_0x186a47(0x102)](),_0x59459d[_0x186a47(0x123)](),_0xb35e41[_0x186a47(0x94)]=_0x186a47(0x93);var _0xe22891=await new Promise(function(_0x3f841d,_0x3d93a7){var _0x2e3c0b=_0x186a47;chrome[_0x2e3c0b(0xdc)][_0x2e3c0b(0xc1)]({'type':'add_chat_gpt_web_connection'},function(_0x449644){var _0x383101=_0x2e3c0b;console[_0x383101(0x137)]('result:\x20',_0x449644),_0x3f841d(_0x449644[_0x383101(0xa9)]);});});console[_0x186a47(0x137)]('response:\x20',_0xe22891),_0xb35e41['innerHTML']=_0x186a47(0x11f),_0xb35e41[_0x186a47(0xb1)][_0x186a47(0x114)](_0x186a47(0xb4)),console[_0x186a47(0x137)](_0x186a47(0xcb));},_0xb35e41;}chrome['runtime']['onMessage']['addListener'](function(_0x444ba0,_0x56d921,_0x4db7ef){var _0x297e4e=a0_0x50b808;console[_0x297e4e(0x137)](_0x297e4e(0x148),_0x444ba0);if(_0x444ba0['message']===_0x297e4e(0x100))return console['log']('ask_chat_gpt_web\x20received',_0x444ba0),messageQueue[_0x297e4e(0x11d)]({'request':_0x444ba0,'sendResponse':_0x4db7ef}),!isProcessing&&processQueue(),!![];if(_0x444ba0[_0x297e4e(0x125)]===_0x297e4e(0xf9))return console['log'](_0x297e4e(0x8d)),_0x4db7ef({'alive':!![]}),!![];if(_0x444ba0[_0x297e4e(0x125)]===_0x297e4e(0xb6)){console['log'](_0x297e4e(0x126));var _0x5a098a=getLastMessage();return console[_0x297e4e(0x137)](_0x297e4e(0x11b),_0x5a098a),_0x4db7ef({'message':_0x5a098a}),!![];}return!![];});function a0_0x5302(_0x195948,_0x4ceaee){var _0x537a9a=a0_0x537a();return a0_0x5302=function(_0x530237,_0x1f1220){_0x530237=_0x530237-0x8a;var _0x759d14=_0x537a9a[_0x530237];return _0x759d14;},a0_0x5302(_0x195948,_0x4ceaee);}async function processQueue(){var _0x4e6fa4=a0_0x50b808;if(messageQueue[_0x4e6fa4(0xa2)]===0x0){isProcessing=![];return;}isProcessing=!![];let _0x519939=messageQueue[_0x4e6fa4(0x9a)]();_0x519939['request']['options']['startNewChat']&&(await startNewChat(),setDocumentTitle('New\x20Chat\x20Started\x20from\x20processQueue'));try{setDocumentTitle(_0x4e6fa4(0x10c));let _0x57bdf=await ask_chat_gpt_web(_0x519939['request'][_0x4e6fa4(0xcd)]);setDocumentTitle('Message\x20Processed:\x20'+_0x519939[_0x4e6fa4(0x134)]['prompt']+',\x20'+_0x57bdf[_0x4e6fa4(0xa2)]),_0x519939[_0x4e6fa4(0xd1)](_0x57bdf),processedMessagesCount++,processedMessagesCount%0x32===0x0&&await startNewChat();}catch(_0x159ae7){console[_0x4e6fa4(0xea)]('Error\x20processing\x20message:',_0x159ae7);}processQueue();}async function startNewChat(){var _0x5b0765=a0_0x50b808,_0xc4092f=null;webVersion===0x1&&(_0xc4092f=await waitForElementByText(_0x5b0765(0xd8),_0x5b0765(0xf0),0x186a0));webVersion===0x2&&(_0xc4092f=await waitForElement(_0x5b0765(0xe4)));_0xc4092f[_0x5b0765(0x11c)]();var _0x1245cb=await waitForElement(_0x5b0765(0x9f),0x186a0);return console[_0x5b0765(0x137)](_0x5b0765(0x8b),_0x1245cb),await new Promise(_0x2ad1fb=>setTimeout(_0x2ad1fb,0x3e8)),activateTab(),!![];}async function ask_chat_gpt_web(_0x926d82){var _0x61b510=a0_0x50b808;try{clearInterval(checkLimitReachedInterval);}catch(_0x297c97){}var _0x421c4e=document['title'];document[_0x61b510(0x136)]='Asking\x20Chat\x20GPT\x20Web',setDocumentTitle(_0x61b510(0x12c)),sendLogMessage(_0x61b510(0xb9)+_0x926d82);var _0x45612c=checkIfThereWasAnError();if(_0x45612c)return sendLogMessage(_0x61b510(0xa0)),console['log'](_0x61b510(0x143)),sendLogMessage(_0x61b510(0xe5)),await new Promise(_0x3c27f7=>setTimeout(_0x3c27f7,0x2710)),await startNewChat(),await ask_chat_gpt_web(_0x926d82);checkLimitReachedInterval=setInterval(()=>{var _0x86c1a5=_0x61b510;if(checkIfLimitReached())return console[_0x86c1a5(0x137)]('Exiting\x20startProcess\x20due\x20to\x20limit\x20reached.'),clearInterval(checkLimitReachedInterval),null;},0x3e8);var _0x29db17=document[_0x61b510(0xd7)](_0x61b510(0xa4));changeFaviconOfPage2(chrome['runtime'][_0x61b510(0x9c)](chatGptProcessingFavicon));var _0x2fb29b=null;try{await enter_text(_0x29db17,_0x926d82),_0x2fb29b=waitForPostMessage(),setDocumentTitle(_0x61b510(0x12e)),console[_0x61b510(0x137)](_0x61b510(0xef));}catch{return clearInterval(checkLimitReachedInterval),await startNewChat(),await ask_chat_gpt_web(_0x926d82);}var _0x1a5a8d=waitForStopGeneratingButton(),_0xb6d195=_0x29db17[_0x61b510(0xd4)][_0x61b510(0x98)](_0x61b510(0x127));await waitForButtonEnabled(_0xb6d195),_0xb6d195['click'](),console[_0x61b510(0x137)]('chat.js:\x20ask_button.click()\x20done'),sendLogMessage(_0x61b510(0x121)),document[_0x61b510(0x136)]=_0x61b510(0x122),setDocumentTitle(_0x61b510(0x122));var _0x40ffab=checkAndClickButtonWithInnerText('button',_0x61b510(0x140)),_0x2912ad=checkAndClickButtonWithInnerText(_0x61b510(0x107),_0x61b510(0x9e)),_0x4b5f6f=checkAndClickButtonWithInnerText(_0x61b510(0xec),_0x61b510(0x13e));sendLogMessage(_0x61b510(0xbc)),await _0x1a5a8d,setDocumentTitle(_0x61b510(0x96)),sendLogMessage(_0x61b510(0x96));if(webVersion===0x1){var _0x17cc4d=await waitForRegenarteButton();_0x17cc4d[_0x61b510(0x130)](),setDocumentTitle('Regenerate\x20Button\x20Found'),sendLogMessage(_0x61b510(0xaf));}if(webVersion===0x2){var _0xc3fb25=await waitForDisabledSendButton();setDocumentTitle(_0x61b510(0xb7)),sendLogMessage(_0x61b510(0x103));var _0x17cc4d=await waitForRegenerateButtonV2();setDocumentTitle(_0x61b510(0xe2)),sendLogMessage(_0x61b510(0xe6)),_0xc3fb25[_0x61b510(0x130)](),_0x17cc4d[_0x61b510(0x130)]();}await _0x2fb29b,sendLogMessage('Post\x20Status\x20Done'),console[_0x61b510(0x137)](_0x61b510(0xad)),console['log'](_0x61b510(0xb2)),setDocumentTitle('Regenerate\x20Button\x20Found'),clearInterval(_0x40ffab),clearInterval(_0x2912ad),clearInterval(_0x4b5f6f),clearInterval(checkLimitReachedInterval),sendLogMessage(_0x61b510(0xcf));var _0x3e5a9c=document['querySelector'](_0x61b510(0x10b));if(_0x3e5a9c)return sendLogMessage(_0x61b510(0xfb)),console[_0x61b510(0x137)](_0x61b510(0x144),_0x3e5a9c),sendLogMessage(_0x61b510(0xe5)),await ask_chat_gpt_web(_0x926d82);setDocumentTitle('Getting\x20Last\x20Message'),sendLogMessage('Getting\x20Last\x20Message');var _0x479b5e=await getSecondLastMessage();console['log'](_0x61b510(0x101),_0x479b5e),console[_0x61b510(0x137)](_0x61b510(0xfe),_0x926d82),sendLogMessage(_0x61b510(0xeb)+_0x479b5e),setDocumentTitle(_0x61b510(0x9b));if(compareNormalizedStrings(_0x926d82,_0x479b5e))sendLogMessage(_0x61b510(0x142)),console['log'](_0x61b510(0xa3)),setDocumentTitle(_0x61b510(0x142)),setDocumentTitle(_0x61b510(0x110)+_0x479b5e[_0x61b510(0xa2)]);else return sendLogMessage(_0x61b510(0x11e)),console[_0x61b510(0x137)](_0x61b510(0x10e)),setDocumentTitle(_0x61b510(0xba)),await ask_chat_gpt_web(_0x926d82);sendLogMessage(_0x61b510(0x128));var _0x5a4f85=await waitForIdleChatBox();sendLogMessage('Chat\x20Box\x20is\x20Idle'),sendLogMessage(_0x61b510(0x139));var _0x423e5b=null;try{_0x423e5b=getLastMessage(),sendLogMessage(_0x61b510(0x92)+_0x423e5b),setDocumentTitle(_0x61b510(0x115)+_0x423e5b[_0x61b510(0xa2)]);}catch(_0x56387e){sendLogMessage(_0x61b510(0xf7)+_0x56387e),console['log'](_0x61b510(0xcc),_0x56387e),sendLogMessage(_0x61b510(0xa8)),await new Promise(_0x30119e=>setTimeout(_0x30119e,0x2710)),_0x423e5b=getLastMessage();}return console[_0x61b510(0x137)](_0x61b510(0x11b),_0x423e5b),document[_0x61b510(0x136)]=_0x61b510(0xfa),setTimeout(function(){var _0x2dcc2=_0x61b510;document[_0x2dcc2(0x136)]=_0x421c4e;},0xbb8),changeFaviconOfPage2(chrome[_0x61b510(0xdc)][_0x61b510(0x9c)](chatGptCompletedFavicon)),_0x423e5b;}function printDifferences(_0x2815b2,_0x4021ab){var _0x5a12b3=a0_0x50b808;let _0x159e46=[];for(let _0x489be8=0x0;_0x489be8<Math[_0x5a12b3(0xde)](_0x2815b2[_0x5a12b3(0xa2)],_0x4021ab[_0x5a12b3(0xa2)]);_0x489be8++){_0x2815b2[_0x489be8]!==_0x4021ab[_0x489be8]&&(_0x159e46[_0x5a12b3(0x11d)](_0x489be8),console[_0x5a12b3(0x137)]('Difference\x20at\x20position\x20'+_0x489be8+_0x5a12b3(0x10f)+_0x2815b2[_0x489be8]+_0x5a12b3(0x106)+_0x4021ab[_0x489be8]+'\x27'));}_0x159e46[_0x5a12b3(0xa2)]===0x0?console['log'](_0x5a12b3(0x8a)):console[_0x5a12b3(0x137)](_0x5a12b3(0x14d)+_0x159e46['length']);}async function setDocumentTitle(_0x475815){documentTitle=_0x475815;}function normalizeString(_0x16a55f){var _0x1d6e93=a0_0x50b808;return _0x16a55f[_0x1d6e93(0xed)](/\s+/g,'');}function compareNormalizedStrings(_0x2db331,_0x837017){return normalizeString(_0x2db331)===normalizeString(_0x837017);}function dispatchInputEvent(_0x338f28,_0x323c57){var _0xce4a5e=a0_0x50b808;_0x338f28['focus']();const _0x1dfccf=new InputEvent(_0xce4a5e(0xd3),{'bubbles':!![],'inputType':'insertText','data':_0x323c57});_0x338f28[_0xce4a5e(0x141)](_0x1dfccf);const _0x24c9f2=new Event(_0xce4a5e(0xff),{'bubbles':!![]});_0x338f28[_0xce4a5e(0x141)](_0x24c9f2);}async function waitForButtonEnabled(_0x6bf7f1){return new Promise(_0xad9128=>{var _0x21bfe6=a0_0x5302;if(!_0x6bf7f1[_0x21bfe6(0x131)]){_0xad9128();return;}const _0x347ca8=new MutationObserver(_0x5b05ba=>{var _0x59d0f2=_0x21bfe6;_0x5b05ba[_0x59d0f2(0xd5)](_0x14f291=>{var _0x2f744b=_0x59d0f2;_0x14f291[_0x2f744b(0x105)]===_0x2f744b(0x12f)&&!_0x6bf7f1[_0x2f744b(0x131)]&&(_0x347ca8[_0x2f744b(0xbd)](),_0xad9128());});});_0x347ca8[_0x21bfe6(0x12d)](_0x6bf7f1,{'attributes':!![]});});}async function enter_text(_0x3d4a06,_0x5605c3){_0x3d4a06['value']=_0x5605c3,dispatchInputEvent(_0x3d4a06,_0x5605c3);}function getLastMessage(){var _0x2e1942=a0_0x50b808,_0x3213ea=getLastMessageElement(),_0x119873='';return _0x3213ea['childNodes']['forEach'](_0x23290a=>{var _0x50291c=a0_0x5302;if(_0x23290a[_0x50291c(0x10a)]===Node[_0x50291c(0xa6)])_0x119873+=_0x23290a[_0x50291c(0xd9)];else _0x23290a[_0x50291c(0x10a)]===Node[_0x50291c(0xc5)]&&(_0x119873+=_0x23290a[_0x50291c(0xd6)]+'\x0a');}),_0x119873[_0x2e1942(0xaa)]();}function getLastMessageElement(){var _0x163863=a0_0x50b808,_0x24ce98=document['querySelector'](_0x163863(0xdd)),_0x1bca9e=_0x24ce98['querySelectorAll']('.group'),_0x249ec1=_0x1bca9e[_0x1bca9e['length']-0x1],_0x1436d3=_0x249ec1[_0x163863(0x98)](_0x163863(0xc7));return _0x1436d3;}async function getSecondLastMessage(){var _0x3f8e52=a0_0x50b808,_0x4cb48c=document[_0x3f8e52(0x98)]('div[role=\x27presentation\x27]\x20.flex.flex-col.text-sm');await waitForElement(_0x3f8e52(0x95),0x186a0);var _0x3c631b=_0x4cb48c[_0x3f8e52(0x119)](_0x3f8e52(0x108)),_0x3e3712=_0x3c631b[_0x3c631b['length']-0x2],_0x352b00=_0x3e3712[_0x3f8e52(0x98)](_0x3f8e52(0x14a))[_0x3f8e52(0x98)](_0x3f8e52(0xca)),_0x22c49c='';return _0x352b00[_0x3f8e52(0xbb)]['forEach'](_0x528bde=>{var _0x2d3e6c=_0x3f8e52;if(_0x528bde[_0x2d3e6c(0x10a)]===Node[_0x2d3e6c(0xa6)])_0x22c49c+=_0x528bde[_0x2d3e6c(0xd9)];else _0x528bde[_0x2d3e6c(0x10a)]===Node[_0x2d3e6c(0xc5)]&&(_0x22c49c+=_0x528bde['innerText']+'\x0a');}),_0x22c49c[_0x3f8e52(0xaa)]();}async function waitForRegenarteButton(){var _0x4717cb=a0_0x50b808,_0x2bc7d6=await waitForElementByText('button',_0x4717cb(0x9e));return console[_0x4717cb(0x137)](_0x4717cb(0xc4),_0x2bc7d6),await new Promise(_0x53b799=>setTimeout(_0x53b799,0x3e8)),_0x2bc7d6;}async function waitForDisabledSendButton(){var _0x2b8310=a0_0x50b808,_0x2430fc=await waitForElement(_0x2b8310(0x11a),0xea60);return console['log'](_0x2b8310(0xf3),_0x2430fc),_0x2430fc;}function waitForRegenerateButtonV2(){return new Promise((_0x16d548,_0x4ef895)=>{const _0x46e00e=setTimeout(()=>{var _0x383ab0=a0_0x5302;clearInterval(_0xcf1ca1),_0x4ef895(_0x383ab0(0x138));},0x7530),_0xcf1ca1=setInterval(()=>{var _0x560c99=a0_0x5302,_0x3876a2=document['querySelectorAll'](_0x560c99(0x116)),_0x5a9493=_0x3876a2[_0x3876a2[_0x560c99(0xa2)]-0x1],_0x471e85=_0x5a9493?_0x5a9493[_0x560c99(0x98)](_0x560c99(0x120)):null;_0x471e85?(console['log'](_0x560c99(0x112),_0x471e85),clearInterval(_0xcf1ca1),clearTimeout(_0x46e00e),_0x16d548(_0x471e85)):console[_0x560c99(0x137)](_0x560c99(0x91));},0x3e8);});}async function waitForStopGeneratingButton(){var _0x5cf666=a0_0x50b808,_0x20e538;try{webVersion===0x1&&(_0x20e538=await waitForElementByText('button',_0x5cf666(0x149),0x2710)),webVersion===0x2&&(_0x20e538=await waitForElement(_0x5cf666(0xb5),0xea60));}catch(_0x23575d){console[_0x5cf666(0x137)]('chat.js:\x20waitForStopGeneratingButton:\x20error:\x20',_0x23575d);}return console[_0x5cf666(0x137)](_0x5cf666(0x8e),_0x20e538),_0x20e538;}function waitForIdle(_0xaefc8f,_0x5f39e4=0x1388){return new Promise(_0x24b422=>{var _0x778e56=a0_0x5302;let _0x39f5e6,_0x59cff5;const _0x2cc2c8=new MutationObserver(()=>{_0x59cff5=_0xaefc8f['textContent'],_0x59cff5===_0x39f5e6&&(_0x24b422(),_0x2cc2c8['disconnect']()),_0x39f5e6=_0x59cff5;});_0x2cc2c8[_0x778e56(0x12d)](_0xaefc8f,{'childList':!![],'subtree':!![]}),setTimeout(()=>{_0x24b422(),_0x2cc2c8['disconnect']();},_0x5f39e4);});}async function waitForElementByText(_0x4fe1ad,_0x44d782,_0x1c878e=0xea60){return new Promise((_0x267176,_0x152018)=>{const _0x3e330a=setInterval(()=>{var _0x330692=a0_0x5302;const _0x53dd16=document['querySelectorAll'](_0x4fe1ad);_0x53dd16[_0x330692(0xd5)](_0x1d0528=>{_0x1d0528['innerText']===_0x44d782&&(clearInterval(_0x3e330a),_0x267176(_0x1d0528));});},0x64);setTimeout(()=>{var _0x58a8b9=a0_0x5302;clearInterval(_0x3e330a),_0x152018(new Error('Timed\x20out\x20after\x20'+_0x1c878e+_0x58a8b9(0xab)+_0x4fe1ad+_0x58a8b9(0xdb)+_0x44d782));},_0x1c878e);});}async function waitForElement(_0x3af678,_0x90fc0a=0xea60){return new Promise((_0x4d69f6,_0x52ec05)=>{const _0x5a3106=setInterval(()=>{var _0x190d78=a0_0x5302;const _0x1c4d46=document[_0x190d78(0x98)](_0x3af678);_0x1c4d46&&(clearInterval(_0x5a3106),_0x4d69f6(_0x1c4d46));},0x64);setTimeout(()=>{var _0x48ddd9=a0_0x5302;clearInterval(_0x5a3106),_0x52ec05(new Error(_0x48ddd9(0xe7)+_0x90fc0a+_0x48ddd9(0xab)+_0x3af678));},_0x90fc0a);});}function checkAndClickButtonWithInnerText(_0x175560,_0xeebb21){const _0x334b94=setInterval(()=>{var _0x6eda70=a0_0x5302;const _0x40b4dd=document[_0x6eda70(0x119)](_0x175560);_0x40b4dd[_0x6eda70(0xd5)](_0x1906ca=>{var _0x40854e=_0x6eda70;_0x1906ca[_0x40854e(0xd6)][_0x40854e(0xf8)](_0xeebb21)&&(clearInterval(_0x334b94),_0x1906ca[_0x40854e(0x11c)]());});},0x64);return _0x334b94;}async function waitForPostMessage(){var _0x590819=await new Promise((_0x3b8650,_0xafc363)=>{var _0x1b5960=a0_0x5302;const _0x85bc14=function(_0x102622,_0x5d25f4,_0x539fe9){var _0x4abc64=a0_0x5302;_0x102622[_0x4abc64(0x105)]===_0x4abc64(0xc8)&&(console[_0x4abc64(0x137)](_0x4abc64(0x132)),console['log'](_0x4abc64(0x109),_0x102622),chrome['runtime']['onMessage'][_0x4abc64(0xb8)](_0x85bc14),_0x3b8650(_0x102622));};chrome['runtime'][_0x1b5960(0x13c)][_0x1b5960(0xf6)](_0x85bc14);});return _0x590819;}function sendLogMessage(_0x428fb9){var _0x575724=a0_0x50b808;chrome[_0x575724(0xdc)][_0x575724(0xc1)]({'type':_0x575724(0x137),'log':_0x428fb9});}async function waitForElementIdle(_0x5e7a9a,_0x50da04=0x3e8){return new Promise((_0x364f1a,_0x20859b)=>{var _0x4cc507=a0_0x5302;let _0x490061=null;const _0x54c711=()=>{var _0x18961f=a0_0x5302;_0x393321[_0x18961f(0xbd)](),_0x364f1a();},_0x393321=new MutationObserver(()=>{clearTimeout(_0x490061),_0x490061=setTimeout(_0x54c711,_0x50da04);});_0x393321[_0x4cc507(0x12d)](_0x5e7a9a,{'childList':!![],'subtree':!![],'attributes':!![]}),_0x490061=setTimeout(_0x54c711,_0x50da04);});}async function waitForIdleChatBox(){logPageState();var _0x1e58e5=await makeTabActive();sendLogMessage('Tab\x20Activated:\x20'+_0x1e58e5);var _0x54459a=getLastMessageElement();return await waitForElementIdle(_0x54459a,0x3e8),_0x54459a;}async function makeTabActive(){var _0x103310=await new Promise(function(_0x117e9c,_0x2dccb5){var _0x596f19=a0_0x5302;chrome[_0x596f19(0xdc)]['sendMessage']({'type':_0x596f19(0xa7)},function(_0x27f845){var _0x1c5dbf=_0x596f19;console[_0x1c5dbf(0x137)](_0x1c5dbf(0xc2),_0x27f845);var _0x56361c=![];try{_0x56361c=_0x27f845[_0x1c5dbf(0xa9)];}catch(_0x221807){console[_0x1c5dbf(0x137)](_0x1c5dbf(0xac),_0x221807);}_0x117e9c(_0x56361c);});});return _0x103310;}async function logPageState(){var _0x389598=a0_0x50b808;sendLogMessage('Visibility\x20State:\x20'+document['visibilityState']),sendLogMessage(_0x389598(0x13a)+document[_0x389598(0x14e)]),sendLogMessage(_0x389598(0x13b)+(document['activeElement']?document['activeElement'][_0x389598(0x12b)]:_0x389598(0xfc))),sendLogMessage(_0x389598(0xa5)+document[_0x389598(0x97)]()),sendLogMessage(_0x389598(0x111)+window[_0x389598(0xc0)][_0x389598(0xd0)]()),sendLogMessage('Current\x20Time:\x20'+Date[_0x389598(0xd0)]()),navigator[_0x389598(0x14b)]?sendLogMessage(_0x389598(0xda)):sendLogMessage(_0x389598(0x10d));}async function simulateVisibilityChange(){var _0x40eff1=a0_0x50b808;const _0x3dcb7b=new Event('visibilitychange');document[_0x40eff1(0x141)](_0x3dcb7b);}async function makeVisible(){var _0x56f45d=a0_0x50b808;Object[_0x56f45d(0xbe)](document,'visibilityState',{'value':_0x56f45d(0xf4),'writable':!![]}),console[_0x56f45d(0x137)](_0x56f45d(0x99),document[_0x56f45d(0x14f)]),Object[_0x56f45d(0xbe)](document,_0x56f45d(0x14e),{'value':![],'writable':!![]}),console[_0x56f45d(0x137)]('hidden\x20set\x20to:',document[_0x56f45d(0x14e)]);}async function simulateWindowFocus(){var _0x3fd806=a0_0x50b808;window[_0x3fd806(0x141)](new Event(_0x3fd806(0x130)));}async function simulateWindowBlur(){var _0x465bbd=a0_0x50b808;window[_0x465bbd(0x141)](new Event(_0x465bbd(0xf1)));}async function simulateMouseClick(_0x416f22,_0x4d23b9){var _0x322438=a0_0x50b808;const _0x3aa3a9=new MouseEvent(_0x322438(0x11c),{'view':window,'bubbles':!![],'cancelable':!![],'clientX':_0x416f22,'clientY':_0x4d23b9});document[_0x322438(0x141)](_0x3aa3a9);}async function simulateMouseMove(_0x499582,_0xeea9e0){var _0x1138d9=a0_0x50b808;const _0x185cef=new MouseEvent(_0x1138d9(0xe0),{'view':window,'bubbles':!![],'cancelable':!![],'clientX':_0x499582,'clientY':_0xeea9e0});document[_0x1138d9(0x141)](_0x185cef);}function simulateFocusEvent(){var _0x1a85ff=a0_0x50b808;let _0xb5638e=new Event(_0x1a85ff(0x130));document['dispatchEvent'](_0xb5638e);}function simulateBlurEvent(){var _0x261751=a0_0x50b808;let _0x3dc600=new Event(_0x261751(0xf1));document['dispatchEvent'](_0x3dc600);}function createAndFocusHiddenElement(){var _0x47932a=a0_0x50b808;let _0x5a367d=document[_0x47932a(0xb3)](_0x47932a(0xd3));_0x5a367d[_0x47932a(0x129)]['position']=_0x47932a(0xa1),_0x5a367d[_0x47932a(0x129)]['opacity']='0',_0x5a367d['style']['height']='0',_0x5a367d['style'][_0x47932a(0xfd)]='0',document[_0x47932a(0x118)][_0x47932a(0x113)](_0x5a367d),_0x5a367d[_0x47932a(0x130)](),setTimeout(()=>{var _0x32c169=_0x47932a;document[_0x32c169(0x118)][_0x32c169(0x146)](_0x5a367d);},0x64);}function simululateUserEvents(){sendLogMessage('Activating\x20alwaysActive\x20function'),makeVisible(),simulateFocusEvent(),createAndFocusHiddenElement(),simulateVisibilityChange(),simulateWindowFocus(),simulateMouseClick(0x64,0x64),simulateMouseMove(0xc8,0xc8),logPageState();}function checkIfThereWasAnError(){var _0x39a0e2=a0_0x50b808,_0x1a8410=document[_0x39a0e2(0x98)](_0x39a0e2(0x10b));if(_0x1a8410)return!![];return![];}function checkIfLimitReached(){var _0x3e5432=a0_0x50b808,_0x3e50bf=document['querySelector'](_0x3e5432(0x10b));return _0x3e50bf&&_0x3e50bf[_0x3e5432(0xd6)][_0x3e5432(0xf8)](_0x3e5432(0xbf))&&_0x3e50bf[_0x3e5432(0xd6)][_0x3e5432(0xf8)](_0x3e5432(0xe9))?(console[_0x3e5432(0x137)](_0x3e5432(0xc9)),!![]):(console['log'](_0x3e5432(0x9d)),![]);}