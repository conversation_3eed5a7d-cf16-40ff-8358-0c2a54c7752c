/* Reset some default styling */
body, html, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block;
}

body {
    line-height: 1;
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    color: #333;
}

#filters {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Label and input styling */
label {
    display: block;
    font-size: 14px;
    color: #555;
    margin-bottom: 10px; /* Provide space between label and input */
}

input[type=number] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box; /* Includes padding in width calculation */
}

/* Button styling consistent with the theme */
button {
    padding: 10px 20px;
    background-color: #2883a7;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background-color: #1f6a8d;
}



/* Responsive design for filter groups */
@media (min-width: 600px) {
    .filter-group label {
        flex-basis: 20%; /* Label takes up 40% of the filter group */
    }

    .filter-group input {
        flex-basis: 80%; /* Input takes up 60% of the filter group */
    }
}

#items-list {
    max-width: 800px;
    margin: auto;
    margin-top: 50px;
}

.item {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    opacity: 0;
    animation: fadeIn 0.5s forwards;
    animation-delay: 0.2s;
}

.item-image {
    max-width: 100px;
    border-radius: 5px;
    margin-bottom: 10px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}


/* ... (previous CSS styles) ... */

.filter-group {
    margin-bottom: 20px;
}

#results {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 20px; /* Creates a space between the button and the span */
}

/* If you want the inputs and labels to be side-by-side */
@media (min-width: 600px) {
    .filter-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filter-group label {
        margin-bottom: 0;
    }
}

.page-title {
    text-align: center;
    color: #333;
    font-size: 2em;
    margin-bottom: 40px;
    font-family: Arial, sans-serif;
}


/* ... (your existing styles) ... */

.item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
    transition: box-shadow 0.3s ease-in-out;
}

.item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.item img {
    max-width: 150px;
    border-right: 1px solid #eee;
}

.item-info {
    padding: 20px;
}

.displayed-titles {
    font-size: 1.2em;
    margin-bottom: 10px;
}

.item p {
    margin-bottom: 5px;
}


#clear_items {
    background-color: red;
    color: white;
    transition: background-color 0.3s ease-in-out;
    position: relative;
}

#clear_items:hover {
    background-color: darkred;
}


.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

button:disabled {
    background-color: grey;
    cursor: not-allowed;
}

#export_results {
    background-color: green; /* Change the background color to green */
    color: white;
    transition: background-color 0.3s ease-in-out;
}

#export_results:hover {
    background-color: darkgreen; /* Change the background color to darkgreen */
}


.total-sold-table {
    max-width: 300px; /* Keep the table compact */
    border-collapse: collapse;
    font-size: 12px; /* Maintain small font size */

    /* add space above and below and left and right of the table */
    margin: 20px;
    
}
.total-sold-table, .total-sold-table th, .total-sold-table td {
    border: 1px solid #ccc; /* Subtle border color */
    text-align: center; /* Centered text for better alignment */
    padding: 2px; /* Compact padding */
}
.total-sold-table th {
    background-color: #4CAF50; /* Green header background */
    color: white; /* White text for header */
}
.total-sold-table tr:nth-child(odd) {
    background-color: #f2f2f2; /* Light grey for odd rows */
}
.total-sold-table tr:nth-child(even) {
    background-color: #ffffff; /* White for even rows */
}

.import_export_buttons_container {
    justify-content: space-between;
    margin-bottom: 20px;
}



.custom-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.custom-modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
}

.custom-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.custom-modal-close:hover,
.custom-modal-close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

#import_modal_textarea {
    width: 95%;
    height: 200px;
    margin-bottom: 20px;
}
