<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>eBay Arbitrage Researcher Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎯 Arbitrage Researcher Pro</h1>
            <div class="version">v1.0.0</div>
        </header>

        <!-- Configuration Section -->
        <section class="config-section">
            <h3>📊 Search Configuration</h3>
            
            <div class="config-grid">
                <div class="config-item">
                    <label for="minSales">Min Sales (30 days):</label>
                    <input type="number" id="minSales" value="3" min="1" max="50">
                </div>
                
                <div class="config-item">
                    <label for="daysBack">Days to analyze:</label>
                    <input type="number" id="daysBack" value="30" min="7" max="90">
                </div>
                
                <div class="config-item">
                    <label for="maxPages">Max pages to scan:</label>
                    <input type="number" id="maxPages" value="10" min="1" max="50">
                </div>
                
                <div class="config-item">
                    <label for="minProfit">Min profit ($):</label>
                    <input type="number" id="minProfit" value="10" min="1" max="1000">
                </div>
                
                <div class="config-item">
                    <label for="minMargin">Min margin (%):</label>
                    <input type="number" id="minMargin" value="15" min="5" max="100">
                </div>
                
                <div class="config-item">
                    <label for="maxRisk">Max risk score:</label>
                    <input type="number" id="maxRisk" value="70" min="0" max="100">
                </div>
            </div>

            <div class="price-range">
                <h4>💰 Price Range</h4>
                <div class="range-inputs">
                    <input type="number" id="minPrice" value="10" min="1" placeholder="Min $">
                    <span>to</span>
                    <input type="number" id="maxPrice" value="500" min="1" placeholder="Max $">
                </div>
            </div>
        </section>

        <!-- Control Section -->
        <section class="control-section">
            <button id="startAnalysis" class="btn-primary">
                🚀 Start Arbitrage Analysis
            </button>
            <button id="diagnosticMode" class="btn-diagnostic">
                ⚡ Rapid Test (1 day, 1 page)
            </button>
            <button id="testSelectors" class="btn-test">
                🧪 Test eBay Selectors
            </button>
            <button id="minimalTest" class="btn-minimal">
                🔬 Minimal Scraper Test
            </button>
            <button id="stopAnalysis" class="btn-secondary" disabled>
                ⏹️ Stop Analysis
            </button>
        </section>

        <!-- Progress Section -->
        <section class="progress-section" id="progressSection" style="display: none;">
            <div class="progress-header">
                <h3>📈 Analysis Progress</h3>
                <span id="progressPercent">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-status" id="progressStatus">Ready to start...</div>
            <div class="progress-stats" id="progressStats"></div>
        </section>

        <!-- Results Section -->
        <section class="results-section">
            <div class="results-header">
                <h3>💎 Opportunities Found</h3>
                <div class="results-controls">
                    <span id="opportunityCount" class="count-badge">0</span>
                    <button id="exportResults" class="btn-export" disabled>📊 Export CSV</button>
                    <button id="refreshResults" class="btn-refresh">🔄 Refresh</button>
                </div>
            </div>

            <!-- Summary Stats -->
            <div class="summary-stats" id="summaryStats" style="display: none;">
                <div class="stat-item">
                    <span class="stat-label">Total Profit:</span>
                    <span class="stat-value" id="totalProfit">$0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Margin:</span>
                    <span class="stat-value" id="avgMargin">0%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">High Value:</span>
                    <span class="stat-value" id="highValueCount">0</span>
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="filter-controls" id="filterControls" style="display: none;">
                <select id="sortBy">
                    <option value="profit">Sort by Profit</option>
                    <option value="margin">Sort by Margin</option>
                    <option value="roi">Sort by ROI</option>
                    <option value="grade">Sort by Grade</option>
                </select>
                <select id="gradeFilter">
                    <option value="all">All Grades</option>
                    <option value="A+">A+ Only</option>
                    <option value="A">A and above</option>
                    <option value="B">B and above</option>
                </select>
            </div>

            <!-- Results List -->
            <div class="results-list" id="resultsList">
                <div class="no-results">
                    <p>🔍 No opportunities found yet.</p>
                    <p>Click "Start Arbitrage Analysis" to begin scanning for profitable products.</p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-links">
                <a href="#" id="helpLink">❓ Help</a>
                <a href="#" id="settingsLink">⚙️ Settings</a>
                <a href="#" id="aboutLink">ℹ️ About</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
