function a0_0xec14(_0x54a4e1,_0x4a7506){var _0x2e7b78=a0_0x2e7b();return a0_0xec14=function(_0xec14c4,_0x59d5e9){_0xec14c4=_0xec14c4-0xd9;var _0x468dd9=_0x2e7b78[_0xec14c4];return _0x468dd9;},a0_0xec14(_0x54a4e1,_0x4a7506);}var a0_0x3704b9=a0_0xec14;(function(_0x5692f5,_0x5b9e44){var _0x1b59e5=a0_0xec14,_0x170137=_0x5692f5();while(!![]){try{var _0x332086=-parseInt(_0x1b59e5(0xfa))/0x1*(-parseInt(_0x1b59e5(0xf4))/0x2)+-parseInt(_0x1b59e5(0xfc))/0x3*(-parseInt(_0x1b59e5(0xf2))/0x4)+parseInt(_0x1b59e5(0xe7))/0x5+-parseInt(_0x1b59e5(0xd9))/0x6*(parseInt(_0x1b59e5(0xf0))/0x7)+parseInt(_0x1b59e5(0xed))/0x8+-parseInt(_0x1b59e5(0xe1))/0x9+parseInt(_0x1b59e5(0xe6))/0xa;if(_0x332086===_0x5b9e44)break;else _0x170137['push'](_0x170137['shift']());}catch(_0x1ed3cb){_0x170137['push'](_0x170137['shift']());}}}(a0_0x2e7b,0x801ac),console[a0_0x3704b9(0xf9)]('ebay\x20contact\x20content.js\x20loaded'));var appendElement,chatBoxElement,debounceTimer;document[a0_0x3704b9(0xe8)](a0_0x3704b9(0xf3),async function(){var _0x5dfcf1=a0_0x3704b9;console['log'](_0x5dfcf1(0xfb)),await onPageLoadAndStable(),console[_0x5dfcf1(0xf9)](_0x5dfcf1(0xf7));var {quick_chat_switch:_0xab91b6}=await chrome[_0x5dfcf1(0xe4)][_0x5dfcf1(0xdf)][_0x5dfcf1(0xf1)]('quick_chat_switch');if(!_0xab91b6){console[_0x5dfcf1(0xf9)](_0x5dfcf1(0xea));return;}var _0x96bf8b=window[_0x5dfcf1(0xf5)][_0x5dfcf1(0xe9)];_0x96bf8b[_0x5dfcf1(0xda)](_0x5dfcf1(0xee))&&(console['log']('ebay\x20contact\x20content.js\x20loaded'),chatBoxElement=document['querySelector']('#msg_cnt_cnt'),appendElement=chatBoxElement[_0x5dfcf1(0xe2)]['parentElement'][_0x5dfcf1(0xe5)]),_0x96bf8b['includes']('/viewMessage?')&&(console[_0x5dfcf1(0xf9)]('ebay\x20viewMessage\x20content.js\x20loaded'),chatBoxElement=document['querySelector'](_0x5dfcf1(0xe3)),appendElement=chatBoxElement[_0x5dfcf1(0xe2)][_0x5dfcf1(0xe2)]['parentElement'][_0x5dfcf1(0xe2)][_0x5dfcf1(0xe2)]),_0x96bf8b[_0x5dfcf1(0xda)](_0x5dfcf1(0xf6))&&(console[_0x5dfcf1(0xf9)](_0x5dfcf1(0xec)),chatBoxElement=document[_0x5dfcf1(0xe0)](_0x5dfcf1(0xf8)),appendElement=chatBoxElement[_0x5dfcf1(0xe2)][_0x5dfcf1(0xe2)][_0x5dfcf1(0xe2)][_0x5dfcf1(0xe5)]),await initQuickChatContentButtons(chatBoxElement,appendElement),chatBoxElement[_0x5dfcf1(0xe8)](_0x5dfcf1(0xeb),debounce(onChatBoxChange,0x12c));});function debounce(_0x23a1ec,_0xf9d0bf){return function(){var _0x4fee3d=a0_0xec14;const _0x89cbf5=this,_0x5a2c8e=arguments;clearTimeout(debounceTimer),debounceTimer=setTimeout(()=>_0x23a1ec[_0x4fee3d(0xdc)](_0x89cbf5,_0x5a2c8e),_0xf9d0bf);};}async function onChatBoxChange(_0x46bfbf){var _0x4b12da=a0_0x3704b9;console[_0x4b12da(0xf9)](_0x4b12da(0xdb));var _0x2bce7d=chatBoxElement[_0x4b12da(0xef)];const _0x537070=/{{(.*?)}}/g;let _0x47268e;while((_0x47268e=_0x537070['exec'](_0x2bce7d))!==null){let _0x256ebb=_0x47268e[0x0],_0x12a842=_0x47268e[0x1],_0x437603=prompt('Please\x20enter\x20a\x20value\x20for\x20'+_0x12a842+':');if(_0x437603!==null)_0x2bce7d=_0x2bce7d[_0x4b12da(0xdd)](_0x256ebb,_0x437603),writeInChat(_0x2bce7d,chatBoxElement);else{alert(_0x4b12da(0xde));break;}}}function a0_0x2e7b(){var _0x5f4d3d=['href','quick_chat_switch\x20is\x20off','input','ebay\x20cnt/ReplyToMessages\x20content.js\x20loaded','7129336plGSXn','/contact/','value','21kpysUc','get','17324eyOnAC','DOMContentLoaded','1140026KkURdt','location','/cnt/ReplyToMessages','ebay\x20contact\x20content.js\x20DOMContentLoaded\x20onPageLoadAndStable','#app-page-form-message-0','log','1fwpzvx','ebay\x20contact\x20content.js\x20DOMContentLoaded','69iszCWf','1976592iiwlub','includes','Chat\x20box\x20changed','apply','replace','You\x20cancelled\x20the\x20input.\x20Placeholder\x20will\x20remain\x20unchanged.','local','querySelector','9285210doIlvb','parentElement','#imageupload__sendmessage--textbox','storage','nextElementSibling','8534750HDKMEp','652170meQBrr','addEventListener'];a0_0x2e7b=function(){return _0x5f4d3d;};return a0_0x2e7b();}