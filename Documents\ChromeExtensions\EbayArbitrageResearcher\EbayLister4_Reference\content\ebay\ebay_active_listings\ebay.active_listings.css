/* Base styles for all buttons */
.end-item-button, .opti-relist-button, .standard-relist-button, .clone-list-button, .smart-relist-button, .update-listing-button {
    border: none;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

/* End Item Button */
.end-item-button {
    background-color: #e44d4d;
}

.end-item-button:disabled {
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.end-item-button:hover {
    background-color: #c0392b;
    transform: scale(1.1);
}

/* Opti Relist Button */
.opti-relist-button, .smart-relist-button {
    background-color: #2ecc71;
}

.opti-relist-button:disabled, .smart-relist-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.opti-relist-button:hover, .smart-relist-button:hover {
    background-color: #27ae60;
    transform: scale(1.1);
}

/* Standard Relist Button */
.standard-relist-button, .update-listing-button {
    background-color: lightgrey;
    color: #000;
}

.standard-relist-button:disabled, .update-listing-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.standard-relist-button:hover, .update-listing-button:hover{
    background-color: lightgrey;
    transform: scale(1.1);
}



.clone-list-button {
    background-color: #3498db;
}

.clone-list-button:hover {
    background-color: #2980b9;
    transform: scale(1.1);
}

.clone-list-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}



/* Add a span element to the button for the loader */


/* Add loader styles */
.loader {
    border: 2px solid grey; /* Changed border color to be visible against the button */
    border-top: 2px solid white; /* Made border-top white for a spinning effect */
    border-radius: 50%;
    width: 14px;
    height: 14px;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}



/* Style for the info-box */
.info-box {
    max-width: 300px;
    background-color: #f0f0f0;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-size: 0.9em;
}

.info-box p {
    margin: 5px 0;
}

/* Adding a bit of style to the URL for aesthetics */
.info-box a {
    color: #3498db;
    text-decoration: none;
}

.info-box a:hover {
    text-decoration: underline;
}


/* Style for the Low Performing Nodes */
.low-performing {
    /* make css over ride the default css */


    border: 2px solid #ff6961; /* Subtle red border */
    background-color: rgba(255, 105, 97, 0.1); /* Very light red background */
    padding: 2px; /* Minimal padding to avoid layout distortion */
    margin: 2px 0; /* Small margin to maintain layout structure */
    box-shadow: none; /* Remove box shadow to prevent layout shift */
    transition: border-color 0.3s ease; /* Smooth transition for border color */
}

.low-performing:hover {
    border-color: #ff4747; /* Slightly darker border on hover for interactivity */
}






.processed-node {
    background-color: #dff0d8; /* Light green background for successful processing */
    border-left: 4px solid #3c763d; /* Dark green left border for emphasis */
    color: #3c763d; /* Dark green text color for contrast */
}

.processed-node::after {
    content: '✔ Processed'; /* Adds a checkmark and text */
    color: #3c763d; /* Dark green text color */
    font-weight: bold; /* Bold text for emphasis */
    margin-left: 10px; /* Space before the text */
    font-size: 0.9em; /* Slightly smaller font size than the node text */
}


/* Base class for the smart relist feature to avoid conflicts */
.smart-relist-ui .bulk-smart-relist-button-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    color: #333333;
}

/* Style for buttons inside the smart relist feature */
.smart-relist-ui button.bulk-smart-relist-button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
    width: 100%;
}

/* when its disabled  */
.smart-relist-ui button.bulk-smart-relist-button:disabled {
    background-color: #ccc;
    cursor: default;
}

.smart-relist-ui button.bulk-smart-relist-button:hover {
    background-color: #45a049;
}

/* Style for input fields and selects inside the smart relist feature */
.smart-relist-ui .div-input-group input[type="number"], 
.smart-relist-ui .div-input-group input[type="checkbox"], 
.smart-relist-ui .div-input-group select {
    width: 100%;
    padding: 8px;
    margin: 5px 0 15px 0;
    display: inline-block;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
}

/* Style for labels inside the smart relist feature */
.smart-relist-ui .div-input-group label {
    display: block;
    font-size: 12px;
    color: #666666;
    margin-top: -10px;
    margin-bottom: 5px;
}

/* Custom loader style specific to the smart relist feature */
.smart-relist-ui .custom-loader {
    border: 3px solid #f3f3f3; 
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fine-tuning checkbox appearance in the smart relist feature */
.smart-relist-ui .div-input-group input[type="checkbox"] {
    margin-right: 5px;
    vertical-align: middle;
}

/* Responsive layout adjustments for the smart relist feature */
@media (max-width: 600px) {
    .smart-relist-ui .bulk-smart-relist-button-container {
        padding: 10px;
        margin: 10px;
    }

    .smart-relist-ui button, 
    .smart-relist-ui input[type="number"], 
    .smart-relist-ui select {
        padding: 6px;
        font-size: 12px;
    }

    .smart-relist-ui label {
        font-size: 11px;
    }
}


.smart-relist-intro {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 10px 20px;
    margin-bottom: 20px;
    font-family: 'Arial', sans-serif;
    color: #333;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.smart-relist-explanation {
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.stop-button {
    background-color: #e44d4d; /* Red background */
    color: #fff; /* White text */
    border: none;
    border-radius: 4px; /* Rounded corners */
    cursor: pointer;
    font-size: 14px;
    display: inline-block; /* Make the button wrap around the text */
    text-align: center; /* Center-align text and icon */
    vertical-align: middle; /* Align icon and text vertically */

    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
}


.stop-button:hover {
    background-color: #c0392b;
}

.stop-button:disabled {
    background-color: grey;
    color: darkgrey;
    cursor: default;
}

.stop-button:disabled:hover {
    background-color: grey;
    color: darkgrey;
    cursor: default;
}




.action-button-container {
    display: flex;
}