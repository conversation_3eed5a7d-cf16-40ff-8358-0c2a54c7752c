const a0_0x3bf4d1=a0_0x394c;(function(_0x3f864c,_0x5229e1){const _0x43d17f=a0_0x394c,_0x345f93=_0x3f864c();while(!![]){try{const _0x14c9cc=parseInt(_0x43d17f(0x1d4))/0x1+parseInt(_0x43d17f(0x1b3))/0x2*(parseInt(_0x43d17f(0x22e))/0x3)+parseInt(_0x43d17f(0x1e5))/0x4*(parseInt(_0x43d17f(0x224))/0x5)+-parseInt(_0x43d17f(0x228))/0x6*(parseInt(_0x43d17f(0x250))/0x7)+parseInt(_0x43d17f(0x1f5))/0x8+-parseInt(_0x43d17f(0x1c1))/0x9*(-parseInt(_0x43d17f(0x1f7))/0xa)+-parseInt(_0x43d17f(0x219))/0xb*(parseInt(_0x43d17f(0x1d7))/0xc);if(_0x14c9cc===_0x5229e1)break;else _0x345f93['push'](_0x345f93['shift']());}catch(_0x3654bd){_0x345f93['push'](_0x345f93['shift']());}}}(a0_0xaa97,0xdc21f));let showOnlyUnprocessed=![];const showUnprocessedOnlyCheckbox=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1e7));function a0_0x394c(_0x53fed6,_0x243766){const _0xaa9709=a0_0xaa97();return a0_0x394c=function(_0x394cc8,_0x12388c){_0x394cc8=_0x394cc8-0x1af;let _0x2dd1e0=_0xaa9709[_0x394cc8];return _0x2dd1e0;},a0_0x394c(_0x53fed6,_0x243766);}showUnprocessedOnlyCheckbox[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x1bb),_0x237383=>{const _0x5af606=a0_0x3bf4d1;showOnlyUnprocessed=_0x237383[_0x5af606(0x247)]['checked'],renderTable();});const actionsConfig={'gspr':{'label':a0_0x3bf4d1(0x241),'dataColumns':['Manufacturer\x20Name',a0_0x3bf4d1(0x1d0),a0_0x3bf4d1(0x214),a0_0x3bf4d1(0x212),'Manufacturer\x20Country',a0_0x3bf4d1(0x244),a0_0x3bf4d1(0x1c6),a0_0x3bf4d1(0x21a),a0_0x3bf4d1(0x20c),a0_0x3bf4d1(0x1ef),'Responsible\x20Person\x201\x20Type',a0_0x3bf4d1(0x1b9),a0_0x3bf4d1(0x1c9),'Responsible\x20Person\x201\x20City',a0_0x3bf4d1(0x223),a0_0x3bf4d1(0x242),a0_0x3bf4d1(0x21d),'Responsible\x20Person\x201\x20Phone',a0_0x3bf4d1(0x1e1),a0_0x3bf4d1(0x1dc),a0_0x3bf4d1(0x235)],'scannedColumn':a0_0x3bf4d1(0x22b)},'chineseSeller':{'label':a0_0x3bf4d1(0x1f4),'dataColumns':[a0_0x3bf4d1(0x208)],'scannedColumn':'scannedChinese'}};let debugMode=![],originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,concurrency=0x5,isProcessing=![],currentHeaders=[],itemIdToRowElement={},itemIdToRowIndex={};const showRowNumber=!![];let currentLineIndex=0x0;const csvUpload=document['getElementById'](a0_0x3bf4d1(0x201)),headerSelectionContainer=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x238)),headerCheckboxes=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1c2)),confirmHeadersBtn=document[a0_0x3bf4d1(0x1cc)]('confirmHeadersBtn'),mainSection=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1c3)),dataTable=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x22c)),downloadCsvBtn=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x232)),concurrencyInput=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1d9)),processedCountEl=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1c7)),totalCountEl=document['getElementById']('totalCount'),statusMessageEl=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x248)),restoreFromStorageBtn=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x253)),reUploadCsvBtn=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x239)),reUploadInput=document['getElementById'](a0_0x3bf4d1(0x23c)),clearDataBtn=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x24f)),runActionsBtn=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1ce)),actionCheckboxes=document[a0_0x3bf4d1(0x1cc)](a0_0x3bf4d1(0x1b4));document[a0_0x3bf4d1(0x1fe)]('DOMContentLoaded',onDOMContentLoaded),csvUpload['addEventListener'](a0_0x3bf4d1(0x1bb),handleFileUpload),confirmHeadersBtn['addEventListener'](a0_0x3bf4d1(0x245),handleConfirmHeaders),downloadCsvBtn['addEventListener']('click',handleDownloadCSV),restoreFromStorageBtn[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x245),handleRestoreFromStorage),reUploadCsvBtn[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x245),()=>reUploadInput[a0_0x3bf4d1(0x245)]()),reUploadInput[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x1bb),handleReUploadCSV),clearDataBtn[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x245),handleClearData),runActionsBtn['addEventListener'](a0_0x3bf4d1(0x245),handleRunActions),concurrencyInput[a0_0x3bf4d1(0x1fe)](a0_0x3bf4d1(0x1bb),_0x3b047a=>{const _0x3763ac=a0_0x3bf4d1;concurrency=parseInt(_0x3b047a[_0x3763ac(0x247)]['value'],0xa)||0x5;});async function getGSPR(_0x140f86){const _0x3f93d5=a0_0x3bf4d1;await new Promise(_0x81682=>setTimeout(_0x81682,0x64));let _0x39c238;try{_0x39c238=atob(_0x140f86);}catch(_0x64e7ee){throw new Error('Failed\x20to\x20decode\x20customLabel:\x20'+_0x140f86);}const {domain:_0x3fd96d}=await chrome[_0x3f93d5(0x1e6)][_0x3f93d5(0x252)]['get'](_0x3f93d5(0x233))||{};if(!_0x3fd96d)throw new Error(_0x3f93d5(0x21f));const _0x328957=_0x3f93d5(0x20b)+_0x3fd96d+_0x3f93d5(0x1ee)+_0x39c238+'/?th=1&psc=1';console[_0x3f93d5(0x202)](_0x3f93d5(0x210)+_0x328957);let _0x504cfe;try{_0x504cfe=await new Promise(_0x38e9cf=>{const _0x54f7ae=_0x3f93d5;chrome[_0x54f7ae(0x1af)]['sendMessage']({'type':_0x54f7ae(0x220),'url':_0x328957},function(_0x23d35b){_0x38e9cf(_0x23d35b);});});}catch(_0x2dd2f7){throw new Error(_0x3f93d5(0x1d1)+_0x2dd2f7[_0x3f93d5(0x1df)]);}if(!_0x504cfe||!_0x504cfe['gspr'])throw new Error(_0x3f93d5(0x1b5)+_0x140f86);const _0x336adf=_0x504cfe[_0x3f93d5(0x1b7)],_0xa5b18b=_0x336adf['manufacturerInfo']||{},_0x597259=_0x336adf[_0x3f93d5(0x1e4)]||{};var _0x450e91=_0x336adf[_0x3f93d5(0x1cd)],_0x3be2b4=_0x336adf[_0x3f93d5(0x23a)];return{'Manufacturer\x20Name':_0xa5b18b['name']??null,'Manufacturer\x20AddressLine1':_0xa5b18b[_0x3f93d5(0x215)]??null,'Manufacturer\x20AddressLine2':_0xa5b18b[_0x3f93d5(0x24c)]??null,'Manufacturer\x20City':_0xa5b18b[_0x3f93d5(0x21e)]??null,'Manufacturer\x20Country':_0xa5b18b['country']??null,'Manufacturer\x20PostalCode':_0xa5b18b[_0x3f93d5(0x240)]??null,'Manufacturer\x20StateOrProvince':_0xa5b18b['stateOrProvince']??null,'Manufacturer\x20Phone':_0xa5b18b[_0x3f93d5(0x213)]??null,'Manufacturer\x20Email':_0xa5b18b[_0x3f93d5(0x1de)]??null,'Responsible\x20Person\x201':_0x597259[_0x3f93d5(0x23d)]??null,'Responsible\x20Person\x201\x20Type':null,'Responsible\x20Person\x201\x20AddressLine1':_0x597259[_0x3f93d5(0x215)]??null,'Responsible\x20Person\x201\x20AddressLine2':_0x597259[_0x3f93d5(0x24c)]??null,'Responsible\x20Person\x201\x20City':_0x597259[_0x3f93d5(0x21e)]??null,'Responsible\x20Person\x201\x20Country':_0x597259[_0x3f93d5(0x1b0)]??null,'Responsible\x20Person\x201\x20PostalCode':_0x597259[_0x3f93d5(0x240)]??null,'Responsible\x20Person\x201\x20StateOrProvince':_0x597259[_0x3f93d5(0x221)]??null,'Responsible\x20Person\x201\x20Phone':_0x597259['phoneNumber']??null,'Responsible\x20Person\x201\x20Email':_0x597259[_0x3f93d5(0x1de)]??null,'Manufacturer\x20Info\x20Text':_0x450e91??null,'Responsible\x20Person\x20EU\x20Text':_0x3be2b4??null};}async function checkIfSellerChinese(_0x50c89a){const _0x2e1b9c=a0_0x3bf4d1;await new Promise(_0x519406=>setTimeout(_0x519406,0x32));const _0x5362ab=Math[_0x2e1b9c(0x1e9)]()>0.5?_0x2e1b9c(0x1bc):_0x2e1b9c(0x24d);return{'Chinese\x20Seller':_0x5362ab};}async function onDOMContentLoaded(){const _0x31bc9c=a0_0x3bf4d1;await restoreState();if(originalData[_0x31bc9c(0x1fb)]>0x0&&selectedHeaders[_0x31bc9c(0x1fb)]>0x0){initActionsUI(),renderTable(),mainSection['style'][_0x31bc9c(0x216)]=_0x31bc9c(0x22f);const _0x35d24f=document[_0x31bc9c(0x1cc)](_0x31bc9c(0x1c0));_0x35d24f&&(_0x35d24f[_0x31bc9c(0x1d3)]=currentLineIndex);}const _0x15ea9f=document[_0x31bc9c(0x1cc)](_0x31bc9c(0x1fc));_0x15ea9f['addEventListener'](_0x31bc9c(0x1bb),_0x5aadb4=>{const _0x2b34e0=_0x31bc9c;debugMode=_0x5aadb4[_0x2b34e0(0x247)]['checked'],console[_0x2b34e0(0x202)](_0x2b34e0(0x229),debugMode?'ON':_0x2b34e0(0x20d));});}async function handleFileUpload(_0x563e76){const _0xcdc751=a0_0x3bf4d1,_0x326a45=_0x563e76['target'][_0xcdc751(0x1da)][0x0];if(!_0x326a45)return;Papa[_0xcdc751(0x23f)](_0x326a45,{'header':!![],'skipEmptyLines':!![],'dynamicTyping':![],'complete':function(_0x5bc165){const _0x2f0e1c=_0xcdc751,_0x4baa5c=_0x5bc165[_0x2f0e1c(0x211)];originalData=_0x4baa5c,selectedHeaders=Object[_0x2f0e1c(0x207)](originalData[0x0]||{}),appliedActions={};if(originalData[_0x2f0e1c(0x1fb)]===0x0){alert(_0x2f0e1c(0x249));return;}showHeaderSelection(),saveState();},'error':function(_0x24fb72){const _0x138f12=_0xcdc751;console[_0x138f12(0x1c4)](_0x138f12(0x1e3),_0x24fb72),alert(_0x138f12(0x204));}});}function showHeaderSelection(){const _0x230724=a0_0x3bf4d1;headerSelectionContainer[_0x230724(0x1b1)][_0x230724(0x216)]=_0x230724(0x22f),headerCheckboxes[_0x230724(0x222)]='';const _0x4602b3=Object['keys'](originalData[0x0]);_0x4602b3['forEach'](_0x5d6ca2=>{const _0xc07845=_0x230724,_0x3936f0=document[_0xc07845(0x1eb)](_0xc07845(0x1be));_0x3936f0['innerHTML']=_0xc07845(0x1e2)+_0x5d6ca2+'\x22\x20checked>\x20'+_0x5d6ca2+'</label>',headerCheckboxes[_0xc07845(0x1f9)](_0x3936f0);});}async function handleConfirmHeaders(){const _0x5eff89=a0_0x3bf4d1,_0x1beecb=headerCheckboxes[_0x5eff89(0x218)](_0x5eff89(0x209));selectedHeaders=Array[_0x5eff89(0x231)](_0x1beecb)[_0x5eff89(0x1ec)](_0x1c4c73=>_0x1c4c73['checked'])['map'](_0x51bafe=>_0x51bafe[_0x5eff89(0x1d3)]);if(selectedHeaders[_0x5eff89(0x1fb)]===0x0){alert('Please\x20select\x20at\x20least\x20one\x20header.');return;}originalData=originalData[_0x5eff89(0x1e8)](_0xa4599e=>{const _0x42ef9e={};for(const _0x97c9e1 of selectedHeaders){_0x42ef9e[_0x97c9e1]=_0xa4599e[_0x97c9e1];}return _0x42ef9e;}),headerSelectionContainer[_0x5eff89(0x1b1)][_0x5eff89(0x216)]=_0x5eff89(0x206),mainSection['style']['display']=_0x5eff89(0x22f),initActionsUI(),renderTable(),await saveState();}function initActionsUI(){const _0x1ec6a2=a0_0x3bf4d1;actionCheckboxes[_0x1ec6a2(0x222)]='';for(const [_0x2fa7cb,_0x51e437]of Object['entries'](actionsConfig)){const _0xe58d15=document[_0x1ec6a2(0x1eb)](_0x1ec6a2(0x1be));_0xe58d15['innerHTML']=_0x1ec6a2(0x1ba)+_0x2fa7cb+_0x1ec6a2(0x1e0)+_0x51e437['label']+'</label>',actionCheckboxes['appendChild'](_0xe58d15);}}function isRowProcessed(_0x5c0066){const _0x3e7372=a0_0x3bf4d1,_0x17a107=Object[_0x3e7372(0x207)](appliedActions)[_0x3e7372(0x1ec)](_0x5ddd9a=>appliedActions[_0x5ddd9a]);if(_0x17a107[_0x3e7372(0x1fb)]===0x0)return![];return _0x17a107[_0x3e7372(0x20e)](_0x2d0f25=>{const _0x4830a5=_0x3e7372,_0x3c265e=actionsConfig[_0x2d0f25][_0x4830a5(0x23e)];return _0x5c0066[_0x3c265e]===!![];});}function renderTable(){const _0x528240=a0_0x3bf4d1;dataTable[_0x528240(0x222)]='';let _0x1f0200=[];showRowNumber&&_0x1f0200[_0x528240(0x217)](_0x528240(0x1f2));_0x1f0200['push'](...selectedHeaders);for(const [_0x42830e,_0x1d516a]of Object[_0x528240(0x23b)](appliedActions)){if(_0x1d516a){const _0x1bd7b9=actionsConfig[_0x42830e];for(const _0x3d7943 of _0x1bd7b9[_0x528240(0x1fa)]){if(!_0x1f0200[_0x528240(0x24b)](_0x3d7943))_0x1f0200[_0x528240(0x217)](_0x3d7943);}!_0x1f0200[_0x528240(0x24b)](_0x1bd7b9[_0x528240(0x23e)])&&_0x1f0200['push'](_0x1bd7b9['scannedColumn']);}}currentHeaders=_0x1f0200;const _0x52f644=document[_0x528240(0x1eb)](_0x528240(0x1f1)),_0x994bce=document[_0x528240(0x1eb)]('tr');_0x1f0200[_0x528240(0x1b8)](_0x2a2b99=>{const _0xf8c1bf=_0x528240,_0x5d2ee2=document[_0xf8c1bf(0x1eb)]('th');_0x5d2ee2['textContent']=_0x2a2b99,_0x2a2b99['startsWith'](_0xf8c1bf(0x1ff))&&_0x5d2ee2[_0xf8c1bf(0x24a)]['add'](_0xf8c1bf(0x1ca)),_0x994bce['appendChild'](_0x5d2ee2);}),_0x52f644[_0x528240(0x1f9)](_0x994bce),dataTable[_0x528240(0x1f9)](_0x52f644);const _0x24e75b=document[_0x528240(0x1eb)](_0x528240(0x237));itemIdToRowElement={},itemIdToRowIndex={};let _0x45ef19=originalData;showOnlyUnprocessed&&(_0x45ef19=_0x45ef19[_0x528240(0x1ec)](isRowUnprocessed)),_0x45ef19[_0x528240(0x1b8)]((_0x29fc9b,_0x1ca776)=>{const _0x366aba=_0x528240,_0x330362=document['createElement']('tr');currentHeaders[_0x366aba(0x1b8)]((_0x2b2ac1,_0x50fe11)=>{const _0xd8de4d=_0x366aba,_0x5256c0=document['createElement']('td');let _0x24d6ea='';_0x2b2ac1===_0xd8de4d(0x1f2)?_0x24d6ea=(_0x1ca776+0x1)[_0xd8de4d(0x1cb)]():(_0x24d6ea=_0x29fc9b[_0x2b2ac1]??'',_0x2b2ac1[_0xd8de4d(0x227)]('scanned')&&(_0x24d6ea=_0x24d6ea===!![]||_0x24d6ea===_0xd8de4d(0x1bc)?'✓':'')),_0x5256c0[_0xd8de4d(0x1ed)]=_0x24d6ea,_0x330362[_0xd8de4d(0x1f9)](_0x5256c0);}),isRowProcessed(_0x29fc9b)&&_0x330362[_0x366aba(0x24a)]['add'](_0x366aba(0x1ea)),_0x24e75b[_0x366aba(0x1f9)](_0x330362),_0x29fc9b[_0x366aba(0x1f6)]&&(itemIdToRowElement[_0x29fc9b[_0x366aba(0x1f6)]]=_0x330362,itemIdToRowIndex[_0x29fc9b['ItemID']]=_0x1ca776);}),dataTable[_0x528240(0x1f9)](_0x24e75b),updateProgressUI();}function isRowUnprocessed(_0x31e9d0){const _0x16e75c=a0_0x3bf4d1,_0x587ffa=Object[_0x16e75c(0x207)](appliedActions)['filter'](_0x52b965=>appliedActions[_0x52b965]);if(_0x587ffa[_0x16e75c(0x1fb)]===0x0)return![];return!_0x587ffa[_0x16e75c(0x20e)](_0x209963=>{const _0x5269eb=_0x16e75c,_0xedf623=actionsConfig[_0x209963][_0x5269eb(0x23e)];return _0x31e9d0[_0xedf623]===!![]||_0x31e9d0[_0xedf623]===_0x5269eb(0x1bc);});}async function handleRestoreFromStorage(){const _0x5ffd90=a0_0x3bf4d1;await restoreState();if(originalData[_0x5ffd90(0x1fb)]===0x0){alert(_0x5ffd90(0x251));return;}selectedHeaders[_0x5ffd90(0x1fb)]===0x0?showHeaderSelection():(initActionsUI(),renderTable(),mainSection['style']['display']='block');}async function handleClearData(){const _0x3c98ca=a0_0x3bf4d1;originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,isProcessing=![],dataTable[_0x3c98ca(0x222)]='',mainSection['style'][_0x3c98ca(0x216)]=_0x3c98ca(0x206),headerSelectionContainer[_0x3c98ca(0x1b1)][_0x3c98ca(0x216)]='none',await chrome[_0x3c98ca(0x1e6)]['local'][_0x3c98ca(0x1f0)]([_0x3c98ca(0x234),_0x3c98ca(0x243),_0x3c98ca(0x1f3),_0x3c98ca(0x200)]),csvUpload[_0x3c98ca(0x1d3)]='',statusMessageEl['textContent']='',processedCountEl['textContent']='0',totalCountEl[_0x3c98ca(0x1ed)]='0',alert(_0x3c98ca(0x1b2));}function handleDownloadCSV(){const _0x149a6d=a0_0x3bf4d1,_0x2de196=generateCSV(),_0x4bce73=new Blob([_0x2de196],{'type':_0x149a6d(0x1d6)}),_0x2215b8=URL[_0x149a6d(0x1fd)](_0x4bce73),_0x4b797c=document[_0x149a6d(0x1eb)]('a');_0x4b797c[_0x149a6d(0x1d2)]=_0x2215b8,_0x4b797c['download']=_0x149a6d(0x1b6),_0x4b797c[_0x149a6d(0x245)](),URL[_0x149a6d(0x205)](_0x2215b8);}function generateCSV(){const _0x2ae810=a0_0x3bf4d1;let _0x1ceeba=[...selectedHeaders];for(const [_0x32b195,_0x534aa1]of Object[_0x2ae810(0x23b)](appliedActions)){if(_0x534aa1){const _0x327852=actionsConfig[_0x32b195];_0x327852[_0x2ae810(0x1fa)]['forEach'](_0x40a14a=>{const _0x185de8=_0x2ae810;if(!_0x1ceeba[_0x185de8(0x24b)](_0x40a14a))_0x1ceeba[_0x185de8(0x217)](_0x40a14a);}),!_0x1ceeba['includes'](_0x327852['scannedColumn'])&&_0x1ceeba[_0x2ae810(0x217)](_0x327852[_0x2ae810(0x23e)]);}}const _0x21d65f=originalData[_0x2ae810(0x1e8)](_0x1f6f80=>{const _0x4ef830=_0x2ae810,_0x26c162={..._0x1f6f80};return _0x1ceeba[_0x4ef830(0x1b8)](_0x57244e=>{const _0x4b4828=_0x4ef830;if(_0x57244e[_0x4b4828(0x227)](_0x4b4828(0x1ff))){const _0x16ac00=_0x26c162[_0x57244e];_0x26c162[_0x57244e]=_0x16ac00===!![]?'true':_0x4b4828(0x24d);}}),_0x26c162;}),_0x4f3319=Papa[_0x2ae810(0x1cf)]({'fields':_0x1ceeba,'data':_0x21d65f},{'quotes':!![],'delimiter':',','newline':'\x0a'});return _0x4f3319;}async function handleReUploadCSV(_0x5a62a3){const _0x4e2824=a0_0x3bf4d1,_0x4ceaa5=_0x5a62a3[_0x4e2824(0x247)][_0x4e2824(0x1da)][0x0];if(!_0x4ceaa5)return;Papa['parse'](_0x4ceaa5,{'header':!![],'skipEmptyLines':!![],'complete':async _0x2c58bb=>{const _0x5c0aa9=_0x4e2824,_0x5c3fb5=_0x2c58bb[_0x5c0aa9(0x211)];if(_0x5c3fb5['length']===0x0){alert('Uploaded\x20CSV\x20is\x20empty\x20or\x20invalid.');return;}originalData=_0x5c3fb5,originalData[_0x5c0aa9(0x1b8)](_0x1ec780=>{const _0x1d7707=_0x5c0aa9;for(const _0x29f842 in _0x1ec780){if(_0x29f842['startsWith']('scanned')){const _0x45b0f2=(_0x1ec780[_0x29f842]||'')[_0x1d7707(0x22d)]()['toLowerCase']();_0x1ec780[_0x29f842]=_0x45b0f2==='true';}}});const _0x1fb5d1=Object[_0x5c0aa9(0x207)](originalData[0x0]);console[_0x5c0aa9(0x202)]('All\x20headers\x20on\x20reupload:',_0x1fb5d1),appliedActions={};for(const [_0x5bb6f1,_0x5ece77]of Object[_0x5c0aa9(0x23b)](actionsConfig)){appliedActions[_0x5bb6f1]=_0x1fb5d1['includes'](_0x5ece77['scannedColumn']);}console['log'](_0x5c0aa9(0x20f),appliedActions);const _0x51c1b=Object[_0x5c0aa9(0x22a)](actionsConfig)['flatMap'](_0x3c133b=>[_0x3c133b[_0x5c0aa9(0x23e)],..._0x3c133b['dataColumns']]),_0x3a866e=_0x1fb5d1[_0x5c0aa9(0x1ec)](_0x9ccc98=>!_0x51c1b[_0x5c0aa9(0x24b)](_0x9ccc98));selectedHeaders=_0x3a866e,await saveState(),initActionsUI(),renderTable(),console[_0x5c0aa9(0x202)](_0x5c0aa9(0x246),originalData[0x0]),console['log'](_0x5c0aa9(0x20a),isRowProcessed(originalData[0x0])),console[_0x5c0aa9(0x202)](_0x5c0aa9(0x230),countProcessedRows());},'error':_0x149c6a=>{const _0x1d1a92=_0x4e2824;console['error'](_0x1d1a92(0x1e3),_0x149c6a),alert(_0x1d1a92(0x204));}});}async function saveState(){const _0x45a5d1=a0_0x3bf4d1;await chrome['storage'][_0x45a5d1(0x252)][_0x45a5d1(0x1bd)]({'originalData':originalData,'selectedHeaders':selectedHeaders,'appliedActions':appliedActions,'currentLineIndex':currentLineIndex});}function a0_0xaa97(){const _0x577d33=['files','number','Manufacturer\x20Info\x20Text','then','emailAddress','message','\x22>\x20','Responsible\x20Person\x201\x20Email','<label><input\x20type=\x22checkbox\x22\x20value=\x22','Error\x20parsing\x20CSV:','responsiblePersonEU','4YYRqHb','storage','showUnprocessedOnly','map','random','row-done','createElement','filter','textContent','/dp/','Responsible\x20Person\x201','remove','thead','Row','appliedActions','Check\x20Chinese\x20Seller','3721664zroQVv','ItemID','3839410YTNTbt','add','appendChild','dataColumns','length','debugModeSwitch','createObjectURL','addEventListener','scanned','currentLineIndex','csvUpload','log','Dummy\x20Data','Error\x20parsing\x20CSV.\x20Check\x20the\x20browser\x20console\x20for\x20details.','revokeObjectURL','none','keys','Chinese\x20Seller','input[type=\x22checkbox\x22]','Is\x20first\x20row\x20processed?','https://www.amazon.','Manufacturer\x20Email','OFF','every','Applied\x20Actions\x20after\x20reupload:','Fetching\x20GSPR\x20data\x20for\x20URL:\x20','data','Manufacturer\x20City','phoneNumber','Manufacturer\x20AddressLine2','address1','display','push','querySelectorAll','21054781xOXFeJ','Manufacturer\x20Phone','max','chineseSeller','Responsible\x20Person\x201\x20StateOrProvince','city','Domain\x20not\x20found\x20in\x20storage','get_gspr','stateOrProvince','innerHTML','Responsible\x20Person\x201\x20Country','254530mEsYTa','get','No\x20actions\x20selected.','startsWith','108WOXlIw','Debug\x20mode\x20is\x20now','values','scannedGSPR','dataTable','trim','60iYCieA','block','Processed\x20count:','from','downloadCsvBtn','domain','originalData','Responsible\x20Person\x20EU\x20Text','Processed:\x20','tbody','headerSelectionContainer','reUploadCsvBtn','responsiblePersonEUText','entries','reUploadInput','name','scannedColumn','parse','postalCode','Get\x20GSPR','Responsible\x20Person\x201\x20PostalCode','selectedHeaders','Manufacturer\x20PostalCode','click','First\x20row\x20after\x20reupload:','target','statusMessage','CSV\x20appears\x20to\x20be\x20empty\x20or\x20invalid.','classList','includes','address2','false','\x20/\x20','clearDataBtn','92183QAMeqd','No\x20saved\x20data\x20found\x20in\x20storage.','local','restoreFromStorageBtn','Processing\x20complete.','runtime','country','style','CSV\x20data\x20cleared.','26342BcdzfK','actionCheckboxes','GSPR\x20data\x20not\x20found\x20for\x20SKU:\x20','exported_data.csv','gspr','forEach','Responsible\x20Person\x201\x20AddressLine1','<label><input\x20type=\x22checkbox\x22\x20class=\x22actionCheck\x22\x20value=\x22','change','true','set','div','Skipping\x20due\x20to\x20error:','startLineInput','36htzihb','headerCheckboxes','mainSection','error','CustomLabel','Manufacturer\x20StateOrProvince','processedCount','\x20(Line:\x20','Responsible\x20Person\x201\x20AddressLine2','scanned-header','toString','getElementById','manufacturerInfoText','runActionsBtn','unparse','Manufacturer\x20AddressLine1','Failed\x20to\x20get\x20GSPR\x20data\x20from\x20background:\x20','href','value','737478ViDiRP','Processing...','text/csv;charset=utf-8;','12QodIZc','warn','concurrencyInput'];a0_0xaa97=function(){return _0x577d33;};return a0_0xaa97();}async function restoreState(){const _0xee86ed=a0_0x3bf4d1,_0x1bd5cc=await chrome[_0xee86ed(0x1e6)][_0xee86ed(0x252)][_0xee86ed(0x225)]([_0xee86ed(0x234),'selectedHeaders',_0xee86ed(0x1f3),_0xee86ed(0x200)]);_0x1bd5cc[_0xee86ed(0x234)]&&_0x1bd5cc['selectedHeaders']&&(originalData=_0x1bd5cc[_0xee86ed(0x234)],selectedHeaders=_0x1bd5cc[_0xee86ed(0x243)],appliedActions=_0x1bd5cc[_0xee86ed(0x1f3)]||{}),typeof _0x1bd5cc[_0xee86ed(0x200)]===_0xee86ed(0x1db)&&(currentLineIndex=_0x1bd5cc[_0xee86ed(0x200)]);}function updateProgressUI(){const _0x369a44=a0_0x3bf4d1;processedCount=countProcessedRows();const _0x7bfc39=originalData[_0x369a44(0x1fb)],_0x1dac8a=_0x7bfc39-processedCount;processedCountEl[_0x369a44(0x1ed)]=processedCount,totalCountEl[_0x369a44(0x1ed)]=_0x7bfc39;const _0x209156=document[_0x369a44(0x1cc)]('unprocessedCount');_0x209156&&(_0x209156[_0x369a44(0x1ed)]=_0x1dac8a),updateTitleWithProgress();}function updateTitleWithProgress(){const _0x301563=a0_0x3bf4d1,_0x54c2cf=countProcessedRows(),_0x32ebcb=originalData[_0x301563(0x1fb)];document['title']=_0x301563(0x236)+_0x54c2cf+_0x301563(0x24e)+_0x32ebcb+_0x301563(0x1c8)+currentLineIndex+')';}function countProcessedRows(){const _0x38d2d9=a0_0x3bf4d1,_0x1531b6=Object[_0x38d2d9(0x23b)](actionsConfig)[_0x38d2d9(0x1ec)](([_0x17d347])=>appliedActions[_0x17d347])['map'](([_0x3770a3])=>_0x3770a3);if(_0x1531b6[_0x38d2d9(0x1fb)]===0x0)return 0x0;return originalData[_0x38d2d9(0x1ec)](_0x30ecbe=>{const _0xd2ae28=_0x38d2d9;return _0x1531b6[_0xd2ae28(0x20e)](_0x47f148=>{const _0x1cca28=_0xd2ae28,_0x27f0a4=actionsConfig[_0x47f148]['scannedColumn'];return _0x30ecbe[_0x27f0a4]===!![]||_0x30ecbe[_0x27f0a4]===_0x1cca28(0x1bc);});})[_0x38d2d9(0x1fb)];}async function handleRunActions(){const _0x13877f=a0_0x3bf4d1;if(isProcessing)return;concurrency=parseInt(concurrencyInput['value'],0xa)||0x1;const _0x3a7cab=Array[_0x13877f(0x231)](actionCheckboxes['querySelectorAll']('.actionCheck:checked'))[_0x13877f(0x1e8)](_0x190fd4=>_0x190fd4[_0x13877f(0x1d3)]);if(_0x3a7cab[_0x13877f(0x1fb)]===0x0){alert(_0x13877f(0x226));return;}const _0x12d8f6=document[_0x13877f(0x1cc)](_0x13877f(0x1c0));if(_0x12d8f6){const _0x4f7ebc=parseInt(_0x12d8f6[_0x13877f(0x1d3)],0xa)||0x0;currentLineIndex=Math[_0x13877f(0x21b)](0x0,Math['min'](_0x4f7ebc,originalData[_0x13877f(0x1fb)]-0x1)),await saveState();}isProcessing=!![],statusMessageEl[_0x13877f(0x1ed)]=_0x13877f(0x1d5);for(const _0x500370 of _0x3a7cab){if(!appliedActions[_0x500370]){appliedActions[_0x500370]=!![];const _0x3c830a=actionsConfig[_0x500370];for(const _0x5be703 of originalData){_0x5be703[_0x3c830a[_0x13877f(0x23e)]]=_0x5be703[_0x3c830a[_0x13877f(0x23e)]]||![];}renderTable();}}const _0xcb8f15=originalData['slice'](currentLineIndex);await runWithConcurrency(_0xcb8f15,async _0x3ba61a=>{await processAllActionsForRow(_0x3ba61a,_0x3a7cab),currentLineIndex++,await saveState(),updateTitleWithProgress();}),statusMessageEl[_0x13877f(0x1ed)]=_0x13877f(0x254),isProcessing=![],await saveState();}async function processAllActionsForRow(_0x7c6212,_0x442a28){for(const _0x524434 of _0x442a28){!isRowActionScanned(_0x7c6212,_0x524434)&&await processRowAction(_0x7c6212,_0x524434);}}function isRowActionScanned(_0x918045,_0x53493a){const _0x16a713=a0_0x3bf4d1,_0x2a415a=actionsConfig[_0x53493a][_0x16a713(0x23e)];return _0x918045[_0x2a415a]===!![]||_0x918045[_0x2a415a]==='true';}async function runWithConcurrency(_0x2f6c8b,_0x292e12){let _0x340fce=0x0,_0x490684=0x0;return new Promise(_0x5745e0=>{const _0xffe5ba=async()=>{const _0x188ca9=a0_0x394c;while(_0x490684<concurrency&&_0x340fce<_0x2f6c8b[_0x188ca9(0x1fb)]){const _0x580c47=_0x2f6c8b[_0x340fce++];_0x490684++,_0x292e12(_0x580c47)[_0x188ca9(0x1dd)](async()=>{const _0x57edeb=_0x188ca9;_0x490684--,updateProgressUI(),await saveState(),_0x340fce===_0x2f6c8b[_0x57edeb(0x1fb)]&&_0x490684===0x0?_0x5745e0():_0xffe5ba();});}};_0xffe5ba();});}async function processRowAction(_0x59fcdf,_0xe4e9dc){const _0x48dac4=a0_0x3bf4d1;if(isRowActionScanned(_0x59fcdf,_0xe4e9dc))return;const _0x108d0f=actionsConfig[_0xe4e9dc],_0x2a158a=_0x59fcdf[_0x48dac4(0x1c5)]||'';try{if(_0xe4e9dc===_0x48dac4(0x1b7)){const _0x5e5acd=await getGSPR(_0x2a158a);_0x108d0f['dataColumns'][_0x48dac4(0x1b8)](_0x492950=>{_0x59fcdf[_0x492950]=_0x5e5acd[_0x492950]??'';});}else{if(_0xe4e9dc===_0x48dac4(0x21c)){const _0x167119=await checkIfSellerChinese(_0x2a158a);_0x108d0f[_0x48dac4(0x1fa)][_0x48dac4(0x1b8)](_0xdde48a=>{_0x59fcdf[_0xdde48a]=_0x167119[_0xdde48a]??'';});}else _0x108d0f[_0x48dac4(0x1fa)]['forEach'](_0x2b4353=>{const _0x1e6606=_0x48dac4;_0x59fcdf[_0x2b4353]=_0x1e6606(0x203);});}_0x59fcdf[_0x108d0f[_0x48dac4(0x23e)]]=!![];}catch(_0x9e52b3){console[_0x48dac4(0x1c4)]('Error\x20processing\x20action:',_0xe4e9dc,_0x9e52b3);if(debugMode)throw _0x9e52b3;else console[_0x48dac4(0x1d8)](_0x48dac4(0x1bf),_0x9e52b3['message']);}updateRowInDOM(_0x59fcdf),await saveState();}function updateRowInDOM(_0x49f2e1){const _0x4fae3f=a0_0x3bf4d1,_0x9b24f2=itemIdToRowElement[_0x49f2e1[_0x4fae3f(0x1f6)]];if(!_0x9b24f2)return;const _0x57c36f=itemIdToRowIndex[_0x49f2e1[_0x4fae3f(0x1f6)]],_0x2e88d4=_0x9b24f2[_0x4fae3f(0x218)]('td');currentHeaders[_0x4fae3f(0x1b8)]((_0x14ffdf,_0x184081)=>{const _0x3eadcb=_0x4fae3f;let _0x2df154='';_0x14ffdf===_0x3eadcb(0x1f2)?_0x2df154=(_0x57c36f+0x1)[_0x3eadcb(0x1cb)]():(_0x2df154=_0x49f2e1[_0x14ffdf]??'',_0x14ffdf[_0x3eadcb(0x227)](_0x3eadcb(0x1ff))&&(_0x2df154=_0x2df154===!![]||_0x2df154===_0x3eadcb(0x1bc)?'✓':'')),_0x2e88d4[_0x184081][_0x3eadcb(0x1ed)]=_0x2df154;}),isRowProcessed(_0x49f2e1)?_0x9b24f2[_0x4fae3f(0x24a)][_0x4fae3f(0x1f8)](_0x4fae3f(0x1ea)):_0x9b24f2[_0x4fae3f(0x24a)][_0x4fae3f(0x1f0)]('row-done');}