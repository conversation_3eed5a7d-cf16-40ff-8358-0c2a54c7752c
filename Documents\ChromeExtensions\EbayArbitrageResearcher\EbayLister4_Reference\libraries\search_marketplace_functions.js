var a0_0x39713d=a0_0x5ad0;(function(_0x3d018c,_0x2fc07a){var _0x4f80c3=a0_0x5ad0,_0x468523=_0x3d018c();while(!![]){try{var _0x58fe2a=-parseInt(_0x4f80c3(0xbe))/0x1+parseInt(_0x4f80c3(0xb6))/0x2*(parseInt(_0x4f80c3(0x129))/0x3)+-parseInt(_0x4f80c3(0x9f))/0x4*(parseInt(_0x4f80c3(0x146))/0x5)+-parseInt(_0x4f80c3(0xb5))/0x6+parseInt(_0x4f80c3(0x130))/0x7+-parseInt(_0x4f80c3(0x17b))/0x8+-parseInt(_0x4f80c3(0xc5))/0x9*(-parseInt(_0x4f80c3(0xc1))/0xa);if(_0x58fe2a===_0x2fc07a)break;else _0x468523['push'](_0x468523['shift']());}catch(_0x6ad074){_0x468523['push'](_0x468523['shift']());}}}(a0_0x56bd,0x99240));function createAmazonSearchButton(_0x18d3fc=null,_0x4f47e9=null,_0x5391b6=null,_0xb9c9dd=null){var _0xc0f318=a0_0x5ad0,_0x92f2c9=document[_0xc0f318(0xdf)]('a');_0x92f2c9['setAttribute']('class',_0xc0f318(0xfb)),_0x92f2c9['classList'][_0xc0f318(0x13d)](_0xc0f318(0xa0)),_0x92f2c9[_0xc0f318(0x177)][_0xc0f318(0x13d)]('amazonSearchLink'),_0x92f2c9[_0xc0f318(0x155)]('title',_0xc0f318(0x197));var _0x335d7b=document[_0xc0f318(0xdf)](_0xc0f318(0xa7));return _0x335d7b['setAttribute'](_0xc0f318(0x9d),chrome['runtime'][_0xc0f318(0x15c)]('icons/amazon-icon.png')),_0x335d7b[_0xc0f318(0x155)]('style',_0xc0f318(0x11e)),_0x92f2c9[_0xc0f318(0x14d)](_0x335d7b),_0x92f2c9[_0xc0f318(0x165)]('click',async function(_0x260521){var _0x2b61d8=_0xc0f318;_0x260521[_0x2b61d8(0x182)](),this[_0x2b61d8(0x177)][_0x2b61d8(0x13d)](_0x2b61d8(0xc9)),setTimeout(()=>{var _0x465f2e=_0x2b61d8;this[_0x465f2e(0x177)]['remove'](_0x465f2e(0xc9));},0x320);if(!_0x18d3fc){var _0x5d2ad1=getItemContainer(_0x260521);if(!_0x5d2ad1)return;var _0x3164bb=extractItemData(_0x5d2ad1);_0x18d3fc=_0x3164bb['title'],_0x18d3fc[_0x2b61d8(0xa4)]('...')&&(_0x18d3fc=_0x18d3fc[_0x2b61d8(0x126)](0x0,_0x18d3fc['lastIndexOf']('\x20'))),_0x18d3fc[_0x2b61d8(0xc0)]>0x4b&&(_0x18d3fc=_0x18d3fc['substring'](0x0,_0x18d3fc[_0x2b61d8(0xf1)]('\x20')));}var {amazonSortType:_0x4a5b90}=await chrome[_0x2b61d8(0xea)][_0x2b61d8(0xc4)]['get'](_0x2b61d8(0xce));console[_0x2b61d8(0xc7)](_0x2b61d8(0xce),_0x4a5b90);!_0x4a5b90&&(_0x4a5b90=_0x2b61d8(0x132));console[_0x2b61d8(0xc7)](_0x2b61d8(0xce),_0x4a5b90),console[_0x2b61d8(0xc7)](_0x2b61d8(0x16a));var {domain:_0x4e27b7}=await chrome[_0x2b61d8(0xea)][_0x2b61d8(0xc4)][_0x2b61d8(0xfa)](_0x2b61d8(0x178));while(_0x18d3fc[_0x2b61d8(0xc0)]>0x50){_0x18d3fc=_0x18d3fc[_0x2b61d8(0x126)](0x0,_0x18d3fc[_0x2b61d8(0xf1)]('\x20'));}var {amazonSearchType:_0x4dafc0}=await chrome[_0x2b61d8(0xea)]['local'][_0x2b61d8(0xfa)](_0x2b61d8(0x101)),_0x4199c0=_0x18d3fc;_0x4dafc0==_0x2b61d8(0x154)&&(_0x4199c0=await getKeywordsFromTitle(_0x18d3fc));var _0x3164bb;try{var _0x3164bb=extractItemData(_0x5d2ad1);}catch(_0x345136){console[_0x2b61d8(0xc7)](_0x2b61d8(0x10a),_0x345136);}!_0x3164bb&&(_0x3164bb={'title':_0x18d3fc,'price':_0x4f47e9,'itemNumber':_0x5391b6,'image':_0xb9c9dd}),console[_0x2b61d8(0xc7)](_0x2b61d8(0xcd),_0x3164bb),chrome[_0x2b61d8(0xbd)][_0x2b61d8(0xd8)]({'type':_0x2b61d8(0xf0),'searchQuery':_0x4199c0,'options':{'isTabActive':!![],'sort':_0x4a5b90},'itemData':_0x3164bb});}),_0x92f2c9;}function createAmazonSearchButtonFromItemNumber(_0x3fdffa){var _0x2cc24e=a0_0x5ad0,_0x46a2f2=document[_0x2cc24e(0xdf)]('a');_0x46a2f2[_0x2cc24e(0x155)]('id',_0x2cc24e(0x167)),_0x46a2f2[_0x2cc24e(0x155)](_0x2cc24e(0x10f),_0x2cc24e(0xfb)),_0x46a2f2[_0x2cc24e(0x177)][_0x2cc24e(0x13d)](_0x2cc24e(0xa0)),_0x46a2f2[_0x2cc24e(0x177)]['add'](_0x2cc24e(0xd1)),_0x46a2f2['setAttribute'](_0x2cc24e(0xe1),_0x2cc24e(0x197));var _0x2d3425=document[_0x2cc24e(0xdf)](_0x2cc24e(0xa7));return _0x2d3425[_0x2cc24e(0x155)](_0x2cc24e(0x9d),chrome[_0x2cc24e(0xbd)][_0x2cc24e(0x15c)](_0x2cc24e(0x138))),_0x2d3425[_0x2cc24e(0x155)](_0x2cc24e(0x179),_0x2cc24e(0x11e)),_0x46a2f2[_0x2cc24e(0x14d)](_0x2d3425),_0x46a2f2[_0x2cc24e(0x165)](_0x2cc24e(0xb1),async function(_0x2c3449){var _0xf17ac0=_0x2cc24e;_0x2c3449[_0xf17ac0(0x182)](),this[_0xf17ac0(0x177)][_0xf17ac0(0x13d)](_0xf17ac0(0xc9)),setTimeout(()=>{var _0x3427a4=_0xf17ac0;this[_0x3427a4(0x177)][_0x3427a4(0x12c)](_0x3427a4(0xc9));},0x320),console[_0xf17ac0(0xc7)]('amazonLinkFromItemNumber\x20clicked',_0x3fdffa),chrome[_0xf17ac0(0xbd)][_0xf17ac0(0xd8)]({'type':'grab_sku_from_ebay_item_and_open_product','itemNumber':_0x3fdffa});}),_0x46a2f2;}function openAmazonSkuButton(_0x4d721a){var _0x55303c=a0_0x5ad0,_0x229795=document[_0x55303c(0xdf)]('a');_0x229795[_0x55303c(0x155)]('id','amazonLink'),_0x229795[_0x55303c(0x155)](_0x55303c(0x10f),'a-link-text'),_0x229795[_0x55303c(0x177)][_0x55303c(0x13d)]('icon'),_0x229795[_0x55303c(0x155)]('title',_0x55303c(0x187));var _0x574373=document[_0x55303c(0xdf)]('img');return _0x574373[_0x55303c(0x155)]('src',chrome[_0x55303c(0xbd)][_0x55303c(0x15c)](_0x55303c(0x138))),_0x574373[_0x55303c(0x155)](_0x55303c(0x179),_0x55303c(0x11e)),_0x229795[_0x55303c(0x14d)](_0x574373),_0x229795[_0x55303c(0x165)](_0x55303c(0xb1),async function(_0x48e445){var _0x5b9653=_0x55303c;_0x48e445[_0x5b9653(0x182)](),this[_0x5b9653(0x177)][_0x5b9653(0x13d)]('clicked'),setTimeout(()=>{var _0x3f90e6=_0x5b9653;this['classList'][_0x3f90e6(0x12c)](_0x3f90e6(0xc9));},0x320);var {domain:_0x29d18b}=await chrome[_0x5b9653(0xea)]['local'][_0x5b9653(0xfa)]('domain'),_0x421053='https://www.amazon.'+_0x29d18b+_0x5b9653(0x106)+_0x4d721a+_0x5b9653(0x190);chrome['runtime'][_0x5b9653(0xd8)]({'type':_0x5b9653(0x164),'url':_0x421053});}),_0x229795;}function createEbaySearchButton(_0x316023=null,_0x2733e8,_0x3d26ab=a0_0x39713d(0x11b)){var _0x36836a=a0_0x39713d;console[_0x36836a(0xc7)]('createEbaySearchButton',_0x316023,_0x2733e8);var _0x171f47=document[_0x36836a(0xdf)]('a');_0x171f47[_0x36836a(0x155)]('id','ebayLink'),_0x171f47[_0x36836a(0x155)]('class',_0x36836a(0xfb)),_0x171f47[_0x36836a(0x177)]['add'](_0x36836a(0xa0));_0x2733e8&&_0x2733e8[_0x36836a(0x10c)]?_0x171f47['setAttribute'](_0x36836a(0xe1),_0x36836a(0x134)):_0x171f47[_0x36836a(0x155)](_0x36836a(0xe1),_0x36836a(0x157));var _0x30ebc1=document[_0x36836a(0xdf)](_0x36836a(0xa7));return _0x30ebc1['setAttribute'](_0x36836a(0x9d),chrome[_0x36836a(0xbd)][_0x36836a(0x15c)](_0x3d26ab)),_0x30ebc1['setAttribute']('style',_0x36836a(0x11e)),_0x171f47[_0x36836a(0x14d)](_0x30ebc1),_0x171f47['addEventListener'](_0x36836a(0xb1),async function(_0xcf405f){var _0x324193=_0x36836a;_0xcf405f[_0x324193(0x182)](),this[_0x324193(0x177)][_0x324193(0x13d)]('clicked'),setTimeout(()=>{var _0x38bb62=_0x324193;this[_0x38bb62(0x177)]['remove'](_0x38bb62(0xc9));},0x320);if(!_0x316023){console[_0x324193(0xc7)](_0x324193(0x103));var _0x5b0f61=getItemContainer(_0xcf405f);if(!_0x5b0f61)return;var _0x3bda50=extractItemData(_0x5b0f61);_0x316023=_0x3bda50[_0x324193(0xe1)];}else console[_0x324193(0xc7)](_0x324193(0xbb),_0x316023);console[_0x324193(0xc7)](_0x324193(0x16a));var {domain:_0x1e7ad4}=await chrome['storage'][_0x324193(0xc4)][_0x324193(0xfa)](_0x324193(0x178));while(_0x316023[_0x324193(0xc0)]>0x50){_0x316023=_0x316023[_0x324193(0x126)](0x0,_0x316023['lastIndexOf']('\x20'));}var _0x217ba7='https://www.ebay.'+_0x1e7ad4+'/sch/i.html?_nkw='+encodeURIComponent(_0x316023)+_0x324193(0x18f)+encodeURIComponent(_0x316023);_0x2733e8&&_0x2733e8[_0x324193(0x10c)]&&(_0x217ba7+=_0x324193(0x9b)),_0x2733e8&&_0x2733e8[_0x324193(0xd0)]&&(_0x217ba7+=_0x324193(0x143)),_0x2733e8&&_0x2733e8[_0x324193(0xb2)]&&(_0x217ba7+=_0x324193(0x11a)),_0x217ba7+=_0x324193(0xdd),_0x217ba7+='&LH_FS=1',chrome[_0x324193(0xbd)][_0x324193(0xd8)]({'type':'openNewTab','url':_0x217ba7});}),_0x171f47;}function createGoogleImageSearchButton(_0x2ec5c8=null){var _0x5b14dd=a0_0x39713d,_0x56f897=document[_0x5b14dd(0xdf)]('a');_0x56f897[_0x5b14dd(0x155)]('id',_0x5b14dd(0xfe)),_0x56f897[_0x5b14dd(0x155)](_0x5b14dd(0x10f),_0x5b14dd(0xfb)),_0x56f897[_0x5b14dd(0x177)][_0x5b14dd(0x13d)]('icon'),_0x56f897[_0x5b14dd(0x155)](_0x5b14dd(0xe1),_0x5b14dd(0x15d));var _0x4c6f01=document[_0x5b14dd(0xdf)](_0x5b14dd(0xa7));return _0x4c6f01[_0x5b14dd(0x155)](_0x5b14dd(0x9d),chrome[_0x5b14dd(0xbd)][_0x5b14dd(0x15c)]('icons/google-icon.png')),_0x4c6f01[_0x5b14dd(0x155)]('style',_0x5b14dd(0x11e)),_0x56f897[_0x5b14dd(0x14d)](_0x4c6f01),_0x56f897[_0x5b14dd(0x165)]('click',async function(_0x57f06c){var _0x3a7c5b=_0x5b14dd;_0x57f06c[_0x3a7c5b(0x182)](),this[_0x3a7c5b(0x177)][_0x3a7c5b(0x13d)](_0x3a7c5b(0xc9)),setTimeout(()=>{var _0x4c9874=_0x3a7c5b;this[_0x4c9874(0x177)][_0x4c9874(0x12c)](_0x4c9874(0xc9));},0x320);if(!_0x2ec5c8){var _0x565b71=getItemContainer(_0x57f06c);if(!_0x565b71)return;var _0x4ca816=extractItemData(_0x565b71);_0x2ec5c8=_0x4ca816[_0x3a7c5b(0x18e)];}var {domain:_0x22b1fe}=await chrome[_0x3a7c5b(0xea)]['local']['get'](_0x3a7c5b(0x178)),_0x1c2625=domainToHl(_0x22b1fe),_0x593f40=encodeURIComponent(_0x2ec5c8),_0x5b83a7=_0x3a7c5b(0xa6)+_0x593f40+'&hl='+_0x1c2625;chrome['runtime'][_0x3a7c5b(0xd8)]({'type':_0x3a7c5b(0x14e),'imageUrl':_0x2ec5c8});}),_0x56f897;}function createGoogleSearchDescriptionButton(_0x59ad7f=null){var _0x224495=a0_0x39713d,_0x3c570d=document[_0x224495(0xdf)]('a');_0x3c570d[_0x224495(0x155)]('id',_0x224495(0xfe)),_0x3c570d[_0x224495(0x155)]('class',_0x224495(0xfb)),_0x3c570d[_0x224495(0x177)][_0x224495(0x13d)]('icon'),_0x3c570d[_0x224495(0x155)](_0x224495(0xe1),_0x224495(0x176));var _0x1fb8eb=document[_0x224495(0xdf)](_0x224495(0xa7));return _0x1fb8eb[_0x224495(0x155)](_0x224495(0x9d),chrome[_0x224495(0xbd)][_0x224495(0x15c)](_0x224495(0x162))),_0x1fb8eb[_0x224495(0x155)](_0x224495(0x179),_0x224495(0x11e)),_0x3c570d[_0x224495(0x14d)](_0x1fb8eb),_0x3c570d['addEventListener'](_0x224495(0xb1),async function(_0x1fa6e6){var _0x21125a=_0x224495;_0x1fa6e6[_0x21125a(0x182)](),this['classList'][_0x21125a(0x13d)](_0x21125a(0xc9)),setTimeout(()=>{var _0x4ecacf=_0x21125a;this[_0x4ecacf(0x177)][_0x4ecacf(0x12c)](_0x4ecacf(0xc9));},0x320);if(!_0x59ad7f){var _0xc30414=getItemContainer(_0x1fa6e6);if(!_0xc30414)return;var _0xcd7c33=extractItemData(_0xc30414);_0x59ad7f=_0xcd7c33['itemNumber'];}chrome[_0x21125a(0xbd)][_0x21125a(0xd8)]({'type':_0x21125a(0x193),'itemNumber':_0x59ad7f});}),_0x3c570d;}function createLookUpSkuButton(_0x1b2958){var _0x7f5469=a0_0x39713d,_0x29a76a=document[_0x7f5469(0xdf)]('a');_0x29a76a[_0x7f5469(0x155)]('id',_0x7f5469(0x199)),_0x29a76a[_0x7f5469(0x155)](_0x7f5469(0x10f),_0x7f5469(0xfb)),_0x29a76a[_0x7f5469(0x177)][_0x7f5469(0x13d)]('icon'),_0x29a76a['setAttribute'](_0x7f5469(0xe1),'Look\x20up\x20this\x20SKU');var _0xb1f32e=document[_0x7f5469(0xdf)]('img');return _0xb1f32e[_0x7f5469(0x155)](_0x7f5469(0x9d),chrome[_0x7f5469(0xbd)]['getURL']('icons/search.png')),_0xb1f32e[_0x7f5469(0x155)](_0x7f5469(0x179),_0x7f5469(0x11e)),_0x29a76a[_0x7f5469(0x14d)](_0xb1f32e),_0x29a76a[_0x7f5469(0x165)](_0x7f5469(0xb1),async function(_0x5f2f00){var _0x569aff=_0x7f5469;_0x5f2f00['preventDefault'](),this['classList'][_0x569aff(0x13d)](_0x569aff(0xc9)),setTimeout(()=>{var _0x1562d8=_0x569aff;this[_0x1562d8(0x177)][_0x1562d8(0x12c)]('clicked');},0x320);var {domain:_0x52b1b5}=await chrome['storage'][_0x569aff(0xc4)][_0x569aff(0xfa)]('domain'),_0x505ad6=_0x569aff(0x191)+_0x52b1b5+_0x569aff(0x106)+_0x1b2958+_0x569aff(0x19a);chrome['runtime'][_0x569aff(0xd8)]({'type':_0x569aff(0x164),'url':_0x505ad6,'options':{'active':!![]}});}),_0x29a76a;}function domainToHl(_0x292e57){var _0x45e054=a0_0x39713d;const _0x514fc5={'com':_0x45e054(0xdc),'ca':_0x45e054(0xa9),'co.uk':_0x45e054(0x152)};return _0x514fc5[_0x292e57]||'en-US';}function createSearchTerapeakButton(_0x2ec52c=null){var _0x49e4fc=a0_0x39713d;console[_0x49e4fc(0xc7)]('createSearchTerapeakButton',_0x2ec52c);var _0x1852f4=document['createElement']('a');_0x1852f4['setAttribute'](_0x49e4fc(0x10f),_0x49e4fc(0xfb)),_0x1852f4['classList'][_0x49e4fc(0x13d)](_0x49e4fc(0x181)),_0x1852f4[_0x49e4fc(0x177)][_0x49e4fc(0x13d)](_0x49e4fc(0xa0)),_0x1852f4[_0x49e4fc(0x155)](_0x49e4fc(0xe1),_0x49e4fc(0x125));_0x2ec52c&&_0x1852f4[_0x49e4fc(0x155)](_0x49e4fc(0x141),_0x2ec52c);var _0xccb9bf=document[_0x49e4fc(0xdf)]('img');return _0xccb9bf[_0x49e4fc(0x155)](_0x49e4fc(0x9d),chrome[_0x49e4fc(0xbd)][_0x49e4fc(0x15c)](_0x49e4fc(0x109))),_0xccb9bf[_0x49e4fc(0x155)](_0x49e4fc(0x179),'width:\x2020px;\x20height:\x2020px;\x20vertical-align:\x20middle;\x20margin-right:\x205px;'),_0x1852f4[_0x49e4fc(0x14d)](_0xccb9bf),_0x1852f4['addEventListener'](_0x49e4fc(0xb1),async function(_0xaa9216){var _0xf733d3=_0x49e4fc;_0xaa9216['preventDefault']();this['getAttribute'](_0xf733d3(0x141))&&(_0x3ba7a7=this[_0xf733d3(0x104)](_0xf733d3(0x141)));console[_0xf733d3(0xc7)](_0xf733d3(0x189),_0x3ba7a7),this[_0xf733d3(0x177)][_0xf733d3(0x13d)](_0xf733d3(0xc9)),setTimeout(()=>{var _0x310b3c=_0xf733d3;this['classList'][_0x310b3c(0x12c)]('clicked');},0x320);if(!_0x3ba7a7){var _0x402f4d=getItemContainer(_0xaa9216);if(!_0x402f4d)return;var _0x3c25ed=extractItemData(_0x402f4d);_0x3ba7a7=_0x3c25ed[_0xf733d3(0xe1)];}console[_0xf733d3(0xc7)](_0xf733d3(0xe1),_0x3ba7a7);var {convertToKeywords:_0x5addad}=await chrome[_0xf733d3(0xea)][_0xf733d3(0xc4)]['get'](_0xf733d3(0xee));if(_0x5addad)var _0x3ba7a7=await getKeywordsFromTitle(_0x3ba7a7);var {domain:_0x4fe261}=await chrome[_0xf733d3(0xea)][_0xf733d3(0xc4)]['get'](_0xf733d3(0x178)),_0x4a2657=buildTerapeakSearchUrl(_0x3ba7a7,_0x4fe261);chrome[_0xf733d3(0xbd)]['sendMessage']({'type':_0xf733d3(0x164),'url':_0x4a2657});}),_0x1852f4;}async function getKeywordsFromTitle(_0x503a37){var _0x519aea=a0_0x39713d,_0x534258=await fetch(chrome['runtime'][_0x519aea(0x15c)]('prompts_json/3_word_descriptor.json'))[_0x519aea(0x9c)](_0x5eba3b=>_0x5eba3b[_0x519aea(0x175)]());_0x534258[_0x519aea(0x173)]=_0x503a37,console['log'](_0x519aea(0x113),_0x534258);var _0x2636f4=await new Promise((_0x87284d,_0x3e195c)=>{var _0x2f278e=_0x519aea;chrome[_0x2f278e(0xbd)][_0x2f278e(0xd8)]({'type':_0x2f278e(0xc3),'url':_0x2f278e(0xc8),'data':_0x534258},function(_0x42424e){_0x87284d(_0x42424e['data']);});});console[_0x519aea(0xc7)](_0x519aea(0x117),_0x2636f4),_0x2636f4=JSON['parse'](_0x2636f4);var _0x3a9db2=_0x2636f4['output'];return _0x3a9db2;}function a0_0x5ad0(_0x6192b8,_0x1058e8){var _0x56bdc9=a0_0x56bd();return a0_0x5ad0=function(_0x5ad000,_0x17e1cf){_0x5ad000=_0x5ad000-0x99;var _0x242c27=_0x56bdc9[_0x5ad000];return _0x242c27;},a0_0x5ad0(_0x6192b8,_0x1058e8);}function buildTerapeakSearchUrl(_0x3135f8,_0x50e6b2='ca',_0x1bc55b=0x1e,_0x125475=0x0,_0x1b6ef5=0x0,_0x51edf1=0x32,_0x34d3af=a0_0x39713d(0xcc),_0x4a8bb5=a0_0x39713d(0xf8),_0x270e2b=a0_0x39713d(0x124),_0x2ae495=a0_0x39713d(0xb0),_0x5bce81=a0_0x39713d(0x185),_0x147317=0x0){var _0x388603=a0_0x39713d,_0x270e2b='';switch(_0x50e6b2){case'ca':_0x270e2b=_0x388603(0x124);break;case _0x388603(0x192):_0x270e2b='EBAY-US';break;case'co.uk':_0x270e2b=_0x388603(0xe9);break;default:_0x270e2b=_0x388603(0x124);}const _0x5b8e1e='https://www.ebay.'+_0x50e6b2+_0x388603(0xe8),_0x3dff0e=_0x388603(0xf6)+_0x3135f8,_0xc55137=_0x388603(0xb8)+_0x1bc55b,_0x3d08ff=_0x388603(0xa3)+_0x125475,_0xa05572=_0x388603(0x142)+_0x1b6ef5,_0x219594=_0x388603(0x12d)+_0x51edf1,_0x12d6bb=_0x388603(0xcb)+_0x34d3af,_0x4a9090=_0x388603(0x18c)+_0x4a8bb5,_0x334711=_0x388603(0x9a)+_0x270e2b,_0x434234='tz='+encodeURIComponent(_0x2ae495),_0x2a47b4=_0x388603(0xac)+_0x147317,_0x6b67c5=[_0x3dff0e,_0xc55137,_0x3d08ff,_0xa05572,_0x219594,_0x12d6bb,_0x4a9090,_0x334711,_0x434234,_0x2a47b4][_0x388603(0xba)]('&');return _0x5b8e1e+'?'+_0x6b67c5;}async function openSellerItemsPage(_0x5db938){var _0x6300c=a0_0x39713d,{domain:_0x579d56}=await chrome[_0x6300c(0xea)][_0x6300c(0xc4)]['get'](_0x6300c(0x178)),_0x52d529=_0x6300c(0x17c)+_0x579d56+'/sch/i.html?_dkr=1&_fsrp=1&iconV2Request=true&_blrs=recall_filtering&_ssn='+_0x5db938+_0x6300c(0x184)+_0x5db938+_0x6300c(0xd3);chrome[_0x6300c(0xbd)][_0x6300c(0xd8)]({'type':_0x6300c(0x164),'url':_0x52d529});}async function openItemPageThenSellerItemsPage(_0x32fc02){var _0x5bb458=a0_0x39713d;chrome[_0x5bb458(0xbd)]['sendMessage']({'type':_0x5bb458(0x114),'itemNumber':_0x32fc02}),console[_0x5bb458(0xc7)](_0x5bb458(0x144),_0x32fc02);}async function openItemPageThenGetSeller(_0x35ce37){var _0x398abe=a0_0x39713d,{response:_0x5c1c73}=await chrome['runtime'][_0x398abe(0xd8)]({'type':_0x398abe(0xaf),'itemNumber':_0x35ce37});return console[_0x398abe(0xc7)](_0x398abe(0x16f),_0x5c1c73),_0x5c1c73;}function createOpenSellerItemsButton(_0x2989c5=null){var _0x348cf3=a0_0x39713d;console['log'](_0x348cf3(0xad),_0x2989c5);var _0x19ef99=document[_0x348cf3(0xdf)]('a');_0x19ef99[_0x348cf3(0x155)]('id','sellerItemsLink'),_0x19ef99[_0x348cf3(0x155)](_0x348cf3(0x10f),'a-link-text'),_0x19ef99[_0x348cf3(0x177)][_0x348cf3(0x13d)](_0x348cf3(0xa0)),_0x19ef99[_0x348cf3(0x155)](_0x348cf3(0xe1),_0x348cf3(0x16d));var _0xc6b523=document[_0x348cf3(0xdf)](_0x348cf3(0xa7));return _0xc6b523[_0x348cf3(0x155)](_0x348cf3(0x9d),chrome[_0x348cf3(0xbd)][_0x348cf3(0x15c)](_0x348cf3(0x156))),_0xc6b523[_0x348cf3(0x155)](_0x348cf3(0x179),'width:\x2020px;\x20height:\x2020px;\x20vertical-align:\x20middle;\x20margin-right:\x205px;'),_0x19ef99[_0x348cf3(0x14d)](_0xc6b523),_0x19ef99[_0x348cf3(0x165)](_0x348cf3(0xb1),async function(_0x49f3c5){var _0x21732b=_0x348cf3;_0x49f3c5[_0x21732b(0x182)](),console['log']('sellerItemsLink\x20clicked\x201',_0x2989c5),this['classList'][_0x21732b(0x13d)](_0x21732b(0xc9)),setTimeout(()=>{var _0x8409ce=_0x21732b;this[_0x8409ce(0x177)][_0x8409ce(0x12c)](_0x8409ce(0xc9));},0x320),console['log'](_0x21732b(0x135),_0x2989c5);var _0x2a28e6;if(!_0x2989c5){console['log']('username\x20not\x20found');var _0x16fe21=getItemContainer(_0x49f3c5);console[_0x21732b(0xc7)](_0x21732b(0x111),_0x16fe21);if(!_0x16fe21)return;var _0x308d7e=extractItemData(_0x16fe21);console[_0x21732b(0xc7)](_0x21732b(0x127),_0x308d7e),_0x2989c5=_0x308d7e['username'],_0x2a28e6=_0x308d7e[_0x21732b(0x198)];}if(_0x2989c5[_0x21732b(0xa5)]('\x20')||_0x2989c5!==_0x2989c5['toLowerCase']()){console[_0x21732b(0xc7)](_0x21732b(0x9e),_0x2989c5);if(!_0x2a28e6){var _0x16fe21=getItemContainer(_0x49f3c5);if(!_0x16fe21)return;var _0x308d7e=extractItemData(_0x16fe21);_0x2a28e6=_0x308d7e[_0x21732b(0x198)];}openItemPageThenSellerItemsPage(_0x2a28e6);}else _0x2989c5=_0x2989c5[_0x21732b(0x16c)](),console[_0x21732b(0xc7)](_0x21732b(0xd6),_0x2989c5),openSellerItemsPage(_0x2989c5);}),_0x19ef99;}function getParentItemContainer(_0x418ba8){var _0x39bb27=a0_0x39713d;while(_0x418ba8&&(!_0x418ba8['classList'][_0x39bb27(0x120)](_0x39bb27(0x174))&&!_0x418ba8[_0x39bb27(0x177)]['contains'](_0x39bb27(0xb9)))){_0x418ba8=_0x418ba8[_0x39bb27(0xf5)];}return _0x418ba8;}function createCheckPurchaseHistoryButton(_0x154799=null){var _0x267dc3=a0_0x39713d,_0x5c955f=document[_0x267dc3(0xdf)]('a');_0x5c955f['setAttribute']('id',_0x267dc3(0xcf)),_0x5c955f[_0x267dc3(0x155)](_0x267dc3(0x10f),_0x267dc3(0xfb)),_0x5c955f[_0x267dc3(0x177)][_0x267dc3(0x13d)](_0x267dc3(0xa0)),_0x5c955f[_0x267dc3(0x155)](_0x267dc3(0xe1),_0x267dc3(0x133));var _0x2e8fef=document[_0x267dc3(0xdf)](_0x267dc3(0xa7));return _0x2e8fef[_0x267dc3(0x155)](_0x267dc3(0x9d),chrome['runtime'][_0x267dc3(0x15c)](_0x267dc3(0xa8))),_0x2e8fef[_0x267dc3(0x155)](_0x267dc3(0x179),'width:\x2020px;\x20height:\x2020px;\x20vertical-align:\x20middle;\x20margin-right:\x205px;'),_0x5c955f[_0x267dc3(0x14d)](_0x2e8fef),_0x5c955f[_0x267dc3(0x165)](_0x267dc3(0xb1),async function(_0x22c861){var _0x5247b7=_0x267dc3;console[_0x5247b7(0xc7)](_0x5247b7(0xc6),_0x154799),this[_0x5247b7(0x177)][_0x5247b7(0x13d)](_0x5247b7(0xc9)),setTimeout(()=>{var _0x4b74a1=_0x5247b7;this[_0x4b74a1(0x177)][_0x4b74a1(0x12c)](_0x4b74a1(0xc9));},0x320),_0x22c861[_0x5247b7(0x182)]();var _0x5f28a6=getItemContainer(_0x22c861);console['log'](_0x5247b7(0xfd),_0x5f28a6);if(!_0x5f28a6){try{var _0x5f0196=await chrome[_0x5247b7(0xbd)]['sendMessage']({'type':_0x5247b7(0x180),'itemNumber':_0x154799,'lastXDays':0x1e,'closeTabAfterSearch':![]});console[_0x5247b7(0xc7)](_0x5247b7(0xd5),_0x5f0196),_0x5eb1ac=_0x5f0196[_0x5247b7(0x12a)];}catch(_0xa4477f){console[_0x5247b7(0xc7)](_0x5247b7(0x10a),_0xa4477f),_0x5eb1ac=-0x3e7;}return;}var {selectedFilter:_0x37cf44}=await chrome[_0x5247b7(0xea)]['local']['get'](_0x5247b7(0x140));!_0x37cf44&&(_0x37cf44='90',await chrome[_0x5247b7(0xea)]['local'][_0x5247b7(0x11d)]({'selectedFilter':_0x37cf44}));var _0x48c922=_0x37cf44,_0x5eb1ac=await checkPurchaseHistoryAndAddToItem(_0x5f28a6,_0x48c922,![],!![]);console[_0x5247b7(0xc7)](_0x5247b7(0x12a),_0x5eb1ac);}),_0x5c955f;}function getItemContainer(_0x286e41){var _0x4717a7=a0_0x39713d,_0x522c9d=_0x286e41['target'];return _0x522c9d=getParentItemContainer(_0x522c9d),console[_0x4717a7(0xc7)]('found\x20s-item',_0x522c9d),_0x522c9d;}function createButtonToCopyData(_0x44e0fe=null,_0x552d9b=null,_0x2fff31=null){var _0xce4afe=a0_0x39713d,_0x464de3=document['createElement']('a');_0x464de3['setAttribute']('id',_0xce4afe(0xf2)),_0x464de3[_0xce4afe(0x155)](_0xce4afe(0x10f),_0xce4afe(0xfb)),_0x464de3[_0xce4afe(0x177)][_0xce4afe(0x13d)](_0xce4afe(0xa0)),_0x464de3[_0xce4afe(0x155)](_0xce4afe(0xe1),_0xce4afe(0x112));var _0x556e36=document[_0xce4afe(0xdf)]('img');return _0x556e36[_0xce4afe(0x155)]('src',chrome['runtime'][_0xce4afe(0x15c)](_0xce4afe(0x170))),_0x556e36[_0xce4afe(0x155)](_0xce4afe(0x179),_0xce4afe(0x11e)),_0x464de3['appendChild'](_0x556e36),_0x464de3[_0xce4afe(0x165)](_0xce4afe(0xb1),async function(_0x37e304){var _0x380bc5=_0xce4afe;console[_0x380bc5(0xc7)](_0x380bc5(0x136),_0x44e0fe,_0x552d9b,_0x2fff31),_0x37e304[_0x380bc5(0x182)](),this[_0x380bc5(0x177)]['add'](_0x380bc5(0xc9)),setTimeout(()=>{var _0x42d3de=_0x380bc5;this[_0x42d3de(0x177)][_0x42d3de(0x12c)](_0x42d3de(0xc9));},0x320);if(_0x44e0fe&&_0x552d9b&&_0x2fff31){console['log'](_0x380bc5(0x137),_0x44e0fe,_0x552d9b,_0x2fff31);isNaN(_0x552d9b)&&(console[_0x380bc5(0xc7)](_0x380bc5(0x15e),_0x552d9b),_0x552d9b=_0x552d9b[_0x380bc5(0xe7)](/[^0-9.]/g,''));var _0x1f5c26=JSON['stringify']({'title':_0x44e0fe,'price':_0x552d9b,'itemNumber':_0x2fff31});copyToClipboard(_0x1f5c26);return;}if(!_0x44e0fe||!_0x552d9b||!_0x2fff31){var _0x539e9a=getItemContainer(_0x37e304);if(!_0x539e9a)return;}var _0x59bc15=extractItemData(_0x539e9a);console['log'](_0x380bc5(0xcd),_0x59bc15);var _0x1f5c26=JSON['stringify'](_0x59bc15);copyToClipboard(_0x1f5c26);}),_0x464de3;}function copyToClipboard(_0x245093){var _0x5065c0=a0_0x39713d,_0x13385d=document[_0x5065c0(0xdf)](_0x5065c0(0xbc));document[_0x5065c0(0x108)][_0x5065c0(0x14d)](_0x13385d),_0x13385d[_0x5065c0(0xab)]=_0x245093,_0x13385d[_0x5065c0(0xef)](),document[_0x5065c0(0x19b)](_0x5065c0(0x147)),document['body'][_0x5065c0(0x99)](_0x13385d);}async function showBreakEvenPrice(_0x4bd82a=null){var _0x4238fa=a0_0x39713d;console[_0x4238fa(0xc7)](_0x4238fa(0x16e),_0x4bd82a);if(_0x4bd82a){try{_0x4bd82a=_0x4bd82a[_0x4238fa(0xe7)](/[^0-9.]/g,'');}catch(_0x609d51){}_0x4bd82a=parseFloat(_0x4bd82a);}var _0x5ea952=await calculateBreakEvenPrice(_0x4bd82a),_0x13d398=document[_0x4238fa(0xdf)](_0x4238fa(0x16b));return _0x13d398[_0x4238fa(0x155)]('id','breakEvenPrice'),_0x13d398[_0x4238fa(0x155)](_0x4238fa(0x10f),_0x4238fa(0xca)),_0x13d398[_0x4238fa(0x160)]='Break-even\x20price:\x20$'+_0x5ea952[_0x4238fa(0x15b)](0x2),_0x13d398;}async function calculateBreakEvenPrice2(_0xfed888){var _0x4818b0=a0_0x39713d,_0x2fe6e9=![],_0xcde1ac=![],{includeCurrencyConversion:_0xcde1ac}=await chrome[_0x4818b0(0xea)][_0x4818b0(0xc4)][_0x4818b0(0xfa)](_0x4818b0(0xe0)),{includeInternationalFee:_0x2fe6e9}=await chrome[_0x4818b0(0xea)][_0x4818b0(0xc4)][_0x4818b0(0xfa)](_0x4818b0(0x145));const _0x3d3e62=13.25,_0x38a0f8=0.4,_0x35526c=2.1,_0x23925b=0.4,_0x43a54b=3.5,_0xbd8eb3=_0x3d3e62/0x64,_0x5e5a0d=_0x35526c/0x64,_0x2a6c60=_0x2fe6e9?_0x23925b/0x64:0x0;let _0x4ac8fc=_0xfed888*_0xbd8eb3+_0xfed888*_0x5e5a0d+_0xfed888*_0x2a6c60+_0x38a0f8;if(_0xcde1ac){const _0x337d30=_0x43a54b/0x64;_0x4ac8fc+=_0xfed888*_0x337d30;}const _0x12155d=_0xfed888-_0x4ac8fc;return _0x12155d;}function a0_0x56bd(){var _0xccaf5=['lowest-price','save-seller\x20clicked\x20username','appendChild','search_image_on_google','productLinksModalOverlay','100%','3\x20Days\x20Button\x20Click','en-GB','.s-customize-v2\x20.lightbox-dialog__main','keywords','setAttribute','icons/people.jpg','Search\x20eBay\x20for\x20this\x20item','ebayCompetitors','doesUserHaveEbaySubscription','username\x20from\x20openItemPageThenGetSeller','toFixed','getURL','Search\x20Google\x20for\x20this\x20image','price\x20is\x20not\x20a\x20number','Get\x20similiar\x20product\x20links','textContent','Similar\x20Product\x20Links','icons/google-search-icon.png','3\x20Days','openNewTab','addEventListener','indexOf','amazonLinkFromItemNumber','Search\x20with\x20Keywords\x20Button\x20Click','observe','ebayButton\x20clicked','div','toLowerCase','Opens\x20the\x20eBay\x20seller\x27s\x20sold\x20items','price','openItemPageThenGetSeller','icons/copy.png','a-link-text\x20save-seller\x20icon\x20saveSellerLink','save-seller\x20clicked\x20username\x20already\x20exists','user_input','s-item','json','Search\x20Google\x20for\x20this\x20description','classList','domain','style','width','324584FlYqHR','https://www.ebay.','main-buttons-div','Clicked\x20the\x20customize\x20button,\x20waiting\x20for\x20form...','60\x20Days\x20Button\x20Click','checkPurchaseHistory','terapeakLink','preventDefault','sort-type','&store_name=','BuyerLocation:::CA','90\x20Days\x20Button\x20Click','Open\x20Amazon\x20Listing\x20for\x20this\x20SKU','button','terapeakLink\x20clicked','remove-seller\x20clicked\x20username','filter-type','tabName=','findSimiliarProducts','image','&_odkw=','?th=1&psc=1','https://www.amazon.','com','search_ebay_item_description_on_google','createEcommerceSearchButtonsPanel2','14\x20Days\x20Button\x20Click','isInternational','Search\x20Amazon\x20for\x20this\x20item','itemNumber','lookUpSkuLink','/?th=1&psc=1','execCommand','getElementById','removeChild','marketplace=','&LH_Sold=1','then','src','username\x20has\x20spaces\x20or\x20any\x20capital\x20letters,\x20therefor\x20its\x20a\x20store\x20name','4EPggAR','icon','saveSellerLink\x20clicked\x201','readonly','categoryId=','endsWith','includes','https://lens.google.com/uploadbyurl?url=','img','icons/graph.png','en-CA','Save\x20eBay\x20Seller','value','minPrice=','createOpenSellerItemsButton','disconnect','open_item_page_then_get_seller','America/Toronto','click','endedRecently','Details\x20form\x20did\x20not\x20appear\x20after\x20maximum\x20retries.','21\x20Days','3312048gNGgbg','1346PVwUnA','300px','dayRange=','su-card-container','join','title\x20found','textarea','runtime','1053402BHfJNN','writeText','length','170ORXGgr','message','fetchData','local','1086651aAjvxv','createCheckPurchaseHistoryButton','log','https://openai-function-call-djybcnnsgq-uc.a.run.app','clicked','break-even-price','sorting=','-itemssold','itemData','amazonSortType','purchaseHistoryLink','sortLowToHigh','amazonSearchLink','icons/sold.png','&_ipg=240&_oac=1&LH_Sold=1','CopyDataButton','response','username','top','sendMessage','remove-seller','Search\x20with\x20Title','spinner','en-US','&LH_ItemCondition=1000','.srp-view-options\x20li:nth-child(2)\x20button','createElement','includeCurrencyConversion','title','Customize\x20button\x20not\x20found.','modal-button-container','14\x20Days','saveSellerLink','querySelector','replace','/sh/research','EBAY-UK','storage','icons/save.png','.s-item__caption-section,\x20.s-item__caption--signal.POSITIVE','splice','convertToKeywords','select','search-on-amazon','lastIndexOf','copyDataLink','Search\x20with\x20Keywords','search-type','parentElement','keywords=','7\x20Days\x20Button\x20Click','SOLD','<b>\x20Search\x20on:\x20\x20</b>','get','a-link-text','You\x20can\x20now\x20perform\x20operations\x20on\x20the\x20form.','item\x20from\x20createCheckPurchaseHistoryButton','googleLink','productLinks','push','amazonSearchType','30\x20Days','title\x20not\x20found','getAttribute','save-seller','/dp/','Sort\x20by\x20Reviews\x20Button\x20Click','body','icons/terapeak.png','error','search-div','soldItems','90\x20Days','isUserTaxExempt','class','30\x20Days\x20Button\x20Click','item\x20from\x20createOpenSellerItemsButton','Snipe\x20the\x20Title\x20And\x20Price,\x20Saves\x20this\x20to\x20Clipboard','jsonPrompt','open_seller_page_from_item_number','innerText','createButtonToSaveSeller','data','1\x20Day\x20Button\x20Click','icons/floppy-disk.png','&_sop=13','icons/ebay-bag.png','Sort\x20by\x20Price\x20Button\x20Click','set','width:\x2020px;\x20height:\x2020px;\x20vertical-align:\x20middle;\x20margin-right:\x205px;','Details\x20form\x20found!','contains','data-seller-name','copy-button','7\x20Days','EBAY-CA','Search\x20Terapeak\x20for\x20this\x20item','substring','itemData\x20from\x20createOpenSellerItemsButton','Details\x20form\x20not\x20found,\x20retrying...\x20(','2913OaXOTx','totalSold','init','remove','limit=','stringify','Copy','1123836UShiMX','innerHTML','reviews','Check\x20How\x20Many\x20Sold','Search\x20eBay\x20for\x20sold\x20items','sellerItemsLink\x20clicked\x202','copyDataLink\x20clicked','title,\x20price,\x20itemNumber','icons/amazon-icon.png','clipboard','product-links-modal-overlay','1\x20Day','Search\x20with\x20Title\x20Button\x20Click','add','find_similiar_products','Copied\x20to\x20clipboard!','selectedFilter','item_title','offset=','&_sop=15','openItemPageThenSellerItemsPage','includeInternationalFee','2966735ZwueQB','copy','Failed\x20to\x20copy:','Sort\x20by\x20Highest\x20Reviews','save-seller\x20clicked'];a0_0x56bd=function(){return _0xccaf5;};return a0_0x56bd();}async function calculateBreakEvenPrice(_0x3e6eb2){var _0x54cbfe=a0_0x39713d,{isInternational:_0x437f6a}=await chrome[_0x54cbfe(0xea)][_0x54cbfe(0xc4)][_0x54cbfe(0xfa)](_0x54cbfe(0x196)),{doesUserHaveEbaySubscription:_0x18accf}=await chrome[_0x54cbfe(0xea)][_0x54cbfe(0xc4)][_0x54cbfe(0xfa)](_0x54cbfe(0x159));!_0x437f6a&&(_0x437f6a=![]);!_0x18accf&&(_0x18accf=!![]);var _0x5c0be9=13.25;_0x18accf&&(_0x5c0be9=12.35);var _0x3f89f5=0.4,_0x42a389=2.1,_0x56a7ad=0.4,_0x376e82=7.25,_0x38a760=_0x3e6eb2*(_0x376e82/0x64),_0x37c551=_0x3e6eb2+_0x38a760,_0x1c0aed=_0x37c551*(_0x5c0be9/0x64),_0x10b13b=_0x437f6a?_0x37c551*(_0x56a7ad/0x64):0x0,_0x235632=_0x1c0aed+_0x3f89f5+_0x10b13b,_0x5c5a6f=_0x437f6a?_0x235632*(0x5/0x64):0x0,_0x124ae9=_0x235632+_0x5c5a6f,_0x5e3cb5=_0x437f6a?_0x37c551*(3.5/0x64):0x0,_0x39de26=_0x3e6eb2-_0x124ae9-_0x5e3cb5,{isUserTaxExempt:_0x4c3ae0}=await chrome[_0x54cbfe(0xea)][_0x54cbfe(0xc4)][_0x54cbfe(0xfa)](_0x54cbfe(0x10e));!_0x4c3ae0&&(_0x4c3ae0=![]);if(!_0x4c3ae0){var _0x129c8b=_0x376e82,_0x1fa530=_0x39de26/(0x1+_0x129c8b/0x64);_0x39de26=_0x1fa530;}return _0x437f6a&&(_0x39de26=_0x39de26-_0x39de26*3.5/0x64),_0x39de26;}function createButtonToSaveSeller(_0x429d2d=null){var _0x2f4b67=a0_0x39713d;console['log'](_0x2f4b67(0x116),_0x429d2d);var _0xe00beb=document[_0x2f4b67(0xdf)]('a');_0xe00beb['setAttribute']('id',_0x2f4b67(0xe5)),_0xe00beb[_0x2f4b67(0x155)](_0x2f4b67(0x10f),_0x2f4b67(0x171)),_0xe00beb[_0x2f4b67(0x155)]('title',_0x2f4b67(0xaa));var _0x4c8d86=document[_0x2f4b67(0xdf)]('img');return _0x4c8d86[_0x2f4b67(0x155)]('src',chrome['runtime'][_0x2f4b67(0x15c)](_0x2f4b67(0x119))),_0x4c8d86['setAttribute'](_0x2f4b67(0x179),_0x2f4b67(0x11e)),_0xe00beb[_0x2f4b67(0x14d)](_0x4c8d86),_0xe00beb[_0x2f4b67(0x165)](_0x2f4b67(0xb1),async function(_0x18ffe5){var _0x1e7d3c=_0x2f4b67;_0x18ffe5[_0x1e7d3c(0x182)](),this['classList'][_0x1e7d3c(0x13d)](_0x1e7d3c(0xc9)),setTimeout(()=>{var _0x518891=_0x1e7d3c;this[_0x518891(0x177)]['remove']('clicked');},0x320),console[_0x1e7d3c(0xc7)](_0x1e7d3c(0xa1),_0x429d2d);var _0x596a1a;if(!_0x429d2d){var _0x2696cd=getItemContainer(_0x18ffe5);if(!_0x2696cd)return;var _0xbf0f1e=extractItemData(_0x2696cd);_0x429d2d=_0xbf0f1e['username'],_0x596a1a=_0xbf0f1e[_0x1e7d3c(0x198)];}_0x429d2d[_0x1e7d3c(0xa5)]('\x20')||_0x429d2d!==_0x429d2d[_0x1e7d3c(0x16c)]()?(console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x9e),_0x429d2d),_0x429d2d=await openItemPageThenGetSeller(_0x596a1a),console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x15a),_0x429d2d)):_0x429d2d=_0x429d2d['toLowerCase']();_0xe00beb[_0x1e7d3c(0x155)](_0x1e7d3c(0x121),_0x429d2d);var {ebayCompetitors:_0x5d1398}=await chrome[_0x1e7d3c(0xea)][_0x1e7d3c(0xc4)][_0x1e7d3c(0xfa)](_0x1e7d3c(0x158));_0x5d1398=_0x5d1398||[];var _0x5319cd=_0x5d1398['indexOf'](_0x429d2d);console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x158),_0x5d1398);if(this[_0x1e7d3c(0x177)][_0x1e7d3c(0x120)]('save-seller'))console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x14a)),_0x5319cd===-0x1?(console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x14c),_0x429d2d),_0x5d1398[_0x1e7d3c(0x100)](_0x429d2d),this[_0x1e7d3c(0x177)][_0x1e7d3c(0xe7)](_0x1e7d3c(0x105),_0x1e7d3c(0xd9)),_0x4c8d86[_0x1e7d3c(0x155)](_0x1e7d3c(0x9d),chrome[_0x1e7d3c(0xbd)][_0x1e7d3c(0x15c)](_0x1e7d3c(0xeb)))):(console['log'](_0x1e7d3c(0x172),_0x429d2d),this['classList'][_0x1e7d3c(0xe7)](_0x1e7d3c(0x105),_0x1e7d3c(0xd9)),_0x4c8d86['setAttribute'](_0x1e7d3c(0x9d),chrome[_0x1e7d3c(0xbd)][_0x1e7d3c(0x15c)](_0x1e7d3c(0xeb))));else this['classList'][_0x1e7d3c(0x120)](_0x1e7d3c(0xd9))&&(_0x5319cd!==-0x1?(console[_0x1e7d3c(0xc7)](_0x1e7d3c(0x18a),_0x429d2d),_0x5d1398[_0x1e7d3c(0xed)](_0x5319cd,0x1),this['classList'][_0x1e7d3c(0xe7)](_0x1e7d3c(0xd9),_0x1e7d3c(0x105)),_0x4c8d86[_0x1e7d3c(0x155)](_0x1e7d3c(0x9d),chrome['runtime'][_0x1e7d3c(0x15c)](_0x1e7d3c(0x119)))):console[_0x1e7d3c(0xc7)]('remove-seller\x20clicked\x20username\x20not\x20found',_0x429d2d));await chrome['storage'][_0x1e7d3c(0xc4)][_0x1e7d3c(0x11d)]({'ebayCompetitors':_0x5d1398});}),_0xe00beb;}async function updateSellerButton(_0xd35348,_0xd4673){var _0x34b472=a0_0x39713d,{ebayCompetitors:_0x2fc0a5}=await chrome[_0x34b472(0xea)][_0x34b472(0xc4)][_0x34b472(0xfa)](_0x34b472(0x158));_0x2fc0a5=_0x2fc0a5||[];var _0x46629b=_0x2fc0a5[_0x34b472(0x166)](_0xd4673),_0x4d5d88=_0xd35348[_0x34b472(0xe6)](_0x34b472(0xa7));_0x46629b!==-0x1?(_0xd35348[_0x34b472(0x177)][_0x34b472(0xe7)]('save-seller','remove-seller'),_0x4d5d88[_0x34b472(0x155)](_0x34b472(0x9d),chrome[_0x34b472(0xbd)]['getURL'](_0x34b472(0xeb)))):(_0xd35348['classList'][_0x34b472(0xe7)](_0x34b472(0xd9),_0x34b472(0x105)),_0x4d5d88['setAttribute']('src',chrome[_0x34b472(0xbd)][_0x34b472(0x15c)](_0x34b472(0x119))));}function createEcommerceSearchButtonsPanel2(_0x3d33fa=null,_0xeb18d9=null,_0x2ac48f=null,_0x10d706=!![],_0x5993cd=null,_0x276d36=null){var _0x8a2bfb=a0_0x39713d;console['log'](_0x8a2bfb(0x194)),console[_0x8a2bfb(0xc7)](_0x8a2bfb(0xe1),_0x3d33fa);var _0x310033=createButtonToSaveSeller(_0xeb18d9),_0x594a60=createSearchTerapeakButton(_0x3d33fa),_0x176649=createCheckPurchaseHistoryButton(_0x5993cd),_0x583854=createEbaySearchButton(_0x3d33fa),_0x5e95b0=createAmazonSearchButton(_0x3d33fa,_0x276d36,_0x5993cd,_0x2ac48f),_0x132a5f=createEbaySearchButton(_0x3d33fa,{'soldItems':!![],'endedRecently':!![]},_0x8a2bfb(0xd2)),_0x4aec8b=createGoogleImageSearchButton(_0x2ac48f),_0x50859c=createGoogleSearchDescriptionButton(_0x5993cd),_0x8603c4=createOpenSellerItemsButton(_0xeb18d9),_0x1ac483=document[_0x8a2bfb(0xdf)]('div');_0x1ac483['setAttribute']('id',_0x8a2bfb(0x10b));var _0x188d0b=document['createElement']('label');_0x188d0b[_0x8a2bfb(0x131)]=_0x8a2bfb(0xf9),_0x1ac483['appendChild'](_0x188d0b),_0x1ac483['appendChild'](_0x5e95b0),_0x1ac483[_0x8a2bfb(0x14d)](_0x583854),_0x1ac483['appendChild'](_0x594a60),_0x1ac483[_0x8a2bfb(0x14d)](_0x4aec8b),_0x1ac483[_0x8a2bfb(0x14d)](_0x50859c),_0x1ac483[_0x8a2bfb(0x14d)](_0x132a5f),console['log'](_0x8a2bfb(0xd4),_0x3d33fa,_0x276d36,_0x5993cd);var _0x1099f4=createButtonToCopyData(_0x3d33fa,_0x276d36,_0x5993cd),_0x5e2e4d=document[_0x8a2bfb(0xdf)](_0x8a2bfb(0x16b));_0x5e2e4d[_0x8a2bfb(0x155)]('id','item-buttons-div');var _0x3ee70e=document['createElement']('div');_0x3ee70e[_0x8a2bfb(0x155)]('id',_0x8a2bfb(0x17d)),_0x3ee70e[_0x8a2bfb(0x14d)](_0x8603c4),_0x3ee70e[_0x8a2bfb(0x14d)](_0x176649),_0x3ee70e[_0x8a2bfb(0x14d)](_0x1099f4),_0x3ee70e[_0x8a2bfb(0x14d)](_0x310033),_0x5e2e4d[_0x8a2bfb(0x14d)](_0x3ee70e);if(_0x10d706){var _0x5add15=createButtonListToEbay();_0x5e2e4d['appendChild'](_0x5add15);}return _0x5e2e4d['appendChild'](_0x1ac483),_0x5e2e4d;}var terapeakMenuItems=[{'content':a0_0x39713d(0xda),'selected':![],'id':a0_0x39713d(0xf4),'value':'title','events':{'click':_0x49d2c1=>{var _0x1af4b6=a0_0x39713d;console[_0x1af4b6(0xc7)](_0x49d2c1,_0x1af4b6(0x13c)),chrome['storage']['local'][_0x1af4b6(0x11d)]({'convertToKeywords':![]}),updateContextMenu(terapeakMenuItems,_0x1af4b6(0xf4),_0x1af4b6(0xe1));}}},{'content':'Search\x20with\x20Keywords','selected':![],'id':'search-type','value':a0_0x39713d(0x154),'events':{'click':_0x9411da=>{var _0xc21330=a0_0x39713d;console[_0xc21330(0xc7)](_0x9411da,_0xc21330(0x168)),chrome['storage'][_0xc21330(0xc4)][_0xc21330(0x11d)]({'convertToKeywords':!![]}),updateContextMenu(terapeakMenuItems,_0xc21330(0xf4),_0xc21330(0x154));}}}];async function initTerapeakOptionsContextMenu(){var _0x34879c=a0_0x39713d,{convertToKeywords:_0x3f704e}=await chrome['storage'][_0x34879c(0xc4)]['get'](_0x34879c(0xee));!_0x3f704e&&(_0x3f704e=![],await chrome[_0x34879c(0xea)][_0x34879c(0xc4)][_0x34879c(0x11d)]({'convertToKeywords':![]}));updateContextMenu(terapeakMenuItems,_0x34879c(0xf4),_0x3f704e?_0x34879c(0x154):'title');var _0x4ee90b=new ContextMenu({'target':'.terapeakLink','menuItems':terapeakMenuItems});_0x4ee90b[_0x34879c(0x12b)]();}var amznMenuItems=[{'id':a0_0x39713d(0x183),'value':a0_0x39713d(0x132),'content':a0_0x39713d(0x149),'selected':![],'events':{'click':_0x14b8c4=>{var _0x1ea01b=a0_0x39713d;console['log'](_0x14b8c4,'Sort\x20by\x20Reviews\x20Button\x20Click'),chrome[_0x1ea01b(0xea)][_0x1ea01b(0xc4)][_0x1ea01b(0x11d)]({'amazonSortType':_0x1ea01b(0x132)}),updateContextMenu(amznMenuItems,_0x1ea01b(0x183),_0x1ea01b(0x132));}}},{'id':'sort-type','value':'lowest-price','content':'Sort\x20By\x20Lowest\x20Price','selected':![],'events':{'click':_0x5728d1=>{var _0x2d99d9=a0_0x39713d;console[_0x2d99d9(0xc7)](_0x5728d1,_0x2d99d9(0x11c)),chrome['storage']['local'][_0x2d99d9(0x11d)]({'amazonSortType':_0x2d99d9(0x14b)}),updateContextMenu(amznMenuItems,_0x2d99d9(0x183),_0x2d99d9(0x14b));}}},{'divider':a0_0x39713d(0xd7),'id':a0_0x39713d(0xf4),'value':a0_0x39713d(0xe1),'content':a0_0x39713d(0xda),'selected':![],'events':{'click':_0x5f2adb=>{var _0xf89af6=a0_0x39713d;console[_0xf89af6(0xc7)](_0x5f2adb,_0xf89af6(0x13c)),chrome['storage'][_0xf89af6(0xc4)][_0xf89af6(0x11d)]({'amazonSearchType':_0xf89af6(0xe1)}),updateContextMenu(amznMenuItems,_0xf89af6(0xf4),_0xf89af6(0xe1));}}},{'id':'search-type','value':'keywords','content':a0_0x39713d(0xf3),'selected':![],'events':{'click':_0x2d2c10=>{var _0x43bdb8=a0_0x39713d;console[_0x43bdb8(0xc7)](_0x2d2c10,_0x43bdb8(0x168)),chrome[_0x43bdb8(0xea)][_0x43bdb8(0xc4)][_0x43bdb8(0x11d)]({'amazonSearchType':'keywords'}),updateContextMenu(amznMenuItems,_0x43bdb8(0xf4),'keywords');}}}];async function initAmazonSortTypeContextMenu(){var _0x10785d=a0_0x39713d,{amazonSortType:_0x35693b}=await chrome[_0x10785d(0xea)]['local'][_0x10785d(0xfa)](_0x10785d(0xce)),{amazonSearchType:_0x271343}=await chrome[_0x10785d(0xea)]['local']['get'](_0x10785d(0x101));!_0x271343&&(_0x271343=_0x10785d(0xe1),await chrome[_0x10785d(0xea)][_0x10785d(0xc4)][_0x10785d(0x11d)]({'amazonSearchType':_0x10785d(0xe1)}));!_0x35693b&&(_0x35693b='reviews',await chrome[_0x10785d(0xea)][_0x10785d(0xc4)][_0x10785d(0x11d)]({'amazonSortType':_0x10785d(0x132)}));updateContextMenu(amznMenuItems,_0x10785d(0x183),_0x35693b),updateContextMenu(amznMenuItems,_0x10785d(0xf4),_0x271343);var _0x130378=new ContextMenu({'target':'.amazonSearchLink','menuItems':amznMenuItems});_0x130378[_0x10785d(0x12b)]();}var amznMenuItems=[{'id':a0_0x39713d(0x183),'value':a0_0x39713d(0x132),'content':a0_0x39713d(0x149),'selected':![],'events':{'click':_0x2f0901=>{var _0x5eeaf1=a0_0x39713d;console[_0x5eeaf1(0xc7)](_0x2f0901,_0x5eeaf1(0x107)),chrome[_0x5eeaf1(0xea)][_0x5eeaf1(0xc4)][_0x5eeaf1(0x11d)]({'amazonSortType':_0x5eeaf1(0x132)}),updateContextMenu(amznMenuItems,_0x5eeaf1(0x183),_0x5eeaf1(0x132));}}},{'id':a0_0x39713d(0x183),'value':a0_0x39713d(0x14b),'content':'Sort\x20By\x20Lowest\x20Price','selected':![],'events':{'click':_0x4860da=>{var _0x192866=a0_0x39713d;console[_0x192866(0xc7)](_0x4860da,_0x192866(0x11c)),chrome[_0x192866(0xea)][_0x192866(0xc4)]['set']({'amazonSortType':_0x192866(0x14b)}),updateContextMenu(amznMenuItems,_0x192866(0x183),_0x192866(0x14b));}}}],dateFilterMenuItems=[{'id':a0_0x39713d(0x18b),'value':'1','content':a0_0x39713d(0x13b),'selected':![],'events':{'click':_0x2b94dc=>{var _0x213479=a0_0x39713d;console[_0x213479(0xc7)](_0x2b94dc,_0x213479(0x118)),chrome[_0x213479(0xea)][_0x213479(0xc4)][_0x213479(0x11d)]({'selectedFilter':'1'}),updateContextMenu(dateFilterMenuItems,_0x213479(0x18b),'1');}}},{'id':a0_0x39713d(0x18b),'value':'3','content':a0_0x39713d(0x163),'selected':![],'events':{'click':_0xc7c89d=>{var _0x2dc5f8=a0_0x39713d;console['log'](_0xc7c89d,_0x2dc5f8(0x151)),chrome[_0x2dc5f8(0xea)][_0x2dc5f8(0xc4)][_0x2dc5f8(0x11d)]({'selectedFilter':'3'}),updateContextMenu(dateFilterMenuItems,_0x2dc5f8(0x18b),'3');}}},{'id':'filter-type','value':'7','content':a0_0x39713d(0x123),'selected':![],'events':{'click':_0x427dfc=>{var _0x11e484=a0_0x39713d;console[_0x11e484(0xc7)](_0x427dfc,_0x11e484(0xf7)),chrome[_0x11e484(0xea)][_0x11e484(0xc4)]['set']({'selectedFilter':'7'}),updateContextMenu(dateFilterMenuItems,_0x11e484(0x18b),'7');}}},{'id':'filter-type','value':'14','content':a0_0x39713d(0xe4),'selected':![],'events':{'click':_0x17282d=>{var _0xf86559=a0_0x39713d;console[_0xf86559(0xc7)](_0x17282d,_0xf86559(0x195)),chrome[_0xf86559(0xea)][_0xf86559(0xc4)][_0xf86559(0x11d)]({'selectedFilter':'14'}),updateContextMenu(dateFilterMenuItems,'filter-type','14');}}},{'id':a0_0x39713d(0x18b),'value':'21','content':a0_0x39713d(0xb4),'selected':![],'events':{'click':_0x4192db=>{var _0x2a28dc=a0_0x39713d;console[_0x2a28dc(0xc7)](_0x4192db,'21\x20Days\x20Button\x20Click'),chrome['storage'][_0x2a28dc(0xc4)]['set']({'selectedFilter':'21'}),updateContextMenu(dateFilterMenuItems,_0x2a28dc(0x18b),'21');}}},{'id':'filter-type','value':'30','content':a0_0x39713d(0x102),'selected':![],'events':{'click':_0x47c5da=>{var _0x135250=a0_0x39713d;console[_0x135250(0xc7)](_0x47c5da,_0x135250(0x110)),chrome[_0x135250(0xea)][_0x135250(0xc4)][_0x135250(0x11d)]({'selectedFilter':'30'}),updateContextMenu(dateFilterMenuItems,_0x135250(0x18b),'30');}}},{'id':a0_0x39713d(0x18b),'value':'60','content':'60\x20Days','selected':![],'events':{'click':_0x3fe777=>{var _0xd9866=a0_0x39713d;console[_0xd9866(0xc7)](_0x3fe777,_0xd9866(0x17f)),chrome['storage'][_0xd9866(0xc4)][_0xd9866(0x11d)]({'selectedFilter':'60'}),updateContextMenu(dateFilterMenuItems,_0xd9866(0x18b),'60');}}},{'id':a0_0x39713d(0x18b),'value':'90','content':a0_0x39713d(0x10d),'selected':![],'events':{'click':_0x3d74a2=>{var _0x584e65=a0_0x39713d;console[_0x584e65(0xc7)](_0x3d74a2,_0x584e65(0x186)),chrome['storage'][_0x584e65(0xc4)][_0x584e65(0x11d)]({'selectedFilter':'90'}),updateContextMenu(dateFilterMenuItems,'filter-type','90');}}}];async function initDateFilterContextMenu(){var _0x819d8=a0_0x39713d,{selectedFilter:_0x56d447}=await chrome['storage'][_0x819d8(0xc4)]['get'](_0x819d8(0x140));!_0x56d447&&(_0x56d447='90',await chrome[_0x819d8(0xea)]['local']['set']({'selectedFilter':'90'}));updateContextMenu(dateFilterMenuItems,'filter-type',_0x56d447);var _0x523ff3=new ContextMenu({'target':'.filter-date-context','menuItems':dateFilterMenuItems});_0x523ff3['init']();}function getSoldCaptionSelector(){var _0x45dcdd=a0_0x39713d,_0x416cdf=_0x45dcdd(0xec);return _0x416cdf;}async function tickSearchOptions(){var _0x468e1a=a0_0x39713d;const _0xc17080=document['querySelector'](_0x468e1a(0x153)),_0xe5d753=_0x468e1a(0xde);let _0x32f288=0x0;const _0x2acb6a=0x3,_0x1e79a1=()=>new Promise((_0x363ff7,_0x405b9d)=>{var _0x4adf30=_0x468e1a;const _0x483eca=new MutationObserver((_0x2cea08,_0x126e8e)=>{var _0x13248b=a0_0x5ad0;const _0x5134fb=document['querySelector']('.s-customize-form__details');_0x5134fb&&(console['log'](_0x13248b(0x11f)),_0x126e8e[_0x13248b(0xae)](),_0x363ff7(_0x5134fb));});_0x483eca[_0x4adf30(0x169)](_0xc17080,{'childList':!![],'subtree':!![],'attributes':![]}),setTimeout(()=>{var _0x43ab92=_0x4adf30;_0x483eca[_0x43ab92(0xae)](),_0x32f288<_0x2acb6a?(console[_0x43ab92(0xc7)](_0x43ab92(0x128)+(_0x32f288+0x1)+'/'+_0x2acb6a+')'),_0x32f288++,document[_0x43ab92(0xe6)](_0xe5d753)?.[_0x43ab92(0xb1)](),setTimeout(()=>_0x363ff7(_0x1e79a1()),0x1388)):_0x405b9d(new Error(_0x43ab92(0xb3)));},0x4e20);});var _0x5cf40a=document[_0x468e1a(0xe6)](_0xe5d753);if(_0x5cf40a){_0x5cf40a[_0x468e1a(0xb1)](),console[_0x468e1a(0xc7)](_0x468e1a(0x17e));try{const _0x2af78c=await _0x1e79a1();console[_0x468e1a(0xc7)](_0x468e1a(0xfc));}catch(_0x3cf799){console[_0x468e1a(0x10a)](_0x3cf799[_0x468e1a(0xc2)]);}}else console['error'](_0x468e1a(0xe2));}function createSnipeButton(_0xb8d503=null,_0x243f4b=null,_0x2882e4=null){var _0x4e3bd6=a0_0x39713d,_0x23628e=document[_0x4e3bd6(0xdf)]('a');_0x23628e[_0x4e3bd6(0x155)]('id',_0x4e3bd6(0xf2)),_0x23628e[_0x4e3bd6(0x155)](_0x4e3bd6(0x10f),'a-link-text'),_0x23628e[_0x4e3bd6(0x177)][_0x4e3bd6(0x13d)](_0x4e3bd6(0xa0)),_0x23628e[_0x4e3bd6(0x155)](_0x4e3bd6(0xe1),_0x4e3bd6(0x15f));var _0x56c9ff=document[_0x4e3bd6(0xdf)](_0x4e3bd6(0xa7));return _0x56c9ff[_0x4e3bd6(0x155)](_0x4e3bd6(0x9d),chrome[_0x4e3bd6(0xbd)][_0x4e3bd6(0x15c)]('icons/ecomsniper.png')),_0x56c9ff[_0x4e3bd6(0x155)]('style','width:\x2020px;\x20height:\x2020px;\x20vertical-align:\x20middle;\x20margin-right:\x205px;'),_0x23628e[_0x4e3bd6(0x14d)](_0x56c9ff),_0x23628e['addEventListener'](_0x4e3bd6(0xb1),async function(_0x334aaa){var _0x4369eb=_0x4e3bd6;console['log'](_0x4369eb(0x136),_0xb8d503,_0x243f4b,_0x2882e4),_0x334aaa[_0x4369eb(0x182)](),this[_0x4369eb(0x177)][_0x4369eb(0x13d)](_0x4369eb(0xdb));if(_0xb8d503&&_0x243f4b&&_0x2882e4){console[_0x4369eb(0xc7)]('title,\x20price,\x20itemNumber',_0xb8d503,_0x243f4b,_0x2882e4);isNaN(_0x243f4b)&&(console[_0x4369eb(0xc7)](_0x4369eb(0x15e),_0x243f4b),_0x243f4b=_0x243f4b[_0x4369eb(0xe7)](/[^0-9.]/g,''));var _0x284e2c=JSON[_0x4369eb(0x12e)]({'title':_0xb8d503,'price':_0x243f4b,'itemNumber':_0x2882e4}),_0x46a561=await findSimiliarProducts(_0x284e2c),_0x104a4c=_0x46a561[_0x4369eb(0xba)]('\x0a');showProductLinksModal(_0x104a4c),console['log'](_0x4369eb(0xff),_0x46a561),this[_0x4369eb(0x177)][_0x4369eb(0x12c)]('spinner');return;}if(!_0xb8d503||!_0x243f4b||!_0x2882e4){var _0xe87d0a=getItemContainer(_0x334aaa);if(!_0xe87d0a)return;}var _0x3eaf80=extractItemData(_0xe87d0a);console[_0x4369eb(0xc7)](_0x4369eb(0xcd),_0x3eaf80);var _0x284e2c=JSON['stringify'](_0x3eaf80),_0x46a561=await findSimiliarProducts(_0x284e2c),_0x104a4c=_0x46a561[_0x4369eb(0xba)]('\x0a');showProductLinksModal(_0x104a4c),console['log'](_0x4369eb(0xff),_0x46a561),this[_0x4369eb(0x177)][_0x4369eb(0x12c)](_0x4369eb(0xdb));}),_0x23628e;}async function findSimiliarProducts(_0x57853a){var _0x1dc1f=a0_0x39713d;console[_0x1dc1f(0xc7)](_0x1dc1f(0x18d),_0x57853a);var _0x51510e=await chrome['runtime'][_0x1dc1f(0xd8)]({'type':_0x1dc1f(0x13e),'jsonString':_0x57853a});return console[_0x1dc1f(0xc7)](_0x1dc1f(0xd5),_0x51510e),_0x51510e[_0x1dc1f(0xff)];}function showProductLinksModal(_0x514307){var _0x4f04a1=a0_0x39713d;const _0x11f553=document[_0x4f04a1(0x19c)](_0x4f04a1(0x14f));_0x11f553&&_0x11f553['remove']();const _0x772afe=document[_0x4f04a1(0xdf)](_0x4f04a1(0x16b));_0x772afe['id']='productLinksModalOverlay',_0x772afe[_0x4f04a1(0x177)][_0x4f04a1(0x13d)](_0x4f04a1(0x13a));const _0x4791ba=document['createElement']('div');_0x4791ba[_0x4f04a1(0x177)]['add']('product-links-modal');const _0x57fae7=document['createElement'](_0x4f04a1(0x16b));_0x57fae7[_0x4f04a1(0x177)][_0x4f04a1(0x13d)](_0x4f04a1(0xe3));const _0x5a6eef=document[_0x4f04a1(0xdf)](_0x4f04a1(0x188));_0x5a6eef[_0x4f04a1(0x177)][_0x4f04a1(0x13d)]('close-button'),_0x5a6eef[_0x4f04a1(0x115)]='Close',_0x5a6eef[_0x4f04a1(0x165)](_0x4f04a1(0xb1),()=>{_0x772afe['remove']();});const _0x3fa845=document['createElement'](_0x4f04a1(0x188));_0x3fa845[_0x4f04a1(0x177)]['add'](_0x4f04a1(0x122)),_0x3fa845[_0x4f04a1(0x115)]=_0x4f04a1(0x12f),_0x3fa845[_0x4f04a1(0x165)](_0x4f04a1(0xb1),async()=>{var _0x2ac2fb=_0x4f04a1;try{await navigator[_0x2ac2fb(0x139)][_0x2ac2fb(0xbf)](_0x514307),alert(_0x2ac2fb(0x13f));}catch(_0x4bec16){console[_0x2ac2fb(0x10a)](_0x2ac2fb(0x148),_0x4bec16);}});const _0x161d2b=document['createElement']('h2');_0x161d2b[_0x4f04a1(0x115)]=_0x4f04a1(0x161);const _0x3b70fb=document[_0x4f04a1(0xdf)](_0x4f04a1(0xbc));_0x3b70fb['value']=_0x514307,_0x3b70fb[_0x4f04a1(0x155)](_0x4f04a1(0xa2),!![]),_0x3b70fb['style'][_0x4f04a1(0x17a)]=_0x4f04a1(0x150),_0x3b70fb[_0x4f04a1(0x179)]['height']=_0x4f04a1(0xb7),_0x57fae7[_0x4f04a1(0x14d)](_0x3fa845),_0x57fae7[_0x4f04a1(0x14d)](_0x5a6eef),_0x4791ba[_0x4f04a1(0x14d)](_0x57fae7),_0x4791ba[_0x4f04a1(0x14d)](_0x161d2b),_0x4791ba[_0x4f04a1(0x14d)](_0x3b70fb),_0x772afe[_0x4f04a1(0x14d)](_0x4791ba),document['body']['appendChild'](_0x772afe);}