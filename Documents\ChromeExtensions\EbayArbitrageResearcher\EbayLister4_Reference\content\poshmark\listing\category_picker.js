const a0_0x379a5d=a0_0x37af;function a0_0x45fb(){const _0x2392cf=['Jewelry','Skirts','Dog','Wall\x20Decor','Bath,\x20Skin\x20&\x20Hair','3925974EyAhKa','fetchData','Shorts','Bath','959570SBlgHg','Cat','Accents','Art','Shoes','user_input','Kitchen','Jeans','then','Men','Storage\x20&\x20Organization','1571697tULMNP','Party\x20Supplies','Cameras,\x20Photo\x20&\x20Video','Swim','Cell\x20Phones\x20&\x20Accessories','output','Media','Bottoms','Makeup','Tablets\x20&\x20Accessories','Skincare','Jackets\x20&\x20Coats','Kids','Pants\x20&\x20Jumpsuits','enum','VR,\x20AR\x20&\x20Accessories','8IoebNk','lastError','Grooming','Networking','Fish','json','includes','62898fPacdG','Accessories','length','Tops','Pants','Computers,\x20Laptops\x20&\x20Parts','jsonPrompt','Headphones','Small\x20Pets','728148lIuZGZ','properties','Wearables','Hair','Shirts','prompts_json/get_subcategory.json','Women','One\x20Pieces','Bags','Suits\x20&\x20Blazers','Bath\x20&\x20Body','runtime','Global\x20&\x20Traditional\x20Wear','Intimates\x20&\x20Sleepwear','Reptile','toLowerCase','Shirts\x20&\x20Tops','Sweaters','Holiday','Dining','Electronics','https://openai-function-call-djybcnnsgq-uc.a.run.app','38DZruwb','log','484680KzdFlo','Office','Portable\x20Audio\x20&\x20Video','Pets','data','Other','Car\x20Audio,\x20Video\x20&\x20GPS','prompts_json/get_main_category.json','Pajamas','Video\x20Games\x20&\x20Consoles','Toys','5jCCFYO','Bedding','Costumes','Dresses','4127515dvGrJH','Design','getURL','parse','Underwear\x20&\x20Socks','66DDhzWV','Matching\x20Sets','Home'];a0_0x45fb=function(){return _0x2392cf;};return a0_0x45fb();}(function(_0x286540,_0x489555){const _0x2fc219=a0_0x37af,_0x2f0397=_0x286540();while(!![]){try{const _0x26a53d=-parseInt(_0x2fc219(0x231))/0x1+parseInt(_0x2fc219(0x247))/0x2*(parseInt(_0x2fc219(0x228))/0x3)+-parseInt(_0x2fc219(0x249))/0x4*(parseInt(_0x2fc219(0x1f1))/0x5)+parseInt(_0x2fc219(0x202))/0x6+parseInt(_0x2fc219(0x1f5))/0x7*(-parseInt(_0x2fc219(0x221))/0x8)+parseInt(_0x2fc219(0x211))/0x9+-parseInt(_0x2fc219(0x206))/0xa*(-parseInt(_0x2fc219(0x1fa))/0xb);if(_0x26a53d===_0x489555)break;else _0x2f0397['push'](_0x2f0397['shift']());}catch(_0x1978ef){_0x2f0397['push'](_0x2f0397['shift']());}}}(a0_0x45fb,0x58e3f));function mapToPoshmarkMainCategory(_0x5129c2){const _0x56f51a=a0_0x37af;var _0x5aa505=[_0x56f51a(0x237),_0x56f51a(0x20f),'Kids','Home',_0x56f51a(0x24c),_0x56f51a(0x245)];for(let _0x472d50=0x0;_0x472d50<_0x5aa505[_0x56f51a(0x22a)];_0x472d50++){const _0x50f49b=_0x5aa505[_0x472d50][_0x56f51a(0x240)]();for(let _0x1786c6=0x0;_0x1786c6<_0x5129c2[_0x56f51a(0x22a)];_0x1786c6++){const _0x3be192=_0x5129c2[_0x1786c6][_0x56f51a(0x240)]();if(_0x3be192['includes'](_0x50f49b))return _0x5aa505[_0x472d50];}}return null;}const mainCategories=[a0_0x379a5d(0x237),a0_0x379a5d(0x20f),a0_0x379a5d(0x21d),a0_0x379a5d(0x1fc),a0_0x379a5d(0x24c),a0_0x379a5d(0x245)],womenSubcategories=[a0_0x379a5d(0x229),a0_0x379a5d(0x239),'Dresses','Intimates\x20&\x20Sleepwear',a0_0x379a5d(0x21c),a0_0x379a5d(0x20d),a0_0x379a5d(0x1fd),a0_0x379a5d(0x219),a0_0x379a5d(0x21e),a0_0x379a5d(0x20a),a0_0x379a5d(0x204),a0_0x379a5d(0x1fe),'Sweaters','Swim','Tops',a0_0x379a5d(0x21b),'Hair',a0_0x379a5d(0x23b),a0_0x379a5d(0x23d),a0_0x379a5d(0x1eb)],menSubcategories=['Accessories','Bags',a0_0x379a5d(0x21c),a0_0x379a5d(0x20d),a0_0x379a5d(0x22c),a0_0x379a5d(0x235),a0_0x379a5d(0x20a),a0_0x379a5d(0x204),a0_0x379a5d(0x23a),a0_0x379a5d(0x242),a0_0x379a5d(0x214),a0_0x379a5d(0x1f9),a0_0x379a5d(0x223),'Global\x20&\x20Traditional\x20Wear',a0_0x379a5d(0x1eb)],kidsSubcategories=['Accessories',a0_0x379a5d(0x218),a0_0x379a5d(0x1f4),a0_0x379a5d(0x21c),a0_0x379a5d(0x1fb),a0_0x379a5d(0x238),'Pajamas',a0_0x379a5d(0x241),a0_0x379a5d(0x20a),a0_0x379a5d(0x214),a0_0x379a5d(0x1f3),'Bath,\x20Skin\x20&\x20Hair',a0_0x379a5d(0x1f0),a0_0x379a5d(0x1eb)],homeSubcategories=[a0_0x379a5d(0x208),a0_0x379a5d(0x209),a0_0x379a5d(0x205),a0_0x379a5d(0x1f2),'Design',a0_0x379a5d(0x244),'Games',a0_0x379a5d(0x243),'Kitchen',a0_0x379a5d(0x24a),a0_0x379a5d(0x212),a0_0x379a5d(0x210),a0_0x379a5d(0x200),a0_0x379a5d(0x1eb)],petsSubcategories=[a0_0x379a5d(0x1ff),a0_0x379a5d(0x207),'Bird',a0_0x379a5d(0x225),'Reptile',a0_0x379a5d(0x230),a0_0x379a5d(0x1eb)],electronicsSubcategories=[a0_0x379a5d(0x213),'Computers,\x20Laptops\x20&\x20Parts',a0_0x379a5d(0x215),'Car\x20Audio,\x20Video\x20&\x20GPS',a0_0x379a5d(0x233),a0_0x379a5d(0x21a),a0_0x379a5d(0x1ef),a0_0x379a5d(0x220),a0_0x379a5d(0x217),a0_0x379a5d(0x224),'Headphones',a0_0x379a5d(0x24b),a0_0x379a5d(0x1eb)];function a0_0x37af(_0x1ae065,_0x16da2b){const _0x45fb51=a0_0x45fb();return a0_0x37af=function(_0x37af2f,_0x15995f){_0x37af2f=_0x37af2f-0x1eb;let _0x3b1a91=_0x45fb51[_0x37af2f];return _0x3b1a91;},a0_0x37af(_0x1ae065,_0x16da2b);}async function getMainCategory(_0x1ea880){const _0x37b589=a0_0x379a5d;var _0x54ee14=await fetch(chrome[_0x37b589(0x23c)][_0x37b589(0x1f7)](_0x37b589(0x1ed)))['then'](_0xe6eeb9=>_0xe6eeb9[_0x37b589(0x226)]());_0x54ee14[_0x37b589(0x20b)]=_0x1ea880,console['log'](_0x37b589(0x22e),_0x54ee14);var _0x514aad=await new Promise((_0xd5d582,_0x52297e)=>{const _0x268682=_0x37b589;chrome[_0x268682(0x23c)]['sendMessage']({'type':_0x268682(0x203),'url':'https://openai-function-call-djybcnnsgq-uc.a.run.app','data':_0x54ee14},function(_0x9a7bcc){const _0x47fae6=_0x268682;chrome[_0x47fae6(0x23c)][_0x47fae6(0x222)]?_0x52297e(chrome[_0x47fae6(0x23c)]['lastError']):_0xd5d582(_0x9a7bcc[_0x47fae6(0x24d)]);});});console[_0x37b589(0x248)](_0x37b589(0x24d),_0x514aad),_0x514aad=JSON[_0x37b589(0x1f8)](_0x514aad);var _0x1d3b56=_0x514aad[_0x37b589(0x216)];const _0x917142=[_0x37b589(0x237),'Men',_0x37b589(0x21d),_0x37b589(0x1fc),_0x37b589(0x24c),'Electronics'];return!_0x917142['includes'](_0x1d3b56)&&(_0x1d3b56=_0x37b589(0x1eb)),_0x1d3b56;}async function getSubcategory(_0x5c84da,_0x1b447e){const _0x3ead96=a0_0x379a5d;var _0x5dd1f7=await fetch(chrome[_0x3ead96(0x23c)][_0x3ead96(0x1f7)](_0x3ead96(0x236)))[_0x3ead96(0x20e)](_0x40f645=>_0x40f645[_0x3ead96(0x226)]()),_0x54e2c0=getSubcategoriesForMainCategory(_0x1b447e);_0x5dd1f7['function_schema']['parameters'][_0x3ead96(0x232)]['output'][_0x3ead96(0x21f)]=_0x54e2c0,_0x5dd1f7[_0x3ead96(0x20b)]={'title':_0x5c84da,'main_category':_0x1b447e,'subcategories':_0x54e2c0},console[_0x3ead96(0x248)](_0x3ead96(0x22e),_0x5dd1f7);var _0x3930b4=await new Promise((_0x5031ff,_0x310467)=>{const _0x17dd99=_0x3ead96;chrome[_0x17dd99(0x23c)]['sendMessage']({'type':'fetchData','url':_0x17dd99(0x246),'data':_0x5dd1f7},function(_0x111510){const _0x5d89fd=_0x17dd99;chrome[_0x5d89fd(0x23c)][_0x5d89fd(0x222)]?_0x310467(chrome[_0x5d89fd(0x23c)]['lastError']):_0x5031ff(_0x111510['data']);});});console[_0x3ead96(0x248)](_0x3ead96(0x24d),_0x3930b4),_0x3930b4=JSON[_0x3ead96(0x1f8)](_0x3930b4);var _0x5a2759=_0x3930b4[_0x3ead96(0x216)];return!_0x54e2c0[_0x3ead96(0x227)](_0x5a2759)&&(_0x5a2759=_0x3ead96(0x1eb)),_0x5a2759;}function getSubcategoriesForMainCategory(_0x4b72c3){const _0x1455b9=a0_0x379a5d,_0x25dfe5={'Women':['Accessories','Bags',_0x1455b9(0x1f4),_0x1455b9(0x23e),_0x1455b9(0x21c),_0x1455b9(0x20d),'Jewelry',_0x1455b9(0x219),_0x1455b9(0x21e),_0x1455b9(0x20a),'Shorts','Skirts',_0x1455b9(0x242),_0x1455b9(0x214),_0x1455b9(0x22b),_0x1455b9(0x21b),_0x1455b9(0x234),_0x1455b9(0x23b),'Global\x20&\x20Traditional\x20Wear','Other'],'Men':[_0x1455b9(0x229),_0x1455b9(0x239),_0x1455b9(0x21c),_0x1455b9(0x20d),'Pants',_0x1455b9(0x235),_0x1455b9(0x20a),_0x1455b9(0x204),_0x1455b9(0x23a),_0x1455b9(0x242),_0x1455b9(0x214),'Underwear\x20&\x20Socks',_0x1455b9(0x223),_0x1455b9(0x23d),_0x1455b9(0x1eb)],'Kids':[_0x1455b9(0x229),_0x1455b9(0x218),_0x1455b9(0x1f4),_0x1455b9(0x21c),_0x1455b9(0x1fb),_0x1455b9(0x238),_0x1455b9(0x1ee),_0x1455b9(0x241),'Shoes',_0x1455b9(0x214),_0x1455b9(0x1f3),_0x1455b9(0x201),_0x1455b9(0x1f0),_0x1455b9(0x1eb)],'Home':[_0x1455b9(0x208),_0x1455b9(0x209),_0x1455b9(0x205),_0x1455b9(0x1f2),_0x1455b9(0x1f6),_0x1455b9(0x244),'Games',_0x1455b9(0x243),_0x1455b9(0x20c),_0x1455b9(0x24a),_0x1455b9(0x212),'Storage\x20&\x20Organization',_0x1455b9(0x200),_0x1455b9(0x1eb)],'Pets':[_0x1455b9(0x1ff),_0x1455b9(0x207),'Bird',_0x1455b9(0x225),_0x1455b9(0x23f),_0x1455b9(0x230),_0x1455b9(0x1eb)],'Electronics':[_0x1455b9(0x213),_0x1455b9(0x22d),_0x1455b9(0x215),_0x1455b9(0x1ec),'Wearables',_0x1455b9(0x21a),_0x1455b9(0x1ef),'VR,\x20AR\x20&\x20Accessories',_0x1455b9(0x217),_0x1455b9(0x224),_0x1455b9(0x22f),_0x1455b9(0x24b),_0x1455b9(0x1eb)]};return _0x25dfe5[_0x4b72c3]||[_0x1455b9(0x1eb)];}