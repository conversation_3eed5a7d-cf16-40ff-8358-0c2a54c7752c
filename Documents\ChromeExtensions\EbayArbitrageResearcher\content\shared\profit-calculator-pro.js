/**
 * Profit Calculator Pro - Simplified version for content script compatibility
 */
class ProfitCalculatorPro {
  constructor() {
    this.feeStructures = window.FEE_STRUCTURES || {};
    console.log('💰 ProfitCalculatorPro initialized');
  }

  /**
   * Calculate arbitrage opportunity
   * @param {object} config - Configuration object
   * @returns {object} Calculation results
   */
  calculateOpportunity(config = {}) {
    try {
      const {
        buyPrice = 0,
        sellPrice = 0,
        category = 'default',
        marketplace = 'ebay'
      } = config;

      // Basic profit calculation
      const fees = this.calculateFees(sellPrice, category, marketplace);
      const profit = sellPrice - buyPrice - fees.total;
      const margin = sellPrice > 0 ? (profit / sellPrice) * 100 : 0;

      return {
        success: true,
        buyPrice,
        sellPrice,
        fees,
        profit,
        margin,
        isViable: profit > 0 && margin > 10
      };
    } catch (error) {
      console.error('❌ ProfitCalculatorPro calculation error:', error);
      return {
        success: false,
        error: error.message,
        profit: 0,
        margin: 0,
        isViable: false
      };
    }
  }

  /**
   * Calculate marketplace fees
   * @param {number} price - Sale price
   * @param {string} category - Item category
   * @param {string} marketplace - Marketplace name
   * @returns {object} Fee breakdown
   */
  calculateFees(price, category = 'default', marketplace = 'ebay') {
    const fees = {
      listing: 0,
      final_value: 0,
      payment: 0,
      shipping: 0,
      total: 0
    };

    try {
      // Use fee structures if available
      const feeStructure = this.feeStructures[marketplace] || this.feeStructures.default;
      
      if (feeStructure) {
        fees.final_value = price * (feeStructure.final_value_fee || 0.1);
        fees.payment = price * (feeStructure.payment_fee || 0.03);
        fees.listing = feeStructure.listing_fee || 0;
      } else {
        // Default eBay-like fees
        fees.final_value = price * 0.1; // 10%
        fees.payment = price * 0.03;    // 3%
        fees.listing = 0.35;            // $0.35
      }

      fees.total = fees.listing + fees.final_value + fees.payment + fees.shipping;
    } catch (error) {
      console.error('❌ Fee calculation error:', error);
      fees.total = price * 0.15; // 15% fallback
    }

    return fees;
  }

  /**
   * Batch calculate multiple opportunities
   * @param {array} items - Array of item configurations
   * @returns {array} Array of calculation results
   */
  batchCalculate(items = []) {
    return items.map(item => this.calculateOpportunity(item));
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.ProfitCalculatorPro = ProfitCalculatorPro;
window.globalContext.ProfitCalculatorPro = ProfitCalculatorPro;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProfitCalculatorPro;
}

console.log('✅ ProfitCalculatorPro loaded and exported to window.ProfitCalculatorPro');
