// eBay Arbitrage Researcher Pro - Popup Interface
class ArbitrageUI {
  constructor() {
    this.results = [];
    this.isProcessing = false;
    this.currentSort = 'profit';
    this.currentGradeFilter = 'all';
    
    this.initializeEventListeners();
    this.loadPreviousResults();
    this.startProgressPolling();
  }

  initializeEventListeners() {
    // Main action buttons
    document.getElementById('startAnalysis').addEventListener('click', () => {
      this.startAnalysis();
    });

    document.getElementById('stopAnalysis').addEventListener('click', () => {
      this.stopAnalysis();
    });

    document.getElementById('exportResults').addEventListener('click', () => {
      this.exportToCSV();
    });

    document.getElementById('refreshResults').addEventListener('click', () => {
      this.loadPreviousResults();
    });

    // Config toggle
    document.getElementById('configToggle').addEventListener('click', () => {
      this.toggleAdvancedConfig();
    });

    // Filter controls
    document.getElementById('sortBy').addEventListener('change', (e) => {
      this.currentSort = e.target.value;
      this.displayResults();
    });

    document.getElementById('gradeFilter').addEventListener('change', (e) => {
      this.currentGradeFilter = e.target.value;
      this.displayResults();
    });

    // Footer links
    document.getElementById('helpLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.showHelp();
    });

    document.getElementById('settingsLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.showSettings();
    });

    document.getElementById('aboutLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.showAbout();
    });

    // Input validation
    this.setupInputValidation();
  }

  toggleAdvancedConfig() {
    const content = document.getElementById('configContent');
    const icon = document.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
      content.style.display = 'block';
      icon.classList.add('expanded');
    } else {
      content.style.display = 'none';
      icon.classList.remove('expanded');
    }
  }

  setupInputValidation() {
    const inputs = document.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
      input.addEventListener('change', () => {
        const min = parseInt(input.getAttribute('min'));
        const max = parseInt(input.getAttribute('max'));
        const value = parseInt(input.value);
        
        if (value < min) input.value = min;
        if (value > max) input.value = max;
      });
    });
  }

  async startAnalysis() {
    if (this.isProcessing) return;

    // Update status indicator
    this.updateStatusIndicator('processing', 'Starting analysis...');

    // First test service worker connectivity
    console.log('🔍 Testing service worker connectivity...');
    try {
      const pingResponse = await this.sendMessage({ action: 'ping' });
      console.log('✅ Service worker ping successful:', pingResponse);
    } catch (error) {
      console.error('❌ Service worker ping failed:', error);
      this.showNotification('❌ Service worker not responding. Try reloading the extension.', 'error');
      this.updateStatusIndicator('error', 'Service worker error');
      return;
    }

    const config = this.getConfiguration();

    // Validate configuration
    if (!this.validateConfiguration(config)) {
      this.updateStatusIndicator('ready', 'Ready to start');
      return;
    }

    this.setProcessingState(true);
    this.showProgressSection();
    this.updateProgress(0, 'Initializing analysis...');

    try {
      console.log('🚀 Starting analysis with config:', config);
      
      const response = await this.sendMessage({ 
        action: 'startAnalysis', 
        config 
      });
      
      if (response.success) {
        this.results = response.results || [];
        this.displayResults();
        this.displaySummaryStats(response.summary);
        this.updateProgress(100, `Analysis complete! Found ${response.summary.opportunities} opportunities.`);

        // Show success notification
        this.showNotification(`✅ Found ${response.summary.opportunities} profitable opportunities!`, 'success');
        this.updateStatusIndicator('ready', `Found ${response.summary.opportunities} opportunities`);

        document.getElementById('exportResults').disabled = false;
      } else {
        throw new Error(response.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('❌ Analysis failed:', error);
      this.updateProgress(0, `Error: ${error.message}`);
      this.showNotification(`❌ Analysis failed: ${error.message}`, 'error');
      this.updateStatusIndicator('error', 'Analysis failed');
    } finally {
      this.setProcessingState(false);
    }
  }

  async stopAnalysis() {
    try {
      await this.sendMessage({ action: 'stopAnalysis' });
      this.setProcessingState(false);
      this.updateProgress(0, 'Analysis stopped by user');
      this.updateStatusIndicator('ready', 'Analysis stopped');
      this.showNotification('⏹️ Analysis stopped', 'info');
    } catch (error) {
      console.error('Failed to stop analysis:', error);
      this.updateStatusIndicator('error', 'Stop failed');
    }
  }




  getConfiguration() {
    return {
      // Search parameters
      minSales: parseInt(document.getElementById('minSales').value),
      daysBack: parseInt(document.getElementById('daysBack').value),
      maxPages: parseInt(document.getElementById('maxPages').value),
      
      // Profit filters
      minProfit: parseFloat(document.getElementById('minProfit').value),
      minMargin: parseFloat(document.getElementById('minMargin').value),
      maxRisk: parseInt(document.getElementById('maxRisk').value),
      
      // Price range
      priceRange: {
        min: parseInt(document.getElementById('minPrice').value),
        max: parseInt(document.getElementById('maxPrice').value)
      },
      
      // Advanced options
      maxMatches: 2,
      minDemand: 30,
      minGrade: 'C',
      
      profitOptions: {
        category: 'default',
        region: 'US',
        shippingType: 'standard',
        isInternational: false,
        usePromotedListings: false
      }
    };
  }

  validateConfiguration(config) {
    if (config.priceRange.min >= config.priceRange.max) {
      this.showNotification('❌ Min price must be less than max price', 'error');
      return false;
    }
    
    if (config.minSales < 1 || config.minSales > 50) {
      this.showNotification('❌ Min sales must be between 1 and 50', 'error');
      return false;
    }
    
    if (config.maxPages < 1 || config.maxPages > 50) {
      this.showNotification('❌ Max pages must be between 1 and 50', 'error');
      return false;
    }
    
    return true;
  }

  displayResults() {
    const resultsContainer = document.getElementById('resultsList');
    const countElement = document.getElementById('opportunityCount');
    
    let filteredResults = this.filterResults(this.results);
    filteredResults = this.sortResults(filteredResults);
    
    countElement.textContent = filteredResults.length;
    
    if (filteredResults.length === 0) {
      resultsContainer.innerHTML = `
        <div class="no-results">
          <p>🔍 No opportunities match your current filters.</p>
          <p>Try adjusting your criteria or running a new analysis.</p>
        </div>
      `;
      return;
    }

    // Show filter controls
    document.getElementById('filterControls').style.display = 'flex';
    
    resultsContainer.innerHTML = '';
    
    filteredResults.slice(0, 50).forEach((opportunity, index) => {
      const opportunityElement = this.createOpportunityElement(opportunity, index + 1);
      resultsContainer.appendChild(opportunityElement);
    });
  }

  filterResults(results) {
    if (this.currentGradeFilter === 'all') return results;
    
    const gradeOrder = ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'];
    const filterIndex = gradeOrder.indexOf(this.currentGradeFilter);
    
    return results.filter(result => {
      const gradeIndex = gradeOrder.indexOf(result.profitAnalysis.opportunityGrade);
      return gradeIndex <= filterIndex;
    });
  }

  sortResults(results) {
    return [...results].sort((a, b) => {
      const analysisA = a.profitAnalysis;
      const analysisB = b.profitAnalysis;

      switch (this.currentSort) {
        case 'profit':
          return analysisB.grossProfit - analysisA.grossProfit;
        case 'margin':
          return analysisB.profitMargin - analysisA.profitMargin;
        case 'roi':
          return analysisB.roi - analysisA.roi;
        case 'grade':
          const gradeOrder = ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'];
          return gradeOrder.indexOf(analysisA.opportunityGrade) - gradeOrder.indexOf(analysisB.opportunityGrade);
        default:
          return analysisB.grossProfit - analysisA.grossProfit;
      }
    });
  }

  createOpportunityElement(opportunity, rank) {
    const div = document.createElement('div');
    div.className = 'opportunity';

    const profit = opportunity.profitAnalysis;
    const ebay = opportunity.ebayProduct;
    const amazon = opportunity.amazonProduct;
    const grade = profit.opportunityGrade;

    div.innerHTML = `
      <div class="opportunity-header">
        <span class="opportunity-rank">#${rank}</span>
        <span class="opportunity-grade grade-${grade.toLowerCase().replace('+', '-plus')}">${grade}</span>
      </div>
      <div class="opportunity-profit">
        $${profit.grossProfit.toFixed(2)} profit (${profit.profitMargin.toFixed(1)}% margin)
      </div>
      <div class="opportunity-titles">
        <div class="opportunity-title"><strong>eBay:</strong> ${ebay.title.substring(0, 55)}...</div>
        <div class="opportunity-title"><strong>Amazon:</strong> ${amazon.title.substring(0, 55)}...</div>
      </div>
      <div class="opportunity-metrics">
        <span>eBay: $${ebay.price.toFixed(2)}</span>
        <span>Amazon: $${amazon.price.toFixed(2)}</span>
        <span>ROI: ${profit.roi.toFixed(1)}%</span>
        <span>Sales: ${ebay.soldQuantity || 0}</span>
      </div>
    `;

    div.addEventListener('click', () => {
      this.showDetailedView(opportunity);
    });

    return div;
  }

  displaySummaryStats(summary) {
    if (!summary) return;

    document.getElementById('summaryStats').style.display = 'grid';
    document.getElementById('totalProfit').textContent = `$${summary.totalPotentialProfit?.toFixed(2) || '0.00'}`;
    document.getElementById('avgMargin').textContent = `${summary.averageMargin?.toFixed(1) || '0.0'}%`;
    document.getElementById('highValueCount').textContent = summary.highValueCount || 0;
  }

  showDetailedView(opportunity) {
    const profit = opportunity.profitAnalysis;
    const ebay = opportunity.ebayProduct;
    const amazon = opportunity.amazonProduct;

    const detailHTML = `
      <html>
      <head>
        <title>Arbitrage Opportunity Details</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin-bottom: 30px; }
          .product-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
          .product-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
          .profit-table { width: 100%; border-collapse: collapse; }
          .profit-table th, .profit-table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          .profit-table th { background: #f8f9fa; }
          .total-row { font-weight: bold; border-top: 2px solid #333; }
          .grade-${profit.opportunityGrade.toLowerCase().replace('+', '-plus')} {
            background: #28a745; color: white; padding: 4px 8px; border-radius: 4px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎯 Arbitrage Opportunity Analysis</h1>
          <p><span class="grade-${profit.opportunityGrade.toLowerCase().replace('+', '-plus')}">${profit.opportunityGrade}</span>
             - $${profit.grossProfit.toFixed(2)} profit (${profit.profitMargin.toFixed(1)}% margin)</p>
        </div>

        <div class="product-grid">
          <div class="product-card">
            <h3>📦 eBay Product</h3>
            <p><strong>Title:</strong> ${ebay.title}</p>
            <p><strong>Price:</strong> $${ebay.price.toFixed(2)}</p>
            <p><strong>Sales:</strong> ${ebay.soldQuantity || 0} in last 30 days</p>
            <p><strong>Condition:</strong> ${ebay.condition || 'Not specified'}</p>
            <p><a href="${ebay.link}" target="_blank">🔗 View on eBay</a></p>
          </div>

          <div class="product-card">
            <h3>📦 Amazon Product</h3>
            <p><strong>Title:</strong> ${amazon.title}</p>
            <p><strong>Price:</strong> $${amazon.price.toFixed(2)}</p>
            <p><strong>Rating:</strong> ${amazon.rating || 'N/A'}/5 (${amazon.reviewCount || 0} reviews)</p>
            <p><strong>Prime:</strong> ${amazon.hasPrime ? 'Yes' : 'No'}</p>
            <p><a href="${amazon.link}" target="_blank">🔗 View on Amazon</a></p>
          </div>
        </div>

        <div class="section">
          <h3>💰 Detailed Profit Analysis</h3>
          <table class="profit-table">
            <tr><th>Item</th><th>Amount</th></tr>
            <tr><td>eBay Sale Price</td><td>$${profit.revenue.toFixed(2)}</td></tr>
            <tr><td>Amazon Cost (with tax)</td><td>-$${profit.costs.amazon.toFixed(2)}</td></tr>
            <tr><td>eBay Final Value Fee</td><td>-$${profit.feeBreakdown.finalValue?.toFixed(2) || '0.00'}</td></tr>
            <tr><td>Payment Processing Fee</td><td>-$${profit.feeBreakdown.payment?.toFixed(2) || '0.00'}</td></tr>
            <tr><td>Shipping Cost</td><td>-$${profit.costs.shipping.toFixed(2)}</td></tr>
            <tr class="total-row"><td><strong>Net Profit</strong></td><td><strong>$${profit.grossProfit.toFixed(2)}</strong></td></tr>
          </table>
        </div>

        <div class="section">
          <h3>📊 Key Metrics</h3>
          <p><strong>Profit Margin:</strong> ${profit.profitMargin.toFixed(1)}%</p>
          <p><strong>Return on Investment:</strong> ${profit.roi.toFixed(1)}%</p>
          <p><strong>Risk Score:</strong> ${profit.riskScore}/100</p>
          <p><strong>Demand Score:</strong> ${profit.demandScore}/100</p>
          <p><strong>Product Similarity:</strong> ${(amazon.similarity * 100).toFixed(1)}%</p>
        </div>
      </body>
      </html>
    `;

    const detailWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
    detailWindow.document.write(detailHTML);
    detailWindow.document.close();
  }

  async exportToCSV() {
    if (this.results.length === 0) {
      this.showNotification('❌ No results to export', 'error');
      return;
    }

    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `arbitrage_opportunities_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();

    URL.revokeObjectURL(url);
    this.showNotification('📊 Results exported to CSV', 'success');
  }

  generateCSV() {
    const headers = [
      'Rank', 'Grade', 'eBay Title', 'Amazon Title', 'eBay Price', 'Amazon Price',
      'Gross Profit', 'Profit Margin %', 'ROI %', 'Sales Count', 'Risk Score',
      'Demand Score', 'Similarity %', 'eBay Link', 'Amazon Link'
    ];

    const rows = this.results.map((opportunity, index) => {
      const profit = opportunity.profitAnalysis;
      const ebay = opportunity.ebayProduct;
      const amazon = opportunity.amazonProduct;

      return [
        index + 1,
        profit.opportunityGrade,
        `"${ebay.title.replace(/"/g, '""')}"`,
        `"${amazon.title.replace(/"/g, '""')}"`,
        ebay.price.toFixed(2),
        amazon.price.toFixed(2),
        profit.grossProfit.toFixed(2),
        profit.profitMargin.toFixed(1),
        profit.roi.toFixed(1),
        ebay.soldQuantity || 0,
        profit.riskScore,
        profit.demandScore,
        ((amazon.similarity || 0) * 100).toFixed(1),
        ebay.link,
        amazon.link
      ].join(',');
    });

    return [headers.join(','), ...rows].join('\n');
  }

  async loadPreviousResults() {
    try {
      const response = await this.sendMessage({ action: 'getResults' });
      if (response.success && response.results.length > 0) {
        this.results = response.results;
        this.displayResults();
        document.getElementById('exportResults').disabled = false;
        this.showNotification('📋 Previous results loaded', 'info');
      }

      if (response.isProcessing) {
        this.setProcessingState(true);
        this.showProgressSection();
      }
    } catch (error) {
      console.warn('Could not load previous results:', error);
    }
  }

  startProgressPolling() {
    // Listen for progress updates from background script
    chrome.runtime.onMessage.addListener((message) => {
      if (message.type === 'PROGRESS_UPDATE') {
        this.updateProgress(message.progress.current, message.progress.stage);
      }
    });
  }

  setProcessingState(processing) {
    this.isProcessing = processing;
    const startBtn = document.getElementById('startAnalysis');
    const stopBtn = document.getElementById('stopAnalysis');

    if (processing) {
      // Hide start button, show stop button
      startBtn.style.display = 'none';
      stopBtn.style.display = 'block';
      this.updateStatusIndicator('processing', 'Analysis in progress...');
    } else {
      // Show start button, hide stop button
      startBtn.style.display = 'block';
      stopBtn.style.display = 'none';
      startBtn.textContent = '🚀 Start Analysis';
      this.updateStatusIndicator('ready', 'Ready to start');
    }
  }

  updateStatusIndicator(state, text) {
    const statusText = document.querySelector('.status-text');
    const statusDot = document.querySelector('.status-dot');

    statusText.textContent = text;

    // Remove all state classes
    statusDot.classList.remove('ready', 'processing', 'error');
    // Add current state class
    statusDot.classList.add(state);
  }

  showProgressSection() {
    document.getElementById('progressSection').style.display = 'block';
  }

  updateProgress(percent, message) {
    document.getElementById('progressPercent').textContent = `${percent}%`;
    document.getElementById('progressFill').style.width = `${percent}%`;
    document.getElementById('progressStatus').textContent = message;

    if (percent === 100 || percent === 0) {
      setTimeout(() => {
        if (!this.isProcessing) {
          document.getElementById('progressSection').style.display = 'none';
        }
      }, 3000);
    }
  }

  showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      max-width: 300px;
      word-wrap: break-word;
      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
    `;

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 4000);
  }

  showHelp() {
    const helpContent = `
      <h2>🎯 eBay Arbitrage Researcher Pro - Help</h2>
      <h3>How it works:</h3>
      <ol>
        <li><strong>Configure Settings:</strong> Set minimum sales, profit margins, and price ranges</li>
        <li><strong>Start Analysis:</strong> Click "Start Arbitrage Analysis" to begin scanning</li>
        <li><strong>Review Results:</strong> Browse profitable opportunities sorted by potential</li>
        <li><strong>Export Data:</strong> Download CSV for purchasing decisions</li>
      </ol>

      <h3>Key Metrics:</h3>
      <ul>
        <li><strong>Grade:</strong> A+ (best) to D (worst) opportunity rating</li>
        <li><strong>Profit Margin:</strong> Percentage profit after all fees</li>
        <li><strong>ROI:</strong> Return on investment percentage</li>
        <li><strong>Risk Score:</strong> 0 (low risk) to 100 (high risk)</li>
      </ul>

      <h3>Tips:</h3>
      <ul>
        <li>Start with 3-5 minimum sales for reliable demand signals</li>
        <li>Focus on A and B grade opportunities</li>
        <li>Consider shipping costs and handling time</li>
        <li>Verify product condition and authenticity</li>
      </ul>
    `;

    this.showModal('Help', helpContent);
  }

  showSettings() {
    const settingsContent = `
      <h2>⚙️ Advanced Settings</h2>
      <p>Advanced configuration options will be available in future updates.</p>
      <p>Current features:</p>
      <ul>
        <li>Automatic fee calculation</li>
        <li>Multi-marketplace support</li>
        <li>Risk assessment</li>
        <li>Demand analysis</li>
      </ul>
    `;

    this.showModal('Settings', settingsContent);
  }

  showAbout() {
    const aboutContent = `
      <h2>ℹ️ About eBay Arbitrage Researcher Pro</h2>
      <p><strong>Version:</strong> 1.0.0</p>
      <p><strong>Purpose:</strong> Professional arbitrage opportunity finder</p>

      <h3>Features:</h3>
      <ul>
        <li>🔍 Automated eBay sold listings analysis</li>
        <li>🎯 Amazon product matching</li>
        <li>💰 Comprehensive profit calculations</li>
        <li>📊 Risk and demand assessment</li>
        <li>📈 Opportunity grading system</li>
        <li>📋 CSV export functionality</li>
      </ul>

      <p><strong>Built for:</strong> Drop shipping and retail arbitrage professionals</p>
      <p><strong>License:</strong> Free for personal use</p>
    `;

    this.showModal('About', aboutContent);
  }

  showModal(title, content) {
    const modal = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');
    modal.document.write(`
      <html>
      <head>
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
          h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
          h3 { color: #495057; margin-top: 20px; }
          ul, ol { margin-left: 20px; }
          li { margin-bottom: 5px; }
        </style>
      </head>
      <body>${content}</body>
      </html>
    `);
    modal.document.close();
  }

  sendMessage(message) {
    console.log('🚀 POPUP SENDING MESSAGE:', JSON.stringify(message));
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        console.log('📥 POPUP RECEIVED RESPONSE:', response);
        console.log('🔍 Chrome runtime error:', chrome.runtime.lastError);

        if (chrome.runtime.lastError) {
          console.error('❌ Message sending error:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
        } else if (!response) {
          console.error('❌ No response received from service worker');
          reject(new Error('No response from service worker'));
        } else {
          console.log('✅ Message sent successfully, response:', response);
          resolve(response);
        }
      });
    });
  }
}

// Initialize UI when popup loads
document.addEventListener('DOMContentLoaded', () => {
  new ArbitrageUI();
});
