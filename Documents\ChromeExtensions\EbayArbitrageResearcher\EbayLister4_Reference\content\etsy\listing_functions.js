(function(_0x253f07,_0x53db98){var _0x5d9861=a0_0x261a,_0x15eba1=_0x253f07();while(!![]){try{var _0x225161=parseInt(_0x5d9861(0x12e))/0x1*(-parseInt(_0x5d9861(0x10d))/0x2)+-parseInt(_0x5d9861(0x11f))/0x3*(parseInt(_0x5d9861(0x10f))/0x4)+parseInt(_0x5d9861(0x109))/0x5*(-parseInt(_0x5d9861(0x102))/0x6)+parseInt(_0x5d9861(0x104))/0x7*(parseInt(_0x5d9861(0x12b))/0x8)+parseInt(_0x5d9861(0x124))/0x9+parseInt(_0x5d9861(0x108))/0xa*(parseInt(_0x5d9861(0x122))/0xb)+parseInt(_0x5d9861(0x10e))/0xc;if(_0x225161===_0x53db98)break;else _0x15eba1['push'](_0x15eba1['shift']());}catch(_0x11943b){_0x15eba1['push'](_0x15eba1['shift']());}}}(a0_0x24a8,0xdf927));function pasteTitle(_0x1fe47c){return new Promise((_0x3086f4,_0x4fbc9f)=>{var _0x2f33ab=a0_0x261a,_0x3e917b=document[_0x2f33ab(0x131)](_0x2f33ab(0x127));_0x3e917b['value']=_0x1fe47c,console[_0x2f33ab(0x120)](_0x2f33ab(0x11c),_0x1fe47c+_0x2f33ab(0x11a)+_0x1fe47c[_0x2f33ab(0x12d)]),_0x3e917b['dispatchEvent'](new Event('input',{'bubbles':!![]})),_0x3086f4();});}function pasteCategory(_0xe07400){return new Promise((_0x4bd3ec,_0x48db20)=>{var _0x5a814a=a0_0x261a;const _0x22f402=0x7d0;var _0x52c626=document[_0x5a814a(0x131)](_0x5a814a(0x138));_0x52c626['value']=_0xe07400,_0x52c626['dispatchEvent'](new Event(_0x5a814a(0x11d),{'bubbles':!![]}));var _0x49b957=new KeyboardEvent('keydown',{'altKey':![],'bubbles':!![],'cancelBubble':![],'cancelable':!![],'charCode':0x0,'code':_0x5a814a(0x140),'composed':!![],'ctrlKey':![],'currentTarget':null,'defaultPrevented':!![],'detail':0x0,'eventPhase':0x0,'isComposing':![],'isTrusted':!![],'key':_0x5a814a(0x140),'keyCode':0xd,'location':0x0,'metaKey':![],'repeat':![],'returnValue':![],'shiftKey':![],'type':'keydown','which':0xd});setTimeout(()=>{var _0x1b86ba=_0x5a814a;_0x52c626[_0x1b86ba(0x134)](_0x49b957),_0x4bd3ec();},_0x22f402);});}function chooseShipping(){return new Promise((_0x51b930,_0xc7cb7c)=>{var _0x4fb3c5=a0_0x261a,_0x3325d5=document[_0x4fb3c5(0x106)](_0x4fb3c5(0x125));_0x3325d5[_0x4fb3c5(0x106)](_0x4fb3c5(0x11d))['click'](),_0x51b930();});}function checkIfTitleHasStrangeFirstCharacter(){var _0x3e5134=a0_0x261a,_0xdd1a52='(2packs)\x20-Solar\x20Lantern\x20Outdoor\x20Hanging\x20Solar\x20Light\x208\x20Lumens\x20Metal\x20Warm\x20White\x20For\x20Patio\x20Garden\x20Courtyard\x20Pathway',_0x3a15b4=_0xdd1a52[_0x3e5134(0x101)]('\x20'),_0x5b106d=_0x3a15b4;console['log'](_0x3e5134(0x136),_0x5b106d);for(let _0x17824b=0x0;_0x17824b<_0x5b106d[_0x3e5134(0x12d)];_0x17824b++){var _0x2b29e8=_0x5b106d[_0x17824b],_0x4e1df6=/[^a-zA-Z \d]/g;console[_0x3e5134(0x120)]('word',_0x2b29e8);if(_0x2b29e8['match'](_0x4e1df6)){var _0x535585=_0x2b29e8[_0x3e5134(0x123)](_0x4e1df6);console['log'](_0x3e5134(0x123),_0x535585),_0x3a15b4[_0x3e5134(0x13c)](_0x3a15b4[_0x3e5134(0x130)](_0x3a15b4[_0x3e5134(0x132)](_0x2b29e8),0x1)[0x0]);}else{console[_0x3e5134(0x120)](_0x3e5134(0x112),_0x2b29e8);break;}}}function characterLimitString(_0x2271bf){var _0xbbd9ac=a0_0x261a,_0x2c89c4=[],_0x189061=_0x2271bf[_0xbbd9ac(0x11e)](),_0x2bef9c=_0x189061[_0xbbd9ac(0x101)]('\x20');_0x2bef9c=_0x2bef9c[_0xbbd9ac(0x144)](_0x5c217b=>{var _0x7558e4=_0xbbd9ac;_0x5c217b[_0x7558e4(0x12d)]>0x0&&(newWord=_0x5c217b[0x0][_0x7558e4(0x129)]()+_0x5c217b[_0x7558e4(0x143)](0x1,_0x5c217b[_0x7558e4(0x12d)]),_0x2c89c4[_0x7558e4(0x13c)](newWord));});var _0x14af8c=_0x2c89c4[_0xbbd9ac(0x139)]('\x20');return _0x14af8c[_0xbbd9ac(0x12d)]>0x8c&&(_0x14af8c=_0x14af8c[_0xbbd9ac(0x143)](0x0,0x88)+'...'),_0x14af8c;}function promiseToWait(_0x14ec50){return new Promise((_0x1a3a63,_0x530888)=>{setTimeout(()=>{_0x1a3a63();},_0x14ec50);});}function getSearchSuggestionsByOpeningTab(_0x37b6dc){return new Promise((_0x505d53,_0x53f164)=>{var _0x2aa2c0=a0_0x261a;chrome[_0x2aa2c0(0x12f)]['sendMessage']({'type':_0x2aa2c0(0x116),'searchTerm':_0x37b6dc},function(_0x52279b){});});}function getSearchSuggestions(_0x3dc5a6){var _0x55fd8a=a0_0x261a;return console[_0x55fd8a(0x120)](_0x55fd8a(0x119)+_0x3dc5a6+']'),new Promise((_0x2f0b3f,_0x10d4ad)=>{var _0xccd74e=_0x55fd8a,_0x54ba90=_0xccd74e(0x13b)+encodeURIComponent(_0x3dc5a6)+_0xccd74e(0x114);console['log']('url',_0x54ba90),fetch(_0x54ba90)[_0xccd74e(0x100)](function(_0x485e6d){var _0x3c6b20=_0xccd74e;return _0x485e6d[_0x3c6b20(0x141)]();})[_0xccd74e(0x100)](function(_0x1fda7d){var _0x4c2619=_0xccd74e;console[_0x4c2619(0x120)]('getSearchSuggestions\x20data:',_0x1fda7d),console[_0x4c2619(0x120)]('getSearchSuggestions\x20data.results:',_0x1fda7d['results']);var _0xfcbf1e=[],_0x433965=_0x1fda7d[_0x4c2619(0x128)];return _0x433965[_0x4c2619(0x13e)](_0x3615d9=>{var _0x1e823c=_0x4c2619;!_0x3615d9[_0x1e823c(0x142)]&&_0xfcbf1e[_0x1e823c(0x13c)](_0x3615d9['query']);}),console['log'](_0x4c2619(0x12c),_0xfcbf1e),_0x2f0b3f(_0xfcbf1e);})['catch'](function(_0x2c4ce4){var _0x55eb74=_0xccd74e;return console[_0x55eb74(0x120)](_0x2c4ce4),_0x2f0b3f(_0x2c4ce4);});});}function a0_0x261a(_0x272def,_0x11300f){var _0x24a858=a0_0x24a8();return a0_0x261a=function(_0x261aa3,_0x1139b5){_0x261aa3=_0x261aa3-0x100;var _0x5d62eb=_0x24a858[_0x261aa3];return _0x5d62eb;},a0_0x261a(_0x272def,_0x11300f);}function pasteDescription(_0x35e5c2){return new Promise((_0x1f4a45,_0x2b5d2c)=>{var _0x32c589=a0_0x261a,_0x731c92=document[_0x32c589(0x131)](_0x32c589(0x12a));_0x731c92[_0x32c589(0x13d)]=_0x35e5c2,_0x731c92[_0x32c589(0x134)](new Event(_0x32c589(0x11d),{'bubbles':!![]})),_0x1f4a45();});}function pastePrice(_0x4f03d1){return new Promise((_0x1c16ef,_0x2376a1)=>{var _0x217fba=a0_0x261a,_0x52786d=document[_0x217fba(0x131)](_0x217fba(0x118));_0x52786d[_0x217fba(0x13d)]=_0x4f03d1,_0x52786d[_0x217fba(0x134)](new Event(_0x217fba(0x11d),{'bubbles':!![]})),_0x1c16ef();});}function pasteAboutThisListingFunction(){return new Promise((_0x2e33e6,_0x587b04)=>{var _0x35422e=a0_0x261a,_0x40ff44=document[_0x35422e(0x131)](_0x35422e(0x111));_0x40ff44[_0x35422e(0x13d)]=_0x35422e(0x121),_0x40ff44['dispatchEvent'](new Event(_0x35422e(0x126),{'bubbles':!![]}));var _0x379476=document['getElementById'](_0x35422e(0x13f));_0x379476[_0x35422e(0x13d)]=_0x35422e(0x113),_0x379476[_0x35422e(0x134)](new Event(_0x35422e(0x126),{'bubbles':!![]}));var _0x1765b4=document[_0x35422e(0x131)](_0x35422e(0x10c));_0x1765b4[_0x35422e(0x13d)]=_0x35422e(0x117),_0x1765b4['dispatchEvent'](new Event(_0x35422e(0x126),{'bubbles':!![]})),_0x2e33e6();});}function chooseRenewalOption(){return new Promise((_0x2c45b0,_0x20649f)=>{var _0x57cae7=a0_0x261a;_0x2c45b0();var _0x36a2bf=document[_0x57cae7(0x131)](_0x57cae7(0x110));_0x36a2bf[_0x57cae7(0x10a)]();});}function originPostcode(){}function postagePricing(){}function uploadPhotos(){}function uploadVideos(){}function pasteTag(_0x18b2d2){return new Promise(_0x138342=>{var _0x5b3b0e=a0_0x261a;const _0x529893=0x64;console[_0x5b3b0e(0x120)](_0x5b3b0e(0x105)+_0x18b2d2);var _0x392fed=document[_0x5b3b0e(0x131)](_0x5b3b0e(0x115));_0x392fed[_0x5b3b0e(0x13d)]=_0x18b2d2,_0x392fed['dispatchEvent'](new Event(_0x5b3b0e(0x11d),{'bubbles':!![]}));var _0x50b5d5=_0x392fed['parentElement'][_0x5b3b0e(0x107)][_0x5b3b0e(0x107)][_0x5b3b0e(0x106)](_0x5b3b0e(0x13a)),_0x3b9db7=new KeyboardEvent(_0x5b3b0e(0x103),{'altKey':![],'bubbles':!![],'cancelBubble':![],'cancelable':!![],'charCode':0x0,'code':'Enter','composed':!![],'ctrlKey':![],'currentTarget':null,'defaultPrevented':!![],'detail':0x0,'eventPhase':0x0,'isComposing':![],'isTrusted':!![],'key':_0x5b3b0e(0x140),'keyCode':0xd,'location':0x0,'metaKey':![],'repeat':![],'returnValue':![],'shiftKey':![],'type':_0x5b3b0e(0x103),'which':0xd});setTimeout(()=>{var _0x1e191a=_0x5b3b0e;_0x392fed[_0x1e191a(0x134)](_0x3b9db7),_0x50b5d5[_0x1e191a(0x10a)](),_0x138342();},_0x529893);});}function a0_0x24a8(){var _0x4be2f5=['forEach','is_supply-input','Enter','json','link','substring','map','then','split','6RkcZEB','keydown','7LgxbAf','tag\x20to\x20input:\x20','querySelector','parentElement','10QEtQQI','7943835qIQyFJ','click','div.overlay-footer\x20button.btn.btn-primary','when_made-input','6746mxLIsx','21415140OhJspF','804LRCGZE','renewalOptionManual','who_made-input','word\x20did\x20not\x20match\x20any\x20word','false','&search_type=all','tags','hey_background_get_search_results_for_etsy','2020_2022','price_retail-input','getSearchSuggestions:\x20\x20[','\x20=\x20','btn\x20btn-primary','data:\x20and\x20data\x20link','input','toLowerCase','4503ldWxYo','log','collective','2689544mhfoih','match','14812110vztoqJ','div.wt-mr-xs-1','change','title-input','results','toUpperCase','description-text-area-input','3763288fHcjBI','searchQuerys','length','397iohySW','runtime','splice','getElementById','indexOf','quantity-input','dispatchEvent','getElementsByClassName','titleWordArray','SKU-input','taxonomy-search','join','button','https://www.etsy.com/suggestions_ajax.php?search_query=','push','value'];a0_0x24a8=function(){return _0x4be2f5;};return a0_0x24a8();}function chooseQuantity(){var _0x4f8f7c=a0_0x261a,_0xe0487f=document[_0x4f8f7c(0x131)](_0x4f8f7c(0x133));_0xe0487f[_0x4f8f7c(0x13d)]=0x1;}function processingTime(){}function handlingfee(){}function chooseMaterial(){}function getItemWeight(){}function getItemSize(){}function choosePricing(){}function pasteSKU(_0x266fff){return new Promise((_0x119823,_0x568f34)=>{var _0x41d44e=a0_0x261a,_0x7f1881=document[_0x41d44e(0x131)](_0x41d44e(0x137));_0x7f1881[_0x41d44e(0x13d)]=_0x266fff,_0x119823();});}async function submitListing(){var _0x14a8a7=a0_0x261a,_0x3fef3e=document[_0x14a8a7(0x135)](_0x14a8a7(0x11b))[0x0];_0x3fef3e[_0x14a8a7(0x10a)]();const _0x5144ab=await _waitForElement(_0x14a8a7(0x10b));_0x5144ab['click']();const _0x19a5b8=await _waitForElement('alert.alert-success');tellChromeToCloseTab();}function tellChromeToCloseTab(){var _0x12e0b2=a0_0x261a;chrome[_0x12e0b2(0x12f)]['sendMessage']({'type':'close_tab'},function(_0x3ee9b5){});}