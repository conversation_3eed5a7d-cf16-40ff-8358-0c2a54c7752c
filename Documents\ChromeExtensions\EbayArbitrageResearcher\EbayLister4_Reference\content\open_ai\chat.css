#initialize_chat_gpt_web {
    display: block;
    width: 100%;
    height: 100%;
    padding: 1px;
    margin: 0;
    font-size: 100%;
    background-color: #007bff;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    position: relative;
    z-index: 1;
    transition: background-color 0.3s;
    /* Smooth transition for background color change */
}

#initialize_chat_gpt_web:hover {
    background-color: #0056b3;
    /* Slightly darker blue for hover effect */
}

#initialize_chat_gpt_web:active {
    background-color: #004080;
    /* Even darker blue for active/click effect */
}

#initialize_chat_gpt_web.connected {
    background-color: #28a745;
    /* Green background for connected state */
    color: #ffffff;
    /* White text for connected state */
}

#initialize_chat_gpt_web.connected:hover {
    background-color: #218838;
    /* Slightly darker green for hover effect in connected state */
}

#initialize_chat_gpt_web.connected:active {
    background-color: #1e7e34;
    /* Even darker green for active/click effect in connected state */
}

span .initialize-btn {
    display: block;
    width: 100%;
    text-align: center;
}


#send_prompt {
    padding: 0;
    border: 0;
    margin: 0;
    background: none;
    box-sizing: border-box;
    cursor: pointer;
    font: inherit;
    white-space: nowrap;
    display: inline-block;
    line-height: normal;
    text-align: center;
    vertical-align: middle;
    overflow: visible;

    padding: 0;
    /* Removes any default padding */
    border: none;
    /* Removes any default border */
    background: none;
    /* Removes any default background */
    box-sizing: border-box;
    /* Ensures the padding and border are included in the total width and height */
    cursor: pointer;
    /* Changes the mouse cursor to a pointer when hovering over the button */
    font: inherit;
    /* Inherits font styles from parent elements */
    background-color: #007bff;
    color: #ffffff;
    margin-bottom: auto;

}

#send_prompt:hover {
    background-color: #0056b3;
    /* Slightly darker blue for hover effect */
}

#send_prompt:active {
    background-color: #004080;
    /* Even darker blue for active/click effect */
}