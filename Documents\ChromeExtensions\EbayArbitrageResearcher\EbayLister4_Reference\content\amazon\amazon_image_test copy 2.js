(function(_0x27bdb8,_0x474a52){var _0x483127=a0_0xd66c,_0x4381d0=_0x27bdb8();while(!![]){try{var _0x581ff0=-parseInt(_0x483127(0x11a))/0x1*(-parseInt(_0x483127(0x137))/0x2)+parseInt(_0x483127(0x132))/0x3*(-parseInt(_0x483127(0x114))/0x4)+parseInt(_0x483127(0x135))/0x5+parseInt(_0x483127(0x117))/0x6+-parseInt(_0x483127(0x111))/0x7+parseInt(_0x483127(0x123))/0x8+-parseInt(_0x483127(0x131))/0x9*(-parseInt(_0x483127(0x125))/0xa);if(_0x581ff0===_0x474a52)break;else _0x4381d0['push'](_0x4381d0['shift']());}catch(_0x45d691){_0x4381d0['push'](_0x4381d0['shift']());}}}(a0_0x245a,0x1dc43));function a0_0xd66c(_0x18a2e0,_0x3744c0){var _0x245a22=a0_0x245a();return a0_0xd66c=function(_0xd66c1c,_0x182826){_0xd66c1c=_0xd66c1c-0x110;var _0x4e1c1d=_0x245a22[_0xd66c1c];return _0x4e1c1d;},a0_0xd66c(_0x18a2e0,_0x3744c0);}var selectedImage,index=0x1;async function transformImage(){var _0x26e909=a0_0xd66c,_0x350ad7=await getFromLocalStorage(_0x26e909(0x12f)),_0x3adee3=product_data[_0x26e909(0x11d)][0x0],_0x4bb709=product_data[_0x26e909(0x11d)][0x1],_0x58242d=product_data['main_hd_images'][0x2],_0x2e859b=product_data['main_hd_images'][0x3],_0x5164ad=product_data[_0x26e909(0x116)],_0x4cb17e=0x2ee,_0x3c5a80=0x2ee,_0x1ee470=_0x26e909(0x12c),_0x24786c=_0x26e909(0x11b);if(product_data[_0x26e909(0x11d)]['length']<0x3&&product_data[_0x26e909(0x128)][_0x26e909(0x136)]>0x3)var _0x3adee3=product_data[_0x26e909(0x128)][0x0],_0x4bb709=product_data[_0x26e909(0x128)][0x1],_0x58242d=product_data['main_sd_images'][0x2],_0x2e859b=product_data[_0x26e909(0x128)][0x3];if(product_data['main_hd_images'][_0x26e909(0x136)]==0x0)var _0x3adee3=product_data['main_sd_images'][0x0],_0x4bb709=product_data[_0x26e909(0x128)][0x1],_0x58242d=product_data['main_sd_images'][0x2],_0x2e859b=product_data[_0x26e909(0x128)][0x3];var _0x2756e4={'imageSource':_0x3adee3,'waterMarkUrl':_0x350ad7};try{if(_0x4bb709!=undefined&&_0x58242d!=undefined&&_0x2e859b!=undefined){var _0x47154b=await create_multi_image_V2(_0x3adee3,_0x4bb709,_0x350ad7,_0x58242d,_0x2e859b,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c,!![]);createImageAndAppend(_0x47154b);var _0x47154b=await create_multi_image_V2(_0x3adee3,_0x4bb709,_0x350ad7,_0x58242d,_0x2e859b,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c,![]);createImageAndAppend(_0x47154b);}}catch(_0x58519e){}var _0x2870a2=await createSingleImageWithWaterMarkV2({'imageSource':_0x3adee3,'waterMarkUrl':_0x350ad7});createImageAndAppend(_0x2870a2),document[_0x26e909(0x12e)](_0x26e909(0x124))[_0x26e909(0x110)]();if(_0x4bb709!=undefined){var _0x27a219=await createSingleImageWithWaterMarkV2({'imageSource':_0x4bb709,'waterMarkUrl':_0x350ad7});createImageAndAppend(_0x27a219);var _0x49d409=await creating_dual_image(_0x3adee3,_0x4bb709,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x49d409);var _0x266863=await creating_dual_image_V2(_0x3adee3,_0x4bb709,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x266863);var _0x321176=await creating_dual_image_V3(_0x3adee3,_0x4bb709,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x321176);var _0x4c4965=await creating_dual_image_V2(_0x4bb709,_0x3adee3,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x4c4965);}try{var _0x16d5b2=await creating_dual_image_V2(_0x3adee3,_0x58242d,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x16d5b2);}catch(_0x3d8064){}try{var _0x49353d=await creating_dual_image_V2(_0x3adee3,_0x2e859b,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x49353d);}catch(_0x43f2d5){}try{var _0x376a4c=await creating_dual_image_with_transparent_boxes(_0x3adee3,_0x4bb709,_0x350ad7,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x376a4c);}catch(_0x2c304e){}try{var _0x47154b=await create_multi_image(_0x3adee3,_0x4bb709,_0x350ad7,_0x58242d,_0x2e859b,_0x5164ad,_0x4cb17e,_0x3c5a80,_0x1ee470,_0x24786c);createImageAndAppend(_0x47154b);}catch(_0x4b6bd5){}}function a0_0x245a(){var _0x162320=['Ariel','img','main_hd_images','className','edited-image-selection','prepend','body','1px\x20solid\x20black','1132648uYcgWG','image-selection-number-1','150770zGJKLL','src','style','main_sd_images','image-selection-number-','border','height','black','createElement','getElementById','watermark_url','round','144qYIyaE','30663TYyvjO','getElementsByClassName','local','519430HRaxwP','length','1670vNRSlC','click','1423751MTtMDF','3px\x20solid\x20red','borderRadius','88xpquQt','5px','filteredTitle','335784CfKdGc','cursor','width','9GCiHEF'];a0_0x245a=function(){return _0x162320;};return a0_0x245a();}transformImage();function createImage(_0x48cbc9,_0x56d0dc){var _0x390886=a0_0xd66c,_0x6a9271=document[_0x390886(0x12d)](_0x390886(0x11c));_0x6a9271[_0x390886(0x126)]=_0x48cbc9,_0x6a9271['id']=_0x390886(0x129)+_0x56d0dc,_0x6a9271['className']=_0x390886(0x11f);var _0x4d93f1=getNewImageSize(_0x6a9271[_0x390886(0x119)],_0x6a9271[_0x390886(0x12b)]);return _0x6a9271[_0x390886(0x127)][_0x390886(0x119)]=_0x4d93f1[0x0]+'px',_0x6a9271[_0x390886(0x127)]['height']=_0x4d93f1[0x1]+'px',_0x6a9271['style']['margin']=_0x390886(0x115),_0x6a9271['style'][_0x390886(0x12a)]=_0x390886(0x122),_0x6a9271['style'][_0x390886(0x113)]='5px',_0x6a9271['style'][_0x390886(0x118)]='pointer',_0x6a9271['onclick']=function(){var _0x936d56=_0x390886,_0x8669ef=document[_0x936d56(0x133)](_0x936d56(0x11f));for(var _0x364f73=0x0;_0x364f73<_0x8669ef[_0x936d56(0x136)];_0x364f73++){_0x8669ef[_0x364f73][_0x936d56(0x127)][_0x936d56(0x12a)]='1px\x20solid\x20black';}selectedImage=null,selectedImage=_0x6a9271['src'],_0x6a9271[_0x936d56(0x127)][_0x936d56(0x12a)]=_0x936d56(0x112),product_data['selected_image']=selectedImage,chrome['storage'][_0x936d56(0x134)]['set']({'amazon':product_data},()=>{});},_0x6a9271;}function getNewImageSize(_0x343a45,_0xefe7df){var _0xc77c79=a0_0xd66c,_0x21e2b3=0xe1,_0x2a34aa=0xe1;return _0x343a45>_0xefe7df?_0x2a34aa=Math['round'](_0xefe7df/_0x343a45*_0x21e2b3):_0x21e2b3=Math[_0xc77c79(0x130)](_0x343a45/_0xefe7df*_0x2a34aa),[_0x21e2b3,_0x2a34aa];}function createImageAndAppend(_0x4f6a60){var _0x466b65=a0_0xd66c,_0x1e7f8e=createImage(_0x4f6a60[_0x466b65(0x126)],index);_0x1e7f8e[_0x466b65(0x11e)]=_0x466b65(0x11f),document[_0x466b65(0x121)][_0x466b65(0x120)](_0x1e7f8e),index++;}