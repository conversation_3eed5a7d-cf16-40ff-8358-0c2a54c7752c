How to Export Docker Containers


To get container ID:
Docker PS


Run this code:

docker save mlapp2 -o C:\Users\<USER>\Documents\saved_mlapp2.tar

importing windows

docker load -i saved_mlapp2.tar
docker load -i saved_mlapp1.tar


Running the software with ports



docker run -i -p 9003:5000 mlapp1
docker run -i -p 9005:5000 mlapp2





------------Not Working------------

curl example


curl http://127.0.0.1:9003 \
         -X POST \
	-H "Accept: application/json" \
          -d 'The tower is 324 metres ' 

   
    



    curl http://127.0.0.1:9003 \
	-X POST \
	'{"inputs": "Treeeeeeeeeeeeeeeeee"}' \
	-H "Accept: application/json" \
