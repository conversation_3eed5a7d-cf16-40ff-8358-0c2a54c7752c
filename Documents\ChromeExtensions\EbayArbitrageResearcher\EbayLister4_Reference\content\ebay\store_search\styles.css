.btn-stop-extract-all{
    background-color: #d9534f;
    border-color: #d43f3a;
    color: #fff;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    cursor: pointer;
    
}

.btn-stop-extract-all:hover{
    background-color: #c9302c;
    border-color: #ac2925;
    color: #fff;
}

.btn-stop-extract-all:active{
    background-color: #c9302c;
    border-color: #ac2925;
    color: #fff;
}

.btn-stop-extract-all:focus{
    background-color: #d9534f;
    border-color: #d43f3a;
    color: #fff;
}


.btn-extract-all{
    background-color: #5cb85c;
    border-color: #4cae4c;
    color: #fff;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    cursor: pointer;

    margin-left: 10px;
    
}

.btn-extract-all:hover{
    background-color: #449d44;
    border-color: #398439;
    color: #fff;
}

.btn-extract-all:active{
    background-color: #449d44;
    border-color: #398439;
    color: #fff;
}

.btn-extract-all:focus{
    background-color: #5cb85c;
    border-color: #4cae4c;
    color: #fff;
}



.btn-show-all-titles{
    background-color: #5bc0de;
    border-color: #46b8da;
    color: #fff;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    cursor: pointer;
    
}

.btn-show-all-titles:hover{
    background-color: #31b0d5;
    border-color: #269abc;
    color: #fff;
}

.btn-show-all-titles:active{
    background-color: #31b0d5;
    border-color: #269abc;
    color: #fff;
}

.btn-show-all-titles:focus{
    background-color: #5bc0de;
    border-color: #46b8da;
    color: #fff;
}



.btn-clear-all-titles{
    background-color: #f0ad4e;
    border-color: #eea236;
    color: #fff;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    cursor: pointer;
    
}

.btn-clear-all-titles:hover{
    background-color: #ec971f;
    border-color: #d58512;
    color: #fff;
}


.btn-clear-all-titles:active{
    background-color: #ec971f;
    border-color: #d58512;
    color: #fff;
}

.btn-clear-all-titles:focus{
    background-color: #f0ad4e;
    border-color: #eea236;
    color: #fff;
}



#keyword_modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

#keyword_modal_content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */

    text-align: center;  /* Center the content */
}

#keyword_modal_close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

#keyword_modal_close:hover,
#keyword_modal_close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}


#keyword_modal_body textarea,
#keyword_modal_footer button {
    display: block;
    margin: 0 auto;  /* Center the elements */
}



.filter-dropdown-class {
    font-size: 16px;
    padding: 10px 15px;
    margin: 10px 0;
    border: 1px solid #dcdcdc;
    border-radius: 5px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease-in-out;
    appearance: none;
    cursor: pointer;
    outline: none;
}

.filter-dropdown-class:hover {
    border-color: #a5a5a5;
}

.filter-dropdown-class:focus {
    border-color: #333;
}





/* Style for the Terapeak Search Button */
.btn-terapeak-search {
    background: linear-gradient(90deg, #74cf70, #3fa55f);
    border: none;
    border-radius: 5px;
    color: white;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    margin: 10px 0; /* Add some margin to separate it from other elements */
}

.btn-terapeak-search:hover {
    background: linear-gradient(90deg, #3fa55f, #74cf70);
    box-shadow: 0 4px 10px rgba(0,0,0,0.5);
}




.total-competitors-element {
    background: #f0faff; /* A light blue background */
    border: 1px solid #7070cc; /* Border color changed to a blue shade */
    border-radius: 5px;
    color: #3f55a5; /* Text color changed to a darker blue */
    padding: 5px 10px;
    font-size: 1em;
    margin-top: 10px; /* Add some margin to separate it from other elements */
    display: inline-block;
}

/* Updated style with blue gradient background */
.total-competitors-element.alt-style {
    background: linear-gradient(90deg, #70a5cf, #3f55a5);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Updated hover effect with reversed blue gradient background */
.total-competitors-element.alt-style:hover {
    background: linear-gradient(90deg, #3f55a5, #70a5cf);
    box-shadow: 0 4px 10px rgba(0,0,0,0.5);
}




/* Label Styling */
.filter-label {
    display: inline-block;
    margin: 10px;
    padding: 5px;
    font-size: 14px;
    color: #333;
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 5px;
}

/* Input Field Styling */
.filter-input {
    margin-left: 5px;
    padding: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
}


/* Smooth scrolling */
html {
    scroll-behavior: smooth;
  }
  
  /* Initial style for the item wrapper */
  .s-item__wrapper {
    transition: background-color 0.3s ease, transform 0.3s ease;
    transform: scale(1);
  }
  
  /* Highlighted style for the item wrapper */
  .s-item__wrapper.highlight {
    background-color: yellow;
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
  }
  





  /* overlay
   */

   /* Overlay styles */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.5s ease;
}

/* Text styles within the overlay */
.overlay .text {
    color: #fff;
    font-size: 2em;
}

/* Animation for fade-in effect */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}




/* CSS for dramatic pop-in animation of the appended elements */
.total-sold-element, .total-competitors-element {
    opacity: 0;
    transform: scale(0);
    transition: none; /* Removing transition to use keyframe animation instead */
}

/* Keyframes for the dramatic animation */
@keyframes popIn {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    80% {
        opacity: 0.8;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* CSS class to trigger the dramatic pop-in animation */
.show-element {
    animation: popIn 0.8s ease both; /* Applying the keyframe animation */
}



.filter-dropdown-class{
    font-size: 16px;
    padding: 10px 15px;
    margin: 10px 0;
    border: 1px solid #dcdcdc;
    border-radius: 5px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease-in-out;
    appearance: none;
    cursor: pointer;
    outline: none;
}

.filter-dropdown-class:hover{
    border-color: #a5a5a5;
}

.filter-dropdown-class:focus{
    border-color: #333;
}



#item-buttons-div {
    display: flex; /* Enables the use of Flexbox for layout */
    flex-direction: column; /* Arranges children (buttons) in a vertical column */
    align-items: flex-start; 
    gap: 15px; /* Adds space between each button/row */
    padding: 20px; /* Adds padding inside the div for some breathing room around the edges */
}


#btn-check-purchase-history {
    background-color: #555; /* Dark grey background */
    color: #fff; /* White text */
    padding: 5px 10px; 
    border: none; /* No border */
    border-radius: 2px; /* Rounded corners */
    cursor: pointer; /* Cursor pointer to indicate button */
    font-size: 16px; /* Larger font size for better readability */
    font-family: Arial, sans-serif; /* Standard, compact font-family */
    transition: background-color 0.2s, box-shadow 0.2s; /* Smooth transition for background and shadow */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Subtle shadow for a slight depth */
}

#btn-check-purchase-history:hover {
    background-color: #666; /* Slightly lighter grey on hover */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Increased shadow for more depth on hover */
}

#btn-check-purchase-history:active {
    background-color: #444; /* Darker grey on click */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Revert to subtle shadow */
    position: relative;
    top: 1px; /* Slightly move down the button on click for tactile feedback */
}

#btn-check-purchase-history:focus {
    background-color: #555; /* Dark grey background */
    color: #fff; /* White text */
    padding: 10px 20px; /* Padding for a larger button */
    border: none; /* No border */
    border-radius: 5px; /* Rounded corners */
    cursor: pointer; /* Cursor pointer to indicate button */
    font-size: 16px; /* Larger font size for better readability */
    font-family: Arial, sans-serif; /* Standard, compact font-family */
    transition: background-color 0.2s, box-shadow 0.2s; /* Smooth transition for background and shadow */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Subtle shadow for a slight depth */
}


#breakEvenPrice{
    color: red;
}






.total-sold-table {
    max-width: 300px; /* Keep the table compact */
    border-collapse: collapse;
    font-size: 12px !important; /* Maintain small font size */
    color: #333 !important; /* Dark text color */

    /* add space above and below and left and right of the table */
    margin: 20px;
    
}
.total-sold-table, .total-sold-table th, .total-sold-table td {
    border: 1px solid #ccc !important; /* Subtle border color */
    text-align: center !important; /* Centered text for better alignment */
    padding: 2px !important; /* Compact padding */
}
.total-sold-table th {
    background-color: #4CAF50 !important; /* Green header background */
    color: white !important; /* White text for header */
}
.total-sold-table tr:nth-child(odd) {
    background-color: #f2f2f2 !important; /* Light grey for odd rows */
}
.total-sold-table tr:nth-child(even) {
    background-color: #ffffff !important; /* White for even rows */
}




#scan-settings-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    max-width: 400px;
}

#scan-settings-container label {
    margin-bottom: 10px;
}

#scan-settings-container select {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

#scan-settings-container label,
#scan-settings-container select {
    display: block;
    font-size: 14px;
    line-height: 1.5;
}
