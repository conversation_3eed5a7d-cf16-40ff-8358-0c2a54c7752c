const a0_0x3240b7=a0_0x242d;(function(_0x4341e1,_0x30da78){const _0x3121a6=a0_0x242d,_0x18863e=_0x4341e1();while(!![]){try{const _0x43a037=parseInt(_0x3121a6(0xee))/0x1+parseInt(_0x3121a6(0x12a))/0x2+parseInt(_0x3121a6(0xd8))/0x3+parseInt(_0x3121a6(0x119))/0x4*(-parseInt(_0x3121a6(0xdc))/0x5)+-parseInt(_0x3121a6(0xe3))/0x6*(-parseInt(_0x3121a6(0xff))/0x7)+parseInt(_0x3121a6(0x139))/0x8*(-parseInt(_0x3121a6(0x121))/0x9)+-parseInt(_0x3121a6(0xe6))/0xa;if(_0x43a037===_0x30da78)break;else _0x18863e['push'](_0x18863e['shift']());}catch(_0x50466b){_0x18863e['push'](_0x18863e['shift']());}}}(a0_0x3e55,0x2ae3d),console[a0_0x3240b7(0x11d)](a0_0x3240b7(0xdf)));var EbayApi={async 'printTest'(){const _0x31846c=a0_0x3240b7;console[_0x31846c(0x11d)]('printTest\x20function');},async 'getCategoryRecommendations'(_0x337065){const _0x17d853=a0_0x3240b7,_0x279984=getCurrentEbaySuffix(),_0x2744a6=_0x17d853(0x12e)+_0x279984+_0x17d853(0xfd)+encodeURIComponent(_0x337065),_0x14f2f2=await fetch(_0x2744a6,{'method':_0x17d853(0xe7),'credentials':_0x17d853(0x144)});if(!_0x14f2f2['ok'])throw new Error('Failed\x20to\x20get\x20category\x20recommendations.\x20Status:\x20'+_0x14f2f2[_0x17d853(0x103)]);return await _0x14f2f2[_0x17d853(0x107)]();},async 'createListing'(_0x5122ad,_0x2d9f74){const _0x41018f=a0_0x3240b7,_0x123165=getCurrentEbaySuffix(),_0xc97bc=new URLSearchParams();_0xc97bc['append'](_0x41018f(0xd4),_0x41018f(0xd2)),_0xc97bc[_0x41018f(0x111)](_0x41018f(0x10f),_0x2d9f74[_0x41018f(0x133)]()),_0xc97bc[_0x41018f(0x111)](_0x41018f(0x13a),_0x5122ad),_0xc97bc[_0x41018f(0x111)]('condition',_0x41018f(0x10e));const _0x365d39='https://www.ebay.'+_0x123165+_0x41018f(0xf7)+_0xc97bc[_0x41018f(0x133)](),_0x519354=await fetch(_0x365d39,{'method':_0x41018f(0xe7),'credentials':_0x41018f(0x144)});if(!_0x519354['ok'])throw new Error(_0x41018f(0x123)+_0x519354[_0x41018f(0x103)]);console[_0x41018f(0x11d)](_0x41018f(0xd3),_0x519354);var _0x32c1d1=await _0x519354[_0x41018f(0xea)]();return parseSessionData(_0x32c1d1);},async 'updateListing'(_0x14bd7e,_0x7cdb89,_0x460f72,_0x375e9d,_0x34d4c4){const _0x4ff78f=a0_0x3240b7,_0x214a49=getCurrentEbaySuffix(),_0x12fc7d=_0x4ff78f(0x12e)+_0x214a49+_0x4ff78f(0xec)+_0x14bd7e+_0x4ff78f(0x106),_0x2f1839=await fetch(_0x12fc7d,{'method':'PUT','credentials':'include','headers':{'Content-Type':_0x4ff78f(0x120),'srt':_0x7cdb89},'body':JSON['stringify'](_0x34d4c4)});if(!_0x2f1839['ok'])throw new Error(_0x4ff78f(0xfa)+_0x2f1839['status']);const _0xe27486=await _0x2f1839[_0x4ff78f(0x107)]();if(_0xe27486&&_0xe27486[_0x4ff78f(0xe5)])throw new Error(_0x4ff78f(0x11f)+_0xe27486[_0x4ff78f(0xe5)]);return _0xe27486;},async 'addVariations'(_0x175e57,_0x37b1bf,_0x295dbe,_0x10bfa4){const _0x4c04d2=a0_0x3240b7,_0xdb34d2=getCurrentEbaySuffix(),_0x595566=_0x295dbe[_0x4c04d2(0x112)][_0x4c04d2(0xf6)]((_0x13dbe3,_0x190389)=>{const _0x34fe7f=_0x4c04d2;let _0x1a91f5={};return Object['keys'](_0x13dbe3[_0x34fe7f(0xd7)])[_0x34fe7f(0xf9)](_0x5a49da=>{const _0x40ee17=_0x34fe7f;_0x1a91f5[_0x5a49da]=_0x13dbe3[_0x40ee17(0xd7)][_0x5a49da]['productName'];}),{'index':_0x190389,'variationSpecific':_0x1a91f5,'listingVariation':{'price':_0x13dbe3[_0x34fe7f(0x14b)]['toFixed'](0x2),'quantity':_0x13dbe3['quantity']||0x1},'state':_0x34fe7f(0xe0)};}),_0x401a7a={'action':_0x4c04d2(0xed),'draftId':_0x175e57,'listingMode':'AddItem','variationItem':_0x595566},_0x4435cc='https://bulkedit.ebay.'+_0xdb34d2+_0x4c04d2(0xef),_0x279e0b=await fetch(_0x4435cc,{'method':'POST','credentials':_0x4c04d2(0x144),'body':JSON[_0x4c04d2(0xd5)](_0x401a7a)});if(!_0x279e0b['ok'])throw new Error(_0x4c04d2(0xfb)+_0x279e0b[_0x4c04d2(0x103)]);const _0x2b2e37=await _0x279e0b[_0x4c04d2(0x107)]();if(_0x2b2e37&&_0x2b2e37[_0x4c04d2(0xe5)])throw new Error(_0x4c04d2(0x129)+_0x2b2e37[_0x4c04d2(0xe5)]);},async 'uploadPhotoToEbay'(_0x570b44,_0x224a25){const _0x45236d=a0_0x3240b7,_0x31feb1=new FormData();console[_0x45236d(0x11d)](_0x45236d(0xd6),_0x224a25),console[_0x45236d(0x11d)]('imageUrl',_0x570b44),_0x31feb1[_0x45236d(0x111)]('s','SuperSize'),_0x31feb1[_0x45236d(0x111)]('n','i'),_0x31feb1[_0x45236d(0x111)]('v','2'),_0x31feb1['append'](_0x45236d(0xe9),_0x224a25['uaek']),_0x31feb1['append'](_0x45236d(0x128),_0x224a25[_0x45236d(0x128)]),_0x31feb1[_0x45236d(0x111)](_0x45236d(0x141),'2'),_0x31feb1[_0x45236d(0x111)]('wm',''),_0x31feb1[_0x45236d(0x111)]('w',_0x570b44);const _0x4a5843=await fetch(_0x45236d(0x10c),{'method':_0x45236d(0x113),'body':_0x31feb1});console[_0x45236d(0x11d)](_0x45236d(0x12c),_0x4a5843);const _0xc8dd44=(await _0x4a5843[_0x45236d(0xea)]())[_0x45236d(0xe1)](';');console[_0x45236d(0x11d)](_0x45236d(0xea),_0xc8dd44);if(_0xc8dd44[_0x45236d(0x137)]<0x2)throw new Error(_0x45236d(0x12d));if(_0xc8dd44[0x1]==='ERROR:WC002')throw new Error(_0x45236d(0x105));return _0xc8dd44[0x1];},async 'uploadImageFileToEbay'(_0x314c5c,_0x4db0fc){const _0x1e9744=a0_0x3240b7,_0x269923=new FormData();_0x269923['append']('s',_0x1e9744(0x13d)),_0x269923['append']('n','i'),_0x269923[_0x1e9744(0x111)]('v','2'),_0x269923[_0x1e9744(0x111)](_0x1e9744(0xe9),_0x4db0fc[_0x1e9744(0xe9)]),_0x269923[_0x1e9744(0x111)]('uaes',_0x4db0fc[_0x1e9744(0x128)]),_0x269923['append'](_0x1e9744(0x141),'2'),_0x269923[_0x1e9744(0x111)]('wm',''),_0x269923[_0x1e9744(0x111)]('i',_0x314c5c),console[_0x1e9744(0x11d)]('sending\x20formData',_0x269923);var _0x39fcff=await fetch('https://msa-b1.ebay.com/ws/eBayISAPI.dll?EpsBasic',{'method':_0x1e9744(0x113),'body':_0x269923});console[_0x1e9744(0x11d)](_0x1e9744(0x12c),_0x39fcff);const _0x1a9f61=(await _0x39fcff['text']())[_0x1e9744(0xe1)](';');console[_0x1e9744(0x11d)](_0x1e9744(0xea),_0x1a9f61);if(_0x1a9f61[_0x1e9744(0x137)]<0x2)throw new Error('Photo\x20upload\x20failed:\x20no\x20valid\x20response\x20from\x20eBay');if(_0x1a9f61[0x1]===_0x1e9744(0xdd))throw new Error('Photo\x20upload\x20error:\x20eBay\x20returned\x20WC002');return _0x1a9f61[0x1];}};async function uploadVariationImages(_0x5949d7,_0x2c336c,_0x2d99b9,_0x24ffa4){const _0x1b7ed6=a0_0x3240b7;console[_0x1b7ed6(0x11d)]('===\x20uploadVariationImages()\x20called\x20==='),console[_0x1b7ed6(0x11d)](_0x1b7ed6(0x11e),_0x5949d7),console[_0x1b7ed6(0x11d)]('epsData:',_0x2c336c),console[_0x1b7ed6(0x11d)](_0x1b7ed6(0x135),JSON[_0x1b7ed6(0xd5)](_0x2d99b9,null,0x2)),console[_0x1b7ed6(0x11d)](_0x1b7ed6(0xd1),_0x24ffa4),_0x2d99b9[_0x1b7ed6(0x112)]=_0x2d99b9[_0x1b7ed6(0x112)][_0x1b7ed6(0x13f)](0x0,0x1);(!_0x2d99b9[_0x1b7ed6(0x117)]||!_0x2d99b9[_0x1b7ed6(0x117)][_0x1b7ed6(0x104)]())&&(_0x2d99b9[_0x1b7ed6(0x117)]=_0x1b7ed6(0x130));(!_0x2d99b9['images']||_0x2d99b9[_0x1b7ed6(0x110)][_0x1b7ed6(0x137)]===0x0)&&(_0x2d99b9['variations']&&_0x2d99b9['variations'][_0x1b7ed6(0x137)]>0x0&&_0x2d99b9[_0x1b7ed6(0x112)][0x0]['image']?_0x2d99b9[_0x1b7ed6(0x110)]=[_0x2d99b9[_0x1b7ed6(0x112)][0x0][_0x1b7ed6(0xf2)]]:_0x2d99b9[_0x1b7ed6(0x110)]=[_0x1b7ed6(0x148)]);(!_0x2d99b9['title']||!_0x2d99b9[_0x1b7ed6(0x13a)][_0x1b7ed6(0x104)]())&&(_0x2d99b9[_0x1b7ed6(0x13a)]=_0x1b7ed6(0xf0));if(!_0x2d99b9[_0x1b7ed6(0x112)]||!Array['isArray'](_0x2d99b9['variations'])){console[_0x1b7ed6(0x126)](_0x1b7ed6(0x13b));return;}_0x2d99b9[_0x1b7ed6(0x112)]=_0x2d99b9[_0x1b7ed6(0x112)][_0x1b7ed6(0x13f)](0x0,0x1);const _0x518bc0=_0x2d99b9['variations'][_0x1b7ed6(0xf6)]((_0x25ee11,_0x45e953)=>{const _0x3c41fc=_0x1b7ed6;console['log']('---\x20Transforming\x20Variation\x20#'+_0x45e953+_0x3c41fc(0x115),_0x25ee11);const _0x451fd6={};for(const _0x417380 in _0x25ee11[_0x3c41fc(0x114)]){const _0x11cd26=_0x25ee11[_0x3c41fc(0x114)][_0x417380];_0x451fd6[_0x417380]={'productName':_0x11cd26};}const _0x4b2330=typeof _0x25ee11[_0x3c41fc(0x114)][_0x3c41fc(0x132)]==='string',_0x2c3986=_0x4b2330?'Color':_0x3c41fc(0x140),_0x4a1df7=_0x25ee11[_0x3c41fc(0x124)]?_0x25ee11[_0x3c41fc(0x149)]||0x1:0x0;let _0x2c89a2=_0x25ee11[_0x3c41fc(0x11a)]||_0x25ee11[_0x3c41fc(0xd9)]||0x1;return typeof _0x2c89a2!==_0x3c41fc(0x14a)&&(_0x2c89a2=parseFloat(_0x2c89a2)||0x1),_0x2c89a2<0.99&&(_0x2c89a2=10.99),{'attrs':_0x451fd6,'img':_0x25ee11[_0x3c41fc(0xf2)]||'','imgProp':_0x2c3986,'price':_0x2c89a2,'currency':_0x3c41fc(0xd0),'quantity':_0x4a1df7};});_0x2d99b9['variations']=_0x518bc0;if(_0x2d99b9[_0x1b7ed6(0x112)][_0x1b7ed6(0x137)]===0x0){console[_0x1b7ed6(0x126)](_0x1b7ed6(0xeb));return;}const _0x584326=[],_0x401236={},_0x32e132=new Map();let _0x2aaa7f=_0x2d99b9['variations'][0x0]?.[_0x1b7ed6(0x108)]||'';for(let _0xd0c007=0x0;_0xd0c007<_0x2d99b9[_0x1b7ed6(0x112)][_0x1b7ed6(0x137)];_0xd0c007++){const _0x45ec52=_0x2d99b9[_0x1b7ed6(0x112)][_0xd0c007],{attrs:_0x3ad216,img:_0x5e9694,imgProp:_0x90ca7a,price:_0x3b4021,currency:_0x51e831,quantity:quantity=0x1}=_0x45ec52;let _0x4a41f3='';if(_0x5e9694){if(!_0x32e132['has'](_0x5e9694))try{console[_0x1b7ed6(0x11d)](_0x1b7ed6(0x138)+_0xd0c007+_0x1b7ed6(0xf3));var _0x527497=await urlToImage(_0x5e9694);console['log'](_0x1b7ed6(0xda)),_0x527497=await upscaleToMinimumSize(_0x527497,0x1f4,0x1f4);var _0x45c84a=_0x527497[_0x1b7ed6(0x12f)],_0x58f6d7=b64StringToFile(_0x45c84a,_0x1b7ed6(0x101));console[_0x1b7ed6(0x11d)](_0x1b7ed6(0x10b));var _0x3ebbbf=await EbayApi[_0x1b7ed6(0x102)](_0x58f6d7,_0x2c336c);console[_0x1b7ed6(0x11d)](_0x1b7ed6(0x11b),_0x3ebbbf),_0x32e132['set'](_0x5e9694,_0x3ebbbf),_0x4a41f3=_0x3ebbbf;}catch(_0x5c07d4){console[_0x1b7ed6(0xe5)](_0x1b7ed6(0xf4)+_0xd0c007+':',_0x5c07d4);}else _0x4a41f3=_0x32e132[_0x1b7ed6(0x122)](_0x5e9694);}if(_0x2aaa7f&&_0x3ad216[_0x2aaa7f]?.['productName']&&_0x4a41f3){const _0x3e847a=_0x2aaa7f,_0x2352a3=_0x3ad216[_0x3e847a]['productName'];!_0x401236[_0x3e847a]&&(_0x401236[_0x3e847a]={}),!_0x401236[_0x3e847a][_0x2352a3]&&(_0x401236[_0x3e847a][_0x2352a3]=[]),_0x401236[_0x3e847a][_0x2352a3][_0x1b7ed6(0x125)](_0x4a41f3);}const _0x298180={};for(const _0x3bab34 in _0x3ad216){_0x298180[_0x3bab34]=_0x3ad216[_0x3bab34]['productName'];}const _0x4a6c57={'price':_0x3b4021['toFixed'](0x2),'quantity':quantity};_0x584326['push']({'variationSpecific':_0x298180,'listingVariation':_0x4a6c57,'index':_0xd0c007,'state':'enabled','sku':_0x1b7ed6(0xe2)+_0xd0c007});}const _0x570a94={'action':'save','draftId':_0x5949d7,'listingMode':'AddItem','restricted':![],'upiFieldName':_0x1b7ed6(0x127),'variationItem':_0x584326,'variationSpecificPictureSet':_0x401236,'variationSpecificsMetaData':gatherAllAttributes(_0x584326)},_0x2eab38=getCurrentEbaySuffix(),_0x5e56d3='https://bulkedit.ebay.'+_0x2eab38+'/msku-update';console[_0x1b7ed6(0x11d)]('Sending\x20variations\x20payload\x20to\x20eBay\x20Bulk\x20Editor:',JSON[_0x1b7ed6(0xd5)](_0x570a94,null,0x2));const _0x513077=await fetch(_0x5e56d3,{'method':'POST','credentials':_0x1b7ed6(0x144),'body':JSON[_0x1b7ed6(0xd5)](_0x570a94),'headers':{'Content-Type':_0x1b7ed6(0xe4)}}),_0x4b74d4=await _0x513077[_0x1b7ed6(0x107)]();console[_0x1b7ed6(0x11d)](_0x1b7ed6(0xdb),JSON[_0x1b7ed6(0xd5)](_0x4b74d4,null,0x2));}function gatherAllAttributes(_0x3bb32b){const _0x492ac2=a0_0x3240b7;console[_0x492ac2(0x11d)]('gatherAllAttributes()\x20called.\x20Building\x20sets\x20of\x20unique\x20attribute\x20values...');const _0x7ff76e={};for(const _0x53f4ac of _0x3bb32b){for(const _0x41c753 in _0x53f4ac[_0x492ac2(0x13e)]){const _0x3f5687=_0x53f4ac['variationSpecific'][_0x41c753];!_0x7ff76e[_0x41c753]&&(_0x7ff76e[_0x41c753]=new Set()),_0x7ff76e[_0x41c753][_0x492ac2(0x136)](_0x3f5687);}}const _0x529066=[];for(const _0x5774c5 in _0x7ff76e){_0x529066[_0x492ac2(0x125)]({'name':_0x5774c5,'value':Array[_0x492ac2(0xfe)](_0x7ff76e[_0x5774c5])});}return console[_0x492ac2(0x11d)](_0x492ac2(0x10a),JSON[_0x492ac2(0xd5)](_0x529066,null,0x2)),_0x529066;}function a0_0x242d(_0x1195ae,_0x18774c){const _0x3e5552=a0_0x3e55();return a0_0x242d=function(_0x242de8,_0x43b3ac){_0x242de8=_0x242de8-0xd0;let _0x3778c6=_0x3e5552[_0x242de8];return _0x3778c6;},a0_0x242d(_0x1195ae,_0x18774c);}function getCurrentEbaySuffix(){const _0x5d4b39=a0_0x3240b7;if(window[_0x5d4b39(0xf5)][_0x5d4b39(0xf8)][_0x5d4b39(0x116)]('ebay.co.uk'))return _0x5d4b39(0x147);if(window[_0x5d4b39(0xf5)][_0x5d4b39(0xf8)]['includes']('ebay.com.au'))return'com.au';return _0x5d4b39(0xfc);}function extractMatches(_0x5ca6be,_0x15c291){const _0x184096=a0_0x3240b7;let _0x1e6b74,_0x2086bf=[];while((_0x1e6b74=_0x5ca6be[_0x184096(0x142)](_0x15c291))!==null){_0x1e6b74[_0x184096(0xf1)]===_0x5ca6be[_0x184096(0xe8)]&&_0x5ca6be['lastIndex']++,_0x1e6b74[_0x184096(0xf9)]((_0x560884,_0x36d650)=>{const _0x199cd3=_0x184096;_0x2086bf[_0x199cd3(0x125)](_0x560884);});}return _0x2086bf;}function parseSessionData(_0x11d3c5){const _0x44c6af=a0_0x3240b7;var _0xeefd04=extractMatches(/(?<=\.concat\()[\s\S]*?(?=<\/script>)/gi,_0x11d3c5);console['log']('results',_0xeefd04);var _0x4b88a1=_0xeefd04[_0x44c6af(0x118)](_0x24cebf=>_0x24cebf['includes']('APPSTATUS')&&_0x24cebf[_0x44c6af(0x116)]('widgetConfig'));if(_0x4b88a1<0x0)throw new Error(_0x44c6af(0x134));var _0x5c9c91=_0xeefd04[_0x4b88a1][_0x44c6af(0xde)](/\)(?=[^\)]*$)/,''),_0xf24acf=JSON['parse'](_0x5c9c91);console[_0x44c6af(0x11d)](_0x44c6af(0x14c),_0xf24acf);var _0x2dd27f=_0xf24acf['o']['w'][0x0][0x2]['model'][_0x44c6af(0x10d)][_0x44c6af(0x11c)][_0x44c6af(0x131)][_0x44c6af(0x109)];console[_0x44c6af(0x11d)]('CSRF\x20token:',_0x2dd27f);var _0x16ef7a=_0xf24acf['o']['w'][0x0][0x2][_0x44c6af(0x13c)][_0x44c6af(0x10d)][_0x44c6af(0x11c)][_0x44c6af(0x12b)][_0x44c6af(0x128)],_0x5398a9=_0xf24acf['o']['w'][0x0][0x2][_0x44c6af(0x13c)][_0x44c6af(0x10d)][_0x44c6af(0x11c)][_0x44c6af(0x12b)]['uaek'],_0x4b88a1=_0xeefd04['findIndex'](_0x9cb439=>_0x9cb439[_0x44c6af(0x116)](_0x44c6af(0x11e)));if(_0x4b88a1<0x0)throw new Error(_0x44c6af(0x134));var _0x52f329=_0xeefd04[_0x4b88a1]['replace'](/\)(?=[^\)]*$)/,''),_0x4fcf34=JSON[_0x44c6af(0x100)](_0x52f329);console['log'](_0x44c6af(0x143),_0x4fcf34);var _0x5b4f3f;try{_0x5b4f3f=_0x4fcf34['w'][0x0][0x2][_0x44c6af(0x13c)][_0x44c6af(0x146)][_0x44c6af(0x145)];}catch(_0x195346){console[_0x44c6af(0x11d)](_0x44c6af(0xe5),_0x195346);}if(!_0x5b4f3f)try{_0x5b4f3f=_0x4fcf34['o']['w'][0x0][0x2]['model'][_0x44c6af(0x146)][_0x44c6af(0x145)];}catch(_0x444ca1){console['log'](_0x44c6af(0xe5),_0x444ca1);}return console[_0x44c6af(0x11d)]('Draft\x20ID:',_0x5b4f3f),{'draftId':_0x5b4f3f,'csrf':_0x2dd27f,'uaes':_0x16ef7a,'uaek':_0x5398a9};}function a0_0x3e55(){const _0x2785e1=['images','append','variations','POST','attributes','\x20---','includes','descriptionText','findIndex','368VZZHcc','salePrice','Image\x20uploaded:','widgetConfig','log','draftId:','eBay\x20responded\x20with\x20an\x20error:\x20','application/json;charset=UTF-8','207xDKuEr','get','Failed\x20to\x20create\x20listing.\x20Status:\x20','salable','push','warn','upc','uaes','Error\x20from\x20the\x20msku-update\x20endpoint:\x20','62342uVlsBR','epsData','response','Photo\x20upload\x20failed:\x20no\x20valid\x20response\x20from\x20eBay','https://www.ebay.','src','A\x20wonderful\x20product!','csrf','Color','toString','Could\x20not\x20locate\x20the\x20portion\x20containing\x20draftId\x20or\x20csrf','product\x20(initial):','add','length','Attempting\x20image\x20upload\x20for\x20Variation\x20#','38728PtSJMV','title','No\x20variations\x20found\x20or\x20product.variations\x20is\x20not\x20an\x20array.\x20Exiting.','model','SuperSize','variationSpecific','slice','Size','aXRequest','exec','parsedDraftId','include','draftId','meta','co.uk','https://via.placeholder.com/500','stock','number','price','parsed','USD','smsAspects:','AddItem','response\x20createListing','mode','stringify','sessionData','attrs','364800ygxZxd','originalPrice','Image\x20loaded','Bulk\x20Editor\x20response:','13435ljaOcu','ERROR:WC002','replace','api.js\x20loaded','enabled','split','SKU-','1231266pyFVFM','application/json','error','352700JFDAoJ','GET','lastIndex','uaek','text','No\x20variations\x20after\x20transform.\x20Exiting.','/lstng/api/listing_draft/','save','211512aVeifK','/msku-update','Untitled\x20Product','index','image','...','Image\x20upload\x20failed\x20Variation\x20#','location','map','/lstng?','host','forEach','Failed\x20to\x20update\x20the\x20listing.\x20Status:\x20','Failed\x20to\x20add\x20variations.\x20Status:\x20','com','/sl/prelist/api/category/search?keyword=','from','7TrRqeJ','parse','test.png','uploadImageFileToEbay','status','trim','Photo\x20upload\x20error:\x20eBay\x20returned\x20WC002','?mode=AddItem','json','imgProp','/lstng/api/listing_draft/:draftId(\x5cd+)','gatherAllAttributes()\x20result:','Image\x20file\x20created','https://msa-b1.ebay.com/ws/eBayISAPI.dll?EpsBasic','APPSTATUS','1000','categoryId'];a0_0x3e55=function(){return _0x2785e1;};return a0_0x3e55();}