var a0_0x3ff3fb=a0_0x248a;(function(_0x569efc,_0x1dd7f3){var _0x42f22e=a0_0x248a,_0x32d5d0=_0x569efc();while(!![]){try{var _0x32a6f7=-parseInt(_0x42f22e(0x224))/0x1+parseInt(_0x42f22e(0x236))/0x2*(parseInt(_0x42f22e(0x299))/0x3)+-parseInt(_0x42f22e(0x1fc))/0x4+parseInt(_0x42f22e(0x24b))/0x5*(-parseInt(_0x42f22e(0x235))/0x6)+parseInt(_0x42f22e(0x26a))/0x7*(-parseInt(_0x42f22e(0x2e1))/0x8)+parseInt(_0x42f22e(0x27d))/0x9*(-parseInt(_0x42f22e(0x26b))/0xa)+parseInt(_0x42f22e(0x220))/0xb;if(_0x32a6f7===_0x1dd7f3)break;else _0x32d5d0['push'](_0x32d5d0['shift']());}catch(_0x579c68){_0x32d5d0['push'](_0x32d5d0['shift']());}}}(a0_0x3c01,0xafa70));var shouldStopProcessing=![];console[a0_0x3ff3fb(0x2a8)](a0_0x3ff3fb(0x232));async function getAllSKU(_0x583a3e){var _0x254239=a0_0x3ff3fb,_0x5fe0a0=!![],_0x287c6c=getTotalPgNumber(),_0x1843fc,_0x584d42;while(_0x5fe0a0){_0x1843fc=getCurrentPgNumber(),_0x584d42=await getSkusOnPage(),skuList=skuList[_0x254239(0x1ea)](_0x584d42),await saveSkuListToLocalStorage(_0x584d42),document[_0x254239(0x281)]=_0x254239(0x271)+_0x1843fc+'/'+_0x287c6c+_0x254239(0x2ca)+skuList[_0x254239(0x2ba)],_0x5fe0a0=await doesNextPageExistFunc(),_0x5fe0a0&&(await goToNextPage(),await waitSomeTime(0x3e8),await waitForNextPageToLoad(_0x1843fc+0x1),chrome['runtime'][_0x254239(0x239)]({'type':_0x254239(0x2b9)}),chrome[_0x254239(0x2cd)][_0x254239(0x29b)][_0x254239(0x285)]({'page_number':_0x1843fc+0x1}));}document[_0x254239(0x281)]='Saving\x20Skus\x20-\x20'+document['title'],await saveSkuListToLocalStorage(_0x584d42),document['title']='Saved\x20SKUS:\x20'+skuList[_0x254239(0x2ba)];}function saveSkuListToLocalStorage(_0x31bff0){return new Promise(_0x365a09=>{var _0x2503b1=a0_0x248a;chrome[_0x2503b1(0x2cd)]['local'][_0x2503b1(0x2ef)]('skuList',_0x125271=>{var _0x23e159=_0x2503b1,_0x5f0992=_0x125271['skuList']?_0x125271[_0x23e159(0x29e)][_0x23e159(0x1ea)](_0x31bff0):_0x31bff0;chrome[_0x23e159(0x2cd)][_0x23e159(0x29b)][_0x23e159(0x285)]({'skuList':_0x5f0992},_0x365a09);});});}async function saveTheSkusOnPageManual(){return new Promise(_0x28bd62=>{var _0x6d914b=a0_0x248a;chrome[_0x6d914b(0x2cd)][_0x6d914b(0x29b)][_0x6d914b(0x2ef)](_0x6d914b(0x29e),async _0x574d75=>{var _0x11acd1=_0x6d914b,_0x2ccf90=await getSkusOnPage(),_0x48d238=_0x574d75[_0x11acd1(0x29e)][_0x11acd1(0x1ea)](_0x2ccf90);chrome[_0x11acd1(0x2cd)][_0x11acd1(0x29b)]['set']({'skuList':_0x48d238},_0x28bd62);});});}function waitSomeTime(_0x26e08c){return new Promise(_0xa08a37=>setTimeout(_0xa08a37,_0x26e08c));}async function waitForNextPageToLoad(_0x2652db){while(getCurrentPgNumber()!==_0x2652db){await waitSomeTime(0x3e8);}}function goToNextPage(){var _0x358e52=a0_0x3ff3fb;document[_0x358e52(0x205)]('pagination__next')[0x0][_0x358e52(0x25d)]();}function doesNextPageExistFunc(){var _0x23f06c=a0_0x3ff3fb,_0x44fb7a=document[_0x23f06c(0x274)]('.pagination__next');if(_0x44fb7a)return _0x44fb7a['getAttribute']('aria-disabled')!==_0x23f06c(0x2a6);return![];}function getTotalPgNumber(){var _0x5db5b2=a0_0x3ff3fb,_0x2d8075=document['querySelector']('.go-to-page\x20.label');if(_0x2d8075)return parseInt(_0x2d8075[_0x5db5b2(0x1f8)][_0x5db5b2(0x21f)](/\D/g,''));var _0x34019d=document[_0x5db5b2(0x1ef)](_0x5db5b2(0x26d));if(_0x34019d[_0x5db5b2(0x2ba)]>0x0){var _0x59a402=_0x34019d[_0x34019d[_0x5db5b2(0x2ba)]-0x1];return parseInt(_0x59a402['innerText']);}return 0x1;}function getCurrentPgNumber(){var _0x1e7763=a0_0x3ff3fb,_0x91d503=document['querySelector'](_0x1e7763(0x294));if(_0x91d503)return parseInt(_0x91d503['value']);var _0x91d503=document[_0x1e7763(0x274)]('.pagination__items\x20[aria-current=\x22page\x22]');if(_0x91d503)return parseInt(_0x91d503[_0x1e7763(0x1f8)]);return 0x1;}function getSkusOnPage(){return new Promise(_0x505c98=>{var _0x154bcc=a0_0x248a;const _0x2a772a=[],_0x40dee2=getItemNodes();_0x40dee2[_0x154bcc(0x226)](_0x241df1=>{var _0x2c76a1=_0x154bcc;_0x241df1[_0x2c76a1(0x1ef)]('tr')['forEach'](_0x540adf=>{var _0x433d2e=_0x2c76a1;const _0x3072bf=_0x540adf[_0x433d2e(0x274)](_0x433d2e(0x28e));if(_0x3072bf){const _0x29e012=_0x3072bf[_0x433d2e(0x2b4)][_0x433d2e(0x252)]();if(_0x29e012)_0x2a772a['push'](_0x29e012);}});}),_0x505c98(_0x2a772a);});}function getItemNodes(){var _0x5ba6c4=a0_0x3ff3fb;return document[_0x5ba6c4(0x1ef)](_0x5ba6c4(0x26e));}function getItemNodeByItemNumber(_0x68d02){var _0x235d2a=a0_0x3ff3fb;const _0x1f6c0f=getItemNodes();for(const _0x54b9d2 of _0x1f6c0f){const _0x2c311c=_0x54b9d2['querySelector']('.grid-row')[_0x235d2a(0x24d)](_0x235d2a(0x1f0));if(_0x2c311c===_0x68d02)return _0x54b9d2;}return null;}function checkIfPageLoadedProperly(){var _0x2402ed=a0_0x3ff3fb;return document[_0x2402ed(0x1ef)](_0x2402ed(0x20b))['length']>0x0;}function fetchEbayData(_0x4ee4fe){var _0x274059=a0_0x3ff3fb;let _0x29a698={};var _0x2ae51a=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x24f));_0x2ae51a&&(_0x29a698[_0x274059(0x281)]=_0x2ae51a[_0x274059(0x2b4)][_0x274059(0x252)]());var _0x236dfe=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x2e3));_0x236dfe&&(_0x29a698[_0x274059(0x221)]=_0x236dfe[_0x274059(0x24d)](_0x274059(0x21d)));var _0x5f5564=_0x4ee4fe[_0x274059(0x274)]('td[class*=\x22listingSKU');_0x5f5564&&(_0x29a698[_0x274059(0x2db)]=_0x5f5564[_0x274059(0x2b4)][_0x274059(0x252)]());var _0x583f83=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x29d));if(_0x583f83){var _0x49f126=_0x583f83[_0x274059(0x2b4)][_0x274059(0x252)]();_0x29a698['price']=globalizedParsePrice(_0x49f126);}else{var _0x583f83=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x2bb));if(_0x583f83){const _0x174ad4=_0x583f83[_0x274059(0x2b4)][_0x274059(0x252)]();_0x29a698[_0x274059(0x230)]=globalizedParsePrice(_0x174ad4);}}var _0x5a2492=_0x4ee4fe[_0x274059(0x274)]('td[class*=\x22availableQuantity\x22]\x20[class*=\x22cell-wrapper\x22]');_0x5a2492&&(_0x29a698[_0x274059(0x298)]=parseInt(_0x5a2492[_0x274059(0x2b4)][_0x274059(0x252)]()[_0x274059(0x21f)](/[^0-9\.]+/g,'')));var _0x2d8b41=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x27e));_0x2d8b41&&(_0x29a698[_0x274059(0x227)]=_0x2d8b41[_0x274059(0x2b4)]['trim']());var _0xf43d62=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x2c7));_0xf43d62&&(_0x29a698[_0x274059(0x28c)]=parseInt(_0xf43d62['textContent'][_0x274059(0x252)]()['replace'](/[^0-9\.]+/g,'')));var _0x288ed6=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x242));_0x288ed6&&(_0x29a698['scheduledStartDate']=_0x288ed6[_0x274059(0x2b4)][_0x274059(0x252)]());var _0x2f9fe5=_0x4ee4fe[_0x274059(0x274)]('td[class*=\x22timeRemaining\x22]\x20[class*=\x22text-column\x22]\x20div');_0x2f9fe5&&(_0x29a698[_0x274059(0x207)]=_0x2f9fe5[_0x274059(0x2b4)][_0x274059(0x252)]());var _0x19bda6=_0x4ee4fe[_0x274059(0x274)](_0x274059(0x280));return _0x19bda6&&(_0x29a698[_0x274059(0x2b5)]=parseInt(_0x19bda6[_0x274059(0x2b4)][_0x274059(0x252)]()[_0x274059(0x21f)](/[^0-9\.]+/g,''))),_0x29a698;}function createRelistButton(_0x340a05){var _0x2ff71a=a0_0x3ff3fb,_0x2fc314=document[_0x2ff71a(0x22c)]('button'),_0x4415dd=_0x340a05[_0x2ff71a(0x2f1)](0x0)[_0x2ff71a(0x2dd)]()+_0x340a05[_0x2ff71a(0x20c)](0x1);return _0x2fc314[_0x2ff71a(0x213)]=_0x4415dd+_0x2ff71a(0x1e7),_0x2fc314[_0x2ff71a(0x2de)][_0x2ff71a(0x260)](_0x2ff71a(0x2b0)),_0x2fc314[_0x2ff71a(0x2de)][_0x2ff71a(0x260)](_0x340a05+_0x2ff71a(0x222)),_0x2fc314['onclick']=async function(_0x1c78fc){var _0x101695=_0x2ff71a;_0x1c78fc[_0x101695(0x2a4)](),console[_0x101695(0x2a8)](_0x101695(0x202)),this[_0x101695(0x20e)]=!![],this['innerHTML']=_0x101695(0x1ed)+_0x4415dd+_0x101695(0x28f),chrome[_0x101695(0x2cd)][_0x101695(0x29b)]['set']({'bulkListType':_0x340a05});var _0x6ad16c=this;while(_0x6ad16c['tagName']!=='TR'){_0x6ad16c=_0x6ad16c['parentElement'];}console[_0x101695(0x2a8)](_0x6ad16c);var _0x4dd361=fetchEbayData(_0x6ad16c);console[_0x101695(0x2a8)](_0x4dd361);var _0x3b2a0c=await new Promise(_0x33d0b=>{var _0xdfbb62=_0x101695;chrome['runtime'][_0xdfbb62(0x239)]({'type':_0xdfbb62(0x259),'ebayData':_0x4dd361},function(_0x2b1ccf){var _0xc09545=_0xdfbb62;console[_0xc09545(0x2a8)](_0x2b1ccf),_0x33d0b(_0x2b1ccf['response']);});});console[_0x101695(0x2a8)](_0x101695(0x2c9),_0x3b2a0c);var _0x3cd6d9=document['createElement'](_0x101695(0x2b7));_0x3cd6d9[_0x101695(0x27c)]=_0x101695(0x1fd);if(_0x3b2a0c){var _0xe3565a=_0x3b2a0c[_0x101695(0x244)]?_0x3b2a0c[_0x101695(0x244)]:_0x101695(0x246),_0x1f6b29=_0x3b2a0c['url']?_0x101695(0x2ed)+_0x3b2a0c[_0x101695(0x275)]+_0x101695(0x2cb)+_0x3b2a0c[_0x101695(0x275)]+_0x101695(0x21c):'Not\x20Available',_0x280d47=_0x3b2a0c[_0x101695(0x2e4)]?_0x3b2a0c[_0x101695(0x2e4)]:_0x101695(0x246),_0xb026b4=_0x3b2a0c[_0x101695(0x1fa)]?'<a\x20href=\x22'+_0x3b2a0c['ebayItemLink']+'\x22\x20target=\x22_blank\x22>View\x20Item</a>':_0x101695(0x246);_0x3cd6d9[_0x101695(0x213)]=_0x101695(0x26f)+_0xe3565a+_0x101695(0x283)+_0x1f6b29+_0x101695(0x273)+_0x280d47+_0x101695(0x2bd)+_0xb026b4+_0x101695(0x2c8);}else _0x3cd6d9[_0x101695(0x213)]=_0x101695(0x23c);const _0x4ea303=_0x6ad16c['querySelector'](_0x101695(0x225));_0x4ea303[_0x101695(0x297)](_0x3cd6d9),await new Promise(_0x363197=>setTimeout(_0x363197,0x7d0)),this[_0x101695(0x213)]=_0x4415dd+_0x101695(0x2ee);},_0x2fc314;}function createDisplayElement(_0x3a71d0){var _0x57df3c=a0_0x3ff3fb;console[_0x57df3c(0x2a8)]('createDisplayElement\x20message',_0x3a71d0);var _0x338048=document[_0x57df3c(0x22c)](_0x57df3c(0x2b7));_0x338048[_0x57df3c(0x27c)]=_0x57df3c(0x1fd);var _0x4365cb=_0x3a71d0[_0x57df3c(0x244)]=='itemListed'?_0x57df3c(0x22b):'error',_0x5db05f=_0x3a71d0[_0x57df3c(0x1fa)]?_0x57df3c(0x2ed)+_0x3a71d0[_0x57df3c(0x1fa)]+'\x22\x20target=\x22_blank\x22>View\x20Item</a>':_0x57df3c(0x246),_0x245bff=_0x3a71d0[_0x57df3c(0x275)]?_0x57df3c(0x2ed)+_0x3a71d0[_0x57df3c(0x275)]+_0x57df3c(0x219):_0x57df3c(0x246),_0x37ea5b=new Date()['toLocaleTimeString'](),_0x16a9f4=_0x57df3c(0x2b1)+_0x37ea5b+_0x57df3c(0x2cf);return _0x338048[_0x57df3c(0x213)]=_0x57df3c(0x2e6)+_0x4365cb+_0x57df3c(0x269)+_0x3a71d0[_0x57df3c(0x2e4)]+'</p>\x0a\x20\x20\x20\x20\x20\x20<p>'+_0x245bff+_0x57df3c(0x263)+_0x5db05f+_0x57df3c(0x263)+_0x16a9f4+'</p>\x0a\x20\x20\x20\x20\x20\x20',_0x338048;}function a0_0x248a(_0xbbdfed,_0x313390){var _0x3c01c1=a0_0x3c01();return a0_0x248a=function(_0x248a8e,_0x372051){_0x248a8e=_0x248a8e-0x1e5;var _0x1a6596=_0x3c01c1[_0x248a8e];return _0x1a6596;},a0_0x248a(_0xbbdfed,_0x313390);}function createUpdateListButton(){var _0x3653d2=a0_0x3ff3fb,_0x325574=document[_0x3653d2(0x22c)]('button');return _0x325574['innerHTML']='Update\x20List',_0x325574['className']='update-listing-button',_0x325574[_0x3653d2(0x248)]=async function(_0x4f2c0a){var _0x356e65=_0x3653d2;_0x4f2c0a[_0x356e65(0x2a4)](),console[_0x356e65(0x2a8)]('Update\x20Listing\x20button\x20clicked'),this[_0x356e65(0x20e)]=!![],this[_0x356e65(0x213)]='Updating\x20Listing...\x20<span\x20class=\x27loader\x27></span>';var _0x59d9fe=this;while(_0x59d9fe[_0x356e65(0x2ae)]!=='TR'){_0x59d9fe=_0x59d9fe[_0x356e65(0x22a)];}console[_0x356e65(0x2a8)](_0x59d9fe);var _0x3a51a2=fetchEbayData(_0x59d9fe);console[_0x356e65(0x2a8)](_0x3a51a2);var _0x1946a5=await deductCredits(0.2);if(!_0x1946a5[_0x356e65(0x22b)]){console[_0x356e65(0x2a8)]('deductCreditsResponse',_0x1946a5),this[_0x356e65(0x213)]=_0x356e65(0x216)+_0x1946a5[_0x356e65(0x2e4)];return;}var _0x293432=await new Promise(_0x179d0e=>{var _0x438d7e=_0x356e65;chrome[_0x438d7e(0x249)][_0x438d7e(0x239)]({'type':_0x438d7e(0x256),'ebayData':_0x3a51a2},function(_0xfb7225){console['log'](_0xfb7225),_0x179d0e(_0xfb7225);});});console[_0x356e65(0x2a8)](_0x356e65(0x2c9),_0x293432),this[_0x356e65(0x213)]=_0x356e65(0x25f);var _0x1c3576=createDisplayElement(_0x293432['response']),_0x31db8a=_0x59d9fe[_0x356e65(0x274)]('td[class*=\x22title\x22]');_0x31db8a[_0x356e65(0x297)](_0x1c3576);},_0x325574;}function createSmartRelistButton(){var _0x2cf4ea=a0_0x3ff3fb,_0xe20107=document[_0x2cf4ea(0x22c)]('button');return _0xe20107[_0x2cf4ea(0x213)]=_0x2cf4ea(0x2e9),_0xe20107['className']=_0x2cf4ea(0x262),_0xe20107[_0x2cf4ea(0x248)]=async function(_0x3113c8){var _0x164715=_0x2cf4ea;_0x3113c8['preventDefault'](),console['log'](_0x164715(0x229));var _0x33a53a=this;while(_0x33a53a[_0x164715(0x2ae)]!=='TR'){_0x33a53a=_0x33a53a[_0x164715(0x22a)];}console['log'](_0x33a53a);var _0x233f38=await deductCredits(0x1);if(!_0x233f38[_0x164715(0x22b)]){console[_0x164715(0x2a8)](_0x164715(0x2d1),_0x233f38),this[_0x164715(0x213)]=_0x164715(0x216)+_0x233f38[_0x164715(0x2e4)];return;}await smartRelistItem(_0x33a53a);},_0xe20107;}async function smartRelistItem(_0x5d94d4){var _0x2a659f=a0_0x3ff3fb;chrome['storage'][_0x2a659f(0x29b)][_0x2a659f(0x285)]({'run_status_bulk_lister':!![]});var _0x59fdc9=_0x5d94d4[_0x2a659f(0x274)]('.smart-relist-button');_0x59fdc9['disabled']=!![],_0x59fdc9['innerHTML']=_0x2a659f(0x2b2);var _0x41d1c0=fetchEbayData(_0x5d94d4);console[_0x2a659f(0x2a8)](_0x41d1c0);var _0x10092a=await new Promise(_0xa80ae2=>{var _0x5de114=_0x2a659f;chrome[_0x5de114(0x249)][_0x5de114(0x239)]({'type':_0x5de114(0x241),'ebayData':_0x41d1c0},function(_0x2cc263){var _0x2ee131=_0x5de114;console[_0x2ee131(0x2a8)](_0x2cc263),_0xa80ae2(_0x2cc263);});});console[_0x2a659f(0x2a8)](_0x2a659f(0x2c9),_0x10092a),_0x59fdc9[_0x2a659f(0x213)]=_0x2a659f(0x1f2);var _0x1396cb=createDisplayElement(_0x10092a[_0x2a659f(0x20d)]),_0x56dc3f=_0x5d94d4['querySelector'](_0x2a659f(0x225));return _0x56dc3f[_0x2a659f(0x297)](_0x1396cb),_0x10092a;}function createCloneListButton(){var _0x2bea4b=a0_0x3ff3fb,_0x2b1ac7=document[_0x2bea4b(0x22c)](_0x2bea4b(0x211));return _0x2b1ac7[_0x2bea4b(0x213)]=_0x2bea4b(0x237),_0x2b1ac7[_0x2bea4b(0x27c)]='clone-list-button',_0x2b1ac7[_0x2bea4b(0x248)]=async function(_0x49b5be){var _0xd8dbcb=_0x2bea4b;_0x49b5be[_0xd8dbcb(0x2a4)](),console['log'](_0xd8dbcb(0x200)),this[_0xd8dbcb(0x20e)]=!![],this[_0xd8dbcb(0x213)]='Cloning\x20List...\x20<span\x20class=\x27loader\x27></span>';var _0x417a6f=this;while(_0x417a6f[_0xd8dbcb(0x2ae)]!=='TR'){_0x417a6f=_0x417a6f[_0xd8dbcb(0x22a)];}console[_0xd8dbcb(0x2a8)](_0x417a6f);var _0x58aeaf=fetchEbayData(_0x417a6f);console[_0xd8dbcb(0x2a8)](_0x58aeaf);var _0x523083=await deductCredits(0.2);if(!_0x523083[_0xd8dbcb(0x22b)]){console[_0xd8dbcb(0x2a8)](_0xd8dbcb(0x2d1),_0x523083),this['innerHTML']=_0xd8dbcb(0x216)+_0x523083[_0xd8dbcb(0x2e4)];return;}var _0x1c59f9=await new Promise(_0x482c01=>{var _0x1902ec=_0xd8dbcb;chrome[_0x1902ec(0x249)][_0x1902ec(0x239)]({'type':_0x1902ec(0x1f7),'ebayData':_0x58aeaf},function(_0x1dcd88){var _0x489f1a=_0x1902ec;console[_0x489f1a(0x2a8)](_0x1dcd88),_0x482c01(_0x1dcd88);});});console[_0xd8dbcb(0x2a8)](_0xd8dbcb(0x2c9),_0x1c59f9),this[_0xd8dbcb(0x213)]=_0xd8dbcb(0x289);var _0x4e0cc7=createDisplayElement(_0x1c59f9[_0xd8dbcb(0x20d)][_0xd8dbcb(0x20d)]['message']),_0x3ad416=_0x417a6f[_0xd8dbcb(0x274)](_0xd8dbcb(0x225));_0x3ad416['appendChild'](_0x4e0cc7);},_0x2b1ac7;}function findItemNode(_0x146c87){var _0x1ff88e=a0_0x3ff3fb,_0x68a761=getItemNodes();for(let _0x45f528=0x0;_0x45f528<_0x68a761[_0x1ff88e(0x2ba)];_0x45f528++){var _0x3f1e86=_0x68a761[_0x45f528];console[_0x1ff88e(0x2a8)](_0x1ff88e(0x2c0),_0x3f1e86);var _0x2c272e=_0x3f1e86[_0x1ff88e(0x274)]('.grid-row:not(.grid-row-notice)')[_0x1ff88e(0x24d)](_0x1ff88e(0x1f0));if(_0x2c272e==_0x146c87)return _0x3f1e86;}}async function waitForElement(_0x4d7531,_0x69fda2){return new Promise(_0x425b12=>{var _0x6a445d=a0_0x248a;const _0x3b4cd7=new MutationObserver((_0x58aa6d,_0x2b7adf)=>{var _0x2b85aa=a0_0x248a;for(let _0x48ddff of _0x58aa6d){if(_0x48ddff[_0x2b85aa(0x2dc)]===_0x2b85aa(0x261)){const _0x1ce99c=Array['from'](document[_0x2b85aa(0x1ef)](_0x4d7531));for(let _0x190b47 of _0x1ce99c){if(_0x69fda2(_0x190b47)){console['log'](_0x2b85aa(0x23a),_0x190b47),_0x2b7adf[_0x2b85aa(0x210)](),_0x425b12(_0x190b47);break;}}}}});_0x3b4cd7[_0x6a445d(0x279)](document[_0x6a445d(0x284)],{'childList':!![],'subtree':!![]});});}async function endItem(_0x16bb90){var _0x231d67=a0_0x3ff3fb;setTimeout(()=>{var _0x2c70c3=a0_0x248a;return console[_0x2c70c3(0x2a8)](_0x2c70c3(0x295)),_0x2c70c3(0x1f9);},0x1d4c0),console[_0x231d67(0x2a8)](_0x231d67(0x2ac),_0x16bb90);const _0x24d7e8=_0x16bb90[_0x231d67(0x274)](_0x231d67(0x1f1));_0x24d7e8[_0x231d67(0x25d)]();var _0x3fa02a=await waitForElement(_0x231d67(0x276),_0x11de77=>{var _0x417e53=_0x231d67;const _0x4d5bcc=_0x11de77[_0x417e53(0x2b4)][_0x417e53(0x252)]();return _0x4d5bcc===_0x417e53(0x2da)||_0x4d5bcc===_0x417e53(0x21a)||_0x4d5bcc===_0x417e53(0x2d7)||_0x4d5bcc===_0x417e53(0x2a1)||_0x4d5bcc===_0x417e53(0x2e5)||(_0x11de77[_0x417e53(0x24d)]('_sp')||'')===_0x417e53(0x290);});_0x3fa02a=_0x3fa02a[_0x231d67(0x2aa)],await new Promise(_0x38072d=>setTimeout(_0x38072d,0x7d0)),_0x3fa02a[_0x231d67(0x25d)]();var _0x3c7001=await waitForElement('[class*=se-end-listing]',_0x48308d=>_0x48308d!=null);console[_0x231d67(0x2a8)](_0x231d67(0x204),_0x3c7001);var _0x1789ec=_0x3c7001['querySelectorAll'](_0x231d67(0x211)),_0x3a1d8e;for(let _0x1b9f10=0x0;_0x1b9f10<_0x1789ec[_0x231d67(0x2ba)];_0x1b9f10++){var _0x2e1ae1=_0x1789ec[_0x1b9f10];if(_0x2e1ae1[_0x231d67(0x1f8)][_0x231d67(0x252)]()==='End\x20listing'||_0x2e1ae1[_0x231d67(0x1f8)][_0x231d67(0x252)]()===_0x231d67(0x21a)||_0x2e1ae1[_0x231d67(0x1f8)][_0x231d67(0x252)]()===_0x231d67(0x2d7)||_0x2e1ae1[_0x231d67(0x1f8)]['trim']()==='Chiudi\x20l\x27inserzione'||_0x2e1ae1[_0x231d67(0x1f8)][_0x231d67(0x252)]()===_0x231d67(0x2e5)){_0x3a1d8e=_0x2e1ae1;break;}}return console[_0x231d67(0x2a8)](_0x231d67(0x1e9),_0x3a1d8e),await new Promise(_0x5a6777=>setTimeout(_0x5a6777,0x7d0)),_0x3a1d8e[_0x231d67(0x25d)](),_0x231d67(0x2f4);}function createEndItemButton(){var _0x1c0d88=a0_0x3ff3fb,_0x35bce6=document[_0x1c0d88(0x22c)](_0x1c0d88(0x211));return _0x35bce6[_0x1c0d88(0x213)]='End\x20Item',_0x35bce6[_0x1c0d88(0x27c)]=_0x1c0d88(0x1eb),_0x35bce6['onclick']=async function(_0x1dd531){var _0x881fea=_0x1c0d88;_0x1dd531[_0x881fea(0x2a4)](),console['log']('End\x20Item\x20button\x20clicked'),this[_0x881fea(0x20e)]=!![],this[_0x881fea(0x213)]='Ending\x20Item...\x20<span\x20class=\x27loader\x27></span>';var _0x1c7121=this;while(_0x1c7121[_0x881fea(0x2ae)]!=='TR'){_0x1c7121=_0x1c7121[_0x881fea(0x22a)];}console['log'](_0x1c7121);var _0x376169=fetchEbayData(_0x1c7121);console[_0x881fea(0x2a8)](_0x376169);var _0x53bb3e=await new Promise(_0x42b94c=>{var _0x1926b9=_0x881fea;chrome[_0x1926b9(0x249)][_0x1926b9(0x239)]({'type':_0x1926b9(0x2ac),'ebayData':_0x376169},function(_0x21050a){var _0x382a64=_0x1926b9;console[_0x382a64(0x2a8)](_0x21050a),_0x42b94c(_0x21050a);});});console[_0x881fea(0x2a8)](_0x881fea(0x2c9),_0x53bb3e),_0x53bb3e[_0x881fea(0x20d)]?this[_0x881fea(0x213)]=_0x881fea(0x24c):this[_0x881fea(0x213)]=_0x881fea(0x2d9);},_0x35bce6;}function enableOpenTitleLinksInNewTab(_0x43535c,_0x416936){var _0x439cfc=a0_0x3ff3fb;const _0x138e63=document[_0x439cfc(0x274)](_0x43535c);if(!_0x138e63){console[_0x439cfc(0x2d3)](_0x439cfc(0x29a));return;}_0x138e63[_0x439cfc(0x258)](_0x439cfc(0x25d),function(_0x24c38e){var _0x3b61c7=_0x439cfc;let _0x1bef23=_0x24c38e['target'];while(_0x1bef23&&_0x1bef23!==_0x138e63){if(_0x1bef23[_0x3b61c7(0x254)](_0x416936)){_0x24c38e[_0x3b61c7(0x2a4)]();const _0x2e9410=_0x1bef23[_0x3b61c7(0x2b8)];window['open'](_0x2e9410,'_blank'),console[_0x3b61c7(0x2a8)](_0x3b61c7(0x2a7)+_0x2e9410+_0x3b61c7(0x223));return;}_0x1bef23=_0x1bef23[_0x3b61c7(0x22a)];}}),console[_0x439cfc(0x2a8)](_0x439cfc(0x2d6));}async function fetchAllEbayData(_0x2b864a){var _0x421fb6=a0_0x3ff3fb,_0x4a036d=[];for(const _0x490679 of _0x2b864a){_0x4a036d[_0x421fb6(0x29f)](fetchEbayData(_0x490679));}return _0x4a036d;}async function getItemNodesByItemNumbers(_0x3169d5){var _0x1afbd1=a0_0x3ff3fb,_0xbdb00b=getItemNodes();console['log'](_0x1afbd1(0x2ce),_0xbdb00b);var _0x32d632=[];for(const _0x2d1fdb of _0xbdb00b){var _0x554722=fetchEbayData(_0x2d1fdb);_0x3169d5[_0x1afbd1(0x1ff)](_0x554722[_0x1afbd1(0x227)])&&(console[_0x1afbd1(0x2a8)](_0x1afbd1(0x23b),_0x2d1fdb),_0x32d632['push'](_0x2d1fdb));}return _0x32d632;}async function selectLowPerformingItems(_0xe377d9){var _0x217c50=a0_0x3ff3fb;for(var _0x306dce of _0xe377d9){var _0x49d0ab=_0x306dce[_0x217c50(0x274)](_0x217c50(0x2ea));if(_0x49d0ab){var _0x1e3f4b=new MouseEvent(_0x217c50(0x25d),{'view':window,'bubbles':!![],'cancelable':!![]});_0x49d0ab[_0x217c50(0x287)](_0x1e3f4b);}}return _0xe377d9;}function getLowPerformingItemNodes(_0x24ca78,_0x52a983,_0x10a0ec=0x2d0){var _0x489f6c=a0_0x3ff3fb;const _0x526fa6=[],_0x4f377b=getItemNodes();console[_0x489f6c(0x2a8)](_0x489f6c(0x2ce),_0x4f377b);for(const _0xf154c0 of _0x4f377b){var _0x5ebe5b=fetchEbayData(_0xf154c0),_0x1d45bd=_0x5ebe5b[_0x489f6c(0x28c)],_0x396259=_0x5ebe5b[_0x489f6c(0x2b5)];if(isNaN(_0x1d45bd)||isNaN(_0x396259))continue;if(!isTimeLessThan(_0x5ebe5b[_0x489f6c(0x207)],_0x10a0ec))continue;_0x1d45bd<=_0x24ca78&&_0x396259<=_0x52a983&&_0x526fa6[_0x489f6c(0x29f)](_0xf154c0);}return _0x526fa6;}function createStopButton(){var _0x29f1b3=a0_0x3ff3fb;const _0x52e2f2=document['createElement'](_0x29f1b3(0x211));return _0x52e2f2['textContent']=_0x29f1b3(0x25a),_0x52e2f2['className']='stop-button',_0x52e2f2['addEventListener'](_0x29f1b3(0x25d),async _0x2c674d=>{var _0x3481b3=_0x29f1b3;_0x2c674d['preventDefault'](),console[_0x3481b3(0x2a8)](_0x3481b3(0x22d)),document[_0x3481b3(0x281)]=_0x3481b3(0x1e8)+document[_0x3481b3(0x281)],_0x52e2f2['disabled']=!![],_0x52e2f2['innerHTML']=_0x3481b3(0x233),shouldStopProcessing=!![],_0x52e2f2[_0x3481b3(0x2b4)]=_0x3481b3(0x2b3);}),_0x52e2f2;}async function createBulkSmartRelistButton(){var _0x256f83=a0_0x3ff3fb;const _0xde8e42=document[_0x256f83(0x22c)](_0x256f83(0x211));_0xde8e42['textContent']=_0x256f83(0x1f6),_0xde8e42['className']=_0x256f83(0x1f3),_0xde8e42[_0x256f83(0x258)](_0x256f83(0x25d),async _0x4ad900=>{var _0x5b523f=_0x256f83;_0x4ad900[_0x5b523f(0x2a4)](),console[_0x5b523f(0x2a8)](_0x5b523f(0x291)),document[_0x5b523f(0x281)]='Bulk\x20Smart\x20Relist\x20-\x20'+document[_0x5b523f(0x281)],_0xde8e42[_0x5b523f(0x20e)]=!![],_0xde8e42[_0x5b523f(0x213)]=_0x5b523f(0x29c);var {minViewCount:_0xf2468f}=await chrome[_0x5b523f(0x2cd)][_0x5b523f(0x29b)][_0x5b523f(0x2ef)]('minViewCount'),{minSoldQuantity:_0x29b5a0}=await chrome[_0x5b523f(0x2cd)][_0x5b523f(0x29b)][_0x5b523f(0x2ef)](_0x5b523f(0x2d2)),{filterByHours:_0x3b1569}=await chrome[_0x5b523f(0x2cd)][_0x5b523f(0x29b)][_0x5b523f(0x2ef)](_0x5b523f(0x1e5)),_0x30438c=await getLowPerformingItemNodes(_0x29b5a0,_0xf2468f,_0x3b1569);console[_0x5b523f(0x2a8)]('Low\x20Performing\x20Nodes:',_0x30438c);var {maxConcurrentTasks:_0x130aed}=await chrome['storage'][_0x5b523f(0x29b)]['get'](_0x5b523f(0x21b));_0x130aed=parseInt(_0x130aed,0xa);_0x130aed>0xa&&(_0x130aed=0xa);console[_0x5b523f(0x2a8)]('lowPerformingNodes\x20to\x20process',_0x30438c);_0x30438c[_0x5b523f(0x2ba)]>0x0&&await processLowPerformingItems(_0x30438c,_0x130aed);_0xde8e42[_0x5b523f(0x2b4)]=_0x5b523f(0x2a2);var {autoRefresh:_0x2b3894}=await chrome[_0x5b523f(0x2cd)][_0x5b523f(0x29b)][_0x5b523f(0x2ef)]('autoRefresh');_0x2b3894?(console[_0x5b523f(0x2a8)](_0x5b523f(0x217)),console[_0x5b523f(0x2a8)](_0x5b523f(0x23f)),document['title']='Refreshing\x20Page\x20in\x205\x20minutes\x20-\x20'+document[_0x5b523f(0x281)],await waitSomeTime(0x5*0x3c*0x3e8),chrome[_0x5b523f(0x249)][_0x5b523f(0x239)]({'type':_0x5b523f(0x1f5)})):(console[_0x5b523f(0x2a8)]('autoRefreshCheckbox\x20not\x20checked'),document[_0x5b523f(0x281)]=_0x5b523f(0x251)+document[_0x5b523f(0x281)]);});var _0x4fdd0e=await createConcurrentTasksSelect(),_0x56d07d=await createAutoRefreshCheckbox(),_0x409a98=await createFilterByHoursSelect(),_0x1ed0b7=await createMinSoldQuantityInput(),_0xc9d5a6=await createMinViewCountInput(),_0x533b96=await createMaxPriceInput(),_0x18d5c0=await createMinPriceInput(),_0x331256=await createIntroContainer(),_0x2d5841=createStopButton(),_0x24d3f1=document['createElement']('div');_0x24d3f1[_0x256f83(0x27c)]=_0x256f83(0x264),_0x24d3f1['appendChild'](_0x56d07d),_0x24d3f1[_0x256f83(0x297)](_0x4fdd0e),_0x24d3f1[_0x256f83(0x297)](_0x409a98),_0x24d3f1[_0x256f83(0x297)](_0x1ed0b7),_0x24d3f1['appendChild'](_0xc9d5a6);var _0x4a3006=document['createElement'](_0x256f83(0x2b7));_0x4a3006['style'][_0x256f83(0x2f3)]=_0x256f83(0x2d4),_0x24d3f1[_0x256f83(0x297)](_0x4a3006),_0x24d3f1[_0x256f83(0x297)](_0x533b96),_0x24d3f1[_0x256f83(0x297)](_0x18d5c0);var _0x33c087=document['createElement'](_0x256f83(0x2b7));_0x33c087[_0x256f83(0x27c)]=_0x256f83(0x20a),_0x33c087[_0x256f83(0x297)](_0xde8e42),_0x24d3f1[_0x256f83(0x297)](_0x33c087);var _0xf1782=document[_0x256f83(0x22c)](_0x256f83(0x2b7));return _0xf1782[_0x256f83(0x27c)]=_0x256f83(0x240),_0xf1782[_0x256f83(0x297)](_0x24d3f1),_0xf1782[_0x256f83(0x297)](_0x331256),_0xf1782;}function a0_0x3c01(){var _0x44e5c7=['select\x20changed','minViewCountInput.value','button[type=\x22reset\x22]','race','then','input','td[class*=\x22soldQuantity\x22]','</p>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','Response\x20received','\x20SKUS:\x20','\x22\x20target=\x22_blank\x22>','min-view-count-input','storage','itemNodes',']</span>','auto-refresh-checkbox','deductCreditsResponse','minSoldQuantity','warn','10px','Customizable\x20Table\x20on\x20eBay\x20must\x20include:\x20\x27Custom\x20Label\x27,\x20\x27Item\x20Number\x27,\x20\x27Available\x20Quantity\x27,\x20\x27Sold\x20Quantity\x27,\x20\x27Views\x27,\x20\x27Current\x20Price\x27,\x20\x27Start\x20Date\x27','Event\x20delegation\x20setup\x20complete\x20for\x20opening\x20title\x20links\x20in\x20new\x20tab.','Mettre\x20fin\x20à\x20l\x27annonce','getElementById','Error\x20Ending\x20Item','End\x20listing','customLabel','type','toUpperCase','classList','max-price-to-list-input','\x20processed.\x20Total\x20processed:\x20','8XpoIBf','addedNodes','td[class*=\x22image\x22]\x20img','message','Finalizar\x20anuncio','\x0a\x20\x20\x20\x20\x20\x20<p>Status:\x20','maxPrice','#fc95ab','Smart\x20Relist','input[id^=\x22shui-dt-checkone\x22]','exec','minSoldQuantityInput.value','<a\x20href=\x22','\x20Relisted','get','auto-refresh-checkbox-div','charAt','minViewCountInput\x20changed','height','itemEnded','value','filterByHours','filter-by-hours-select','\x20Relist','Stop\x20-\x20','finalEndListingButton','concat','end-item-button','Min\x20Sold\x20Quantity','Relisting\x20','min-sold-quantity-input','querySelectorAll','data-id','.toggle-menu.btn','Smart\x20Relisted','bulk-smart-relist-button','processed-node','refresh_page_and_relist','Bulk\x20Smart\x20Relist\x20Low\x20Performing\x20Items','cloneList','innerText','timeout','ebayItemLink','\x22]\x20button[data]','2591464QJAEsD','info-box','Processing\x20node:\x20','includes','Clone\x20List\x20button\x20clicked','min-price-to-list-input-div','Relist\x20button\x20clicked','smart-relist-explanation','.se-end-listing\x20element\x20found','getElementsByClassName','fontWeight','timeLeft','focus','td[class*=\x22','action-button-container','table[id*=gridData]','slice','response','disabled','finally','disconnect','button','step','innerHTML',']\x22]','min-price-to-list-input','Error:\x20','autoRefreshCheckbox\x20checked','filter','\x22\x20target=\x22_blank\x22>Amazon</a>','Angebot\x20beenden','maxConcurrentTasks','</a>','src','1000','replace','47115541rhMHbJ','photos','-relist-button','\x20in\x20a\x20new\x20tab.','1349375LLYUDA','td[class*=\x22title\x22]','forEach','itemNumber','concurrent-tasks-select-div','Smart\x20Relist\x20button\x20clicked','parentElement','success','createElement','Stop\x20button\x20clicked','Processing\x20node','select','price','error','ebay.active_listings_functions.js\x20loaded','Stopping...\x20<span\x20class=\x27custom-loader\x27></span>','character','62718oszIhk','216cWRNWJ','Clone\x20List','Welcome\x20to\x20the\x20Smart\x20Relist\x20feature!\x20Set\x20your\x20preferences\x20below\x20to\x20relist\x20items\x20on\x20your\x20eBay\x20account\x20based\x20on\x20ending\x20time,\x20minimum\x20sold\x20quantity,\x20and\x20view\x20count.\x20Choose\x20the\x20number\x20of\x20concurrent\x20listings\x20our\x20AI-enhanced\x20tool\x20will\x20process,\x20optimizing\x20item\x20titles\x20for\x20better\x20visibility.\x20Note:\x20Enabling\x20\x27Auto\x20Refresh\x20Page\x27\x20will\x20refresh\x20the\x20page\x20automatically\x20after\x20relisting.\x20Disable\x20this\x20feature\x20and\x20refresh\x20the\x20page\x20manually\x20to\x20turn\x20it\x20off.','sendMessage','Element\x20found','itemNode\x20found','<p>Error:\x20No\x20response\x20received</p>','min','filter-by-hours-select-div','refreshing\x20page\x20in\x205\x20minutes','smart-relist-ui','smartRelist','td[class*=\x22scheduledStartDate\x22]\x20[class*=\x22text-column\x22]\x20div','label','status','isFulfilled','Not\x20Available','name','onclick','runtime','max','365hWNCff','Item\x20Ended','getAttribute','Started\x20processing\x20node\x20at\x20index\x20','div[class*=\x22title__text\x22]','moveEnd','Done\x20-\x20','trim','createTextNode','matches','change','updateListing','12h','addEventListener','relistItem','Stop','Sek','htmlFor','click','setSelectionRange','Listing\x20Updated','add','childList','smart-relist-button','</p>\x0a\x20\x20\x20\x20\x20\x20<p>','bulk-smart-relist-button-container','number','select.value','prototype','checkbox','</p>\x0a\x20\x20\x20\x20\x20\x20<p>Message:\x20','3859660xtaASr','2527550pVShlJ','style','.pagination__items\x20li','table[id*=gridData]\x20tbody','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>Status:\x20','text','Page\x20','input[name=\x22members[0][','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>Message:\x20','querySelector','url','[class*=menu__item]','maxPriceToListInput.value','removedNodes','observe','minSoldQuantityInput\x20changed','all','className','27rIlqAc','td[class*=\x22listingId\x22]','min-sold-quantity-input-div','td[class*=\x22visitCount\x22]\x20.cell-wrapper\x20.fake-link','title','filterByHoursSelect.value','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>URL:\x20','body','set','concurrent-tasks-select','dispatchEvent','option','List\x20Cloned','inputBox','autoRefresh','soldQuantity','bold','td[class*=\x22listingSKU\x22]\x20.cell-wrapper','...\x20<span\x20class=\x27loader\x27></span>','p2380676.m4639.l73072','Bulk\x20Smart\x20Relist\x20button\x20clicked','Concurrent\x20Tasks','Error\x20processing\x20node:\x20','.go-to-page\x20.textbox__control','endItem\x20timeout','Node\x20at\x20index\x20','appendChild','availableQty','14061vjuyVD','Container\x20not\x20found.','local','Processing...\x20<span\x20class=\x27custom-loader\x27></span>','td[class*=\x22price\x22]\x20[class*=\x22sh-strikethrough\x22]','skuList','push','Max\x20Price\x20To\x20List','Chiudi\x20l\x27inserzione','Items\x20Bulk\x20Smartly\x20Relisted','checked','preventDefault','min-view-count-input-div','true','Opening\x20','log','Processing\x20stopped','firstElementChild','editButton','endItem','backgroundColor','tagName','form[class*=\x22inline-edit-\x22]\x20button[type=\x22submit\x22]','relist-button','<span\x20class=\x22time-tracker\x22>[','Smart\x20Relisting...\x20<span\x20class=\x27loader\x27></span>','Stopped','textContent','viewCount','Auto\x20Refresh\x20Page','div','href','updateData','length','td[class*=\x22price\x22]\x20[class*=\x22price__current\x22]','All\x20nodes\x20processed','</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>','.popover-content-wrap','minViewCount','itemNode'];a0_0x3c01=function(){return _0x44e5c7;};return a0_0x3c01();}async function createFilterByHoursSelect(){var _0x775106=a0_0x3ff3fb,_0x39244a=document['createElement'](_0x775106(0x22f));_0x39244a['className']=_0x775106(0x1e6),_0x39244a[_0x775106(0x247)]=_0x775106(0x1e6),_0x39244a['id']=_0x775106(0x1e6);var _0x2af2fe=document[_0x775106(0x22c)]('option');_0x2af2fe['value']='12',_0x2af2fe['text']=_0x775106(0x257),_0x39244a[_0x775106(0x297)](_0x2af2fe);var _0x5b59d7=document['createElement'](_0x775106(0x288));_0x5b59d7['value']='24',_0x5b59d7[_0x775106(0x270)]='24h',_0x39244a[_0x775106(0x297)](_0x5b59d7);var _0x160b8e=document[_0x775106(0x22c)](_0x775106(0x288));_0x160b8e[_0x775106(0x2f5)]='48',_0x160b8e[_0x775106(0x270)]='48h',_0x39244a[_0x775106(0x297)](_0x160b8e);var {filterByHours:_0x546bf1}=await chrome[_0x775106(0x2cd)][_0x775106(0x29b)][_0x775106(0x2ef)](_0x775106(0x1e5));_0x546bf1?_0x39244a['value']=_0x546bf1:(_0x39244a['value']='24',await chrome['storage'][_0x775106(0x29b)][_0x775106(0x285)]({'filterByHours':'24'}));_0x39244a[_0x775106(0x258)]('change',function(_0x592dbc){var _0x4eb5ac=_0x775106;_0x592dbc[_0x4eb5ac(0x2a4)](),console['log']('filterByHoursSelect\x20changed'),console[_0x4eb5ac(0x2a8)](_0x4eb5ac(0x282),_0x39244a['value']),chrome[_0x4eb5ac(0x2cd)][_0x4eb5ac(0x29b)][_0x4eb5ac(0x285)]({'filterByHours':_0x39244a[_0x4eb5ac(0x2f5)]});});var _0x348cc5=document[_0x775106(0x22c)]('label');_0x348cc5['htmlFor']=_0x775106(0x1e6),_0x348cc5[_0x775106(0x297)](document[_0x775106(0x253)]('Filter\x20By\x20Hours'));var _0x222daa=document[_0x775106(0x22c)](_0x775106(0x2b7));return _0x222daa[_0x775106(0x27c)]=_0x775106(0x23e),_0x222daa[_0x775106(0x297)](_0x39244a),_0x222daa[_0x775106(0x297)](_0x348cc5),_0x222daa;}async function createMinSoldQuantityInput(){var _0x29ee0f=a0_0x3ff3fb,_0x44d960=document[_0x29ee0f(0x22c)]('input');_0x44d960['type']=_0x29ee0f(0x265),_0x44d960['id']=_0x29ee0f(0x1ee),_0x44d960[_0x29ee0f(0x247)]=_0x29ee0f(0x1ee),_0x44d960['value']='0',_0x44d960[_0x29ee0f(0x23d)]='0',_0x44d960[_0x29ee0f(0x24a)]=_0x29ee0f(0x21e),_0x44d960[_0x29ee0f(0x212)]='1';var _0x3cff3f=document[_0x29ee0f(0x22c)](_0x29ee0f(0x243));_0x3cff3f[_0x29ee0f(0x25c)]=_0x29ee0f(0x1ee),_0x3cff3f[_0x29ee0f(0x297)](document[_0x29ee0f(0x253)](_0x29ee0f(0x1ec)));var _0x3dcf16=document[_0x29ee0f(0x22c)](_0x29ee0f(0x2b7));_0x3dcf16[_0x29ee0f(0x27c)]=_0x29ee0f(0x27f),_0x3dcf16[_0x29ee0f(0x297)](_0x44d960),_0x3dcf16[_0x29ee0f(0x297)](_0x3cff3f);var {minSoldQuantity:_0x143466}=await chrome['storage'][_0x29ee0f(0x29b)]['get']('minSoldQuantity');return _0x143466?_0x44d960[_0x29ee0f(0x2f5)]=_0x143466:(_0x44d960['value']='0',await chrome['storage'][_0x29ee0f(0x29b)][_0x29ee0f(0x285)]({'minSoldQuantity':'0'})),_0x44d960[_0x29ee0f(0x258)]('change',function(){var _0x3f2e8b=_0x29ee0f;console['log'](_0x3f2e8b(0x27a)),console['log'](_0x3f2e8b(0x2ec),_0x44d960[_0x3f2e8b(0x2f5)]),chrome['storage'][_0x3f2e8b(0x29b)][_0x3f2e8b(0x285)]({'minSoldQuantity':_0x44d960[_0x3f2e8b(0x2f5)]}),highlightLowPerformingNodes(document[_0x3f2e8b(0x2d8)]('min-sold-quantity-input')[_0x3f2e8b(0x2f5)],document[_0x3f2e8b(0x2d8)](_0x3f2e8b(0x2cc))[_0x3f2e8b(0x2f5)]);}),_0x3dcf16;}async function createMinViewCountInput(){var _0x546677=a0_0x3ff3fb,_0x1e23e7=document[_0x546677(0x22c)](_0x546677(0x2c6));_0x1e23e7[_0x546677(0x2dc)]='number',_0x1e23e7['id']=_0x546677(0x2cc),_0x1e23e7[_0x546677(0x247)]=_0x546677(0x2cc),_0x1e23e7[_0x546677(0x2f5)]='0',_0x1e23e7[_0x546677(0x23d)]='0',_0x1e23e7[_0x546677(0x24a)]=_0x546677(0x21e),_0x1e23e7[_0x546677(0x212)]='1';var _0x15fe5b=document[_0x546677(0x22c)]('label');_0x15fe5b[_0x546677(0x25c)]=_0x546677(0x2cc),_0x15fe5b[_0x546677(0x297)](document[_0x546677(0x253)]('Min\x20View\x20Count'));var _0x1230bd=document[_0x546677(0x22c)](_0x546677(0x2b7));_0x1230bd[_0x546677(0x27c)]=_0x546677(0x2a5),_0x1230bd['appendChild'](_0x1e23e7),_0x1230bd[_0x546677(0x297)](_0x15fe5b);var {minViewCount:_0x3ebe95}=await chrome[_0x546677(0x2cd)][_0x546677(0x29b)]['get'](_0x546677(0x2bf));return _0x3ebe95?_0x1e23e7[_0x546677(0x2f5)]=_0x3ebe95:(_0x1e23e7[_0x546677(0x2f5)]='0',await chrome[_0x546677(0x2cd)][_0x546677(0x29b)][_0x546677(0x285)]({'minViewCount':'0'})),_0x1e23e7[_0x546677(0x258)](_0x546677(0x255),function(){var _0x10e7a3=_0x546677;console[_0x10e7a3(0x2a8)](_0x10e7a3(0x2f2)),console['log'](_0x10e7a3(0x2c2),_0x1e23e7['value']),chrome[_0x10e7a3(0x2cd)][_0x10e7a3(0x29b)][_0x10e7a3(0x285)]({'minViewCount':_0x1e23e7[_0x10e7a3(0x2f5)]}),highlightLowPerformingNodes(document['getElementById'](_0x10e7a3(0x1ee))[_0x10e7a3(0x2f5)],document[_0x10e7a3(0x2d8)]('min-view-count-input')['value']);}),_0x1230bd;}async function createMaxPriceInput(){var _0x4a5455=a0_0x3ff3fb,_0x5efa94=document[_0x4a5455(0x22c)](_0x4a5455(0x2c6));_0x5efa94[_0x4a5455(0x2dc)]=_0x4a5455(0x265),_0x5efa94['id']='max-price-to-list-input',_0x5efa94[_0x4a5455(0x247)]=_0x4a5455(0x2df),_0x5efa94['value']='0',_0x5efa94['min']='0',_0x5efa94[_0x4a5455(0x24a)]='1000',_0x5efa94['step']='1';var _0x38b5c5=document[_0x4a5455(0x22c)](_0x4a5455(0x243));_0x38b5c5[_0x4a5455(0x25c)]=_0x4a5455(0x2df),_0x38b5c5[_0x4a5455(0x297)](document[_0x4a5455(0x253)](_0x4a5455(0x2a0)));var _0xa7303=document[_0x4a5455(0x22c)]('div');_0xa7303[_0x4a5455(0x27c)]='max-price-to-list-input-div',_0xa7303[_0x4a5455(0x297)](_0x5efa94),_0xa7303[_0x4a5455(0x297)](_0x38b5c5);var {maxPrice:_0xee6039}=await chrome['storage'][_0x4a5455(0x29b)][_0x4a5455(0x2ef)](_0x4a5455(0x2e7));return!_0xee6039&&(_0xee6039=0x64),_0x5efa94[_0x4a5455(0x2f5)]=_0xee6039,_0x5efa94[_0x4a5455(0x258)](_0x4a5455(0x255),function(){var _0x279dba=_0x4a5455;console[_0x279dba(0x2a8)]('maxPriceToListInput\x20changed'),console[_0x279dba(0x2a8)](_0x279dba(0x277),_0x5efa94[_0x279dba(0x2f5)]),chrome[_0x279dba(0x2cd)][_0x279dba(0x29b)][_0x279dba(0x285)]({'maxPrice':_0x5efa94[_0x279dba(0x2f5)]});}),_0xa7303;}async function createMinPriceInput(){var _0x41672a=a0_0x3ff3fb,_0x1db08f=document[_0x41672a(0x22c)](_0x41672a(0x2c6));_0x1db08f[_0x41672a(0x2dc)]=_0x41672a(0x265),_0x1db08f['id']=_0x41672a(0x215),_0x1db08f[_0x41672a(0x247)]=_0x41672a(0x215),_0x1db08f[_0x41672a(0x2f5)]='0',_0x1db08f[_0x41672a(0x23d)]='0',_0x1db08f[_0x41672a(0x24a)]=_0x41672a(0x21e),_0x1db08f['step']='1';var _0x4657b1=document[_0x41672a(0x22c)](_0x41672a(0x243));_0x4657b1[_0x41672a(0x25c)]=_0x41672a(0x215),_0x4657b1[_0x41672a(0x297)](document['createTextNode']('Min\x20Price\x20To\x20List'));var _0x15a5a7=document[_0x41672a(0x22c)](_0x41672a(0x2b7));_0x15a5a7[_0x41672a(0x27c)]=_0x41672a(0x201),_0x15a5a7[_0x41672a(0x297)](_0x1db08f),_0x15a5a7['appendChild'](_0x4657b1);var {minPrice:_0x3df97c}=await chrome[_0x41672a(0x2cd)]['local'][_0x41672a(0x2ef)]('minPrice');return!_0x3df97c&&(_0x3df97c=0x0),_0x1db08f[_0x41672a(0x2f5)]=_0x3df97c,_0x1db08f[_0x41672a(0x258)](_0x41672a(0x255),function(){var _0x5ab966=_0x41672a;console[_0x5ab966(0x2a8)]('minPriceToListInput\x20changed'),console['log']('minPriceToListInput.value',_0x1db08f[_0x5ab966(0x2f5)]),chrome[_0x5ab966(0x2cd)][_0x5ab966(0x29b)][_0x5ab966(0x285)]({'minPrice':_0x1db08f[_0x5ab966(0x2f5)]});}),_0x15a5a7;}async function createConcurrentTasksSelect(){var _0x4d05d7=a0_0x3ff3fb,_0x4423eb=document['createElement'](_0x4d05d7(0x22f));_0x4423eb[_0x4d05d7(0x27c)]=_0x4d05d7(0x286),_0x4423eb[_0x4d05d7(0x247)]=_0x4d05d7(0x286),_0x4423eb['id']=_0x4d05d7(0x286);for(var _0x1df50a=0x1;_0x1df50a<=0xa;_0x1df50a++){var _0x4ce0c9=document[_0x4d05d7(0x22c)](_0x4d05d7(0x288));_0x4ce0c9[_0x4d05d7(0x2f5)]=_0x1df50a,_0x4ce0c9['text']=_0x1df50a,_0x4423eb[_0x4d05d7(0x297)](_0x4ce0c9);}_0x4423eb[_0x4d05d7(0x2f5)]=0x4;var _0x5d305f=document[_0x4d05d7(0x22c)](_0x4d05d7(0x243));_0x5d305f[_0x4d05d7(0x25c)]=_0x4d05d7(0x286),_0x5d305f[_0x4d05d7(0x297)](document['createTextNode'](_0x4d05d7(0x292)));var _0x2fc343=document[_0x4d05d7(0x22c)](_0x4d05d7(0x2b7));_0x2fc343[_0x4d05d7(0x27c)]=_0x4d05d7(0x228),_0x2fc343[_0x4d05d7(0x297)](_0x4423eb),_0x2fc343['appendChild'](_0x5d305f);var {maxConcurrentTasks:_0x474861}=await chrome[_0x4d05d7(0x2cd)][_0x4d05d7(0x29b)][_0x4d05d7(0x2ef)](_0x4d05d7(0x21b));return _0x474861?_0x4423eb[_0x4d05d7(0x2f5)]=_0x474861:(_0x4423eb[_0x4d05d7(0x2f5)]=0x4,await chrome['storage'][_0x4d05d7(0x29b)][_0x4d05d7(0x285)]({'maxConcurrentTasks':'4'})),_0x4423eb['addEventListener']('change',function(){var _0x131004=_0x4d05d7;console['log'](_0x131004(0x2c1)),console[_0x131004(0x2a8)](_0x131004(0x266),_0x4423eb['value']),chrome['storage'][_0x131004(0x29b)][_0x131004(0x285)]({'maxConcurrentTasks':_0x4423eb['value']});}),_0x2fc343;}async function createAutoRefreshCheckbox(){var _0x47329c=a0_0x3ff3fb,_0x428099=document[_0x47329c(0x22c)](_0x47329c(0x2c6));_0x428099[_0x47329c(0x2dc)]=_0x47329c(0x268),_0x428099['id']=_0x47329c(0x2d0),_0x428099[_0x47329c(0x247)]='auto-refresh-checkbox',_0x428099[_0x47329c(0x2f5)]=_0x47329c(0x2d0),_0x428099['checked']=![];var _0x467179=document[_0x47329c(0x22c)]('label');_0x467179[_0x47329c(0x25c)]=_0x47329c(0x2d0),_0x467179[_0x47329c(0x297)](document[_0x47329c(0x253)](_0x47329c(0x2b6)));var _0x29cdbd=document[_0x47329c(0x22c)](_0x47329c(0x2b7));_0x29cdbd[_0x47329c(0x27c)]=_0x47329c(0x2f0),_0x29cdbd['appendChild'](_0x428099),_0x29cdbd[_0x47329c(0x297)](_0x467179);var {autoRefresh:_0x295ba8}=await chrome[_0x47329c(0x2cd)][_0x47329c(0x29b)][_0x47329c(0x2ef)](_0x47329c(0x28b));return _0x295ba8?_0x428099['checked']=!![]:(_0x428099[_0x47329c(0x2a3)]=![],await chrome[_0x47329c(0x2cd)][_0x47329c(0x29b)]['set']({'autoRefresh':![]})),_0x428099[_0x47329c(0x258)](_0x47329c(0x255),function(){var _0x5afc08=_0x47329c;this[_0x5afc08(0x2a3)]?(console['log'](_0x5afc08(0x2a3)),chrome['storage'][_0x5afc08(0x29b)][_0x5afc08(0x285)]({'autoRefresh':!![]})):(console['log']('unchecked'),chrome[_0x5afc08(0x2cd)][_0x5afc08(0x29b)][_0x5afc08(0x285)]({'autoRefresh':![]}));}),_0x29cdbd;}async function createIntroContainer(){var _0x5602d5=a0_0x3ff3fb,_0x4c172b=document[_0x5602d5(0x22c)](_0x5602d5(0x2b7));_0x4c172b[_0x5602d5(0x27c)]='smart-relist-intro';var _0x1619bb=document[_0x5602d5(0x22c)]('p');_0x1619bb[_0x5602d5(0x27c)]=_0x5602d5(0x203),_0x1619bb[_0x5602d5(0x2b4)]=_0x5602d5(0x238);var _0x42bd01=document[_0x5602d5(0x22c)]('p');return _0x42bd01[_0x5602d5(0x26c)][_0x5602d5(0x206)]=_0x5602d5(0x28d),_0x42bd01['textContent']=_0x5602d5(0x2d5),_0x4c172b[_0x5602d5(0x297)](_0x1619bb),_0x4c172b['appendChild'](_0x42bd01),_0x4c172b;}function isTimeLessThan(_0x392249,_0xcdb92b){function _0x5a5461(_0x2cee24){var _0x256f23=a0_0x248a;let _0x232821=0x0,_0xfb0a29=0x0,_0x4881d0=0x0,_0x3b45d2=0x0;const _0x20b216=/(\d+)\s*(d|T|h|Std|m|Min|s|Sek)/g;let _0xb11eaf;while((_0xb11eaf=_0x20b216[_0x256f23(0x2eb)](_0x2cee24))!==null){const _0x5b2f78=parseInt(_0xb11eaf[0x1],0xa),_0x341042=_0xb11eaf[0x2];if(_0x341042==='d'||_0x341042==='T')_0x232821+=_0x5b2f78;else{if(_0x341042==='h'||_0x341042==='Std')_0xfb0a29+=_0x5b2f78;else{if(_0x341042==='m'||_0x341042==='Min')_0x4881d0+=_0x5b2f78;else(_0x341042==='s'||_0x341042===_0x256f23(0x25b))&&(_0x3b45d2+=_0x5b2f78);}}}return{'days':_0x232821,'hours':_0xfb0a29,'minutes':_0x4881d0,'seconds':_0x3b45d2};}const {days:_0x2698f5,hours:_0x12f8b6,minutes:_0x4af96a,seconds:_0x8db703}=_0x5a5461(_0x392249),_0x23b300=_0x2698f5*0x18+_0x12f8b6+_0x4af96a/0x3c+_0x8db703/0xe10;return _0x23b300<_0xcdb92b;}function updatePageOnStop(){var _0x25d94b=a0_0x3ff3fb;alert(_0x25d94b(0x2a9));}async function processLowPerformingItems(_0x4321f4,_0xc2ee7d=0x4){var _0x43642f=a0_0x3ff3fb;let _0x22f8dd=[],_0x32b8fe=0x0,_0x4a73d9=0x0,_0x36d113=![];const _0x464eda=()=>{var _0x586d0d=a0_0x248a;if(_0x4a73d9<_0x4321f4[_0x586d0d(0x2ba)]){console['log']('Preparing\x20to\x20process\x20node\x20at\x20index\x20'+_0x4a73d9);const _0x534e1a=_0x4321f4[_0x4a73d9++],_0x33ee7f=processNode(_0x534e1a)[_0x586d0d(0x2c5)](()=>{var _0x1807af=_0x586d0d;_0x32b8fe++,console['log'](_0x1807af(0x296)+(_0x4a73d9-0x1)+_0x1807af(0x2e0)+_0x32b8fe);})[_0x586d0d(0x20f)](()=>{var _0x5b7da0=_0x586d0d;_0x22f8dd=_0x22f8dd[_0x5b7da0(0x218)](_0x506eb3=>_0x506eb3!==_0x33ee7f),_0x464eda();});_0x22f8dd[_0x586d0d(0x29f)](_0x33ee7f),console[_0x586d0d(0x2a8)](_0x586d0d(0x24e)+(_0x4a73d9-0x1)+'.\x20Ongoing\x20tasks:\x20'+_0x22f8dd[_0x586d0d(0x2ba)]);}else _0x36d113=!![];};for(let _0x372a66=0x0;_0x372a66<_0xc2ee7d&&_0x372a66<_0x4321f4[_0x43642f(0x2ba)];_0x372a66++){_0x464eda();}const _0x4bd80a=async()=>{var _0x1b9f5b=_0x43642f;while(_0x22f8dd[_0x1b9f5b(0x2ba)]>0x0||!_0x36d113){await Promise[_0x1b9f5b(0x27b)](_0x22f8dd);}};await _0x4bd80a(),console['log'](_0x43642f(0x2bc));}async function processNode(_0x5149f1){var _0xf15230=a0_0x3ff3fb;console[_0xf15230(0x2a8)](_0xf15230(0x1fe)+_0x5149f1);try{console[_0xf15230(0x2a8)](_0xf15230(0x22e),_0x5149f1),await smartRelistItem(_0x5149f1);}catch(_0x376cb6){console[_0xf15230(0x231)](_0xf15230(0x293)+_0x5149f1,_0x376cb6);}return _0x5149f1[_0xf15230(0x2de)][_0xf15230(0x260)](_0xf15230(0x1f4)),_0x5149f1;}Promise[a0_0x3ff3fb(0x267)][a0_0x3ff3fb(0x245)]=function(){var _0x1f7ef9=a0_0x3ff3fb;return Promise[_0x1f7ef9(0x2c4)]([this,new Promise(_0x3a9b5c=>setTimeout(_0x3a9b5c,0x0))])===this;};async function setRowCellValue(_0x253fb6,_0x513867,_0x5f7a93){var _0x40d572=a0_0x3ff3fb,_0xa1d444=_0x253fb6[_0x40d572(0x274)](_0x40d572(0x209)+_0x513867+_0x40d572(0x1fb));console['log'](_0x40d572(0x2ab),_0xa1d444);!_0xa1d444&&(_0xa1d444=_0x253fb6[_0x40d572(0x274)]('td[class*=\x22'+_0x513867+'\x22]\x20button[column=\x22'+_0x513867+'\x22]'),console[_0x40d572(0x2a8)](_0x40d572(0x2ab),_0xa1d444));if(!_0xa1d444)return console[_0x40d572(0x2a8)]('editButton\x20button\x20not\x20found'),![];{console[_0x40d572(0x2a8)]('#1');}_0xa1d444['click'](),await new Promise(_0x526b96=>setTimeout(_0x526b96,0xbb8));var _0x33feb5=await waitForInputBox(_0x513867);console['log'](_0x40d572(0x28a),_0x33feb5),console[_0x40d572(0x2a8)]('#2'),_0x33feb5[_0x40d572(0x2f5)]=_0x5f7a93,_0x33feb5[_0x40d572(0x287)](new Event(_0x40d572(0x2c6),{'bubbles':!![]})),focusOnTextBoxAtEndOfTheLine(_0x33feb5),await new Promise(_0x102eca=>setTimeout(_0x102eca,0xbb8)),console[_0x40d572(0x2a8)]('#3');var _0x1f70e9=document['querySelector'](_0x40d572(0x2af));_0x1f70e9[_0x40d572(0x25d)](),console[_0x40d572(0x2a8)]('formButton',_0x1f70e9),console[_0x40d572(0x2a8)]('#4');var _0x1f063e=await waitForPopoverToDisappear();return _0x1f063e?console[_0x40d572(0x2a8)]('succssfullyUpdated'):(_0x253fb6[_0x40d572(0x26c)][_0x40d572(0x2ad)]=_0x40d572(0x2e8),console['log']('failed\x20to\x20update')),console[_0x40d572(0x2a8)]('#5'),await new Promise(_0x3bbbd4=>setTimeout(_0x3bbbd4,0xbb8)),_0x1f063e;}function focusOnTextBoxAtEndOfTheLine(_0x12e93c){var _0x20d600=a0_0x3ff3fb,_0x15e6c4=_0x12e93c[_0x20d600(0x2f5)][_0x20d600(0x2ba)];if(_0x12e93c[_0x20d600(0x25e)])_0x12e93c[_0x20d600(0x208)](),_0x12e93c['setSelectionRange'](_0x15e6c4,_0x15e6c4);else{if(_0x12e93c['createTextRange']){var _0x410af3=_0x12e93c['createTextRange']();_0x410af3['collapse'](!![]),_0x410af3[_0x20d600(0x250)](_0x20d600(0x234),_0x15e6c4),_0x410af3['moveStart'](_0x20d600(0x234),_0x15e6c4),_0x410af3['select']();}}}async function waitForInputBox(_0x29d97d){return new Promise(function(_0x263ea7,_0x50f248){var _0x49c359=a0_0x248a,_0x43e03a=document['querySelector'](_0x49c359(0x272)+_0x29d97d+']\x22]');if(_0x43e03a)_0x263ea7(_0x43e03a);else{var _0x302ad4=new MutationObserver(function(_0x46a6d3){var _0x18cfca=_0x49c359;_0x46a6d3[_0x18cfca(0x226)](function(_0x1d2aa8){var _0x481ccb=_0x18cfca;_0x1d2aa8[_0x481ccb(0x2e2)][_0x481ccb(0x2ba)]>0x0&&(_0x43e03a=document[_0x481ccb(0x274)](_0x481ccb(0x272)+_0x29d97d+_0x481ccb(0x214)),_0x43e03a&&(_0x302ad4[_0x481ccb(0x210)](),_0x263ea7(_0x43e03a)));});});_0x302ad4[_0x49c359(0x279)](document['body'],{'childList':!![],'subtree':!![],'attributes':![],'characterData':![]});}});}async function waitForPopoverToDisappear(){return new Promise(function(_0x104a51,_0x1b4a1d){var _0x16a97c=a0_0x248a,_0x1e7120=document[_0x16a97c(0x274)](_0x16a97c(0x2be)),_0x250003=_0x1e7120[_0x16a97c(0x274)](_0x16a97c(0x2c3)),_0x33821c=!![],_0x495913,_0x533035;if(!_0x1e7120)_0x104a51();else{var _0x4882fb=new MutationObserver(function(_0x5f3010){var _0x2943b0=_0x16a97c;_0x5f3010[_0x2943b0(0x226)](function(_0x139b29){var _0x4ef0b4=_0x2943b0;_0x139b29[_0x4ef0b4(0x278)][_0x4ef0b4(0x2ba)]>0x0&&(_0x1e7120=document[_0x4ef0b4(0x274)]('.popover-content-wrap'),!_0x1e7120&&(_0x4882fb['disconnect'](),clearTimeout(_0x495913),clearTimeout(_0x533035),_0x104a51(_0x33821c)));});});_0x4882fb['observe'](document['body'],{'childList':!![],'subtree':!![],'attributes':![],'characterData':![]}),_0x495913=setTimeout(function(){var _0xc3be81=_0x16a97c;_0x1e7120=document[_0xc3be81(0x274)](_0xc3be81(0x2be)),console[_0xc3be81(0x2a8)]('popoverElement',_0x1e7120),console['log']('cancelButton',_0x250003),_0x1e7120&&(_0x250003[_0xc3be81(0x25d)](),_0x33821c=![]);},0x3a98),_0x533035=setTimeout(function(){var _0x16fc54=_0x16a97c;_0x1e7120=document['querySelector'](_0x16fc54(0x2be)),console[_0x16fc54(0x2a8)]('popoverElement',_0x1e7120),console[_0x16fc54(0x2a8)]('sending\x20message\x20to\x20continue\x20tracking\x20page'),_0x1e7120&&chrome[_0x16fc54(0x249)][_0x16fc54(0x239)]({'type':'continue_tracking'});},0x7530);}});}