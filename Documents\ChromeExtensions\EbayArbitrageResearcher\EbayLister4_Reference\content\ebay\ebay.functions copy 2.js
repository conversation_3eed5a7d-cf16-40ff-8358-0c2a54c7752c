(function(_0x2caba6,_0x423eca){var _0x59720e=a0_0x5f49,_0x454683=_0x2caba6();while(!![]){try{var _0x275901=-parseInt(_0x59720e(0x213))/0x1*(-parseInt(_0x59720e(0x228))/0x2)+parseInt(_0x59720e(0x18d))/0x3+-parseInt(_0x59720e(0x1ac))/0x4*(parseInt(_0x59720e(0x173))/0x5)+-parseInt(_0x59720e(0x24a))/0x6+-parseInt(_0x59720e(0x1f0))/0x7+-parseInt(_0x59720e(0x1e4))/0x8*(-parseInt(_0x59720e(0x242))/0x9)+parseInt(_0x59720e(0x1da))/0xa*(-parseInt(_0x59720e(0x1f9))/0xb);if(_0x275901===_0x423eca)break;else _0x454683['push'](_0x454683['shift']());}catch(_0x2e4b22){_0x454683['push'](_0x454683['shift']());}}}(a0_0x5e81,0x68cde));async function pasteImages(_0x536dfc,_0xd213ac,_0x545dda){var _0x32bad5=a0_0x5f49,_0xac5a3d=_0x536dfc,_0x4cf0cd=0x1,_0x28a37c=_0x32bad5(0x195),_0xe9372d=_0xd213ac[_0x32bad5(0x1a9)](0x0,0x64)+'-'+_0x4cf0cd+_0x32bad5(0x1d0),_0x171b7c=await getFromLocalStorage(_0x32bad5(0x26b)),_0x409d64=_0xac5a3d[0x0],_0x3dc768=0x5dc,_0x2ff063=0x5dc,_0x2edd0d='black',_0x488b8d=_0x32bad5(0x265),{useReviewImages:_0x208acf}=await chrome[_0x32bad5(0x187)][_0x32bad5(0x1b7)]['get'](_0x32bad5(0x1db));if(_0xac5a3d[_0x32bad5(0x271)]>=0x4&&_0x545dda[_0x32bad5(0x18a)]==_0x32bad5(0x1d2)&&!_0x208acf){var _0x15f909=_0xac5a3d[0x1],_0x4d800c=_0xac5a3d[0x2],_0x151ecb=_0xac5a3d[0x3],_0x2cb681=await create_multi_image(_0x409d64,_0x15f909,_0x171b7c,_0x4d800c,_0x151ecb,_0xd213ac,_0x3dc768,_0x2ff063,_0x2edd0d,_0x488b8d);documentTitle=_0x32bad5(0x1cb),await uploadImageAndWaitForCounterToUpload(_0x2cb681[_0x32bad5(0x1a5)],_0xe9372d,IMAGES_TYPES[_0x32bad5(0x16a)]),await waitForNetworkResponseCountIncrease(),_0x4cf0cd++;}var _0x3874fa;_0xac5a3d[_0x32bad5(0x271)]+_0x4cf0cd>0xc?_0x3874fa=_0xac5a3d[_0x32bad5(0x271)]-_0x4cf0cd:_0x3874fa=_0xac5a3d['length'];for(var _0x19c07d=0x0;_0x19c07d<_0x3874fa;_0x19c07d++){documentTitle=_0x32bad5(0x15e)+(_0x19c07d+0x1)+'/'+_0x3874fa+_0x32bad5(0x1f1),_0xe9372d=_0xd213ac[_0x32bad5(0x1a9)](0x0,0x64)+'-'+_0x4cf0cd+_0x32bad5(0x1d0);try{var _0x3a7a92=await urlToImage(_0xac5a3d[_0x19c07d]);}catch(_0x2bf1eb){console[_0x32bad5(0x21c)](_0x32bad5(0x16f),_0x2bf1eb);continue;}_0x3a7a92=await upscaleToMinimumSize(_0x3a7a92,0x1f4,0x1f4),await uploadImageAndWaitForCounterToUpload(_0x3a7a92[_0x32bad5(0x1a5)],_0xe9372d,IMAGES_TYPES[_0x32bad5(0x16a)]),await waitForNetworkResponseCountIncrease(),_0x4cf0cd++;}}async function pasteOnlyImages(_0x4c0db0,_0xef18b6){var _0x540b4f=a0_0x5f49;for(var _0x322db7=0x0;_0x322db7<_0x4c0db0['length'];_0x322db7++){documentTitle=_0x540b4f(0x15e)+(_0x322db7+0x1)+'/'+_0x4c0db0[_0x540b4f(0x271)]+_0x540b4f(0x1f1),imageName=_0xef18b6[_0x540b4f(0x1a9)](0x0,0x64)+'-'+_0x322db7+'.jpg';var _0x5da89f=await urlToImage(_0x4c0db0[_0x322db7]);_0x5da89f=await upscaleToMinimumSize(_0x5da89f,0x1f4,0x1f4),await uploadImageAndWaitForCounterToUpload(_0x5da89f[_0x540b4f(0x1a5)],imageName,IMAGES_TYPES[_0x540b4f(0x16a)]),await waitForNetworkResponseCountIncrease();}}function pastePrice(_0x42075b){return new Promise(async(_0x364e96,_0x4fe7fc)=>{var _0x154cf7=a0_0x5f49,{domain:_0x14f3b8}=await chrome[_0x154cf7(0x187)][_0x154cf7(0x1b7)][_0x154cf7(0x243)]('domain');(_0x14f3b8=='de'||_0x14f3b8=='fr'||_0x14f3b8=='it'||_0x14f3b8=='es')&&(_0x42075b=_0x42075b['replace']('.',','));var _0x17e202=_0x154cf7(0x1bd),_0x22f2bf=document[_0x154cf7(0x1ab)](_0x17e202);_0x22f2bf[_0x154cf7(0x260)]=_0x42075b,scrollIntoView(_0x22f2bf);var _0x62939c=document['createEvent'](_0x154cf7(0x274));_0x62939c['initEvent'](_0x154cf7(0x171),!![],!![]),_0x22f2bf['dispatchEvent'](_0x62939c),_0x364e96();});}function pasteTitle(_0x4c38f3){return new Promise((_0x51332d,_0x41e7b6)=>{var _0xbed67b=a0_0x5f49;_0x4c38f3[_0xbed67b(0x271)]>0x50&&(_0x4c38f3=_0x4c38f3['substring'](0x0,0x4d)+_0xbed67b(0x1d4));var _0x492f6a=_0xbed67b(0x253),_0x268dba=document[_0xbed67b(0x1ab)](_0x492f6a);_0x268dba['value']=_0x4c38f3,scrollIntoView(_0x268dba);var _0x485d41=document[_0xbed67b(0x25a)](_0xbed67b(0x274));_0x485d41['initEvent']('change',!![],!![]),_0x268dba[_0xbed67b(0x167)](_0x485d41),_0x268dba[_0xbed67b(0x1b2)][_0xbed67b(0x24b)]=_0xbed67b(0x189),_0x51332d();});}function pasteSKU(_0x5e29d0){return new Promise((_0x1ffa62,_0x2bfcf4)=>{var _0x4c09f8=a0_0x5f49,_0x1feb89=_0x4c09f8(0x1bc),_0x32aaef=document[_0x4c09f8(0x1ab)](_0x1feb89);scrollIntoView(_0x32aaef),console[_0x4c09f8(0x21c)](_0x4c09f8(0x206)+_0x5e29d0),console[_0x4c09f8(0x21c)](_0x4c09f8(0x206)+_0x32aaef),_0x32aaef['value']=_0x5e29d0,_0x32aaef[_0x4c09f8(0x167)](new Event('change',{'bubbles':!![]})),_0x32aaef[_0x4c09f8(0x1b2)][_0x4c09f8(0x24b)]=_0x4c09f8(0x189),_0x1ffa62();});}function pasteUpc(_0x41bacf){return new Promise((_0x313c5f,_0x4665cd)=>{var _0x55c3e2=a0_0x5f49,_0x352201=document[_0x55c3e2(0x1ab)](_0x55c3e2(0x16b));_0x352201[_0x55c3e2(0x260)]=_0x41bacf,_0x352201[_0x55c3e2(0x167)](new Event(_0x55c3e2(0x160),{'bubbles':!![]})),_0x352201[_0x55c3e2(0x1b2)][_0x55c3e2(0x24b)]='3px\x20solid\x20#DFFF00',_0x313c5f();});}function pasteUpcV2(_0x29247f){return new Promise(async(_0x465bf8,_0x26d930)=>{var _0x7277a2=a0_0x5f49;console[_0x7277a2(0x21c)](_0x7277a2(0x1ee)+_0x29247f),await enterCustomItemSpecificSynchronously(_0x7277a2(0x208),_0x29247f),await waitSomeTime(0x3e8);var _0x29092d=document[_0x7277a2(0x1ab)](_0x7277a2(0x1ef));_0x29092d&&(_0x29092d['focus'](),_0x29092d['click']()),_0x465bf8();});}function a0_0x5e81(){var _0x8b0bf9=['...','blur','unit','<tr>','filter','#_errTag','468910hTdwDI','useReviewImages','promotedListingSwitch\x20does\x20not\x20exist','.details__euResponsiblePersons\x20input[name=\x22phoneNumber\x22]','exitingFieldNames','error','itemLocation','country/region\x20of\x20manufacture','parentElement','currentValue','978008RpgQDN','Product','No\x20option\x20found\x20for\x20country\x20code:\x20','input#pkgWidth','initEvent','Error\x20while\x20filling\x20product\x20manufacturer\x20info:','push','children','\x20of\x2012\x20photos','dimensions','pasting\x20upc:\x20','button[aria-label=\x27Close\x20dialog\x27]','3678633WwfOnQ','\x20To\x20Ebay','required','setSelectionRange','.summary__attributes-gridview--label\x20label','success','findIndex','input#pkgHeight','from','11KbssGy','MPN','Year\x20Manufactured','input[id*=\x27Listing.Item.ItemSpecific\x27]','.uploader-thumbnails-ux__header__photo-count','createTextRange','application/json','address1','input[name=\x22name\x22]','input[name=\x22adRate\x22]','button','itemLocationCityState','Exists\x20fieldName:\x20','pasteSKU:\x20','123456789','UPC','returnsPolicyId','fieldname','itemSpecs','2nd\x20Floor','input#majorUnitWeight','22.86\x20x\x208.74\x20x\x201.78\x20cm;\x20362.87\x20grams','34696313170','deleteOldBrand','GSPR\x20already\x20set','Setting\x20GSPR','30CluqqI','context','.details__euResponsiblePersons\x20input[name=\x22emailAddress\x22]','input[name=\x22productContactInformation\x22]','inputBox:','Setting\x20item\x20location','[n*=\x27Brand\x27]','[name*=\x27_st_\x27]','[class$=\x22label\x22]','log','Show\x20more\x20button\x20clicked','Catalonia','singleNodeValue','shippingWeight:\x20','[_track=\x221.productManufacturer.2.Done\x22].btn.btn--primary','input[name=\x22emailAddress\x22]','Barcelona','manufacturer\x20part\x20number','shippingWeight','data.data.score\x20>\x200.3','Not\x20Set','15196hraYdL','No\x20corresponding\x20dropdown\x20option\x20found\x20for\x20index\x20','#_isVal','Item\x20was\x20not\x20successfully\x20posted.','reqd','28046','replace','responseFromBertToken','.listbox__option','inputBox\x20does\x20not\x20exist','data','What\x20is\x20the\x20','Unable\x20to\x20find\x20index\x20for\x20country\x20code:\x20','\x20item\x20specific(s)\x20selected\x20manually.','input[name*=\x27_st_\x27]:not([type=checkbox])','No\x20country\x20select\x20found','toLowerCase','Form\x20fields\x20populated\x20successfully\x20(country\x20set\x20by\x20code).','.uploader-thumbnails__photo-count','stateOrProvince','Failed\x20to\x20fill\x20EU\x20Responsible\x20Person\x20info:','.btn--primary','setDate','</pre>\x0a\x09\x09<h4>Responsible\x20Person\x20EU:</h4>\x0a\x09\x09<pre>','\x0a\x09\x20\x20<div\x20style=\x22border:1px\x20solid\x20#ccc;\x20padding:10px;\x20margin-top:10px;\x22>\x0a\x09\x09<h3>EU\x20Compliance\x20Information</h3>\x0a\x09\x09<h4>Manufacturer\x20Info:</h4>\x0a\x09\x09<pre>','N/A','63QKGygx','get','country','Item\x20was\x20successfully\x20posted!','Does\x20Not\x20Apply','split','input[id*=\x27Listing.Item\x27]','getAttribute','2954628dkyDcz','border','label','.details__euResponsiblePersons\x20.listbox__option','08001','</p>\x0a\x09\x09<p><strong>Location:</strong>\x20','No\x20checkboxes\x20found\x20to\x20select.','New','2.1','.smry.summary__title\x20input[name=\x27title\x27]','Madrid','find','width','pasting\x20brand:\x20','focus','.summary__extracted-aspects','createEvent','shippingWeightUnit:\x20','button[aria-label*=\x27List\x27]','city','json','keydown','value','[class$=\x22value\x22]','Enter','All\x20applicable\x20item\x20specifics\x20selected\x20using\x20the\x20Select\x20all\x20button.','entries','Ariel','label:\x20[','Manufactured','Brand','</tr>','[id=\x22Listing.Item.ItemSpecific[Type]','watermark_url','loading','Setting\x20item\x20location\x20country','filterMenuContent\x20does\x20not\x20exist','a[id$=cancle]','Additional\x20Parts\x20Required','length','.smry.summary__condition\x20button','//a[text()=\x27\x20+\x20Add\x20custom\x20item\x20specific\x27]','HTMLEvents','[_track=\x221.euResponsiblePersons1.2.Done\x22].btn.btn--primary','itemLocationCountry','<EMAIL>','answer','getMonth','moveStart','getAllItemSpecificsAndFillDetailsWithMachineLearning','html','input#privateAuction','POST','Uploading\x20Image\x20#','FIRST_ORDERED_NODE_TYPE','input','address2','toUpperCase','textContent','pasteCustomItemSpecifics:\x20','.success__header','returnPolicyId','dispatchEvent','</td>','Setting\x20item\x20location\x20city\x20state','BASE_64','input#upc','catch','*************************************','then','error\x20getting\x20image\x20from\x20url:\x20','button[_track*=\x27ShowMore\x27]','change','Show\x20more','5gxqebZ','querySelectorAll','fieldset\x20.summary__attributes-gridview','optinCheckbox','match','fillProductManufacturer','<table\x20style=\x22width:\x20100%;\x20table-layout:\x20fixed;\x22>','input#minorUnitWeight','</p>\x0a\x20\x20\x0a\x09\x09<h4>Responsible\x20Person\x20(EU):</h4>\x0a\x09\x09<p><strong>Name:</strong>\x20','trim','requiredItemSpecifics:\x20','s0-0-0-24-7-23[7]-0-28-3-16[0]-0-16-8-38[0[0]]-0-se-filter-list-1-input-se-textbox','getDate','input[name=\x22promotedListingSelection\x22]','.smry.summary__cta','.details__euResponsiblePersons\x20input[name=\x22stateOrProvince\x22]','getElementById','Details\x20in\x20Description','question','No\x20country\x20dropdown\x20button\x20found','storage','</p>\x0a\x09\x20\x20</div>\x0a\x09','3px\x20solid\x20#DFFF00','listingType','getFullYear','href','2380716xTQICu','.product-manufacturer__add-button','<td>&nbsp;</td>','#_isSave','emailAddress','map','.details__euResponsiblePersons\x20input[name=\x22city\x22]','.summary__attributes-gridview--label\x20button.fake-link.tooltip__host','input[id^=\x27upl-\x27]','format','[id=\x22Listing.Item.ItemSpecific[MPN]','GAVIMOSA\x20CONSULTORIA,\x20SOCIEDAD\x20LIMITADA','tabooItemSpecifics','Apple','options','Unavailable','</p>\x0a\x09\x09<p><strong>Address:</strong>\x20','Promoting\x20your\x20listing!','character','includes','Manufacturer\x20Warranty','input[name=\x22address2\x22]','Manufacture','score','src','filterMenuContent\x20exists','.condition-recommendation','height','substring','</table>','querySelector','1532572UmQGwt','1totalNetworkResponse\x20','.smry.summary__attributes\x20.summary__attributes-gridview','[name=\x22contact-list-add-button\x22]','.details__euResponsiblePersons\x20input[name=\x22address1\x22]','sendMessage','style','.success__body-link\x20a','getElementsByTagName','#editpane_itmspc\x20.reqd','input#pkgLength','local','Bearer\x20','getElementsByClassName','checked','matchedElems','.smry.summary__title\x20input[name=\x27customLabel\x27]','.smry.summary__price\x20input[name=\x27price\x27]',':visible','.details__euResponsiblePersons\x20input[name=\x22postalCode\x22]','name','Model','click','select[name=\x22country\x22]','<td><strong>','postalCode','input[type=\x22checkbox\x22]','Country','[id=\x22Listing.Item.ItemSpecific[Material]\x22]','stringify','.details__contact-info.details__euResponsiblePersons','Uploading\x20Multi\x20Image!','#_isTag','phoneNumber','innerText','<EMAIL>','.jpg','value:','paid','3totalNetworkResponse\x20'];a0_0x5e81=function(){return _0x8b0bf9;};return a0_0x5e81();}function waitSomeTime(_0x7ab88b){return new Promise((_0x5993f9,_0x2e1325)=>{setTimeout(()=>{_0x5993f9();},_0x7ab88b);});}async function clickShowMoreButton(){var _0xad1981=a0_0x5f49,_0x42e3f0=document[_0xad1981(0x174)]('button'),_0x5a6a9b=searchElementsForInnerText(_0xad1981(0x172),_0x42e3f0);console[_0xad1981(0x21c)]('showMoreButton',_0x5a6a9b),!_0x5a6a9b&&(_0x5a6a9b=document[_0xad1981(0x1ab)](_0xad1981(0x170))),_0x5a6a9b&&(documentTitle='Show\x20more\x20button\x20found',_0x5a6a9b[_0xad1981(0x1c2)](),await waitSomeTime(0x3e8),documentTitle=_0xad1981(0x21d));}function pickItemCondition(){return new Promise((_0x15562e,_0x360183)=>{var _0x57c478=a0_0x5f49,_0x5266f1=_0x57c478(0x272),_0x347c0b=document[_0x57c478(0x174)](_0x5266f1),_0x59ef69=searchElementsForInnerText(_0x57c478(0x251),_0x347c0b);_0x59ef69[_0x57c478(0x1c2)](),_0x59ef69[_0x57c478(0x258)]();var _0x3406d1=new Event(_0x57c478(0x171),{'bubbles':!![]});_0x59ef69[_0x57c478(0x167)](_0x3406d1),_0x15562e();});}function focusOnTextBoxAtEndOfTheLine(_0x3b8fc7){var _0x15d367=a0_0x5f49,_0x46ba17=_0x3b8fc7['value'][_0x15d367(0x271)];if(_0x3b8fc7['setSelectionRange'])_0x3b8fc7[_0x15d367(0x258)](),_0x3b8fc7[_0x15d367(0x1f3)](_0x46ba17,_0x46ba17);else{if(_0x3b8fc7[_0x15d367(0x1fe)]){var _0x328f81=_0x3b8fc7[_0x15d367(0x1fe)]();_0x328f81['collapse'](!![]),_0x328f81['moveEnd'](_0x15d367(0x19f),_0x46ba17),_0x328f81[_0x15d367(0x159)]('character',_0x46ba17),_0x328f81['select']();}}}function testInput(){var _0x3ef737=a0_0x5f49,_0x5a1a7a=document[_0x3ef737(0x183)](_0x3ef737(0x17e));_0x5a1a7a[_0x3ef737(0x260)]=_0x3ef737(0x19a),keydownEvent=new KeyboardEvent(_0x3ef737(0x25f),{'bubbles':!![],'key':_0x3ef737(0x262)}),_0x5a1a7a['dispatchEvent'](keydownEvent),changeEvent=new Event(_0x3ef737(0x171),{'bubbles':!![]}),_0x5a1a7a['dispatchEvent'](changeEvent);}async function enterCustomItemSpecific(_0x1f2a18,_0x387b16){var _0x166d9d=a0_0x5f49,_0x3df780=document[_0x166d9d(0x174)](_0x166d9d(0x175));for(var _0x30944c=0x0;_0x30944c<_0x3df780['length'];_0x30944c++){var _0x369414=_0x3df780[_0x30944c],_0x1bfde5=_0x369414['querySelector'](_0x166d9d(0x21b)),_0x53bce5=_0x369414[_0x166d9d(0x1ab)](_0x166d9d(0x261));console[_0x166d9d(0x21c)](_0x166d9d(0x266)+_0x1bfde5[_0x166d9d(0x1ce)]+']');if(_0x1bfde5['innerText']==_0x1f2a18){console['log'](_0x166d9d(0x1d1),_0x53bce5);var _0xde339a=_0x53bce5[_0x166d9d(0x1ab)]('[class$=\x22filter-menu__content\x22][id$=\x22-content\x22]');console[_0x166d9d(0x21c)]('filterMenuContent:',_0xde339a);var _0x4796a2;if(_0xde339a)console[_0x166d9d(0x21c)](_0x166d9d(0x1a6)),_0x4796a2=_0xde339a[_0x166d9d(0x1ab)](_0x166d9d(0x160)),console['log']('inputBox:',_0x4796a2),_0x4796a2[_0x166d9d(0x260)]=_0x387b16,keydownEvent=new KeyboardEvent(_0x166d9d(0x25f),{'bubbles':!![],'key':_0x166d9d(0x262)}),_0x4796a2[_0x166d9d(0x167)](keydownEvent),changeEvent=new Event(_0x166d9d(0x171),{'bubbles':!![]}),_0x4796a2[_0x166d9d(0x167)](changeEvent),_0x4796a2[_0x166d9d(0x1b2)][_0x166d9d(0x24b)]='3px\x20solid\x20#DFFF00';else{console['log'](_0x166d9d(0x26e));try{_0x53bce5[_0x166d9d(0x1ab)](_0x166d9d(0x203))['click']();}catch(_0x41fe00){console[_0x166d9d(0x21c)]('error\x20clicking\x20button');}}if(!_0x4796a2){console[_0x166d9d(0x21c)](_0x166d9d(0x1ad),totalNetworkResponse),console[_0x166d9d(0x21c)](_0x166d9d(0x231)),_0x4796a2=_0x53bce5[_0x166d9d(0x1ab)](_0x166d9d(0x160)),console[_0x166d9d(0x21c)](_0x166d9d(0x217),_0x4796a2),_0x4796a2[_0x166d9d(0x258)](),_0x4796a2[_0x166d9d(0x260)]=_0x387b16,_0x4796a2[_0x166d9d(0x167)](new Event(_0x166d9d(0x171),{'bubbles':!![]})),blurEvent=new Event(_0x166d9d(0x1d5),{'bubbles':!![]}),_0x4796a2[_0x166d9d(0x167)](blurEvent);var _0x348464=_0x166d9d(0x253),_0x296825=document[_0x166d9d(0x1ab)](_0x348464);_0x296825['focus'](),console[_0x166d9d(0x21c)]('2totalNetworkResponse\x20',totalNetworkResponse),await waitForNetworkResponseCountIncrease(),console[_0x166d9d(0x21c)](_0x166d9d(0x1d3),totalNetworkResponse),_0x4796a2['style']['border']='3px\x20solid\x20#DFFF00';}break;}}}function pasteBrand(_0x1b4b27){return new Promise(async(_0x1df807,_0x157a68)=>{var _0x2c1e9b=a0_0x5f49;console[_0x2c1e9b(0x21c)](_0x2c1e9b(0x257)+_0x1b4b27),await enterCustomItemSpecificSynchronously('Brand',_0x1b4b27),_0x1df807();});}function selectFormat(_0x45c157){return new Promise((_0x5a7efd,_0x1a2f62)=>{var _0x1dddd4=a0_0x5f49,_0x40b5b6=document[_0x1dddd4(0x183)](_0x1dddd4(0x196));_0x40b5b6[_0x1dddd4(0x260)]=_0x45c157,_0x40b5b6[_0x1dddd4(0x167)](new Event(_0x1dddd4(0x160),{'bubbles':!![]})),_0x5a7efd();});}function applyPromotedListingV2(){return new Promise(async(_0x324f6c,_0x4a3f1a)=>{var _0x1e7962=a0_0x5f49;documentTitle=_0x1e7962(0x19e);var _0x12809e=document[_0x1e7962(0x1ab)](_0x1e7962(0x180));!_0x12809e&&(console[_0x1e7962(0x21c)](_0x1e7962(0x1dc)),_0x324f6c());_0x12809e[_0x1e7962(0x1c2)](),scrollIntoView(_0x12809e),await waitForNetworkResponseCountIncrease();const _0x5ca465=await _waitForElement(_0x1e7962(0x202));var _0x344046=document[_0x1e7962(0x1ab)](_0x1e7962(0x202));highlightElement(_0x344046,_0x1e7962(0x26c)),_0x344046[_0x1e7962(0x260)]=_0x1e7962(0x252),dispatchKeyBoardAndChangeEvent(_0x344046),await waitForNetworkResponseCountIncrease(),documentTitle='Promoted\x20Listing\x20Applied:\x20'+_0x344046[_0x1e7962(0x260)]+'%',highlightElement(_0x344046,_0x1e7962(0x1f5)),_0x324f6c();});}function applyPromotedListing(){var _0x49b819=a0_0x5f49,_0x44c29b=document[_0x49b819(0x183)](_0x49b819(0x176));_0x44c29b[_0x49b819(0x1c2)]();var _0x2382a4=document[_0x49b819(0x183)]('adRate');_0x2382a4[_0x49b819(0x260)]=1.2;var _0x5830de=document['createEvent'](_0x49b819(0x274));_0x5830de[_0x49b819(0x1e8)](_0x49b819(0x171),![],!![]);var _0x5830de=new Event('input',{'bubbles':!![],'cancelable':!![]});_0x2382a4[_0x49b819(0x167)](_0x5830de);}function pasteItemSpecifics(_0x3216ca){return new Promise((_0x20359a,_0x4e8488)=>{var _0x9fb9dc=a0_0x5f49,_0x10aae7=document['querySelectorAll'](_0x9fb9dc(0x1fc));for(var _0x22caaa=0x0;_0x22caaa<_0x10aae7[_0x9fb9dc(0x271)];_0x22caaa++){var _0x575f98=_0x10aae7[_0x22caaa],_0x2a707b=_0x575f98[_0x9fb9dc(0x249)](_0x9fb9dc(0x20a));if(_0x2a707b){_0x2a707b=_0x2a707b[_0x9fb9dc(0x238)]();for(var _0x1626b8=0x0;_0x1626b8<_0x3216ca[_0x9fb9dc(0x271)];_0x1626b8++){var _0x2688fd=_0x3216ca[_0x1626b8],_0x193622=_0x2688fd['label']['toLowerCase'](),_0x30c1b0=_0x2688fd[_0x9fb9dc(0x260)];_0x30c1b0['length']>0x40&&(_0x30c1b0=_0x30c1b0['substring'](0x0,0x40));try{_0x30c1b0=jsUcfirst(string);}catch(_0x2fc186){}_0x2a707b[_0x9fb9dc(0x1a0)](_0x193622)&&(!_0x575f98[_0x9fb9dc(0x260)]&&(_0x575f98[_0x9fb9dc(0x260)]=_0x30c1b0)),_0x193622[_0x9fb9dc(0x1a0)](_0x2a707b)&&(!_0x575f98[_0x9fb9dc(0x260)]&&(_0x575f98[_0x9fb9dc(0x260)]=_0x30c1b0));}}}_0x20359a();});}function pasteNAToRequiredItemSpecificsV2(){return new Promise(async(_0x5424e8,_0x57348b)=>{var _0x29e932=a0_0x5f49,_0x33a0ae=getAllTypeOfItemSpecific(_0x29e932(0x1f2));console[_0x29e932(0x21c)](_0x29e932(0x17d),_0x33a0ae);for(var _0x27cb22=0x0;_0x27cb22<_0x33a0ae[_0x29e932(0x271)];_0x27cb22++){var _0xb4e47c=_0x33a0ae[_0x27cb22];await enterCustomItemSpecificSynchronously(_0xb4e47c,_0x29e932(0x184));}_0x5424e8();});}function enterCustomItemSpecificSynchronously(_0x2e27be,_0x3a511e){return new Promise(async(_0x4d3703,_0x14fb0d)=>{try{await enterCustomItemSpecific(_0x2e27be,_0x3a511e),_0x4d3703();}catch(_0x4c96dd){setTimeout(async()=>{await enterCustomItemSpecific(_0x2e27be,_0x3a511e),_0x4d3703();},0x7d0);}});}function getAllTypeOfItemSpecific(_0x5c9617){var _0x1cf4b0=a0_0x5f49,_0x442082=[],_0x1b3447=document[_0x1cf4b0(0x1ab)](_0x1cf4b0(0x1ae)),_0x43e38d=_0x1b3447[_0x1cf4b0(0x1eb)];for(var _0x2906eb=0x0;_0x2906eb<_0x43e38d['length'];_0x2906eb++){var _0x4b1bf8=_0x43e38d[_0x2906eb],_0x55e6e4=_0x4b1bf8['querySelector']('.se-panel-section__title-container');if(_0x55e6e4){var _0x1f9a93=_0x55e6e4[_0x1cf4b0(0x163)];if(_0x1f9a93){if(_0x1f9a93[_0x1cf4b0(0x238)]()['includes'](_0x5c9617)){var _0x5ea393=_0x4b1bf8[_0x1cf4b0(0x174)](_0x1cf4b0(0x194));!_0x5ea393['length']&&(_0x5ea393=_0x4b1bf8[_0x1cf4b0(0x174)](_0x1cf4b0(0x1f4)));for(var _0x32d397=0x0;_0x32d397<_0x5ea393[_0x1cf4b0(0x271)];_0x32d397++){var _0x55a8b7=_0x5ea393[_0x32d397][_0x1cf4b0(0x163)];console['log'](_0x55a8b7),_0x442082[_0x1cf4b0(0x1ea)](_0x55a8b7);}break;}}}}return _0x442082;}function pasteNAToRequiredItemSpecifics(){return new Promise((_0x5cf08c,_0x5c3f5b)=>{var _0x31eae5=a0_0x5f49,_0x18262f=document[_0x31eae5(0x174)](_0x31eae5(0x1b5));for(var _0x4e25bc=0x0;_0x4e25bc<_0x18262f[_0x31eae5(0x271)];_0x4e25bc++){var _0x37f192=_0x18262f[_0x4e25bc][_0x31eae5(0x1e2)],_0x1cebc4=_0x37f192[_0x31eae5(0x174)](_0x31eae5(0x248));if(_0x1cebc4[_0x31eae5(0x271)]>0x0){var _0x3630fb=_0x1cebc4[0x0];(_0x3630fb[_0x31eae5(0x260)]==''||_0x3630fb[_0x31eae5(0x260)]==0x0||_0x3630fb[_0x31eae5(0x260)]==null)&&(_0x3630fb['value']='Unavailable');}}var _0x235f04=document[_0x31eae5(0x1b9)](_0x31eae5(0x22c));for(let _0x5a1d0a=0x0;_0x5a1d0a<_0x235f04[_0x31eae5(0x271)];_0x5a1d0a++){var _0x35be7d=_0x235f04[_0x5a1d0a]['parentElement'][_0x31eae5(0x1e2)][_0x31eae5(0x1e2)],_0x11b8c2=_0x35be7d['querySelectorAll'](_0x31eae5(0x21a));if(_0x11b8c2[_0x31eae5(0x271)]>0x0){var _0x3630fb=_0x35be7d[_0x31eae5(0x174)]('[name*=\x27_st_\x27]')[0x0];(_0x3630fb[_0x31eae5(0x260)]==''||_0x3630fb[_0x31eae5(0x260)]==0x0||_0x3630fb[_0x31eae5(0x260)]==null)&&(_0x3630fb[_0x31eae5(0x260)]=_0x31eae5(0x19c));}}var _0xac11ac=document[_0x31eae5(0x174)](_0x31eae5(0x1c8));_0xac11ac[_0x31eae5(0x271)]>0x0&&((_0xac11ac[0x0][_0x31eae5(0x260)]==''||_0xac11ac[0x0][_0x31eae5(0x260)]==0x0||_0xac11ac[0x0]['value']==null)&&(_0xac11ac[0x0][_0x31eae5(0x260)]=_0x31eae5(0x19c)));var _0x47102b=document['querySelectorAll'](_0x31eae5(0x197));_0x47102b['length']>0x0&&((_0x47102b[0x0][_0x31eae5(0x260)]==''||_0x47102b[0x0]['value']==0x0||_0x47102b[0x0][_0x31eae5(0x260)]==null)&&(_0x47102b[0x0][_0x31eae5(0x260)]='Does\x20Not\x20Apply'));var _0x303bc2=document[_0x31eae5(0x174)](_0x31eae5(0x26a));_0x303bc2[_0x31eae5(0x271)]>0x0&&((_0x303bc2[0x0][_0x31eae5(0x260)]==''||_0x303bc2[0x0]['value']==0x0||_0x303bc2[0x0][_0x31eae5(0x260)]==null)&&(_0x303bc2[0x0][_0x31eae5(0x260)]=_0x31eae5(0x246))),_0x5cf08c();});}function deleteOldBrand(){var _0x234888=a0_0x5f49;console[_0x234888(0x21c)](_0x234888(0x210));var _0x594e94=document['querySelectorAll'](_0x234888(0x219));console[_0x234888(0x21c)](_0x234888(0x1bb),_0x594e94);for(let _0x291588=0x0;_0x291588<_0x594e94['length'];_0x291588++){try{_0x594e94[_0x291588][_0x234888(0x1b4)](_0x234888(0x203))[0x0][_0x234888(0x1c2)]();}catch(_0x3de36d){console[_0x234888(0x21c)](_0x3de36d);}}}function pasteShippingWeight(_0x1bf4c8){return new Promise((_0x2399b8,_0x3b63a3)=>{var _0x17e456=a0_0x5f49;console[_0x17e456(0x21c)]('pasteShippingWeight');if(_0x1bf4c8[_0x17e456(0x225)]){console[_0x17e456(0x21c)]('pasteShippingWeight\x20true');try{var _0x271c72=_0x1bf4c8[_0x17e456(0x225)][_0x17e456(0x260)],_0x3947fe=_0x1bf4c8['shippingWeight'][_0x17e456(0x1d6)];console['log'](_0x17e456(0x220)+_0x271c72),console['log'](_0x17e456(0x25b)+_0x3947fe),_0x3947fe==='g'&&(console[_0x17e456(0x21c)]('g'),document[_0x17e456(0x1ab)](_0x17e456(0x17a))[_0x17e456(0x260)]=_0x271c72),_0x3947fe==='kg'&&(console['log']('kg'),document['querySelector'](_0x17e456(0x20d))[_0x17e456(0x260)]=_0x271c72);}catch(_0x1c366c){console[_0x17e456(0x21c)](_0x1c366c);}}_0x2399b8();});}function pasteDimensions(_0x1901aa){return new Promise((_0xa51394,_0x1cb30c)=>{var _0x8c1cbd=a0_0x5f49,_0x4686cd=_0x1901aa[_0x8c1cbd(0x1ed)];_0x4686cd&&(_0x4686cd[_0x8c1cbd(0x1d6)]==='cm'&&(document[_0x8c1cbd(0x1ab)](_0x8c1cbd(0x1b6))[_0x8c1cbd(0x260)]=_0x4686cd[_0x8c1cbd(0x271)],document['querySelector'](_0x8c1cbd(0x1e7))[_0x8c1cbd(0x260)]=_0x4686cd[_0x8c1cbd(0x256)],document[_0x8c1cbd(0x1ab)](_0x8c1cbd(0x1f7))[_0x8c1cbd(0x260)]=_0x4686cd[_0x8c1cbd(0x1a8)])),_0xa51394();});}function pasteCustomItemSpecific(_0x339669,_0x1c8ffc){return new Promise(_0x15f50c=>{var _0x5ecd39=a0_0x5f49;try{var _0x6cfa25,_0x400a0b=_0x5ecd39(0x273);_0x6cfa25=document['evaluate'](_0x400a0b,document,null,XPathResult[_0x5ecd39(0x15f)],null)[_0x5ecd39(0x21f)],_0x1c8ffc=jsUcfirst(_0x1c8ffc),_0x339669=jsUcfirst(_0x339669),_0x1c8ffc[_0x5ecd39(0x271)]>0x40&&(_0x1c8ffc=_0x1c8ffc[_0x5ecd39(0x1a9)](0x0,0x3e)+_0x5ecd39(0x1d4)),_0x1c8ffc[_0x5ecd39(0x271)]>0x40&&(_0x1c8ffc=_0x1c8ffc[_0x5ecd39(0x1a9)](0x0,0x3e)+_0x5ecd39(0x1d4)),_0x6cfa25[_0x5ecd39(0x1c2)](),document[_0x5ecd39(0x174)](_0x5ecd39(0x1cc))[0x0][_0x5ecd39(0x260)]=_0x339669,document[_0x5ecd39(0x174)](_0x5ecd39(0x22a))[0x0][_0x5ecd39(0x260)]=_0x1c8ffc,$(_0x5ecd39(0x1d9))['is'](_0x5ecd39(0x1be))?($('a[id$=cancle]')[0x0][_0x5ecd39(0x1c2)](),_0x15f50c()):($(_0x5ecd39(0x190))[0x0][_0x5ecd39(0x1c2)](),_0x15f50c());}catch(_0x2caee7){console[_0x5ecd39(0x21c)](_0x2caee7),$(_0x5ecd39(0x26f))[0x0][_0x5ecd39(0x1c2)](),_0x15f50c();}});}async function pasteCustomItemSpecifics(_0x5a866a){var _0x1bfad1=a0_0x5f49,_0x5c2a3b=getExistingFieldNames();console[_0x1bfad1(0x21c)](_0x1bfad1(0x1de),_0x5c2a3b),console[_0x1bfad1(0x21c)](_0x1bfad1(0x164),_0x5a866a),_0x5a866a=_0x5a866a['filter']((_0x29c69a,_0x34dc94,_0x2faadf)=>_0x2faadf[_0x1bfad1(0x1f6)](_0x35352e=>_0x35352e[_0x1bfad1(0x24c)]===_0x29c69a[_0x1bfad1(0x24c)])===_0x34dc94),console[_0x1bfad1(0x21c)](_0x1bfad1(0x164),_0x5a866a);for(var _0x16374e=0x0;_0x16374e<_0x5a866a['length'];_0x16374e++){var _0x4aa2f8=_0x5a866a[_0x16374e],_0x34fcc3=_0x4aa2f8[_0x1bfad1(0x24c)],_0x47777b=_0x4aa2f8['value'];!_0x5c2a3b[_0x1bfad1(0x1a0)](_0x34fcc3)&&await pasteCustomItemSpecific(_0x34fcc3,_0x47777b);}}async function getAllEbayRecommendedItemSpecifics(){var _0x48eab4=a0_0x5f49,_0xa3235b=[],_0x144cbb=document[_0x48eab4(0x174)](_0x48eab4(0x236));for(var _0x5234c8=0x0;_0x5234c8<_0x144cbb[_0x48eab4(0x271)];_0x5234c8++){var _0x1c411d=_0x144cbb[_0x5234c8],_0x4d5b38=_0x1c411d[_0x48eab4(0x249)]('fieldname'),_0x4a819d=_0x1c411d[_0x48eab4(0x260)];_0x4d5b38&&_0x1c411d[_0x48eab4(0x260)]&&(console['log'](_0x48eab4(0x205),_0x4d5b38),_0xa3235b[_0x48eab4(0x1ea)]({'label':_0x4d5b38,'value':_0x4a819d}));}return _0xa3235b;}async function pasteItemSpecificWithLabelAndValue(_0x4b914e,_0x38823d){return new Promise(_0x7d9a25=>{var _0x26ebff=a0_0x5f49;try{var _0x3b41a7=document[_0x26ebff(0x174)](_0x26ebff(0x236));for(var _0x4a93c3=0x0;_0x4a93c3<_0x3b41a7[_0x26ebff(0x271)];_0x4a93c3++){var _0x1f69b9=_0x3b41a7[_0x4a93c3],_0x5cdd13=_0x1f69b9[_0x26ebff(0x249)](_0x26ebff(0x20a)),_0x5224c1=_0x1f69b9[_0x26ebff(0x260)];_0x5cdd13===_0x4b914e&&_0x5224c1===''&&(_0x1f69b9[_0x26ebff(0x260)]=_0x38823d);}_0x7d9a25();}catch(_0x3a2819){console[_0x26ebff(0x21c)](_0x3a2819),_0x7d9a25();}});}function getExistingFieldNames(){var _0x234302=a0_0x5f49,_0x5be805=[],_0x3c81cd=document['querySelectorAll']('[name*=\x27_st_\x27]');for(var _0x41988f=0x0;_0x41988f<_0x3c81cd[_0x234302(0x271)];_0x41988f++){var _0x25b299=_0x3c81cd[_0x41988f],_0x3d3e84=_0x25b299['getAttribute'](_0x234302(0x20a));_0x3d3e84&&_0x5be805[_0x234302(0x1ea)](_0x3d3e84[_0x234302(0x238)]()[_0x234302(0x17c)]());}return _0x5be805=_0x5be805=_0x5be805[_0x234302(0x1d8)]((_0x3cddd6,_0x17ca9c,_0x1e0a31)=>_0x1e0a31[_0x234302(0x1f6)](_0xa21e81=>_0xa21e81===_0x3cddd6)===_0x17ca9c),_0x5be805;}function allowBuyersToStayAnonymous(){return new Promise((_0x1763eb,_0x333ab3)=>{var _0x528bb3=a0_0x5f49;privateAuction=document[_0x528bb3(0x1ab)](_0x528bb3(0x15c)),privateAuction[_0x528bb3(0x1ba)]=!![],_0x1763eb();});}function getBertToken(_0x4a94b7,_0x2c9585,_0x2a5dd7){return new Promise((_0x1ced56,_0x1b40f3)=>{var _0x20b5e1=a0_0x5f49,_0x4dc615='https://api-inference.huggingface.co/models/deepset/roberta-base-squad2',_0x1e34b7={'inputs':{'question':_0x4a94b7,'context':_0x2c9585}};;var _0x53ed1f=_0x20b5e1(0x1b8)+_0x2a5dd7,_0x3cd67e={'Content-Type':_0x20b5e1(0x1ff),'Authorization':_0x53ed1f},_0xf6d1ca={'method':_0x20b5e1(0x15d),'headers':_0x3cd67e,'body':JSON[_0x20b5e1(0x1c9)](_0x1e34b7)};fetch(_0x4dc615,_0xf6d1ca)[_0x20b5e1(0x16e)](_0x387d49=>{var _0x4ab749=_0x20b5e1;return _0x387d49[_0x4ab749(0x25e)]();})[_0x20b5e1(0x16e)](_0x3c86c4=>{console['log'](_0x3c86c4),_0x1ced56(_0x3c86c4);})[_0x20b5e1(0x16c)](_0x49f21f=>{console['log'](_0x49f21f),_0x1b40f3(_0x49f21f);});});}async function getAllRequiredItemSpecificsAndGetAResponse(_0x46de85){var _0x2a48e0=a0_0x5f49,_0x175882=[],_0x413847=document[_0x2a48e0(0x174)](_0x2a48e0(0x21a));for(var _0x5026d5=0x0;_0x5026d5<_0x413847[_0x2a48e0(0x271)];_0x5026d5++){var _0x1c36a5=_0x413847[_0x5026d5],_0x52fc39=_0x1c36a5[_0x2a48e0(0x249)](_0x2a48e0(0x20a));if(_0x52fc39){_0x175882[_0x2a48e0(0x1ea)](_0x52fc39['toLowerCase']()['trim']());var _0x40f2f1=_0x2a48e0(0x233)+_0x52fc39+'?',_0x59efd7=_0x2a48e0(0x16d);console[_0x2a48e0(0x21c)](_0x2a48e0(0x185),_0x40f2f1),console[_0x2a48e0(0x21c)](_0x2a48e0(0x214),_0x46de85);var _0x6bcc59=await getBertToken(_0x40f2f1,_0x46de85,_0x59efd7);console['log'](_0x2a48e0(0x22f),_0x6bcc59);}}return _0x175882;}async function getAllItemSpecificsAndFillDetailsWithMachineLearning(_0x3cd70d){var _0x3d8ef9=a0_0x5f49;console[_0x3d8ef9(0x21c)](_0x3d8ef9(0x15a));var _0x59e16c=[],_0x5a7755=document[_0x3d8ef9(0x174)](_0x3d8ef9(0x236)),_0x221604=getTabooItemSpecifics();_0x221604=_0x221604[_0x3d8ef9(0x192)](function(_0x122bb6){return _0x122bb6['toLowerCase']();}),console[_0x3d8ef9(0x21c)](_0x3d8ef9(0x199),_0x221604);for(var _0x86f981=0x0;_0x86f981<_0x5a7755[_0x3d8ef9(0x271)];_0x86f981++){var _0x382568=_0x5a7755[_0x86f981],_0x16b889=_0x382568[_0x3d8ef9(0x249)](_0x3d8ef9(0x20a)),_0xc69c6c=_0x382568['value'],_0x59a484=![];for(var _0x15913c=0x0;_0x15913c<_0x221604[_0x3d8ef9(0x271)];_0x15913c++){_0x16b889&&_0x16b889[_0x3d8ef9(0x238)]()[_0x3d8ef9(0x1a0)](_0x221604[_0x15913c][_0x3d8ef9(0x238)]())&&_0x221604[_0x15913c][_0x3d8ef9(0x238)]()[_0x3d8ef9(0x1a0)](_0x16b889[_0x3d8ef9(0x238)]())&&(_0x59a484=!![]);}if(!_0x59a484&&_0x16b889&&_0xc69c6c===''){var _0x1d90b1='What\x20is\x20the\x20'+_0x16b889+'?',_0x38f8dd=await sendMessageToBackGroundToAnswerQuestion(_0x1d90b1,_0x3cd70d);console[_0x3d8ef9(0x21c)](_0x1d90b1,_0x38f8dd);if(_0x38f8dd['data'][_0x3d8ef9(0x1a4)]>0.3){console[_0x3d8ef9(0x21c)](_0x3d8ef9(0x226),_0x38f8dd[_0x3d8ef9(0x232)][_0x3d8ef9(0x1a4)]);var _0x582cb6=_0x38f8dd['data'][_0x3d8ef9(0x157)];_0x582cb6=_0x582cb6[_0x3d8ef9(0x22e)](/\n/g,'\x20'),_0x582cb6=_0x582cb6[_0x3d8ef9(0x22e)](/\s{2,}/g,'\x20'),_0x582cb6=_0x582cb6[_0x3d8ef9(0x17c)](),_0x582cb6=_0x582cb6[_0x3d8ef9(0x22e)](/\w\S*/g,function(_0x3cdac6){var _0x583190=_0x3d8ef9;return _0x3cdac6['charAt'](0x0)[_0x583190(0x162)]()+_0x3cdac6['substr'](0x1)[_0x583190(0x238)]();}),await pasteItemSpecificWithLabelAndValue(_0x16b889,_0x582cb6);}}}}function getTabooItemSpecifics(){var _0x5c0865=a0_0x5f49,_0x21447d=[_0x5c0865(0x268),_0x5c0865(0x1fa),_0x5c0865(0x1c7),_0x5c0865(0x1c1),_0x5c0865(0x1e5),'Region',_0x5c0865(0x1a3),_0x5c0865(0x1e1),_0x5c0865(0x270),_0x5c0865(0x267),'Year',_0x5c0865(0x1fb),'Manufacturer\x20Part\x20Number','Warranty',_0x5c0865(0x224),'OE/OEM\x20Part\x20Number','Import\x20designation',_0x5c0865(0x1a1)];return _0x21447d;}function sendMessageToBackGroundToAnswerQuestion(_0x5c6a1b,_0x3e2ad9){return new Promise((_0x341b2a,_0x35cc90)=>{var _0x304c49=a0_0x5f49;chrome['runtime'][_0x304c49(0x1b1)]({'type':'answerQuestion','question':_0x5c6a1b,'context':_0x3e2ad9},function(_0x41d027){_0x341b2a(_0x41d027);});});}function getTotalImagesUploaded(){var _0x3cd66e=a0_0x5f49,_0x5e2e33;_0x5e2e33=document[_0x3cd66e(0x1ab)](_0x3cd66e(0x23a));!_0x5e2e33&&(_0x5e2e33=document[_0x3cd66e(0x1ab)]('.uploader-ui-img-g__header'));!_0x5e2e33&&(_0x5e2e33=document[_0x3cd66e(0x1ab)](_0x3cd66e(0x1fd)));totalImagesUploaded=_0x5e2e33[_0x3cd66e(0x163)]['replace'](_0x3cd66e(0x1ec),''),totalImagesUploaded=parseInt(totalImagesUploaded);if(isNaN(totalImagesUploaded))try{totalImagesUploaded=_0x5e2e33[_0x3cd66e(0x163)][_0x3cd66e(0x177)](/\d+/),totalImagesUploaded=parseInt(totalImagesUploaded);}catch(_0x4f39f8){return totalImagesUploaded;}return totalImagesUploaded;}async function submitTheListing(){var _0x324024=a0_0x5f49,_0x550bfe=getSubmitButton();_0x550bfe[_0x324024(0x1c2)]();}function getSubmitButton(){var _0x47064b=a0_0x5f49,_0x562de5=document['querySelector'](_0x47064b(0x181));scrollIntoView(_0x562de5);var _0x3691aa=_0x562de5[_0x47064b(0x1ab)](_0x47064b(0x25c));return!_0x3691aa&&(_0x3691aa=_0x562de5[_0x47064b(0x1ab)](_0x47064b(0x23d))),_0x3691aa;}function a0_0x5f49(_0x2178e2,_0x539bb7){var _0x5e811=a0_0x5e81();return a0_0x5f49=function(_0x5f497a,_0x1cbee3){_0x5f497a=_0x5f497a-0x156;var _0x5fcf9a=_0x5e811[_0x5f497a];return _0x5fcf9a;},a0_0x5f49(_0x2178e2,_0x539bb7);}async function pastePackageDimensions(){var _0x5b8be8=a0_0x5f49,_0x48a346=_0x5b8be8(0x20e);if(_0x458dd3[_0x5b8be8(0x1a0)](';'))var _0x352d4c=_0x458dd3[_0x5b8be8(0x247)](';'),_0x458dd3=_0x352d4c[0x0],_0x352d4c=_0x458dd3[_0x5b8be8(0x247)]('x'),_0x4a64ef=_0x352d4c[0x0],_0x5647da=_0x352d4c[0x1],_0x56196d=_0x352d4c[0x2];}async function checkIfItemWasSuccessfullyPosted(){var _0x5e967d=a0_0x5f49;for(let _0x14d35f=0x0;_0x14d35f<0x4;_0x14d35f++){await new Promise(_0x3b9e4b=>setTimeout(_0x3b9e4b,0x1388));var _0x1cc3b4=document[_0x5e967d(0x1ab)](_0x5e967d(0x165));if(_0x1cc3b4)return console['log'](_0x5e967d(0x245)),!![];}return console['log'](_0x5e967d(0x22b)),![];}async function getSuccessLink(){var _0x4e8645=a0_0x5f49,_0x1f3e44=document[_0x4e8645(0x1ab)](_0x4e8645(0x1b3)),_0x3598fb=_0x1f3e44[_0x4e8645(0x18c)];return _0x3598fb=_0x3598fb['toString'](),_0x3598fb;}async function selectAllExtractedAspects(){var _0x23678b=a0_0x5f49;const _0x3e056b=document[_0x23678b(0x1ab)](_0x23678b(0x259));if(!_0x3e056b){console[_0x23678b(0x1df)]('Extracted\x20aspects\x20container\x20not\x20found.');return;}const _0xf83df7=_0x3e056b['querySelector']('button.fake-link');if(_0xf83df7){_0xf83df7[_0x23678b(0x1c2)](),console[_0x23678b(0x21c)](_0x23678b(0x263));return;}const _0x35f13b=_0x3e056b[_0x23678b(0x174)](_0x23678b(0x1c6));if(_0x35f13b['length']===0x0){console[_0x23678b(0x1df)](_0x23678b(0x250));return;}_0x35f13b['forEach'](_0x312275=>{var _0x2b3668=_0x23678b;_0x312275['dispatchEvent'](new MouseEvent(_0x2b3668(0x1c2),{'view':window,'bubbles':!![],'cancelable':!![]}));}),console[_0x23678b(0x21c)](_0x35f13b['length']+_0x23678b(0x235));}async function selectCondition(_0x4ea379='New'){var _0x565b2b=a0_0x5f49,_0x8c60f9=document['querySelector'](_0x565b2b(0x1a7));if(!_0x8c60f9){console[_0x565b2b(0x1df)]('Condition\x20recommendation\x20not\x20found.');return;}var _0x4abe34=_0x8c60f9['querySelectorAll'](_0x565b2b(0x203));_0x4ea379===_0x565b2b(0x251)&&_0x4abe34[0x0][_0x565b2b(0x1c2)]();}async function setItemLocation(){var _0x3ce79e=a0_0x5f49,{itemLocation:_0x38ad2a}=await chrome[_0x3ce79e(0x187)][_0x3ce79e(0x1b7)][_0x3ce79e(0x243)](_0x3ce79e(0x1e0)),{itemLocationCountry:_0x22a6a5}=await chrome['storage']['local'][_0x3ce79e(0x243)]('itemLocationCountry'),{itemLocationCityState:_0x478fed}=await chrome[_0x3ce79e(0x187)][_0x3ce79e(0x1b7)][_0x3ce79e(0x243)](_0x3ce79e(0x204));console[_0x3ce79e(0x21c)](_0x3ce79e(0x1e0),_0x38ad2a),console[_0x3ce79e(0x21c)](_0x3ce79e(0x276),_0x22a6a5),console[_0x3ce79e(0x21c)](_0x3ce79e(0x204),_0x478fed);if(!_0x38ad2a||!_0x22a6a5||!_0x478fed){console['error']('Item\x20location\x20not\x20found.');return;}documentTitle=_0x3ce79e(0x218),await postViaNetworkRequest(_0x3ce79e(0x1e0),_0x38ad2a),await waitForNetworkResponseCountIncrease(),documentTitle=_0x3ce79e(0x26d),await postViaNetworkRequest('itemLocationCountry',_0x22a6a5),await waitForNetworkResponseCountIncrease(),documentTitle=_0x3ce79e(0x169),await postViaNetworkRequest(_0x3ce79e(0x204),_0x478fed),await waitForNetworkResponseCountIncrease();}async function setReturnPolicy(){var _0x4bee17=a0_0x5f49,{returnPolicyId:_0x63ee4f}=await chrome[_0x4bee17(0x187)][_0x4bee17(0x1b7)][_0x4bee17(0x243)]('returnPolicyId');console[_0x4bee17(0x21c)](_0x4bee17(0x166),_0x63ee4f);if(!_0x63ee4f){console[_0x4bee17(0x1df)]('Return\x20policy\x20not\x20found.');return;}_0x63ee4f=parseInt(_0x63ee4f),documentTitle='Setting\x20return\x20policy',await postViaNetworkRequest(_0x4bee17(0x209),_0x63ee4f),await waitForNetworkResponseCountIncrease();}async function createItemSpecificsHtmlTable(){var _0x31b36d=a0_0x5f49,_0x4df402=getCombinedItemSpecificFieldProperties();console[_0x31b36d(0x21c)](_0x31b36d(0x20b),_0x4df402);var _0x48d4ab=_0x31b36d(0x179),_0x484286=0x0;_0x48d4ab+=_0x31b36d(0x1d7);for(var _0x4fb9ed in _0x4df402){_0x484286>=0x4&&(_0x48d4ab+='</tr><tr>',_0x484286=0x0);var _0x1f3541=_0x4df402[_0x4fb9ed]['label'],_0x33cb88=_0x4df402[_0x4fb9ed][_0x31b36d(0x1e3)]||_0x31b36d(0x227);_0x48d4ab+=_0x31b36d(0x1c4)+_0x1f3541+':</strong>\x20'+_0x33cb88+_0x31b36d(0x168),_0x484286++;}while(_0x484286<0x4){_0x48d4ab+=_0x31b36d(0x18f),_0x484286++;}return _0x48d4ab+=_0x31b36d(0x269),_0x48d4ab+=_0x31b36d(0x1aa),console[_0x31b36d(0x21c)](_0x31b36d(0x15b),_0x48d4ab),_0x48d4ab;}async function scheduleListing(_0x3b2928=0xf){var _0xaea47d=a0_0x5f49;const _0x14587a=new Date();let _0x4e7f79=new Date(_0x14587a[_0xaea47d(0x18b)](),_0x14587a[_0xaea47d(0x158)](),_0x14587a[_0xaea47d(0x17f)](),_0x3b2928,0x0,0x0);_0x14587a>=_0x4e7f79&&_0x4e7f79[_0xaea47d(0x23e)](_0x4e7f79[_0xaea47d(0x17f)]()+0x1);const _0x5bc859=_0x4e7f79['getFullYear'](),_0x5428ef=_0x4e7f79['getMonth']()+0x1,_0x9b3fdc=_0x4e7f79['getDate'](),_0x1ad9af=0x0,_0x4c160e=0x0,_0x321837={'scheduleYear':_0x5bc859,'scheduleMonth':_0x5428ef,'scheduleDay':_0x9b3fdc,'scheduleHour':_0x3b2928,'scheduleMinute':_0x1ad9af,'scheduleSecond':_0x4c160e,'scheduleSelection':!![]};await postObjectViaNetworkRequest(_0x321837),await waitForNetworkResponseCountIncrease();}async function setGSPR(_0x58e837,_0x1ef7df){var _0x10ee8c=a0_0x5f49,_0x17d139=![];function _0x5703cb(_0x31f769){var _0x28eaf9=a0_0x5f49;return Object['fromEntries'](Object[_0x28eaf9(0x264)](_0x31f769)[_0x28eaf9(0x1d8)](([_0x5382c7,_0x348a7a])=>_0x348a7a!=null));}const _0x44b76d=_0x5703cb({'address1':_0x1ef7df[_0x10ee8c(0x200)],'address2':_0x1ef7df[_0x10ee8c(0x161)],'city':_0x1ef7df[_0x10ee8c(0x25d)],'country':_0x1ef7df['country'],'emailAddress':_0x1ef7df[_0x10ee8c(0x191)],'name':_0x1ef7df[_0x10ee8c(0x1c0)],'phoneNumber':_0x1ef7df[_0x10ee8c(0x1cd)],'postalCode':_0x1ef7df[_0x10ee8c(0x1c5)],'stateOrProvince':_0x1ef7df[_0x10ee8c(0x23b)]}),_0x12f17f=_0x5703cb({'address1':_0x58e837['address1'],'address2':_0x58e837[_0x10ee8c(0x161)],'city':_0x58e837[_0x10ee8c(0x25d)],'country':_0x58e837[_0x10ee8c(0x244)],'emailAddress':_0x58e837[_0x10ee8c(0x191)],'name':_0x58e837['name'],'phoneNumber':_0x58e837[_0x10ee8c(0x1cd)],'postalCode':_0x58e837[_0x10ee8c(0x1c5)],'stateOrProvince':_0x58e837[_0x10ee8c(0x23b)]}),_0x436c62={'euResponsiblePersons':[_0x44b76d],'productManufacturer':_0x12f17f};console[_0x10ee8c(0x21c)](_0x10ee8c(0x212),_0x436c62);var _0x310103=document[_0x10ee8c(0x1ab)](_0x10ee8c(0x216));try{if(_0x310103[_0x10ee8c(0x1ba)])return console['log'](_0x10ee8c(0x211)),_0x17d139=!![],_0x17d139;}catch(_0x315dd4){return _0x17d139=![],_0x17d139;}try{_0x310103['click']();}catch(_0x38f9ec){return _0x17d139=![],_0x17d139;}return await postObjectViaNetworkRequest(_0x436c62),await waitForNetworkResponseCountIncrease(),_0x17d139=!![],_0x17d139;}async function createGsprSection(_0x18de75,_0x5f5dea){var _0x163280=a0_0x5f49;return'\x0a\x09\x20\x20<div\x20style=\x22border:1px\x20solid\x20#ccc;\x20padding:10px;\x20margin-top:10px;\x22>\x0a\x09\x09<h3>EU\x20Compliance\x20Information</h3>\x0a\x09\x09<h4>Manufacturer:</h4>\x0a\x09\x09<p><strong>Name:</strong>\x20'+(_0x18de75[_0x163280(0x1c0)]||_0x163280(0x241))+'</p>\x0a\x09\x09<p><strong>Address:</strong>\x20'+(_0x18de75[_0x163280(0x200)]||'')+'\x20'+(_0x18de75[_0x163280(0x161)]||'')+_0x163280(0x24f)+(_0x18de75[_0x163280(0x25d)]||'')+',\x20'+(_0x18de75['postalCode']||'')+',\x20'+(_0x18de75[_0x163280(0x244)]||'')+_0x163280(0x17b)+(_0x5f5dea[_0x163280(0x1c0)]||_0x163280(0x241))+_0x163280(0x19d)+(_0x5f5dea['address1']||'')+'\x20'+(_0x5f5dea[_0x163280(0x161)]||'')+_0x163280(0x24f)+(_0x5f5dea[_0x163280(0x25d)]||'')+',\x20'+(_0x5f5dea[_0x163280(0x1c5)]||'')+',\x20'+(_0x5f5dea['country']||'')+_0x163280(0x188);}async function createGsprSectionFromText(_0x186363,_0x1751d2){var _0x4d370d=a0_0x5f49;return _0x4d370d(0x240)+(_0x186363||'N/A')+_0x4d370d(0x23f)+(_0x1751d2||'N/A')+'</pre>\x0a\x09\x20\x20</div>\x0a\x09';}async function setGSPRWithClicks(_0xa4f483,_0x5b2f4b,_0xa90bd5,_0x20c016){var _0xc73698=a0_0x5f49,_0x25364e=document[_0xc73698(0x1ab)]('input[name=\x22productContactInformation\x22]');if(!_0x25364e)return console[_0xc73698(0x1df)]('Regulatory\x20compliance\x20switch\x20not\x20found.'),![];if(_0x25364e['checked'])return console[_0xc73698(0x21c)](_0xc73698(0x211)),!![];await clickRegulatoryComplianceSwitch(),await fillProductManufacturer(_0xa4f483),await clickContactListAddButton(),await fillEUResponsiblePerson(_0x5b2f4b),await saveGSPR();var _0x303130=getSubmitButton();scrollIntoView(_0x303130),_0x303130[_0xc73698(0x1c2)]();var _0x29b57a=await checkIfItemWasSuccessfullyPosted();return _0x29b57a;}async function saveGSPR(){var _0xfd78f7=a0_0x5f49,_0xad50=document[_0xfd78f7(0x1ab)](_0xfd78f7(0x275));_0xad50['click']();var _0x5a84df=document['querySelector'](_0xfd78f7(0x221));while(!_0x5a84df){await new Promise(_0xc93076=>setTimeout(_0xc93076,0x3e8)),_0x5a84df=document[_0xfd78f7(0x1ab)](_0xfd78f7(0x221));}_0x5a84df[_0xfd78f7(0x1c2)]();}async function clickContactListAddButton(){var _0x54e1c0=a0_0x5f49,_0x3f5908=document[_0x54e1c0(0x1ab)](_0x54e1c0(0x1af));_0x3f5908['click']();var _0x1ead09=document['querySelector'](_0x54e1c0(0x1ca));while(!_0x1ead09){await new Promise(_0x119a7f=>setTimeout(_0x119a7f,0x3e8)),_0x1ead09=document[_0x54e1c0(0x1ab)]('.details__contact-info.details__euResponsiblePersons');}return _0x1ead09;}async function clickRegulatoryComplianceSwitch(){var _0x40eb8e=a0_0x5f49,_0x535e4a=document[_0x40eb8e(0x1ab)](_0x40eb8e(0x216));_0x535e4a['click']();var _0x1cc75e=document[_0x40eb8e(0x1ab)](_0x40eb8e(0x18e));while(!_0x1cc75e){await new Promise(_0x2211dc=>setTimeout(_0x2211dc,0x3e8)),_0x1cc75e=document[_0x40eb8e(0x1ab)]('.product-manufacturer__add-button');}_0x1cc75e[_0x40eb8e(0x1c2)]();}async function fillProductManufacturer(_0x252029){var _0x3b2d0f=a0_0x5f49;_0x252029={'name':'TACVASEN-EU','country':'ES','address1':_0x3b2d0f(0x198),'address2':'CASTELLANA\x209144,\x2028046\x20Madrid','city':_0x3b2d0f(0x254),'postalCode':_0x3b2d0f(0x22d),'stateOrProvince':null,'phoneNumber':_0x3b2d0f(0x20f),'emailAddress':_0x3b2d0f(0x1cf),'website':null},console['log'](_0x3b2d0f(0x178),_0x252029);function _0x2618fb(_0x22d314,_0x3334c2){var _0x4ac62f=_0x3b2d0f;if(!_0x22d314)return;_0x22d314[_0x4ac62f(0x260)]=_0x3334c2||'',_0x22d314['dispatchEvent'](new Event(_0x4ac62f(0x160),{'bubbles':!![],'cancelable':!![]})),_0x22d314[_0x4ac62f(0x167)](new Event(_0x4ac62f(0x171),{'bubbles':!![],'cancelable':!![]}));}async function _0x48d771(_0x598a7e){var _0x13ff48=_0x3b2d0f;const _0x2b51c4=document['querySelector'](_0x13ff48(0x1c3));if(!_0x2b51c4){console[_0x13ff48(0x1df)](_0x13ff48(0x237));return;}const _0x38bd93=Array[_0x13ff48(0x1f8)](_0x2b51c4[_0x13ff48(0x19b)]),_0x30bc5d=_0x38bd93['find'](_0x213133=>_0x213133[_0x13ff48(0x260)]===_0x598a7e);if(!_0x30bc5d){console[_0x13ff48(0x1df)](_0x13ff48(0x1e6)+_0x598a7e);return;}const _0x18eca1=_0x38bd93['indexOf'](_0x30bc5d);if(_0x18eca1<0x0){console[_0x13ff48(0x1df)](_0x13ff48(0x234)+_0x598a7e);return;}const _0x1a5792=document[_0x13ff48(0x1ab)]('.country\x20.listbox-button__control');if(!_0x1a5792){console['error'](_0x13ff48(0x186));return;}_0x1a5792[_0x13ff48(0x1c2)](),await new Promise(_0x3feeec=>setTimeout(_0x3feeec,0xc8));const _0x2621c9=document['querySelectorAll'](_0x13ff48(0x230));if(_0x18eca1<_0x2621c9['length']){const _0xce8a77=_0x2621c9[_0x18eca1];_0xce8a77['click']();}else console[_0x13ff48(0x1df)](_0x13ff48(0x229)+_0x18eca1);}try{const _0x51e15f=document['querySelector'](_0x3b2d0f(0x201));_0x2618fb(_0x51e15f,_0x252029['name']),_0x48d771(_0x252029[_0x3b2d0f(0x244)]);const _0x1d9d10=document[_0x3b2d0f(0x1ab)]('input[name=\x22address1\x22]');_0x2618fb(_0x1d9d10,_0x252029[_0x3b2d0f(0x200)]);const _0x381fb5=document[_0x3b2d0f(0x1ab)](_0x3b2d0f(0x1a2));_0x2618fb(_0x381fb5,_0x252029[_0x3b2d0f(0x161)]);const _0x16274c=document['querySelector']('input[name=\x22city\x22]');_0x2618fb(_0x16274c,_0x252029['city']);const _0x35467a=document[_0x3b2d0f(0x1ab)]('input[name=\x22postalCode\x22]');_0x2618fb(_0x35467a,_0x252029[_0x3b2d0f(0x1c5)]);const _0x146713=document['querySelector']('input[name=\x22stateOrProvince\x22]');_0x2618fb(_0x146713,_0x252029['stateOrProvince']);const _0x33691c=document['querySelector']('input[name=\x22phoneNumber\x22]');_0x2618fb(_0x33691c,_0x252029['phoneNumber']);const _0xdfe16=document['querySelector'](_0x3b2d0f(0x222));_0x2618fb(_0xdfe16,_0x252029['emailAddress']),console['log'](_0x3b2d0f(0x239));}catch(_0xceb24b){console[_0x3b2d0f(0x1df)](_0x3b2d0f(0x1e9),_0xceb24b);}}async function fillEUResponsiblePerson(_0x4a4672){var _0x487130=a0_0x5f49,_0x4a4672={'name':'John\x20Doe','country':'ES','address1':'Some\x20Street\x20123','address2':_0x487130(0x20c),'city':_0x487130(0x223),'postalCode':_0x487130(0x24e),'stateOrProvince':_0x487130(0x21e),'phoneNumber':_0x487130(0x207),'emailAddress':_0x487130(0x156)};function _0x48fe22(_0x27afdf,_0x970d2a){var _0x4ce27a=_0x487130;if(!_0x27afdf)return;_0x27afdf[_0x4ce27a(0x260)]=_0x970d2a||'',_0x27afdf[_0x4ce27a(0x167)](new Event(_0x4ce27a(0x160),{'bubbles':!![],'cancelable':!![]})),_0x27afdf[_0x4ce27a(0x167)](new Event(_0x4ce27a(0x171),{'bubbles':!![],'cancelable':!![]}));}async function _0x4374b3(_0x19acba){var _0x27010c=_0x487130;const _0x2af607=document[_0x27010c(0x1ab)]('.details__euResponsiblePersons\x20select[name=\x22country\x22]');if(!_0x2af607){console['error']('No\x20EU\x20Responsible\x20Person\x20country\x20select\x20found');return;}const _0x572c2f=Array[_0x27010c(0x1f8)](_0x2af607[_0x27010c(0x19b)]),_0x80e016=_0x572c2f[_0x27010c(0x255)](_0x4c4f80=>_0x4c4f80[_0x27010c(0x260)]===_0x19acba);if(!_0x80e016){console[_0x27010c(0x1df)](_0x27010c(0x1e6)+_0x19acba);return;}const _0x4e8550=_0x572c2f['indexOf'](_0x80e016);if(_0x4e8550<0x0){console['error'](_0x27010c(0x234)+_0x19acba);return;}const _0x1c7103=document['querySelector']('.details__euResponsiblePersons\x20.country\x20.listbox-button__control');if(!_0x1c7103){console[_0x27010c(0x1df)]('No\x20EU\x20Responsible\x20Person\x20country\x20dropdown\x20button\x20found');return;}_0x1c7103[_0x27010c(0x1c2)](),await new Promise(_0x51ed3f=>setTimeout(_0x51ed3f,0xc8));const _0x42d1e4=document[_0x27010c(0x174)](_0x27010c(0x24d));_0x4e8550<_0x42d1e4[_0x27010c(0x271)]?_0x42d1e4[_0x4e8550][_0x27010c(0x1c2)]():console['error'](_0x27010c(0x229)+_0x4e8550);}try{_0x48fe22(document[_0x487130(0x1ab)]('.details__euResponsiblePersons\x20input[name=\x22name\x22]'),_0x4a4672['name']),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x1b0)),_0x4a4672[_0x487130(0x200)]),_0x48fe22(document[_0x487130(0x1ab)]('.details__euResponsiblePersons\x20input[name=\x22address2\x22]'),_0x4a4672[_0x487130(0x161)]),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x193)),_0x4a4672[_0x487130(0x25d)]),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x1bf)),_0x4a4672['postalCode']),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x182)),_0x4a4672[_0x487130(0x23b)]),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x1dd)),_0x4a4672['phoneNumber']),_0x48fe22(document[_0x487130(0x1ab)](_0x487130(0x215)),_0x4a4672['emailAddress']),_0x4a4672['country']&&await _0x4374b3(_0x4a4672['country']),console['log']('EU\x20Responsible\x20Person\x20fields\x20populated\x20successfully.');}catch(_0x4c0071){console[_0x487130(0x1df)](_0x487130(0x23c),_0x4c0071);}}