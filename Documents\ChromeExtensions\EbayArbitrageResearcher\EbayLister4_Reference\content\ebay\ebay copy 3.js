var a0_0x5164c5=a0_0x166d;(function(_0x276589,_0x4b6f6e){var _0xdf2919=a0_0x166d,_0x13a8b7=_0x276589();while(!![]){try{var _0xe30e5a=-parseInt(_0xdf2919(0x169))/0x1*(parseInt(_0xdf2919(0x17a))/0x2)+-parseInt(_0xdf2919(0x16c))/0x3*(-parseInt(_0xdf2919(0x11b))/0x4)+parseInt(_0xdf2919(0x122))/0x5+-parseInt(_0xdf2919(0x145))/0x6+parseInt(_0xdf2919(0x12c))/0x7*(-parseInt(_0xdf2919(0xf5))/0x8)+-parseInt(_0xdf2919(0x168))/0x9*(-parseInt(_0xdf2919(0x164))/0xa)+-parseInt(_0xdf2919(0x13f))/0xb*(-parseInt(_0xdf2919(0x137))/0xc);if(_0xe30e5a===_0x4b6f6e)break;else _0x13a8b7['push'](_0x13a8b7['shift']());}catch(_0x2b5e2c){_0x13a8b7['push'](_0x13a8b7['shift']());}}}(a0_0x1802,0xe7a43),console['log'](a0_0x5164c5(0x183)));var startedListing=![],totalNetworkResponse=0x0,previousTotalNetworkResponse=0x0,itemSpecificPrompt='',itemSpecificPromptResponse='',previousTabId,previousWindowId,receivedDescriptionNetworkResponse=![],documentTitle=a0_0x5164c5(0x157),isScriptCompleted=![],requestHeaders=[];chrome[a0_0x5164c5(0x10e)]['onMessage'][a0_0x5164c5(0x113)](async(_0x252899,_0xd95049,_0x441865)=>{var _0x459325=a0_0x5164c5;_0x252899[_0x459325(0x124)]===_0x459325(0x15a)&&startedListing===![]&&(console['log'](_0x459325(0x109)),startedListing=!![],console['log'](_0x459325(0x161),_0x252899),await waitForPageToLoad(),setInterval(function(){var _0x4e86d5=_0x459325;!isScriptCompleted?document[_0x4e86d5(0xee)]=documentTitle:clearInterval();},0x64),postEbayData(_0x252899[_0x459325(0xec)])),_0x252899['type']===_0x459325(0x125)&&(console[_0x459325(0x16f)]('networkResponse',_0x252899[_0x459325(0x14e)]),previousTotalNetworkResponse=totalNetworkResponse,totalNetworkResponse++),_0x252899[_0x459325(0x124)]==='ebay_description_network_response'&&(console['log']('ebay_description_network_response',_0x252899['details']),receivedDescriptionNetworkResponse=!![]),_0x252899[_0x459325(0x124)]===_0x459325(0x172)&&(console[_0x459325(0x16f)]('received\x20message\x20requestVariables',_0x252899),requestHeaders=_0x252899[_0x459325(0x175)]);});function getHeader(_0x450539,_0x4e412c){var _0x445b25=a0_0x5164c5;for(var _0x1892e8=0x0;_0x1892e8<_0x4e412c[_0x445b25(0x171)];_0x1892e8++){if(_0x4e412c[_0x1892e8][_0x445b25(0xeb)]===_0x450539)return _0x4e412c[_0x1892e8];}return null;}async function postViaNetworkRequest(_0x488763,_0x635278){var _0x3ffd64=a0_0x5164c5,_0x3848e1=new Headers();for(var _0x422225=0x0;_0x422225<requestHeaders[_0x3ffd64(0x171)];_0x422225++){var _0xceda07=requestHeaders[_0x422225];_0x3848e1['append'](_0xceda07[_0x3ffd64(0xeb)],_0xceda07[_0x3ffd64(0xf0)]);}var _0x5be466=new URLSearchParams(new URL(window['location'][_0x3ffd64(0x163)])[_0x3ffd64(0x14d)]),_0x50a087=_0x5be466[_0x3ffd64(0x153)](_0x3ffd64(0x120)),_0xe5ef3=window['location'][_0x3ffd64(0x163)][_0x3ffd64(0x187)]('/')[0x2],_0x72b09b='https://'+_0xe5ef3+'/lstng/api/listing_draft/'+_0x50a087+_0x3ffd64(0x142),_0x5b4cc8=_0x3ffd64(0x116),_0x28b884={[_0x488763]:_0x635278},_0x1f54d7=new TextEncoder()[_0x3ffd64(0x15e)](JSON[_0x3ffd64(0x104)](_0x28b884));try{const _0x52ecd0=await fetch(_0x72b09b,{'method':_0x5b4cc8,'headers':_0x3848e1,'body':_0x1f54d7});console[_0x3ffd64(0x16f)]('Response:',_0x52ecd0);}catch(_0x599a54){console['error'](_0x3ffd64(0x10a),_0x599a54);}}function waitForRequestHeaders(){return new Promise((_0xc16b93,_0x5a7534)=>{var _0x4f8a6b=setInterval(()=>{var _0x2c430f=a0_0x166d;requestHeaders[_0x2c430f(0x171)]>0x0&&(clearInterval(_0x4f8a6b),_0xc16b93());},0x3e8);});}function parseCookies(_0x53742d){var _0x11f841=a0_0x5164c5,_0xfdce0f=[],_0x300ba9=_0x53742d['split'](';');for(var _0x27ab20=0x0;_0x27ab20<_0x300ba9[_0x11f841(0x171)];_0x27ab20++){var _0x2725a8=_0x300ba9[_0x27ab20],_0x4cd445=_0x2725a8[_0x11f841(0x187)]('='),_0x4a47af=_0x4cd445[0x0],_0x2f09b7=_0x4cd445[0x1],_0x57d9ac={'name':_0x4a47af,'value':_0x2f09b7};_0xfdce0f['push'](_0x57d9ac);}return _0xfdce0f;}function waitForPageToLoad(){return new Promise((_0x111b84,_0x14bdf5)=>{var _0x1aca05=setInterval(()=>{var _0x1d3797=a0_0x166d;document[_0x1d3797(0x126)]===_0x1d3797(0x188)&&(clearInterval(_0x1aca05),_0x111b84());},0x3e8);});}testDraft();async function testDraft(){var _0x234513=a0_0x5164c5;window[_0x234513(0x144)][_0x234513(0x163)][_0x234513(0x14f)](_0x234513(0x120))&&chrome['storage'][_0x234513(0x162)][_0x234513(0x153)](_0x234513(0x16d),async function(_0x16f6c2){var _0x8eae01=_0x234513,_0x51e278=_0x16f6c2[_0x8eae01(0x16d)];console[_0x8eae01(0x16f)](_0x8eae01(0x178),_0x51e278),await waitForPageToLoad(),setInterval(function(){var _0x2941cb=_0x8eae01;document[_0x2941cb(0xee)]=documentTitle;},0x32),console[_0x8eae01(0x16f)]('0'),await pasteTitle(_0x51e278[_0x8eae01(0x121)]),documentTitle='Title\x20Pasted',await fillItemSpecificValuesWithAiV5(_0x51e278,_0x8eae01(0x118));});}async function postEbayData(_0x2e8ad2){var _0x4af7c1=a0_0x5164c5;changeFaviconToTaskBar(),console[_0x4af7c1(0x16f)](_0x2e8ad2),await uploadImageConfig(_0x2e8ad2),console[_0x4af7c1(0x16f)]('0'),documentTitle=_0x4af7c1(0x10b),await clickShowMoreButton(),documentTitle=_0x4af7c1(0x189),await pasteTitle(_0x2e8ad2[_0x4af7c1(0x121)]),console['log'](_0x4af7c1(0x12e)),await waitForNetworkResponseCountIncrease(),console[_0x4af7c1(0x16f)]('\x20waitForNetworkResponseCountIncrease\x20Done'),await waitForRequestHeaders(),console[_0x4af7c1(0x16f)](_0x4af7c1(0x158)),documentTitle=_0x4af7c1(0xf1),await clickShowMoreButton();(_0x2e8ad2[_0x4af7c1(0x174)]=='paid'||_0x2e8ad2[_0x4af7c1(0x174)]=='paid2')&&(await fillItemSpecificValuesWithAiV5(_0x2e8ad2,_0x4af7c1(0x118)),await clickShowMoreButton(),await fillItemSpecificValuesWithAiV5(_0x2e8ad2,_0x4af7c1(0x16b)));documentTitle='Adding\x20UPC';if(_0x2e8ad2[_0x4af7c1(0x18a)]){var _0x332ea8=capitalizeFirstLetterOfEachWordInItemSpecifics(_0x2e8ad2[_0x4af7c1(0x15b)]);console['log']('filteredItemSpecifics',_0x332ea8),await fillEmptyItemSpecifics(_0x332ea8);}await fillEmptyRequiredItemSpecificsV2(_0x4af7c1(0x128)),console[_0x4af7c1(0x16f)]('1'),console[_0x4af7c1(0x16f)]('2'),documentTitle=_0x4af7c1(0x112);var _0x4cc6e0=_0x2e8ad2[_0x4af7c1(0xf4)];await pastePrice(_0x4cc6e0),console['log']('4'),console[_0x4af7c1(0x16f)]('5'),documentTitle='Adding\x20The\x20SKU';if(_0x2e8ad2['listingType']==_0x4af7c1(0x13a)||_0x2e8ad2[_0x4af7c1(0x174)]==_0x4af7c1(0x11d))try{await pasteSKU(btoa(_0x2e8ad2['sku']));}catch(_0x381372){console[_0x4af7c1(0x16f)](_0x4af7c1(0xed));}console[_0x4af7c1(0x16f)]('6'),console['log']('7'),console['log']('7.1');try{await fillSingleSelectItemSpecific(_0x4af7c1(0x13e),_0x4af7c1(0x12a));}catch(_0x514773){console[_0x4af7c1(0x16f)]('error\x20pasting\x20brand');}console['log'](_0x4af7c1(0x108)),documentTitle=_0x4af7c1(0x186);if(_0x2e8ad2[_0x4af7c1(0x174)]==_0x4af7c1(0x11d)){await pasteDescription(_0x2e8ad2);var _0x571463=await getBasicDescription(_0x2e8ad2);await addHtmlToEbayDescription(_0x571463),await postViaNetworkRequest(_0x4af7c1(0x123),_0x571463),await waitForNetworkResponseCountIncrease();}else{if(_0x2e8ad2[_0x4af7c1(0x174)]=='paid'){var _0x1cef91=await getDescriptionHtmlFromOpenAi(_0x2e8ad2),_0x39374b=await createDescriptionFromTemplate2023(_0x1cef91,_0x2e8ad2);await addHtmlToEbayDescription(_0x39374b),await postViaNetworkRequest(_0x4af7c1(0x123),_0x39374b),await waitForNetworkResponseCountIncrease();}else{if(_0x2e8ad2[_0x4af7c1(0x174)]=='paid2'){var _0x1cef91=await getDescriptionHtmlFromOpenAi(_0x2e8ad2),_0x39374b=await createDescriptionFromTemplate2023(_0x1cef91,_0x2e8ad2);await addHtmlToEbayDescription(_0x39374b),await postViaNetworkRequest('description',_0x39374b),await waitForNetworkResponseCountIncrease();}}}console[_0x4af7c1(0x16f)](_0x4af7c1(0x131));var _0x36b9d0=document[_0x4af7c1(0x117)](_0x4af7c1(0x184));scrollIntoView(_0x36b9d0),documentTitle=_0x4af7c1(0x12b);var _0x2e8c87=document[_0x4af7c1(0x117)](_0x4af7c1(0x135));_0x2e8c87&&(await postViaNetworkRequest(_0x4af7c1(0xf6),!![]),await waitForNetworkResponseCountIncrease(),await postViaNetworkRequest(_0x4af7c1(0x127),_0x4af7c1(0xe9)),await waitForNetworkResponseCountIncrease());documentTitle=_0x4af7c1(0x173),await clickShowMoreButton();var {domain:_0x478a21}=await chrome[_0x4af7c1(0x143)][_0x4af7c1(0x162)]['get']('domain');if(_0x478a21==_0x4af7c1(0xf9)){await postViaNetworkRequest(_0x4af7c1(0x12f),_0x4af7c1(0x15f)),await waitForNetworkResponseCountIncrease();var _0x399736={'EAN':[_0x4af7c1(0x165)]};await postViaNetworkRequest(_0x4af7c1(0x170),_0x399736),await waitForNetworkResponseCountIncrease();}else{await postViaNetworkRequest('universalProductCode',_0x4af7c1(0x15f)),await waitForNetworkResponseCountIncrease();var _0x399736={'UPC':[_0x4af7c1(0x165)]};await postViaNetworkRequest(_0x4af7c1(0x170),_0x399736),await waitForNetworkResponseCountIncrease();}console[_0x4af7c1(0x16f)]('Ready\x20To\x20Submit!'),documentTitle=_0x4af7c1(0x134),addSubmitButtonListener(_0x2e8ad2);var {autoSubmitEnabled:_0x4ba940}=await chrome[_0x4af7c1(0x143)][_0x4af7c1(0x162)]['get'](_0x4af7c1(0xfd));_0x4ba940&&await submitTheListing();}function addSubmitButtonListener(_0x4af1ac){var _0x280c27=a0_0x5164c5,_0x44523e=getSubmitButton();console['log'](_0x280c27(0x182),_0x44523e),_0x44523e[_0x280c27(0xf7)]('click',async function(){var _0x37d877=_0x280c27;console[_0x37d877(0x16f)]('submitButton\x20clicked');var _0x2148d5=await checkIfItemWasSuccessfullyPosted(),_0x240fdc='';try{_0x240fdc=await getSuccessLink();}catch(_0xce9367){}if(_0x2148d5)chrome[_0x37d877(0x10e)][_0x37d877(0x119)]({'type':_0x37d877(0x16e),'sku':_0x4af1ac[_0x37d877(0x17f)],'message':_0x37d877(0x10f),'ebayItemLink':_0x240fdc}),changeFaviconOfPage(_0x37d877(0x17d)),documentTitle=_0x37d877(0xf8),addSkuToSkuListAndSaveToLocalStorage(_0x4af1ac[_0x37d877(0x17f)]);else{var _0x14737b=getPageErrorMessage();console[_0x37d877(0x16f)](_0x37d877(0x14c),_0x14737b),!_0x14737b&&(_0x14737b=_0x37d877(0x159)+window[_0x37d877(0x144)][_0x37d877(0x163)]),chrome[_0x37d877(0x10e)][_0x37d877(0x119)]({'type':'itemFailed','sku':_0x4af1ac[_0x37d877(0x17f)],'message':_0x14737b}),changeFaviconOfPage('https://www.freeiconspng.com/uploads/error-icon-4.png'),documentTitle=_0x37d877(0x16a);}});}function getPageErrorMessage(){var _0x34b9a7=a0_0x5164c5,_0x21f4c3=document[_0x34b9a7(0x117)](_0x34b9a7(0x15d));if(_0x21f4c3){var _0x14fe14=_0x21f4c3[_0x34b9a7(0x149)]+'\x20'+window[_0x34b9a7(0x144)]['href'];return _0x14fe14;}else return null;}async function uploadImageConfig(_0x502d22){var _0x57e5eb=a0_0x5164c5,_0x25978a=[],_0x4a155f=_0x502d22[_0x57e5eb(0x106)],_0x1558f8=_0x502d22[_0x57e5eb(0x13b)];console['log'](_0x57e5eb(0x101),_0x4a155f[_0x57e5eb(0x171)]),console[_0x57e5eb(0x16f)](_0x57e5eb(0x151),_0x1558f8[_0x57e5eb(0x171)]),console[_0x57e5eb(0x16f)](_0x57e5eb(0xfe),typeof _0x4a155f),console[_0x57e5eb(0x16f)](_0x57e5eb(0x147),typeof _0x1558f8),_0x25978a=_0x4a155f;_0x4a155f['length']<0x2&&_0x1558f8[_0x57e5eb(0x171)]>0x2?_0x25978a=_0x1558f8:_0x25978a=_0x4a155f;var _0x562216=_0x502d22[_0x57e5eb(0x121)];documentTitle=_0x57e5eb(0xff);var _0x22cfad=_0x502d22[_0x57e5eb(0x166)];console[_0x57e5eb(0x16f)](_0x57e5eb(0x146)),await uploadImageAndWaitForCounterToUpload(_0x22cfad,_0x562216[_0x57e5eb(0x103)](0x0,0x64)+'-'+0x1+_0x57e5eb(0xe8),IMAGES_TYPES[_0x57e5eb(0x110)]),await waitForNetworkResponseCountIncrease(),console[_0x57e5eb(0x16f)]('uploading\x20other\x20images'),console[_0x57e5eb(0x16f)](_0x57e5eb(0xfc),_0x25978a[_0x57e5eb(0x171)]),documentTitle='Uploading\x20Images',await pasteImages(_0x25978a,_0x562216,_0x502d22),documentTitle='Done\x20Uploading\x20Images';}async function waitForDescriptionNetworkResponse(){return new Promise((_0x3d40a2,_0x33425f)=>{async function _0x1b00c5(){var _0x459146=a0_0x166d;receivedDescriptionNetworkResponse===!![]?(documentTitle='Received\x20Description\x20Network\x20Response',console[_0x459146(0x16f)]('receivedDescriptionNetworkResponse\x20received'),_0x3d40a2()):(documentTitle=_0x459146(0x141),setTimeout(async()=>{await _0x1b00c5();},0x64));}_0x1b00c5();});}async function waitForNetworkResponseCountIncrease(){let _0x1e2c73=totalNetworkResponse;return new Promise((_0x246922,_0x44ea83)=>{async function _0x31c694(_0x21a054=0x0){var _0x2b5d8e=a0_0x166d;totalNetworkResponse>_0x1e2c73?(waitSomeTime(0x64),_0x246922()):(_0x21a054++,_0x21a054%0x3c===0x0&&(console[_0x2b5d8e(0x16f)](_0x2b5d8e(0x180),_0x21a054),console[_0x2b5d8e(0x16f)](_0x2b5d8e(0x132)),await makeTabActive()),setTimeout(async()=>{await _0x31c694(_0x21a054);},0x64)),_0x21a054>0x3c&&(totalNetworkResponse=_0x1e2c73+0x1,_0x246922());}_0x31c694();});}function makeTabActive(){return new Promise((_0x4dae75,_0x24725f)=>{var _0xbc3443=a0_0x166d;chrome[_0xbc3443(0x10e)][_0xbc3443(0x119)]({'type':_0xbc3443(0x17b)},async function(_0x8117fe){var _0x2aba12=_0xbc3443;previousTabId=_0x8117fe[_0x2aba12(0x11e)],previousWindowId=_0x8117fe['previousWindowId'];var _0x398f8d=_0x2aba12(0x140),_0x852d7e=document[_0x2aba12(0x117)](_0x398f8d);await waitSomeTime(0xc8),_0x4dae75();});});}function a0_0x1802(){var _0x558a95=['PUT','querySelector','required','sendMessage','send_message_to_promoted_listing_to_click_promoted_listing_button','12EIeGFc','previousWindowId:\x20','free','previousTabId','pageLoaded_for_promoted_listing','draftId','custom_title','5433980HYpIiO','description','type','networkResponse','readyState','adRate','Details\x20In\x20Description','pasteTitle','Unbranded','Promoting\x20Listing','14YvWDxP','visibility','Paste\x20Title\x20Done','europeanArticleNumber','.smry.summary__photos','7.3','make\x20tab\x20active','data','Ready\x20To\x20Submit!','input[name=\x22promotedListingSelection\x22]','.summary__promoted-listing','14815536AwrHGO','center','success_msg','paid','main_sd_images','button[innerHTML=\x27','message','Brand','11hDcJYE','.smry.summary__title\x20input[name=\x27title\x27]','Waiting\x20For\x20Description\x20Network\x20Response','?mode=AddItem&forceValidation=true','storage','location','7930530pXucvU','uploading\x20selected\x20image','mainSdImages\x20type','pastePrice','textContent','response:\x20','previousTabId:\x20','pageErrorMessage','search','details','includes','style','mainSdImages','price','get','button','pageLoaded','visible','Inserted\x20Script','waitForRequestHeaders\x20Done','Item\x20Failed\x20to\x20List!\x20','insert_ebay_data','filteredItemSpecifics','round','.page-notice__main','encode','Does\x20Not\x20Apply','#fehelix-uploader','request','local','href','10uVwNiK','Does\x20not\x20apply','selected_image','picUploadUpdate','14977782EOHqbu','2117wsLMfq','Failed\x20to\x20Submit\x20the\x20Listing!','optional','809355XwfKvn','amazon','itemListed','log','attributes','length','requestVariables','Adding\x20UPC','listingType','requestHeaders','getElementById','scrollIntoView','DraftURL\x20product','actionbar','1762uKxRqj','makeTabActive','receive\x20pageLoaded','https://favicon-generator.org/favicon-generator/htdocs/favicons/2014-12-02/b14c9dab43e1876748e6262327d99884.ico','src','sku','waiting\x20for\x20network\x20response\x20count\x20increase','uploadImageConfig','submitButton\x20initiated','ebay.js','#gh','imageName','Adding\x20The\x20AI\x20Description','split','complete','Adding\x20Title','fillEmptyItemSpecifics','smooth','.jpg','2.1','pasteDescriptionFromOpenAi','name','productData','error\x20pasting\x20sku','title','createElement','value','Adding\x20Item\x20Specifics','switchToWindowAndTab','removeEventListener','custom_price','2637368TFPbRP','promotedListingSelection','addEventListener','Submitted\x20the\x20Listing!','co.uk','uploadImageToIframeOnEbay\x20imageObject','.summary__description','images','autoSubmitEnabled','mainHdImages\x20type','Uploading\x20Selected\x20Image','parse','mainHdImages','[value=\x27List\x20item\x27]','substring','stringify','b64Image','main_hd_images','close_tab','7.2','insert_ebay_data\x20begins','Error:','Started\x20inserting\x20product','click','innerHTML','runtime','Item\x20Listed\x20Successfully!','BASE_64','totalImagesUploaded','Adding\x20Price','addListener','PLDataUpdate','visibilityState'];a0_0x1802=function(){return _0x558a95;};return a0_0x1802();}function waitUntilDocumentIsVisible(){return new Promise((_0x359aaf,_0x160f09)=>{var _0x3fc119=setInterval(()=>{var _0x474337=a0_0x166d;document[_0x474337(0x115)]===_0x474337(0x156)&&(clearInterval(_0x3fc119),_0x359aaf());},0x32);});}function uploadImageAndWaitForCounterToUpload(_0xf171b3,_0x1f6036,_0x344d9d){return new Promise((_0x37cfca,_0x150919)=>{var _0x2f7b05=a0_0x166d,_0x5149a7=getTotalImagesUploaded();console['log'](_0x2f7b05(0x111),_0x5149a7),uploadImage(_0xf171b3,'#fehelix-uploader',_0x1f6036,_0x344d9d);var _0x20d65b=setInterval(()=>{var _0x498340=getTotalImagesUploaded();_0x498340>_0x5149a7&&(clearInterval(_0x20d65b),_0x37cfca());},0x3e8);});}function uploadImageToIframeOnEbay(_0x4a1b33,_0x154827,_0x413b14){return new Promise((_0x47ace9,_0x51d9e4)=>{var _0x1811c0=a0_0x166d,_0x2c027e={'inputTagSelector':_0x413b14,'imageName':_0x154827,'b64Image':_0x4a1b33};console[_0x1811c0(0x16f)](_0x1811c0(0xfa),_0x2c027e),uploadImage(_0x2c027e[_0x1811c0(0x105)],_0x1811c0(0x160),_0x2c027e[_0x1811c0(0x185)],IMAGES_TYPES[_0x1811c0(0x110)]);function _0x5e12cf(_0x462aa2){var _0x5b3335=_0x1811c0,_0xd1798b=JSON[_0x5b3335(0x100)](_0x462aa2[_0x5b3335(0x133)]);_0xd1798b['cmd']===_0x5b3335(0x167)&&(window[_0x5b3335(0xf3)](_0x5b3335(0x13d),_0x5e12cf,![]),_0x47ace9());}});}function waitForPicUploadIFrameToLoad(){return new Promise((_0x45522c,_0x49b9ad)=>{var _0x45aab8=a0_0x166d;window['addEventListener'](_0x45aab8(0x13d),_0x28806f,![]);function _0x28806f(_0x1ef4a1){var _0x5bef6a=_0x45aab8,_0xc7b910=_0x1ef4a1['data'];console['log']('receive\x20page\x20load\x20waitForPicUploadIFrameToLoad',_0xc7b910),(_0xc7b910[_0x5bef6a(0x155)]||_0xc7b910[_0x5bef6a(0x11f)])&&(console[_0x5bef6a(0x16f)](_0x5bef6a(0x17c)),window[_0x5bef6a(0xf3)](_0x5bef6a(0x13d),_0x28806f,![]),_0x45522c());}});}function waitForPromotedListingIFrameToLoad(){return new Promise((_0x44dec0,_0xf0f752)=>{var _0x281d7c=a0_0x166d;window[_0x281d7c(0xf7)](_0x281d7c(0x13d),_0x1f13ca,![]);function _0x1f13ca(_0x5becd6){var _0x4b996a=_0x281d7c,_0x2f98ba=_0x5becd6['data'];_0x2f98ba[_0x4b996a(0x11f)]&&(window['removeEventListener'](_0x4b996a(0x13d),_0x1f13ca,![]),_0x44dec0());}});}function promoteListing(_0x57cf45){return new Promise((_0x44d919,_0x447c23)=>{var _0x42dc2b=a0_0x166d,_0x59963a=document[_0x42dc2b(0x176)]('promotedListing_iframe');_0x59963a['contentWindow']['postMessage']({'type':_0x42dc2b(0x11a),'adRate':_0x57cf45},_0x59963a[_0x42dc2b(0x17e)]),window['addEventListener'](_0x42dc2b(0x13d),_0x187995,![]);function _0x187995(_0x73453b){var _0x50ec02=_0x42dc2b,_0x17077c=JSON[_0x50ec02(0x100)](_0x73453b[_0x50ec02(0x133)]);_0x17077c['cmd']===_0x50ec02(0x114)&&(window[_0x50ec02(0xf3)]('message',_0x187995,![]),_0x44d919());}});}async function listItem(){return new Promise((_0xe98368,_0x34c0c8)=>{var _0x467960=a0_0x166d,_0x561ef8=document[_0x467960(0x176)](_0x467960(0x179)),_0x5ccd2a=_0x561ef8[_0x467960(0x117)](_0x467960(0x102));_0x5ccd2a[_0x467960(0x10c)](),_0x5ccd2a[_0x467960(0x10c)](),_0xe98368();});}function checkIfListingSucceeded(){return new Promise((_0x65327c,_0x5d76cc)=>{var _0x22d6e1=a0_0x166d,_0x363b35=document[_0x22d6e1(0x176)](_0x22d6e1(0x139));_0x363b35[_0x22d6e1(0x150)]['visibility']!=='visible'&&_0x65327c(![]),_0x363b35[_0x22d6e1(0x150)][_0x22d6e1(0x12d)]===_0x22d6e1(0x156)&&_0x65327c(!![]);});}function waitSomeTime(_0x585afd){return new Promise((_0x30e838,_0x5b641f)=>{setTimeout(()=>{_0x30e838();},_0x585afd);});}function tellChromeToCloseTab(){var _0x26882b=a0_0x5164c5;chrome[_0x26882b(0x10e)][_0x26882b(0x119)]({'type':_0x26882b(0x107)},function(_0x173ea1){});}function a0_0x166d(_0x49a14a,_0x3cbe33){var _0x1802ef=a0_0x1802();return a0_0x166d=function(_0x166d00,_0x503d26){_0x166d00=_0x166d00-0xe8;var _0x3dd62d=_0x1802ef[_0x166d00];return _0x3dd62d;},a0_0x166d(_0x49a14a,_0x3cbe33);}function tellChromeToSwitchToPreviousTab(){return new Promise((_0x57a596,_0x33c2c8)=>{var _0x2bdd94=a0_0x166d;console['log'](_0x2bdd94(0x14b),previousTabId),console['log'](_0x2bdd94(0x11c),previousWindowId),previousTabId&&previousWindowId?chrome['runtime'][_0x2bdd94(0x119)]({'type':_0x2bdd94(0xf2),'windowId':previousWindowId,'tabId':previousTabId},function(_0x5f487a){var _0x467968=_0x2bdd94;console[_0x467968(0x16f)](_0x467968(0x14a),_0x5f487a),_0x57a596();}):_0x57a596();});}function scrollIntoView(_0x1e1b42){var _0x31d741=a0_0x5164c5;_0x1e1b42[_0x31d741(0x177)]({'behavior':_0x31d741(0x18b),'block':'center','inline':_0x31d741(0x138)});}function createButtonToExecuteFunctionWithParameter(_0x43df19,_0x4311d,_0x2f1219){var _0x46ca4d=a0_0x5164c5,_0x54b9ce=document[_0x46ca4d(0xef)](_0x46ca4d(0x154));_0x54b9ce[_0x46ca4d(0x10d)]=_0x43df19,_0x54b9ce['onclick']=function(_0x5a1226){_0x5a1226['stopPropagation'](),window[_0x43df19](_0x2f1219);},_0x4311d[_0x46ca4d(0x117)](_0x46ca4d(0x13c)+_0x43df19+'\x27]')===null&&_0x4311d['querySelector'](_0x46ca4d(0x13c)+_0x43df19+'\x27]')===undefined&&_0x4311d['appendChild'](_0x54b9ce);}function createManuelInputButtons(_0xc7d66a){var _0x5a79ab=a0_0x5164c5,_0x1b9396=document[_0x5a79ab(0x117)](_0x5a79ab(0xfb));createButtonToExecuteFunctionWithParameter(_0x5a79ab(0xea),_0x1b9396,_0xc7d66a);var _0x1b9396=document[_0x5a79ab(0x117)]('.summary__title\x20.se-textbox--fluid');createButtonToExecuteFunctionWithParameter(_0x5a79ab(0x129),_0x1b9396,_0xc7d66a[_0x5a79ab(0x121)]);var _0x1b9396=document[_0x5a79ab(0x117)]('.summary__price-fields\x20.se-textbox--input');createButtonToExecuteFunctionWithParameter(_0x5a79ab(0x148),_0x1b9396,Math[_0x5a79ab(0x15c)](_0xc7d66a[_0x5a79ab(0x152)]*1.25));var _0x1b9396=document[_0x5a79ab(0x117)](_0x5a79ab(0x136));createButtonToExecuteFunctionWithParameter('applyPromotedListingV2',_0x1b9396,null);var _0x1b9396=document[_0x5a79ab(0x117)](_0x5a79ab(0x130));createButtonToExecuteFunctionWithParameter(_0x5a79ab(0x181),_0x1b9396,_0xc7d66a);}function checkIfTotalNetworkResponseIncreased(_0x3756be,_0x4c2019){window[_0x3756be](_0x4c2019),setInterval(function(){totalNetworkResponse>previousTotalNetworkResponse&&(previousTotalNetworkResponse=totalNetworkResponse,window[_0x3756be](_0x4c2019));},0x3e8);}