var a0_0x233c2d=a0_0x4c35;(function(_0x39b30f,_0x1dbe78){var _0x19b4f0=a0_0x4c35,_0x4c9a87=_0x39b30f();while(!![]){try{var _0x1324e4=-parseInt(_0x19b4f0(0x6d))/0x1*(parseInt(_0x19b4f0(0x90))/0x2)+parseInt(_0x19b4f0(0x8a))/0x3*(parseInt(_0x19b4f0(0x6c))/0x4)+-parseInt(_0x19b4f0(0x71))/0x5+parseInt(_0x19b4f0(0x96))/0x6+-parseInt(_0x19b4f0(0x81))/0x7+parseInt(_0x19b4f0(0x89))/0x8*(-parseInt(_0x19b4f0(0x93))/0x9)+-parseInt(_0x19b4f0(0x87))/0xa*(-parseInt(_0x19b4f0(0x8c))/0xb);if(_0x1324e4===_0x1dbe78)break;else _0x4c9a87['push'](_0x4c9a87['shift']());}catch(_0x5a7ff0){_0x4c9a87['push'](_0x4c9a87['shift']());}}}(a0_0x23a6,0xcba42),console[a0_0x233c2d(0x94)](a0_0x233c2d(0x7c)));var checkoutInitiated=![];chrome[a0_0x233c2d(0x6e)][a0_0x233c2d(0x8b)]['addListener']((_0x32586f,_0x455206,_0x41553b)=>{var _0x35787b=a0_0x233c2d;console['log'](_0x35787b(0x7a),_0x32586f);if(_0x32586f[_0x35787b(0x74)]===_0x35787b(0x86)&&_0x32586f[_0x35787b(0x92)]===_0x35787b(0x8f)){var _0x3df08b=_0x32586f[_0x35787b(0x7e)];console[_0x35787b(0x94)](_0x35787b(0x83),_0x3df08b),fullOrder(_0x3df08b);}_0x32586f['type']===_0x35787b(0x86)&&_0x32586f['action']==='checkout'&&!checkoutInitiated&&(checkoutInitiated=!![],console[_0x35787b(0x94)](_0x35787b(0x95)),orderItem(_0x32586f[_0x35787b(0x7e)])),_0x32586f['type']===_0x35787b(0x8e)&&(console[_0x35787b(0x94)](_0x35787b(0x79)),navigator[_0x35787b(0x82)][_0x35787b(0x6f)]()[_0x35787b(0x7f)](_0xd9e52f=>{var _0x50612d=_0x35787b;console['log'](_0x50612d(0x8d),_0xd9e52f);var _0xc01d54=JSON['parse'](_0xd9e52f);console[_0x50612d(0x94)](_0x50612d(0x88),_0xc01d54),setDeliveryAddressAmazon(_0xc01d54[_0x50612d(0x84)]);}));});async function fullOrder(_0x5661fd){var _0x137528=a0_0x233c2d,_0x45aaf8=_0x5661fd[_0x137528(0x78)];_0x45aaf8=parseInt(_0x45aaf8);try{_0x45aaf8>0x1&&(await setQuantity(_0x45aaf8),await new Promise(_0x25d3cf=>setTimeout(_0x25d3cf,0x3e8)));}catch(_0x231548){console[_0x137528(0x94)](_0x137528(0x91),_0x231548);}await checkCouponCode(),await buyNowAndNavigateToCheckout(),console['log'](_0x137528(0x70));var _0x5402d8=await new Promise((_0x59a296,_0x75d79)=>{var _0xd59b8f=setInterval(()=>{var _0xd845fd=a0_0x4c35,_0x5d3e62=document[_0xd845fd(0x75)](_0xd845fd(0x77));_0x5d3e62&&(clearInterval(_0xd59b8f),_0x59a296(_0x5d3e62));},0x64);});console[_0x137528(0x94)](_0x137528(0x7d),_0x5402d8);var _0x49199c=await getAddToCartButton();console[_0x137528(0x94)](_0x137528(0x76),_0x49199c),_0x49199c['click'](),await new Promise(_0x1629d0=>setTimeout(_0x1629d0,0x3e8)),_0x49199c[_0x137528(0x80)]();for(let _0x27d1b2=0x0;_0x27d1b2<0xa;_0x27d1b2++){var _0x19e687=document[_0x137528(0x75)](_0x137528(0x7b));if(_0x19e687){_0x19e687['click']();break;}await new Promise(_0x1d87c9=>setTimeout(_0x1d87c9,0x3e8));}}async function orderItem(_0x5f55b8){var _0x2f27df=a0_0x233c2d;await setDeliveryAddress(_0x5f55b8),document[_0x2f27df(0x85)]=_0x2f27df(0x73),console[_0x2f27df(0x94)]('Navigating\x20to\x20Checkout'),await new Promise(_0x110a4a=>setTimeout(_0x110a4a,0xbb8)),_0x5f55b8[_0x2f27df(0x72)]&&await setGiftMessage(_0x5f55b8),document[_0x2f27df(0x85)]='#11.1\x20Proceeding\x20to\x20Checkout',console[_0x2f27df(0x94)]('Proceeding\x20to\x20payment');}async function buyNowAndNavigateToCheckout(){var _0x4f802d=a0_0x233c2d,_0x2e935d=await getBuyNowButton();if(_0x2e935d)_0x2e935d['click']();else{var _0x2f8d80=await getAddToCartButton();_0x2f8d80&&_0x2f8d80[_0x4f802d(0x80)]();}}function a0_0x4c35(_0x3e5943,_0x370115){var _0x23a6c5=a0_0x23a6();return a0_0x4c35=function(_0x4c35d6,_0x3ee2bd){_0x4c35d6=_0x4c35d6-0x6c;var _0x4bd74a=_0x23a6c5[_0x4c35d6];return _0x4bd74a;},a0_0x4c35(_0x3e5943,_0x370115);}function a0_0x23a6(){var _0x2aeb11=['onMessage','11IkEePL','Pasted\x20text:\x20','fill_address','addToCart','194YmscLU','Error\x20setting\x20quantity','action','1061082FRYDbC','log','checkout\x20message\x20received','4709610UaOuKn','340dRncJb','8923QwjBkR','runtime','readText','Turbo\x20Checkout\x20check','4417375CMsCzg','shouldUseGiftOption','#10\x20Navigating\x20to\x20Checkout','type','querySelector','Add\x20to\x20Cart\x20Button:\x20','#turbo-checkout-frame','quantitySold','fill_address\x20message\x20received','Message\x20received','#attachSiNoCoverage','content/amazon/auto_order/content.js\x20loaded','Turbo\x20Checkout\x20Frame:\x20','orderDetails','then','click','5968879ViVjmy','clipboard','addToCart\x20message\x20received','customer','title','autoOrder','29533360waYCGE','Parsed\x20JSON:\x20','64UHAknF','22614ayFuBe'];a0_0x23a6=function(){return _0x2aeb11;};return a0_0x23a6();}