var a0_0x1638ae=a0_0x428e;(function(_0x5c6103,_0x1e2d3a){var _0x36b082=a0_0x428e,_0x1de4d5=_0x5c6103();while(!![]){try{var _0x49126e=parseInt(_0x36b082(0x132))/0x1*(parseInt(_0x36b082(0x1f1))/0x2)+-parseInt(_0x36b082(0xf1))/0x3*(-parseInt(_0x36b082(0x1c4))/0x4)+-parseInt(_0x36b082(0x90))/0x5*(-parseInt(_0x36b082(0x1a6))/0x6)+-parseInt(_0x36b082(0x1d1))/0x7+-parseInt(_0x36b082(0xa2))/0x8+parseInt(_0x36b082(0x1fd))/0x9*(parseInt(_0x36b082(0x13e))/0xa)+parseInt(_0x36b082(0x201))/0xb*(parseInt(_0x36b082(0x19b))/0xc);if(_0x49126e===_0x1e2d3a)break;else _0x1de4d5['push'](_0x1de4d5['shift']());}catch(_0x3ecb2c){_0x1de4d5['push'](_0x1de4d5['shift']());}}}(a0_0x3251,0x88352));function addColorDropDown(){var _0x407da2=a0_0x428e,_0x16e5b4=document['createElement'](_0x407da2(0x1b7));_0x16e5b4['id']='color_id',_0x16e5b4[_0x407da2(0x16e)](new Option(_0x407da2(0x1ae),'black')),_0x16e5b4['appendChild'](new Option(_0x407da2(0x9b),_0x407da2(0x9b))),_0x16e5b4[_0x407da2(0x16e)](new Option(_0x407da2(0xf7),'blue')),_0x16e5b4[_0x407da2(0x16e)](new Option(_0x407da2(0x17b),_0x407da2(0x17b))),_0x16e5b4[_0x407da2(0x16e)](new Option(_0x407da2(0x127),_0x407da2(0x127))),_0x16e5b4['appendChild'](new Option(_0x407da2(0x13c),_0x407da2(0x13c))),_0x16e5b4[_0x407da2(0x16e)](new Option(_0x407da2(0x147),_0x407da2(0x147))),_0x16e5b4[_0x407da2(0xde)][_0x407da2(0x209)]=_0x407da2(0x91),document[_0x407da2(0x1bb)][_0x407da2(0x113)](_0x16e5b4);function _0x1505e1(){var _0xf08cbf=_0x407da2,_0x337761=document[_0xf08cbf(0x162)](_0xf08cbf(0x165))[_0xf08cbf(0xa6)],_0x30d233=document[_0xf08cbf(0x162)](_0xf08cbf(0x1b4));_0x30d233[_0xf08cbf(0xde)]['color']=_0x337761;}document[_0x407da2(0x162)](_0x407da2(0x165))[_0x407da2(0x1e0)]=_0x1505e1;}function createSimilarItemNumberInput(){var _0x1b6f2a=a0_0x428e;const _0x3c0d41=document[_0x1b6f2a(0x141)](_0x1b6f2a(0x15e)),_0x1abf1b=document['createElement'](_0x1b6f2a(0x118));_0x1abf1b[_0x1b6f2a(0xd4)]=_0x1b6f2a(0x14d),_0x1abf1b[_0x1b6f2a(0xde)][_0x1b6f2a(0x1ad)]=_0x1b6f2a(0x10f);const _0x12ebcb=document[_0x1b6f2a(0x141)](_0x1b6f2a(0xad));return _0x12ebcb['type']='text',_0x12ebcb['id']=_0x1b6f2a(0xca),_0x12ebcb['style'][_0x1b6f2a(0x1c2)]='1px\x20solid\x20#ccc',_0x12ebcb[_0x1b6f2a(0xde)][_0x1b6f2a(0x8d)]=_0x1b6f2a(0x122),_0x12ebcb[_0x1b6f2a(0xde)][_0x1b6f2a(0x1a9)]='8px',_0x12ebcb['style'][_0x1b6f2a(0xe2)]=_0x1b6f2a(0x213),_0x3c0d41[_0x1b6f2a(0x16e)](_0x1abf1b),_0x3c0d41[_0x1b6f2a(0x16e)](_0x12ebcb),_0x3c0d41['style'][_0x1b6f2a(0x209)]=_0x1b6f2a(0x1ba),_0x3c0d41['style']['marginLeft']=_0x1b6f2a(0x213),_0x3c0d41;}function createOptionalPromotedListingInput(){var _0x45e377=a0_0x428e;const _0x451dfd=document['createElement'](_0x45e377(0x15e));_0x451dfd[_0x45e377(0xee)][_0x45e377(0x159)](_0x45e377(0x14f));const _0x5a605f=document[_0x45e377(0x141)](_0x45e377(0x118));_0x5a605f[_0x45e377(0xd4)]=_0x45e377(0x1be),_0x5a605f[_0x45e377(0xde)][_0x45e377(0x1ad)]=_0x45e377(0x10f);const _0x1e69c9=document[_0x45e377(0x141)]('input');return _0x1e69c9['type']=_0x45e377(0x179),_0x1e69c9['id']='promoted-listing',_0x1e69c9[_0x45e377(0xde)]['border']=_0x45e377(0x1ec),_0x1e69c9[_0x45e377(0xde)]['borderRadius']=_0x45e377(0x122),_0x1e69c9[_0x45e377(0xde)][_0x45e377(0x1a9)]='8px',_0x1e69c9[_0x45e377(0xde)][_0x45e377(0xe2)]=_0x45e377(0x213),_0x1e69c9[_0x45e377(0x10e)]='2',_0x1e69c9['max']='99',_0x1e69c9['step']=_0x45e377(0x15b),_0x451dfd['appendChild'](_0x5a605f),_0x451dfd[_0x45e377(0x16e)](_0x1e69c9),_0x451dfd[_0x45e377(0xde)]['display']=_0x45e377(0x1ba),_0x451dfd[_0x45e377(0xde)][_0x45e377(0xb8)]='16px',chrome[_0x45e377(0xba)][_0x45e377(0xaf)]['get']('promoted_listing_ad_rate',async function(_0x12952c){var _0x28a365=_0x45e377,_0x55a51b=_0x12952c[_0x28a365(0xc0)];(_0x55a51b===undefined||isNaN(_0x55a51b))&&(_0x55a51b=2.1,chrome['storage'][_0x28a365(0xaf)][_0x28a365(0xab)]({'promoted_listing_ad_rate':_0x55a51b})),_0x1e69c9[_0x28a365(0xa6)]=_0x55a51b;}),_0x451dfd;}function createPriceInput(){var _0x403c02=a0_0x428e;const _0x2f2686=document[_0x403c02(0x141)](_0x403c02(0x15e)),_0x345ca3=document['createElement'](_0x403c02(0x118));_0x345ca3['textContent']=_0x403c02(0x10c);const _0x19e511=document[_0x403c02(0x141)](_0x403c02(0xad));return _0x19e511['type']=_0x403c02(0x179),_0x19e511[_0x403c02(0x10e)]='0',_0x19e511[_0x403c02(0x134)]=_0x403c02(0xf0),_0x19e511['id']=_0x403c02(0x109),_0x19e511[_0x403c02(0xde)][_0x403c02(0x1c2)]='1px\x20solid\x20#ccc',_0x19e511[_0x403c02(0xde)][_0x403c02(0x8d)]='4px',_0x19e511[_0x403c02(0xde)][_0x403c02(0x1a9)]=_0x403c02(0x10f),_0x19e511[_0x403c02(0xde)]['fontSize']=_0x403c02(0x213),chrome[_0x403c02(0xba)][_0x403c02(0xaf)][_0x403c02(0x1ea)](_0x403c02(0x212),async function(_0x132262){var _0x1758a8=_0x403c02;const _0x382f8c=_0x132262[_0x1758a8(0x212)];if(_0x382f8c!==undefined){var _0x5d5a58=getAmazonPrice();_0x5d5a58<0x0&&(_0x5d5a58=product_data['price']);const _0x338c94=0x1+_0x382f8c/0x64;let _0x4b60bc=_0x5d5a58*_0x338c94;_0x4b60bc=Math[_0x1758a8(0xa5)](_0x4b60bc);var _0x4544f5=await chrome['storage']['local'][_0x1758a8(0x1ea)](_0x1758a8(0x167));_0x4544f5=parseFloat(_0x4544f5[_0x1758a8(0x167)]);var _0x32f629=0x1-_0x4544f5;_0x32f629==0x1&&(_0x32f629=0x0),_0x4b60bc=_0x4b60bc-_0x32f629,_0x19e511[_0x1758a8(0xa6)]=_0x4b60bc;}}),_0x2f2686[_0x403c02(0x16e)](_0x345ca3),_0x2f2686[_0x403c02(0x16e)](_0x19e511),_0x2f2686['style'][_0x403c02(0x209)]='inline-block',_0x2f2686['style'][_0x403c02(0xb8)]=_0x403c02(0x213),_0x2f2686;}function addTagsToWords(_0x58599b,_0x2f2973,_0x5b1dd4){var _0x2626ae=a0_0x428e,_0xbdf42b=document[_0x2626ae(0x104)](_0x58599b);for(let _0x70a39e=0x0;_0x70a39e<_0xbdf42b[_0x2626ae(0x142)];_0x70a39e++){var _0x24381f=_0xbdf42b[_0x70a39e],_0x149edd=_0x24381f[_0x2626ae(0x1cc)][_0x2626ae(0x11b)]('\x20'),_0x42d656='';for(let _0x3f7a78=0x0;_0x3f7a78<_0x149edd[_0x2626ae(0x142)];_0x3f7a78++){var _0x3f3353=_0x149edd[_0x3f7a78];_0x3f3353='<'+_0x2f2973+_0x2626ae(0x10b)+_0x5b1dd4+'\x22>'+_0x3f3353+'</'+_0x2f2973+'>',_0x42d656+='\x20'+_0x3f3353,console[_0x2626ae(0x133)](_0x42d656);}console[_0x2626ae(0x133)](_0x42d656),_0x24381f[_0x2626ae(0xe3)]=_0x42d656;}}function makeDraggable(){var _0x1f15ae=a0_0x428e;addTagsToWords(_0x1f15ae(0x1f9),'div',_0x1f15ae(0x1b5)),$('#productTitle')[_0x1f15ae(0xcb)](),console[_0x1f15ae(0x133)]('made\x20sortable');}function highlightTextRange(){var _0x71df6=a0_0x428e;$(_0x71df6(0xa4))['highlightWithinTextarea']({'highlight':[0x0,0x50],'className':_0x71df6(0x17b)});var _0x19898e=document[_0x71df6(0x162)](_0x71df6(0x1b4));_0x19898e['cols']=_0x71df6(0x1f8),_0x19898e[_0x71df6(0x1e3)]='3';}async function mainTitleBuilder(){var _0x5efbe3=a0_0x428e;try{var _0x53028c=document[_0x5efbe3(0x162)](_0x5efbe3(0xc5));_0x53028c[_0x5efbe3(0x121)][_0x5efbe3(0x107)](_0x53028c);}catch(_0x425641){console[_0x5efbe3(0x133)](_0x5efbe3(0x1bf),_0x425641);}addColorDropDown();var _0x3a5296=createPriceInput(),_0x430b55=createSimilarItemNumberInput(),_0x33e325=createOptionalPromotedListingInput(),_0x39d437=createSnipeTitleButton(),_0x1776e1=await createCleanTitleButton(),_0x531628=createFreeListingButton(),_0x4a4021=createSnipeListingButton(),_0x5059c5=createPaidListingButton(),_0x26d956=document['createElement'](_0x5efbe3(0x15e));_0x26d956['id']=_0x5efbe3(0x100);var _0x28d20a=await createSpinner();console[_0x5efbe3(0x133)](_0x5efbe3(0x149),_0x28d20a),_0x26d956[_0x5efbe3(0x16e)](_0x1776e1),_0x26d956['appendChild'](_0x39d437),_0x26d956[_0x5efbe3(0x16e)](_0x28d20a),_0x26d956[_0x5efbe3(0x16e)](_0x4a4021),_0x26d956[_0x5efbe3(0x16e)](_0x5059c5),_0x26d956[_0x5efbe3(0x16e)](_0x531628),_0x26d956['appendChild'](_0x3a5296),_0x26d956['appendChild'](_0x430b55);var _0x52b950=createImportButton();_0x26d956[_0x5efbe3(0x16e)](_0x52b950),_0x26d956[_0x5efbe3(0x16e)](_0x33e325);var {useReviewImages:_0x2cbfe9}=await chrome[_0x5efbe3(0xba)][_0x5efbe3(0xaf)][_0x5efbe3(0x1ea)](_0x5efbe3(0x89));if(_0x2cbfe9){var _0x594aa4=createImportReviewImagesButton();_0x26d956['appendChild'](_0x594aa4);}var {useCustomImagePrompt:_0x46e4bc}=await chrome[_0x5efbe3(0xba)]['local']['get'](_0x5efbe3(0x200));if(_0x46e4bc){var _0x46c44c=await createGenerateImageDivV2();_0x26d956[_0x5efbe3(0x16e)](_0x46c44c);}await new Promise(_0x234b58=>{const _0x1c372e=setInterval(()=>{var _0xb4a1f3=a0_0x428e;const _0x20938c=document[_0xb4a1f3(0x116)](_0xb4a1f3(0x1b0));_0x20938c&&(clearInterval(_0x1c372e),_0x234b58());},0x64);}),document['querySelector'](_0x5efbe3(0x1b0))['appendChild'](_0x26d956);var _0x173a35=document[_0x5efbe3(0x116)](_0x5efbe3(0x137)),_0x52f97b=document[_0x5efbe3(0x141)](_0x5efbe3(0x1aa));_0x52f97b[_0x5efbe3(0x120)]=_0x5efbe3(0x1a8),_0x52f97b['id']='maximum',_0x52f97b[_0x5efbe3(0xf8)]=_0x5efbe3(0x1e5);var _0x240787=document[_0x5efbe3(0x141)]('span');_0x240787[_0x5efbe3(0x120)]=_0x5efbe3(0x1a8),_0x240787['id']=_0x5efbe3(0x186),_0x240787[_0x5efbe3(0xf8)]='0';var _0x2eacf8=document[_0x5efbe3(0x141)]('textarea');_0x2eacf8['id']=_0x5efbe3(0x1b4),_0x2eacf8[_0x5efbe3(0x1ac)]='20',_0x2eacf8[_0x5efbe3(0x1e3)]='3';var _0x5e1f62='';for(let _0x5baacd=0x0;_0x5baacd<0x50;_0x5baacd++){_0x5e1f62+='a';}_0x2eacf8[_0x5efbe3(0xa6)]=_0x5e1f62;var _0x26d956=document['createElement'](_0x5efbe3(0x15e));_0x26d956['id']=_0x5efbe3(0x18a),_0x26d956[_0x5efbe3(0x16e)](_0x2eacf8),_0x173a35[_0x5efbe3(0x16e)](_0x26d956),_0x173a35[_0x5efbe3(0x16e)](_0x240787),_0x173a35[_0x5efbe3(0x16e)](_0x52f97b),highlightTextRange();try{_0x2eacf8['value']=getFilteredTitle();}catch(_0x24959d){_0x2eacf8['value']=product_data[_0x5efbe3(0xb9)];}_0x37e6cd(),$('textarea')[_0x5efbe3(0x1af)](function(){_0x37e6cd();});function _0x37e6cd(){var _0x4f3f8e=_0x5efbe3,_0x1d3bb0=$(_0x4f3f8e(0xa4))[_0x4f3f8e(0xe9)]()[_0x4f3f8e(0x142)],_0x4ee343=$(_0x4f3f8e(0xf4)),_0x1cea95=$('#maximum'),_0xdea926=$(_0x4f3f8e(0x11f));_0x4ee343['text'](_0x1d3bb0),_0x1cea95[_0x4f3f8e(0xf8)]('/\x2080');}}window['location'][a0_0x1638ae(0xff)][a0_0x1638ae(0xc3)](a0_0x1638ae(0xcd))>-0x1&&mainTitleBuilder()['catch'](_0x34941d=>console[a0_0x1638ae(0x12a)](_0x34941d));function waitForMediaItems(){return new Promise(_0x1f69b8=>{const _0x452f81=setInterval(()=>{var _0x4449d1=a0_0x428e,_0x10be49=document['querySelectorAll']('[class*=\x27media-gallery-item\x27]');_0x10be49[_0x4449d1(0x142)]>0x0&&(console['log'](_0x4449d1(0x106)),clearInterval(_0x452f81),_0x1f69b8());},0x1f4);});}var activeNotifications=[];function createImportReviewImagesButton(){var _0x126e29=a0_0x1638ae,_0x194f4e=document[_0x126e29(0x141)](_0x126e29(0x1d7));_0x194f4e['id']=_0x126e29(0x112),_0x194f4e[_0x126e29(0xd4)]=_0x126e29(0xe8),_0x194f4e['classList'][_0x126e29(0x159)](_0x126e29(0x14c));if(product_data[_0x126e29(0x117)][_0x126e29(0x142)]===0x0){var _0x424f84=document[_0x126e29(0x141)](_0x126e29(0x15e));_0x424f84[_0x126e29(0xe3)]=_0x126e29(0xa8),_0x424f84[_0x126e29(0xde)][_0x126e29(0x1ca)]=_0x126e29(0x9b),_0x424f84[_0x126e29(0xde)][_0x126e29(0xfe)]=_0x126e29(0xc6),_0x424f84[_0x126e29(0xde)][_0x126e29(0xe2)]='20px',_0x424f84[_0x126e29(0xde)][_0x126e29(0x1a9)]=_0x126e29(0x13d),document[_0x126e29(0x162)](_0x126e29(0x111))[_0x126e29(0x16e)](_0x424f84),_0x194f4e=_0x424f84;}else _0x194f4e[_0x126e29(0x1fe)]=async function(){var _0x3242bc=_0x126e29;console[_0x3242bc(0x133)](_0x3242bc(0xc9));var _0x4eb150=document[_0x3242bc(0x116)](_0x3242bc(0x188));if(_0x4eb150)_0x4eb150['click'](),console[_0x3242bc(0x133)](_0x3242bc(0x198));else{console['log'](_0x3242bc(0x185));return;}await waitForMediaItems(),attachEventListenersToMediaItems();};return _0x194f4e;}function attachEventListenersToMediaItems(){var _0x2ffa46=a0_0x1638ae;function _0x305888(_0x474c6e){_0x474c6e['forEach'](_0x1c5d94=>{var _0x14205a=a0_0x428e;!_0x1c5d94[_0x14205a(0x92)]('data-listener-attached')&&(_0x1c5d94[_0x14205a(0x8c)](_0x14205a(0x1c9),'true'),_0x1c5d94[_0x14205a(0xbd)]('click',async function(_0xb34d99){var _0x1a84e7=_0x14205a;_0xb34d99[_0x1a84e7(0xdf)](),_0xb34d99[_0x1a84e7(0x157)](),console[_0x1a84e7(0x133)](_0x1a84e7(0x170)),_0x1c5d94[_0x1a84e7(0xde)]['border']=_0x1a84e7(0x8a);var _0x199d81=_0x1c5d94[_0x1a84e7(0x116)](_0x1a84e7(0x18f));if(_0x199d81){var _0x5b741c=_0x199d81[_0x1a84e7(0x11d)];_0x5b741c=enlargeAmazonImage(_0x5b741c);var _0x10848a=await urlToImage(_0x5b741c);createImageAndAppend(_0x10848a,!![]),showNotification(_0x1a84e7(0x152));}else console['log']('No\x20image\x20found\x20in\x20the\x20clicked\x20item.');}));});}var _0x535c01=document[_0x2ffa46(0x104)](_0x2ffa46(0xda));_0x305888(_0x535c01);var _0x4b0210=document[_0x2ffa46(0x104)](_0x2ffa46(0x1c0))[0x3];if(_0x4b0210){var _0x3b2847=new MutationObserver(function(_0x18ef2d){var _0x4f47b5=_0x2ffa46;_0x18ef2d[_0x4f47b5(0xe5)](_0x55a509=>{var _0x283e64=_0x4f47b5;_0x55a509['addedNodes'][_0x283e64(0xe5)](_0x197b22=>{var _0x19c46d=_0x283e64;_0x197b22['nodeType']===Node[_0x19c46d(0x1e6)]&&_0x197b22['matches'](_0x19c46d(0xda))&&_0x305888([_0x197b22]);});});});_0x3b2847[_0x2ffa46(0x12e)](_0x4b0210,{'childList':!![],'subtree':!![]});}else console[_0x2ffa46(0x12a)](_0x2ffa46(0xb3));}function showNotification(_0x5b7320){var _0x121ce0=a0_0x1638ae;const _0x74258a=document[_0x121ce0(0x141)](_0x121ce0(0x15e));_0x74258a[_0x121ce0(0x120)]=_0x121ce0(0x190),_0x74258a[_0x121ce0(0xd4)]=_0x5b7320,document[_0x121ce0(0x1bb)]['appendChild'](_0x74258a);var _0x43350=activeNotifications['length'];_0x74258a['style'][_0x121ce0(0x115)]=0x14+_0x43350*0x5%0x64+'px',_0x74258a[_0x121ce0(0xde)]['top']=0x32+_0x43350*0x5%0x64+'px',activeNotifications[_0x121ce0(0xeb)](_0x74258a),setTimeout(()=>{var _0x51228b=_0x121ce0;_0x74258a[_0x51228b(0xde)]['opacity']='0',setTimeout(()=>{var _0x5ebb7e=_0x51228b;const _0x4d4545=activeNotifications[_0x5ebb7e(0xc3)](_0x74258a);_0x4d4545!==-0x1&&activeNotifications[_0x5ebb7e(0x175)](_0x4d4545,0x1),_0x74258a[_0x5ebb7e(0x1d3)]();},0x1f4);},0xbb8);}function createButtonToReplaceAnd(){var _0x4c54b1=a0_0x1638ae,_0x171ca0=document['createElement'](_0x4c54b1(0x1d7));_0x171ca0['id']='replace-and-button',_0x171ca0[_0x4c54b1(0xd4)]=_0x4c54b1(0x16c),_0x171ca0[_0x4c54b1(0x1fe)]=function(){var _0xe66154=_0x4c54b1;console[_0xe66154(0x133)]('clicked\x20button');var _0x5a1e22=document[_0xe66154(0x162)](_0xe66154(0x1b4)),_0x1831c4=_0x5a1e22[_0xe66154(0xa6)];console[_0xe66154(0x133)](_0xe66154(0x1ff)+_0x1831c4),_0x1831c4=_0x1831c4[_0xe66154(0x20e)](/\band\b/gi,'&'),_0x5a1e22[_0xe66154(0xa6)]=_0x1831c4,updateCharacterCount();},document[_0x4c54b1(0x1bb)][_0x4c54b1(0x113)](_0x171ca0);}function createButtonToReplaceWith(){var _0x1ea170=a0_0x1638ae,_0x26b986=document[_0x1ea170(0x141)]('button');_0x26b986['id']=_0x1ea170(0x15f),_0x26b986[_0x1ea170(0xd4)]=_0x1ea170(0xd5),_0x26b986[_0x1ea170(0x1fe)]=function(){var _0x206cac=_0x1ea170;console[_0x206cac(0x133)](_0x206cac(0x202));var _0x29bf0c=document[_0x206cac(0x162)](_0x206cac(0x1b4)),_0x26e8f8=_0x29bf0c[_0x206cac(0xa6)];console['log'](_0x206cac(0x1ff)+_0x26e8f8),_0x26e8f8=_0x26e8f8[_0x206cac(0x20e)](/\bwith\b/gi,'w/'),_0x29bf0c['value']=_0x26e8f8,updateCharacterCount();},document[_0x1ea170(0x1bb)][_0x1ea170(0x113)](_0x26b986);}function createButtonToLowerCaseUnnecessaryWords(){var _0x425696=a0_0x1638ae,_0xd417eb=document[_0x425696(0x141)](_0x425696(0x1d7));_0xd417eb['id']='replace-unnecessary-button',_0xd417eb[_0x425696(0xd4)]=_0x425696(0xf9),_0xd417eb[_0x425696(0x1fe)]=function(){var _0x513517=_0x425696;console[_0x513517(0x133)](_0x513517(0x202));var _0x317a2b=document[_0x513517(0x162)](_0x513517(0x1b4)),_0x1cc441=_0x317a2b[_0x513517(0xa6)];console['log'](_0x513517(0x1ff)+_0x1cc441),_0x1cc441=_0x1cc441[_0x513517(0x1b1)](),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\b([a-z])/gi,function(_0x2f934){var _0x23758b=_0x513517;return _0x2f934[_0x23758b(0x204)]();}),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\band\b/gi,'&'),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\bwith\b/gi,_0x513517(0x1d5)),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\band\b/gi,_0x513517(0x114)),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\bfor\b/gi,_0x513517(0xa7)),_0x1cc441=_0x1cc441['replace'](/\bin\b/gi,'in'),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\bor\b/gi,'or'),_0x1cc441=_0x1cc441['replace'](/\bof\b/gi,'of'),_0x1cc441=_0x1cc441[_0x513517(0x20e)](/\bupto\b/gi,_0x513517(0xe1)),_0x317a2b[_0x513517(0xa6)]=_0x1cc441,updateCharacterCount();},document['body'][_0x425696(0x113)](_0xd417eb);}function a0_0x3251(){var _0x56ce5d=['Remove\x20Shopify\x20Credentials','9auEeBj','onclick','text:\x20','useCustomImagePrompt','209czBedP','clicked\x20button','toString','toUpperCase','list-button','price','isArray','shouldGetGspr','display','addMenuItem','finished\x20flyInText','marginTop','farewell','replace','domain','listItemToShopify','chatGpt','markupPrice','16px','map','useReviewImages','5px\x20solid\x20red','<b>Sniping\x20Title...</b>','setAttribute','borderRadius','Cleaning\x20The\x20Perfect\x20Title\x20-\x20','selected_image','2445GAAnmO','none','hasAttribute','<b>Cleaning\x20Title...</b>','block','text_prompts/CleanTitlePrompts/v1.txt','errorMessage','great_title_chat_gpt_web_version','Action','<b>Sniping\x20SEO\x20Title...</b>','str','red','Listed\x20Data','Please\x20purchase\x20credits\x20to\x20list\x20to\x20Poshmark','Favicons/OpenAi/icon.png','cleanedTitle:\x20','listing\x20to\x20ebay','shouldGetGspr:\x20','5597640RBGHTr','free','#the-textarea','ceil','value','for','No\x20Review\x20Images\x20Found','You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20more\x20credits.','(Product\x20Title):\x20','set','Great\x20Title','input','{{productTitle}}','local','\x0a\x0a(Product\x20Description):\x20','create-seo-title-v4','descriptionText','Failed\x20to\x20find\x20the\x20container\x20to\x20observe.\x20Check\x20the\x20selector.','responsiblePersonEU:\x20','payload:\x20','shopify_admin_access_token','<b>Cleaned\x20Title!</b>','marginLeft','title','storage','<b>Snipe\x20Title\x20(1\x20credit)</b>','showMenu','addEventListener','Basic-List','100%','promoted_listing_ad_rate','toFixed','#create-title-v4','indexOf','asin','skiplink','bold','disabled','manufacturerInfo','Button\x20clicked','similar-item-number','sortable','#listTitle','/dp/','filter','french','deductCredits','message','text_prompts/simple-prompt-title.txt','title:\x20','textContent','Replace\x20\x27with\x27\x20with\x20\x27/w\x27','paid','Creating\x20The\x20Perfect\x20Title\x20-\x20','Save\x20The\x20Title\x20To\x20Top','#10a37f','[class*=\x27media-gallery-item\x27]','<b>Opti-List</b>\x20(No\x20Credits)','Manuel','prompt:\x20','style','preventDefault','Snipe\x20Title\x20🎯','upto','fontSize','innerHTML','create10TitlesV3AndAppendToTable','forEach','height','<b>Chat\x20Title</b>\x20(No\x20Membership)','Load\x20Review\x20Images','val','success','push','membership:\x20','italian','classList','build_title','0.01','6jyJIfL','parse','getURL','#current','fixed','Import','blue','text','Lower\x20Case\x20Unnecessary\x20Words','clean-title-button','Perfect\x20SEO\x20Title','\x0aENTER\x20IN\x20NEW\x20API\x20KEY','choices','fontWeight','href','sniper-action-buttons','You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20more\x20credits\x20to\x20continue\x20listing.','com','Basic-List\x20(Please\x20Purchase\x20Membership)','querySelectorAll','content','Media\x20items\x20found,\x20proceeding\x20with\x20adding\x20event\x20listeners','removeChild','Rank','sell-price','spanish','\x20class=\x22','Sell\x20it\x20for:','clipboardData:\x20','min','8px','Shopify\x20credentials\x20saved\x20successfully!','imagesContainer','import-review-images-button','prepend','and','right','querySelector','main_hd_images','label','german','<b>Snipe\x20Title</b>\x20(No\x20Credits)','split','Error','src','<b>Chatted\x20Title!</b>','#the-count','className','parentNode','4px','please\x20wait...','<b>SEO\x20Title</b>','paid-listing-button','Perfect\x20Title','purple','Create\x2010\x20Titles','createSnipeTitleButton\x20response:\x20','error','reasoning','snipe-title-context-menu','your-store.myshopify.com','observe','country','https://ebaysnipertitlebuilder.ngrok.io/api/TitleBuilder/BuildTitle','\x20\x0a\x0a\x0aItem\x20Details:\x0a','577aApogo','log','step','creditsAvailable','Multi-myAi-','#listingToEbayUiStatus','Save','finished\x20textArea.value\x20=\x20ai_title;','checkFreeCredits','marginBottom','orange','10px','5888530cqLJdL','OpenAI:\x20','{{Competitor_title}}','createElement','length','Error\x20-\x20','create10TitlesV3AndAppendToTable\x20data:\x20','listingType','lastIndexOf','yellow','seoTitles','spinner:\x20','<b>Opti-List</b>','substring','rainbow-button','Similar\x20Item\x20Number:','Favicons/Completed/received.png','optional-promoted-listing','text\x20to\x20feed\x20openAI','responsiblePersonEUText','Picture\x20added!','custom_price','Received\x20Title:\x20\x22','Change','textPrompt','stopPropagation','finished\x20waiting\x201\x20second','add','Admin\x20Access\x20Token:','0.1','https://suggest-optimized-titles-2-djybcnnsgq-uc.a.run.app','data','div','replace-with-button','transform','list','getElementById','snipe-listing-button','getAiTitleFromApi\x20data:\x20','color_id','Please\x20set\x20your\x20Shopify\x20store\x20URL\x20and\x20admin\x20access\x20token\x20in\x20the\x20extension\x20settings.','end_price','placeholder','user_input','list_to_ebay','sendMessage','Replace\x20\x27and\x27\x20with\x20\x27&\x27','contextmenu','appendChild','titles:\x20','Media\x20item\x20clicked','json','Unfiltered\x20Title\x20To\x20Mimic:\x20','clipboard','<b>Chat\x20Title\x20(1\x20Credit)</b>','splice','com.au','Snipe-List\x20(Please\x20Purchase\x20Membership)','description','number','manufacturerInfoText','green','backgroundColor','Please\x20upgrade\x20to\x20Ultimate\x20membership\x20to\x20list\x20to\x20Poshmark','manufacturerInfo:\x20','#3a86ff','itemNumber','myAi-v3','grey','english','List\x20it\x20now\x20on\x20','Carousel\x20element\x20not\x20found','current','promoted-listing','[data-csa-c-slot-id*=\x22see_all_image\x22]','sku','divDroppedFields','Error\x20-\x20Try\x20Again','Favicons/Completed/right_arrow.png','optimized_title','o1-preview','img','popup-notification','zIndex','_blank','type','Title','Custom\x20Title','https://script.google.com/macros/s/AKfycby6JkD3JFSzYZhreDcB1SOcJ1gMiQjSd7gsX7y619hvnvILuUHOC0pcS7f3qspaIvrP/exec?','left','Carousel\x20clicked','{{productDescription}}','10000','243972kaoSeu','\x20createTitleV4AndAppendToTable\x20ai_title:\x20','responsiblePersonEU','Error:\x20No\x20data\x20returned','Make\x20a\x20optimized,\x20clear\x20eBay\x20title.','open','List\x20To\x20Poshmark','custom_title','You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20a\x20membership\x20to\x20continue\x20listing.','<b>Chat\x20Title\x20(0.5\x20Credit)</b>','No\x20title\x20found','198GzJOlJ','list_to_','counted','padding','span','user','cols','marginRight','black','keyup','#listingToEbayUiButtons','toLowerCase','Total\x20Characters','Great\x20SEO\x20Title','the-textarea','word','25px','select','click','<b>Clean\x20Title</b>\x20(No\x20Membership)','inline-block','body','<b>Snipe\x20Title</b>','listing-data-table','Promoted\x20Listing\x20Percentage\x20%:','error:\x20','.a-popover-wrapper','<b>Chatting\x20Title...</b>','border','Chat-List','888160eKegML','password','create-title-v4','readText','Create\x20Title\x20V3','data-listener-attached','color','1px\x20solid\x20black','innerText','5px','ERROR!\x20Manufacturer\x20country\x20is\x20empty!','data:\x20','includes','4229057RSTOdd','shopify_store_url','remove','combinedTitleAndDescription:\x20','with','position','button','finished\x20createCellWithTitle','width','jsonPrompt','ultimate','.title-cell','create-title-from-openai-button','trim','checkCredits','onchange','gpt-4.1-nano','ask_bg_build_the_seo_titles','rows','then','/\x2080','ELEMENT_NODE','undefined','Sniped','list_similar_item','get','create-10-title-from-openai-button','1px\x20solid\x20#ccc','runtime','customTitle','Shopify\x20Store\x20URL:','filteredTitle','1478lWSmqf','{{Our_title}}','list_to_poshmark','custom_title_prompt','Type','response:\x20','<b>Sniped\x20Title!</b>','200','#productTitle','getAiGeneratedTitle','Chat-List\x20(Please\x20Purchase\x20Membership)'];a0_0x3251=function(){return _0x56ce5d;};return a0_0x3251();}function createButtonToSaveTitleToTop(){var _0xbcb0a6=a0_0x1638ae,_0x3b1bc8=document[_0xbcb0a6(0x141)](_0xbcb0a6(0x1d7));_0x3b1bc8['id']='save-title-button',_0x3b1bc8[_0xbcb0a6(0xd4)]=_0xbcb0a6(0xd8),_0x3b1bc8[_0xbcb0a6(0x1fe)]=function(){var _0x585bbc=_0xbcb0a6;console['log'](_0x585bbc(0x202));var _0x3819e1=document['getElementById']('the-textarea'),_0x1f804e=_0x3819e1[_0x585bbc(0xa6)];console[_0x585bbc(0x133)](_0x585bbc(0x1ff)+_0x1f804e);var _0x4c2e25=document['getElementById']('listing-data-table'),_0x40f290=createRow(_0x4c2e25);createCell(_0x4c2e25,{'rowNumber':_0x40f290,'cellValue':_0x40f290,'headerName':'Rank'}),createCell(_0x4c2e25,{'rowNumber':_0x40f290,'cellValue':_0x1f804e,'headerName':_0x585bbc(0x194)}),createCell(_0x4c2e25,{'rowNumber':_0x40f290,'cellValue':_0x585bbc(0xdc),'headerName':_0x585bbc(0x1f5)}),createCell(_0x4c2e25,{'rowNumber':_0x40f290,'cellValue':_0x1f804e[_0x585bbc(0x142)],'headerName':'Total\x20Characters'});var _0x38209c=createButtonToUpdateTextArea({'buttonInnerText':_0x585bbc(0x155),'textAreaSelector':_0x585bbc(0xa4),'valueToSet':_0x1f804e,'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x4c2e25,{'button':_0x38209c,'rowNumber':_0x40f290,'headerName':_0x585bbc(0x98)});},document[_0xbcb0a6(0x1bb)][_0xbcb0a6(0x113)](_0x3b1bc8);}function createButtonToUpdateGoogleSheets(){var _0x24e171=a0_0x1638ae,_0x2bc595=document['createElement'](_0x24e171(0x1d7));_0x2bc595['id']='update-google-sheets-button',_0x2bc595[_0x24e171(0xd4)]='Update\x20Google\x20Sheets',_0x2bc595['onclick']=function(){var _0x59c9d3=_0x24e171;console[_0x59c9d3(0x133)](_0x59c9d3(0x202));var _0x3b40c0=[],_0x11b6a5=getProductDescriptionAndFeatures(),_0x51bf40=getProductTitle(),_0x30776a=getTheAsinFromHref(),_0x400457=document[_0x59c9d3(0x162)](_0x59c9d3(0x1b4))[_0x59c9d3(0xa6)];_0x3b40c0[_0x59c9d3(0xeb)]({'key':_0x59c9d3(0x178),'value':_0x11b6a5}),_0x3b40c0['push']({'key':_0x59c9d3(0xb9),'value':_0x51bf40}),_0x3b40c0['push']({'key':_0x59c9d3(0xc4),'value':_0x30776a}),_0x3b40c0['push']({'key':_0x59c9d3(0x1ee),'value':_0x400457});var _0xf322eb=_0x59c9d3(0x196);updateGoogleSheets(_0x3b40c0,_0xf322eb);},document['body'][_0x24e171(0x113)](_0x2bc595);}function a0_0x428e(_0x14936b,_0x1f7575){var _0x3251a8=a0_0x3251();return a0_0x428e=function(_0x428ee1,_0x5a2f9f){_0x428ee1=_0x428ee1-0x88;var _0x52b20d=_0x3251a8[_0x428ee1];return _0x52b20d;},a0_0x428e(_0x14936b,_0x1f7575);}async function createButtonToCreateTitleFromOpenAi(){var _0x144d3b=a0_0x1638ae,_0x26b97e=document[_0x144d3b(0x141)](_0x144d3b(0x1d7));_0x26b97e['id']=_0x144d3b(0x1dd),_0x26b97e[_0x144d3b(0xd4)]='Create\x20Title',_0x26b97e[_0x144d3b(0x1fe)]=async function(){var _0x1c599b=_0x144d3b;console[_0x1c599b(0x133)](_0x1c599b(0x202));var _0x1e453a=getProductDescriptionAndFeatures();console[_0x1c599b(0x133)](_0x1c599b(0x150),_0x1e453a);var _0xd47931={'title':getFilteredTitle(),'description':_0x1e453a},_0x2bb398=await createOneTitle(_0xd47931),_0x144479=document[_0x1c599b(0x162)](_0x1c599b(0x1bd)),_0x36ec68=createRow(_0x144479);createCell(_0x144479,{'rowNumber':_0x36ec68,'cellValue':_0x36ec68,'headerName':'Rank'}),createCell(_0x144479,{'rowNumber':_0x36ec68,'cellValue':_0x2bb398,'headerName':_0x1c599b(0x194)}),createCell(_0x144479,{'rowNumber':_0x36ec68,'cellValue':'myAi','headerName':_0x1c599b(0x1f5)}),createCell(_0x144479,{'rowNumber':_0x36ec68,'cellValue':_0x2bb398[_0x1c599b(0x142)],'headerName':'Total\x20Characters'});var _0x9ac7a4=createButtonToUpdateTextArea({'buttonInnerText':_0x1c599b(0x155),'textAreaSelector':_0x1c599b(0xa4),'valueToSet':_0x2bb398,'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x144479,{'button':_0x9ac7a4,'rowNumber':_0x36ec68,'headerName':'Action'});},document[_0x144d3b(0x1bb)][_0x144d3b(0x113)](_0x26b97e);}async function createButtonToCreate10TitlesFromOpenAi(){var _0xf0d378=a0_0x1638ae,_0x11c45a=document[_0xf0d378(0x141)](_0xf0d378(0x1d7));_0x11c45a['id']=_0xf0d378(0x1eb),_0x11c45a['textContent']=_0xf0d378(0x128),_0x11c45a[_0xf0d378(0x1fe)]=async function(){var _0x1219f0=_0xf0d378;console[_0x1219f0(0x133)](_0x1219f0(0x202));var _0x510382=getProductDescriptionAndFeatures();console[_0x1219f0(0x133)](_0x1219f0(0x150),_0x510382);var _0x2f0381={'title':getFilteredTitle(),'description':_0x510382},_0x53e44e=await create10Titles(_0x2f0381);for(var _0x425cd6=0x0;_0x425cd6<_0x53e44e['length'];_0x425cd6++){var _0xd9f7bb=_0x53e44e[_0x425cd6],_0x1d3623=document['getElementById'](_0x1219f0(0x1bd)),_0xeae789=createRow(_0x1d3623);createCell(_0x1d3623,{'rowNumber':_0xeae789,'cellValue':_0xeae789,'headerName':_0x1219f0(0x108)}),createCell(_0x1d3623,{'rowNumber':_0xeae789,'cellValue':_0xd9f7bb,'headerName':_0x1219f0(0x194)}),createCell(_0x1d3623,{'rowNumber':_0xeae789,'cellValue':_0x1219f0(0x136)+_0x425cd6,'headerName':_0x1219f0(0x1f5)}),createCell(_0x1d3623,{'rowNumber':_0xeae789,'cellValue':_0xd9f7bb[_0x1219f0(0x142)],'headerName':'Total\x20Characters'});var _0x887bf9=createButtonToUpdateTextArea({'buttonInnerText':'Change','textAreaSelector':_0x1219f0(0xa4),'valueToSet':_0xd9f7bb,'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x1d3623,{'button':_0x887bf9,'rowNumber':_0xeae789,'headerName':_0x1219f0(0x98)});}},document[_0xf0d378(0x1bb)][_0xf0d378(0x113)](_0x11c45a);}async function createButtonToCreateTitleV3FromOpenAi(){var _0x414000=a0_0x1638ae,_0x2571bb=document[_0x414000(0x141)](_0x414000(0x1d7));_0x2571bb['id']='create-simple-title-from-openai-button',_0x2571bb[_0x414000(0xd4)]=_0x414000(0x1c8),_0x2571bb[_0x414000(0x1fe)]=async function(){var _0x4380f4=_0x414000;console[_0x4380f4(0x133)]('clicked\x20button');var _0x2cdca5=getProductDescriptionAndFeatures(),_0x522f19=getFilteredTitle(),_0x46ebb5=_0x4380f4(0xd2),_0x45d280=await fetchPrompt(_0x46ebb5);console[_0x4380f4(0x133)](_0x4380f4(0xdd),_0x45d280),_0x45d280=_0x45d280[_0x4380f4(0x20e)](_0x4380f4(0xae),_0x522f19),_0x45d280=_0x45d280[_0x4380f4(0x20e)](_0x4380f4(0x199),_0x2cdca5),console[_0x4380f4(0x133)](_0x4380f4(0xdd),_0x45d280);var _0x38457d=await receiveOpenAiResponseFromBackgroundScript(_0x45d280);_0x38457d[_0x4380f4(0x12a)]?alert(_0x4380f4(0x13f)+_0x38457d[_0x4380f4(0x12a)][_0x4380f4(0xd1)]+_0x4380f4(0xfc)):handleResponse(_0x38457d);},document[_0x414000(0x1bb)][_0x414000(0x113)](_0x2571bb);}function generateUUID(){var _0x1c7ea5=a0_0x1638ae;let _0x2a22e7='',_0xaa8853,_0x537278;for(_0xaa8853=0x0;_0xaa8853<0x20;_0xaa8853++){_0x537278=Math['random']()*0x10|0x0,(_0xaa8853===0x8||_0xaa8853===0xc||_0xaa8853===0x10||_0xaa8853===0x14)&&(_0x2a22e7+='-'),_0x2a22e7+=(_0xaa8853===0xc?0x4:_0xaa8853===0x10?_0x537278&0x3|0x8:_0x537278)[_0x1c7ea5(0x203)](0x10);}return _0x2a22e7;}async function getAiTitleFromApi(){var _0x3a7454=a0_0x1638ae,_0x18a186=getProductDescriptionAndFeatures(),_0x49057f=getFilteredTitle(),_0x11b4ea=_0x49057f+'\x0a\x0a'+_0x18a186;console[_0x3a7454(0x133)](_0x3a7454(0x1d4),_0x11b4ea);_0x46526d:var _0x1bd992=_0x3a7454(0x130);var _0x4c68bc=await postToServer(_0x1bd992,{'request_type':_0x3a7454(0xef),'product_description':_0x11b4ea});return console['log'](_0x3a7454(0x164),_0x4c68bc),_0x4c68bc[_0x3a7454(0xb9)];}function createSnipeTitleButton(){var _0x259629=a0_0x1638ae;const _0x4e7aa8=document['createElement']('button');_0x4e7aa8[_0x259629(0xe3)]=_0x259629(0xbb),_0x4e7aa8['id']=_0x259629(0x1c6),_0x4e7aa8['classList']['add'](_0x259629(0x1c6)),chrome[_0x259629(0x1ed)]['sendMessage']({'type':_0x259629(0x1df)},function(_0x141c85){var _0x4273f9=_0x259629;console['log']('createSnipeTitleButton\x20response:\x20',_0x141c85),!_0x141c85[_0x4273f9(0x135)]?(_0x4e7aa8['disabled']=!![],_0x4e7aa8[_0x4273f9(0xe3)]=_0x4273f9(0x11a),_0x4e7aa8[_0x4273f9(0xde)]['backgroundColor']=_0x4273f9(0x182)):_0x4e7aa8[_0x4273f9(0x1fe)]=function(){var _0x40e711=_0x4273f9;console[_0x40e711(0x133)](_0x40e711(0x202)),chrome[_0x40e711(0x1ed)][_0x40e711(0x16b)]({'type':_0x40e711(0x1df)},async function(_0x18f6f4){var _0x1ea0bc=_0x40e711;console[_0x1ea0bc(0x133)](_0x1ea0bc(0x1f6),_0x18f6f4);if(_0x18f6f4[_0x1ea0bc(0x135)]){var {useCustomTitleGenerator:_0x5e0a10}=await chrome[_0x1ea0bc(0xba)]['local']['get'](['useCustomTitleGenerator']);if(_0x5e0a10)_0x5a3291=await createCustomTitleAndAppendToTable();else{var _0x5a3291=await create10TitlesV3AndAppendToTable();_0x5a3291&&chrome[_0x1ea0bc(0x1ed)][_0x1ea0bc(0x16b)]({'type':_0x1ea0bc(0xd0),'amount':0.5});}}else alert(_0x1ea0bc(0xa9));});};});var _0x2bb8c6=createContextMenu(_0x259629(0x12c));return _0x2bb8c6[_0x259629(0x20a)]('Customize\x20Your\x20Titles',function(){var _0x52b45b=_0x259629,_0x910822=chrome[_0x52b45b(0x1ed)][_0x52b45b(0xf3)]('Prompt_Tester_page/index.html');window[_0x52b45b(0x1a0)](_0x910822,_0x52b45b(0x192));}),_0x4e7aa8[_0x259629(0xbd)](_0x259629(0x16d),function(_0x378a1e){var _0x3a0b7f=_0x259629;_0x378a1e['preventDefault'](),_0x2bb8c6[_0x3a0b7f(0xbc)](_0x378a1e);}),_0x4e7aa8;}async function createGreatTitleButton(){var _0xe7066d=a0_0x1638ae,_0x3e240a=document[_0xe7066d(0x141)](_0xe7066d(0x1d7));_0x3e240a['id']=_0xe7066d(0x97),_0x3e240a[_0xe7066d(0xee)]['add']('list-button');var _0x5bd360=chrome[_0xe7066d(0x1ed)]['getURL'](_0xe7066d(0x9e)),_0x3aea7a=document[_0xe7066d(0x141)](_0xe7066d(0x18f));_0x3aea7a[_0xe7066d(0x11d)]=_0x5bd360,_0x3aea7a[_0xe7066d(0xde)]['width']=_0xe7066d(0x1b6),_0x3aea7a['style'][_0xe7066d(0xe6)]=_0xe7066d(0x1b6),_0x3aea7a[_0xe7066d(0xde)]['marginRight']=_0xe7066d(0x1cd),_0x3e240a['appendChild'](_0x3aea7a);var _0x3d1b14=await checkMembership();return console[_0xe7066d(0x133)](_0xe7066d(0xec),_0x3d1b14),_0x3d1b14==_0xe7066d(0x1db)?(_0x3e240a[_0xe7066d(0xc7)]=![],_0x3e240a[_0xe7066d(0xe3)]+=_0xe7066d(0x174)):(_0x3e240a[_0xe7066d(0xc7)]=!![],_0x3e240a[_0xe7066d(0xe3)]+=_0xe7066d(0xe7),_0x3e240a[_0xe7066d(0xde)]['backgroundColor']='grey'),_0x3e240a[_0xe7066d(0x1fe)]=async function(){var _0xb0d0b9=_0xe7066d;_0x3e240a[_0xb0d0b9(0xc7)]=!![],_0x3e240a['style'][_0xb0d0b9(0x17c)]=_0xb0d0b9(0x182),_0x3e240a[_0xb0d0b9(0xe3)]=_0xb0d0b9(0x1c1),await generateGreatTitle(),_0x3e240a[_0xb0d0b9(0xe3)]=_0xb0d0b9(0x11e),setTimeout(function(){var _0x10c5fc=_0xb0d0b9;_0x3e240a[_0x10c5fc(0xe3)]='',_0x3e240a[_0x10c5fc(0x16e)](_0x3aea7a),_0x3e240a[_0x10c5fc(0xe3)]+=_0x10c5fc(0x1a4),_0x3e240a[_0x10c5fc(0xc7)]=![],_0x3e240a['style'][_0x10c5fc(0x17c)]=_0x10c5fc(0xd9);},0x7d0);},_0x3e240a;}async function createCleanTitleButton(){var _0x2ef940=a0_0x1638ae,_0x119412=document['createElement']('button');_0x119412['id']=_0x2ef940(0xfa),_0x119412['classList'][_0x2ef940(0x159)]('list-button'),_0x119412[_0x2ef940(0xc7)]=!![];var _0x5eeac7=chrome[_0x2ef940(0x1ed)][_0x2ef940(0xf3)]('Favicons/addons/sparkle.png'),_0x18e3c2=document['createElement'](_0x2ef940(0x18f));_0x18e3c2['src']=_0x5eeac7,_0x18e3c2[_0x2ef940(0xde)][_0x2ef940(0x1d9)]=_0x2ef940(0x1b6),_0x18e3c2[_0x2ef940(0xde)][_0x2ef940(0xe6)]=_0x2ef940(0x1b6),_0x18e3c2[_0x2ef940(0xde)][_0x2ef940(0x1ad)]=_0x2ef940(0x1cd),_0x119412[_0x2ef940(0x16e)](_0x18e3c2);var _0x37a82d=await checkMembership();return console[_0x2ef940(0x133)](_0x2ef940(0xec),_0x37a82d),_0x37a82d==_0x2ef940(0x1db)?_0x119412[_0x2ef940(0xe3)]+='<b>Clean\x20Title</b>':(_0x119412[_0x2ef940(0xc7)]=!![],_0x119412[_0x2ef940(0xe3)]+=_0x2ef940(0x1b9),_0x119412[_0x2ef940(0xde)][_0x2ef940(0x17c)]=_0x2ef940(0x182)),_0x119412[_0x2ef940(0x1fe)]=async function(){var _0x36d4e4=_0x2ef940;_0x119412[_0x36d4e4(0xc7)]=!![],_0x119412[_0x36d4e4(0xe3)]=_0x36d4e4(0x93),await generateCleanTitle(),_0x119412[_0x36d4e4(0xe3)]=_0x36d4e4(0xb7),setTimeout(function(){var _0x4fb777=_0x36d4e4;_0x119412[_0x4fb777(0xe3)]='<b>Clean\x20Title</b>',_0x119412[_0x4fb777(0xc7)]=![];},0x7d0);},_0x119412;}async function cleanTitleV2(_0x49a6a8,_0x5926d5){var _0x6452d1=a0_0x1638ae,_0x9a06e0=await fetch(chrome[_0x6452d1(0x1ed)][_0x6452d1(0xf3)](_0x6452d1(0x95)))[_0x6452d1(0x1e4)](_0x209b3e=>_0x209b3e[_0x6452d1(0xf8)]());_0x9a06e0=_0x9a06e0[_0x6452d1(0x20e)](_0x6452d1(0x140),_0x5926d5),_0x9a06e0=_0x9a06e0[_0x6452d1(0x20e)](_0x6452d1(0x1f2),_0x49a6a8),console[_0x6452d1(0x133)](_0x6452d1(0x156),_0x9a06e0);var _0x125608={'model':_0x6452d1(0x18e),'messages':[{'role':_0x6452d1(0x1ab),'content':_0x9a06e0}]},_0x2cfe89=await new Promise((_0x59afc7,_0x196ede)=>{var _0x1cefa7=_0x6452d1;chrome['runtime'][_0x1cefa7(0x16b)]({'type':'fetchData','url':'https://openai-chat-completion-djybcnnsgq-uc.a.run.app','data':_0x125608},function(_0x88e331){var _0x2ab42a=_0x1cefa7;_0x59afc7(_0x88e331[_0x2ab42a(0x15d)]);});});console['log'](_0x6452d1(0x15d),_0x2cfe89);if(!_0x2cfe89)return _0x6452d1(0x19e);return _0x2cfe89=_0x2cfe89[_0x6452d1(0x105)],_0x2cfe89=_0x2cfe89[_0x6452d1(0x14b)](_0x2cfe89[_0x6452d1(0xc3)]('{'),_0x2cfe89[_0x6452d1(0x146)]('}')+0x1),_0x2cfe89=JSON[_0x6452d1(0xf2)](_0x2cfe89),console[_0x6452d1(0x133)](_0x6452d1(0x12b),_0x2cfe89[_0x6452d1(0x12b)]),_0x2cfe89['restructured_title'];}async function cleanTitle(_0x18da9f,_0x147a21){var _0x39b077=a0_0x1638ae,_0x175d09=await fetch(chrome[_0x39b077(0x1ed)][_0x39b077(0xf3)]('prompts_json/clean_title.json'))['then'](_0xcf5c60=>_0xcf5c60[_0x39b077(0x171)]());_0x175d09[_0x39b077(0x169)]=_0x39b077(0x172)+_0x18da9f+_0x39b077(0x131)+_0x147a21,console[_0x39b077(0x133)](_0x39b077(0x1da),_0x175d09);var _0xca399=await new Promise((_0x3e8e0b,_0x67b525)=>{var _0x314292=_0x39b077;chrome[_0x314292(0x1ed)][_0x314292(0x16b)]({'type':'fetchData','url':'https://openai-function-call-djybcnnsgq-uc.a.run.app','data':_0x175d09},function(_0x14ffea){var _0x139450=_0x314292;_0x3e8e0b(_0x14ffea[_0x139450(0x15d)]);});});console[_0x39b077(0x133)](_0x39b077(0x15d),_0xca399),_0xca399=JSON[_0x39b077(0xf2)](_0xca399),console[_0x39b077(0x133)](_0x39b077(0x15d),_0xca399);var _0x441319=_0xca399[_0x39b077(0x18d)];try{var _0x46ec6b=_0xca399['brand'];_0x441319=_0x441319['replace'](new RegExp(_0x46ec6b,'gi'),''),_0x441319=_0x441319[_0x39b077(0x1de)]();}catch(_0x28a8bc){console[_0x39b077(0x133)](_0x39b077(0x1bf),_0x28a8bc);}return _0x441319;}async function createSeoTitlesButton(){var _0x110e5d=a0_0x1638ae,_0x11120b=document[_0x110e5d(0x141)]('button');_0x11120b['id']=_0x110e5d(0xb1),_0x11120b[_0x110e5d(0xee)][_0x110e5d(0x159)](_0x110e5d(0x205));var _0x5d6856=chrome[_0x110e5d(0x1ed)][_0x110e5d(0xf3)](_0x110e5d(0x9e)),_0x2c26be=document['createElement'](_0x110e5d(0x18f));_0x2c26be[_0x110e5d(0x11d)]=_0x5d6856,_0x2c26be['style'][_0x110e5d(0x1d9)]='25px',_0x2c26be[_0x110e5d(0xde)][_0x110e5d(0xe6)]=_0x110e5d(0x1b6),_0x2c26be[_0x110e5d(0xde)][_0x110e5d(0x1ad)]=_0x110e5d(0x1cd),_0x11120b[_0x110e5d(0x16e)](_0x2c26be);var _0x17b953=await checkMembership();return console[_0x110e5d(0x133)](_0x110e5d(0xec),_0x17b953),_0x17b953==_0x110e5d(0x1db)?(_0x11120b['disabled']=![],_0x11120b[_0x110e5d(0xe3)]+='<b>SEO\x20Title</b>'):(_0x11120b[_0x110e5d(0xc7)]=!![],_0x11120b[_0x110e5d(0xe3)]+='<b>SEO\x20Title</b>\x20(No\x20Membership)',_0x11120b['style'][_0x110e5d(0x17c)]='grey'),_0x11120b['onclick']=async function(){var _0x2fd4b9=_0x110e5d;_0x11120b[_0x2fd4b9(0xc7)]=!![],_0x11120b[_0x2fd4b9(0xde)][_0x2fd4b9(0x17c)]=_0x2fd4b9(0x182),_0x11120b['innerHTML']=_0x2fd4b9(0x99),await generateSeoTitles(),_0x11120b[_0x2fd4b9(0xe3)]='<b>Sniped\x20SEO\x20Title!</b>',setTimeout(function(){var _0xd93048=_0x2fd4b9;_0x11120b[_0xd93048(0xe3)]=_0xd93048(0x124),_0x11120b['disabled']=![],_0x11120b['style']['backgroundColor']='#3a86ff';},0x7d0);},_0x11120b;}async function generateCleanTitle(){var _0x544a11=a0_0x1638ae;turnOffFavicon(),startSpinner();var _0x2f03b1=getTheAsinFromHref();document[_0x544a11(0xb9)]=_0x544a11(0x8e)+_0x2f03b1+_0x544a11(0x123),changeFaviconToSniper(),console[_0x544a11(0x133)](_0x544a11(0x202));var _0x507fab=getProductDescriptionAndFeatures(),_0x311ae4=getFilteredTitle(),_0x12ef73=_0x544a11(0xaa)+_0x311ae4+_0x544a11(0xb0)+_0x507fab,_0x330190=await cleanTitle(document[_0x544a11(0x116)](_0x544a11(0xa4))[_0x544a11(0xa6)],_0x12ef73);console[_0x544a11(0x133)](_0x544a11(0x9f),_0x330190),changeFaviconOfPage(chrome[_0x544a11(0x1ed)][_0x544a11(0xf3)](_0x544a11(0x14e))),createCellWithTitle(_0x330190,'Cleaned\x20Title');var _0x1f7187=document[_0x544a11(0x162)]('listing-data-table'),_0x3d886f=_0x1f7187[_0x544a11(0x1e3)][_0x1f7187[_0x544a11(0x1e3)]['length']-0x1],_0x429008=_0x3d886f['querySelector']('.title-cell');flyInText(_0x330190,_0x429008),startFlash(),await new Promise(_0x42f5d3=>setTimeout(_0x42f5d3,0x12c)),stopFlash(),stopSpinner();var _0x2ffc3a=document['querySelector'](_0x544a11(0xa4));return _0x2ffc3a['value']=_0x330190,updateTheCharacterCountOnTextArea(),_0x330190;}async function generateSeoTitles(_0x3382fb=![]){var _0xae34a7=a0_0x1638ae;turnOffFavicon(),startSpinner();var _0x3806b1=getTheAsinFromHref();document[_0xae34a7(0xb9)]='Creating\x20The\x20Perfect\x20Title\x20-\x20'+_0x3806b1+'please\x20wait...',changeFaviconToSniper(),console['log']('clicked\x20button');var _0x4a586f=getProductDescriptionAndFeatures(),_0x5f0148=getFilteredTitle(),_0x5c7e44=getHighResProductPictures(),_0x4b7d51=getProductPictures(),_0x578f05;_0x5c7e44[_0xae34a7(0x142)]>0x0?_0x578f05=_0x5c7e44[0x0]:_0x578f05=_0x4b7d51[0x0];var _0x396e8f;if(_0x3382fb)var _0x396e8f=await buildTheSeoTitles(_0x5f0148,_0x4a586f);else var _0x39acbb={'title':_0x5f0148,'description':_0x4a586f,'imageUrl':_0x578f05},_0x396e8f=await new Promise((_0x1a098a,_0x1de508)=>{var _0x5afb14=_0xae34a7;chrome['runtime'][_0x5afb14(0x16b)]({'type':_0x5afb14(0x1e2),'options':_0x39acbb},function(_0x49e321){var _0x19511a=_0x5afb14;_0x1a098a(_0x49e321[_0x19511a(0x148)]);});});console[_0xae34a7(0x133)](_0xae34a7(0x16f),_0x396e8f),changeFaviconOfPage(chrome[_0xae34a7(0x1ed)][_0xae34a7(0xf3)](_0xae34a7(0x14e)));var _0x1fbe67='';for(var _0x733099=0x0;_0x733099<_0x396e8f[_0xae34a7(0x142)];_0x733099++){var _0x5f0148=_0x396e8f[_0x733099];_0x5f0148['length']>_0x1fbe67[_0xae34a7(0x142)]&&_0x5f0148[_0xae34a7(0x142)]<=0x50&&(_0x1fbe67=_0x5f0148);}_0x1fbe67[_0xae34a7(0x142)]==0x0&&(_0x1fbe67=_0x396e8f[0x0]);for(var _0x733099=0x0;_0x733099<_0x396e8f[_0xae34a7(0x142)];_0x733099++){var _0x537100=_0x396e8f[_0x733099];_0x1fbe67==_0x537100?createCellWithTitle(_0x537100,_0xae34a7(0xfb)):createCellWithTitle(_0x537100,_0xae34a7(0x1b3));var _0x363b2e=document[_0xae34a7(0x162)](_0xae34a7(0x1bd)),_0xa26ac9=_0x363b2e[_0xae34a7(0x1e3)][_0x363b2e['rows']['length']-0x1],_0x1e7314=_0xa26ac9['querySelector']('.title-cell');flyInText(_0x537100,_0x1e7314),startFlash(),await new Promise(_0x1f5134=>setTimeout(_0x1f5134,0x12c)),stopFlash();}stopSpinner(),startFlash(),await new Promise(_0x42218f=>setTimeout(_0x42218f,0x7d0)),stopFlash();var _0x6239e9=document[_0xae34a7(0x116)]('#the-textarea');return _0x6239e9['value']=_0x1fbe67,updateTheCharacterCountOnTextArea(),_0x1fbe67;}async function generateGreatTitle(){var _0x47cfb0=a0_0x1638ae;turnOffFavicon(),startSpinner();var _0x3e78ae=getTheAsinFromHref();document[_0x47cfb0(0xb9)]='Creating\x20The\x20Perfect\x20Title\x20-\x20'+_0x3e78ae+_0x47cfb0(0x123),changeFaviconToSniper(),console[_0x47cfb0(0x133)]('clicked\x20button');var _0x3a1928=getProductDescriptionAndFeatures(),_0x991e38=getFilteredTitle(),_0x1f3d18=await askChatGptForHighQualityTitles(_0x991e38,_0x3a1928);console[_0x47cfb0(0x133)]('titles:\x20',_0x1f3d18),changeFaviconOfPage(chrome[_0x47cfb0(0x1ed)][_0x47cfb0(0xf3)](_0x47cfb0(0x14e)));var _0x291830='';for(var _0x39cd6e=0x0;_0x39cd6e<_0x1f3d18[_0x47cfb0(0x142)];_0x39cd6e++){var _0x991e38=_0x1f3d18[_0x39cd6e];_0x991e38[_0x47cfb0(0x142)]>_0x291830[_0x47cfb0(0x142)]&&_0x991e38['length']<=0x50&&(_0x291830=_0x991e38);}_0x291830['length']==0x0&&(_0x291830=_0x1f3d18[0x0]);for(var _0x39cd6e=0x0;_0x39cd6e<_0x1f3d18[_0x47cfb0(0x142)];_0x39cd6e++){var _0x1ef642=_0x1f3d18[_0x39cd6e];_0x291830==_0x1ef642?createCellWithTitle(_0x1ef642,_0x47cfb0(0x126)):createCellWithTitle(_0x1ef642,'Great\x20Title');var _0x403d99=document[_0x47cfb0(0x162)](_0x47cfb0(0x1bd)),_0x348b5d=_0x403d99[_0x47cfb0(0x1e3)][_0x403d99['rows'][_0x47cfb0(0x142)]-0x1],_0x3c7c7a=_0x348b5d[_0x47cfb0(0x116)](_0x47cfb0(0x1dc));flyInText(_0x1ef642,_0x3c7c7a),startFlash(),await new Promise(_0x3151fd=>setTimeout(_0x3151fd,0x12c)),stopFlash();}stopSpinner(),startFlash(),await new Promise(_0x1df5b7=>setTimeout(_0x1df5b7,0x7d0)),stopFlash();var _0x115e5e=document[_0x47cfb0(0x116)](_0x47cfb0(0xa4));return _0x115e5e['value']=_0x291830,updateTheCharacterCountOnTextArea(),_0x291830;}async function createButtonToCreateTitleV4FromOpenAi(){var _0x13b1f5=a0_0x1638ae,_0x4dd569=document[_0x13b1f5(0x141)](_0x13b1f5(0x1d7));_0x4dd569[_0x13b1f5(0xe3)]=_0x13b1f5(0xe0);var _0x259dad=chrome[_0x13b1f5(0x1ed)][_0x13b1f5(0xf3)]('icons/Sniper.png');return _0x4dd569['id']=_0x13b1f5(0x1c6),_0x4dd569[_0x13b1f5(0xee)][_0x13b1f5(0x159)](_0x13b1f5(0x1c6)),_0x4dd569[_0x13b1f5(0x1fe)]=async function(){var _0x2107af=_0x13b1f5;console[_0x2107af(0x133)](_0x2107af(0x202)),chrome['runtime'][_0x2107af(0x16b)]({'type':_0x2107af(0x1df)},function(_0x34b13b){var _0x4ad9b3=_0x2107af;console[_0x4ad9b3(0x133)](_0x4ad9b3(0x1f6),_0x34b13b),_0x34b13b[_0x4ad9b3(0x135)]?(chrome[_0x4ad9b3(0x1ed)]['sendMessage']({'type':_0x4ad9b3(0xd0),'amount':0.5}),createTitleV4AndAppendToTable()):alert(_0x4ad9b3(0xa9));});},_0x4dd569;}async function createTitleV4AndAppendToTable(){var _0x2457a1=a0_0x1638ae,_0x3ea257=document[_0x2457a1(0x116)](_0x2457a1(0xc2));_0x3ea257[_0x2457a1(0xc7)]=!![],_0x3ea257[_0x2457a1(0xde)][_0x2457a1(0x17c)]=_0x2457a1(0x182),_0x3ea257[_0x2457a1(0xe3)]='<b>Sniping\x20Title...</b>',turnOffFavicon(),startSpinner();var _0x2b27b9=getTheAsinFromHref();document[_0x2457a1(0xb9)]=_0x2457a1(0xd7)+_0x2b27b9+_0x2457a1(0x123),changeFaviconToSniper();var _0x15f9bb=await getAiTitleFromApi();console['log'](_0x2457a1(0x19c),_0x15f9bb),changeFaviconOfPage(chrome[_0x2457a1(0x1ed)][_0x2457a1(0xf3)](_0x2457a1(0x14e))),await new Promise(_0xe147e4=>setTimeout(_0xe147e4,0x3e8)),console['log'](_0x2457a1(0x158)),document[_0x2457a1(0xb9)]=_0x2457a1(0x154)+_0x15f9bb+'\x22',createCellWithTitle(_0x15f9bb,_0x2457a1(0x126)),console[_0x2457a1(0x133)](_0x2457a1(0x1d8));var _0x5bfa2a=document[_0x2457a1(0x162)](_0x2457a1(0x1bd)),_0x294547=_0x5bfa2a[_0x2457a1(0x1e3)][_0x5bfa2a[_0x2457a1(0x1e3)]['length']-0x1],_0x284737=_0x294547['querySelector'](_0x2457a1(0x1dc));console[_0x2457a1(0x133)]('finished\x20tableRow.querySelector(.title-cell);');!_0x15f9bb&&(_0x15f9bb=_0x2457a1(0x1e7));flyInText(_0x15f9bb,_0x284737),console['log'](_0x2457a1(0x20b));var _0x24eff0=document['querySelector'](_0x2457a1(0xa4));return _0x24eff0[_0x2457a1(0xa6)]=_0x15f9bb,console['log'](_0x2457a1(0x139)),stopSpinner(),startFlash(),await new Promise(_0xe77894=>setTimeout(_0xe77894,0x7d0)),stopFlash(),_0x3ea257[_0x2457a1(0xe3)]='<b>Sniped\x20Title!</b>',_0x3ea257['style'][_0x2457a1(0x17c)]='#3a86ff',setTimeout(function(){var _0x4fb7e8=_0x2457a1;_0x3ea257[_0x4fb7e8(0xe3)]=_0x4fb7e8(0x1bc),_0x3ea257[_0x4fb7e8(0xc7)]=![];},0x7d0),_0x15f9bb;}async function create10TitlesV3AndAppendToTable(_0x4813db,_0x24bced){var _0x3148b5=a0_0x1638ae,_0x388cca=document['querySelector'](_0x3148b5(0xc2));_0x388cca[_0x3148b5(0xc7)]=!![],_0x388cca['style']['backgroundColor']='grey',_0x388cca[_0x3148b5(0xe3)]=_0x3148b5(0x8b),turnOffFavicon(),startSpinner(),console[_0x3148b5(0x133)](_0x3148b5(0xe4));if(!_0x4813db)try{_0x4813db=getFilteredTitle();}catch(_0x9a0acd){if(!product_data[_0x3148b5(0x1f0)])throw new Error(_0x3148b5(0x1a5));else _0x4813db=product_data[_0x3148b5(0x1f0)],_0x24bced=product_data[_0x3148b5(0xb2)];}!_0x24bced&&(_0x24bced=getProductDescriptionAndFeatures());var _0x392f72=_0x3148b5(0x15c),{domain:_0x30cb5a}=await chrome[_0x3148b5(0xba)][_0x3148b5(0xaf)]['get'](_0x3148b5(0x20f));function _0x4a9c76(_0x3a7514){var _0x1f585b=_0x3148b5;if(_0x3a7514=='de')return _0x1f585b(0x119);else{if(_0x3a7514==_0x1f585b(0x102))return'english';else{if(_0x3a7514=='co.uk')return'english';else{if(_0x3a7514=='fr')return _0x1f585b(0xcf);else{if(_0x3a7514=='it')return _0x1f585b(0xed);else{if(_0x3a7514=='es')return _0x1f585b(0x10a);else{if(_0x3a7514==_0x1f585b(0x176))return _0x1f585b(0x183);else return _0x3a7514=='ca'?_0x1f585b(0x183):_0x1f585b(0x183);}}}}}}}var _0x512129=_0x4a9c76(_0x30cb5a),_0x3ecece=await postToServer(_0x392f72,{'request_type':'generate_10_titles','product_description':_0x24bced,'product_title':_0x4813db,'language':_0x512129});console['log'](_0x3148b5(0x144),_0x3ecece);var _0x4e8668=_0x3ecece;if(!_0x4e8668)return createCellWithTitle(_0x3148b5(0x18b),'Perfect\x20Title'),stopSpinner(),startFlash(),await new Promise(_0x5b5fc5=>setTimeout(_0x5b5fc5,0x7d0)),stopFlash(),_0x388cca[_0x3148b5(0xe3)]=_0x3148b5(0x1f7),_0x388cca[_0x3148b5(0xde)]['backgroundColor']=_0x3148b5(0x17f),setTimeout(function(){var _0x5be922=_0x3148b5;_0x388cca[_0x5be922(0xe3)]=_0x5be922(0x1bc),_0x388cca[_0x5be922(0xc7)]=![];},0x7d0),null;!Array[_0x3148b5(0x207)](_0x4e8668)&&(_0x4e8668=[_0x4e8668]);_0x4e8668=_0x4e8668['filter'](function(_0x3f7bd9){var _0xb068b=_0x3148b5;return _0x3f7bd9[_0xb068b(0x1de)]()[_0xb068b(0x142)]>0x0;})[_0x3148b5(0x88)](function(_0x41f61d){var _0x3d00e5=_0x3148b5;return _0x41f61d[_0x3d00e5(0x1de)]();});var _0xd34f5a='';for(var _0x4a0e86=0x0;_0x4a0e86<_0x4e8668[_0x3148b5(0x142)];_0x4a0e86++){var _0x4813db=_0x4e8668[_0x4a0e86];_0x4813db[_0x3148b5(0x142)]>_0xd34f5a[_0x3148b5(0x142)]&&_0x4813db['length']<=0x50&&(_0xd34f5a=_0x4813db);}_0xd34f5a[_0x3148b5(0x142)]==0x0&&(_0xd34f5a=_0x4e8668[0x0]);for(_0x4a0e86=0x0;_0x4a0e86<_0x4e8668[_0x3148b5(0x142)];_0x4a0e86++){var _0x4813db=_0x4e8668[_0x4a0e86];console['log'](_0x3148b5(0xd3),_0x4813db);if(_0x4813db['length']>0x5a)continue;_0xd34f5a==_0x4813db?createCellWithTitle(_0x4813db,_0x3148b5(0x126)):createCellWithTitle(_0x4813db,_0x3148b5(0xac));var _0xe2226f=document['getElementById'](_0x3148b5(0x1bd)),_0x15af5e=_0xe2226f[_0x3148b5(0x1e3)][_0xe2226f[_0x3148b5(0x1e3)][_0x3148b5(0x142)]-0x1],_0x45cd27=_0x15af5e[_0x3148b5(0x116)](_0x3148b5(0x1dc));flyInText(_0x4813db,_0x45cd27),startFlash(),await new Promise(_0x5fd7c2=>setTimeout(_0x5fd7c2,0x12c)),stopFlash();}var _0x55a597=document['querySelector'](_0x3148b5(0xa4));!_0x55a597&&(_0x55a597=document[_0x3148b5(0x116)](_0x3148b5(0xcc)));_0x55a597[_0x3148b5(0xa6)]=_0xd34f5a;try{updateTheCharacterCountOnTextArea();}catch(_0x1fc54c){}return stopSpinner(),startFlash(),await new Promise(_0x51bcc2=>setTimeout(_0x51bcc2,0x7d0)),stopFlash(),_0x388cca[_0x3148b5(0xe3)]=_0x3148b5(0x1f7),_0x388cca[_0x3148b5(0xde)][_0x3148b5(0x17c)]='#3a86ff',setTimeout(function(){var _0x5934b4=_0x3148b5;_0x388cca[_0x5934b4(0xe3)]=_0x5934b4(0x1bc),_0x388cca[_0x5934b4(0xc7)]=![];},0x7d0),_0xd34f5a;}async function createCustomTitleAndAppendToTable(_0x326d51,_0x2e5c40){var _0xa5fe57=a0_0x1638ae,_0x531c2b=document[_0xa5fe57(0x116)](_0xa5fe57(0xc2));_0x531c2b[_0xa5fe57(0xc7)]=!![],_0x531c2b[_0xa5fe57(0xde)][_0xa5fe57(0x17c)]='grey',_0x531c2b[_0xa5fe57(0xe3)]=_0xa5fe57(0x8b),turnOffFavicon(),startSpinner(),console[_0xa5fe57(0x133)]('create10TitlesV3AndAppendToTable');if(!_0x326d51)try{_0x326d51=getFilteredTitle();}catch(_0x5b8a20){if(!product_data[_0xa5fe57(0x1f0)])throw new Error(_0xa5fe57(0x1a5));else _0x326d51=product_data[_0xa5fe57(0x1f0)],_0x2e5c40=product_data[_0xa5fe57(0xb2)];}!_0x2e5c40&&(_0x2e5c40=getProductDescriptionAndFeatures());var _0x5a3b9d=_0x326d51+'\x0a\x0a'+_0x2e5c40,_0x5bb94b=await createCustomTitle(_0x5a3b9d);if(!_0x5bb94b||_0x5bb94b[_0xa5fe57(0x1d0)](_0xa5fe57(0x11c)))return createCellWithTitle(_0xa5fe57(0x18b),_0xa5fe57(0x195)),stopSpinner(),startFlash(),await new Promise(_0x497c30=>setTimeout(_0x497c30,0x7d0)),stopFlash(),_0x531c2b[_0xa5fe57(0xe3)]=_0xa5fe57(0x1f7),_0x531c2b[_0xa5fe57(0xde)][_0xa5fe57(0x17c)]=_0xa5fe57(0x17f),setTimeout(function(){_0x531c2b['innerHTML']='<b>Snipe\x20Title</b>',_0x531c2b['disabled']=![];},0x7d0),null;!Array[_0xa5fe57(0x207)](_0x5bb94b)&&(_0x5bb94b=[_0x5bb94b]);_0x5bb94b=_0x5bb94b[_0xa5fe57(0xce)](function(_0x3f7e3a){var _0x373ce4=_0xa5fe57;return _0x3f7e3a[_0x373ce4(0x1de)]()['length']>0x0;})[_0xa5fe57(0x88)](function(_0x3d1ac8){var _0x1c8619=_0xa5fe57;return _0x3d1ac8[_0x1c8619(0x1de)]();});var _0x3805c5='';for(var _0x5b0b90=0x0;_0x5b0b90<_0x5bb94b[_0xa5fe57(0x142)];_0x5b0b90++){var _0x326d51=_0x5bb94b[_0x5b0b90];_0x326d51[_0xa5fe57(0x142)]>_0x3805c5[_0xa5fe57(0x142)]&&_0x326d51[_0xa5fe57(0x142)]<=0x50&&(_0x3805c5=_0x326d51);}_0x3805c5[_0xa5fe57(0x142)]==0x0&&(_0x3805c5=_0x5bb94b[0x0]);var _0x326d51=_0x5bb94b[0x0];createCellWithTitle(_0x326d51,'Custom\x20Title');var _0x588d18=document[_0xa5fe57(0x162)]('listing-data-table'),_0x215360=_0x588d18['rows'][_0x588d18['rows'][_0xa5fe57(0x142)]-0x1],_0x2eef98=_0x215360[_0xa5fe57(0x116)](_0xa5fe57(0x1dc));flyInText(_0x326d51,_0x2eef98),startFlash(),await new Promise(_0x21052c=>setTimeout(_0x21052c,0x12c)),stopFlash();var _0x65c8e3=document[_0xa5fe57(0x116)]('#the-textarea');!_0x65c8e3&&(_0x65c8e3=document[_0xa5fe57(0x116)]('#listTitle'));_0x65c8e3[_0xa5fe57(0xa6)]=_0x326d51;try{updateTheCharacterCountOnTextArea();}catch(_0x4294e6){}return stopSpinner(),startFlash(),await new Promise(_0x48df04=>setTimeout(_0x48df04,0x7d0)),stopFlash(),_0x531c2b['innerHTML']='<b>Sniped\x20Title!</b>',_0x531c2b['style'][_0xa5fe57(0x17c)]=_0xa5fe57(0x17f),setTimeout(function(){var _0x71e922=_0xa5fe57;_0x531c2b[_0x71e922(0xe3)]='<b>Snipe\x20Title</b>',_0x531c2b[_0x71e922(0xc7)]=![];},0x7d0),_0x3805c5;}async function createCustomTitle(_0x20f3cf,_0x20375d=null){var _0x23cb29=a0_0x1638ae;if(!_0x20375d){var {custom_title_prompt:_0x26f59e}=await chrome[_0x23cb29(0xba)][_0x23cb29(0xaf)][_0x23cb29(0x1ea)](_0x23cb29(0x1f4));_0x20375d=_0x26f59e,!_0x20375d&&(_0x20375d=_0x23cb29(0x19f),await chrome[_0x23cb29(0xba)][_0x23cb29(0xaf)]['set']({'custom_title_prompt':_0x20375d}));}var {email:_0x30cbf7}=await chrome[_0x23cb29(0xba)]['local'][_0x23cb29(0x1ea)](['email']),_0x3afc92={'system_message':_0x20375d,'openai_model':_0x23cb29(0x1e1),'model_definition':{'title':_0x23cb29(0x9a)},'email':_0x30cbf7};const _0xa51355={..._0x3afc92,'user_message':_0x20f3cf};console['log'](_0x23cb29(0xb5),_0xa51355);try{var _0x2c7188=await postToServer('https://us-central1-ecomsniper-cb046.cloudfunctions.net/structured_output',_0xa51355);return console[_0x23cb29(0x133)]('Response\x20from\x20server:',_0x2c7188),_0x2c7188[_0x23cb29(0xea)]?_0x2c7188['result'][_0x23cb29(0xb9)]:'Error\x20-\x20'+_0x2c7188[_0x23cb29(0x96)];}catch(_0x238086){return _0x23cb29(0x143)+_0x238086[_0x23cb29(0xd1)];}}function handleResponse(_0x35409a){var _0x351bc2=a0_0x1638ae,_0x586824=_0x35409a[_0x351bc2(0xfd)][0x0][_0x351bc2(0xf8)];_0x586824=_0x586824[_0x351bc2(0x1de)](),_0x586824=_0x586824[_0x351bc2(0x20e)](/\n/g,'\x20'),console['log']('titleFromAi:\x20',_0x586824);var _0x2bb414=document[_0x351bc2(0x162)](_0x351bc2(0x1bd)),_0x41236c=createRow(_0x2bb414);createCell(_0x2bb414,{'rowNumber':_0x41236c,'cellValue':_0x41236c,'headerName':_0x351bc2(0x108)}),createCell(_0x2bb414,{'rowNumber':_0x41236c,'cellValue':_0x586824,'headerName':_0x351bc2(0x194)}),createCell(_0x2bb414,{'rowNumber':_0x41236c,'cellValue':_0x351bc2(0x181),'headerName':_0x351bc2(0x1f5)}),createCell(_0x2bb414,{'rowNumber':_0x41236c,'cellValue':_0x586824['length'],'headerName':'Total\x20Characters'});var _0x24fd2f=createButtonToUpdateTextArea({'buttonInnerText':_0x351bc2(0x155),'textAreaSelector':_0x351bc2(0xa4),'valueToSet':_0x586824,'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x2bb414,{'button':_0x24fd2f,'rowNumber':_0x41236c,'headerName':_0x351bc2(0x98)});}async function createCellWithTitle(_0x2842f7,_0x3a4c25){var _0x292c03=a0_0x1638ae,_0x4b0ba3=document[_0x292c03(0x162)](_0x292c03(0x1bd)),_0xd3961c=createRow(_0x4b0ba3);createCell(_0x4b0ba3,{'rowNumber':_0xd3961c,'cellValue':_0xd3961c,'headerName':_0x292c03(0x108)}),createCell(_0x4b0ba3,{'rowNumber':_0xd3961c,'cellValue':_0x2842f7,'headerName':_0x292c03(0x194)}),createCell(_0x4b0ba3,{'rowNumber':_0xd3961c,'cellValue':_0x3a4c25,'headerName':_0x292c03(0x1f5)}),createCell(_0x4b0ba3,{'rowNumber':_0xd3961c,'cellValue':_0x2842f7[_0x292c03(0x142)],'headerName':_0x292c03(0x1b2)});var _0xe17882=createButtonToUpdateTextArea({'buttonInnerText':_0x292c03(0x155),'textAreaSelector':_0x292c03(0xa4),'valueToSet':_0x2842f7,'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x4b0ba3,{'button':_0xe17882,'rowNumber':_0xd3961c,'headerName':_0x292c03(0x98)});}async function getAiGeneratedTitle(_0x30424c){return new Promise((_0x1f6c59,_0x176cc0)=>{var _0x168b03=a0_0x428e;chrome[_0x168b03(0x1ed)][_0x168b03(0x16b)]({'type':_0x168b03(0x1fa),'data':_0x30424c},function(_0x5f492a){var _0x2bfc5f=_0x168b03;console[_0x2bfc5f(0x133)](_0x2bfc5f(0x1f6),_0x5f492a),_0x1f6c59(_0x5f492a);});});}async function createButtonAndSendMessage(_0x5340d7){var _0x376fdf=a0_0x1638ae,_0xad2ece=document[_0x376fdf(0x141)](_0x376fdf(0x1d7));_0xad2ece[_0x376fdf(0xe3)]=_0x376fdf(0x184)+_0x5340d7,_0xad2ece['id']=_0x376fdf(0x161)+_0x5340d7,_0xad2ece[_0x376fdf(0xee)][_0x376fdf(0x159)]('list-button');var _0x517226=createButtonToCreateTitleV4FromOpenAi();document[_0x376fdf(0x1bb)][_0x376fdf(0x113)](_0xad2ece),_0xad2ece[_0x376fdf(0xbd)]('click',function(){var _0x1e1974=_0x376fdf;console[_0x1e1974(0x133)](_0x1e1974(0xa0)),addSkuToSkuListAndSaveToLocalStorage(product_data[_0x1e1974(0x189)]),product_data[_0x1e1974(0x1a2)]=document[_0x1e1974(0x162)](_0x1e1974(0x1b4))[_0x1e1974(0xa6)],console[_0x1e1974(0x133)](_0x1e1974(0x9c),product_data),product_data[_0x1e1974(0x8f)]=selectedImage,product_data[_0x1e1974(0x153)]=document[_0x1e1974(0x162)]('sell-price')['value'],product_data[_0x1e1974(0xc0)]=document[_0x1e1974(0x162)](_0x1e1974(0x187))['value'];var _0x241b56=chrome[_0x1e1974(0x1ed)]['getURL'](_0x1e1974(0x18c));chrome[_0x1e1974(0x1ed)][_0x1e1974(0x16b)]({'type':_0x1e1974(0x1a7)+_0x5340d7,'product_data':product_data},function(_0x2cca2d){var _0x2d1a0a=_0x1e1974;console['log'](_0x2cca2d[_0x2d1a0a(0x20d)]);}),changeFaviconOfPage(_0x241b56);});}async function listItemsForFreeUsers(){var _0x4b9d5d=a0_0x1638ae;await listProductData(),chrome[_0x4b9d5d(0x1ed)][_0x4b9d5d(0x16b)]({'type':_0x4b9d5d(0x1df)},function(_0x2f0dc5){var _0x3b4683=_0x4b9d5d;console[_0x3b4683(0x133)](_0x3b4683(0x1f6),_0x2f0dc5),_0x2f0dc5['creditsAvailable']?(chrome[_0x3b4683(0x1ed)]['sendMessage']({'type':_0x3b4683(0xd0),'amount':0.2}),product_data[_0x3b4683(0x145)]=_0x3b4683(0xa3),chrome[_0x3b4683(0x1ed)]['sendMessage']({'type':_0x3b4683(0x16a),'product_data':product_data},function(_0x16c25a){var _0x420dea=_0x3b4683;console[_0x420dea(0x133)](_0x16c25a[_0x420dea(0x20d)]);})):alert(_0x3b4683(0x1a3));});}async function listItemsWithChatGpt(){var _0x5c53bd=a0_0x1638ae;await listProductData(),chrome[_0x5c53bd(0x1ed)][_0x5c53bd(0x16b)]({'type':'checkCredits'},function(_0x48a8af){var _0x5e77e6=_0x5c53bd;console[_0x5e77e6(0x133)](_0x5e77e6(0x1f6),_0x48a8af),_0x48a8af[_0x5e77e6(0x135)]?(chrome[_0x5e77e6(0x1ed)]['sendMessage']({'type':'deductCredits','amount':0.5}),product_data[_0x5e77e6(0x145)]=_0x5e77e6(0x211),chrome[_0x5e77e6(0x1ed)]['sendMessage']({'type':_0x5e77e6(0x16a),'product_data':product_data},function(_0x4f48a4){var _0x2759c9=_0x5e77e6;console[_0x2759c9(0x133)](_0x4f48a4[_0x2759c9(0x20d)]);})):alert('You\x20have\x20no\x20credits\x20left.\x20Please\x20purchase\x20a\x20membership\x20to\x20continue\x20listing.');});}async function listItemsForPaidUsers(){var _0x3ea227=a0_0x1638ae;await listProductData(),chrome[_0x3ea227(0x1ed)][_0x3ea227(0x16b)]({'type':_0x3ea227(0x1df)},function(_0x3d31a9){var _0x5dfde7=_0x3ea227;console[_0x5dfde7(0x133)](_0x5dfde7(0x1f6),_0x3d31a9),_0x3d31a9[_0x5dfde7(0x135)]?(chrome[_0x5dfde7(0x1ed)]['sendMessage']({'type':_0x5dfde7(0xd0),'amount':0.5}),product_data[_0x5dfde7(0x145)]='paid',chrome[_0x5dfde7(0x1ed)][_0x5dfde7(0x16b)]({'type':_0x5dfde7(0x16a),'product_data':product_data},function(_0x5cbaa7){var _0x1ab7bc=_0x5dfde7;console[_0x1ab7bc(0x133)](_0x5cbaa7[_0x1ab7bc(0x20d)]);}),addSkuToSkuListAndSaveToLocalStorage(product_data[_0x5dfde7(0x189)])):alert(_0x5dfde7(0x101));});}async function listProductData(){var _0x49daae=a0_0x1638ae;addSkuToSkuListAndSaveToLocalStorage(product_data['sku']),product_data[_0x49daae(0x1a2)]=document[_0x49daae(0x162)](_0x49daae(0x1b4))[_0x49daae(0xa6)],console[_0x49daae(0x133)](_0x49daae(0x9c),product_data);while(!selectedImage){await new Promise(_0x2c6a4f=>setTimeout(_0x2c6a4f,0x3e8));}product_data[_0x49daae(0x8f)]=selectedImage,product_data['custom_price']=document['getElementById']('sell-price')[_0x49daae(0xa6)],product_data[_0x49daae(0xc0)]=document[_0x49daae(0x162)](_0x49daae(0x187))['value'];var _0x10aaaa=chrome[_0x49daae(0x1ed)]['getURL'](_0x49daae(0x18c));changeFaviconOfPage(_0x10aaaa);var {shouldGetGspr:_0x18e7ed}=await chrome[_0x49daae(0xba)][_0x49daae(0xaf)][_0x49daae(0x1ea)](_0x49daae(0x208));if(_0x18e7ed){console[_0x49daae(0x133)](_0x49daae(0xa1),_0x18e7ed);var {manufacturerInfo:_0x43b293,responsiblePersonEU:_0x3ed9b2,manufacturerInfoText:_0x15b7f4,responsiblePersonEUText:_0x47afaf}=await fetchGSPR();console[_0x49daae(0x133)](_0x49daae(0x17e),_0x43b293),console['log'](_0x49daae(0xb4),_0x3ed9b2);if(_0x43b293[_0x49daae(0x12f)]==''||_0x43b293[_0x49daae(0x12f)]==null){document[_0x49daae(0xb9)]=_0x49daae(0x1ce);throw new Error('Manufacturer\x20country\x20is\x20empty!');}product_data[_0x49daae(0xc8)]=_0x43b293,product_data[_0x49daae(0x19d)]=_0x3ed9b2,product_data[_0x49daae(0x17a)]=_0x15b7f4,product_data[_0x49daae(0x151)]=_0x47afaf,await new Promise(_0xea8cf=>setTimeout(_0xea8cf,0x3e8));}}function createFreeListingButton(){var _0x3304dc=a0_0x1638ae,_0x4a8c0b=document['createElement']('button');return _0x4a8c0b[_0x3304dc(0xe3)]=_0x3304dc(0xbe),_0x4a8c0b['id']='standard-listing-button',_0x4a8c0b[_0x3304dc(0xee)][_0x3304dc(0x159)](_0x3304dc(0x205)),chrome[_0x3304dc(0x1ed)]['sendMessage']({'type':'checkFreeCredits'},function(_0x14202b){var _0x2cdfa4=_0x3304dc;console['log'](_0x2cdfa4(0x129),_0x14202b),!_0x14202b[_0x2cdfa4(0x135)]?(_0x4a8c0b['disabled']=!![],_0x4a8c0b['innerHTML']=_0x2cdfa4(0x103),_0x4a8c0b[_0x2cdfa4(0xde)][_0x2cdfa4(0x17c)]='grey'):_0x4a8c0b[_0x2cdfa4(0xbd)](_0x2cdfa4(0x1b8),function(){listItemsForFreeUsers();});}),_0x4a8c0b;}function createImportButton(){var _0x25e2b3=a0_0x1638ae,_0x3308ae=document[_0x25e2b3(0x141)](_0x25e2b3(0x1d7));return _0x3308ae['innerHTML']=_0x25e2b3(0xf6),_0x3308ae['id']='import-button',_0x3308ae[_0x25e2b3(0xee)]['add'](_0x25e2b3(0x205)),_0x3308ae['addEventListener']('click',async function(){var _0x1bdc0f=_0x25e2b3,_0x8ed123=navigator[_0x1bdc0f(0x173)][_0x1bdc0f(0x1c7)](),_0x8ed123=await navigator[_0x1bdc0f(0x173)][_0x1bdc0f(0x1c7)]();console[_0x1bdc0f(0x133)](_0x1bdc0f(0x10d),_0x8ed123);var _0x5ed563=JSON[_0x1bdc0f(0xf2)](_0x8ed123);console[_0x1bdc0f(0x133)](_0x1bdc0f(0x1cf),_0x5ed563);var _0x14e653=_0x5ed563[_0x1bdc0f(0x206)];try{_0x14e653=_0x14e653[_0x1bdc0f(0x20e)](/[^0-9.]/g,'');}catch(_0x18bcaa){console[_0x1bdc0f(0x133)](_0x1bdc0f(0x1bf),_0x18bcaa);}var {sniper_price_markdown:_0x59bc98}=await chrome['storage'][_0x1bdc0f(0xaf)][_0x1bdc0f(0x1ea)](['sniper_price_markdown']);_0x14e653=_0x14e653-_0x59bc98,_0x14e653=_0x14e653[_0x1bdc0f(0xc1)](0x2),document['getElementById']('sell-price')[_0x1bdc0f(0xa6)]=_0x14e653,createCellWithTitle(_0x5ed563['title'],_0x1bdc0f(0x1e8)),document[_0x1bdc0f(0x162)](_0x1bdc0f(0xca))[_0x1bdc0f(0xa6)]=_0x5ed563[_0x1bdc0f(0x180)];var _0x41b878=document[_0x1bdc0f(0x162)](_0x1bdc0f(0x1bd)),_0x519050=_0x41b878[_0x1bdc0f(0x1e3)][_0x41b878['rows'][_0x1bdc0f(0x142)]-0x1],_0x1f6dc0=_0x519050[_0x1bdc0f(0x116)](_0x1bdc0f(0x1dc));flyInText(_0x5ed563[_0x1bdc0f(0xb9)],_0x1f6dc0),startFlash(),await new Promise(_0x44c12f=>setTimeout(_0x44c12f,0x12c)),stopFlash();var _0x2c5bc1=document['querySelector'](_0x1bdc0f(0xa4));_0x2c5bc1[_0x1bdc0f(0xa6)]=_0x5ed563['title'];var _0x2aecf1=await checkMembership();console[_0x1bdc0f(0x133)](_0x1bdc0f(0xec),_0x2aecf1),_0x2aecf1=='ultimate'&&(document[_0x1bdc0f(0x162)]('snipe-listing-button')[_0x1bdc0f(0xc7)]=![],document[_0x1bdc0f(0x162)]('clean-title-button')[_0x1bdc0f(0xc7)]=![],document[_0x1bdc0f(0x162)](_0x1bdc0f(0x125))[_0x1bdc0f(0xc7)]=!![]);}),_0x3308ae;}function createChatGptListingButton(){var _0x424208=a0_0x1638ae,_0x185ca0=document[_0x424208(0x141)](_0x424208(0x1d7));_0x185ca0['id']='chat-listing-button',_0x185ca0[_0x424208(0xee)][_0x424208(0x159)](_0x424208(0x205));var _0x42e0b1=chrome[_0x424208(0x1ed)][_0x424208(0xf3)](_0x424208(0x9e)),_0x3a40a0=document[_0x424208(0x141)]('img');return _0x3a40a0[_0x424208(0x11d)]=_0x42e0b1,_0x3a40a0[_0x424208(0xde)][_0x424208(0x1d9)]='25px',_0x3a40a0[_0x424208(0xde)][_0x424208(0xe6)]='25px',_0x3a40a0[_0x424208(0xde)][_0x424208(0x1ad)]=_0x424208(0x1cd),_0x185ca0[_0x424208(0x16e)](_0x3a40a0),_0x185ca0[_0x424208(0xe3)]+=_0x424208(0x1c3),chrome[_0x424208(0x1ed)][_0x424208(0x16b)]({'type':_0x424208(0x13a)},function(_0x540e2c){var _0x504ebd=_0x424208;console[_0x504ebd(0x133)](_0x504ebd(0x129),_0x540e2c),!_0x540e2c['creditsAvailable']?(_0x185ca0['disabled']=!![],_0x185ca0[_0x504ebd(0xe3)]=_0x504ebd(0x1fb),_0x185ca0[_0x504ebd(0xde)][_0x504ebd(0x17c)]=_0x504ebd(0x182)):_0x185ca0[_0x504ebd(0xbd)](_0x504ebd(0x1b8),function(){listItemsWithChatGpt();});}),_0x185ca0;}function createSnipeListingButton(){var _0xa1e15d=a0_0x1638ae,_0x1e3369=document[_0xa1e15d(0x141)](_0xa1e15d(0x1d7));return _0x1e3369[_0xa1e15d(0xe3)]='Snipe-List',_0x1e3369['id']=_0xa1e15d(0x163),_0x1e3369[_0xa1e15d(0xee)][_0xa1e15d(0x159)]('list-button'),_0x1e3369[_0xa1e15d(0xc7)]=!![],chrome[_0xa1e15d(0x1ed)][_0xa1e15d(0x16b)]({'type':_0xa1e15d(0x1df)},function(_0x472af4){var _0xffd6e9=_0xa1e15d;console[_0xffd6e9(0x133)](_0xffd6e9(0x129),_0x472af4),!_0x472af4[_0xffd6e9(0x135)]?(_0x1e3369[_0xffd6e9(0xc7)]=!![],_0x1e3369[_0xffd6e9(0xe3)]=_0xffd6e9(0x177),_0x1e3369[_0xffd6e9(0xde)]['backgroundColor']='grey'):_0x1e3369['addEventListener'](_0xffd6e9(0x1b8),async function(){var _0x392c2b=_0xffd6e9;await listProductData(),product_data['listingType']=_0x392c2b(0xd6);var _0x4cb6fc=document['getElementById'](_0x392c2b(0xca))['value'];chrome[_0x392c2b(0x1ed)]['sendMessage']({'type':_0x392c2b(0xd0),'amount':0.2}),chrome[_0x392c2b(0x1ed)]['sendMessage']({'type':_0x392c2b(0x1e9),'productData':product_data,'similarItemNumber':_0x4cb6fc},function(_0x526719){var _0x3937af=_0x392c2b;console[_0x3937af(0x133)](_0x526719['farewell']);});});}),_0x1e3369;}function createPaidListingButton(){var _0x188d8d=a0_0x1638ae,_0x105a0=document[_0x188d8d(0x141)](_0x188d8d(0x1d7));_0x105a0[_0x188d8d(0xe3)]=_0x188d8d(0x14a),_0x105a0['id']=_0x188d8d(0x125),_0x105a0['classList'][_0x188d8d(0x159)]('list-button'),chrome[_0x188d8d(0x1ed)][_0x188d8d(0x16b)]({'type':_0x188d8d(0x1df)},function(_0x1484b1){var _0x11630c=_0x188d8d;console['log']('createSnipeTitleButton\x20response:\x20',_0x1484b1),!_0x1484b1[_0x11630c(0x135)]?(_0x105a0['disabled']=!![],_0x105a0[_0x11630c(0xe3)]=_0x11630c(0xdb),_0x105a0['style'][_0x11630c(0x17c)]=_0x11630c(0x182)):_0x105a0['addEventListener']('click',function(){listItemsForPaidUsers();});});var _0x54bdd9=createContextMenu('my-custom-context-menu');return _0x54bdd9[_0x188d8d(0x20a)](_0x188d8d(0x1a1),function(){listItemToPoshmark();}),_0x54bdd9[_0x188d8d(0x20a)]('List\x20To\x20Shopify',function(){listItemToShopify();}),_0x54bdd9[_0x188d8d(0x20a)](_0x188d8d(0x1fc),function(){removeShopifyCredentials();}),_0x105a0['addEventListener'](_0x188d8d(0x16d),function(_0x279881){var _0x2e8b01=_0x188d8d;_0x279881[_0x2e8b01(0xdf)](),_0x54bdd9[_0x2e8b01(0xbc)](_0x279881);}),_0x105a0;}async function removeShopifyCredentials(){var _0x23db84=a0_0x1638ae;await chrome[_0x23db84(0xba)][_0x23db84(0xaf)][_0x23db84(0x1d3)](_0x23db84(0x1d2)),await chrome[_0x23db84(0xba)]['local']['remove'](_0x23db84(0xb6)),alert('Shopify\x20credentials\x20removed\x20successfully!');}async function listItemToShopify(){var _0x4549ef=a0_0x1638ae;console[_0x4549ef(0x133)](_0x4549ef(0x210)),await listProductData();var _0x118f23=await checkMembership();if(_0x118f23!=_0x4549ef(0x1db)){alert('Please\x20upgrade\x20to\x20Ultimate\x20membership\x20to\x20list\x20to\x20Poshmark');return;}var _0x48a808=await checkIfCreditsAreAvailable();if(!_0x48a808){alert(_0x4549ef(0x9d));return;}var {shopify_store_url:_0x45121d}=await chrome[_0x4549ef(0xba)]['local'][_0x4549ef(0x1ea)](_0x4549ef(0x1d2)),{shopify_admin_access_token:_0x44dd64}=await chrome[_0x4549ef(0xba)][_0x4549ef(0xaf)]['get']('shopify_admin_access_token');(!_0x45121d||!_0x44dd64)&&(alert(_0x4549ef(0x166)),await createInputPrompt());chrome['runtime'][_0x4549ef(0x16b)]({'type':_0x4549ef(0xd0),'amount':0.5});var {response:_0x41b2d3}=await new Promise((_0xb65bd7,_0x4759d0)=>{var _0x2ac0cd=_0x4549ef;chrome[_0x2ac0cd(0x1ed)][_0x2ac0cd(0x16b)]({'type':'list_to_shopify','product_data':product_data},function(_0x1742e7){_0xb65bd7(_0x1742e7);});});console[_0x4549ef(0x133)]('response:\x20',_0x41b2d3),alert(_0x41b2d3['message']);}async function createInputPrompt(){return new Promise(_0x4a1a6e=>{var _0x755c6f=a0_0x428e;const _0x322c75=document[_0x755c6f(0x141)](_0x755c6f(0x15e));_0x322c75['style'][_0x755c6f(0x1d6)]=_0x755c6f(0xf5),_0x322c75[_0x755c6f(0xde)]['top']='50%',_0x322c75[_0x755c6f(0xde)][_0x755c6f(0x197)]='50%',_0x322c75[_0x755c6f(0xde)][_0x755c6f(0x160)]='translate(-50%,\x20-50%)',_0x322c75[_0x755c6f(0xde)]['padding']='20px',_0x322c75[_0x755c6f(0xde)]['backgroundColor']='white',_0x322c75[_0x755c6f(0xde)][_0x755c6f(0x1c2)]=_0x755c6f(0x1cb),_0x322c75[_0x755c6f(0xde)][_0x755c6f(0x191)]=_0x755c6f(0x19a);const _0xfdaa10=document['createElement'](_0x755c6f(0x118));_0xfdaa10['textContent']=_0x755c6f(0x1ef);const _0x4a4fb1=document[_0x755c6f(0x141)](_0x755c6f(0xad));_0x4a4fb1[_0x755c6f(0x193)]='text',_0x4a4fb1[_0x755c6f(0x168)]=_0x755c6f(0x12d),_0x4a4fb1['style']['width']=_0x755c6f(0xbf),_0x4a4fb1[_0x755c6f(0xde)]['marginBottom']=_0x755c6f(0x13d);const _0x51ed50=document[_0x755c6f(0x141)](_0x755c6f(0x118));_0x51ed50[_0x755c6f(0xd4)]=_0x755c6f(0x15a);const _0x3d789e=document['createElement'](_0x755c6f(0xad));_0x3d789e[_0x755c6f(0x193)]=_0x755c6f(0x1c5),_0x3d789e[_0x755c6f(0x168)]='Your\x20admin\x20access\x20token',_0x3d789e['style'][_0x755c6f(0x1d9)]=_0x755c6f(0xbf),_0x3d789e[_0x755c6f(0xde)][_0x755c6f(0x13b)]=_0x755c6f(0x13d);const _0x59d5b8=document[_0x755c6f(0x141)](_0x755c6f(0x1d7));_0x59d5b8['textContent']=_0x755c6f(0x138),_0x59d5b8[_0x755c6f(0xde)][_0x755c6f(0x209)]=_0x755c6f(0x94),_0x59d5b8[_0x755c6f(0xde)][_0x755c6f(0x20c)]=_0x755c6f(0x13d),_0x59d5b8['addEventListener'](_0x755c6f(0x1b8),async()=>{var _0x19cc3f=_0x755c6f;const _0x4663f7=_0x4a4fb1[_0x19cc3f(0xa6)][_0x19cc3f(0x1de)](),_0x338854=_0x3d789e['value'][_0x19cc3f(0x1de)]();if(!_0x4663f7||!_0x338854){alert('Both\x20fields\x20are\x20required.');return;}await chrome[_0x19cc3f(0xba)][_0x19cc3f(0xaf)][_0x19cc3f(0xab)]({'shopify_store_url':_0x4663f7,'shopify_admin_access_token':_0x338854}),alert(_0x19cc3f(0x110)),document['body'][_0x19cc3f(0x107)](_0x322c75),_0x4a1a6e();}),_0x322c75[_0x755c6f(0x16e)](_0xfdaa10),_0x322c75['appendChild'](_0x4a4fb1),_0x322c75[_0x755c6f(0x16e)](_0x51ed50),_0x322c75[_0x755c6f(0x16e)](_0x3d789e),_0x322c75[_0x755c6f(0x16e)](_0x59d5b8),document['body']['appendChild'](_0x322c75);});}async function listItemToPoshmark(){var _0x253dcc=a0_0x1638ae;await listProductData();var _0x440bb0=await checkMembership();if(_0x440bb0!=_0x253dcc(0x1db)){alert(_0x253dcc(0x17d));return;}var _0x209861=await checkIfCreditsAreAvailable();if(!_0x209861){alert('Please\x20purchase\x20credits\x20to\x20list\x20to\x20Poshmark');return;}chrome[_0x253dcc(0x1ed)][_0x253dcc(0x16b)]({'type':_0x253dcc(0x1f3),'product_data':product_data});}