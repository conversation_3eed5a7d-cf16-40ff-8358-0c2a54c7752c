/**
 * eBay Scraper Pro - Simplified version for content script compatibility
 */
class EbayScraperPro {
  constructor() {
    this.isActive = false;
    this.results = [];
    console.log('🔍 EbayScraperPro initialized');
  }

  /**
   * Start bulk scraping operation
   * @param {object} config - Scraping configuration
   * @returns {Promise<object>} Scraping results
   */
  async startBulkScraping(config = {}) {
    try {
      this.isActive = true;
      console.log('🚀 Starting bulk scraping with config:', config);

      const {
        maxItems = 50,
        delay = 1000,
        selectors = this.getDefaultSelectors()
      } = config;

      // Find all listing items on the page
      const listingElements = document.querySelectorAll(selectors.listingItem);
      console.log(`📋 Found ${listingElements.length} listing elements`);

      const results = [];
      const maxToProcess = Math.min(listingElements.length, maxItems);

      for (let i = 0; i < maxToProcess; i++) {
        try {
          const listing = await this.scrapeListing(listingElements[i], selectors);
          if (listing) {
            results.push(listing);
          }

          // Add delay between items
          if (i < maxToProcess - 1) {
            await this.delay(delay);
          }
        } catch (error) {
          console.error(`❌ Error scraping listing ${i}:`, error);
        }
      }

      this.results = results;
      this.isActive = false;

      console.log(`✅ Bulk scraping completed: ${results.length} items scraped`);
      return {
        success: true,
        itemCount: results.length,
        items: results,
        timestamp: Date.now()
      };

    } catch (error) {
      this.isActive = false;
      console.error('❌ Bulk scraping failed:', error);
      return {
        success: false,
        error: error.message,
        itemCount: 0,
        items: []
      };
    }
  }

  /**
   * Scrape individual listing
   * @param {Element} element - Listing DOM element
   * @param {object} selectors - CSS selectors
   * @returns {object} Scraped listing data
   */
  async scrapeListing(element, selectors) {
    try {
      const listing = {
        title: this.extractText(element, selectors.title),
        price: this.extractPrice(element, selectors.price),
        shipping: this.extractText(element, selectors.shipping),
        condition: this.extractText(element, selectors.condition),
        seller: this.extractText(element, selectors.seller),
        url: this.extractUrl(element, selectors.url),
        image: this.extractImage(element, selectors.image),
        timestamp: Date.now()
      };

      // Clean up the data
      listing.price = this.parsePrice(listing.price);
      listing.shipping = this.parsePrice(listing.shipping);

      return listing;
    } catch (error) {
      console.error('❌ Error scraping individual listing:', error);
      return null;
    }
  }

  /**
   * Get default CSS selectors for eBay
   * @returns {object} Selector configuration
   */
  getDefaultSelectors() {
    return {
      listingItem: '.s-item',
      title: '.s-item__title',
      price: '.s-item__price',
      shipping: '.s-item__shipping',
      condition: '.s-item__subtitle',
      seller: '.s-item__seller-info-text',
      url: '.s-item__link',
      image: '.s-item__image img'
    };
  }

  /**
   * Extract text from element using selector
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Extracted text
   */
  extractText(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.textContent.trim() : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract price text from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Price text
   */
  extractPrice(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.textContent.trim() : '0';
    } catch (error) {
      return '0';
    }
  }

  /**
   * Extract URL from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} URL
   */
  extractUrl(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.href : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract image URL from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Image URL
   */
  extractImage(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.src : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Parse price string to number
   * @param {string} priceText - Price text
   * @returns {number} Parsed price
   */
  parsePrice(priceText) {
    try {
      const cleaned = priceText.replace(/[^0-9.]/g, '');
      return parseFloat(cleaned) || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current scraping status
   * @returns {object} Status information
   */
  getStatus() {
    return {
      isActive: this.isActive,
      resultCount: this.results.length,
      lastUpdate: Date.now()
    };
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.ebayScraperPro = new EbayScraperPro();
window.EbayScraperPro = EbayScraperPro;
window.globalContext.ebayScraperPro = window.ebayScraperPro;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EbayScraperPro;
}

console.log('✅ EbayScraperPro loaded and exported to window.ebayScraperPro');
