{
  "response_format": "structured_json",
  "task": "Keyword relevance check with critical thinking",
  "instructions": "Please provide a structured JSON response with the relevance assessment of each keyword to the item. Consider the context and logical relevance beyond the title and description. Exclude generic words like 'for,' 'in,' and 'with.' Example: { 'heater': 'relevant', 'space': 'relevant', ... }",
  "item_title": "{{Title}}".
  "item_description": "{{Description}}",
  "keywords": [
    {{Keywords}}
  ]
}