/**
 * ✅ SEARCH PAGE HANDLER - Handles complex search operations on eBay search pages
 * Loads at document_idle when all dependencies are available
 * Based on EbayLister4 reference pattern with robust dependency management
 */

(function() {
  'use strict';

  console.log('🔍 Search Page Handler loading at document_idle...');

  // Global state
  let isInitialized = false;
  let initializationAttempts = 0;
  const maxInitAttempts = 50;
  const retryDelay = 200;
  let dependencies = {};

  /**
   * Check if all required dependencies are available with enhanced debugging
   */
  function checkDependencies() {
    console.log('🔍 DEBUGGING DEPENDENCIES IN CONTENT SCRIPT CONTEXT:');

    // Check window object
    console.log('📋 window keys:', Object.keys(window));
    console.log('📋 window.globalContext:', window.globalContext);
    console.log('📋 window.FEE_STRUCTURES:', window.FEE_STRUCTURES);
    console.log('📋 window.ArbitrageProfitCalculator:', window.ArbitrageProfitCalculator);

    // Check what we're actually looking for
    const hasFeeStructures = typeof window.FEE_STRUCTURES !== 'undefined';
    const hasProfitCalculator = window.globalContext?.ArbitrageProfitCalculator;
    const hasEventBus = typeof window.EventBus !== 'undefined';
    const hasErrorHandler = typeof window.ErrorHandler !== 'undefined';

    console.log('✅ Dependency Check Results:');
    console.log('  - FEE_STRUCTURES:', hasFeeStructures, window.FEE_STRUCTURES);
    console.log('  - ArbitrageProfitCalculator:', !!hasProfitCalculator, hasProfitCalculator);
    console.log('  - EventBus:', hasEventBus, window.EventBus);
    console.log('  - ErrorHandler:', hasErrorHandler, window.ErrorHandler);

    // Enhanced debugging - check for any arbitrage-related globals
    console.log('🔍 SEARCHING FOR ARBITRAGE-RELATED GLOBALS:');
    const arbitrageKeys = Object.keys(window).filter(key =>
      key.toLowerCase().includes('arbitrage') ||
      key.toLowerCase().includes('fee') ||
      key.toLowerCase().includes('profit') ||
      key.toLowerCase().includes('calculator')
    );
    console.log('📋 Found arbitrage-related keys:', arbitrageKeys);

    // For now, let's use minimal dependency check
    console.log('🎯 MINIMAL DEPENDENCY CHECK - Just checking FEE_STRUCTURES:');
    console.log('  - FEE_STRUCTURES available:', hasFeeStructures);

    // Temporarily relax the requirement to just FEE_STRUCTURES
    return hasFeeStructures;
  }

  /**
   * Initialize all dependencies using actual available classes
   */
  async function initializeDependencies() {
    console.log('🔄 Search handler initializing dependencies...');

    const requiredDeps = {
      rateLimiter: 'RateLimiter',
      profitCalculator: 'ProfitCalculatorPro',
      scraper: 'ebayScraperPro',
      commonUtils: 'CommonUtils'
    };

    const missing = [];

    for (const [key, globalName] of Object.entries(requiredDeps)) {
      if (typeof window[globalName] !== 'undefined') {
        try {
          // For classes that need instantiation
          if (globalName === 'RateLimiter') {
            dependencies[key] = new window[globalName](2000, 5000);
            console.log(`✅ ${globalName} instantiated successfully`);
          } else if (globalName === 'ProfitCalculatorPro') {
            dependencies[key] = new window[globalName]();
            console.log(`✅ ${globalName} instantiated successfully`);
          } else {
            // For already instantiated objects
            dependencies[key] = window[globalName];
            console.log(`✅ ${globalName} referenced successfully`);
          }
        } catch (error) {
          console.error(`❌ Failed to instantiate ${globalName}:`, error);
          missing.push(globalName);
        }
      } else {
        missing.push(globalName);
      }
    }

    if (missing.length > 0) {
      throw new Error(`Missing dependencies: ${missing.join(', ')}`);
    }

    // Validate that all dependencies have required methods
    const validations = [
      ['scraper.startBulkScraping', typeof dependencies.scraper?.startBulkScraping === 'function'],
      ['profitCalculator.calculateOpportunity', typeof dependencies.profitCalculator?.calculateOpportunity === 'function'],
      ['commonUtils.log', typeof dependencies.commonUtils?.log === 'function']
    ];

    for (const [name, isValid] of validations) {
      if (!isValid) {
        throw new Error(`${name} is not available or not a function`);
      }
    }

    console.log('✅ Search handler dependencies validated successfully');
    return true;
  }
  
  /**
   * Attempt initialization with retry logic and dependency checking
   */
  async function attemptInitialization() {
    if (isInitialized) return true;

    initializationAttempts++;
    console.log(`🔍 Search handler initialization attempt ${initializationAttempts}/${maxInitAttempts}`);
    console.log(`🕐 Current time: ${new Date().toISOString()}`);
    console.log(`📍 Current URL: ${window.location.href}`);

    try {
      // First check if dependencies are available
      console.log('🔍 About to check dependencies...');
      const depsAvailable = checkDependencies();
      console.log('🎯 Dependency check result:', depsAvailable);

      if (!depsAvailable) {
        throw new Error('Dependencies not yet available');
      }

      // Initialize dependencies
      console.log('🚀 Dependencies available, initializing...');
      await initializeDependencies();
      isInitialized = true;

      console.log('✅ Search Page Handler initialized successfully');

      // Send ready message with initialized: true
      notifyServiceWorkerReady();

      return true;
    } catch (error) {
      console.log(`⏳ Search handler initialization attempt ${initializationAttempts} failed:`, error.message);

      if (initializationAttempts < maxInitAttempts) {
        // Retry after a delay
        console.log(`⏰ Retrying in ${retryDelay}ms...`);
        setTimeout(attemptInitialization, retryDelay);
      } else {
        console.error('❌ Search Page Handler initialization failed after maximum attempts');
        console.error('🔍 Final dependency check:');
        checkDependencies();
      }

      return false;
    }
  }

  /**
   * Notify service worker that handler is ready
   */
  function notifyServiceWorkerReady() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'search-page-handler',
      url: window.location.href,
      timestamp: Date.now(),
      initialized: true  // ✅ This is the key fix!
    }).catch(error => {
      console.log('📡 Service worker not ready yet for search handler');
    });
  }
  
  /**
   * Handle search-specific messages with robust error handling
   */
  async function handleMessage(message, sender, sendResponse) {
    console.log('📨 Search Page Handler received message:', message.action);

    try {
      // Handle ping requests immediately (even if not initialized)
      if (message.action === 'ping') {
        console.log('🏓 Search handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'search-page-handler',
          url: window.location.href,
          initialized: isInitialized
        });
        return true;
      }

      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Search handler confirming ready');
        sendResponse({
          success: true,
          ready: isInitialized,
          handler: 'search-page-handler',
          dependencies: Object.keys(dependencies),
          attempts: initializationAttempts
        });
        return true;
      }
      
      // For search operations, ensure we're initialized
      if (!isInitialized) {
        console.log('⏳ Search handler not initialized yet, responding with error');
        sendResponse({
          success: false,
          error: 'Search handler not initialized',
          action: message.action,
          initialized: false,
          attempts: initializationAttempts,
          handler: 'search-page-handler'
        });
        return true;
      }
      
      // Handle scraping requests
      if (message.action === 'startScraping') {
        console.log('🔍 Search handler starting eBay scraping...');

        try {
          const results = await dependencies.scraper.startBulkScraping(message.config || {});
          console.log('✅ Search handler scraping completed successfully:', results);

          sendResponse({
            success: true,
            products: results,
            action: message.action,
            handler: 'search-page-handler'
          });
        } catch (error) {
          console.error('❌ Search handler scraping failed:', error);
          sendResponse({
            success: false,
            error: error.message,
            action: message.action,
            handler: 'search-page-handler'
          });
        }

        return true;
      }
      
      // Handle other search-specific actions
      if (message.action === 'testSelectors' || message.action === 'minimalTest') {
        console.log('🧪 Search handler running test:', message.action);
        
        try {
          // Basic test functionality
          const testResult = {
            success: true,
            message: `Test ${message.action} completed`,
            handler: 'search-page-handler',
            timestamp: Date.now()
          };
          
          sendResponse(testResult);
        } catch (error) {
          sendResponse({
            success: false,
            error: error.message,
            action: message.action,
            handler: 'search-page-handler'
          });
        }
        
        return true;
      }
      
      // Unhandled message - still respond to prevent port closure
      console.log('❓ Search handler - unhandled message:', message.action);
      sendResponse({
        success: true, // Changed to true to prevent errors
        message: 'Message received but not handled',
        action: message.action,
        handler: 'search-page-handler',
        initialized: isInitialized
      });

    } catch (error) {
      console.error('❌ Search handler message error:', error);
      sendResponse({
        success: false,
        error: 'Search handler crashed: ' + error.message,
        action: message.action,
        handler: 'search-page-handler'
      });
    }

    return true; // Always keep message channel open
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Start initialization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', attemptInitialization);
  } else {
    // DOM already ready, start immediately
    attemptInitialization();
  }
  
  // Send initial notification (will show initialized: false)
  chrome.runtime.sendMessage({
    action: 'contentScriptReady',
    handler: 'search-page-handler',
    url: window.location.href,
    timestamp: Date.now(),
    initialized: false
  }).catch(error => {
    console.log('📡 Service worker not ready yet for initial search handler notification');
  });
  
  // Global status for debugging
  window.searchPageHandler = {
    isInitialized: () => isInitialized,
    getAttempts: () => initializationAttempts,
    getDependencies: () => Object.keys(dependencies),
    restart: attemptInitialization
  };
  
  console.log('✅ Search Page Handler setup complete');
  
})();
