body {
    margin: 0;
    padding: 20px;
    font-family: Arial, sans-serif;
    background: #f5f8fb;
    color: #333;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }
  
  h1 {
    font-size: 24px;
    color: #0064D2;
    margin-bottom: 20px;
    text-align: center;
  }
  
  /* Mode Buttons */
  .mode-buttons {
    text-align: center;
    margin-bottom: 10px;
  }
  .mode-buttons button {
    padding: 10px 15px;
    margin-right: 5px;
    background-color: #058cd3;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
  }
  .mode-buttons button.active {
    background-color: #0071bc;
  }
  .mode-buttons button:hover:not(.active) {
    background-color: #0a9de8;
  }
  
  /* Editor Wrapper */
  .editor-wrapper {
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fafafa;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    /* No fixed height or overflow here */
  }
  #liveEditor,
  #codeEditor {
    width: 100%;
    /* Let the content expand naturally */
    min-height: 300px; 
    padding: 10px;
    border: none;
    outline: none;
    box-sizing: border-box;
    font-size: 14px;
    /* Let the page itself scroll, not just these elements */
    resize: none;
    background-color: #fff;
  }
  
  /* Code Editor */
  #codeEditor {
    font-family: monospace;
    display: none; /* hidden by default, toggled by JavaScript */
  }
  
  /* Button Row */
  .button-row {
    text-align: center;
    margin-top: 15px;
  }
  .button-row button {
    padding: 10px 15px;
    margin-right: 5px;
    background-color: #5cb85c;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
  }
  .button-row button:hover {
    background-color: #4cae4c;
  }
  button:disabled {
    background-color: #aaa;
    cursor: not-allowed;
  }
  

  .ai-section {
    margin-top: 30px;
    background: #fafafa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
  }
  .ai-section textarea {
    width: 100%;
    min-height: 80px;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid #ccc;
    padding: 8px;
    margin-top: 5px;
    box-sizing: border-box;
    resize: vertical;
  }
  .ai-section .button-row {
    margin-top: 10px;
  }
  