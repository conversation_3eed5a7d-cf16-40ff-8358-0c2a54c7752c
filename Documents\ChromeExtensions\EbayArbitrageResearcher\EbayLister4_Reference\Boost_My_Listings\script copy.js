var a0_0x1fc57f=a0_0x4031;(function(_0x1559cf,_0x33a55b){var _0x42cbd2=a0_0x4031,_0x139cb6=_0x1559cf();while(!![]){try{var _0x4f6566=-parseInt(_0x42cbd2(0xb9))/0x1+parseInt(_0x42cbd2(0x95))/0x2+-parseInt(_0x42cbd2(0xa8))/0x3*(-parseInt(_0x42cbd2(0xcc))/0x4)+-parseInt(_0x42cbd2(0xa5))/0x5*(parseInt(_0x42cbd2(0x98))/0x6)+-parseInt(_0x42cbd2(0xa2))/0x7+parseInt(_0x42cbd2(0xa1))/0x8+parseInt(_0x42cbd2(0xb3))/0x9*(-parseInt(_0x42cbd2(0xc3))/0xa);if(_0x4f6566===_0x33a55b)break;else _0x139cb6['push'](_0x139cb6['shift']());}catch(_0x58450f){_0x139cb6['push'](_0x139cb6['shift']());}}}(a0_0x1c82,0x441b7),console[a0_0x1fc57f(0x9d)](a0_0x1fc57f(0xa4)),chrome[a0_0x1fc57f(0xb4)][a0_0x1fc57f(0xc7)][a0_0x1fc57f(0xc1)](function(_0x1de12e,_0x87318d,_0x17a3d8){var _0x3ca1ac=a0_0x1fc57f;return console[_0x3ca1ac(0x9d)](_0x3ca1ac(0x97),_0x1de12e),_0x1de12e['type']==_0x3ca1ac(0xbf)&&(console[_0x3ca1ac(0x9d)](_0x3ca1ac(0xaa)),updateProgress(_0x1de12e[_0x3ca1ac(0xa9)],_0x1de12e['message'])),_0x1de12e[_0x3ca1ac(0xcb)]==_0x3ca1ac(0xb7)&&(console[_0x3ca1ac(0x9d)](_0x3ca1ac(0xc8)),restartSellSimilarTab()),!![];}));async function restartSellSimilarTab(){var _0x2d55c4=a0_0x1fc57f;console[_0x2d55c4(0x9d)](_0x2d55c4(0x94)),await new Promise(_0x36de80=>setTimeout(_0x36de80,0x7d0)),updateProgress(0x0,_0x2d55c4(0xc4)),await new Promise(_0x2651b9=>setTimeout(_0x2651b9,0x7d0)),document['getElementById'](_0x2d55c4(0xb5))[_0x2d55c4(0xab)]();}function a0_0x1c82(){var _0x549b73=['1029250DuQfGB','end_and_sell_similar_button\x20clicked','message\x20received\x20in\x20script.js','2934MPzLkp','autoCloseToggle','change','status_message','checked','log','get','set','autoCloseSellSimilarTab','922944IEllkW','1425739FWthpr','local','script.js\x20loaded','4155UHJtlL','timeLeft','filters_container','1020hcQpKl','progress','progress-update\x20message\x20received\x20in\x20script.js','click','display','getElementById','width','autoRepeatToggle','minViews','sendMessage','filterByHours','9vEcYbw','runtime','end_and_sell_similar_button','end-and-sell-similar-button-clicked','restart-sell-similar-tab','style','51517mjkAVb','Value\x20currently\x20is\x20','addEventListener','block','minViewCount','value','progress-update','storage','addListener','toggle_options_button','1743130kIzbrR','Restarting\x20until\x20no\x20more\x20low\x20performing\x20items','textContent','autoRepeatSellSimilarTab','onMessage','restart-sell-similar-tab\x20message\x20received\x20in\x20script.js','0%\x20Processing...','disabled','type','5704oXNwFl','minSold','restartSellSimilarTab'];a0_0x1c82=function(){return _0x549b73;};return a0_0x1c82();}var endAndSellSimilarButton=document[a0_0x1fc57f(0xad)](a0_0x1fc57f(0xb5));function a0_0x4031(_0x3b1245,_0x10e716){var _0x1c822b=a0_0x1c82();return a0_0x4031=function(_0x4031f8,_0x19c912){_0x4031f8=_0x4031f8-0x94;var _0x151c24=_0x1c822b[_0x4031f8];return _0x151c24;},a0_0x4031(_0x3b1245,_0x10e716);}endAndSellSimilarButton['addEventListener'](a0_0x1fc57f(0xab),async function(){var _0x45a942=a0_0x1fc57f;endAndSellSimilarButton[_0x45a942(0xca)]=!![],console[_0x45a942(0x9d)](_0x45a942(0x96));var _0x5be875=0x0;updateProgress(_0x5be875,_0x45a942(0xc9));var _0x19a03b=document[_0x45a942(0xad)](_0x45a942(0xcd))[_0x45a942(0xbe)],_0x2c7d73=document['getElementById'](_0x45a942(0xb0))[_0x45a942(0xbe)],_0x25b177=document[_0x45a942(0xad)](_0x45a942(0xa6))[_0x45a942(0xbe)],_0x456dee=await new Promise(_0x2b2b12=>{var _0x220b02=_0x45a942;chrome[_0x220b02(0xb4)][_0x220b02(0xb1)]({'type':_0x220b02(0xb6),'minSold':_0x19a03b,'minViews':_0x2c7d73,'filterByHours':_0x25b177},function(_0x2ffa4b){var _0x378d61=_0x220b02;console[_0x378d61(0x9d)](_0x2ffa4b),_0x2b2b12(_0x2ffa4b);});});});function updateProgress(_0x175262,_0x5db493){var _0x3572bb=a0_0x1fc57f;const _0x45021f=document['getElementById']('loading_progress'),_0x20a573=document[_0x3572bb(0xad)](_0x3572bb(0x9b));_0x45021f[_0x3572bb(0xb8)][_0x3572bb(0xae)]=_0x175262+'%',_0x20a573[_0x3572bb(0xc5)]=_0x5db493,_0x175262>=0x64&&(endAndSellSimilarButton[_0x3572bb(0xca)]=![]);}var minSoldInput=document[a0_0x1fc57f(0xad)]('minSold');minSoldInput[a0_0x1fc57f(0xbb)](a0_0x1fc57f(0x9a),async function(){var _0x93bbb9=a0_0x1fc57f;await chrome[_0x93bbb9(0xc0)][_0x93bbb9(0xa3)]['set']({'minSoldQuantity':minSoldInput[_0x93bbb9(0xbe)]});});var minViewInput=document[a0_0x1fc57f(0xad)]('minViews');minViewInput[a0_0x1fc57f(0xbb)]('change',async function(){var _0x261be9=a0_0x1fc57f;await chrome['storage'][_0x261be9(0xa3)][_0x261be9(0x9f)]({'minViewCount':minViewInput['value']});});var timeLeftInput=document[a0_0x1fc57f(0xad)](a0_0x1fc57f(0xa6));timeLeftInput[a0_0x1fc57f(0xbb)](a0_0x1fc57f(0x9a),async function(){var _0x30ca7a=a0_0x1fc57f;await chrome[_0x30ca7a(0xc0)]['local'][_0x30ca7a(0x9f)]({'filterByHours':timeLeftInput[_0x30ca7a(0xbe)]});}),chrome[a0_0x1fc57f(0xc0)][a0_0x1fc57f(0xa3)][a0_0x1fc57f(0x9e)](['minSoldQuantity',a0_0x1fc57f(0xbd),a0_0x1fc57f(0xb2)],function(_0x5045d4){var _0x4d44d2=a0_0x1fc57f;console[_0x4d44d2(0x9d)](_0x4d44d2(0xba)+_0x5045d4['minSoldQuantity']),minSoldInput[_0x4d44d2(0xbe)]=_0x5045d4['minSoldQuantity']||0x0,console[_0x4d44d2(0x9d)]('Value\x20currently\x20is\x20'+_0x5045d4['minViewCount']),minViewInput[_0x4d44d2(0xbe)]=_0x5045d4[_0x4d44d2(0xbd)]||0x0,console[_0x4d44d2(0x9d)](_0x4d44d2(0xba)+_0x5045d4[_0x4d44d2(0xb2)]),timeLeftInput[_0x4d44d2(0xbe)]=_0x5045d4['filterByHours']||0x18;}),document[a0_0x1fc57f(0xad)](a0_0x1fc57f(0xc2))[a0_0x1fc57f(0xbb)](a0_0x1fc57f(0xab),function(){var _0x1aad94=a0_0x1fc57f,_0x3dd1b0=document[_0x1aad94(0xad)](_0x1aad94(0xa7));_0x3dd1b0[_0x1aad94(0xb8)][_0x1aad94(0xac)]==='none'?_0x3dd1b0[_0x1aad94(0xb8)][_0x1aad94(0xac)]=_0x1aad94(0xbc):_0x3dd1b0[_0x1aad94(0xb8)][_0x1aad94(0xac)]='none';});var autoCloseToggle=document['getElementById'](a0_0x1fc57f(0x99));autoCloseToggle['addEventListener']('change',async function(){var _0x37fce4=a0_0x1fc57f;await chrome[_0x37fce4(0xc0)][_0x37fce4(0xa3)][_0x37fce4(0x9f)]({'autoCloseSellSimilarTab':autoCloseToggle[_0x37fce4(0x9c)]});}),chrome['storage'][a0_0x1fc57f(0xa3)][a0_0x1fc57f(0x9e)]([a0_0x1fc57f(0xa0)],function(_0x38caa5){var _0x1fd9e6=a0_0x1fc57f;console[_0x1fd9e6(0x9d)]('Value\x20currently\x20is\x20'+_0x38caa5[_0x1fd9e6(0xa0)]),autoCloseToggle[_0x1fd9e6(0x9c)]=_0x38caa5[_0x1fd9e6(0xa0)]||![];});var autoRepeatToggle=document['getElementById'](a0_0x1fc57f(0xaf));autoRepeatToggle[a0_0x1fc57f(0xbb)](a0_0x1fc57f(0x9a),async function(){var _0x242bcb=a0_0x1fc57f;await chrome['storage'][_0x242bcb(0xa3)][_0x242bcb(0x9f)]({'autoRepeatSellSimilarTab':autoRepeatToggle['checked']});}),chrome['storage'][a0_0x1fc57f(0xa3)]['get'](['autoRepeatSellSimilarTab'],function(_0x14c4a9){var _0x3f15e0=a0_0x1fc57f;console['log'](_0x3f15e0(0xba)+_0x14c4a9[_0x3f15e0(0xc6)]),autoRepeatToggle[_0x3f15e0(0x9c)]=_0x14c4a9['autoRepeatSellSimilarTab']||![];});