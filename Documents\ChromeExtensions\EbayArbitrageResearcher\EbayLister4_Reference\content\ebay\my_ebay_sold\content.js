function a0_0x1b2a(_0x116ece,_0x5345ce){var _0x1a91e7=a0_0x1a91();return a0_0x1b2a=function(_0x1b2ac0,_0x2d5655){_0x1b2ac0=_0x1b2ac0-0xfc;var _0x38812c=_0x1a91e7[_0x1b2ac0];return _0x38812c;},a0_0x1b2a(_0x116ece,_0x5345ce);}var a0_0xf31f49=a0_0x1b2a;(function(_0x81c09b,_0x8b60ed){var _0x48270c=a0_0x1b2a,_0x3195a9=_0x81c09b();while(!![]){try{var _0x48c0ab=parseInt(_0x48270c(0x105))/0x1*(-parseInt(_0x48270c(0xff))/0x2)+-parseInt(_0x48270c(0x102))/0x3+-parseInt(_0x48270c(0xfe))/0x4+-parseInt(_0x48270c(0x106))/0x5*(parseInt(_0x48270c(0x10d))/0x6)+-parseInt(_0x48270c(0x103))/0x7+-parseInt(_0x48270c(0x100))/0x8*(-parseInt(_0x48270c(0x107))/0x9)+parseInt(_0x48270c(0x10e))/0xa*(parseInt(_0x48270c(0x10a))/0xb);if(_0x48c0ab===_0x8b60ed)break;else _0x3195a9['push'](_0x3195a9['shift']());}catch(_0x55404d){_0x3195a9['push'](_0x3195a9['shift']());}}}(a0_0x1a91,0x1e178),console['log'](a0_0xf31f49(0x108)));function a0_0x1a91(){var _0xfc58bf=['.item__itemid','querySelector','1220262ZEOcXx','2260GeVKhY','log','itemNumber','193556plpiak','58lTiRYJ','1328088lOJivu','appendChild','246204VEXWWe','1319990AQcien','parentElement','161rzvinS','5ijsHQL','9XOSAEm','my_ebay_sold/content.js\x20loaded','length','23573qESxkZ'];a0_0x1a91=function(){return _0xfc58bf;};return a0_0x1a91();}async function main(){var _0x5e364d=a0_0xf31f49;await onPageLoadAndStable(),removeModeFromAllLinks();var _0x16ff87=getSoldItemsOnPage();for(let _0x425876=0x0;_0x425876<_0x16ff87[_0x5e364d(0x109)];_0x425876++){var _0x428d28=_0x16ff87[_0x425876],_0xd59a20=getItemNumberFromSoldItem(_0x428d28);console[_0x5e364d(0xfc)](_0x5e364d(0xfd),_0xd59a20);var _0x270f47=createAmazonSearchButtonFromItemNumber(_0xd59a20);_0x428d28[_0x5e364d(0x10c)](_0x5e364d(0x10b))[_0x5e364d(0x104)][_0x5e364d(0x101)](_0x270f47);}}main();