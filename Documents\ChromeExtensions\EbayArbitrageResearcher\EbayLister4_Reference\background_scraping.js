(function(_0x3b7806,_0x15a28e){const _0x116f0f=a0_0x5f3c,_0x1ba149=_0x3b7806();while(!![]){try{const _0x127b48=parseInt(_0x116f0f(0x79))/0x1*(parseInt(_0x116f0f(0x71))/0x2)+parseInt(_0x116f0f(0x72))/0x3*(-parseInt(_0x116f0f(0x7e))/0x4)+parseInt(_0x116f0f(0x77))/0x5+-parseInt(_0x116f0f(0x6a))/0x6+-parseInt(_0x116f0f(0x7d))/0x7+-parseInt(_0x116f0f(0x76))/0x8+parseInt(_0x116f0f(0x81))/0x9;if(_0x127b48===_0x15a28e)break;else _0x1ba149['push'](_0x1ba149['shift']());}catch(_0x1402cf){_0x1ba149['push'](_0x1ba149['shift']());}}}(a0_0x4b26,0x89927));function a0_0x4b26(){const _0x4298c0=['text','2wdapVu','2667yAfXQW','sendMessage','addListener','removeListener','3826008UNtuIl','1814070zNYinh','Received\x20HTML\x20text','944683wFLKKw','offscreen.html','Fetching\x20data\x20from\x20Amazon\x20URL','parse_amazon_tracking_data','1121337BkhMtP','484LzENPh','onMessage','Received\x20response','9277272lnryyp','Parse\x20HTML\x20using\x20DOM\x20APIs','type','6172794hTTend','offscreen','data','DOM_PARSER','log','runtime'];a0_0x4b26=function(){return _0x4298c0;};return a0_0x4b26();}async function ensureOffscreenDocument(){const _0x5111ae=a0_0x5f3c,_0x13042a=chrome[_0x5111ae(0x6f)]['getURL'](_0x5111ae(0x7a)),_0x5c1873=await chrome[_0x5111ae(0x6b)]['hasDocument']();!_0x5c1873&&await chrome[_0x5111ae(0x6b)]['createDocument']({'url':_0x13042a,'reasons':[_0x5111ae(0x6d)],'justification':_0x5111ae(0x68)});}function a0_0x5f3c(_0x56be64,_0x437930){const _0x4b263d=a0_0x4b26();return a0_0x5f3c=function(_0x5f3cba,_0x3f5522){_0x5f3cba=_0x5f3cba-0x68;let _0x1690a2=_0x4b263d[_0x5f3cba];return _0x1690a2;},a0_0x5f3c(_0x56be64,_0x437930);}async function fetchAmazonDataViaHttp(_0x454d57){const _0x592c1e=a0_0x5f3c;await ensureOffscreenDocument(),console[_0x592c1e(0x6e)](_0x592c1e(0x7b),_0x454d57);const _0x480e02=await fetch(_0x454d57);console[_0x592c1e(0x6e)](_0x592c1e(0x80),_0x480e02);const _0x5e2ed2=await _0x480e02[_0x592c1e(0x70)]();return console[_0x592c1e(0x6e)](_0x592c1e(0x78),_0x5e2ed2),new Promise((_0x477dd1,_0x49a45e)=>{const _0x1cee0b=_0x592c1e;function _0x177dff(_0x1db58d,_0xae0345){const _0x25b37d=a0_0x5f3c;_0x1db58d[_0x25b37d(0x69)]===_0x25b37d(0x7c)&&(chrome[_0x25b37d(0x6f)][_0x25b37d(0x7f)][_0x25b37d(0x75)](_0x177dff),console[_0x25b37d(0x6e)]('Received\x20parsed\x20data',_0x1db58d[_0x25b37d(0x6c)]),_0x477dd1(_0x1db58d['data']));}chrome[_0x1cee0b(0x6f)][_0x1cee0b(0x7f)][_0x1cee0b(0x74)](_0x177dff),chrome[_0x1cee0b(0x6f)][_0x1cee0b(0x73)]({'type':_0x1cee0b(0x7c),'html':_0x5e2ed2});});}