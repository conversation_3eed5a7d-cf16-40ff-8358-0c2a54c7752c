function a0_0x24cd(_0x55b2ee,_0x51de43){const _0x43bb70=a0_0x43bb();return a0_0x24cd=function(_0x24cd83,_0x1b84e9){_0x24cd83=_0x24cd83-0x16c;let _0x4a4bd5=_0x43bb70[_0x24cd83];return _0x4a4bd5;},a0_0x24cd(_0x55b2ee,_0x51de43);}function a0_0x43bb(){const _0x12187b=['2794040mJpBmZ','24047lfUBdc','textContent','84axieFF','click','runtime','appendChild','1112cuLpTp','img','400980pdWkFS','remove','17864796tpOKJt','8703wrqrEk','935KITLHu','rotating','createElement','setAttribute','1271793lSVwAq','24vglBmL','preventDefault','getURL','addEventListener','add','-link','icon-button','4HgyZas','with-number','13250aYeyuM','classList','span'];a0_0x43bb=function(){return _0x12187b;};return a0_0x43bb();}(function(_0x2b62da,_0x2c433b){const _0x4ff442=a0_0x24cd,_0x5374ea=_0x2b62da();while(!![]){try{const _0x1c610f=parseInt(_0x4ff442(0x17f))/0x1*(-parseInt(_0x4ff442(0x172))/0x2)+parseInt(_0x4ff442(0x171))/0x3*(-parseInt(_0x4ff442(0x179))/0x4)+parseInt(_0x4ff442(0x17e))/0x5+-parseInt(_0x4ff442(0x187))/0x6*(parseInt(_0x4ff442(0x181))/0x7)+parseInt(_0x4ff442(0x185))/0x8*(-parseInt(_0x4ff442(0x16c))/0x9)+-parseInt(_0x4ff442(0x17b))/0xa*(-parseInt(_0x4ff442(0x16d))/0xb)+parseInt(_0x4ff442(0x189))/0xc;if(_0x1c610f===_0x2c433b)break;else _0x5374ea['push'](_0x5374ea['shift']());}catch(_0x4db5ae){_0x5374ea['push'](_0x5374ea['shift']());}}}(a0_0x43bb,0x7cd42));function createIconButton(_0x258a55,_0x3f1753,_0x541370,_0x34ec96,_0x44b64f=0x0){const _0x5eab1f=a0_0x24cd,_0x5614e9=document['createElement']('a');_0x5614e9[_0x5eab1f(0x170)]('id',_0x3f1753+_0x5eab1f(0x177)),_0x5614e9['className']=_0x5eab1f(0x178),_0x5614e9[_0x5eab1f(0x170)]('title',_0x541370);const _0x8ffa92=document[_0x5eab1f(0x16f)](_0x5eab1f(0x186));_0x8ffa92['src']=chrome[_0x5eab1f(0x183)][_0x5eab1f(0x174)](_0x258a55),_0x8ffa92['className']='icon-button-img',_0x5614e9[_0x5eab1f(0x184)](_0x8ffa92);const _0x2a64cc=document['createElement'](_0x5eab1f(0x17d));_0x2a64cc['className']='icon-button-status',_0x5614e9[_0x5eab1f(0x184)](_0x2a64cc);if(_0x44b64f>0x0){const _0x5b0502=document['createElement'](_0x5eab1f(0x17d));_0x5b0502['className']='icon-button-number',_0x5b0502[_0x5eab1f(0x180)]=_0x44b64f,_0x5614e9[_0x5eab1f(0x184)](_0x5b0502),_0x5614e9['classList'][_0x5eab1f(0x176)](_0x5eab1f(0x17a));}return _0x5614e9[_0x5eab1f(0x175)](_0x5eab1f(0x182),async function(_0x9334d0){const _0x26cfb0=_0x5eab1f;_0x9334d0[_0x26cfb0(0x173)](),_0x2a64cc[_0x26cfb0(0x180)]='⏳',_0x2a64cc[_0x26cfb0(0x17c)][_0x26cfb0(0x176)](_0x26cfb0(0x16e));try{await _0x34ec96(),_0x2a64cc[_0x26cfb0(0x180)]='✅',setTimeout(()=>{const _0x39aedb=_0x26cfb0;_0x2a64cc[_0x39aedb(0x180)]='';},0xbb8);}catch(_0x311566){_0x2a64cc['textContent']='❌',setTimeout(()=>{const _0x101776=_0x26cfb0;_0x2a64cc[_0x101776(0x180)]='';},0xbb8);}_0x2a64cc[_0x26cfb0(0x17c)][_0x26cfb0(0x188)]('rotating');}),_0x5614e9;}