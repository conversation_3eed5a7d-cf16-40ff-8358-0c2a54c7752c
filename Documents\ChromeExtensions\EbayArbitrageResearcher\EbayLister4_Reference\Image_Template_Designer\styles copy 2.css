.editor-container {
    display: flex;
}

#canvasContainer, #testCanvasContainer {
    width: 750px;
    height: 750px;
    transform: scale(0.5);
    transform-origin: 0 0;

}

#canvasWrapper {
    border: 2px solid #000;
    width: 750px; /* Adjust as per the transformed canvas size */
    height: 750px; /* Adjust as per the transformed canvas size */
}

#canvasContainer {
    transform: scale(0.5);
    transform-origin: 0 0;
}


#controls {
    flex-grow: 1;
}

#controls {
    flex-grow: 1;
    background-color: #f7f9fc; 
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#controls button {
    padding: 10px 20px;
    margin: 5px;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: #fff;
    cursor: pointer;
    transition: background-color 0.3s;
}

#controls button:hover {
    background-color: #0056b3;
}

#controls input[type="text"],
#controls input[type="file"],
#controls select {
    padding: 10px;
    margin: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

#controls label {
    margin-right: 10px;
}

#controls input[type="color"] {
    padding: 0;
    margin: 5px;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 5px;
}
