.styled-table {
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 1.4em;
    font-family: sans-serif;
    min-width: 40px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    line-height: 1.2

}

.styled-table thead tr {
    background-color: #009879;
    color: #ffffff;
    text-align: left;
}

.styled-table th,
.styled-table td {
    padding: 12px 20px;
}

.styled-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tbody tr:last-of-type {
    border-bottom: 2px solid #009879;
}

.total-characters-header{
    width: 100px;
}

.rank-header{
    width: 100px;
}



/* Add this CSS to your project's stylesheet */

/* General button styles */
.create-title-v4 {
  font-family: 'Roboto', sans-serif; /* You can use any other font-family you prefer */
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  background-color: #3a86ff;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.3s;

  /* display: none; */
}

/* Button hover styles */
.create-title-v4:hover {
  background-color: #2a64d9;
  transform: scale(1.05);
}

/* Button active styles */
.create-title-v4:active {
  background-color: #1a3e99;
  transform: scale(1);
}


#paid-listing-button{
  /* display: none; */
}


/* General button styles */
.list-button {
  font-family: 'Roboto', sans-serif; /* You can use any other font-family you prefer */
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  background-color: #2bbe5c;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  margin-left: 10px; /* Add spacing between the buttons */
  transition: background-color 0.3s, transform 0.3s;

}

/* Button hover styles */
.list-button:hover {
  background-color: #136821;
  transform: scale(1.05);
}

/* Button active styles */
.list-button:active {
  background-color: #1a993a;
  transform: scale(1);
}

/* lighter green */
#chat-listing-button{
  background-color: #10a37f; /* Forest Green */

}

#chat-listing-button:hover{
  background-color: #0d815f; /* Darker Forest Green */
}

.create-title-v4, .list-button {
  vertical-align: middle;
  line-height: 1.5; /* Adjust this value as needed */
}

#standard-listing-button{
  color: black;
  background-color: #E0E0E0;
}


#great_title_chat_gpt_web_version{
  background-color: #10a37f; /* Forest Green */


  margin-right: 10px;
}

#great_title_chat_gpt_web_version:hover{
  background-color: #0d815f; /* Darker Forest Green */
}

#create-seo-title-v4 {

  color: #FFFFFF;
  background-color: rgb(238, 112, 112) !important;


}

#create-seo-title-v4:hover {
  background-color: rgb(243, 66, 66);
  transform: scale(1.05);
}

#clean-title-button{
  color: #FFFFFF;
  background-color: rgb(231, 78, 78);

  margin-right: 10px;
}

#clean-title-button:hover {
  background-color: rgb(243, 66, 66);
  transform: scale(1.05);
}

#snipe-listing-button{
  color: #FFFFFF;
  background-color: rgb(238, 112, 112);
}

#snipe-listing-button:hover {
  background-color: rgb(243, 66, 66);
  transform: scale(1.05);
}



.edited-image-selection {
  
  opacity: 0;
  transform: rotateY(-90deg);
  animation: flip-in 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
             fade-in 1s ease-in-out forwards;

}

@keyframes flip-in {
  from {
    transform: rotateY(-90deg);
  }
  to {
    transform: rotateY(0deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.list-button:disabled {
    background-color: gray !important;
  } /* Add closing curly brace here */

  

  




.deleteButton {
    position: relative;
    top: 0;
    right: 0;
    padding: 2px 5px;
    background-color: red;
    color: white;
    font-weight: bold;
    cursor: pointer;
    border-radius: 0 0 0 5px; /* Rounded corner for aesthetics */
    z-index: 10; /* Ensures the button is above other content */
}





/* ------------ GOOGLE FONTS ------------ */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');

/* ------------ VARIABLES ------------ */
:root{
  /* FONT */
  --font: 'Poppins', sans-serif;;

  /* COLORS */
  --bg-color: #151320;
  --conic-gradient: conic-gradient(
    #FF2E2E,
    #FF831E,
    #FCFF51,
    #58FF57,
    #575FFF,
    #D257FF,
    #FF57E5,
    #FF1556
  );
}



/* ---------- BUTTON --------- */
.rainbow-button{
  width: 8em;
  height: 3em;
  border-radius: 1em;
  box-shadow: 0 1em 1em rgba(0,0,0, .5);

  position: relative;

  display: flex;
  align-items: center;
  justify-content: center;

  overflow: hidden;
  cursor: pointer;

  transition: transform .3s ease;

  margin: 10px 0;


}

.rainbow-button::before{
  content: '';
  position: absolute;
  width: 110%;
  height: 350%;
  background: var(--conic-gradient);
}

.rainbow-button::after{
  content: 'Load Review Images!';
  position: absolute;
  background-color: var(--bg-color);
  width: 96%;
  height: 86%;
  border-radius: .4em;

  font-size: 0.8rem;
  color: #fff;
  font-weight: 500;

  display: flex;
  align-items: center;
  justify-content: center;
}

.rainbow-button:hover{
  transform: scale(1.1);
}

.rainbow-button:hover::before{
  animation: spin 1.5s infinite linear;
}

@keyframes spin {
  to{
    transform: rotate(360deg);
  }
}



.popup-notification {
  position: fixed;
  top: 10%;  /* Positioned near the top for visibility */
  left: 50%;  /* Center horizontally */
  transform: translateX(-50%) translateY(-20px);  /* Centering and initial animation position */
  background-color: rgba(0, 0, 0, 0.75);  /* Semi-transparent black background */
  color: white;
  padding: 12px 20px;  /* Adequate padding */
  border-radius: 10px;
  font-size: 16px;  /* Readable font size */
  z-index: 1000;
  opacity: 0;  /* Start hidden for animation */
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-out;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);  /* Subtle shadow for depth */
  animation: slideFadeIn 0.5s ease-out forwards;
  max-width: 300px;  /* Maximum width to avoid overly wide notifications */
  text-align: center;  /* Center text within the notification */
  white-space: nowrap;  /* Prevent text from wrapping */
  overflow: hidden;  /* Hide overflow to maintain compact size */
  text-overflow: ellipsis;  /* Use ellipsis if text exceeds max width */
}

@keyframes slideFadeIn {
  from {
      transform: translateX(-50%) translateY(0); /* Start from original position */
      opacity: 0;
  }
  to {
      transform: translateX(-50%) translateY(-20px); /* End slightly higher for smooth appearance */
      opacity: 1;
  }
}

.imagesContainer {
  display: flex !important;         /* Enables Flexbox */
  flex-wrap: wrap !important;        /* Allows items to wrap onto the next line as needed */
  align-items: flex-start !important; /* Aligns items to the start of the flex container */
  justify-content: flex-start !important; /* Aligns items to the start of the main axis */
}









/* Context Menu Styles */
#custom-context-menu {
  position: absolute;
  background-color: #ffffff;
  border: 1px solid #ccc;
  width: 200px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  z-index: 1000;
  border-radius: 4px;
  overflow: hidden;
}

.context-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}