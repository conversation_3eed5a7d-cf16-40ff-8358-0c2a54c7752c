<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="modal.css" />

    <link rel="stylesheet" href="../libraries/context_window/contextMenu.css" />

    <link
      rel="stylesheet"
      href="../libraries/search_marketplace_functions.css"
    />

    <title>Scanner x Results</title>
  </head>
  <body>
    <h1 class="page-title">Scanner x Results</h1>

    <div id="filters">
      <div class="filter-group">
        <label for="totalSold">Minimum Sold Quantity:</label>
        <input
          type="number"
          id="totalSold"
          placeholder="Enter Minimum Total Sold"
        />

        <label for="totalSoldInXDays">In:</label>
        <select id="totalSoldInXDays">
          <option value="1">Last 24 Hours</option>
          <option value="3">Last 3 Days</option>
          <option value="7">Last 7 Days</option>
          <option value="14">Last 14 Days</option>
          <option value="30" selected>Last 30 Days</option>
          <option value="60">Last 60 Days</option>
          <option value="90">Last 90 Days</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="additionalSold">Additional Sold Quantity:</label>
        <input
          type="number"
          id="additionalSold"
          placeholder="Enter Additional Sold Quantity"
        />

        <label for="additionalSoldInXDays">In:</label>
        <select id="additionalSoldInXDays">
          <option value="1">Last 24 Hours</option>
          <option value="3">Last 3 Days</option>
          <option value="7">Last 7 Days</option>
          <option value="14">Last 14 Days</option>
          <option value="30" selected>Last 30 Days</option>
          <option value="60">Last 60 Days</option>
          <option value="90">Last 90 Days</option>
        </select>

    </div>

    <div class="filter-group">
      <label for="minPrice">Minimum Price:</label>
      <input type="number" id="minPrice" placeholder="Enter Minimum Price" />
      <label for="maxPrice">Maximum Price:</label>
      <input type="number" id="maxPrice" placeholder="Enter Maximum Price" />
  </div>



      <div class="filter-group" style="display: none">
        <label for="totalCompetitors">Min Total Competitors:</label>
        <input
          type="number"
          id="totalCompetitors"
          placeholder="Enter Minimum Total Competitors"
        />
      </div>

      <div class="filter-group">
        <label for="soldInLastDays">Sold Within:</label>
        <input
          type="number"
          id="soldInLastDays"
          placeholder="Enter Number of Days the Item was Sold"
        />
      </div>

      <div class="controls">
        <button id="clear_items">Clear Items</button>

        <button id="apply_filters" style="display: block">Apply Filters</button>
      </div>

      <div class="controls">
        <div id="pagination">
          <button id="prev_page">Previous</button>
          <span id="page_info"></span>
          <!-- Display current page and total pages -->
          <button id="next_page">Next</button>
        </div>

        <div id="results">
          <span id="total_results"></span>
          <button id="view_results">View Titles</button>
        </div>
      </div>

      <div class="import_export_buttons_container">
        <button id="import_results">Import from JSON</button>
        <!-- Import Modal -->

        <!-- <div id="import_modal" class="custom-modal">
          <div class="custom-modal-content">
            <span id="import_modal_close" class="custom-modal-close"
              >&times;</span
            >
            <h2>Import JSON Data</h2>
            <textarea
              id="import_modal_textarea"
              placeholder="Paste JSON here..."
            ></textarea>
            <button id="import_modal_import_button">Import</button>
          </div>
        </div> -->

        <button id="export_results">Export to JSON</button>
      </div>
    </div>

    <div id="items-list-container">
      <div id="items-list"></div>
    </div>

    <script src="../libraries/context_window/contextMenu.js"></script>
    <script src="../libraries/search_marketplace_functions.js"></script>
    <script src="script.js"></script>
  </body>
</html>
