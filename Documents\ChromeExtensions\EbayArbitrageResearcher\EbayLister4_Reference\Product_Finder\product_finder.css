.container {
    display: flex;
    /* sets this element as a Flex container */
    flex-wrap: wrap;
    /* allows the items to wrap onto next line if necessary */
    justify-content: space-between;
    /* gives equal space between the flex items */
}

.search-container,
.settings-container {
    flex: 1;
    /* makes these elements take up an equal amount of space */
    /* min-width: 40%;  prevent the elements from getting too small */
    margin: 10px;
    /* give them a bit of space */
}

.result-container {
    flex-basis: 100%;
    margin: 10px;
    /* give it a bit of space */

    display: flex;

    
}





#pause_resume_buttons {

    margin-top: 50px;

}

#filter_heading {
    margin-top: 10px;
}

#amazonChoice {
    margin-top: 10px;
}


.progress-container {
    width: 100%;
    height: 30px;
    background-color: #f3f3f3;
    border-radius: 5px;
}

#progress-bar {
    height: 100%;
    width: 0;
    background-color: #D3D3D3;
    /* Start with a grey color */
    border-radius: 5px;
    transition: width 0.4s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Center the text horizontally */
    color: #fff;
    font-size: 1em;
    transition: background-color 0.4s ease-in-out;
    /* Transition color change */
}

#progress-percentage {
    visibility: hidden;
    /* Hide initially */
}













.loading-bar-container {
    width: 100%;
    height: 20px;
    background-color: #f3f3f3;
    display: none;
}

.loading-bar {
    height: 100%;
    width: 0%;
    background-color: #3498db;
}



#productTable {
    width: 100%;
    height: 300px;
    margin: 0 auto;
    border: 1px solid #ddd;
    border-collapse: collapse;
}

#productTable {
    display: block;
    /* Makes the tbody a block element */
    height: 200px;
    /* Sets a fixed height on tbody */
    overflow: auto;
    /* Adds scrollability when the content exceeds the set height */

    resize: both;         /* “grab‑corner” handle for width + height */

}




#productTable th,
#productTable td {
    padding: 8px;
    text-align: left;
    vertical-align: top;
    border: 1px solid #ddd;
}

#productTable th {
    background-color: #f2f2f2;
}

#productTable td img {
    max-width: 100%;
    height: auto;
}




/* filter modal */

#filterModal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto; /* adjust top and bottom margins */
    padding: 20px;
    border: 1px solid #888;
    width: 90%; /* adjust width */
    height: 80%; /* adjust height */
    overflow: auto; /* add scrolling if content overflows */


    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}


#closeFilterModal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.modal-buttons{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
}








.product-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px; /* adjust for desired space between elements */
}



.product-title {
    font-size: 2em;
    font-weight: bold;
}

.product-price {
    font-size: 2em;
    font-weight: bold;
    color: #4CAF50;
}

.product-reviews{
    font-size: 1.5em;
    font-weight: bold;
    color: #eeb51a;
}

.price-reviews{
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
}


@keyframes slideLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

@keyframes slideRight {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

.slide-left {
    animation: slideLeft 0.5s forwards;
    background-color: rgb(245, 96, 96); /* or any color that signifies deletion */
}

.slide-right {
    animation: slideRight 0.5s forwards;
    background-color: rgb(119, 241, 119); /* or any color that signifies keeping */
}



#openKeywordTool{
    background-color: #5e5ce6;
    color: white;
    border: none;
    padding: 5px;
    border-radius: 5px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;

}

#openKeywordTool:hover{
    background-color: #4d4bc2;
}

#openKeywordTool:active{
    background-color: #3d3ba2;
}



#quickFilter {
    display: inline-block;

    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #007bff;
    color: #fff;
    border: 2px solid #007bff;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }
  
  #quickFilter:hover {
    background-color: #fff;
    color: #007bff;
  }


 .clear {
    display: inline-block;

    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #dc3545;
    color: #fff;
    border: 2px solid #dc3545;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }
  
.clear:hover {
    background-color: #fff;
    color: #dc3545;
  }


  .exportResults {
    display: inline-block;

    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #28a745;
    color: #fff;
    border: 2px solid #28a745;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }
  
  .exportResults:hover {
    background-color: #fff;
    color: #28a745;
  }


  .searchTitle {
    background-color: orange;
    color: white;
    border: none;
    cursor: pointer;
    font-weight: bold;
  }

    .searchTitle:active {
        background-color: #ff8c00;
    }

  .searchTitle:disabled {
    background-color: gray;
    color: white;
    cursor: not-allowed;
  }
  




  /* Add some styles to make the image look like a clickable element
.img-link {
    width: 50px;
    height: 50px;
    transition: transform 0.3s;
}

.img-link:hover {
    transform: scale(1.3);
    cursor: pointer;
} */


@keyframes wiggle {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(5deg); }
    100% { transform: rotate(0deg); }
}

.img-link {
    width: 50px;
    height: 50px;
    transition: transform 0.3s;
}

.img-link:hover {
    animation: wiggle 0.5s infinite;
    cursor: pointer;
}





/* Base styles for the modal overlay */
.keyword-modal.modal {
    display: none;
    position: fixed;
    z-index: 1001; /* Make sure this is above other content */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6); /* Semi-transparent black background */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Styles for the modal content box */
.keyword-modal .modal-content {
    position: relative;
    background-color: #fff;
    padding: 20px;
    width: 50%; /* Adjust width to your preference */
    max-width: 600px; /* Ensures it doesn't get too large on wider screens */
    border-radius: 8px; /* Soft rounded corners for a modern look */
    box-shadow: 0 4px 6px rgba(0,0,0,0.1); /* Subtle shadow for depth */
    text-align: left;
    overflow: hidden; /* Keeps child elements contained within the border-radius */
    display: flex;
    flex-direction: column; /* Stack children vertically */
}


/* Close button in the top right corner */
.close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #666; /* Dark grey for standard look */
    cursor: pointer;
}

.close:hover {
    color: #000; /* Darker when hovered for better visibility */
}

/* Textarea styles */
.keyword-modal textarea {
    width: 100%; /* Full width of its container */
    flex-grow: 1; /* Take available space */
    min-height: 100px; /* Minimum height */
    max-height: 300px; /* Maximum height before scrolling */
    padding: 10px;
    margin-top: 10px; /* Reduced margin */
    margin-bottom: 10px; /* Reduced margin */
    border: 1px solid #ccc; /* Subtle border */
    border-radius: 4px; /* Consistent rounded corners */
    box-sizing: border-box; /* Include padding and border in the element's dimensions */
    resize: vertical; /* Allow vertical resizing */
}

/* Styling for radio buttons */
.keyword-modal input[type="radio"] {
    margin-right: 10px; /* Space between radio button and label */
    cursor: pointer;  /* Indicates that the element is clickable */
}


/* Styling for labels associated with radio buttons */
.keyword-modal label {
    margin: 0; /* Removes default margin */
    cursor: pointer; /* Indicates that the element is clickable */
}

/* Container for radio buttons */
.required_keyword_radio_container {
    display: flex;
    flex-direction: column; /* Stack the radio options vertically */
    align-items: flex-start; /* Align items to the left side of the container */
    padding: 10px; /* Optional padding for better spacing around the elements */
    margin-top: 10px; /* Space from any element above */
}

/* Individual radio button containers for additional control */
.required_keyword_radio_container div {
    margin-bottom: 5px; /* Space between each radio button */
}

/* Styling for radio buttons */
.required_keyword_radio_container input[type="radio"] {
    margin-right: 5px; /* Space between the radio button and the label */
    cursor: pointer; /* Indicates that the element is clickable */
}

/* Styling for labels */
.required_keyword_radio_container label {
    cursor: pointer; /* Indicates that the element is clickable */
}


/* Responsive adjustments */
@media (max-width: 600px) {
    .modal-content {
        width: 90%; /* Smaller screens get a more responsive modal */
        padding: 10px;
    }
}



.alert {
    /* Basic styling to highlight the element */
    border: 2px solid #f00;
    color: #000;
    padding: 8px 12px;
    font-weight: bold;
    display: inline-block;
    margin-top: 10px;
  
    /* 
      Animation:
      - flash is the name of our keyframes
      - 1s is the duration (can adjust faster/slower)
      - ease-in-out is the timing function for smooth transitions
      - 3 is the number of times the animation will run (change to infinite or remove if you want it indefinite)
      - alternate means it will go from 'from' state to 'to' state and then reverse.
    */
    animation: flash 1s ease-in-out 3 alternate;
  }
  
  @keyframes flash {
    0% {
      background-color: #ffc9c9; /* lighter red  */
    }
    100% {
      background-color: #ff7272; /* deeper red */
    }
  }
  
