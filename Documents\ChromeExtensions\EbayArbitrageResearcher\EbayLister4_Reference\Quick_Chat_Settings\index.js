function a0_0x3431(){var _0x565beb=['4157624BWwUZF','quick_chat_messages_container','stringify','log','storage','closeDefaultQuickChatButtonsModal','23574624EIaZiN','application/json','indexOf','removeChild','quick_chat_buttons','quick_chat_button_name','import_quick_chat_buttons','getElementById','createElement','clear_all_quick_chat_buttons','Quick\x20Chat\x20Messages','get','className','createAttribute','sortable-drag','split','setAttribute','result','quick_chat_buttons.json','addEventListener','name','closeModal','loadDefaultGeneral','remove_quick_chat_button','style','classList','data-checked','placeholder','clear_quick_chat_button','target','innerHTML','click','button','DOMContentLoaded','additional','length','massImportModal','type','data-message-','text/plain;charset=utf-8','quick_chat_button_color','block','div','innerText','Edit','local','firstChild','mass_import_quick_chat_messages','add_quick_chat_message','quick_chat_message','3414523IjvMSu','3466340MAYccY','push','set','backgroundColor','onclick','toString','3180495SLlWBM','none','preventDefault','messages','558794IPKuXp','draggable','download','files','attributes','DefaultQuickChatButtons/general.json','massAddQuickMessages','checked','input','5WPvgVI','display','quick_chat_button_container','quick_chat_button','appendChild','startsWith','loadDefaultQuickChatButtonsModal','Remove','replaceAll','save_quick_chat_buttons','1127814fiRYzl','add','massImportTextarea','substr','getElementsByClassName','quick_chat_butttons_container','value','quick_chat_switch','color','file','textarea','getAttribute','URL'];a0_0x3431=function(){return _0x565beb;};return a0_0x3431();}var a0_0x954d29=a0_0x3785;(function(_0x4c2caa,_0xc82d5d){var _0x38c6ae=a0_0x3785,_0x1e1b01=_0x4c2caa();while(!![]){try{var _0xe82533=-parseInt(_0x38c6ae(0xef))/0x1*(-parseInt(_0x38c6ae(0xe6))/0x2)+-parseInt(_0x38c6ae(0xe2))/0x3+-parseInt(_0x38c6ae(0x106))/0x4+-parseInt(_0x38c6ae(0xdc))/0x5+-parseInt(_0x38c6ae(0xf9))/0x6+-parseInt(_0x38c6ae(0xdb))/0x7+parseInt(_0x38c6ae(0x10c))/0x8;if(_0xe82533===_0xc82d5d)break;else _0x1e1b01['push'](_0x1e1b01['shift']());}catch(_0x38012e){_0x1e1b01['push'](_0x1e1b01['shift']());}}}(a0_0x3431,0xd5ad0),document[a0_0x954d29(0x113)]('create_quick_chat_button')[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x51f96c){var _0x3665f7=a0_0x954d29;_0x51f96c[_0x3665f7(0xe4)]();var _0x365db9=document[_0x3665f7(0xfd)](_0x3665f7(0xf1));for(var _0x2f0adf=0x0;_0x2f0adf<_0x365db9['length'];_0x2f0adf++){var _0x31a24f=_0x365db9[_0x2f0adf][_0x3665f7(0xfd)]('quick_chat_button')[0x0],_0x365955=_0x31a24f[_0x3665f7(0xc7)],_0x4a9bfc=document['getElementById'](_0x3665f7(0x111))[_0x3665f7(0xff)];_0x365955===_0x4a9bfc&&document[_0x3665f7(0x113)](_0x3665f7(0xfe))['removeChild'](_0x365db9[_0x2f0adf]);}var _0x18bedc=document[_0x3665f7(0x113)](_0x3665f7(0x111))[_0x3665f7(0xff)],_0x26c448=getQuickChatMessages(),_0x3ace7f=document[_0x3665f7(0x113)](_0x3665f7(0xd1))[_0x3665f7(0xff)];createQuickChatButton(_0x18bedc,_0x26c448,_0x3ace7f);}),document[a0_0x954d29(0x113)](a0_0x954d29(0xd9))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x1cae81){_0x1cae81['preventDefault'](),addQuickChatMessage('Last\x20Message');}));function addQuickChatMessage(_0x402261){var _0x47e0b3=a0_0x954d29,_0x362c4e=document[_0x47e0b3(0x114)](_0x47e0b3(0xd3));_0x362c4e[_0x47e0b3(0x118)]='quick_chat_message_container',_0x362c4e[_0x47e0b3(0xc2)][_0x47e0b3(0xfa)](_0x47e0b3(0xcb));var _0x1a05fa=document[_0x47e0b3(0x114)](_0x47e0b3(0x103));_0x1a05fa[_0x47e0b3(0x118)]='quick_chat_message',_0x1a05fa[_0x47e0b3(0xc4)]=_0x47e0b3(0x116),_0x1a05fa['innerText']=_0x402261;var _0x2522fd=document['createElement'](_0x47e0b3(0xc9));_0x2522fd['className']='remove_quick_chat_message',_0x2522fd[_0x47e0b3(0xd4)]=_0x47e0b3(0xf6),_0x362c4e[_0x47e0b3(0xf3)](_0x1a05fa),_0x362c4e['appendChild'](_0x2522fd);var _0x467ce0=document[_0x47e0b3(0x113)](_0x47e0b3(0x107));_0x467ce0['appendChild'](_0x362c4e),_0x2522fd['addEventListener'](_0x47e0b3(0xc8),function(_0x24e9f0){var _0x3cd5af=_0x47e0b3;_0x24e9f0[_0x3cd5af(0xe4)](),_0x467ce0[_0x3cd5af(0x10f)](_0x362c4e);});}function getQuickChatMessages(){var _0x54c98=a0_0x954d29,_0xce269d=document['getElementsByClassName']('quick_chat_message_container'),_0x137e92=[];for(var _0x1fe3c2=0x0;_0x1fe3c2<_0xce269d[_0x54c98(0xcc)];_0x1fe3c2++){var _0x379cff=_0xce269d[_0x1fe3c2]['getElementsByClassName']('quick_chat_message')[0x0][_0x54c98(0xff)];_0x137e92[_0x54c98(0xdd)](_0x379cff);}return _0x137e92;}function createQuickChatButton(_0x3e7303,_0x2393aa,_0x4aed0f){var _0x1f67a9=a0_0x954d29,_0x7dfe17=document[_0x1f67a9(0x114)](_0x1f67a9(0xc9));_0x7dfe17['innerHTML']=_0x3e7303,_0x7dfe17[_0x1f67a9(0xc1)][_0x1f67a9(0xdf)]=_0x4aed0f,_0x7dfe17[_0x1f67a9(0x118)]=_0x1f67a9(0xf2),_0x7dfe17[_0x1f67a9(0xc2)]['add'](_0x1f67a9(0xe7));for(var _0x3705ea=0x0;_0x3705ea<_0x2393aa[_0x1f67a9(0xcc)];_0x3705ea++){var _0x5bdc20=document[_0x1f67a9(0x119)]('data-message-'+_0x3705ea);_0x5bdc20[_0x1f67a9(0xff)]=_0x2393aa[_0x3705ea],_0x7dfe17['setAttributeNode'](_0x5bdc20),_0x3705ea===0x0&&_0x7dfe17[_0x1f67a9(0xb9)](_0x1f67a9(0xc3),'0');}_0x7dfe17[_0x1f67a9(0xe0)]=function(){var _0x41e79f=_0x1f67a9,_0x3393eb=parseInt(this[_0x41e79f(0x104)](_0x41e79f(0xc3))),_0x2938f8=(_0x3393eb+0x1)%_0x2393aa[_0x41e79f(0xcc)],_0x49e0a8=document[_0x41e79f(0x113)]('test_quick_chat_button_message');_0x49e0a8[_0x41e79f(0xff)]=this[_0x41e79f(0x104)](_0x41e79f(0xcf)+_0x3393eb),this[_0x41e79f(0xb9)]('data-checked',_0x2938f8[_0x41e79f(0xe1)]());};var _0x15ee3f=document[_0x1f67a9(0x114)](_0x1f67a9(0xd3));_0x15ee3f[_0x1f67a9(0x118)]=_0x1f67a9(0xf1),_0x15ee3f[_0x1f67a9(0xf3)](_0x7dfe17);var _0x260c9d=document[_0x1f67a9(0x114)](_0x1f67a9(0xc9));_0x260c9d[_0x1f67a9(0x118)]=_0x1f67a9(0xc0),_0x260c9d[_0x1f67a9(0xd4)]=_0x1f67a9(0xf6),_0x260c9d[_0x1f67a9(0xbc)](_0x1f67a9(0xc8),function(_0x3b003f){var _0x41295b=_0x1f67a9;_0x3b003f[_0x41295b(0xe4)](),document[_0x41295b(0x113)](_0x41295b(0xfe))[_0x41295b(0x10f)](_0x15ee3f);}),_0x15ee3f[_0x1f67a9(0xf3)](_0x260c9d);var _0x4c6d00=document[_0x1f67a9(0x114)]('button');_0x4c6d00[_0x1f67a9(0x118)]='edit_quick_chat_button',_0x4c6d00[_0x1f67a9(0xd4)]=_0x1f67a9(0xd5),_0x4c6d00[_0x1f67a9(0xbc)](_0x1f67a9(0xc8),function(_0x4715e0){var _0x39449c=_0x1f67a9;_0x4715e0[_0x39449c(0xe4)](),clearFields(),document['getElementById'](_0x39449c(0x111))[_0x39449c(0xff)]=_0x7dfe17[_0x39449c(0xc7)];var _0x4a9c7a=_0x7dfe17[_0x39449c(0xc1)]['backgroundColor'],_0x3da019=rgbToHex(_0x4a9c7a);document[_0x39449c(0x113)](_0x39449c(0xd1))[_0x39449c(0xff)]=_0x3da019;for(var _0x58d18d=0x0;_0x58d18d<_0x2393aa[_0x39449c(0xcc)];_0x58d18d++){var _0x2690a0=_0x2393aa[_0x58d18d];if(_0x58d18d===0x0){var _0x35a7a2=document[_0x39449c(0xfd)](_0x39449c(0xda))[0x0];_0x35a7a2[_0x39449c(0xff)]=_0x2690a0;}else addQuickChatMessage(_0x2690a0);}}),_0x15ee3f[_0x1f67a9(0xf3)](_0x4c6d00),document[_0x1f67a9(0x113)]('quick_chat_butttons_container')[_0x1f67a9(0xf3)](_0x15ee3f),clearFields(),document['getElementById'](_0x1f67a9(0xf8))[_0x1f67a9(0xc8)]();}function rgbToHex(_0x4bee2a){var _0x2536aa=a0_0x954d29,_0x32813a=_0x4bee2a[_0x2536aa(0x10e)](',')>-0x1?',':'\x20';_0x4bee2a=_0x4bee2a[_0x2536aa(0xfc)](0x4)[_0x2536aa(0x11b)](')')[0x0]['split'](_0x32813a);var _0xf2af0a=(+_0x4bee2a[0x0])['toString'](0x10),_0x2b342e=(+_0x4bee2a[0x1])[_0x2536aa(0xe1)](0x10),_0x28055f=(+_0x4bee2a[0x2])[_0x2536aa(0xe1)](0x10);return _0xf2af0a[_0x2536aa(0xcc)]===0x1&&(_0xf2af0a='0'+_0xf2af0a),_0x2b342e[_0x2536aa(0xcc)]===0x1&&(_0x2b342e='0'+_0x2b342e),_0x28055f['length']===0x1&&(_0x28055f='0'+_0x28055f),'#'+_0xf2af0a+_0x2b342e+_0x28055f;}document[a0_0x954d29(0x113)](a0_0x954d29(0xf8))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x1e43dc){var _0xf50e34=a0_0x954d29;_0x1e43dc[_0xf50e34(0xe4)]();var _0x4c23c8=document[_0xf50e34(0xfd)](_0xf50e34(0xf2)),_0x9cb3ab=[];for(var _0xa9d035=0x0;_0xa9d035<_0x4c23c8['length'];_0xa9d035++){var _0x567ce3=_0x4c23c8[_0xa9d035],_0x1b497f={};_0x1b497f[_0xf50e34(0xbd)]=_0x567ce3[_0xf50e34(0xc7)],_0x1b497f[_0xf50e34(0x101)]=_0x567ce3['style'][_0xf50e34(0xdf)],_0x1b497f[_0xf50e34(0xe5)]=[];for(var _0x29c3b9=0x0;_0x29c3b9<_0x567ce3[_0xf50e34(0xea)][_0xf50e34(0xcc)];_0x29c3b9++){var _0x1a82ed=_0x567ce3[_0xf50e34(0xea)][_0x29c3b9];_0x1a82ed[_0xf50e34(0xbd)][_0xf50e34(0xf4)](_0xf50e34(0xcf))&&_0x1b497f[_0xf50e34(0xe5)][_0xf50e34(0xdd)](_0x1a82ed[_0xf50e34(0xff)]);}_0x9cb3ab[_0xf50e34(0xdd)](_0x1b497f);}chrome[_0xf50e34(0x10a)][_0xf50e34(0xd6)]['set']({'quick_chat_buttons':_0x9cb3ab},function(){var _0x3afbac=_0xf50e34;console[_0x3afbac(0x109)](_0x9cb3ab);});}),document['getElementById']('export_quick_chat_buttons')[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x345b80){var _0x1e723b=a0_0x954d29;_0x345b80[_0x1e723b(0xe4)](),chrome[_0x1e723b(0x10a)]['local'][_0x1e723b(0x117)](_0x1e723b(0x110),function(_0x39ed47){var _0x5c7e3a=_0x1e723b,_0x5c185b=_0x39ed47[_0x5c7e3a(0x110)],_0x5897b2=JSON[_0x5c7e3a(0x108)](_0x5c185b),_0x4fa469=new Blob([_0x5897b2],{'type':_0x5c7e3a(0xd0)});saveAs(_0x4fa469,_0x5c7e3a(0xbb));});});function saveAs(_0x4bf519,_0x3d385e){var _0x546473=a0_0x954d29,_0x29b8c3=window[_0x546473(0x105)]['createObjectURL'](_0x4bf519),_0x4ce9bb=document[_0x546473(0x114)]('a');_0x4ce9bb['href']=_0x29b8c3,_0x4ce9bb[_0x546473(0xe8)]=_0x3d385e,_0x4ce9bb[_0x546473(0xc8)]();}document[a0_0x954d29(0x113)](a0_0x954d29(0x112))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x1a8ecb){var _0x1a2bf7=a0_0x954d29;_0x1a8ecb[_0x1a2bf7(0xe4)]();var _0x4475d7=document[_0x1a2bf7(0x114)](_0x1a2bf7(0xee));_0x4475d7[_0x1a2bf7(0xce)]=_0x1a2bf7(0x102),_0x4475d7['accept']=_0x1a2bf7(0x10d),_0x4475d7['addEventListener']('change',function(_0x26763a){var _0x184652=_0x1a2bf7,_0x5d9da0=_0x4475d7[_0x184652(0xe9)][0x0],_0x6336f3=new FileReader();_0x6336f3['onload']=function(_0x261e9f){var _0x4c2df2=_0x184652,_0xa75df9=_0x261e9f[_0x4c2df2(0xc6)][_0x4c2df2(0xba)],_0x55b6ad=JSON['parse'](_0xa75df9);chrome[_0x4c2df2(0x10a)]['local']['set']({'quick_chat_buttons':_0x55b6ad},function(){var _0xaca869=_0x4c2df2;console['log'](_0x55b6ad),clearQuickChatButtons();for(var _0x15bf6e=0x0;_0x15bf6e<_0x55b6ad[_0xaca869(0xcc)];_0x15bf6e++){var _0x53ec1b=_0x55b6ad[_0x15bf6e];createQuickChatButton(_0x53ec1b[_0xaca869(0xbd)],_0x53ec1b[_0xaca869(0xe5)],_0x53ec1b[_0xaca869(0x101)]),document[_0xaca869(0x113)](_0xaca869(0xf8))['click']();}});},_0x6336f3['readAsText'](_0x5d9da0);}),_0x4475d7['click']();}),document[a0_0x954d29(0x113)](a0_0x954d29(0x115))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x46af2b){var _0x5c6fed=a0_0x954d29;_0x46af2b[_0x5c6fed(0xe4)](),clearQuickChatButtons(),chrome[_0x5c6fed(0x10a)][_0x5c6fed(0xd6)]['set']({'quick_chat_buttons':[]},function(){var _0x5dda21=_0x5c6fed;console[_0x5dda21(0x109)]([]);});}),chrome['storage']['local'][a0_0x954d29(0x117)](a0_0x954d29(0x110),function(_0x2bb10c){var _0x2c7177=a0_0x954d29,_0x3297fe=_0x2bb10c['quick_chat_buttons'];for(var _0x5e7609=0x0;_0x5e7609<_0x3297fe[_0x2c7177(0xcc)];_0x5e7609++){var _0x21ff82=_0x3297fe[_0x5e7609];createQuickChatButton(_0x21ff82[_0x2c7177(0xbd)],_0x21ff82[_0x2c7177(0xe5)],_0x21ff82[_0x2c7177(0x101)]);}});function clearQuickChatButtons(){var _0x160e97=a0_0x954d29,_0x618787=document[_0x160e97(0x113)](_0x160e97(0xfe));while(_0x618787[_0x160e97(0xd7)]){_0x618787[_0x160e97(0x10f)](_0x618787[_0x160e97(0xd7)]);}}function clearFields(){var _0xc6dc7b=a0_0x954d29;document[_0xc6dc7b(0x113)](_0xc6dc7b(0x111))[_0xc6dc7b(0xff)]='';var _0x47587e=document[_0xc6dc7b(0xfd)](_0xc6dc7b(0xcb));while(_0x47587e[_0xc6dc7b(0xcc)]>0x0){_0x47587e[0x0]['parentNode']['removeChild'](_0x47587e[0x0]);}var _0x53b8c6=document[_0xc6dc7b(0xfd)](_0xc6dc7b(0xda));for(var _0x5d2206=0x0;_0x5d2206<_0x53b8c6[_0xc6dc7b(0xcc)];_0x5d2206++){_0x53b8c6[_0x5d2206]['value']='';}}document[a0_0x954d29(0x113)](a0_0x954d29(0xd8))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(){var _0x57bdd6=a0_0x954d29;document['getElementById'](_0x57bdd6(0xcd))[_0x57bdd6(0xc1)]['display']='block';}),document[a0_0x954d29(0x113)](a0_0x954d29(0xbe))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(){var _0x71bf1f=a0_0x954d29;document[_0x71bf1f(0x113)]('massImportModal')[_0x71bf1f(0xc1)]['display']=_0x71bf1f(0xe3);}),document['getElementById'](a0_0x954d29(0xec))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x38465b){var _0x451f71=a0_0x954d29,_0xa7b579=document[_0x451f71(0x113)](_0x451f71(0xfb)),_0x370d30=_0xa7b579[_0x451f71(0xff)];document['getElementById']('massImportModal')[_0x451f71(0xc1)][_0x451f71(0xf0)]='none';var _0xd44fb3=_0x370d30[_0x451f71(0x11b)]('\x0a');for(var _0x3a6c40=0x0;_0x3a6c40<_0xd44fb3[_0x451f71(0xcc)];_0x3a6c40++){var _0x47c2ee=_0xd44fb3[_0x3a6c40];_0x47c2ee=formatMessage(_0x47c2ee);if(_0x47c2ee['trim']()[_0x451f71(0xcc)]>0x0){if(_0x3a6c40===0x0){var _0x22b843=document[_0x451f71(0xfd)]('quick_chat_message')[0x0];_0x22b843['value']=_0x47c2ee;}else addQuickChatMessage(_0x47c2ee);}}});function a0_0x3785(_0x4c2d25,_0x987591){var _0x3431fb=a0_0x3431();return a0_0x3785=function(_0x3785e1,_0x5d9738){_0x3785e1=_0x3785e1-0xb9;var _0x4f38c9=_0x3431fb[_0x3785e1];return _0x4f38c9;},a0_0x3785(_0x4c2d25,_0x987591);}function formatMessage(_0x13e488){var _0x35b056=a0_0x954d29,_0xfc2af6=_0x13e488[_0x35b056(0xf7)]('\x5cr','\x0a');return _0xfc2af6=_0xfc2af6['replaceAll']('\x5cn','\x0a'),_0xfc2af6;}document[a0_0x954d29(0xbc)](a0_0x954d29(0xca),function(){var _0x18196b=a0_0x954d29,_0x54d1bb=document[_0x18196b(0x113)](_0x18196b(0xfe));console[_0x18196b(0x109)](_0x54d1bb),new Sortable(_0x54d1bb,{'animation':0x96,'chosenClass':'sortable-chosen','dragClass':_0x18196b(0x11a)});}),document[a0_0x954d29(0x113)](a0_0x954d29(0xc5))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(_0x47f413){var _0x3159af=a0_0x954d29;_0x47f413[_0x3159af(0xe4)](),clearFields();}),document[a0_0x954d29(0x113)]('load_default_quick_chat_buttons')[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(){var _0x524d1d=a0_0x954d29;document[_0x524d1d(0x113)](_0x524d1d(0xf5))[_0x524d1d(0xc1)][_0x524d1d(0xf0)]=_0x524d1d(0xd2);}),document['getElementById'](a0_0x954d29(0x10b))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),function(){var _0x372c3d=a0_0x954d29;document[_0x372c3d(0x113)](_0x372c3d(0xf5))['style'][_0x372c3d(0xf0)]='none';}),document['getElementById'](a0_0x954d29(0xbf))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),async function(_0x507150){var _0x39d98f=a0_0x954d29;_0x507150[_0x39d98f(0xe4)](),document[_0x39d98f(0x113)](_0x39d98f(0xf5))['style']['display']=_0x39d98f(0xe3),clearQuickChatButtons();var _0x81abbe=await fetch(chrome['runtime']['getURL'](_0x39d98f(0xeb))),_0x31becb=await _0x81abbe['json']();for(var _0x1033bf=0x0;_0x1033bf<_0x31becb[_0x39d98f(0xcc)];_0x1033bf++){var _0x1a1773=_0x31becb[_0x1033bf];createQuickChatButton(_0x1a1773[_0x39d98f(0xbd)],_0x1a1773[_0x39d98f(0xe5)],_0x1a1773[_0x39d98f(0x101)]),document[_0x39d98f(0x113)]('save_quick_chat_buttons')[_0x39d98f(0xc8)]();}}),document[a0_0x954d29(0x113)](a0_0x954d29(0x100))[a0_0x954d29(0xbc)](a0_0x954d29(0xc8),async function(_0x85197b){var _0x2348ab=a0_0x954d29,_0x14c6f6=document['getElementById'](_0x2348ab(0x100)),_0x233c2f=_0x14c6f6['checked'];chrome['storage'][_0x2348ab(0xd6)][_0x2348ab(0xde)]({'quick_chat_switch':_0x233c2f},function(){var _0x3d4542=_0x2348ab;console[_0x3d4542(0x109)](_0x233c2f);});}),chrome[a0_0x954d29(0x10a)][a0_0x954d29(0xd6)][a0_0x954d29(0x117)](a0_0x954d29(0x100),async function(_0x4ff558){var _0x3f59a4=a0_0x954d29,_0x1b30ad=_0x4ff558['quick_chat_switch'],_0x5e06e5=document['getElementById'](_0x3f59a4(0x100));_0x1b30ad===undefined?(_0x1b30ad=![],await chrome[_0x3f59a4(0x10a)]['local'][_0x3f59a4(0xde)]({'quick_chat_switch':_0x1b30ad}),_0x5e06e5[_0x3f59a4(0xed)]=_0x1b30ad):_0x5e06e5[_0x3f59a4(0xed)]=_0x1b30ad;});