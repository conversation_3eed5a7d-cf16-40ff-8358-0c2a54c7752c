var a0_0x8f0572=a0_0x53b8;(function(_0x36f6ed,_0xa1c4a){var _0x4fb65e=a0_0x53b8,_0x1dee63=_0x36f6ed();while(!![]){try{var _0x59f59c=-parseInt(_0x4fb65e(0x16f))/0x1*(parseInt(_0x4fb65e(0x1f9))/0x2)+parseInt(_0x4fb65e(0x1c2))/0x3+-parseInt(_0x4fb65e(0x156))/0x4*(parseInt(_0x4fb65e(0x1d3))/0x5)+parseInt(_0x4fb65e(0x19e))/0x6+-parseInt(_0x4fb65e(0x18e))/0x7+-parseInt(_0x4fb65e(0x1ea))/0x8*(parseInt(_0x4fb65e(0x1db))/0x9)+-parseInt(_0x4fb65e(0x1ef))/0xa*(-parseInt(_0x4fb65e(0x17b))/0xb);if(_0x59f59c===_0xa1c4a)break;else _0x1dee63['push'](_0x1dee63['shift']());}catch(_0x20f95d){_0x1dee63['push'](_0x1dee63['shift']());}}}(a0_0x31f2,0x37698));var waitTime=0x12c;function getItemSpecificFieldsWithProperties(_0xfa5cc=a0_0x8f0572(0x18b)){var _0x297eab=a0_0x8f0572,_0xc5a6f6=getItemSpecificFields(_0xfa5cc);console[_0x297eab(0x1c7)](_0x297eab(0x166),_0xc5a6f6);var _0xcb0af1=getSingleSelectItemSpecificFields(_0xc5a6f6);console[_0x297eab(0x1c7)](_0x297eab(0x223),_0xcb0af1);var _0x40f033=getInputRequiredItemSpecificFields(_0xc5a6f6);console[_0x297eab(0x1c7)](_0x297eab(0x1d5),_0x40f033);var _0x2f823a=getMultiSelectItemSpecificFields(_0xc5a6f6);console['log'](_0x297eab(0x19b),_0x2f823a);var _0x4f504a=getSingleSelectWithLimitedOptionsItemSpecificFields(_0xc5a6f6);console['log'](_0x297eab(0x17f),_0x4f504a);var _0x46a43c=getSingleSelectWithNoSearchItemSpecificFields(_0xc5a6f6);console['log'](_0x297eab(0x220),_0x46a43c);var _0x2d169a=getMultiSelectWithNoSearchItemSpecificFields(_0xc5a6f6);console[_0x297eab(0x1c7)](_0x297eab(0x1dd),_0x2d169a);var _0x2619fb={'singleSelectItemSpecificFields':_0xcb0af1,'inputRequiredItemSpecificFields':_0x40f033,'multiSelectItemSpecificFields':_0x2f823a,'singleSelectWithLimitedOptionsItemSpecificFields':_0x4f504a,'singleSelectWithNoSearchItemSpecificFields':_0x46a43c,'multiSelectWithNoSearchItemSpecificFields':_0x2d169a};return _0x2619fb;}function checkValueExistsInItemSpecificField(_0x338fda,_0x437fa1){var _0xe8c57c=a0_0x8f0572,_0x1b65f7=_0x338fda[_0xe8c57c(0x1f0)]('.summary__attributes--value\x20.se-expand-button__button-text');if(_0x1b65f7&&_0x1b65f7['textContent'])return!![];var _0x570077=_0x338fda[_0xe8c57c(0x1f0)](_0xe8c57c(0x1cc));if(_0x570077&&_0x570077[_0xe8c57c(0x181)])return!![];return![];}function getTotalFilledItemSpecificFields(){var _0x288f71=a0_0x8f0572,_0x76a6c1=getItemSpecificFields(_0x288f71(0x17c)),_0x3a1bb6=0x0;for(var _0x252aa0=0x0;_0x252aa0<_0x76a6c1['length'];_0x252aa0++){checkValueExistsInItemSpecificField(_0x76a6c1[_0x252aa0])?_0x3a1bb6++:console['log'](_0x288f71(0x177),_0x76a6c1[_0x252aa0],_0x252aa0);}return _0x3a1bb6;}function getItemSpecificFields(_0x51de39){var _0x2e9e37=a0_0x8f0572,_0x42ef03=0x0;_0x51de39===_0x2e9e37(0x18b)&&(_0x42ef03=0x0);_0x51de39===_0x2e9e37(0x212)&&(_0x42ef03=0x1);if(_0x51de39===_0x2e9e37(0x17c)){var _0x5b4e7c=getItemSpecificFields(_0x2e9e37(0x212)),_0x2ad1cf=getItemSpecificFields(_0x2e9e37(0x18b)),_0x56c35b=_0x2ad1cf[_0x2e9e37(0x1d6)](_0x5b4e7c);return _0x56c35b;}var _0x27ac4c=document[_0x2e9e37(0x1ba)](_0x2e9e37(0x193));if(_0x27ac4c[_0x2e9e37(0x1c9)]===0x0){var _0x27682f=document[_0x2e9e37(0x1f0)]('.smry.summary__attributes\x20.summary__attributes-gridview');if(_0x27682f&&_0x27682f[_0x2e9e37(0x1f5)][_0x2e9e37(0x1c9)]>0x0){var _0x1bcc8c=_0x27682f[_0x2e9e37(0x1f5)],_0x9436ee=[];for(var _0x372b90=0x0;_0x372b90<_0x1bcc8c[_0x2e9e37(0x1c9)];_0x372b90++){_0x1bcc8c[_0x372b90][_0x2e9e37(0x1b7)][_0x2e9e37(0x16a)](_0x2e9e37(0x1e5))&&_0x9436ee[_0x2e9e37(0x19f)](_0x1bcc8c[_0x372b90]);}_0x27ac4c=_0x9436ee;}}if(_0x27ac4c[_0x2e9e37(0x1c9)]===0x0)return[];if(_0x27ac4c[_0x2e9e37(0x1c9)]===0x1){var _0x39e0db=_0x27ac4c[0x0],_0x368965=_0x39e0db['querySelector']('h3');if(!_0x368965){var _0x26c020=_0x39e0db['querySelector'](_0x2e9e37(0x184));_0x26c020&&(_0x368965=_0x26c020);}if(_0x368965){if(_0x368965[_0x2e9e37(0x180)][_0x2e9e37(0x16d)]()['includes'](_0x2e9e37(0x212))||_0x368965[_0x2e9e37(0x180)][_0x2e9e37(0x16d)]()[_0x2e9e37(0x201)](_0x2e9e37(0x1c1))){if(_0x51de39===_0x2e9e37(0x18b))return[];_0x42ef03=0x0;}else _0x42ef03=0x0;}}var _0x5b88f0=_0x27ac4c[_0x42ef03],_0x242168=[];for(var _0x372b90=0x0;_0x372b90<_0x5b88f0[_0x2e9e37(0x1f5)][_0x2e9e37(0x1c9)];_0x372b90++){if(_0x5b88f0[_0x2e9e37(0x1f5)][_0x372b90]['tagName']===_0x2e9e37(0x197)){_0x242168[_0x2e9e37(0x19f)](_0x5b88f0['children'][_0x372b90]);continue;}_0x5b88f0[_0x2e9e37(0x1f5)][_0x372b90][_0x2e9e37(0x1b7)]['contains']('summary__attributes--fields')&&_0x242168[_0x2e9e37(0x19f)](_0x5b88f0[_0x2e9e37(0x1f5)][_0x372b90]);}return _0x242168;}function getItemSpecificField(_0x142bf5){var _0x5bd4c5=a0_0x8f0572,_0x48cf52=getItemSpecificFields(_0x5bd4c5(0x17c));for(var _0x38230a=0x0;_0x38230a<_0x48cf52['length'];_0x38230a++){var _0x3c10fd=getLabel(_0x48cf52[_0x38230a]);if(_0x3c10fd[_0x5bd4c5(0x16d)]()===_0x142bf5['toLowerCase']())return _0x48cf52[_0x38230a];}}function test(){var _0x581e04=a0_0x8f0572,_0x3f06a5=document[_0x581e04(0x1ba)](_0x581e04(0x1d9)),_0x1d9300=_0x3f06a5[0x6],_0x1cb32e=_0x1d9300[_0x581e04(0x1f0)](_0x581e04(0x206));console[_0x581e04(0x1c7)]('input:',_0x1cb32e),_0x1cb32e[_0x581e04(0x181)]=_0x581e04(0x18a),_0x1cb32e[_0x581e04(0x210)](),keydownEvent=new KeyboardEvent(_0x581e04(0x1e7),{'bubbles':!![],'key':_0x581e04(0x1cf)}),_0x1cb32e[_0x581e04(0x161)](keydownEvent);var _0x2e6a4c=new Event('input',{'bubbles':!![]});_0x1cb32e['dispatchEvent'](_0x2e6a4c),changeEvent=new Event(_0x581e04(0x1b0),{'bubbles':!![]}),_0x1cb32e[_0x581e04(0x161)](changeEvent);}async function fillSingleOptionItemSpecifics(){var _0x3ee43d=a0_0x8f0572,_0x1a26be=getItemSpecificFieldsWithProperties(_0x3ee43d(0x18b)),_0x3a66c7=_0x1a26be['singleSelectWithLimitedOptionsItemSpecificFields'];for(var _0x2491ff=0x0;_0x2491ff<_0x3a66c7[_0x3ee43d(0x1c9)];_0x2491ff++){_0x3a66c7[_0x2491ff][_0x3ee43d(0x18c)][_0x3ee43d(0x1c9)]===0x1&&await fillCustomItemSpecific(_0x3a66c7[_0x2491ff][_0x3ee43d(0x1c3)],_0x3a66c7[_0x2491ff][_0x3ee43d(0x18c)][0x0]);}}function getInputRequiredItemSpecificFields(_0x56b323){var _0x203b5c=a0_0x8f0572,_0x5ca40e=[];for(var _0x2a9cba=0x0;_0x2a9cba<_0x56b323[_0x203b5c(0x1c9)];_0x2a9cba++){var _0x14f668=_0x56b323[_0x2a9cba],_0xcb6489=getTypeOfItemSpecific(_0x14f668);if(_0xcb6489!==_0x203b5c(0x1fe))continue;var _0x247c10=getLabelDefinition(_0x14f668),_0x1893c1=getLabel(_0x14f668),_0x4a0982=_0xcb6489,_0x5bb03c=getCurrentValue(_0x14f668,_0x4a0982),_0x4bf25c={'label':_0x1893c1,'type':_0x4a0982,'currentValue':_0x5bb03c,'labelDefinition':_0x247c10,'recommendedValues':[]};_0x5ca40e[_0x203b5c(0x19f)](_0x4bf25c);}return _0x5ca40e;}function getTextContentExcludeClipped(_0x1d5b91){var _0x99e3f0=a0_0x8f0572,_0x453667=_0x1d5b91[_0x99e3f0(0x1be)];return _0x1d5b91['querySelectorAll']('.textual-display.clipped')['length']>0x0&&(clippedText=_0x1d5b91[_0x99e3f0(0x1f0)]('.textual-display.clipped')['textContent'],_0x453667=_0x453667[_0x99e3f0(0x1eb)](clippedText,'')),_0x453667;}function getSingleSelectItemSpecificFields(_0x32dfcc){var _0x2c211f=a0_0x8f0572,_0x3fd639=[];for(var _0x1fe49b=0x0;_0x1fe49b<_0x32dfcc[_0x2c211f(0x1c9)];_0x1fe49b++){var _0x5e6bf7=_0x32dfcc[_0x1fe49b],_0x2e327c=getTypeOfItemSpecific(_0x5e6bf7);console['log']('getSingleSelectItemSpecificFields\x20type\x20of\x20itemSpecificField',_0x2e327c),console[_0x2c211f(0x1c7)](_0x2c211f(0x205),_0x5e6bf7);if(_0x2e327c!==_0x2c211f(0x1b3))continue;var _0x2a0d55=getLabelDefinition(_0x5e6bf7),_0x42f4e5=getLabel(_0x5e6bf7),_0x49aee9=_0x2e327c,_0x25a3df=getCurrentValue(_0x5e6bf7,_0x49aee9),_0x1610d0=getRecommendedValues(_0x5e6bf7,_0x49aee9),_0x4be0b5={'label':_0x42f4e5,'type':_0x49aee9,'currentValue':_0x25a3df,'recommendedValues':_0x1610d0,'labelDefinition':_0x2a0d55};_0x3fd639[_0x2c211f(0x19f)](_0x4be0b5);}return _0x3fd639;}function getMultiSelectItemSpecificFields(_0x104914){var _0x257828=a0_0x8f0572,_0x42887a=[];for(var _0x23fd1e=0x0;_0x23fd1e<_0x104914['length'];_0x23fd1e++){var _0xe1c86d=_0x104914[_0x23fd1e],_0xbfb71c=getTypeOfItemSpecific(_0xe1c86d);if(_0xbfb71c!==_0x257828(0x170))continue;var _0x1271c7=getLabelDefinition(_0xe1c86d),_0x32c663=getLabel(_0xe1c86d),_0x582eee=_0xbfb71c,_0x34fd8b=getCurrentValue(_0xe1c86d,_0x582eee),_0x591629=getRecommendedValues(_0xe1c86d,_0x582eee),_0x2f3d45={'label':_0x32c663,'type':_0x582eee,'recommendedValues':_0x591629,'currentValue':_0x34fd8b,'labelDefinition':_0x1271c7};_0x42887a[_0x257828(0x19f)](_0x2f3d45);}return _0x42887a;}function getSingleSelectWithLimitedOptionsItemSpecificFields(_0xa350dd){var _0x4aaecd=a0_0x8f0572,_0x5c1271=[];for(var _0x4f03ed=0x0;_0x4f03ed<_0xa350dd[_0x4aaecd(0x1c9)];_0x4f03ed++){var _0x3400fb=_0xa350dd[_0x4f03ed],_0xa88303=getTypeOfItemSpecific(_0x3400fb);if(_0xa88303!==_0x4aaecd(0x219))continue;var _0xc7023=getLabelDefinition(_0x3400fb),_0xd2d96d=getLabel(_0x3400fb),_0x40fffe=_0xa88303,_0x1bfb7d=getCurrentValue(_0x3400fb,_0x40fffe),_0x172eb9=getRecommendedValues(_0x3400fb,_0x40fffe),_0x30411f={'label':_0xd2d96d,'type':_0x40fffe,'currentValue':_0x1bfb7d,'recommendedValues':_0x172eb9,'labelDefinition':_0xc7023};_0x5c1271[_0x4aaecd(0x19f)](_0x30411f);}return _0x5c1271;}function getSingleSelectWithNoSearchItemSpecificFields(_0x179b3c){var _0x45853c=a0_0x8f0572,_0x130c13=[];for(var _0xafd022=0x0;_0xafd022<_0x179b3c['length'];_0xafd022++){var _0xb8961f=_0x179b3c[_0xafd022],_0x22a899=getTypeOfItemSpecific(_0xb8961f);if(_0x22a899!==_0x45853c(0x220))continue;var _0x4dd79e=getLabelDefinition(_0xb8961f),_0x244588=getLabel(_0xb8961f),_0x266ad0=_0x22a899,_0x19b99f=getCurrentValue(_0xb8961f,_0x266ad0),_0x27f06f=getRecommendedValues(_0xb8961f,_0x266ad0),_0x2a9202={'label':_0x244588,'type':_0x266ad0,'currentValue':_0x19b99f,'recommendedValues':_0x27f06f,'labelDefinition':_0x4dd79e};_0x130c13[_0x45853c(0x19f)](_0x2a9202);}return _0x130c13;}function getMultiSelectWithNoSearchItemSpecificFields(_0x54369b){var _0x2bde5c=a0_0x8f0572,_0x5228e8=[];for(var _0x55ae13=0x0;_0x55ae13<_0x54369b[_0x2bde5c(0x1c9)];_0x55ae13++){var _0x68268c=_0x54369b[_0x55ae13],_0x5e0755=getTypeOfItemSpecific(_0x68268c);if(_0x5e0755!==_0x2bde5c(0x1d0))continue;var _0x505312=getLabelDefinition(_0x68268c),_0x2e5eb4=getLabel(_0x68268c),_0x4390ac=_0x5e0755,_0x257390=getCurrentValue(_0x68268c,_0x4390ac),_0x1a9fb0=getRecommendedValues(_0x68268c,_0x4390ac),_0x2f12ea={'label':_0x2e5eb4,'type':_0x4390ac,'currentValue':_0x257390,'recommendedValues':_0x1a9fb0,'labelDefinition':_0x505312};_0x5228e8['push'](_0x2f12ea);}return _0x5228e8;}function removedFilledItemSpecificFields(_0x542f43){var _0x43e79c=a0_0x8f0572,_0x4dfc90=[];for(var _0x2a6c85=0x0;_0x2a6c85<_0x542f43['length'];_0x2a6c85++){var _0x201e42=_0x542f43[_0x2a6c85],_0x2ab785=_0x201e42[_0x43e79c(0x1a3)];if(_0x2ab785!==_0x43e79c(0x1b3)&&_0x2ab785!==_0x43e79c(0x1fe)){_0x4dfc90[_0x43e79c(0x19f)](_0x201e42);continue;}(_0x201e42['currentValue']===''||_0x201e42['currentValue']===undefined||_0x201e42['currentValue']===null)&&_0x4dfc90[_0x43e79c(0x19f)](_0x201e42);}return _0x4dfc90;}function getTypeOfItemSpecific(_0x5dcbed){var _0x4d96ef=a0_0x8f0572,_0x204d74='';if(_0x5dcbed[_0x4d96ef(0x1ba)]('[class*=\x27filter-menu\x27]\x20input[aria-label*=\x27nter\x20your\x20own\x27]')[_0x4d96ef(0x1c9)]>0x0){var _0x26ae12=_0x5dcbed[_0x4d96ef(0x1ba)](_0x4d96ef(0x19c));return _0x26ae12[_0x4d96ef(0x1c9)]>0x0?_0x4d96ef(0x170):_0x4d96ef(0x1b3);}if(_0x5dcbed[_0x4d96ef(0x1ba)](_0x4d96ef(0x154))[_0x4d96ef(0x1c9)]>0x0){var _0x26ae12=_0x5dcbed[_0x4d96ef(0x1ba)](_0x4d96ef(0x19c));return _0x26ae12[_0x4d96ef(0x1c9)]>0x0?_0x4d96ef(0x1d0):_0x4d96ef(0x220);}if(_0x5dcbed['querySelectorAll'](_0x4d96ef(0x18f))[_0x4d96ef(0x1c9)]>0x0)return _0x4d96ef(0x1fe);if(_0x5dcbed[_0x4d96ef(0x1ba)]('.btn--tertiary')[_0x4d96ef(0x1c9)]>0x0)return'singleSelectWithLimitedOptions';if(_0x5dcbed[_0x4d96ef(0x1ba)](_0x4d96ef(0x17a))[_0x4d96ef(0x1c9)]>0x0){var _0x26ae12=_0x5dcbed['querySelectorAll'](_0x4d96ef(0x19c));return _0x26ae12[_0x4d96ef(0x1c9)]>0x0?_0x4d96ef(0x170):'singleSelect';}}function backup_getTypeOfItemSpecific(_0x1e6a87){var _0x1cccf9=a0_0x8f0572,_0x3570a1='';if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x173))[_0x1cccf9(0x1c9)]>0x0&&_0x1e6a87[_0x1cccf9(0x1ba)]('.filter-menu__checkbox')[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1=_0x1cccf9(0x1d0),_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x173))[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1=_0x1cccf9(0x220),_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x1a4))[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1=_0x1cccf9(0x219),_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x1e2))[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1=_0x1cccf9(0x170),_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)]('.se-textbox--input\x20input[name^=\x27attributes.\x27]')[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1=_0x1cccf9(0x1fe),_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x1e1))[_0x1cccf9(0x1c9)]>0x0)return _0x3570a1='singleSelect',_0x3570a1;if(_0x1e6a87[_0x1cccf9(0x1ba)](_0x1cccf9(0x1aa))[_0x1cccf9(0x1c9)]==0x0)return _0x3570a1=_0x1cccf9(0x1b3),_0x3570a1;}function checkIfItemSpecificIsFilled(){}function a0_0x31f2(){var _0x39d25d=['Test','required','recommendedValues','split','2984149mTPlop','.se-textbox--input\x20input','\x20with\x20','textFromAi','itemSpecificField','.summary__attributes--section-container','singleSelectItemSpecificField','parse','required\x20itemSpecificFields','FIELDSET','.filter-menu__text','parentElement','appendChild','multiSelectItemSpecifics','[class*=\x27checkbox\x27]','itemSpecificValue','679716lFvzKc','push','splice','✔️\x20Dialogs\x20Closed','not\x20applicable','type','.summary__attributes--pill','totalFilledItemSpecificFields\x20is\x20greater\x20than\x2045,\x20skipping','singleSelectWithLimitedOptionsItemSpecificField','trim','itemSpecificLabel','labelDefinition','.filter-menu-button__list-menu','singleSelectWithLimitedOptionsItemSpecificFields','{{Product\x20Identifier\x20Labels}}','.filter-menu-button__menu-container\x20input','itemSpecificValuesFromAi','filling\x20single\x20select\x20with\x20limited\x20options\x20','change','Does\x20Not\x20Apply','descriptionText','singleSelect','❌\x20Filling\x20','height','Generate\x20Answer','classList','border','clearInputItemSpecific:\x20Field\x20not\x20found\x20for\x20label','querySelectorAll','⏳\x20Creating\x20Custom\x20Spec:\x20','Unavailable','stopPropagation','textContent','getCombinedItemSpecificFieldProperties','[checked]','additional','1058661RkqiAs','label','key','{{type}}','target','log','getFilteredItemSpecificFields\x20label','length','text_prompts/white_listed_item_specifics.txt','itemSpecificsArray','.summary__attributes--value\x20input','2\x20fillSingleSelectItemSpecific\x20itemSpecificValue','borderRadius','Enter','multiSelectWithNoSearch','{{label}}','productIdentifierLabels','629390FItIbB','itemSpecificField\x20not\x20found','inputRequiredItemSpecifics','concat','{{recommendedValues}}','⏳\x20Filling\x20','fieldset','text_prompts/product_specifics_prompt_2.txt','496782DwXQtH','createElement','multiSelectWithNoSearchItemSpecifics','currentValue\x20is\x20array','⏳\x20Dialogs\x20Still\x20Open','dialog\x20closed','.menu__item','.filter-menu__checkbox','filling\x20single\x20select\x20','itemSpecificFields','se-panel-section','inputRequiredItemSpecificFields','keydown','removeItemSpecifics:\x20itemSpecifics','notLoading','24tHfHPC','replace','{{productData}}','text_prompts/ProductIdentifierTemplate.txt','filteredWords','20jYxxMc','querySelector','button','width','fillSingleSelectItemSpecific\x20itemSpecificLabel','isArray','children','3px\x20solid\x20#FF0000','error','loading','6iJszIv','success','animation','title','inputRequiredItemSpecificField','inputRequired','itemSpecific\x20is\x20already\x20added','{{data-template}}','includes','loader','singleSelectWithNoSearchItemSpecificFields','mpn','getSingleSelectItemSpecificFields\x20itemSpecificField','.filter-menu\x20input','checkIfItemSpecificIsAdded','✅\x20Removed\x20restricted\x20words\x20from\x20item\x20specifics','valueButtonOption\x20is\x20null','text','multiSelectWithNoSearchItemSpecificFields','response','itemSpecificFieldsSeparateObjects','.se-filter-menu-button__clear.fake-link','⏳\x20Clearing\x20','focus','Clear\x20button\x20not\x20found\x20for\x20label','optional','text_prompts/item_specific_prompt.txt','whiteListedItemSpecifics','50%','error\x20creating\x20custom\x20item\x20specific','style','{{Label}}','singleSelectWithLimitedOptions','filter','3px\x20solid\x20#FFFF00','.loader','itemSpecificFields[i]:','.btn--tertiary','currentValue[i]','singleSelectWithNoSearch','removeRestrictedWordsFromItemSpecifics:\x20itemSpecifics','currentValue\x20is\x20null\x20or\x20undefined','singleSelectItemSpecifics','clearSingleSelectItemSpecific:\x20Field\x20not\x20found\x20for\x20label','itemSpecificFieldProperties','choices','\x20(1/1)','text_prompts/ProductInformationTemplate2.txt','Lame','[class*=\x27filter-menu\x27]\x20input[aria-label=\x27Search\x27]','div','8aSDaJA','input','generate-answer-button','slice','parentNode','click','charAt','map','attributes','1\x20fillSingleSelectItemSpecific\x20itemSpecificValue','itemSpecPromptV3','dispatchEvent','toUpperCase','currentValue','Total\x20is\x20greater\x20than\x2045,\x20stopping','filteredItemSpecifics','getItemSpecificFieldsWithProperties\x20itemSpecificFields','spin\x202s\x20linear\x20infinite','join','recommendedValuesString','contains','Not\x20Specified','3px\x20solid\x20#00FF00','toLowerCase','multiSelectItemSpecificField','102238SytIWV','multiSelect','.filter-button','offsetWidth','.se-search-box__field\x20input[aria-label=\x27Search\x27]','filledProductInformationTemplate','product_data','Total\x20is\x20greater\x20than\x2045,\x20skipping','value\x20does\x20not\x20exist\x20in\x20itemSpecificField','\x20values','skipping\x20length','[class*=\x27filter-menu\x27]\x20input[id*=\x27search-box-input-textbox\x27]','5011413WRIjko','all','✔️\x20Filling\x20','bullet_points','singleSelectWithLimitedOptionsItemSpecifics','innerText','value','Yes','itemSpecificValuesFromAi[key]','legend','text_prompts/item_specific_prompt_no_recommended_values.txt','substring','multiSelectItemSpecificFields','singleSelectItemSpecificFields','getFilteredItemSpecificFields\x20itemSpecificField'];a0_0x31f2=function(){return _0x39d25d;};return a0_0x31f2();}async function fillInputItemSpecific(_0x1e421e,_0x40719a){var _0x183a9b=a0_0x8f0572;documentTitle=_0x183a9b(0x1d8)+_0x1e421e+_0x183a9b(0x190)+_0x40719a+_0x183a9b(0x151),console[_0x183a9b(0x1c7)](_0x183a9b(0x1a8),_0x1e421e),console[_0x183a9b(0x1c7)](_0x183a9b(0x19d),_0x40719a);var _0x2822d7=getItemSpecificField(_0x1e421e);console[_0x183a9b(0x1c7)](_0x183a9b(0x192),_0x2822d7),scrollIntoView(_0x2822d7),highlightElement(_0x2822d7,'loading'),await waitSomeTime(0x64);var _0x37bf70=checkIfItemSpecificIsAdded(_0x1e421e,_0x40719a);if(_0x37bf70)return console[_0x183a9b(0x1c7)](_0x183a9b(0x1ff)),highlightElement(_0x2822d7,_0x183a9b(0x1fa)),await waitSomeTime(0x64),console[_0x183a9b(0x1c7)]('itemSpecific\x20is\x20already\x20added,\x20skipping'),!![];var _0x254a60=_0x2822d7[_0x183a9b(0x1f0)](_0x183a9b(0x157));return console['log'](_0x183a9b(0x157),_0x254a60),_0x254a60[_0x183a9b(0x210)](),_0x254a60[_0x183a9b(0x181)]=_0x40719a,dispatchKeyBoardAndChangeEvent(_0x254a60),await waitForNetworkResponseCountIncrease(),_0x37bf70=checkIfItemSpecificIsAdded(_0x1e421e,_0x40719a),_0x37bf70&&(highlightElement(_0x2822d7,_0x183a9b(0x1fa)),documentTitle='✔️\x20Filling\x20'+_0x1e421e+_0x183a9b(0x190)+_0x40719a+'\x20(1/1)'),!_0x37bf70&&(highlightElement(_0x2822d7,_0x183a9b(0x1f7)),documentTitle=_0x183a9b(0x1b4)+_0x1e421e+_0x183a9b(0x190)+_0x40719a+'\x20(1/1)'),await waitSomeTime(waitTime),await closeDialogs(),!![];}async function fillSingleSelectItemSpecific(_0xc5a59c,_0x15ff14,_0x28ba3a=0x1,_0x35c379=0x1){var _0x408f20=a0_0x8f0572;console[_0x408f20(0x1c7)](_0x408f20(0x15f),_0x15ff14);if(Array[_0x408f20(0x1f4)](_0x15ff14)){for(let _0xb2e1ab=0x0;_0xb2e1ab<_0x15ff14[_0x408f20(0x1c9)];_0xb2e1ab++){const _0x363f2e=_0x15ff14[_0xb2e1ab];if(_0x363f2e[_0x408f20(0x1c9)]<0x19){_0x15ff14=_0x363f2e;break;}}Array[_0x408f20(0x1f4)](_0x15ff14)&&(_0x15ff14=_0x15ff14[0x0]);}if(_0x15ff14[_0x408f20(0x1c9)]>0x19)return;var _0x463fa7=getItemSpecificField(_0xc5a59c);if(!_0x463fa7){console[_0x408f20(0x1c7)](_0x408f20(0x1d4));return;}documentTitle=_0x408f20(0x1d8)+_0xc5a59c+_0x408f20(0x190)+_0x15ff14+'\x20('+_0x35c379+'/'+_0x28ba3a+')',console[_0x408f20(0x1c7)](_0x408f20(0x1f3),_0xc5a59c),console['log'](_0x408f20(0x1cd),_0x15ff14),scrollIntoView(_0x463fa7),highlightElement(_0x463fa7,_0x408f20(0x1f8)),await waitSomeTime(0x64);var _0x24240f=checkIfItemSpecificIsAdded(_0xc5a59c,_0x15ff14);if(_0x24240f){console[_0x408f20(0x1c7)](_0x408f20(0x1ff)),highlightElement(_0x463fa7,_0x408f20(0x1fa)),await waitSomeTime(0x64);return;}var _0x37dc48=_0x463fa7['querySelector'](_0x408f20(0x157));_0x37dc48[_0x408f20(0x210)](),_0x37dc48[_0x408f20(0x181)]=_0x15ff14,dispatchKeyBoardAndChangeEvent(_0x37dc48),await waitForNetworkResponseCountIncrease(),_0x24240f=checkIfItemSpecificIsAdded(_0xc5a59c,_0x15ff14),_0x24240f&&(documentTitle=_0x408f20(0x17d)+_0xc5a59c+_0x408f20(0x190)+_0x15ff14+'\x20('+_0x35c379+'/'+_0x28ba3a+')',highlightElement(_0x463fa7,_0x408f20(0x1fa)),await waitSomeTime(0x64)),!_0x24240f&&(documentTitle=_0x408f20(0x1b4)+_0xc5a59c+_0x408f20(0x190)+_0x15ff14+'\x20('+_0x35c379+'/'+_0x28ba3a+')',highlightElement(_0x463fa7,_0x408f20(0x1f7)),await waitSomeTime(0x64)),await waitSomeTime(waitTime),await closeDialogs();}async function fillMultiSelectItemSpecific(_0xa9b1c2,_0x8e2185){var _0x260ae5=a0_0x8f0572;!Array['isArray'](_0x8e2185)&&(_0x8e2185=[_0x8e2185]);documentTitle='⏳\x20Filling\x20'+_0xa9b1c2+_0x260ae5(0x190)+_0x8e2185[_0x260ae5(0x1c9)]+_0x260ae5(0x178);var _0x43a673=_0x8e2185[_0x260ae5(0x159)](0x0,0x19);for(var _0x11e57e=0x0;_0x11e57e<_0x43a673['length'];_0x11e57e++){var _0xf91b7e=_0x43a673[_0x11e57e];_0xf91b7e[_0x260ae5(0x1c9)]>0x1e&&_0x43a673[_0x260ae5(0x1a0)](_0x11e57e,0x1);}if(_0x43a673[_0x260ae5(0x1c9)]>0x0){var _0x4c5d4a={[_0xa9b1c2]:_0x43a673};await postViaNetworkRequest(_0x260ae5(0x15e),_0x4c5d4a),await waitForNetworkResponseCountIncrease();}}async function fillSingleSelectWithLimitedOptionsItemSpecific(_0x4b37ef,_0x104636){var _0x3900a3=a0_0x8f0572;Array[_0x3900a3(0x1f4)](_0x104636)&&(_0x104636=_0x104636[0x0]);documentTitle=_0x3900a3(0x1d8)+_0x4b37ef+_0x3900a3(0x190)+_0x104636+'\x20(1/1)';var _0x47702b=getItemSpecificField(_0x4b37ef);console['log']('itemSpecificField',_0x47702b),scrollIntoView(_0x47702b),highlightElement(_0x47702b,_0x3900a3(0x1f8)),await waitSomeTime(0x64);var _0x1f5d9e=getButtonOptionsForSingleSelectWithLimitedOptions(_0x47702b),_0x3063f1=null;for(var _0x28eebf=0x0;_0x28eebf<_0x1f5d9e[_0x3900a3(0x1c9)];_0x28eebf++){var _0x53fce9=_0x1f5d9e[_0x28eebf],_0x5a3965=getTextContentExcludeClipped(_0x53fce9);if(_0x5a3965==_0x104636){_0x3063f1=_0x53fce9;break;}}if(_0x3063f1==null){console[_0x3900a3(0x1c7)](_0x3900a3(0x209)),highlightElement(_0x47702b,_0x3900a3(0x1f7));return;}_0x3063f1[_0x3900a3(0x15b)](),highlightElement(_0x47702b,_0x3900a3(0x1fa)),await waitForNetworkResponseCountIncrease(),await waitSomeTime(0x64),documentTitle='✔️\x20Filling\x20'+_0x4b37ef+_0x3900a3(0x190)+_0x104636+_0x3900a3(0x151),await waitSomeTime(waitTime),await closeDialogs();}async function closeDialogs(){var _0x51fa52=a0_0x8f0572,_0x35daf3=document[_0x51fa52(0x1ba)]('button[aria-label=\x27Close\x20dialog\x27]');_0x35daf3['length']>0x0&&(documentTitle='⏳\x20Closing\x20Dialogs',await makeTabActive(),_0x35daf3[0x0][_0x51fa52(0x210)](),_0x35daf3[0x0][_0x51fa52(0x15b)](),await waitSomeTime(0x12c),await tellChromeToSwitchToPreviousTab(),_0x35daf3=document['querySelectorAll']('button[aria-label=\x27Close\x20dialog\x27]'),_0x35daf3[_0x51fa52(0x1c9)]>0x0?(documentTitle=_0x51fa52(0x1df),await waitSomeTime(0x12c),_0x35daf3[0x0][_0x51fa52(0x210)](),_0x35daf3[0x0][_0x51fa52(0x15b)]()):(console[_0x51fa52(0x1c7)](_0x51fa52(0x1e0)),documentTitle=_0x51fa52(0x1a1)));}function a0_0x53b8(_0x20128d,_0x15e9f9){var _0x31f295=a0_0x31f2();return a0_0x53b8=function(_0x53b8a6,_0x114490){_0x53b8a6=_0x53b8a6-0x150;var _0x16e866=_0x31f295[_0x53b8a6];return _0x16e866;},a0_0x53b8(_0x20128d,_0x15e9f9);}async function fillCustomItemSpecific(_0x9d572f,_0x4676d4){var _0x3d4c16=a0_0x8f0572;documentTitle=_0x3d4c16(0x1bb)+_0x9d572f+'\x20with\x20'+_0x4676d4+'\x20(1/1)';var _0x1bb360={[_0x9d572f]:[_0x4676d4]};await postViaNetworkRequest(_0x3d4c16(0x15e),_0x1bb360),await waitForNetworkResponseCountIncrease();}async function fillItemSpecifics(_0x5ac15c){var _0x143b26=a0_0x8f0572;for(var _0x4a5963=0x0;_0x4a5963<_0x5ac15c[_0x143b26(0x1c9)];_0x4a5963++){await fillItemSpecific(_0x5ac15c[_0x4a5963][_0x143b26(0x1c3)],_0x5ac15c[_0x4a5963][_0x143b26(0x181)]);}}function getCombinedItemSpecificFieldProperties(_0x4c6e28='all'){var _0x19803e=a0_0x8f0572,_0x2e77b3=getItemSpecificFieldsWithProperties(_0x4c6e28);console[_0x19803e(0x1c7)](_0x19803e(0x20d),_0x2e77b3);var _0xcad7ed=_0x2e77b3[_0x19803e(0x1e6)],_0x17e5fe=_0x2e77b3[_0x19803e(0x187)],_0x39e7b0=_0x2e77b3[_0x19803e(0x188)],_0xd9f081=_0x2e77b3[_0x19803e(0x1ab)],_0x30d2d7=_0x2e77b3[_0x19803e(0x203)],_0x58318a=_0x2e77b3['multiSelectWithNoSearchItemSpecificFields'],_0x5f18b0=_0xcad7ed[_0x19803e(0x1d6)](_0x17e5fe,_0x39e7b0,_0xd9f081,_0x30d2d7,_0x58318a);return console[_0x19803e(0x1c7)](_0x19803e(0x1bf),_0x5f18b0),_0x5f18b0;}function getItemSpecificFieldProperties(_0x162ca7){var _0x4c0c2b=a0_0x8f0572,_0x4826cd=getCombinedItemSpecificFieldProperties('all');console['log']('itemSpecificFields',_0x4826cd);for(var _0x1b634b=0x0;_0x1b634b<_0x4826cd['length'];_0x1b634b++){if(_0x4826cd[_0x1b634b][_0x4c0c2b(0x1c3)]==_0x162ca7)return _0x4826cd[_0x1b634b];}}async function fillItemSpecific(_0x2c67f0,_0xb2ef20){var _0x123756=a0_0x8f0572,_0x34418a=![],_0x1ac138=getCombinedItemSpecificFieldProperties(_0x123756(0x17c));for(var _0x2af2f4=0x0;_0x2af2f4<_0x1ac138[_0x123756(0x1c9)];_0x2af2f4++){console[_0x123756(0x1c7)](_0x123756(0x21d),_0x1ac138[_0x2af2f4]);if(_0x1ac138[_0x2af2f4][_0x123756(0x1c3)]['toLowerCase']()==_0x2c67f0[_0x123756(0x16d)]()){if(_0x1ac138[_0x2af2f4][_0x123756(0x1a3)]==_0x123756(0x170)){console[_0x123756(0x1c7)]('filling\x20multi\x20select\x20'+_0x2c67f0+_0x123756(0x190)+_0xb2ef20),await fillMultiSelectItemSpecific(_0x2c67f0,_0xb2ef20),_0x34418a=!![];break;}else{if(_0x1ac138[_0x2af2f4][_0x123756(0x1a3)]=='singleSelect'){console['log'](_0x123756(0x1e3)+_0x2c67f0+'\x20with\x20'+_0xb2ef20),await fillSingleSelectItemSpecific(_0x2c67f0,_0xb2ef20),_0x34418a=!![];break;}else{if(_0x1ac138[_0x2af2f4][_0x123756(0x1a3)]==_0x123756(0x219)){console[_0x123756(0x1c7)](_0x123756(0x1af)+_0x2c67f0+'\x20with\x20'+_0xb2ef20),await fillSingleSelectWithLimitedOptionsItemSpecific(_0x2c67f0,_0xb2ef20),_0x34418a=!![];break;}}}}}!_0x34418a&&(console[_0x123756(0x1c7)]('creating\x20custom\x20item\x20specific\x20'+_0x2c67f0+_0x123756(0x190)+_0xb2ef20),await fillCustomItemSpecific(_0x2c67f0,_0xb2ef20));}async function fillEmptyItemSpecifics(_0x3dc78f){var _0x4376eb=a0_0x8f0572,_0x3f2cf7=getTotalFilledItemSpecificFields();console['log']('Total:\x20',_0x3f2cf7);if(_0x3f2cf7>=0x2d){console[_0x4376eb(0x1c7)](_0x4376eb(0x176));return;}for(var _0x44a29a=0x0;_0x44a29a<_0x3dc78f[_0x4376eb(0x1c9)];_0x44a29a++){await fillEmptyItemSpecific(_0x3dc78f[_0x44a29a][_0x4376eb(0x1c3)],_0x3dc78f[_0x44a29a][_0x4376eb(0x181)]);if(_0x3f2cf7+_0x44a29a>=0x2d){console['log'](_0x4376eb(0x164));return;}}}async function fillEmptyItemSpecific(_0x49c38f,_0x1b4ef6){var _0x196143=a0_0x8f0572,_0x1c30dc=![];if(_0x1b4ef6[_0x196143(0x1c9)]>0x32)return;var _0x22fb50=getCombinedItemSpecificFieldProperties('all');for(var _0x431554=0x0;_0x431554<_0x22fb50[_0x196143(0x1c9)];_0x431554++){if(_0x22fb50[_0x431554][_0x196143(0x1c3)]==_0x49c38f){_0x1c30dc=!![];if(_0x22fb50[_0x431554][_0x196143(0x163)]==null||_0x22fb50[_0x431554]['currentValue']=='')await fillItemSpecific(_0x49c38f,_0x1b4ef6);else continue;}}if(!_0x1c30dc)try{await fillCustomItemSpecific(_0x49c38f,_0x1b4ef6);}catch(_0x570638){console[_0x196143(0x1c7)]('error\x20creating\x20custom\x20item\x20specific',_0x49c38f,_0x1b4ef6);try{await fillCustomItemSpecific(_0x49c38f,_0x1b4ef6);}catch(_0x4a1b7d){console[_0x196143(0x1c7)]('error\x20creating\x20custom\x20item\x20specific',_0x49c38f,_0x1b4ef6);return;}}}async function fillEmptyRequiredItemSpecificsV2(_0x194667){var _0xa299ad=a0_0x8f0572,_0x1bc39f=getCombinedItemSpecificFieldProperties(_0xa299ad(0x18b));console[_0xa299ad(0x1c7)](_0xa299ad(0x196),_0x1bc39f);for(var _0x51a765=0x0;_0x51a765<_0x1bc39f['length'];_0x51a765++){var _0x4e4ffe=_0x1bc39f[_0x51a765];console[_0xa299ad(0x1c7)](_0xa299ad(0x192),_0x4e4ffe);if(_0x4e4ffe['currentValue']==null||_0x4e4ffe['currentValue']==''){var _0x4d963e=_0x194667;_0x4e4ffe['label'][_0xa299ad(0x16d)]()==_0xa299ad(0x204)&&(_0x4d963e=_0x194667[_0xa299ad(0x1eb)](/\s/g,'_'));try{await fillCustomItemSpecific(_0x4e4ffe[_0xa299ad(0x1c3)],_0x4d963e);}catch(_0x28b97d){console['log'](_0xa299ad(0x216),_0x4e4ffe[_0xa299ad(0x1c3)],_0x4d963e);}}}}async function fillEmptyOptionalItemSpecificsV2(_0x418835){var _0x2ead66=a0_0x8f0572,_0xb900ce=getCombinedItemSpecificFieldProperties(_0x2ead66(0x212));console[_0x2ead66(0x1c7)](_0x2ead66(0x1e4),_0xb900ce);for(var _0x16dd46=0x0;_0x16dd46<_0xb900ce[_0x2ead66(0x1c9)];_0x16dd46++){var _0x3c81ff=getTotalFilledItemSpecificFields(_0xb900ce);if(_0x3c81ff>=0x2d){console[_0x2ead66(0x1c7)](_0x2ead66(0x1a5));return;}if(_0xb900ce[_0x16dd46][_0x2ead66(0x1c3)][_0x2ead66(0x16d)]()=='length'){console[_0x2ead66(0x1c7)](_0x2ead66(0x179));return;}var _0x3446d7=_0xb900ce[_0x16dd46];console['log'](_0x2ead66(0x192),_0x3446d7);if(_0x3446d7[_0x2ead66(0x163)]==null||_0x3446d7[_0x2ead66(0x163)]==''){var _0x15d848=_0x418835;_0x3446d7[_0x2ead66(0x1c3)][_0x2ead66(0x16d)]()=='mpn'&&(_0x15d848=_0x418835[_0x2ead66(0x1eb)](/\s/g,'_'));try{await fillCustomItemSpecific(_0x3446d7['label'],_0x15d848);}catch(_0x2c7244){console[_0x2ead66(0x1c7)](_0x2ead66(0x216),_0x3446d7[_0x2ead66(0x1c3)],_0x15d848);}}if(_0x16dd46>=0x4){console['log']('stopping\x20after\x204');return;}}}async function fillEmptyRequiredItemSpecifics(_0x5ae3b0){var _0x16dbf0=a0_0x8f0572,_0x153fa2=getItemSpecificFieldsWithProperties(_0x16dbf0(0x18b));for(var _0x24c2cd=0x0;_0x24c2cd<_0x153fa2[_0x16dbf0(0x1e6)][_0x16dbf0(0x1c9)];_0x24c2cd++){var _0x211504=_0x153fa2[_0x16dbf0(0x1e6)][_0x24c2cd];console[_0x16dbf0(0x1c7)](_0x16dbf0(0x1fd),_0x211504),(_0x211504[_0x16dbf0(0x163)]==null||_0x211504['currentValue']=='')&&await fillItemSpecific(_0x211504[_0x16dbf0(0x1c3)],_0x5ae3b0);}for(var _0x24c2cd=0x0;_0x24c2cd<_0x153fa2[_0x16dbf0(0x187)][_0x16dbf0(0x1c9)];_0x24c2cd++){var _0x41502b=_0x153fa2[_0x16dbf0(0x187)][_0x24c2cd];console['log'](_0x16dbf0(0x16e),_0x41502b),(_0x41502b['currentValue']==null||_0x41502b[_0x16dbf0(0x163)]=='')&&await fillItemSpecific(_0x41502b[_0x16dbf0(0x1c3)],_0x5ae3b0);}for(var _0x24c2cd=0x0;_0x24c2cd<_0x153fa2[_0x16dbf0(0x188)]['length'];_0x24c2cd++){var _0x4a7b3f=_0x153fa2[_0x16dbf0(0x188)][_0x24c2cd];console[_0x16dbf0(0x1c7)](_0x16dbf0(0x194),_0x4a7b3f),(_0x4a7b3f[_0x16dbf0(0x163)]==null||_0x4a7b3f[_0x16dbf0(0x163)]=='')&&await fillItemSpecific(_0x4a7b3f[_0x16dbf0(0x1c3)],_0x5ae3b0);}for(var _0x24c2cd=0x0;_0x24c2cd<_0x153fa2[_0x16dbf0(0x1ab)][_0x16dbf0(0x1c9)];_0x24c2cd++){var _0x206997=_0x153fa2[_0x16dbf0(0x1ab)][_0x24c2cd];console[_0x16dbf0(0x1c7)](_0x16dbf0(0x1a6),_0x206997),(_0x206997[_0x16dbf0(0x181)]==null||_0x206997['value']=='')&&await fillItemSpecific(_0x206997[_0x16dbf0(0x1c3)],_0x5ae3b0);}}async function fillItemSpecificValuesWithAiV2(_0x5c4e82){var _0x4c8e82=a0_0x8f0572,_0x40a298=combineProductData(_0x5c4e82);console[_0x4c8e82(0x1c7)](_0x4c8e82(0x175),_0x40a298);var _0x493d5c=getCombinedItemSpecificFieldProperties('all'),_0x3e203c=await getFilteredItemSpecificFields(_0x493d5c);console[_0x4c8e82(0x1c7)]('filteredItemSpecificFields',_0x3e203c);var _0x4a9411=_0x4c8e82(0x1ed),_0x9df218=await fetchPrompt(_0x4a9411),_0x1d22a4='';for(var _0x4371ef=0x0;_0x4371ef<_0x3e203c['length'];_0x4371ef++){var _0xa0653e=_0x3e203c[_0x4371ef],_0x42afa0=_0xa0653e[_0x4c8e82(0x1c3)],_0x35fde3='';if(_0xa0653e[_0x4c8e82(0x18c)]!=null)for(var _0x7da0bc=0x0;_0x7da0bc<_0xa0653e['recommendedValues'][_0x4c8e82(0x1c9)];_0x7da0bc++){var _0x3e8976=_0xa0653e[_0x4c8e82(0x18c)][_0x7da0bc];_0x7da0bc==0x0?_0x35fde3+=_0x3e8976:_0x35fde3+=',\x20'+_0x3e8976;}var _0x4741ca='';_0x4741ca=_0x9df218['replace']('{{Label}}',_0x42afa0),_0x4741ca=_0x4741ca[_0x4c8e82(0x1eb)]('{{recommendedValues}}',_0x35fde3),_0x1d22a4+=_0x4741ca+'\x0a\x0a';}console[_0x4c8e82(0x1c7)]('combinedProductInformation',_0x1d22a4);var _0x31b6b3=getAllProductIdentifiers(_0x3e203c);console['log'](_0x4c8e82(0x1d2),_0x31b6b3);var _0x4a9411='text_prompts/itemSpecPromptV3.txt',_0x1703ce=await fetchPrompt(_0x4a9411);_0x1703ce=_0x1703ce[_0x4c8e82(0x1eb)](_0x4c8e82(0x1ac),_0x31b6b3),_0x1703ce=_0x1703ce[_0x4c8e82(0x1eb)]('{{Product\x20Identifiers}}',_0x1d22a4),_0x1703ce=_0x1703ce[_0x4c8e82(0x1eb)]('{{productData}}',_0x40a298),console[_0x4c8e82(0x1c7)](_0x4c8e82(0x160),_0x1703ce);var _0x1e0ae3=await receiveOpenAiResponseFromBackgroundScript(_0x1703ce,0x0);console['log'](_0x4c8e82(0x20c),_0x1e0ae3);var _0x242b3a=_0x1e0ae3[_0x4c8e82(0x150)][0x0][_0x4c8e82(0x20a)];console[_0x4c8e82(0x1c7)]('textFromAi',_0x242b3a),_0x242b3a='{'+_0x242b3a,console[_0x4c8e82(0x1c7)](_0x4c8e82(0x191),_0x242b3a);var _0x3f8eb4=JSON['parse'](_0x242b3a);console[_0x4c8e82(0x1c7)](_0x4c8e82(0x1ae),_0x3f8eb4);for(var _0x529c9a in _0x3f8eb4){_0x3f8eb4[_0x529c9a]!=null&&_0x3f8eb4[_0x529c9a]!=''&&((_0x3f8eb4[_0x529c9a]!=_0x4c8e82(0x1b1)||_0x3f8eb4[_0x529c9a]!=_0x4c8e82(0x16b)||_0x3f8eb4[_0x529c9a][_0x4c8e82(0x16d)]()!=_0x4c8e82(0x1a2))&&(console['log'](_0x4c8e82(0x1c4),_0x529c9a),console[_0x4c8e82(0x1c7)](_0x4c8e82(0x183),_0x3f8eb4[_0x529c9a]),await fillItemSpecific(_0x529c9a,_0x3f8eb4[_0x529c9a])));}}function getAllProductIdentifiers(_0x5c492e){var _0x5df8c8=a0_0x8f0572,_0x55cc40='';for(var _0x55e013=0x0;_0x55e013<_0x5c492e[_0x5df8c8(0x1c9)];_0x55e013++){var _0x151a40=_0x5c492e[_0x55e013],_0x300245=_0x151a40[_0x5df8c8(0x1c3)];_0x300245!=null&&(_0x55cc40==''?_0x55cc40+=_0x300245:_0x55cc40+=',\x20'+_0x300245);}return _0x55cc40='['+_0x55cc40+']',_0x55cc40;}async function fillItemSpecificValuesWithAi(_0x173566){var _0x6bff67=a0_0x8f0572,_0x262ef8=combineProductData(_0x173566),_0x43e764=getCombinedItemSpecificFieldProperties(_0x6bff67(0x17c)),_0x2a03ba=await getFilteredItemSpecificFields(_0x43e764);console[_0x6bff67(0x1c7)]('filteredItemSpecificFields',_0x2a03ba);var _0x5f0f16=await fillProductInformationTemplate(_0x2a03ba);console[_0x6bff67(0x1c7)](_0x6bff67(0x174),_0x5f0f16);var _0x47b9d1=_0x6bff67(0x1da),_0x3777f3=await fetchPrompt(_0x47b9d1),_0x3f5806=_0x3777f3[_0x6bff67(0x1eb)]('{{product-data}}',_0x262ef8),_0x3f5806=_0x3f5806[_0x6bff67(0x1eb)](_0x6bff67(0x200),_0x5f0f16);console[_0x6bff67(0x1c7)]('completed_prompt',_0x3f5806);var _0x48d9a8=await receiveOpenAiResponseFromBackgroundScript(_0x3f5806,0x0);console[_0x6bff67(0x1c7)](_0x6bff67(0x20c),_0x48d9a8);var _0x288e15=_0x48d9a8[_0x6bff67(0x150)][0x0][_0x6bff67(0x20a)];console[_0x6bff67(0x1c7)]('textFromAi',_0x288e15);_0x288e15[_0x6bff67(0x15c)](0x0)=='\x22'&&(_0x288e15=_0x288e15[_0x6bff67(0x186)](0x1));_0x288e15['charAt'](_0x288e15['length']-0x1)=='\x22'&&(_0x288e15=_0x288e15[_0x6bff67(0x186)](0x0,_0x288e15[_0x6bff67(0x1c9)]-0x1));console['log'](_0x6bff67(0x191),_0x288e15);var _0x359601=JSON[_0x6bff67(0x195)](_0x288e15);console[_0x6bff67(0x1c7)](_0x6bff67(0x1ae),_0x359601);for(var _0xfeec4d in _0x359601){_0x359601[_0xfeec4d]!=null&&_0x359601[_0xfeec4d]!=''&&(_0x359601[_0xfeec4d]!=_0x6bff67(0x1b1)&&_0x359601[_0xfeec4d]!=_0x6bff67(0x16b)&&(console[_0x6bff67(0x1c7)](_0x6bff67(0x1c4),_0xfeec4d),console['log'](_0x6bff67(0x183),_0x359601[_0xfeec4d]),await fillItemSpecific(_0xfeec4d,_0x359601[_0xfeec4d])));}}function removeQuotes(_0x397f14){var _0x2bade5=a0_0x8f0572;_0x397f14['charAt'](0x0)=='\x22'&&(_0x397f14=_0x397f14[_0x2bade5(0x186)](0x1)),_0x397f14[_0x2bade5(0x15c)](_0x397f14[_0x2bade5(0x1c9)]-0x1)=='\x22'&&(_0x397f14=_0x397f14['substring'](0x0,_0x397f14[_0x2bade5(0x1c9)]-0x1));}async function getFilteredItemSpecificWords(){var _0x167168=a0_0x8f0572,_0x12aefd=[],_0x12f463='text_prompts/filtered_words.txt',_0x46d76e=await fetchPrompt(_0x12f463);return _0x12aefd=_0x46d76e[_0x167168(0x18d)](',')[_0x167168(0x15d)](function(_0x4b431f){var _0xffc30f=_0x167168;return _0x4b431f[_0xffc30f(0x1a7)]();}),console[_0x167168(0x1c7)](_0x167168(0x1ee),_0x12aefd),_0x12aefd;}async function getWhiteListedItemSpecifics(){var _0x481140=a0_0x8f0572,_0x2acb58=[],_0x6fce4c=_0x481140(0x1ca),_0x346baa=await fetchPrompt(_0x6fce4c);return _0x2acb58=_0x346baa[_0x481140(0x18d)](',')['map'](function(_0x305a01){var _0x30dc3e=_0x481140;return _0x305a01[_0x30dc3e(0x1a7)]();}),console[_0x481140(0x1c7)](_0x481140(0x214),_0x2acb58),_0x2acb58;}async function getFilteredItemSpecificFields(_0x3c5e36){var _0x9cd6e8=a0_0x8f0572;console[_0x9cd6e8(0x1c7)]('getFilteredItemSpecificFields\x20itemSpecificFields',_0x3c5e36);var _0x25aac7=await getFilteredItemSpecificWords(),_0x635330=await getWhiteListedItemSpecifics(),_0xc94a5e=[];for(var _0x313ce9=0x0;_0x313ce9<_0x3c5e36[_0x9cd6e8(0x1c9)];_0x313ce9++){var _0x3ed0c2=_0x3c5e36[_0x313ce9];console['log'](_0x9cd6e8(0x189),_0x3ed0c2);var _0x27c5ed=_0x3ed0c2[_0x9cd6e8(0x1c3)],_0x529215=![];for(var _0x9dae68=0x0;_0x9dae68<_0x635330['length'];_0x9dae68++){var _0x494af4=_0x635330[_0x9dae68];console['log'](_0x9cd6e8(0x1c8),_0x27c5ed);if(_0x27c5ed[_0x9cd6e8(0x16d)]()[_0x9cd6e8(0x201)](_0x494af4['toLowerCase']())){_0xc94a5e[_0x9cd6e8(0x19f)](_0x3ed0c2);break;}}for(var _0x9dae68=0x0;_0x9dae68<_0x25aac7[_0x9cd6e8(0x1c9)];_0x9dae68++){var _0x177f96=_0x25aac7[_0x9dae68];if(_0x27c5ed['toLowerCase']()[_0x9cd6e8(0x201)](_0x177f96[_0x9cd6e8(0x16d)]())){_0x529215=!![];break;}}!_0x529215&&_0xc94a5e[_0x9cd6e8(0x19f)](_0x3ed0c2);}return _0xc94a5e;}async function fillProductInformationTemplate(_0x312d3c){var _0x19d607=a0_0x8f0572,_0x42c0c9=_0x19d607(0x152),_0xb702c6=await fetchPrompt(_0x42c0c9),_0x48d99c='';for(var _0xbf65a7=0x0;_0xbf65a7<_0x312d3c['length'];_0xbf65a7++){var _0x5daf19=_0x312d3c[_0xbf65a7],_0x268fd6=_0x5daf19[_0x19d607(0x1c3)],_0x239e52='';if(_0x5daf19[_0x19d607(0x18c)]!=null)for(var _0x3e7bc3=0x0;_0x3e7bc3<_0x5daf19['recommendedValues']['length'];_0x3e7bc3++){var _0x39f016=_0x5daf19[_0x19d607(0x18c)][_0x3e7bc3];_0x3e7bc3==0x0?_0x239e52+=_0x39f016:_0x239e52+=',\x20'+_0x39f016;}var _0x168c7d=_0x5daf19[_0x19d607(0x1a3)],_0x26b431='';_0x26b431=_0xb702c6['replace'](_0x19d607(0x218),_0x268fd6),_0x26b431=_0x26b431[_0x19d607(0x1eb)]('{{recommendedValues}}',_0x239e52),_0x168c7d==_0x19d607(0x170)?_0x26b431=_0x26b431[_0x19d607(0x1eb)](_0x19d607(0x1c5),_0x19d607(0x182)):_0x26b431=_0x26b431[_0x19d607(0x1eb)](_0x19d607(0x1c5),'No'),_0x48d99c+=_0x26b431+'\x0a\x0a';}return _0x48d99c;}function combineProductData(_0xe6ebeb){var _0x282fa7=a0_0x8f0572,_0x4702ff=_0xe6ebeb[_0x282fa7(0x17e)][_0x282fa7(0x168)]('\x0a'),_0x403db0=_0xe6ebeb[_0x282fa7(0x1b2)],_0x4f3f16=_0xe6ebeb[_0x282fa7(0x1fc)],_0x4ef046='';for(var _0x162d2e=0x0;_0x162d2e<_0xe6ebeb[_0x282fa7(0x165)][_0x282fa7(0x1c9)];_0x162d2e++){var _0x282c10=_0xe6ebeb['filteredItemSpecifics'][_0x162d2e],_0x57367e=_0x282c10[_0x282fa7(0x1c3)],_0x35d35e=_0x282c10[_0x282fa7(0x181)];_0x4ef046+=_0x57367e+':\x20'+_0x35d35e+'\x0a';}var _0x414fcd=_0x4f3f16+'\x0a\x0a'+_0x4702ff+'\x0a\x0a'+_0x403db0+'\x0a\x0a'+_0x4ef046;return _0x414fcd;}async function getItemSpecificValueFromAi(_0x51d12d,_0x8e0692){var _0x570e62=a0_0x8f0572,_0x5322bc=getItemSpecificFieldProperties(_0x51d12d),_0xecb3b1=_0x8e0692[_0x570e62(0x17e)][_0x570e62(0x168)]('\x0a'),_0x3cf704=_0x8e0692['descriptionText'],_0x56e1ec=_0x8e0692[_0x570e62(0x1fc)],_0x1aa4e0='';for(var _0x293655=0x0;_0x293655<_0x8e0692[_0x570e62(0x165)]['length'];_0x293655++){var _0x111810=_0x8e0692['filteredItemSpecifics'][_0x293655],_0x3717f3=_0x111810[_0x570e62(0x1c3)],_0x3b09f3=_0x111810['value'];_0x1aa4e0+=_0x3717f3+':\x20'+_0x3b09f3+'\x0a';}var _0xbf7949=_0x56e1ec+'\x0a\x0a'+_0xecb3b1+'\x0a\x0a'+_0x3cf704+'\x0a\x0a'+_0x1aa4e0,_0x2ce64c=_0x5322bc[_0x570e62(0x18c)],_0xe0ad80='';if(_0x2ce64c!=null)for(var _0x293655=0x0;_0x293655<_0x2ce64c[_0x570e62(0x1c9)];_0x293655++){var _0x3a7d34=_0x2ce64c[_0x293655];_0xe0ad80+=_0x3a7d34+',\x20';}console['log'](_0x570e62(0x169),_0xe0ad80);if(_0xe0ad80!=''){var _0x136ab8=_0x570e62(0x213),_0x422edd=await fetchPrompt(_0x136ab8);_0x422edd=_0x422edd[_0x570e62(0x1eb)](_0x570e62(0x1ec),_0xbf7949),_0x422edd=_0x422edd[_0x570e62(0x1eb)](_0x570e62(0x1d1),_0x51d12d),_0x422edd=_0x422edd[_0x570e62(0x1eb)](_0x570e62(0x1d7),_0xe0ad80);}if(_0xe0ad80==='')var _0x136ab8=_0x570e62(0x185);}function clickMoreButtonToShowAllItemSpecifics(){}function filterItemSpecifics(){}function capitalizeFirstLetterOfEachWord(_0x3f5e38){var _0x30dbec=a0_0x8f0572,_0x44e938=_0x3f5e38[_0x30dbec(0x18d)]('\x20'),_0x5ac9b7=[];for(var _0x28deb0=0x0;_0x28deb0<_0x44e938[_0x30dbec(0x1c9)];_0x28deb0++){var _0x30fe73=_0x44e938[_0x28deb0],_0x1fe4bd=_0x30fe73[_0x30dbec(0x15c)](0x0)[_0x30dbec(0x162)]()+_0x30fe73[_0x30dbec(0x159)](0x1);_0x5ac9b7[_0x30dbec(0x19f)](_0x1fe4bd);}var _0x122fab=_0x5ac9b7[_0x30dbec(0x168)]('\x20');return _0x122fab;}function capitalizeFirstLetterOfEachWordInItemSpecifics(_0x1a0eeb){var _0x18ea26=a0_0x8f0572,_0x34a172=[];for(var _0x27c4b3=0x0;_0x27c4b3<_0x1a0eeb[_0x18ea26(0x1c9)];_0x27c4b3++){var _0x1141a9=_0x1a0eeb[_0x27c4b3],_0x5250e0=capitalizeFirstLetterOfEachWord(_0x1141a9[_0x18ea26(0x1c3)]),_0x570970=capitalizeFirstLetterOfEachWord(_0x1141a9[_0x18ea26(0x181)]),_0x5a7c74={'label':_0x5250e0,'value':_0x570970};_0x34a172[_0x18ea26(0x19f)](_0x5a7c74);}return _0x34a172;}function getLabel(_0x3beeb0){var _0xd2b1eb=a0_0x8f0572,_0xf0ef6d=null,_0x255248=_0x3beeb0[_0xd2b1eb(0x1f0)]('[id*=\x27item-specific-dropdown-label\x27]');if(_0x255248)return _0xf0ef6d=_0x255248[_0xd2b1eb(0x1be)],_0xf0ef6d;try{_0xf0ef6d=_0x3beeb0[_0xd2b1eb(0x1f0)]('legend')[_0xd2b1eb(0x1be)];}catch(_0x16e17d){}return!_0xf0ef6d&&(_0xf0ef6d=_0x3beeb0[_0xd2b1eb(0x1f0)](_0xd2b1eb(0x1c3)),_0xf0ef6d&&(_0xf0ef6d=_0xf0ef6d[_0xd2b1eb(0x1be)])),_0xf0ef6d;}function getLabelDefinition(_0x25e394){var _0xd1ade1=a0_0x8f0572,_0x42d48f=_0xd1ade1(0x1bc),_0x2ab831=_0x25e394[_0xd1ade1(0x1f0)]('.tooltip__content');return _0x2ab831&&(_0x42d48f=_0x2ab831[_0xd1ade1(0x1be)],_0x42d48f===''&&(_0x42d48f=_0xd1ade1(0x1bc))),_0x42d48f;}function getRecommendedValues(_0x501fa8,_0x19ee9b){var _0x52ab92=a0_0x8f0572,_0x5014d9=[];if(_0x19ee9b=='multiSelect')return getMultiSelectSearchElementValues(_0x501fa8);if(_0x19ee9b==_0x52ab92(0x1b3))return getSearchElementValues(_0x501fa8);if(_0x19ee9b==_0x52ab92(0x219)){var _0xa8aae6=getButtonOptionsForSingleSelectWithLimitedOptions(_0x501fa8);for(var _0x14e8f1=0x0;_0x14e8f1<_0xa8aae6[_0x52ab92(0x1c9)];_0x14e8f1++){var _0x4b8dab=_0xa8aae6[_0x14e8f1],_0x34967f=getTextContentExcludeClipped(_0x4b8dab);_0x5014d9[_0x52ab92(0x19f)](_0x34967f);}return _0x5014d9=[...new Set(_0x5014d9)],_0x5014d9;}if(_0x19ee9b==_0x52ab92(0x1fe))return'';if(_0x19ee9b=='singleSelectWithNoSearch')return getSearchElementValues(_0x501fa8);if(_0x19ee9b=='multiSelectWithNoSearch')return getMultiSelectSearchElementValues(_0x501fa8);}function getMultiSelectSearchElementValues(_0x16cfb4){var _0x1387d0=a0_0x8f0572,_0x2f99b0=[],_0xc59f5=_0x16cfb4[_0x1387d0(0x1ba)](_0x1387d0(0x198));for(var _0x2e726b=0x0;_0x2e726b<_0xc59f5[_0x1387d0(0x1c9)];_0x2e726b++){var _0x33f396=_0xc59f5[_0x2e726b],_0x260e06=_0x33f396[_0x1387d0(0x1be)];_0x260e06=getTextContentExcludeClipped(_0x33f396),_0x2f99b0[_0x1387d0(0x19f)](_0x260e06);}var _0xc59f5=_0x16cfb4[_0x1387d0(0x1ba)](_0x1387d0(0x1c3));for(var _0x2e726b=0x0;_0x2e726b<_0xc59f5[_0x1387d0(0x1c9)];_0x2e726b++){var _0x33f396=_0xc59f5[_0x2e726b],_0x260e06=_0x33f396[_0x1387d0(0x1be)];_0x260e06=getTextContentExcludeClipped(_0x33f396),_0x2f99b0['push'](_0x260e06);}return _0x2f99b0=[...new Set(_0x2f99b0)],_0x2f99b0;}function getSearchElementValues(_0x2d77ba){var _0x26b522=a0_0x8f0572,_0x36f1b7=[],_0x28bcd9=_0x2d77ba['querySelectorAll'](_0x26b522(0x1e1));for(var _0x1e4fa9=0x0;_0x1e4fa9<_0x28bcd9['length'];_0x1e4fa9++){var _0x579cd5=_0x28bcd9[_0x1e4fa9],_0x5bb7cf=getTextContentExcludeClipped(_0x579cd5);_0x36f1b7['push'](_0x5bb7cf);}return _0x36f1b7=[...new Set(_0x36f1b7)],_0x36f1b7;}function getButtonOptionsForSingleSelectWithLimitedOptions(_0x2b3d6a){var _0x44c1a4=a0_0x8f0572,_0x549013=_0x2b3d6a[_0x44c1a4(0x1ba)](_0x44c1a4(0x21e));return _0x549013;}function getCurrentValue(_0x4b185c,_0x38d665){var _0x1d0e8a=a0_0x8f0572,_0x215925;if(_0x38d665==_0x1d0e8a(0x170))return getMultiSelectCurrentValue(_0x4b185c);if(_0x38d665==_0x1d0e8a(0x1b3))return getSingleSelectCurrentValue(_0x4b185c);if(_0x38d665==_0x1d0e8a(0x219))return null;if(_0x38d665==_0x1d0e8a(0x1fe))return _0x215925=_0x4b185c[_0x1d0e8a(0x1f0)](_0x1d0e8a(0x157))[_0x1d0e8a(0x181)],_0x215925===''&&(_0x215925=null),_0x215925;if(_0x38d665==_0x1d0e8a(0x220))return getSingleSelectCurrentValue(_0x4b185c);if(_0x38d665==_0x1d0e8a(0x1d0))return getMultiSelectCurrentValue(_0x4b185c);}function getMultiSelectCurrentValue(_0x7f78e6){var _0x6b7283=a0_0x8f0572,_0x9f67ad=[],_0x41b17e=_0x7f78e6[_0x6b7283(0x1ba)]('.filter-menu__item[aria-checked=\x22true\x22]');for(var _0x3806ce=0x0;_0x3806ce<_0x41b17e['length'];_0x3806ce++){var _0x3b04f3=_0x41b17e[_0x3806ce],_0x2b1a83=_0x3b04f3[_0x6b7283(0x1f0)](_0x6b7283(0x198)),_0x5af25e=getTextContentExcludeClipped(_0x2b1a83);_0x9f67ad[_0x6b7283(0x19f)](_0x5af25e);}var _0x346cd3=_0x7f78e6[_0x6b7283(0x1ba)]('.se-checkbox-group__option');for(var _0x3806ce=0x0;_0x3806ce<_0x346cd3[_0x6b7283(0x1c9)];_0x3806ce++){var _0x1327f4=_0x346cd3[_0x3806ce];if(_0x1327f4['querySelector'](_0x6b7283(0x1c0))){var _0x2b1a83=_0x1327f4[_0x6b7283(0x1f0)](_0x6b7283(0x1c3)),_0x5af25e=getTextContentExcludeClipped(_0x2b1a83);_0x9f67ad['push'](_0x5af25e);}}return _0x9f67ad=_0x9f67ad[_0x6b7283(0x21a)](function(_0x332b20,_0x2659f3){return _0x9f67ad['indexOf'](_0x332b20)==_0x2659f3;}),_0x9f67ad['length']===0x0&&(_0x9f67ad=null),_0x9f67ad;}function getSingleSelectCurrentValue(_0x2f66ce){var _0x8e771a=a0_0x8f0572,_0x41e507=null,_0x3dc4d;return _0x3dc4d=_0x2f66ce[_0x8e771a(0x1f0)]('[class$=\x27n__cell\x27]'),_0x3dc4d&&(_0x41e507=getTextContentExcludeClipped(_0x3dc4d)),_0x41e507==='Show\x20all'&&(_0x41e507=null),_0x41e507===''&&(_0x41e507=null),_0x41e507;}function dispatchKeyBoardAndChangeEvent(_0xaf6ae8){var _0x3fb301=a0_0x8f0572,_0x4208ce=new Event(_0x3fb301(0x157),{'bubbles':!![]});_0xaf6ae8[_0x3fb301(0x161)](_0x4208ce);var _0x422139=new KeyboardEvent(_0x3fb301(0x1e7),{'bubbles':!![],'key':_0x3fb301(0x1cf)});_0xaf6ae8[_0x3fb301(0x161)](_0x422139);var _0x21c7cd=new Event(_0x3fb301(0x1b0),{'bubbles':!![]});_0xaf6ae8[_0x3fb301(0x161)](_0x21c7cd);}function testInputInput(){var _0x44cef7=a0_0x8f0572,_0x63719f=document[_0x44cef7(0x1ba)](_0x44cef7(0x1d9)),_0x233833=_0x63719f[0x0],_0xc045cf=_0x233833[_0x44cef7(0x1f0)](_0x44cef7(0x1ad));_0xc045cf[_0x44cef7(0x181)]=_0x44cef7(0x153);var _0x239053=new Event('input',{'bubbles':!![]});_0xc045cf[_0x44cef7(0x161)](_0x239053);var _0x192240=new KeyboardEvent(_0x44cef7(0x1e7),{'bubbles':!![],'key':_0x44cef7(0x1cf)});_0xc045cf['dispatchEvent'](_0x192240);var _0x2429d4=new Event(_0x44cef7(0x1b0),{'bubbles':!![]});_0xc045cf[_0x44cef7(0x161)](_0x2429d4);}function highlightElement(_0x442c40,_0x526bb5){var _0x293631=a0_0x8f0572,_0x5be06d=_0x442c40[_0x293631(0x1ba)]('.se-expand-button__button'),_0x490127=_0x442c40[_0x293631(0x1ba)](_0x293631(0x171)),_0x1cd8c=_0x442c40[_0x293631(0x1ba)](_0x293631(0x157));if(_0x5be06d[_0x293631(0x1c9)]>0x0)_0x442c40=_0x5be06d[0x0];else{if(_0x490127[_0x293631(0x1c9)]>0x0)_0x442c40=_0x490127[0x0];else _0x1cd8c[_0x293631(0x1c9)]>0x0&&(_0x442c40=_0x1cd8c[0x0]);}if(_0x526bb5=='success'){_0x442c40[_0x293631(0x217)][_0x293631(0x1b8)]=_0x293631(0x16c);return;}if(_0x526bb5=='error'){_0x442c40[_0x293631(0x217)][_0x293631(0x1b8)]=_0x293631(0x1f6);return;}if(_0x526bb5==_0x293631(0x1f8)){_0x442c40[_0x293631(0x217)][_0x293631(0x1b8)]=_0x293631(0x21b);return;}if(_0x526bb5==_0x293631(0x1e9)){_0x442c40[_0x293631(0x217)][_0x293631(0x1b8)]='3px\x20solid\x20#FFA500';return;}}function checkIfItemSpecificIsAdded(_0x4d0bea,_0x9d4909){var _0x532531=a0_0x8f0572;console[_0x532531(0x1c7)](_0x532531(0x207),_0x4d0bea,_0x9d4909);var _0x2b80a2=getItemSpecificFieldProperties(_0x4d0bea);console[_0x532531(0x1c7)](_0x532531(0x225),_0x2b80a2);var _0x50d136=_0x2b80a2[_0x532531(0x163)];console[_0x532531(0x1c7)](_0x532531(0x163),_0x50d136);if(_0x50d136==null||_0x50d136==undefined)return console[_0x532531(0x1c7)](_0x532531(0x222)),![];if(Array[_0x532531(0x1f4)](_0x50d136)){console[_0x532531(0x1c7)](_0x532531(0x1de));for(var _0x2cc355=0x0;_0x2cc355<_0x50d136[_0x532531(0x1c9)];_0x2cc355++){console['log'](_0x532531(0x21f),_0x50d136[_0x2cc355]);if(_0x50d136[_0x2cc355][_0x532531(0x16d)]()[_0x532531(0x1a7)]()==_0x9d4909[_0x532531(0x16d)]()[_0x532531(0x1a7)]())return!![];}}else{if(_0x50d136['toLowerCase']()['trim']()==_0x9d4909[_0x532531(0x16d)]()[_0x532531(0x1a7)]())return!![];}return![];}function addButtonToItemSpecificFieldsToGetGenerateAnswer(_0x211e09){var _0x365b45=a0_0x8f0572,_0x2fb234=getItemSpecificFields(_0x365b45(0x17c));for(var _0x2e99b0=0x0;_0x2e99b0<_0x2fb234[_0x365b45(0x1c9)];_0x2e99b0++){var _0x139693=_0x2fb234[_0x2e99b0],_0x21d305=document[_0x365b45(0x1dc)](_0x365b45(0x1f1));_0x21d305[_0x365b45(0x1b7)]['add'](_0x365b45(0x158)),_0x21d305[_0x365b45(0x1be)]=_0x365b45(0x1b6),_0x21d305['addEventListener'](_0x365b45(0x15b),async function(_0x3bc935){var _0x1ba8a4=_0x365b45;_0x3bc935[_0x1ba8a4(0x1bd)]();var _0x152158=getLabel(_0x3bc935[_0x1ba8a4(0x1c6)][_0x1ba8a4(0x199)]);await generateAnswer(_0x152158,_0x211e09);}),_0x139693[_0x365b45(0x19a)](_0x21d305);}}async function generateAnswer(_0x5532d7,_0x2661ff){var _0x148eb4=a0_0x8f0572;console[_0x148eb4(0x1c7)]('generateAnswer:',_0x5532d7);var _0x5caa29=getItemSpecificFieldProperties(_0x5532d7);console['log']('itemSpecificFieldProperties',_0x5caa29);var _0xea9dc3=combineProductDataAttributes(_0x2661ff),_0x1d209a=_0x5caa29[_0x148eb4(0x18c)],_0x1e00cd=_0x5caa29[_0x148eb4(0x1c3)],_0x19e11c=_0x5caa29[_0x148eb4(0x1a9)],_0x5048bd=_0x5caa29['type'],_0x206a31='https://ebaysniperitemspecificsapi.ngrok.io/api/ItemSpecifics/GetItemSpecificValues';if(_0x5caa29['type']==_0x148eb4(0x1fe)){startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console[_0x148eb4(0x1c7)](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd);if(_0x73a405[_0x148eb4(0x1c9)]>0x0){var _0x420595=_0x73a405[0x0];await fillInputItemSpecific(_0x1e00cd,_0x420595);}}if(_0x5caa29[_0x148eb4(0x1a3)]==_0x148eb4(0x1b3)){startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console[_0x148eb4(0x1c7)](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd);if(_0x73a405[_0x148eb4(0x1c9)]>0x0){var _0x420595=_0x73a405[0x0];await fillSingleSelectItemSpecific(_0x1e00cd,_0x420595);}}if(_0x5caa29[_0x148eb4(0x1a3)]==_0x148eb4(0x219)){startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console['log'](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd);if(_0x73a405['length']>0x0){var _0x420595=_0x73a405[0x0];await fillSingleSelectWithLimitedOptionsItemSpecific(_0x1e00cd,_0x420595);}}if(_0x5caa29[_0x148eb4(0x1a3)]=='singleSelectWithNoSearch'){startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console['log'](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd);if(_0x73a405['length']>0x0){var _0x420595=_0x73a405[0x0];await fillSingleSelectItemSpecific(_0x1e00cd,_0x420595);}}if(_0x5caa29[_0x148eb4(0x1a3)]==_0x148eb4(0x170)){console['log']('multiSelect'),startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console['log'](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd),_0x73a405[_0x148eb4(0x1c9)]>0x0&&await fillMultiSelectItemSpecific(_0x1e00cd,_0x73a405);}if(_0x5caa29[_0x148eb4(0x1a3)]=='multiSelectWithNoSearch'){console[_0x148eb4(0x1c7)](_0x148eb4(0x1d0)),startVisualInProgressForItemSpecificField(_0x1e00cd);var _0x73a405=await postToServer(_0x206a31,{'productDescription':_0xea9dc3,'recommendedValues':_0x1d209a,'itemSpecific':_0x1e00cd,'labelDefinition':_0x19e11c,'itemSpecificFieldType':_0x5048bd,'forceResponse':!![]});console[_0x148eb4(0x1c7)](_0x148eb4(0x1cb),_0x73a405),stopVisualInProgressForItemSpecificField(_0x1e00cd),_0x73a405[_0x148eb4(0x1c9)]>0x0&&await fillMultiSelectItemSpecific(_0x1e00cd,_0x73a405);}}function startVisualInProgressForItemSpecificField(_0x2e65ae){var _0x49f052=a0_0x8f0572,_0x47cc58=getItemSpecificFields(_0x49f052(0x17c));for(var _0x1b9ecb=0x0;_0x1b9ecb<_0x47cc58['length'];_0x1b9ecb++){var _0x5770a9=_0x47cc58[_0x1b9ecb],_0x3e57d6=getLabel(_0x5770a9);_0x3e57d6==_0x2e65ae&&(highlightElement(_0x5770a9,_0x49f052(0x1f8)),addLoaderToElement(_0x5770a9));}}function stopVisualInProgressForItemSpecificField(_0x4d297b){var _0x2c22d1=a0_0x8f0572,_0x11ff3f=getItemSpecificFields(_0x2c22d1(0x17c));for(var _0x9970d3=0x0;_0x9970d3<_0x11ff3f[_0x2c22d1(0x1c9)];_0x9970d3++){var _0x313283=_0x11ff3f[_0x9970d3],_0xea451f=getLabel(_0x313283);_0xea451f==_0x4d297b&&(highlightElement(_0x313283,_0x2c22d1(0x1e9)),removeLoaderFromElement(_0x313283));}}function addLoaderToElement(_0xbabea7){var _0x2a3ecb=a0_0x8f0572,_0x4f5018=document[_0x2a3ecb(0x1dc)](_0x2a3ecb(0x155));_0x4f5018[_0x2a3ecb(0x1b7)]['add'](_0x2a3ecb(0x202)),_0x4f5018[_0x2a3ecb(0x217)][_0x2a3ecb(0x1b8)]='6px\x20solid\x20#f3f3f3',_0x4f5018[_0x2a3ecb(0x217)][_0x2a3ecb(0x1ce)]=_0x2a3ecb(0x215),_0x4f5018[_0x2a3ecb(0x217)]['borderTop']='6px\x20solid\x20#3498db',_0x4f5018[_0x2a3ecb(0x217)][_0x2a3ecb(0x1f2)]=_0xbabea7[_0x2a3ecb(0x172)]/0x23+'px',_0x4f5018[_0x2a3ecb(0x217)][_0x2a3ecb(0x1b5)]=_0xbabea7[_0x2a3ecb(0x172)]/0x23+'px',_0x4f5018['style']['webkitAnimation']=_0x2a3ecb(0x167),_0x4f5018[_0x2a3ecb(0x217)][_0x2a3ecb(0x1fb)]='spin\x202s\x20linear\x20infinite',_0xbabea7[_0x2a3ecb(0x19a)](_0x4f5018);}function removeLoaderFromElement(_0x490ec6){var _0x4861f8=a0_0x8f0572,_0x673b8c=_0x490ec6['querySelector'](_0x4861f8(0x21c));_0x673b8c&&_0x673b8c[_0x4861f8(0x15a)]['removeChild'](_0x673b8c);}async function removeAllItemSpecifics(){var _0x5eb6a5=a0_0x8f0572,_0x289941=getItemSpecificFieldsWithProperties(_0x5eb6a5(0x17c));console[_0x5eb6a5(0x1c7)](_0x5eb6a5(0x1e8),_0x289941);}async function removeRestrictedWordsFromItemSpecifics(){var _0x18303e=a0_0x8f0572,_0x262642=getItemSpecificFieldsWithProperties(_0x18303e(0x17c));console[_0x18303e(0x1c7)](_0x18303e(0x221),_0x262642);async function _0x1c00e3(_0x167df4,_0x191533){var _0x336b20=_0x18303e;for(let _0x4cba2b of _0x167df4){if(_0x4cba2b[_0x336b20(0x163)]){let _0xbd1792=![];if(Array[_0x336b20(0x1f4)](_0x4cba2b[_0x336b20(0x163)]))for(let _0x4ab09 of _0x4cba2b[_0x336b20(0x163)]){if(await detectRestrictedWords(_0x4ab09)){_0xbd1792=!![];break;}}else await detectRestrictedWords(_0x4cba2b[_0x336b20(0x163)])&&(_0xbd1792=!![]);_0xbd1792&&await _0x191533(_0x4cba2b[_0x336b20(0x1c3)]);}}}await _0x1c00e3(_0x262642[_0x18303e(0x1e6)],clearInputItemSpecific),await _0x1c00e3(_0x262642['singleSelectItemSpecificFields'],clearSingleSelectItemSpecific),await _0x1c00e3(_0x262642[_0x18303e(0x187)],clearMultiSelectItemSpecific),await _0x1c00e3(_0x262642['singleSelectWithLimitedOptionsItemSpecificFields'],clearSingleSelectItemSpecific),await _0x1c00e3(_0x262642[_0x18303e(0x203)],clearSingleSelectItemSpecific),await _0x1c00e3(_0x262642[_0x18303e(0x20b)],clearMultiSelectItemSpecific),document[_0x18303e(0x1fc)]=_0x18303e(0x208);}async function clearInputItemSpecific(_0x2f5c5b){var _0x2c642d=a0_0x8f0572;documentTitle=_0x2c642d(0x20f)+_0x2f5c5b;var _0x423100=getItemSpecificField(_0x2f5c5b);if(!_0x423100){console['log'](_0x2c642d(0x1b9),_0x2f5c5b);return;}scrollIntoView(_0x423100),highlightElement(_0x423100,_0x2c642d(0x1f8)),await waitSomeTime(0x64);var _0x5ef772=_0x423100[_0x2c642d(0x1f0)]('input');_0x5ef772&&(_0x5ef772['focus'](),_0x5ef772[_0x2c642d(0x181)]='',dispatchKeyBoardAndChangeEvent(_0x5ef772),await waitForNetworkResponseCountIncrease(),highlightElement(_0x423100,_0x2c642d(0x1fa))),await waitSomeTime(waitTime),await closeDialogs();}async function clearSingleSelectItemSpecific(_0x511fde){var _0x3787f5=a0_0x8f0572;documentTitle=_0x3787f5(0x20f)+_0x511fde;var _0x869cef=getItemSpecificField(_0x511fde);if(!_0x869cef){console['log'](_0x3787f5(0x224),_0x511fde);return;}scrollIntoView(_0x869cef),highlightElement(_0x869cef,'loading'),await waitSomeTime(0x64);var _0x129401=_0x869cef[_0x3787f5(0x1f0)](_0x3787f5(0x20e));_0x129401?(_0x129401[_0x3787f5(0x15b)](),await waitForNetworkResponseCountIncrease(),highlightElement(_0x869cef,_0x3787f5(0x1fa))):console[_0x3787f5(0x1c7)](_0x3787f5(0x211),_0x511fde),await waitSomeTime(waitTime),await closeDialogs();}async function clearMultiSelectItemSpecific(_0x40988e){var _0xb00455=a0_0x8f0572;documentTitle=_0xb00455(0x20f)+_0x40988e;var _0x141cab=getItemSpecificField(_0x40988e);if(!_0x141cab){console[_0xb00455(0x1c7)]('clearMultiSelectItemSpecific:\x20Field\x20not\x20found\x20for\x20label',_0x40988e);return;}scrollIntoView(_0x141cab),highlightElement(_0x141cab,'loading'),await waitSomeTime(0x64);var _0x351539=_0x141cab[_0xb00455(0x1f0)]('.se-filter-menu-button__clear.fake-link');_0x351539?(_0x351539[_0xb00455(0x15b)](),await waitForNetworkResponseCountIncrease(),highlightElement(_0x141cab,_0xb00455(0x1fa))):console[_0xb00455(0x1c7)]('Clear\x20button\x20not\x20found\x20for\x20label',_0x40988e),await waitSomeTime(waitTime),await closeDialogs();}