<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="/libraries/context_window/ContextMenu.css">
    <title>Bulk Poster</title>
</head>

<body>
    <header>
        <h1>Bulk Poster Settings</h1>
    </header>

    <section class="bulk-list">
        <h2>Bulk List Feature</h2>
        <p>Enter Amazon product links below, separated by new lines:</p>

        <div>
        <textarea id="amazon-links"></textarea>

        </div>
        <button id="list-btn" class="list-btn">Opti-List (1 credit)</button>
        <button id="chat-listing-button" class="list-btn" style="display: none;">Chat-List (1 credit)</button>
        <button id="seo-listing-button" class="list-btn" style="display: none;">Seo-List (1 credit)</button>
        <button id="standard-listing-button" class="list-btn">Standard-List (0.2)</button>
        <button id="clear-btn" class="clear">Clear Links</button>
        <button id="reset-btn" class="reset">Reset & Terminate</button>


        <div class="button-group">
            <button id="pause_button">Pause</button>
            <button id="resume_button">Resume</button>
        </div>

        <div class="position-container">
            <span id="position-label">Position:</span>
            <input id="position-input" type="number" min="0" value="0">/<span id="total-links-label">0</span>
            <button id="status-report">Status Report</button>
        </div>

        <div class="form-group">
            <label for="thread-count">Thread Count:</label>
            <select id="thread-count" name="thread-count">
                <option value="1" selected>1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
                <option value="6">6</option>
                <option value="7">7</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13">13</option>
                <option value="14">14</option>
                <option value="15">15</option>
                <option value="16">16</option>
                <option value="17">17</option>
                <option value="18">18</option>
                <option value="19">19</option>
                <option value="20">20</option>
                <option value="21">21</option>
                <option value="22">22</option>
                <option value="23">23</option>
                <option value="24">24</option>
                <option value="25">25</option>
                <option value="26">26</option>
                <option value="27">27</option>
                <option value="28">28</option>
                <option value="29">29</option>
                <option value="30">30</option>

            </select>
        </div>

        <div class="form-group">
            <label for="min-price">Minimum Price:</label>
            <input type="number" id="min-price" name="min-price" min="0" value="0">
        </div>

        <div class="form-group">
            <label for="max-price">Maximum Price:</label>
            <input type="number" id="max-price" name="max-price" min="0" value="100">
        </div>

        <div class="form-group">
            <label for="fbaOnly">FBA Only:</label>
            <input type="checkbox" id="fbaOnly" name="fba-only" value="fba-only" checked>
        </div>

        <div class="form-group">
            <label for="closeErrors">Close Errored Listings:</label>
            <input type="checkbox" id="closeErrors" name="close-errored" value="close-errored" checked>
        </div>

        <div class="form-group">
            <label for="shouldOnlyListChineseSellers">List Chinese Sellers Only (EU ONLY):</label>
            <input type="checkbox" id="shouldOnlyListChineseSellers" name="chinese-sellers" value="chinese-sellers">
        </div>

        <div class="form-group">
            <label for="shouldGetGspr">Add GPSR:</label>
            <input type="checkbox" id="shouldGetGspr" name="gspr" value="gspr">
        </div>

        <!-- //optimize for slow computers  -->
        <div class="form-group">
            <label for="shouldOptimizeForSlowComputers">Optimize for slow computers:</label>
            <input type="checkbox" id="shouldOptimizeForSlowComputers" name="optimize-for-slow-computers" value="optimize-for-slow-computers">
        </div>

        
        <div class="progress-container">
            <p>Progress <span class="progress-percentage"></span></p>
            <div id="progress-bar">
                <span class="progress-percentage"></span>
            </div>
        </div>

        <h3>Add more links to running list</h3>
        <div class="add-links-container">
            <textarea id="add-links"></textarea>
            <div class="button-group-column">
                <button id="import-links-btn">Import</button>
                <button id="add-links-btn">Add</button>
            </div>
         
        </div>


    </section>




    <script src="/libraries/context_window/ContextMenu.js"></script>
    <script src="/libraries/firebase_ecomsniper_functions.js"></script>
    <script src="bulk_post_settings.js"></script>

</body>

</html>