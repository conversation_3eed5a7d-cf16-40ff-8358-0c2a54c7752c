const a0_0x256e30=a0_0x23e3;(function(_0x89f684,_0x1994cc){const _0xbbba89=a0_0x23e3,_0x1eb3c2=_0x89f684();while(!![]){try{const _0x516273=-parseInt(_0xbbba89(0x164))/0x1+parseInt(_0xbbba89(0x175))/0x2*(-parseInt(_0xbbba89(0x123))/0x3)+-parseInt(_0xbbba89(0x151))/0x4+parseInt(_0xbbba89(0x11f))/0x5+-parseInt(_0xbbba89(0x128))/0x6+parseInt(_0xbbba89(0x15e))/0x7*(parseInt(_0xbbba89(0x16b))/0x8)+parseInt(_0xbbba89(0x161))/0x9*(parseInt(_0xbbba89(0x101))/0xa);if(_0x516273===_0x1994cc)break;else _0x1eb3c2['push'](_0x1eb3c2['shift']());}catch(_0x45ceeb){_0x1eb3c2['push'](_0x1eb3c2['shift']());}}}(a0_0x14a7,0xe9e5e));let showOnlyUnprocessed=![];const showUnprocessedOnlyCheckbox=document['getElementById'](a0_0x256e30(0x17e));showUnprocessedOnlyCheckbox[a0_0x256e30(0xf7)](a0_0x256e30(0x172),_0xcb547c=>{const _0x1efa59=a0_0x256e30;showOnlyUnprocessed=_0xcb547c['target'][_0x1efa59(0x119)],renderTable();});const actionsConfig={'gspr':{'label':a0_0x256e30(0x13e),'dataColumns':[a0_0x256e30(0x189),a0_0x256e30(0x143),a0_0x256e30(0x131),a0_0x256e30(0x10b),'Manufacturer\x20Country',a0_0x256e30(0x115),'Manufacturer\x20StateOrProvince','Manufacturer\x20Phone',a0_0x256e30(0x18a),a0_0x256e30(0x177),a0_0x256e30(0x14e),a0_0x256e30(0xfa),a0_0x256e30(0x137),a0_0x256e30(0x149),a0_0x256e30(0xfc),'Responsible\x20Person\x201\x20PostalCode','Responsible\x20Person\x201\x20StateOrProvince',a0_0x256e30(0x156),'Responsible\x20Person\x201\x20Email',a0_0x256e30(0x12a),a0_0x256e30(0x12e)],'scannedColumn':a0_0x256e30(0x138)},'chineseSeller':{'label':a0_0x256e30(0x12c),'dataColumns':[a0_0x256e30(0x11a)],'scannedColumn':'scannedChinese'}};let debugMode=![],originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,concurrency=0x1,isProcessing=![],currentHeaders=[];const showRowNumber=!![];let currentLineIndex=0x0;const csvUpload=document[a0_0x256e30(0x169)]('csvUpload'),headerSelectionContainer=document['getElementById'](a0_0x256e30(0x117)),headerCheckboxes=document[a0_0x256e30(0x169)](a0_0x256e30(0x18d)),confirmHeadersBtn=document['getElementById'](a0_0x256e30(0x14a)),mainSection=document[a0_0x256e30(0x169)](a0_0x256e30(0x17b)),dataTable=document[a0_0x256e30(0x169)](a0_0x256e30(0x14f)),downloadCsvBtn=document[a0_0x256e30(0x169)](a0_0x256e30(0x126)),concurrencyInput=document[a0_0x256e30(0x169)]('concurrencyInput'),processedCountEl=document[a0_0x256e30(0x169)](a0_0x256e30(0x133)),totalCountEl=document[a0_0x256e30(0x169)](a0_0x256e30(0x15f)),statusMessageEl=document[a0_0x256e30(0x169)](a0_0x256e30(0xf4)),restoreFromStorageBtn=document[a0_0x256e30(0x169)](a0_0x256e30(0xf8)),clearDataBtn=document[a0_0x256e30(0x169)]('clearDataBtn'),runActionsBtn=document[a0_0x256e30(0x169)]('runActionsBtn'),actionCheckboxes=document[a0_0x256e30(0x169)](a0_0x256e30(0x16f)),startLineInput=document[a0_0x256e30(0x169)](a0_0x256e30(0x171));document[a0_0x256e30(0xf7)](a0_0x256e30(0xfe),onDOMContentLoaded),csvUpload[a0_0x256e30(0xf7)](a0_0x256e30(0x172),handleFileUpload),confirmHeadersBtn[a0_0x256e30(0xf7)](a0_0x256e30(0x12f),handleConfirmHeaders),downloadCsvBtn['addEventListener'](a0_0x256e30(0x12f),handleDownloadCSV),restoreFromStorageBtn[a0_0x256e30(0xf7)](a0_0x256e30(0x12f),handleRestoreFromStorage),clearDataBtn[a0_0x256e30(0xf7)](a0_0x256e30(0x12f),handleClearData),runActionsBtn['addEventListener'](a0_0x256e30(0x12f),handleRunActions),concurrencyInput[a0_0x256e30(0xf7)](a0_0x256e30(0x172),_0xd18077=>{const _0x121d5e=a0_0x256e30;concurrency=parseInt(_0xd18077[_0x121d5e(0x160)]['value'],0xa)||0x1;});async function onDOMContentLoaded(){const _0xdb35d4=a0_0x256e30;await restoreState();originalData['length']>0x0&&selectedHeaders[_0xdb35d4(0x173)]>0x0&&(initActionsUI(),renderTable(),mainSection[_0xdb35d4(0xed)][_0xdb35d4(0x136)]=_0xdb35d4(0x112),startLineInput&&(startLineInput['value']=currentLineIndex));const _0x3a4fe0=document['getElementById'](_0xdb35d4(0x181));_0x3a4fe0['addEventListener'](_0xdb35d4(0x172),_0x461cd7=>{const _0x2de45b=_0xdb35d4;debugMode=_0x461cd7['target'][_0x2de45b(0x119)],console['log'](_0x2de45b(0xf1),debugMode?'ON':'OFF');}),createFilterForColumn(_0xdb35d4(0x138));}async function handleFileUpload(_0x5a8a32){const _0x1c70fb=a0_0x256e30,_0x3eefb9=_0x5a8a32['target']['files'][0x0];if(!_0x3eefb9)return;Papa[_0x1c70fb(0x163)](_0x3eefb9,{'header':!![],'skipEmptyLines':!![],'dynamicTyping':![],'complete':async _0x1404f5=>{const _0x3633bd=_0x1c70fb,_0x30ef55=_0x1404f5['data'];if(_0x30ef55[_0x3633bd(0x173)]===0x0){alert(_0x3633bd(0x148));return;}originalData=_0x30ef55,originalData[_0x3633bd(0x10f)](_0x5e48a1=>{const _0x44b2cd=_0x3633bd;for(const _0x40cae9 in _0x5e48a1){if(_0x40cae9[_0x44b2cd(0x114)]('scanned')){const _0x440c9d=(_0x5e48a1[_0x40cae9]||'')[_0x44b2cd(0x180)]()[_0x44b2cd(0x12d)]();_0x5e48a1[_0x40cae9]=_0x440c9d===_0x44b2cd(0x15a);}}});const _0x1cb3f9=Object[_0x3633bd(0x154)](originalData[0x0]);appliedActions={};for(const [_0x469b33,_0xc74ddb]of Object[_0x3633bd(0x14b)](actionsConfig)){appliedActions[_0x469b33]=_0x1cb3f9['includes'](_0xc74ddb[_0x3633bd(0x17c)]);}const _0x49c0c4=Object[_0x3633bd(0x185)](actionsConfig)[_0x3633bd(0xff)](_0x2e8641=>[_0x2e8641['scannedColumn'],..._0x2e8641[_0x3633bd(0x139)]]),_0x578a75=_0x1cb3f9[_0x3633bd(0x134)](_0x3eb8c2=>!_0x49c0c4[_0x3633bd(0x106)](_0x3eb8c2));selectedHeaders=_0x578a75,selectedHeaders[_0x3633bd(0x173)]===0x0||mainSection[_0x3633bd(0xed)]['display']==='none'?showHeaderSelection():(mainSection['style'][_0x3633bd(0x136)]=_0x3633bd(0x112),initActionsUI(),renderTable(),await saveState());},'error':function(_0xccacaf){const _0x4779c4=_0x1c70fb;console[_0x4779c4(0x116)](_0x4779c4(0x14d),_0xccacaf),alert('Error\x20parsing\x20CSV.\x20Check\x20the\x20browser\x20console\x20for\x20details.');}});}function showHeaderSelection(){const _0x122b0c=a0_0x256e30;headerSelectionContainer[_0x122b0c(0xed)][_0x122b0c(0x136)]=_0x122b0c(0x112),headerCheckboxes[_0x122b0c(0x121)]='';const _0x3fe70f=Object[_0x122b0c(0x154)](originalData[0x0]);_0x3fe70f['forEach'](_0x2c1afc=>{const _0x108944=_0x122b0c,_0x15cdfb=document[_0x108944(0x100)](_0x108944(0x142));_0x15cdfb[_0x108944(0x121)]=_0x108944(0x102)+_0x2c1afc+_0x108944(0x16a)+_0x2c1afc+'</label>',headerCheckboxes[_0x108944(0x11b)](_0x15cdfb);});}async function handleConfirmHeaders(){const _0x241a2c=a0_0x256e30,_0x262a7c=headerCheckboxes[_0x241a2c(0x15d)](_0x241a2c(0x18b));selectedHeaders=Array[_0x241a2c(0x157)](_0x262a7c)[_0x241a2c(0x134)](_0x400c3d=>_0x400c3d[_0x241a2c(0x119)])[_0x241a2c(0xef)](_0x5deb0e=>_0x5deb0e[_0x241a2c(0x141)]);if(selectedHeaders[_0x241a2c(0x173)]===0x0){alert(_0x241a2c(0x110));return;}originalData=originalData[_0x241a2c(0xef)](_0x31c914=>{const _0x4d4b03=_0x241a2c,_0x5ca437={};for(const _0x267595 of selectedHeaders){_0x31c914[_0x4d4b03(0xf2)](_0x267595)&&(_0x5ca437[_0x267595]=_0x31c914[_0x267595]);}return _0x5ca437;}),appliedActions={},headerSelectionContainer[_0x241a2c(0xed)]['display']='none',mainSection[_0x241a2c(0xed)]['display']='block',initActionsUI(),renderTable(),await saveState();}function initActionsUI(){const _0x5d2d12=a0_0x256e30;actionCheckboxes['innerHTML']='';for(const [_0xbbede2,_0x4172d3]of Object[_0x5d2d12(0x14b)](actionsConfig)){const _0x4c71f5=appliedActions[_0xbbede2]?_0x5d2d12(0x119):'',_0xd4551e=document['createElement'](_0x5d2d12(0x142));_0xd4551e[_0x5d2d12(0x121)]=_0x5d2d12(0x17f)+_0xbbede2+'\x22\x20'+_0x4c71f5+'>\x20'+_0x4172d3[_0x5d2d12(0x17a)]+_0x5d2d12(0x16d),actionCheckboxes[_0x5d2d12(0x11b)](_0xd4551e);}}function isRowProcessed(_0x5e2497){const _0xd0b140=a0_0x256e30,_0x42f14f=Object[_0xd0b140(0x154)](appliedActions)[_0xd0b140(0x134)](_0x4704a8=>appliedActions[_0x4704a8]);if(_0x42f14f[_0xd0b140(0x173)]===0x0)return![];return _0x42f14f[_0xd0b140(0x150)](_0x2e79ed=>{const _0x1d0528=actionsConfig[_0x2e79ed]['scannedColumn'];if(_0x5e2497[_0x1d0528]===undefined)return![];return _0x5e2497[_0x1d0528]===!![];});}function renderTable(){const _0xb1ba5e=a0_0x256e30;dataTable[_0xb1ba5e(0x121)]='';let _0x2239ec=[];showRowNumber&&_0x2239ec['push'](_0xb1ba5e(0x129));_0x2239ec['push'](...selectedHeaders);for(const [_0x44558b,_0x3232e5]of Object['entries'](appliedActions)){if(_0x3232e5){const _0x579bb3=actionsConfig[_0x44558b];_0x579bb3[_0xb1ba5e(0x139)]['forEach'](_0x36e515=>{const _0x395384=_0xb1ba5e;if(!_0x2239ec[_0x395384(0x106)](_0x36e515))_0x2239ec[_0x395384(0x167)](_0x36e515);}),!_0x2239ec[_0xb1ba5e(0x106)](_0x579bb3[_0xb1ba5e(0x17c)])&&_0x2239ec[_0xb1ba5e(0x167)](_0x579bb3['scannedColumn']);}}currentHeaders=_0x2239ec;const _0x28707a=document[_0xb1ba5e(0x100)]('thead'),_0x3afda2=document['createElement']('tr');_0x2239ec[_0xb1ba5e(0x10f)](_0x310c1f=>{const _0x42ffde=_0xb1ba5e,_0x5defba=document[_0x42ffde(0x100)]('th');_0x5defba[_0x42ffde(0xfd)]=_0x310c1f,_0x310c1f[_0x42ffde(0x114)](_0x42ffde(0x111))&&_0x5defba['classList'][_0x42ffde(0x159)](_0x42ffde(0x103)),_0x3afda2['appendChild'](_0x5defba);}),_0x28707a[_0xb1ba5e(0x11b)](_0x3afda2),dataTable[_0xb1ba5e(0x11b)](_0x28707a);const _0x14053d=document['createElement']('tbody');let _0x3de465=originalData;showOnlyUnprocessed&&(_0x3de465=_0x3de465[_0xb1ba5e(0x134)](isRowUnprocessed)),_0x3de465[_0xb1ba5e(0x10f)]((_0x219661,_0x49d9bc)=>{const _0x16a4e3=_0xb1ba5e,_0x67860e=document[_0x16a4e3(0x100)]('tr');currentHeaders[_0x16a4e3(0x10f)](_0x5d4202=>{const _0x2900df=_0x16a4e3,_0x1d7218=document['createElement']('td');let _0x1cf3f1='';_0x5d4202===_0x2900df(0x129)?_0x1cf3f1=(_0x49d9bc+0x1)[_0x2900df(0x10e)]():(_0x1cf3f1=_0x219661[_0x5d4202]??'',_0x5d4202[_0x2900df(0x114)]('scanned')&&(_0x1cf3f1=_0x219661[_0x5d4202]===!![]?'✓':'')),_0x1d7218[_0x2900df(0xfd)]=_0x1cf3f1,_0x67860e[_0x2900df(0x11b)](_0x1d7218);}),isRowProcessed(_0x219661)&&_0x67860e['classList'][_0x16a4e3(0x159)](_0x16a4e3(0x105)),_0x14053d[_0x16a4e3(0x11b)](_0x67860e);}),dataTable[_0xb1ba5e(0x11b)](_0x14053d),updateProgressUI();}function isRowUnprocessed(_0x35c946){const _0x3c104f=a0_0x256e30,_0x5169d3=Object[_0x3c104f(0x154)](appliedActions)[_0x3c104f(0x134)](_0x204118=>appliedActions[_0x204118]);if(_0x5169d3[_0x3c104f(0x173)]===0x0)return![];return!_0x5169d3[_0x3c104f(0x150)](_0x46c3c3=>{const _0x54ff24=_0x3c104f,_0x3904ed=actionsConfig[_0x46c3c3][_0x54ff24(0x17c)];return _0x35c946[_0x3904ed]===!![];});}async function handleRestoreFromStorage(){const _0x3b04fa=a0_0x256e30;await restoreState();if(originalData[_0x3b04fa(0x173)]===0x0){alert(_0x3b04fa(0xec));return;}selectedHeaders[_0x3b04fa(0x173)]===0x0?showHeaderSelection():(initActionsUI(),renderTable(),mainSection['style'][_0x3b04fa(0x136)]=_0x3b04fa(0x112));}function a0_0x23e3(_0x586296,_0xe47c){const _0x14a7ad=a0_0x14a7();return a0_0x23e3=function(_0x23e364,_0x26a6c6){_0x23e364=_0x23e364-0xec;let _0x9061c5=_0x14a7ad[_0x23e364];return _0x9061c5;},a0_0x23e3(_0x586296,_0xe47c);}async function handleClearData(){const _0x51ba6e=a0_0x256e30;originalData=[],selectedHeaders=[],appliedActions={},processedCount=0x0,isProcessing=![],dataTable[_0x51ba6e(0x121)]='',mainSection[_0x51ba6e(0xed)][_0x51ba6e(0x136)]=_0x51ba6e(0x182),headerSelectionContainer[_0x51ba6e(0xed)]['display']=_0x51ba6e(0x182),await chrome[_0x51ba6e(0x168)][_0x51ba6e(0x11e)][_0x51ba6e(0x118)]([_0x51ba6e(0x13c),_0x51ba6e(0x146),_0x51ba6e(0x162),'currentLineIndex']),csvUpload[_0x51ba6e(0x141)]='',statusMessageEl[_0x51ba6e(0xfd)]='',processedCountEl[_0x51ba6e(0xfd)]='0',totalCountEl[_0x51ba6e(0xfd)]='0',alert(_0x51ba6e(0x165));}function handleDownloadCSV(){const _0x2059fd=a0_0x256e30,_0x58aab0=document['getElementById'](_0x2059fd(0x176));let _0x3588f9=[...originalData];if(_0x58aab0){const _0x24b839=_0x58aab0[_0x2059fd(0x141)];if(_0x24b839!==_0x2059fd(0xf9)){let _0x11ab89;if(_0x24b839===_0x2059fd(0x15a))_0x11ab89=!![];else _0x24b839===_0x2059fd(0x122)?_0x11ab89=![]:_0x11ab89=undefined;_0x3588f9=_0x3588f9[_0x2059fd(0x134)](_0x521783=>{const _0x11e5be=_0x2059fd,_0x333d14=_0x521783['scannedGSPR']===undefined?undefined:_0x521783[_0x11e5be(0x138)];return _0x333d14===_0x11ab89;});}}const _0x1c2816=generateCSV(_0x3588f9),_0x321d98=new Blob([_0x1c2816],{'type':'text/csv;charset=utf-8;'}),_0x48117b=URL[_0x2059fd(0x170)](_0x321d98),_0x4d6000=document['createElement']('a');_0x4d6000['href']=_0x48117b,_0x4d6000[_0x2059fd(0x13b)]=_0x2059fd(0x186),_0x4d6000[_0x2059fd(0x12f)](),URL[_0x2059fd(0x127)](_0x48117b);}function generateCSV(_0x5d05db){const _0x4b62a1=a0_0x256e30;let _0x50e3c0=[...selectedHeaders];for(const [_0x2810e5,_0x560389]of Object[_0x4b62a1(0x14b)](appliedActions)){if(_0x560389){const _0x573eba=actionsConfig[_0x2810e5];_0x573eba['dataColumns'][_0x4b62a1(0x10f)](_0x298aa8=>{const _0x5d6f83=_0x4b62a1;if(!_0x50e3c0['includes'](_0x298aa8))_0x50e3c0[_0x5d6f83(0x167)](_0x298aa8);}),!_0x50e3c0['includes'](_0x573eba[_0x4b62a1(0x17c)])&&_0x50e3c0[_0x4b62a1(0x167)](_0x573eba[_0x4b62a1(0x17c)]);}}const _0x15d3dc=_0x5d05db[_0x4b62a1(0xef)](_0x3884a7=>{const _0x4cdbb8=_0x4b62a1,_0x395f4e={..._0x3884a7};return _0x50e3c0[_0x4cdbb8(0x10f)](_0x307260=>{const _0x544e5b=_0x4cdbb8;_0x307260['startsWith'](_0x544e5b(0x111))&&(_0x395f4e[_0x307260]=_0x395f4e[_0x307260]===!![]?'true':_0x544e5b(0x122));}),_0x395f4e;});return Papa['unparse']({'fields':_0x50e3c0,'data':_0x15d3dc},{'quotes':!![],'delimiter':',','newline':'\x0a'});}async function saveState(){const _0x5cc3e8=a0_0x256e30;await chrome[_0x5cc3e8(0x168)][_0x5cc3e8(0x11e)][_0x5cc3e8(0x18c)]({'originalData':originalData,'selectedHeaders':selectedHeaders,'appliedActions':appliedActions,'currentLineIndex':currentLineIndex});}async function restoreState(){const _0x2cfeb2=a0_0x256e30,_0x16fcd2=await chrome[_0x2cfeb2(0x168)][_0x2cfeb2(0x11e)]['get']([_0x2cfeb2(0x13c),'selectedHeaders',_0x2cfeb2(0x162),_0x2cfeb2(0x104)]);_0x16fcd2[_0x2cfeb2(0x13c)]&&_0x16fcd2[_0x2cfeb2(0x146)]&&(originalData=_0x16fcd2['originalData'],selectedHeaders=_0x16fcd2['selectedHeaders'],appliedActions=_0x16fcd2[_0x2cfeb2(0x162)]||{}),typeof _0x16fcd2[_0x2cfeb2(0x104)]===_0x2cfeb2(0x17d)&&(currentLineIndex=_0x16fcd2['currentLineIndex']);}function updateProgressUI(){const _0x5b42f2=a0_0x256e30;processedCount=countProcessedRows();const _0x20d05b=originalData['length'],_0xc37f19=_0x20d05b-processedCount;processedCountEl[_0x5b42f2(0xfd)]=processedCount,totalCountEl[_0x5b42f2(0xfd)]=_0x20d05b;const _0x1cf32b=document[_0x5b42f2(0x169)](_0x5b42f2(0x16e));_0x1cf32b&&(_0x1cf32b[_0x5b42f2(0xfd)]=_0xc37f19),updateTitleWithProgress();}function updateTitleWithProgress(){const _0x5cd9fd=a0_0x256e30,_0x5a2627=countProcessedRows(),_0x4c3f7a=originalData['length'];document['title']='Processed:\x20'+_0x5a2627+_0x5cd9fd(0x152)+_0x4c3f7a+_0x5cd9fd(0x14c)+currentLineIndex+')';}function updateRunTitle(_0x35b474,_0x6489af){const _0x44f830=a0_0x256e30,_0x4e251f=countProcessedRows(),_0xb6e944=originalData[_0x44f830(0x173)];document[_0x44f830(0x113)]=_0x44f830(0x184)+_0x35b474+'/'+_0x6489af+_0x44f830(0xfb)+_0x4e251f+'/'+_0xb6e944+',\x20Current\x20line:\x20'+currentLineIndex;}function countProcessedRows(){const _0x3035ad=a0_0x256e30,_0x1db9c5=Object[_0x3035ad(0x14b)](actionsConfig)['filter'](([_0x329630])=>appliedActions[_0x329630])[_0x3035ad(0xef)](([_0x2fcfdf])=>_0x2fcfdf);if(_0x1db9c5[_0x3035ad(0x173)]===0x0)return 0x0;return originalData['filter'](_0x47d17e=>{const _0x300143=_0x3035ad;return _0x1db9c5[_0x300143(0x150)](_0x487afc=>{const _0x543f54=_0x300143,_0x436a30=actionsConfig[_0x487afc][_0x543f54(0x17c)];return _0x47d17e[_0x436a30]===!![];});})['length'];}async function handleRunActions(){const _0x544c9c=a0_0x256e30;if(isProcessing)return;concurrency=parseInt(concurrencyInput[_0x544c9c(0x141)],0xa)||0x1;const _0x40df72=Array['from'](actionCheckboxes['querySelectorAll']('.actionCheck:checked'))[_0x544c9c(0xef)](_0xa73017=>_0xa73017['value']);if(_0x40df72[_0x544c9c(0x173)]===0x0){alert('No\x20actions\x20selected.');return;}let _0x1eef7d=0x0;startLineInput&&(_0x1eef7d=parseInt(startLineInput[_0x544c9c(0x141)],0xa)||0x0,_0x1eef7d=Math[_0x544c9c(0x13a)](0x0,Math[_0x544c9c(0xf5)](_0x1eef7d,originalData[_0x544c9c(0x173)]-0x1)),currentLineIndex=_0x1eef7d,await saveState());isProcessing=!![],statusMessageEl[_0x544c9c(0xfd)]=_0x544c9c(0x18f);for(const _0x3cd5d7 of _0x40df72){if(!appliedActions[_0x3cd5d7]){appliedActions[_0x3cd5d7]=!![];const _0x2d2dbb=actionsConfig[_0x3cd5d7];for(const _0x3173c9 of originalData){_0x3173c9[_0x2d2dbb[_0x544c9c(0x17c)]]=_0x3173c9[_0x2d2dbb[_0x544c9c(0x17c)]]||![];}renderTable();}}const _0x5c7c1a=[];for(let _0x5486bb=currentLineIndex;_0x5486bb<originalData[_0x544c9c(0x173)];_0x5486bb++){const _0x1c3e4f=originalData[_0x5486bb];!isRowProcessed(_0x1c3e4f)&&_0x5c7c1a[_0x544c9c(0x167)]({'row':_0x1c3e4f,'idx':_0x5486bb});}const _0x354916=_0x5c7c1a['length'];let _0x564448=0x0;updateRunTitle(_0x564448,_0x354916);if(_0x40df72[_0x544c9c(0x106)]('gspr')){const _0x2f6700=actionsConfig[_0x544c9c(0x12b)];for(let _0xc18978=0x0;_0xc18978<originalData[_0x544c9c(0x173)];_0xc18978++){console['log'](_0x544c9c(0x18e),_0xc18978);const _0x76b83c=originalData[_0xc18978];if(_0x76b83c[_0x2f6700[_0x544c9c(0x17c)]]===!![])continue;const _0x59b925=_0x76b83c['Manufacturer\x20Info\x20Text']??null,_0x2d44f3=_0x76b83c[_0x544c9c(0x12e)]??null;if(!_0x59b925||!_0x2d44f3)continue;if(_0x59b925||_0x2d44f3){const _0x2e601c=parseManufacturerText(_0x59b925),_0x4a9df3=parseEUResponsibleText(_0x2d44f3);let _0x33ca9e=!![];_0x59b925&&(_0x2e601c['Manufacturer\x20Name']===null||_0x2e601c[_0x544c9c(0x107)]===null)&&(_0x33ca9e=![]),_0x2d44f3&&(_0x4a9df3[_0x544c9c(0x177)]===null||_0x4a9df3[_0x544c9c(0xfc)]===null)&&(_0x33ca9e=![]),_0x2f6700[_0x544c9c(0x139)][_0x544c9c(0x10f)](_0x3c1e88=>{_0x76b83c[_0x3c1e88]=_0x4a9df3[_0x3c1e88]??_0x2e601c[_0x3c1e88]??_0x76b83c[_0x3c1e88]??null;}),_0x76b83c[_0x2f6700[_0x544c9c(0x17c)]]=_0x33ca9e;}}await saveState(),renderTable();}await runWithConcurrency(_0x5c7c1a,async _0x870cc2=>{const _0x219c72=_0x544c9c,{row:_0x1d5d8e,idx:_0x54ec3a}=_0x870cc2;await processAllActionsForRow(_0x1d5d8e,_0x40df72),_0x564448++,currentLineIndex=_0x54ec3a,await saveState(),startLineInput&&(startLineInput[_0x219c72(0x141)]=currentLineIndex),updateRunTitle(_0x564448,_0x354916);}),statusMessageEl[_0x544c9c(0xfd)]=_0x544c9c(0x120),isProcessing=![],await saveState(),updateTitleWithProgress();}async function processAllActionsForRow(_0x594860,_0x1ae744){for(const _0x3629a3 of _0x1ae744){!isRowActionScanned(_0x594860,_0x3629a3)&&await processRowAction(_0x594860,_0x3629a3);}}function isRowActionScanned(_0x3bfec,_0xd13f61){const _0x19425c=actionsConfig[_0xd13f61]['scannedColumn'];return _0x3bfec[_0x19425c]===!![];}async function runWithConcurrency(_0x21377a,_0xa62ae6){let _0x42e804=0x0,_0x541461=0x0;return new Promise(_0x59da6c=>{const _0x1bc542=async()=>{while(_0x541461<concurrency&&_0x42e804<_0x21377a['length']){const _0x4dd246=_0x21377a[_0x42e804++];_0x541461++,_0xa62ae6(_0x4dd246)['then'](async()=>{const _0x25dc65=a0_0x23e3;_0x541461--,updateProgressUI(),await saveState(),_0x42e804===_0x21377a[_0x25dc65(0x173)]&&_0x541461===0x0?_0x59da6c():_0x1bc542();});}};_0x1bc542();});}function parseManufacturerText(_0x538e2f){const _0xf7c6d6=a0_0x256e30;if(!_0x538e2f||_0x538e2f[_0xf7c6d6(0x180)]()==='')return{};const _0x4e75fa=parseFirstAddress(_0x538e2f);return{'Manufacturer\x20Name':_0x4e75fa[_0xf7c6d6(0x187)]??null,'Manufacturer\x20AddressLine1':_0x4e75fa[_0xf7c6d6(0x15b)]??null,'Manufacturer\x20AddressLine2':_0x4e75fa[_0xf7c6d6(0x130)]??null,'Manufacturer\x20City':_0x4e75fa[_0xf7c6d6(0x132)]??null,'Manufacturer\x20Country':_0x4e75fa[_0xf7c6d6(0x109)]??null,'Manufacturer\x20PostalCode':_0x4e75fa[_0xf7c6d6(0x15c)]??null,'Manufacturer\x20StateOrProvince':_0x4e75fa[_0xf7c6d6(0x11c)]??null,'Manufacturer\x20Phone':_0x4e75fa[_0xf7c6d6(0x144)]??null,'Manufacturer\x20Email':_0x4e75fa[_0xf7c6d6(0x13d)]??null};}function parseEUResponsibleText(_0x28d004){const _0x16061c=a0_0x256e30;if(!_0x28d004||_0x28d004['trim']()==='')return{};const _0x200167=parseFirstAddress(_0x28d004);return{'Responsible\x20Person\x201':_0x200167['name']??null,'Responsible\x20Person\x201\x20Type':'EUResponsiblePerson','Responsible\x20Person\x201\x20AddressLine1':_0x200167['address1']??null,'Responsible\x20Person\x201\x20AddressLine2':_0x200167[_0x16061c(0x130)]??null,'Responsible\x20Person\x201\x20City':_0x200167['city']??null,'Responsible\x20Person\x201\x20Country':_0x200167[_0x16061c(0x109)]??null,'Responsible\x20Person\x201\x20PostalCode':_0x200167['postalCode']??null,'Responsible\x20Person\x201\x20StateOrProvince':_0x200167[_0x16061c(0x11c)]??null,'Responsible\x20Person\x201\x20Phone':_0x200167[_0x16061c(0x144)]??null,'Responsible\x20Person\x201\x20Email':_0x200167[_0x16061c(0x13d)]??null};}async function processRowAction(_0x4b8545,_0x3d1ac4){const _0x1c9a70=a0_0x256e30;if(isRowActionScanned(_0x4b8545,_0x3d1ac4))return;const _0x1d2245=actionsConfig[_0x3d1ac4],_0x56970d=_0x4b8545[_0x1c9a70(0x10a)]||'';try{if(_0x3d1ac4===_0x1c9a70(0x12b)){const _0x3c75fa=_0x4b8545[_0x1c9a70(0x12a)]??null,_0x4425c9=_0x4b8545[_0x1c9a70(0x12e)]??null;let _0x7442f3={};if(_0x3c75fa||_0x4425c9){const _0x86b377=parseManufacturerText(_0x3c75fa),_0x5955f1=parseEUResponsibleText(_0x4425c9);_0x7442f3={..._0x86b377,..._0x5955f1};let _0x513669=!![];_0x3c75fa&&(_0x86b377[_0x1c9a70(0x189)]===null||_0x86b377['Manufacturer\x20Country']===null)&&(_0x513669=![]),_0x4425c9&&(_0x5955f1[_0x1c9a70(0x177)]===null||_0x5955f1[_0x1c9a70(0xfc)]===null)&&(_0x513669=![]),_0x1d2245[_0x1c9a70(0x139)][_0x1c9a70(0x10f)](_0x3581a2=>{_0x4b8545[_0x3581a2]=_0x7442f3[_0x3581a2]??_0x4b8545[_0x3581a2]??null;}),_0x4b8545[_0x1d2245[_0x1c9a70(0x17c)]]=_0x513669;}else{const _0x4c9550=await getGSPR(_0x56970d);let _0x370d18=!![];(_0x4c9550['Manufacturer\x20Name']===null||_0x4c9550[_0x1c9a70(0x107)]===null)&&(_0x370d18=![]),_0x4c9550['Responsible\x20Person\x201']!==null&&(_0x4c9550[_0x1c9a70(0x177)]===null||_0x4c9550['Responsible\x20Person\x201\x20Country']===null)&&(_0x370d18=![]),_0x1d2245[_0x1c9a70(0x139)][_0x1c9a70(0x10f)](_0x1ed7a8=>{_0x4b8545[_0x1ed7a8]=_0x4c9550[_0x1ed7a8]??null;}),_0x4b8545[_0x1d2245[_0x1c9a70(0x17c)]]=_0x370d18;}}else{if(_0x3d1ac4==='chineseSeller'){console[_0x1c9a70(0x188)](_0x1c9a70(0x183),_0x56970d);const _0x2774f6=await checkIfSellerChinese(_0x56970d);_0x1d2245[_0x1c9a70(0x139)]['forEach'](_0xb5250a=>{_0x4b8545[_0xb5250a]=_0x2774f6[_0xb5250a]??'';}),_0x4b8545[_0x1d2245[_0x1c9a70(0x17c)]]=!![];}else _0x1d2245['dataColumns'][_0x1c9a70(0x10f)](_0x1b82e6=>{_0x4b8545[_0x1b82e6]='Dummy\x20Data';}),_0x4b8545[_0x1d2245['scannedColumn']]=!![];}}catch(_0x3a1e60){console[_0x1c9a70(0x116)](_0x1c9a70(0x10c),_0x3d1ac4,_0x3a1e60);if(debugMode)throw _0x3a1e60;else console['warn']('Skipping\x20due\x20to\x20error:',_0x3a1e60[_0x1c9a70(0x10d)]);}await saveState();}function a0_0x14a7(){const _0x25d118=['storage','getElementById','\x22\x20checked>\x20','12984OkYvtM','All','</label>','unprocessedCount','actionCheckboxes','createObjectURL','startLineInput','change','length','Filter','8mGXaEQ','scannedGSPRFilter','Responsible\x20Person\x201','Fetching\x20GSPR\x20data\x20for\x20URL:\x20','random','label','mainSection','scannedColumn','number','showUnprocessedOnly','<label><input\x20type=\x22checkbox\x22\x20class=\x22actionCheck\x22\x20value=\x22','trim','debugModeSwitch','none','Checking\x20if\x20seller\x20is\x20Chinese\x20for\x20customLabel:','Run:\x20','values','exported_data.csv','name','log','Manufacturer\x20Name','Manufacturer\x20Email','input[type=\x22checkbox\x22]','set','headerCheckboxes','Pre-passing\x20row','Processing...','No\x20saved\x20data\x20found\x20in\x20storage.','style','manufacturerInfo','map','Filter\x20by\x20','Debug\x20mode\x20is\x20now','hasOwnProperty','sendMessage','statusMessage','min','get_gspr','addEventListener','restoreFromStorageBtn','all','Responsible\x20Person\x201\x20AddressLine1',',\x20Overall:\x20','Responsible\x20Person\x201\x20Country','textContent','DOMContentLoaded','flatMap','createElement','16139610ocMcWv','<label><input\x20type=\x22checkbox\x22\x20value=\x22','scanned-header','currentLineIndex','row-done','includes','Manufacturer\x20Country','manufacturerInfoText','country','CustomLabel','Manufacturer\x20City','Error\x20processing\x20action:','message','toString','forEach','Please\x20select\x20at\x20least\x20one\x20header.','scanned','block','title','startsWith','Manufacturer\x20PostalCode','error','headerSelectionContainer','remove','checked','Chinese\x20Seller','appendChild','stateOrProvince','get','local','8170950WhEJBo','Processing\x20complete.','innerHTML','false','159423ysYNUS','option','/?th=1&psc=1','downloadCsvBtn','revokeObjectURL','2517684RnnSHY','Row','Manufacturer\x20Info\x20Text','gspr','Check\x20Chinese\x20Seller','toLowerCase','Responsible\x20Person\x20EU\x20Text','click','address2','Manufacturer\x20AddressLine2','city','processedCount','filter','Domain\x20not\x20found\x20in\x20storage','display','Responsible\x20Person\x201\x20AddressLine2','scannedGSPR','dataColumns','max','download','originalData','emailAddress','Get\x20GSPR','responsiblePersonEUText','undefined','value','div','Manufacturer\x20AddressLine1','phoneNumber','GSPR\x20data\x20not\x20found\x20for\x20SKU:\x20','selectedHeaders','filtersContainer','CSV\x20appears\x20to\x20be\x20empty\x20or\x20invalid.','Responsible\x20Person\x201\x20City','confirmHeadersBtn','entries','\x20(Line:\x20','Error\x20parsing\x20CSV:','Responsible\x20Person\x201\x20Type','dataTable','every','5808080PFVCbD','\x20/\x20','responsiblePersonEU','keys','/dp/','Responsible\x20Person\x201\x20Phone','from','Failed\x20to\x20decode\x20customLabel:\x20','add','true','address1','postalCode','querySelectorAll','5747XrLJBz','totalCount','target','9fgMhKy','appliedActions','parse','1538390oZiywX','CSV\x20data\x20cleared.','Failed\x20to\x20get\x20GSPR\x20data\x20from\x20background:\x20','push'];a0_0x14a7=function(){return _0x25d118;};return a0_0x14a7();}async function getGSPR(_0x16a4c2){const _0x38d5ac=a0_0x256e30;let _0x5ec65b;try{_0x5ec65b=atob(_0x16a4c2);}catch(_0x3b6bea){throw new Error(_0x38d5ac(0x158)+_0x16a4c2);}const {domain:_0xc330be}=await chrome[_0x38d5ac(0x168)][_0x38d5ac(0x11e)][_0x38d5ac(0x11d)]('domain')||{};if(!_0xc330be)throw new Error(_0x38d5ac(0x135));const _0x179b6b='https://www.amazon.'+_0xc330be+_0x38d5ac(0x155)+_0x5ec65b+_0x38d5ac(0x125);console['log'](_0x38d5ac(0x178)+_0x179b6b);let _0x5df948;try{_0x5df948=await new Promise(_0x5a16d8=>{const _0x24aa19=_0x38d5ac;chrome['runtime'][_0x24aa19(0xf3)]({'type':_0x24aa19(0xf6),'url':_0x179b6b},function(_0x19ef53){_0x5a16d8(_0x19ef53);});});}catch(_0x461f70){throw new Error(_0x38d5ac(0x166)+_0x461f70[_0x38d5ac(0x10d)]);}if(!_0x5df948||!_0x5df948[_0x38d5ac(0x12b)])throw new Error(_0x38d5ac(0x145)+_0x16a4c2);const _0x137f32=_0x5df948['gspr'],_0x33394c=_0x137f32[_0x38d5ac(0xee)]||{},_0xb432f8=_0x137f32[_0x38d5ac(0x153)]||{};var _0x2b1c47=_0x137f32[_0x38d5ac(0x108)],_0x13e099=_0x137f32[_0x38d5ac(0x13f)];return{'Manufacturer\x20Name':_0x33394c['name']??null,'Manufacturer\x20AddressLine1':_0x33394c[_0x38d5ac(0x15b)]??null,'Manufacturer\x20AddressLine2':_0x33394c['address2']??null,'Manufacturer\x20City':_0x33394c[_0x38d5ac(0x132)]??null,'Manufacturer\x20Country':_0x33394c[_0x38d5ac(0x109)]??null,'Manufacturer\x20PostalCode':_0x33394c[_0x38d5ac(0x15c)]??null,'Manufacturer\x20StateOrProvince':_0x33394c[_0x38d5ac(0x11c)]??null,'Manufacturer\x20Phone':_0x33394c['phoneNumber']??null,'Manufacturer\x20Email':_0x33394c['emailAddress']??null,'Responsible\x20Person\x201':_0xb432f8[_0x38d5ac(0x187)]??null,'Responsible\x20Person\x201\x20Type':'EUResponsiblePerson','Responsible\x20Person\x201\x20AddressLine1':_0xb432f8['address1']??null,'Responsible\x20Person\x201\x20AddressLine2':_0xb432f8['address2']??null,'Responsible\x20Person\x201\x20City':_0xb432f8[_0x38d5ac(0x132)]??null,'Responsible\x20Person\x201\x20Country':_0xb432f8[_0x38d5ac(0x109)]??null,'Responsible\x20Person\x201\x20PostalCode':_0xb432f8[_0x38d5ac(0x15c)]??null,'Responsible\x20Person\x201\x20StateOrProvince':_0xb432f8[_0x38d5ac(0x11c)]??null,'Responsible\x20Person\x201\x20Phone':_0xb432f8['phoneNumber']??null,'Responsible\x20Person\x201\x20Email':_0xb432f8[_0x38d5ac(0x13d)]??null,'Manufacturer\x20Info\x20Text':_0x2b1c47??null,'Responsible\x20Person\x20EU\x20Text':_0x13e099??null};}async function checkIfSellerChinese(_0x34602d){const _0x47fe63=a0_0x256e30;await new Promise(_0x444cea=>setTimeout(_0x444cea,0x32));const _0x7584dc=Math[_0x47fe63(0x179)]()>0.5?_0x47fe63(0x15a):_0x47fe63(0x122);return{'Chinese\x20Seller':_0x7584dc};}function createFilterForColumn(_0x52b38d){const _0x3e862e=a0_0x256e30,_0xc0dcae=document[_0x3e862e(0x169)](_0x3e862e(0x147)),_0x58d3c4=getUniqueValuesForColumn(_0x52b38d),_0x3ea681=document[_0x3e862e(0x100)](_0x3e862e(0x17a));_0x3ea681[_0x3e862e(0xfd)]=_0x3e862e(0xf0)+_0x52b38d+':\x20';const _0x1369fe=document[_0x3e862e(0x100)]('select');_0x1369fe['id']=_0x52b38d+_0x3e862e(0x174);const _0x48752e=document[_0x3e862e(0x100)](_0x3e862e(0x124));_0x48752e['value']=_0x3e862e(0xf9),_0x48752e[_0x3e862e(0xfd)]=_0x3e862e(0x16c),_0x1369fe[_0x3e862e(0x11b)](_0x48752e);for(const _0x23a16c of _0x58d3c4){const _0x32fffa=document[_0x3e862e(0x100)]('option'),_0x172d95=_0x23a16c===!![]?_0x3e862e(0x15a):_0x23a16c===![]?_0x3e862e(0x122):_0x3e862e(0x140);_0x32fffa[_0x3e862e(0x141)]=_0x172d95,_0x32fffa[_0x3e862e(0xfd)]=_0x172d95,_0x1369fe[_0x3e862e(0x11b)](_0x32fffa);}_0x3ea681[_0x3e862e(0x11b)](_0x1369fe),_0xc0dcae[_0x3e862e(0x11b)](_0x3ea681);}function getUniqueValuesForColumn(_0x561a18){const _0x1a19db=a0_0x256e30,_0x7901cb=new Set();for(const _0x316d4f of originalData){const _0x11a65d=_0x316d4f[_0x561a18]===undefined?undefined:_0x316d4f[_0x561a18];_0x7901cb['add'](_0x11a65d);}return Array[_0x1a19db(0x157)](_0x7901cb);}