body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f4f4f4;
  }
  
  h1 {
    color: #333;
  }
  
  /* Each section styled as a card */
  section {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  fieldset {
    border: none;
    margin: 0;
    padding: 0;
  }
  
  legend {
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  div {
    margin-bottom: 10px;
  }
  
  label {
    color: #666;
    margin-left: 5px;
  }
  
  /* Ensure checkboxes align well */
  input[type="checkbox"] {
    vertical-align: middle;
  }
  
  input[type="text"],
  input[type="number"],
  select,
  textarea {
    display: block;
    width: 100%;
    padding: 6px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
  }
  
  /* Increase the textarea height for multi-line content */
  textarea {
    resize: vertical;
    min-height: 150px;
  }
  
  button {
    padding: 8px 16px;
    background-color: #4caf50;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
  }
  
  button:hover {
    background-color: #45a049;
  }
  


  .description-template {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 20px;
  }
  
  .description-template input[type="checkbox"] {
    transform: scale(1.1);
    margin: 0;
    cursor: pointer;
  }
  
  .description-template label {
    font-size: 14px;
    cursor: pointer;
    color: #333;
    margin: 0;
  }
  
  #openDescriptionTemplateBuilder {
    padding: 8px 12px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  #openDescriptionTemplateBuilder:hover {
    background-color: #0056b3;
  }
  