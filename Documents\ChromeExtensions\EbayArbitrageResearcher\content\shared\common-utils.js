/**
 * Common Utils - Shared utility functions for content scripts
 */
class CommonUtils {
  constructor() {
    this.debugMode = true;
    console.log('🛠️ CommonUtils initialized');
  }

  /**
   * Enhanced logging with context
   * @param {string} message - Log message
   * @param {string} level - Log level (info, warn, error)
   * @param {object} context - Additional context
   */
  log(message, level = 'info', context = {}) {
    const timestamp = new Date().toISOString();
    const prefix = this.getLogPrefix(level);
    
    if (this.debugMode) {
      const logMessage = `${prefix} [${timestamp}] ${message}`;
      
      switch (level) {
        case 'error':
          console.error(logMessage, context);
          break;
        case 'warn':
          console.warn(logMessage, context);
          break;
        default:
          console.log(logMessage, context);
      }
    }
  }

  /**
   * Get log prefix for level
   * @param {string} level - Log level
   * @returns {string} Prefix
   */
  getLogPrefix(level) {
    const prefixes = {
      info: '📝',
      warn: '⚠️',
      error: '❌',
      success: '✅',
      debug: '🔍'
    };
    return prefixes[level] || '📝';
  }

  /**
   * Wait for element to appear in DOM
   * @param {string} selector - CSS selector
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<Element>} Found element
   */
  async waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Safely get nested object property
   * @param {object} obj - Object to traverse
   * @param {string} path - Dot notation path
   * @param {*} defaultValue - Default value if path not found
   * @returns {*} Property value or default
   */
  safeGet(obj, path, defaultValue = null) {
    try {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : defaultValue;
      }, obj);
    } catch (error) {
      return defaultValue;
    }
  }

  /**
   * Debounce function execution
   * @param {function} func - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {function} Debounced function
   */
  debounce(func, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Throttle function execution
   * @param {function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {function} Throttled function
   */
  throttle(func, limit) {
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Format currency value
   * @param {number} value - Numeric value
   * @param {string} currency - Currency code
   * @returns {string} Formatted currency
   */
  formatCurrency(value, currency = 'USD') {
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(value);
    } catch (error) {
      return `$${value.toFixed(2)}`;
    }
  }

  /**
   * Parse price from text
   * @param {string} text - Text containing price
   * @returns {number} Parsed price
   */
  parsePrice(text) {
    try {
      const cleaned = text.replace(/[^0-9.]/g, '');
      return parseFloat(cleaned) || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Generate unique ID
   * @param {string} prefix - Optional prefix
   * @returns {string} Unique ID
   */
  generateId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if element is visible
   * @param {Element} element - DOM element
   * @returns {boolean} True if visible
   */
  isElementVisible(element) {
    try {
      const rect = element.getBoundingClientRect();
      return rect.width > 0 && rect.height > 0 && 
             rect.top >= 0 && rect.left >= 0 &&
             rect.bottom <= window.innerHeight && 
             rect.right <= window.innerWidth;
    } catch (error) {
      return false;
    }
  }

  /**
   * Scroll element into view smoothly
   * @param {Element} element - Element to scroll to
   * @param {object} options - Scroll options
   */
  scrollToElement(element, options = {}) {
    try {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
        ...options
      });
    } catch (error) {
      this.log('Error scrolling to element', 'error', { error: error.message });
    }
  }

  /**
   * Create and dispatch custom event
   * @param {string} eventName - Event name
   * @param {object} detail - Event detail data
   * @param {Element} target - Target element (default: document)
   */
  dispatchEvent(eventName, detail = {}, target = document) {
    try {
      const event = new CustomEvent(eventName, {
        detail,
        bubbles: true,
        cancelable: true
      });
      target.dispatchEvent(event);
    } catch (error) {
      this.log('Error dispatching event', 'error', { eventName, error: error.message });
    }
  }

  /**
   * Get current page info
   * @returns {object} Page information
   */
  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      path: window.location.pathname,
      search: window.location.search,
      timestamp: Date.now()
    };
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.CommonUtils = new CommonUtils();
window.globalContext.CommonUtils = window.CommonUtils;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CommonUtils;
}

console.log('✅ CommonUtils loaded and exported to window.CommonUtils');
