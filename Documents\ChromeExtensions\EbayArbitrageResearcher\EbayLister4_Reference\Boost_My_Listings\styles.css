/* styles.css */
.loading-bar {
    width: 100%;
    background-color: #ddd;
    border-radius: 5px;
    margin-top: 20px;
}

.loading-progress {
    height: 20px;
    background-color: #4CAF50;
    width: 0%; /* Initial width */
    border-radius: 5px;
    transition: width 0.4s ease-in-out;
}

#status_message {
    font-size: 16px;
    color: #666;
    margin-top: 10px;
}




.input-group {
    display: flex;
    flex-direction: column;
}

.filter {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.filter label {
    font-weight: bold;
    flex-basis: 50%;
    cursor: help;
    position: relative;
    display: flex;
    align-items: center;
}

.filter input[type="number"] {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.checkbox-filter {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.tooltip-icon {
    margin-left: 5px;
    color: #6c757d; /* Icon color for normal state */
}

.filter label:hover .tooltip-icon {
    color: #495057; /* Darken icon on hover for feedback */
}

.tooltip {
    visibility: hidden;
    width: 250px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: 130%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.filter label:hover .tooltip {
    visibility: visible;
    opacity: 1;
}



#end_and_sell_similar_button {
    padding: 5px 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}


#end_and_sell_similar_button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.toggle_options_button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px; /* Adjust size of the gear icon */
    color: #4CAF50; /* Color to match the theme */
    vertical-align: middle; /* Aligns button with text or other elements */
}

.toggle_options_button:hover {
    color: #367c39; /* Darker shade on hover for visual feedback */
}


.checkbox-filter {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
}

.checkbox-filter .filter {
    display: flex;
    align-items: center; /* Aligns items vertically */
    justify-content: flex-start; /* Aligns items to the start (left) */
    width: 100%; /* Ensures the container takes full width */
    margin-bottom: 10px;
}
s
.checkbox-filter .filter label {
    display: flex;
    align-items: center;
    cursor: help;
    width: calc(100% - 40px); /* Adjusts width accounting for checkbox and any padding/margin */
    flex-shrink: 0; /* Prevents the label from shrinking */
}

.checkbox-filter .filter input[type="checkbox"] {
    margin-left: 10px; /* Spacing between label and checkbox */
    width: 20px; /* Fixed width for checkbox */
    height: 20px; /* Ensures a fixed height for checkboxes */
}






#current_time_container, #schedule_container {
    margin-top: 20px;
}

#schedule_container input[type="checkbox"], #schedule_container input[type="time"] {
    margin-right: 10px;
    
   
}

#scheduled_time:disabled {
    background-color: #eee; /* Light grey to indicate disabled input */
}






/*for modal*/
/* Modal styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

/* Pulsating effect for the info button */
@keyframes pulsate {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.info-button {
    animation: pulsate 2s infinite;
}



.main_container {
    flex-direction: column;

    margin-top: 50px;


}



/* Styles for the log button */
.log-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: #4CAF50;
    margin-left: 10px;
}

.log-button:hover {
    color: #367c39;
}

/* Styles for the log modal */
#log_list {
    list-style-type: none;
    padding: 0;
}

#log_list li {
    background-color: #f9f9f9;
    margin-bottom: 5px;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}


#bulk_revise_listing_button {
    padding: 5px 10px;
    background-color: #f88b32;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

#bulk_revise_listing_button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#divider {
    margin-top: 20px;
    border-top: 1px solid #ddd;


}