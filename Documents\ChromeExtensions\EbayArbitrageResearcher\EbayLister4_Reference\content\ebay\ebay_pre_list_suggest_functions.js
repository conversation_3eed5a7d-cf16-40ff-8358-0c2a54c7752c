function a0_0x1000(_0x1b3bb3,_0x2207da){var _0x340328=a0_0x3403();return a0_0x1000=function(_0x100020,_0x2a7c2a){_0x100020=_0x100020-0x16e;var _0x50c8e9=_0x340328[_0x100020];return _0x50c8e9;},a0_0x1000(_0x1b3bb3,_0x2207da);}var a0_0xdcf2a4=a0_0x1000;(function(_0x336cd5,_0x5184f3){var _0x198223=a0_0x1000,_0x580912=_0x336cd5();while(!![]){try{var _0x487222=parseInt(_0x198223(0x17a))/0x1+parseInt(_0x198223(0x17c))/0x2*(parseInt(_0x198223(0x17f))/0x3)+-parseInt(_0x198223(0x188))/0x4+-parseInt(_0x198223(0x17b))/0x5+-parseInt(_0x198223(0x18d))/0x6*(-parseInt(_0x198223(0x187))/0x7)+-parseInt(_0x198223(0x190))/0x8*(parseInt(_0x198223(0x16f))/0x9)+-parseInt(_0x198223(0x182))/0xa*(parseInt(_0x198223(0x181))/0xb);if(_0x487222===_0x5184f3)break;else _0x580912['push'](_0x580912['shift']());}catch(_0x410ff4){_0x580912['push'](_0x580912['shift']());}}}(a0_0x3403,0x24e84),console['log'](a0_0xdcf2a4(0x17d)));function tellEbayWhatYourSelling(_0x30e0f3){var _0x115457=a0_0xdcf2a4;document[_0x115457(0x185)]='eBay\x20suggest\x20title\x20-\x20tellEbayWhatYourSelling\x20begins';var _0x4862a5=getSuggestionInput();_0x4862a5[_0x115457(0x176)]=_0x30e0f3,document[_0x115457(0x185)]=_0x115457(0x16e),_0x4862a5[_0x115457(0x175)](new Event(_0x115457(0x170),{'bubbles':!![]})),_0x4862a5[_0x115457(0x175)](new Event('input',{'bubbles':!![]})),document[_0x115457(0x185)]='eBay\x20suggest\x20title\x20-\x20tellEbayWhatYourSelling\x20ends\x20#2';}async function checkIfTitleWasInputted(){var _0x15cfda=a0_0xdcf2a4,_0x22474b=getSuggestionInput(),_0x3a5326=_0x22474b[_0x15cfda(0x176)];return _0x3a5326?!![]:![];}function getSuggestionInput(){var _0x57b63b=a0_0xdcf2a4,_0x437427;return _0x437427=document[_0x57b63b(0x179)](_0x57b63b(0x17e)),!_0x437427&&(_0x437427=document[_0x57b63b(0x179)]('[id*=\x22@simplified-keyword-@search-textbox-@se-textbox\x22]')),_0x437427;}function a0_0x3403(){var _0x1a0038=['186870DWSPNn','64586RfXmEU','Ebay\x20Pre\x20List\x20Suggest\x20Functions\x20initialized','[id$=\x22@box-@input-textbox\x22]','27SffOCM','runtime','31251HCOgpB','430MmqOdR','sendMessage','fetchData','title','data','7eJhYPs','24904rOPrtT','.keyword-suggestion__button','prompts_json/buyer_search_query.json','[aria-label=\x22Go\x20to\x20list\x20an\x20item\x20page\x22\x20i]','json','601266DyvAeg','log','https://openai-function-call-djybcnnsgq-uc.a.run.app','10136iQmOtb','eBay\x20suggest\x20title\x20-\x20tellEbayWhatYourSelling\x20ends','1953vmRurC','change','click','.sell-search-simplified__next-action','[aria-label=\x22Continue\x22\x20i]','jsonPrompt','dispatchEvent','value','output','[aria-label=\x22Search\x22\x20i]','querySelector','201026ESvsaU'];a0_0x3403=function(){return _0x1a0038;};return a0_0x3403();}function clickGoToListAnItem(){var _0x4f27e1=a0_0xdcf2a4,_0x406c64;_0x406c64=document[_0x4f27e1(0x179)](_0x4f27e1(0x173)),!_0x406c64&&(_0x406c64=document[_0x4f27e1(0x179)](_0x4f27e1(0x18b))),!_0x406c64&&(_0x406c64=document[_0x4f27e1(0x179)](_0x4f27e1(0x178))),!_0x406c64&&(_0x406c64=document[_0x4f27e1(0x179)](_0x4f27e1(0x189))),!_0x406c64&&(_0x406c64=document[_0x4f27e1(0x179)](_0x4f27e1(0x172))),_0x406c64[_0x4f27e1(0x171)]();}async function getBuyerSearchQueryFromTitle(_0x4d0207){var _0xb29bf5=a0_0xdcf2a4,_0x41235b=await fetch(chrome[_0xb29bf5(0x180)]['getURL'](_0xb29bf5(0x18a)))['then'](_0xc063d1=>_0xc063d1[_0xb29bf5(0x18c)]());_0x41235b['user_input']=_0x4d0207,console['log'](_0xb29bf5(0x174),_0x41235b);var _0x5de50b=await new Promise((_0x445e50,_0xbdd281)=>{var _0x243cb1=_0xb29bf5;chrome[_0x243cb1(0x180)][_0x243cb1(0x183)]({'type':_0x243cb1(0x184),'url':_0x243cb1(0x18f),'data':_0x41235b},function(_0x2e9cc7){var _0x15f4f8=_0x243cb1;_0x445e50(_0x2e9cc7[_0x15f4f8(0x186)]);});});console[_0xb29bf5(0x18e)](_0xb29bf5(0x186),_0x5de50b),_0x5de50b=JSON['parse'](_0x5de50b);var _0x1d1860=_0x5de50b[_0xb29bf5(0x177)];return _0x1d1860;}