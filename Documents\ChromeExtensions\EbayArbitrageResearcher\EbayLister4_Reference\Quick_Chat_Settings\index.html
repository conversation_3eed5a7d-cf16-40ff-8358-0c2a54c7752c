<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="modal.css">
    <link rel="stylesheet" href="index.css">

    <title>Quick Chat Settings</title>
</head>

<body>
    
    Quick Chat Settings
    <div class="quick_chat_switch_container">
        <label for="quick_chat_switch">Enable Quick Chat Buttons</label>
        <input type="checkbox" id="quick_chat_switch">
    </div>


    <div class="quick_chat_button_settings">
        <h1>Create Quick Chat Button</h1>

        <label for="quick_chat_button_name">Button Name</label>
        <input type="text" id="quick_chat_button_name" placeholder="Button Name" value="Test Button">

        <label for="quick_chat_button_color">Button Color</label>
        <input type="color" id="quick_chat_button_color" placeholder="Button Color" value="#FFFF00">

        <div id="quick_chat_messages_container">
            <!-- <h3>Quick Chat Messages</h3> -->

            <label for="quick_chat_message">Quick Chat Message</label>
            <div class="quick_chat_message_container">
                <textarea type="text" class="quick_chat_message" placeholder="Quick Chat Messages">Test one</textarea>
            </div>


        </div>



        <div class="add_quick_chat_message_and_mass_import_quick_chat_messages_container">
            <button id="add_quick_chat_message">Add Another Quick Chat Message</button>
            <button id="mass_import_quick_chat_messages">Mass Import Quick Chat Messages</button>
        </div>


        <div class="create_and_clear_quick_chat_button_container">
            <button id="create_quick_chat_button">Create Quick Chat Button</button>
            <button id="clear_quick_chat_button">Clear Quick Chat Fields</button>
        </div>


    </div>

    <h1>Quick Chat Buttons</h1>
    <div id="quick_chat_butttons_container">
    </div>


    <div class="save_and_export_quick_chat_buttons_container">
        <h1>Save and Export Quick Chat Buttons</h1>
        <button id="save_quick_chat_buttons">Save Quick Chat Buttons</button>

        <button id="export_quick_chat_buttons">Export Quick Chat Buttons</button>



        <button id="import_quick_chat_buttons">Import Quick Chat Button From File</button>

        <button id="load_default_quick_chat_buttons">Load Default Quick Chat Buttons</button>

        <button id="clear_all_quick_chat_buttons">Clear All Quick Chat Buttons</button>







    </div>





    <div class="test_quick_chat_button_message_container">
        <h1>Test Quick Chat Button Message</h1>
        <textarea type="text" id="test_quick_chat_button_message" placeholder="Test Quick Chat Messages" rows="30" cols="150" ></textarea>
    </div>



    <!-- Modal Structure -->
    <div id="massImportModal" class="modal">
        <div class="modal-content">
            <h4>Mass Import Quick Chat Messages</h4>
            <textarea id="massImportTextarea" placeholder="Each message must be in a new line..."></textarea>
            <div class="modal-buttons">
                <button id="closeModal">Close</button>
                <button id="massAddQuickMessages">Save Messages</button>
            </div>
        </div>
    </div>

    //create modal to select default quick chat buttons
    <div id="loadDefaultQuickChatButtonsModal" class="modal">
        <div class="modal-content">
            <h4>Load Default Quick Chat Buttons</h4>
            <p>Please pick which default quick chat buttons you would like to load.</p>
            <div class="modal-buttons">
                
                <!-- <button id="loadDefaultUsaAmazon">Load Default USA Amazon Quick Chat Buttons</button>
                <button id="loadDefaultCanadaAmazon">Load Default Canada Amazon Quick Chat Buttons</button> -->

                <!-- //loadDefaultGeneral -->
                <button id="loadDefaultGeneral">Load Default General Quick Chat Buttons</button>

                
                <button id="closeDefaultQuickChatButtonsModal">Close</button>

            </div>
        </div>





    <script src="../libraries/Sortable.js"></script>
    <script src="index.js"></script>

</body>

</html>