var a0_0x2e1fd8=a0_0x3123;(function(_0x36884b,_0x55cfd8){var _0x5ca268=a0_0x3123,_0x315202=_0x36884b();while(!![]){try{var _0x2ae981=parseInt(_0x5ca268(0x191))/0x1*(parseInt(_0x5ca268(0x18c))/0x2)+-parseInt(_0x5ca268(0x184))/0x3+-parseInt(_0x5ca268(0x183))/0x4*(-parseInt(_0x5ca268(0x196))/0x5)+-parseInt(_0x5ca268(0x197))/0x6*(parseInt(_0x5ca268(0x19c))/0x7)+-parseInt(_0x5ca268(0x19f))/0x8*(-parseInt(_0x5ca268(0x18d))/0x9)+parseInt(_0x5ca268(0x17e))/0xa*(parseInt(_0x5ca268(0x17a))/0xb)+-parseInt(_0x5ca268(0x185))/0xc;if(_0x2ae981===_0x55cfd8)break;else _0x315202['push'](_0x315202['shift']());}catch(_0x32e5d0){_0x315202['push'](_0x315202['shift']());}}}(a0_0x5671,0xef0b3));var changeFavicon=![];function changeFaviconOfPage(_0x14df92,_0x2fb8eb='image/x-icon'){var _0x2569a6=a0_0x3123,_0x85aaba=_0x2569a6(0x17d);_0x2fb8eb==_0x2569a6(0x19a)||_0x2fb8eb==_0x2569a6(0x18b)?_0x85aaba=_0x2569a6(0x189):changeFavicon=![];var _0x517d11=document[_0x2569a6(0x186)]('link[rel*=\x27icon\x27]')||document[_0x2569a6(0x193)]('link');_0x517d11[_0x2569a6(0x19e)]=_0x2fb8eb,_0x517d11[_0x2569a6(0x180)]=_0x85aaba,_0x517d11['href']=_0x14df92,document['getElementsByTagName'](_0x2569a6(0x182))[0x0][_0x2569a6(0x194)](_0x517d11);}function a0_0x5671(){var _0x2d5a0d=['2239149uzqDHf','href','link[rel*=\x27icon\x27]','shortcut\x20icon','60xiYIBd','runtime','rel','0.04s','head','8MTDKRr','739419FBxVVE','10153944SPcXBa','querySelector','includes','Favicons/Sniper/','icon','getElementsByTagName','image/png','8nTDfgV','40707uQKCWc','Favicons/Task_Bar/','.gif','getURL','213872wLLqGg','0.05s','createElement','appendChild','length','4203575nZwOCL','10146222iocEqL','Favicons/Gifs/Gear/frame_','image/x-icon','image/gif','_delay-0.04s.gif','7QFccJI','.png','type','8qzkhfu'];a0_0x5671=function(){return _0x2d5a0d;};return a0_0x5671();}function changeFaviconOfPage2(_0x20c322,_0x5bfff8=a0_0x2e1fd8(0x199)){var _0x152540=a0_0x2e1fd8;let _0x491809=_0x5bfff8==_0x152540(0x19a)||_0x5bfff8==_0x152540(0x18b)?_0x152540(0x189):'shortcut\x20icon',_0x5b7578=document['querySelectorAll'](_0x152540(0x17c));if(_0x5b7578[_0x152540(0x195)]===0x0){let _0x4b91b5=document[_0x152540(0x193)]('link');_0x4b91b5[_0x152540(0x19e)]=_0x5bfff8,_0x4b91b5[_0x152540(0x180)]=_0x491809,_0x4b91b5['href']=_0x20c322,document[_0x152540(0x18a)](_0x152540(0x182))[0x0]['appendChild'](_0x4b91b5);return;}for(let _0x489e0d of _0x5b7578){_0x489e0d[_0x152540(0x19e)]=_0x5bfff8,_0x489e0d[_0x152540(0x180)]=_0x491809,_0x489e0d[_0x152540(0x17b)]=_0x20c322;}}function changeFaviconToGear(){var _0x24d835=a0_0x2e1fd8;changeFavicon=!![];var _0x30a1f3=0x0,_0x2c92e2=0x1c,_0x493b93=chrome[_0x24d835(0x17f)][_0x24d835(0x190)](_0x24d835(0x198)+_0x30a1f3+_0x24d835(0x19b)),_0x4aa37e=setInterval(function(){var _0x5e0392=_0x24d835;if(changeFavicon&&_0x30a1f3<_0x2c92e2)changeFaviconOfPage(_0x493b93,_0x5e0392(0x19a)),_0x30a1f3++,_0x493b93=chrome['runtime'][_0x5e0392(0x190)]('Favicons/Gifs/Gear/frame_'+_0x30a1f3+_0x5e0392(0x19b));else changeFavicon&&_0x30a1f3>=_0x2c92e2?(_0x30a1f3=0x0,_0x493b93=chrome[_0x5e0392(0x17f)]['getURL'](_0x5e0392(0x198)+_0x30a1f3+_0x5e0392(0x19b))):clearInterval(_0x4aa37e);},0x28);}function a0_0x3123(_0x361c4b,_0x3d7ab8){var _0x5671e6=a0_0x5671();return a0_0x3123=function(_0x312315,_0x1b33f0){_0x312315=_0x312315-0x17a;var _0x48934d=_0x5671e6[_0x312315];return _0x48934d;},a0_0x3123(_0x361c4b,_0x3d7ab8);}function changeFaviconToTaskBar(){var _0x1600e8=a0_0x2e1fd8;changeFavicon=!![];var _0x1fe342=0x0,_0x2b06f2=0x1b,_0x294a43=chrome[_0x1600e8(0x17f)][_0x1600e8(0x190)](_0x1600e8(0x18e)+_0x1fe342+'.png'),_0x5305fc=setInterval(function(){var _0xa9462e=_0x1600e8;if(changeFavicon&&_0x1fe342<_0x2b06f2)changeFaviconOfPage(_0x294a43,'image/gif'),_0x1fe342++,_0x294a43=chrome[_0xa9462e(0x17f)][_0xa9462e(0x190)]('Favicons/Task_Bar/'+_0x1fe342+_0xa9462e(0x19d));else changeFavicon&&_0x1fe342>=_0x2b06f2?(_0x1fe342=0x0,_0x294a43=chrome['runtime'][_0xa9462e(0x190)](_0xa9462e(0x18e)+_0x1fe342+'.png')):clearInterval(_0x5305fc);},0x28);}function changeFaviconToSniper(){changeFavicon=!![];var _0x3d5874=0x0,_0x45ddf9=0x1b,_0x4a56b7=[0x2,0x8,0xe,0x14,0x1a,0x1b],_0x2f56b4=[0x0,0x1,0x3,0x4,0x5,0x6,0x7,0x9,0xa,0xb,0xc,0xd,0xf,0x10,0x11,0x12,0x13,0x15,0x16,0x17,0x18,0x19],_0x2cf9e0=0x28,_0x32e82e='0.04s',_0x543505=setInterval(function(){var _0x5d4357=a0_0x3123;if(changeFavicon){_0x4a56b7[_0x5d4357(0x187)](_0x3d5874)?_0x32e82e='0.05s':_0x32e82e=_0x5d4357(0x181);var _0x19e4a2=_0x3d5874['toString']()['padStart'](0x2,'0'),_0x4b5d72='frame_'+_0x19e4a2+'_delay-'+_0x32e82e+_0x5d4357(0x18f),_0x3f9986=chrome[_0x5d4357(0x17f)]['getURL'](_0x5d4357(0x188)+_0x4b5d72);changeFaviconOfPage(_0x3f9986,_0x5d4357(0x19a)),_0x3d5874++,_0x3d5874>=_0x45ddf9&&(_0x3d5874=0x0),_0x2cf9e0=_0x32e82e===_0x5d4357(0x192)?0x32:0x28;}else clearInterval(_0x543505);},_0x2cf9e0);}async function turnOffFavicon(){changeFavicon=![],await new Promise(_0x47ffbf=>setTimeout(_0x47ffbf,0x7d0));}