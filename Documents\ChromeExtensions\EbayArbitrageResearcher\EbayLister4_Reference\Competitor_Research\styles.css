/* General Styles */
body {
    font-family: 'Roboto', sans-serif;
    color: #333;
    margin: 0;
    padding: 20px;
  }
  
  h1.page-title {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2em;
  }
  
  /* Container Styles */
  .container {
    margin-bottom: 20px;
  }
  
  #usernames-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  #competitor-controls-container,
  #scan-info-container {
    flex: 1 1 45%;
    box-sizing: border-box;
  }
  
  #competitor-controls-container {
    margin-right: 5%;
  }
  
  #scan-info-container {
    margin-left: 5%;
  }
  
  #competitor-controls-container,
  #scan-info-container {
    min-width: 300px; /* Ensure minimum width for smaller screens */
  }
  
  /* Textarea */
  #competitorInput {
    width: 100%;
    height: 150px;
    padding: 10px;
    resize: vertical;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: inherit;
  }
  
  /* <PERSON><PERSON> Styles */
  #button-container {
    margin-top: 10px;
  }
  
  button {
    background-color: #3498db;
    color: #fff;
    border: none;
    padding: 10px 15px;
    margin: 5px 5px 5px 0;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
  }
  
  button:hover {
    background-color: #2980b9;
  }
  
  #showItemsButton {
    background-color: #2ecc71;
  }
  
  #showItemsButton:hover {
    background-color: #27ae60;
  }
  
  /* Scan Info Styles */
  #scan-info-container div {
    margin-bottom: 15px;
  }
  
  #scan-info-container label {
    display: inline-block;
    margin-right: 5px;
    font-weight: bold;
  }
  
  #scan-info-container input[type="number"] {
    width: 80px; /* Adjusted width for number input */
  }
  
  #scan-info-container span {
    font-weight: bold;
  }
  
  #currentUser {
    display: inline-block;
    margin-left: 5px;
  }
  
  /* Options and Links */
  #options-container {
    text-align: center;
  }
  
  #captcha-solver-link {
    display: block;
    text-align: center;
    margin-top: 20px;
    color: #e74c3c;
    font-weight: bold;
    text-decoration: none;
  }
  
  #captcha-solver-link:hover {
    text-decoration: underline;
  }
  
  /* Reset Animation Styles */
  #resetAnimationContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.5s ease;
    z-index: 999;
  }
  
  #resetAnimationContainer.show {
    opacity: 1;
    pointer-events: auto;
  }
  
  #resetAnimationContent {
    text-align: center;
    animation: fadeIn 0.5s ease forwards;
  }
  
  #resetIcon {
    animation: shake 0.5s ease-in-out;
    stroke: #e74c3c;
  }
  
  #resetAnimationContent p {
    font-size: 1.2em;
    color: #e74c3c;
    margin-top: 20px;
  }
  
  @keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-1deg); }
    20% { transform: translate(-3px, 0px) rotate(1deg); }
    30% { transform: translate(3px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(1deg); }
    50% { transform: translate(-1px, 2px) rotate(-1deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(3px, 1px) rotate(-1deg); }
    80% { transform: translate(-1px, -1px) rotate(1deg); }
    90% { transform: translate(1px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-1deg); }
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
  }
  
  /* Input and Dropdown Styles */
  input[type="number"],
  select {
    padding: 6px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1em;
  }
  
  input[type="number"] {
    width: 80px; /* Adjusted width for number input */
  }
  
  label {
    font-weight: bold;
  }
  
  /* Scan Options Container */
  #scan-options-container label {
    display: inline-block;
    margin-right: 10px;
    font-weight: bold;
  }
  
  #scan-options-container select {
    width: auto;
    display: inline-block;
    margin-right: 20px;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    #usernames-container {
      flex-direction: column;
    }
  
    #competitor-controls-container,
    #scan-info-container {
      flex: 1 1 100%;
      margin: 0;
    }
  
    #competitor-controls-container {
      margin-bottom: 20px;
    }
  }
  
  

button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
}