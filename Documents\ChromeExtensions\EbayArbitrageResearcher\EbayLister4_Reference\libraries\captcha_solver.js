const a0_0x50a1b3=a0_0x1af9;(function(_0x1af183,_0x167aa5){const _0x2f7d9b=a0_0x1af9,_0x1101aa=_0x1af183();while(!![]){try{const _0x1e8518=parseInt(_0x2f7d9b(0x1da))/0x1*(-parseInt(_0x2f7d9b(0x1f7))/0x2)+parseInt(_0x2f7d9b(0x1d3))/0x3+-parseInt(_0x2f7d9b(0x1dd))/0x4+parseInt(_0x2f7d9b(0x1eb))/0x5*(parseInt(_0x2f7d9b(0x1d0))/0x6)+-parseInt(_0x2f7d9b(0x1d9))/0x7*(-parseInt(_0x2f7d9b(0x1ea))/0x8)+parseInt(_0x2f7d9b(0x1d4))/0x9+-parseInt(_0x2f7d9b(0x1df))/0xa;if(_0x1e8518===_0x167aa5)break;else _0x1101aa['push'](_0x1101aa['shift']());}catch(_0x1af772){_0x1101aa['push'](_0x1101aa['shift']());}}}(a0_0x213c,0x8c8b2),console[a0_0x50a1b3(0x1e0)](a0_0x50a1b3(0x1f8)));function convertImageToBase64(_0x78f90d){const _0x83ea0e=a0_0x50a1b3;return console[_0x83ea0e(0x1e0)]('Converting\x20image\x20to\x20base64:\x20'+_0x78f90d),fetch(_0x78f90d)[_0x83ea0e(0x1f4)](_0xdcc76d=>_0xdcc76d[_0x83ea0e(0x1f0)]())[_0x83ea0e(0x1f4)](_0xc1624b=>new Promise((_0x33597d,_0x3559a5)=>{const _0xd5cbb1=_0x83ea0e,_0x49155e=new FileReader();_0x49155e[_0xd5cbb1(0x1e1)]=()=>_0x33597d(_0x49155e['result']),_0x49155e[_0xd5cbb1(0x1ed)]=_0x3559a5,_0x49155e[_0xd5cbb1(0x1f9)](_0xc1624b);}));}function getBase64CaptchaImage(_0x403898){const _0x231fac=a0_0x50a1b3;return convertImageToBase64(_0x403898)[_0x231fac(0x1f4)](_0x1403c5=>_0x1403c5[_0x231fac(0x1fa)](_0x231fac(0x1e9),''));}async function solveCaptcha(_0x3413d3){const _0x1ecd52=a0_0x50a1b3,_0x10038a='https://text-solver-service-708081384154.us-central1.run.app/v1/models/text_solver:predict',_0x2aeaf7=await getBase64CaptchaImage(_0x3413d3),_0x895dba={'signature_name':'serving_default','inputs':{'input':{'b64':_0x2aeaf7}}},_0x58ead5=await fetch(_0x10038a,{'method':_0x1ecd52(0x1f1),'body':JSON[_0x1ecd52(0x1d8)](_0x895dba),'headers':{'cache-control':_0x1ecd52(0x1f3),'content-type':_0x1ecd52(0x1db)}})[_0x1ecd52(0x1f4)](_0x34301d=>_0x34301d[_0x1ecd52(0x1d1)]())['then'](_0x1cb936=>_0x1cb936['outputs']['output']);return console[_0x1ecd52(0x1e0)](_0x1ecd52(0x1e5)+_0x58ead5),_0x58ead5;}function isCaptchaPage(){const _0x1c5563=a0_0x50a1b3,_0x12b300=document[_0x1c5563(0x1f2)](_0x1c5563(0x1ef));return document['title']===_0x1c5563(0x1fd)||_0x12b300[_0x1c5563(0x1ee)]>0x0;}function handleCaptcha(){const _0xa4ec96=a0_0x50a1b3;console['log']('Handling\x20Captcha'),console[_0xa4ec96(0x1e0)](_0xa4ec96(0x1e2));const _0x31a425=document[_0xa4ec96(0x1f2)](_0xa4ec96(0x1f5))[0x1][_0xa4ec96(0x1f2)](_0xa4ec96(0x1d7))[0x0][_0xa4ec96(0x1f6)](_0xa4ec96(0x1fb));chrome[_0xa4ec96(0x1d2)]['sendMessage']({'type':_0xa4ec96(0x1d6),'captchaImgUrl':_0x31a425},function(_0x5a356f){const _0x1e4c8b=_0xa4ec96;_0x5a356f[_0x1e4c8b(0x1e6)]==='solved_captcha'&&(console[_0x1e4c8b(0x1e0)](_0x1e4c8b(0x1e3)+_0x5a356f[_0x1e4c8b(0x1ec)]),document[_0x1e4c8b(0x1d5)](_0x1e4c8b(0x1e7))[_0x1e4c8b(0x1e8)]=_0x5a356f['captchaKey'],document[_0x1e4c8b(0x1f2)](_0x1e4c8b(0x1de))[0x0][_0x1e4c8b(0x1e4)]());});}async function solveCaptchaIfNecessary(){const _0x234e4c=a0_0x50a1b3;var _0x27b1f5=isCaptchaPage();console[_0x234e4c(0x1e0)](_0x234e4c(0x1dc),_0x27b1f5);if(_0x27b1f5){var _0x20c74f=document['querySelectorAll'](_0x234e4c(0x1f5))[0x1][_0x234e4c(0x1f2)](_0x234e4c(0x1d7))[0x0][_0x234e4c(0x1f6)](_0x234e4c(0x1fb)),_0x26e9fd=await new Promise((_0xdf1590,_0x239951)=>{const _0x2ea10e=_0x234e4c;chrome['runtime'][_0x2ea10e(0x1fc)]({'type':_0x2ea10e(0x1d6),'captchaImgUrl':_0x20c74f},function(_0x23c873){const _0x224fe0=_0x2ea10e;_0xdf1590(_0x23c873[_0x224fe0(0x1ec)]);});});console[_0x234e4c(0x1e0)](_0x234e4c(0x1ec),_0x26e9fd),document[_0x234e4c(0x1d5)](_0x234e4c(0x1e7))[_0x234e4c(0x1e8)]=_0x26e9fd,document[_0x234e4c(0x1f2)]('button[type=\x22submit\x22]')[0x0]['click']();return;}return;}function getCaptchaImage(){const _0x4df872=a0_0x50a1b3;var _0x441f45=document[_0x4df872(0x1f2)](_0x4df872(0x1f5))[0x1][_0x4df872(0x1f2)](_0x4df872(0x1d7))[0x0][_0x4df872(0x1f6)](_0x4df872(0x1fb));return _0x441f45;}async function solveForCaptchaKey(_0x15cfd0){var _0x5f150e=await new Promise((_0x4fdd92,_0xac3b80)=>{chrome['runtime']['sendMessage']({'type':'solve_captcha','captchaImgUrl':_0x15cfd0},function(_0x313fbe){_0x4fdd92(_0x313fbe['captchaKey']);});});return _0x5f150e;}function a0_0x1af9(_0x5d7c7d,_0x31bcb1){const _0x213cd3=a0_0x213c();return a0_0x1af9=function(_0x1af90d,_0x16b6ef){_0x1af90d=_0x1af90d-0x1d0;let _0x2968a6=_0x213cd3[_0x1af90d];return _0x2968a6;},a0_0x1af9(_0x5d7c7d,_0x31bcb1);}function instructBackgroundPageToWaitForSolvedCaptchaReloadAndListAgain(_0xad7b5){const _0x94c22b=a0_0x50a1b3;chrome[_0x94c22b(0x1d2)]['sendMessage']({'type':'wait_for_solved_captcha_reload','messageOptions':_0xad7b5});}function a0_0x213c(){const _0x46bc15=['replace','src','sendMessage','Robot\x20Check','1890MJxYCF','json','runtime','1528326eQWhKK','2936907oBsYrv','getElementById','solve_captcha','img','stringify','6744269XxkCyx','22201toKCJt','application/json','isCaptcha','2938208BpxvQW','button[type=\x22submit\x22]','4812090eoOikn','log','onloadend','Robot\x20Check\x20Doc\x20Found','code:\x20','click','Solved\x20Captcha:\x20','type','captchacharacters','value','data:image/jpeg;base64,','8aWKuVB','12210erUPpl','captchaKey','onerror','length','#captchacharacters','blob','POST','querySelectorAll','no-cache','then','.a-text-center.a-row','getAttribute','70lfIGLy','captcha_solver.js\x20loaded','readAsDataURL'];a0_0x213c=function(){return _0x46bc15;};return a0_0x213c();}function setCaptchaKey(_0x3e53fa){const _0xf04708=a0_0x50a1b3;document[_0xf04708(0x1d5)]('captchacharacters')['value']=_0x3e53fa;}function submitCaptcha(){const _0x992e6a=a0_0x50a1b3;document[_0x992e6a(0x1f2)](_0x992e6a(0x1de))[0x0][_0x992e6a(0x1e4)]();}async function solveCaptchaOnPage(){var _0x309d01=getCaptchaImage(),_0x355279=await solveForCaptchaKey(_0x309d01);setCaptchaKey(_0x355279),submitCaptcha();return;}