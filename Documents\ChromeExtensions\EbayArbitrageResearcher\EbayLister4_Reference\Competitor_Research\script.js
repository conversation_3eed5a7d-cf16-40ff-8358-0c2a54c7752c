var a0_0x213d0e=a0_0x1cce;(function(_0x41eadb,_0x26e048){var _0x20c022=a0_0x1cce,_0xff7c63=_0x41eadb();while(!![]){try{var _0x1ccfd7=parseInt(_0x20c022(0x172))/0x1+parseInt(_0x20c022(0x171))/0x2+-parseInt(_0x20c022(0x17e))/0x3+parseInt(_0x20c022(0x174))/0x4+-parseInt(_0x20c022(0x161))/0x5*(-parseInt(_0x20c022(0x14f))/0x6)+parseInt(_0x20c022(0x173))/0x7*(-parseInt(_0x20c022(0x18b))/0x8)+-parseInt(_0x20c022(0x153))/0x9*(parseInt(_0x20c022(0x151))/0xa);if(_0x1ccfd7===_0x26e048)break;else _0xff7c63['push'](_0xff7c63['shift']());}catch(_0x1d862c){_0xff7c63['push'](_0xff7c63['shift']());}}}(a0_0x148f,0xc6cfd));let ebayCompetitors=[],currentIndex=0x0,running=![];function a0_0x148f(){var _0x2855a4=['div','addEventListener','Off','split','research_competitor','getElementById','none','classList','textContent','stopButton','forEach','filter-dropdown','459064zPXvpA','592395nxPlez','13895uFzXfJ','4444404mPEsak','click','currentPosition','Fetch\x20Method\x20saved:\x20','competitorCount','selectedFetchMethod','createElement','local','Last\x207\x20Days','storage','2487990xIlyzc','label','join','class','resetAnimationContainer','filter-dropdown-div','showItemsButton','show','add','setAttribute','Select\x20Filter','Only\x20Scan\x20Items\x20That\x20Sold\x20within:\x20','runtime','1080gKYADg','select','get','terapeak-fetch-mode','remove','Concurrency\x20Limit\x20saved:\x20','Researching:\x20','toLowerCase','innerText','Last\x2014\x20Days','openSortEbayItems','appendChild','disabled','resetButton','set','competitor_position','value','concurrencyLimit','selectedFilter','default','filter-dropdown-class','6HGQZar','sendMessage','318730IpOjhr','change','387gJEEWF','competitorInput','length','Position\x20saved:\x20','for','findIndex','Last\x2060\x20Days','Scanning\x20Speed:\x20','log','filter','runButton','text','concurrency-limit-dropdown','concurrent-purchase-history','6745745cJEtxu','savedEbaySearchItems','Last\x2090\x20Days','scan-options-container'];a0_0x148f=function(){return _0x2855a4;};return a0_0x148f();}async function init(){var _0x435516=a0_0x1cce;const {ebayCompetitors:_0x5048b7}=await chrome['storage'][_0x435516(0x17b)]['get']('ebayCompetitors');_0x5048b7&&(document[_0x435516(0x16a)](_0x435516(0x154))[_0x435516(0x14a)]=_0x5048b7['join']('\x0a'));_0x5048b7&&(document['getElementById'](_0x435516(0x178))[_0x435516(0x16d)]=_0x5048b7[_0x435516(0x155)]);var _0x633c6a=document[_0x435516(0x16a)](_0x435516(0x164)),_0x575fc8=await createAndInitializeFilterDropdown(),_0x432127=await createConcurrencyLimitDropdown(),_0x46578b=await createFetchSoldDataDropDown();_0x633c6a[_0x435516(0x145)](_0x46578b),_0x633c6a['appendChild'](_0x575fc8),_0x633c6a[_0x435516(0x145)](_0x432127);}init(),document[a0_0x213d0e(0x16a)](a0_0x213d0e(0x15d))[a0_0x213d0e(0x166)]('click',async()=>{var _0x4a0826=a0_0x213d0e;running=!![],document[_0x4a0826(0x16a)](_0x4a0826(0x15d))[_0x4a0826(0x146)]=!![],document[_0x4a0826(0x16a)](_0x4a0826(0x16e))[_0x4a0826(0x146)]=![],await processCompetitors();}),document['getElementById']('stopButton')[a0_0x213d0e(0x166)](a0_0x213d0e(0x175),()=>{var _0x43ffc4=a0_0x213d0e;running=![],document[_0x43ffc4(0x16a)](_0x43ffc4(0x15d))[_0x43ffc4(0x146)]=![],document[_0x43ffc4(0x16a)](_0x43ffc4(0x16e))[_0x43ffc4(0x146)]=!![];}),document[a0_0x213d0e(0x16a)](a0_0x213d0e(0x147))['addEventListener'](a0_0x213d0e(0x175),()=>{var _0x10abf9=a0_0x213d0e;currentIndex=0x0,document[_0x10abf9(0x16a)](_0x10abf9(0x176))[_0x10abf9(0x14a)]=currentIndex+0x1,document[_0x10abf9(0x16a)]('currentUser')['textContent']='',chrome[_0x10abf9(0x17d)]['local'][_0x10abf9(0x148)]({'competitor_position':currentIndex}),chrome[_0x10abf9(0x17d)]['local'][_0x10abf9(0x13e)](_0x10abf9(0x162));const _0x44ea71=document[_0x10abf9(0x16a)](_0x10abf9(0x182));_0x44ea71[_0x10abf9(0x16c)][_0x10abf9(0x186)]('show'),setTimeout(()=>{var _0x86300=_0x10abf9;_0x44ea71[_0x86300(0x16c)][_0x86300(0x13e)](_0x86300(0x185));},0x7d0);});async function parseInput(){var _0x3f5c3d=a0_0x213d0e;const _0xdd2b16=document[_0x3f5c3d(0x16a)](_0x3f5c3d(0x154))[_0x3f5c3d(0x14a)];ebayCompetitors=_0xdd2b16[_0x3f5c3d(0x168)]('\x0a')[_0x3f5c3d(0x15c)](Boolean),ebayCompetitors=ebayCompetitors[_0x3f5c3d(0x15c)](_0x66fe98=>_0x66fe98['trim']()!==''),ebayCompetitors=ebayCompetitors[_0x3f5c3d(0x15c)]((_0x4c4b73,_0x56721d)=>ebayCompetitors[_0x3f5c3d(0x158)](_0x392ae4=>_0x392ae4['toLowerCase']()===_0x4c4b73[_0x3f5c3d(0x141)]())===_0x56721d),await chrome[_0x3f5c3d(0x17d)]['local'][_0x3f5c3d(0x148)]({'ebayCompetitors':ebayCompetitors}),document['getElementById'](_0x3f5c3d(0x154))[_0x3f5c3d(0x14a)]=ebayCompetitors[_0x3f5c3d(0x180)]('\x0a'),document[_0x3f5c3d(0x16a)](_0x3f5c3d(0x178))[_0x3f5c3d(0x16d)]=ebayCompetitors[_0x3f5c3d(0x155)];}document[a0_0x213d0e(0x16a)]('competitorInput')['addEventListener'](a0_0x213d0e(0x152),async()=>{await parseInput();});function a0_0x1cce(_0xa7fab8,_0x32a5f3){var _0x148f6f=a0_0x148f();return a0_0x1cce=function(_0x1cce9f,_0x5bb856){_0x1cce9f=_0x1cce9f-0x13e;var _0x3f8090=_0x148f6f[_0x1cce9f];return _0x3f8090;},a0_0x1cce(_0xa7fab8,_0x32a5f3);}async function processCompetitors(){var _0x280613=a0_0x213d0e;parseInput();for(;currentIndex<ebayCompetitors[_0x280613(0x155)]&&running;currentIndex++){document[_0x280613(0x16a)](_0x280613(0x176))[_0x280613(0x14a)]=currentIndex+0x1,document[_0x280613(0x16a)]('currentUser')[_0x280613(0x16d)]=ebayCompetitors[currentIndex],await researchCompetitor(ebayCompetitors[currentIndex]),savePosition();}}async function researchCompetitor(_0x4d36cb){var _0x344377=a0_0x213d0e;console[_0x344377(0x15b)](_0x344377(0x140)+_0x4d36cb),console['log']('Finished\x20researching:\x20'+_0x4d36cb);try{var {didResearch:_0x5d753e}=await new Promise(_0x4bd000=>{var _0xf9cd93=_0x344377;chrome[_0xf9cd93(0x18a)][_0xf9cd93(0x150)]({'type':_0xf9cd93(0x169),'username':_0x4d36cb},function(_0x2f69ab){_0x4bd000(_0x2f69ab);});});console['log'](_0x5d753e);}catch(_0x446ad4){console['log']('error',_0x446ad4);}}function savePosition(){var _0x3150f8=a0_0x213d0e;chrome[_0x3150f8(0x17d)][_0x3150f8(0x17b)][_0x3150f8(0x148)]({'competitor_position':currentIndex},function(){var _0x45e7dd=_0x3150f8;console['log'](_0x45e7dd(0x156)+currentIndex);});}window['onload']=function(){var _0x1ccc71=a0_0x213d0e;chrome['storage'][_0x1ccc71(0x17b)][_0x1ccc71(0x18d)]([_0x1ccc71(0x149)],function(_0x166d87){var _0x1f6d2d=_0x1ccc71;currentIndex=_0x166d87[_0x1f6d2d(0x149)]||0x0,document[_0x1f6d2d(0x16a)]('currentPosition')[_0x1f6d2d(0x14a)]=currentIndex+0x1;});},document[a0_0x213d0e(0x16a)](a0_0x213d0e(0x176))[a0_0x213d0e(0x166)](a0_0x213d0e(0x152),async()=>{var _0x5474ce=a0_0x213d0e;currentIndex=parseInt(document[_0x5474ce(0x16a)](_0x5474ce(0x176))[_0x5474ce(0x14a)])-0x1,document[_0x5474ce(0x16a)]('currentUser')['textContent']=ebayCompetitors[currentIndex],await savePosition();}),document[a0_0x213d0e(0x16a)](a0_0x213d0e(0x184))[a0_0x213d0e(0x166)](a0_0x213d0e(0x175),async()=>{var _0x5f517b=a0_0x213d0e;chrome[_0x5f517b(0x18a)][_0x5f517b(0x150)]({'type':_0x5f517b(0x144)});});async function createAndInitializeFilterDropdown(){var _0x1fd782=a0_0x213d0e,_0x2afe66=document['createElement'](_0x1fd782(0x18c));_0x2afe66['id']=_0x1fd782(0x170),_0x2afe66[_0x1fd782(0x187)](_0x1fd782(0x181),_0x1fd782(0x14e));var _0x5ba33d=[{'text':_0x1fd782(0x188),'value':_0x1fd782(0x14d)},{'text':'Last\x201\x20Day','value':'1'},{'text':'Last\x203\x20Days','value':'3'},{'text':_0x1fd782(0x17c),'value':'7'},{'text':_0x1fd782(0x143),'value':'14'},{'text':'Last\x2021\x20Days','value':'21'},{'text':'Last\x2030\x20Days','value':'30'},{'text':_0x1fd782(0x159),'value':'60'},{'text':_0x1fd782(0x163),'value':'90'}];_0x5ba33d['forEach'](_0x4ec291=>{var _0x58895a=_0x1fd782,_0xb9eca6=document[_0x58895a(0x17a)]('option');_0xb9eca6[_0x58895a(0x14a)]=_0x4ec291[_0x58895a(0x14a)],_0xb9eca6[_0x58895a(0x142)]=_0x4ec291[_0x58895a(0x15e)],_0x2afe66[_0x58895a(0x145)](_0xb9eca6);});let {selectedFilter:_0x3f8eda}=await chrome[_0x1fd782(0x17d)][_0x1fd782(0x17b)][_0x1fd782(0x18d)](_0x1fd782(0x14c));!_0x3f8eda&&(_0x3f8eda='3',await chrome[_0x1fd782(0x17d)][_0x1fd782(0x17b)][_0x1fd782(0x148)]({'selectedFilter':_0x3f8eda}));_0x2afe66[_0x1fd782(0x14a)]=_0x3f8eda,_0x2afe66[_0x1fd782(0x166)]('change',async function(){var _0x16071d=_0x1fd782;await chrome[_0x16071d(0x17d)][_0x16071d(0x17b)]['set']({'selectedFilter':this['value']}),console[_0x16071d(0x15b)]('Filter\x20saved:\x20'+this[_0x16071d(0x14a)]);});var _0x170cca=document[_0x1fd782(0x17a)](_0x1fd782(0x17f));_0x170cca['innerText']=_0x1fd782(0x189);var _0x4ab9d5=document[_0x1fd782(0x17a)]('div');return _0x4ab9d5[_0x1fd782(0x187)](_0x1fd782(0x181),_0x1fd782(0x183)),_0x4ab9d5['appendChild'](_0x170cca),_0x4ab9d5[_0x1fd782(0x145)](_0x2afe66),_0x4ab9d5;}async function createConcurrencyLimitDropdown(){var _0x360bfd=a0_0x213d0e,_0x585fb8=document['createElement'](_0x360bfd(0x18c));_0x585fb8['id']=_0x360bfd(0x15f),_0x585fb8[_0x360bfd(0x187)](_0x360bfd(0x181),_0x360bfd(0x14e));var _0x28f972=[{'text':'1','value':0x1},{'text':'3','value':0x3},{'text':'5','value':0x5},{'text':'10','value':0xa},{'text':'15','value':0xf},{'text':'20','value':0x14},{'text':'25','value':0x19},{'text':'30','value':0x1e},{'text':'40','value':0x28},{'text':'50','value':0x32}];_0x28f972[_0x360bfd(0x16f)](_0xaa0e63=>{var _0x239d45=_0x360bfd,_0x90533b=document[_0x239d45(0x17a)]('option');_0x90533b[_0x239d45(0x14a)]=_0xaa0e63[_0x239d45(0x14a)],_0x90533b[_0x239d45(0x142)]=_0xaa0e63[_0x239d45(0x15e)],_0x585fb8[_0x239d45(0x145)](_0x90533b);});var {concurrencyLimit:_0x53c874}=await chrome[_0x360bfd(0x17d)][_0x360bfd(0x17b)]['get']('concurrencyLimit');console[_0x360bfd(0x15b)](_0x360bfd(0x14b),_0x53c874);!_0x53c874&&(_0x53c874=0x5,chrome['storage'][_0x360bfd(0x17b)]['set']({'concurrencyLimit':_0x53c874}));_0x585fb8['value']=_0x53c874,_0x585fb8[_0x360bfd(0x166)](_0x360bfd(0x152),async function(){var _0x7823c2=_0x360bfd;await chrome[_0x7823c2(0x17d)][_0x7823c2(0x17b)][_0x7823c2(0x148)]({'concurrencyLimit':this[_0x7823c2(0x14a)]}),console[_0x7823c2(0x15b)](_0x7823c2(0x13f)+this[_0x7823c2(0x14a)]);});var _0x4d8510=document[_0x360bfd(0x17a)]('label');_0x4d8510[_0x360bfd(0x142)]=_0x360bfd(0x15a),_0x4d8510[_0x360bfd(0x187)](_0x360bfd(0x157),_0x360bfd(0x15f));var _0x1f082c=document['createElement'](_0x360bfd(0x165));return _0x1f082c[_0x360bfd(0x145)](_0x4d8510),_0x1f082c[_0x360bfd(0x145)](_0x585fb8),_0x1f082c;}async function createFetchSoldDataDropDown(){var _0x4a4f67=a0_0x213d0e,_0x3c3c12=document[_0x4a4f67(0x17a)](_0x4a4f67(0x18c));_0x3c3c12['setAttribute'](_0x4a4f67(0x181),_0x4a4f67(0x14e));var _0x19efbf=[{'text':_0x4a4f67(0x167),'value':_0x4a4f67(0x16b)},{'text':'On','value':_0x4a4f67(0x160)}];_0x19efbf[_0x4a4f67(0x16f)](_0x2e0c8f=>{var _0x1b43e3=_0x4a4f67,_0xbce183=document[_0x1b43e3(0x17a)]('option');_0xbce183[_0x1b43e3(0x14a)]=_0x2e0c8f[_0x1b43e3(0x14a)],_0xbce183[_0x1b43e3(0x142)]=_0x2e0c8f[_0x1b43e3(0x15e)],_0x3c3c12[_0x1b43e3(0x145)](_0xbce183);});let {selectedFetchMethod:_0x24c8c0}=await chrome[_0x4a4f67(0x17d)]['local'][_0x4a4f67(0x18d)](_0x4a4f67(0x179));!_0x24c8c0&&(_0x24c8c0=_0x4a4f67(0x16b),await chrome['storage'][_0x4a4f67(0x17b)][_0x4a4f67(0x148)]({'selectedFetchMethod':_0x24c8c0}));_0x3c3c12[_0x4a4f67(0x14a)]=_0x24c8c0,_0x3c3c12['addEventListener'](_0x4a4f67(0x152),async function(){var _0x52fb87=_0x4a4f67;await chrome[_0x52fb87(0x17d)]['local'][_0x52fb87(0x148)]({'selectedFetchMethod':this[_0x52fb87(0x14a)]}),console[_0x52fb87(0x15b)](_0x52fb87(0x177)+this[_0x52fb87(0x14a)]);});var _0x41fbd1=document[_0x4a4f67(0x17a)](_0x4a4f67(0x17f));_0x41fbd1[_0x4a4f67(0x142)]='Get\x20Total\x20Sold\x20History:\x20',_0x41fbd1[_0x4a4f67(0x187)]('for',_0x4a4f67(0x18e));var _0x1014c5=document[_0x4a4f67(0x17a)](_0x4a4f67(0x165));return _0x1014c5[_0x4a4f67(0x145)](_0x41fbd1),_0x1014c5[_0x4a4f67(0x145)](_0x3c3c12),_0x1014c5;}