.button-container {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-gap: 2px !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
    width: 95%; /* Adjust based on your design */
}


.quick_chat_button {
    display: none; /* Initially hide all buttons, controlled by JS */
    text-align: center;
    padding: 10px 0;
}

.quick_chat_button:active {
    box-shadow: 0 5px #666 !important;
    transform: translateY(4px) !important;
}

#leaveFeedback {
    background-color: #ac3e3e !important;
    color: greenyellow !important;
}

.button-container {
    padding-bottom: 20px !important;
    /* Optional: Adjustments for better visibility */
    background-color: rgba(255, 255, 255, 0.8) !important;
    border: 1px solid #ccc !important;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5) !important;
}

.navigation-button {
    width: 40px; /* Fixed width for nav buttons */
    height: 40px; /* Height of the buttons */
    margin-bottom: 5px; /* Space between two buttons */
    cursor: pointer; /* Pointer cursor on hover */
}

.nav-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 10px; /* Space between grid and nav buttons */
}

.button-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 columns grid */
    gap: 5px; /* space between buttons */
    width: calc(100% - 50px); /* Adjust width based on the container size minus navigation width */
}

.main-container {
    display: flex;
    align-items: start;
}