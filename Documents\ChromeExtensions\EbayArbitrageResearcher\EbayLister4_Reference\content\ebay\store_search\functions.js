var a0_0x3474c1=a0_0x3c37;(function(_0x562bda,_0x1a5ea2){var _0x4ca495=a0_0x3c37,_0xa7ca1e=_0x562bda();while(!![]){try{var _0x177134=-parseInt(_0x4ca495(0xd5))/0x1*(parseInt(_0x4ca495(0xa8))/0x2)+parseInt(_0x4ca495(0xca))/0x3*(-parseInt(_0x4ca495(0x107))/0x4)+parseInt(_0x4ca495(0x150))/0x5*(parseInt(_0x4ca495(0x156))/0x6)+parseInt(_0x4ca495(0x8b))/0x7*(parseInt(_0x4ca495(0xe2))/0x8)+-parseInt(_0x4ca495(0x97))/0x9*(parseInt(_0x4ca495(0xff))/0xa)+-parseInt(_0x4ca495(0x158))/0xb*(-parseInt(_0x4ca495(0xa7))/0xc)+-parseInt(_0x4ca495(0xce))/0xd;if(_0x177134===_0x1a5ea2)break;else _0xa7ca1e['push'](_0xa7ca1e['shift']());}catch(_0x389ea4){_0xa7ca1e['push'](_0xa7ca1e['shift']());}}}(a0_0x58fd,0x749c5),console[a0_0x3474c1(0x117)]('ebay/store_search/functions.js\x20loaded'));var rateLimitedReached=![],timeOutMs=0x1f4;function createExtractAllButton(){var _0x200383=a0_0x3474c1,_0x2ff98f=document[_0x200383(0x146)](_0x200383(0x14f));return _0x2ff98f[_0x200383(0x8e)](_0x200383(0xc8),_0x200383(0xf4)),_0x2ff98f['id']=_0x200383(0xf4),_0x2ff98f[_0x200383(0x100)]=_0x200383(0x13c),_0x2ff98f[_0x200383(0x13e)]('click',async function(_0x1fba46){var _0x4a3d89=_0x200383;_0x1fba46[_0x4a3d89(0x139)]();var {run_status_extract_titles:_0x2769b8}=await chrome[_0x4a3d89(0x118)]['local'][_0x4a3d89(0x10b)](_0x4a3d89(0x8f));if(_0x2769b8==undefined||_0x2769b8==![])_0x2769b8=!![],_0x2ff98f[_0x4a3d89(0x96)][_0x4a3d89(0x152)](_0x4a3d89(0xf4)),_0x2ff98f[_0x4a3d89(0x96)][_0x4a3d89(0x14a)](_0x4a3d89(0x124)),_0x2ff98f['innerHTML']=_0x4a3d89(0xd8);else _0x2769b8==!![]&&(_0x2ff98f[_0x4a3d89(0x96)]['remove']('btn-stop-extract-all'),_0x2ff98f[_0x4a3d89(0x96)][_0x4a3d89(0x14a)]('btn-extract-all'),_0x2ff98f['innerHTML']=_0x4a3d89(0x13c),_0x2769b8=![]);chrome[_0x4a3d89(0x118)]['local'][_0x4a3d89(0xdb)]({'run_status_extract_titles':_0x2769b8}),console[_0x4a3d89(0x117)](_0x4a3d89(0x8f),_0x2769b8),extractAllTitles();}),_0x2ff98f;}async function fetchSoldDataBasedOnSelection(){var _0x2caff6=a0_0x3474c1;try{var {selectedFetchMethod:_0x254b59}=await chrome['storage'][_0x2caff6(0x127)][_0x2caff6(0x10b)](_0x2caff6(0xbd));_0x254b59==_0x2caff6(0xcd)&&console['log']('No\x20fetch\x20method\x20selected.'),_0x254b59=='sequential'&&(console[_0x2caff6(0x117)](_0x2caff6(0x108)),await fetchSoldDataSequentially()),_0x254b59==_0x2caff6(0x92)&&(console[_0x2caff6(0x117)]('Fetching\x20data\x20concurrently.'),await fetchSoldDataConcurrently()),_0x254b59=='sequential-purchase-history'&&(console[_0x2caff6(0x117)](_0x2caff6(0xf7)),await fetchSoldDataSequentiallyViaPurchaseHistory()),_0x254b59==_0x2caff6(0x11a)&&(console[_0x2caff6(0x117)](_0x2caff6(0xb5)),await fetchSoldDataConcurrentlyViaPurchaseHistory());}catch(_0x4541ce){console[_0x2caff6(0x10e)]('An\x20error\x20occurred\x20while\x20fetching\x20sold\x20data:\x20',_0x4541ce);}}function createTitlesButton(){var _0x19f8ee=a0_0x3474c1,_0x3f8a7b=document['createElement']('button');return _0x3f8a7b[_0x19f8ee(0x8e)]('class',_0x19f8ee(0xfa)),_0x3f8a7b[_0x19f8ee(0x100)]='Clear\x20Titles',chrome[_0x19f8ee(0x118)][_0x19f8ee(0x127)][_0x19f8ee(0x10b)](_0x19f8ee(0x8c),function(_0x5d2cc1){var _0x5d2dcf=_0x19f8ee;_0x3f8a7b[_0x5d2dcf(0x100)]='Clear\x20Titles'+'\x20('+_0x5d2cc1[_0x5d2dcf(0x8c)][_0x5d2dcf(0xc6)]+')';}),_0x3f8a7b[_0x19f8ee(0x13e)](_0x19f8ee(0x12b),function(_0xe60593){var _0x5289ca=_0x19f8ee;chrome[_0x5289ca(0x118)][_0x5289ca(0x127)][_0x5289ca(0xdb)]({'savedEbaySearchItems':[]}),_0x3f8a7b['innerHTML']=_0x5289ca(0x155),setTimeout(function(){var _0x5100e4=_0x5289ca;_0x3f8a7b[_0x5100e4(0x100)]=_0x5100e4(0x102);},0xbb8);}),_0x3f8a7b;}async function extractAllTitles(){var _0x556cfe=a0_0x3474c1;console[_0x556cfe(0x117)](_0x556cfe(0x14c));var _0x116c50=!![];console['log'](_0x556cfe(0x8f),_0x116c50),document['title']=_0x556cfe(0x133);try{await fetchSoldDataBasedOnSelection();}catch(_0x484af0){console[_0x556cfe(0x117)](_0x556cfe(0x10e),_0x484af0);}try{sortByTotalSold();}catch(_0x4ff264){console['log'](_0x556cfe(0x10e),_0x4ff264);}var _0x277692=getItemsData();console[_0x556cfe(0x117)]('ebaySearchItems',_0x277692);var {savedEbaySearchItems:_0x1b395a}=await chrome[_0x556cfe(0x118)][_0x556cfe(0x127)]['get'](_0x556cfe(0x8c));_0x1b395a==undefined&&(_0x1b395a=[]);_0x1b395a=_0x1b395a[_0x556cfe(0x137)](_0x277692),_0x1b395a=_0x1b395a[_0x556cfe(0x130)]((_0x3c525b,_0x51e450,_0x299539)=>_0x299539['findIndex'](_0x2d376b=>_0x2d376b[_0x556cfe(0x89)]===_0x3c525b[_0x556cfe(0x89)])===_0x51e450),chrome[_0x556cfe(0x118)][_0x556cfe(0x127)][_0x556cfe(0xdb)]({'savedEbaySearchItems':_0x1b395a});var _0x7813f9=await doesNextPageExistFunc();await new Promise(_0x3930c4=>setTimeout(_0x3930c4,0xbb8));var {run_status_extract_titles:_0x116c50}=await chrome['storage']['local'][_0x556cfe(0x10b)](_0x556cfe(0x8f));console['log']('doesNextPageExist',_0x7813f9);var {scrapeAllPages:_0x56bad3}=await chrome[_0x556cfe(0x118)][_0x556cfe(0x127)][_0x556cfe(0x10b)](_0x556cfe(0x10f));if(_0x7813f9==!![]&&_0x116c50==!![]&&_0x56bad3==!![]){console['log'](_0x556cfe(0x8d));var _0x123637=document[_0x556cfe(0xa4)](_0x556cfe(0x104))[0x0];_0x123637[_0x556cfe(0x12b)]();}else{if(_0x7813f9==![]||_0x56bad3==![]){console[_0x556cfe(0x117)](_0x556cfe(0xe7));var _0x5ca54b=document['getElementById'](_0x556cfe(0xf4));_0x5ca54b[_0x556cfe(0x96)][_0x556cfe(0x152)](_0x556cfe(0x124)),_0x5ca54b[_0x556cfe(0x96)]['add'](_0x556cfe(0xf4)),_0x5ca54b[_0x556cfe(0x100)]=_0x556cfe(0xe4);var _0x329af4=document[_0x556cfe(0xa4)]('btn-clear-all-titles')[0x0];_0x329af4[_0x556cfe(0x100)]=_0x556cfe(0x102)+'\x20('+_0x1b395a[_0x556cfe(0xc6)]+')',document['title']='Completed\x20Extraction!\x20✔️',_0x116c50=![],chrome[_0x556cfe(0x118)][_0x556cfe(0x127)]['set']({'run_status_extract_titles':_0x116c50}),await new Promise(_0x494dcc=>setTimeout(_0x494dcc,0xbb8)),_0x5ca54b[_0x556cfe(0x100)]=_0x556cfe(0x13c);}}}function filterEbaySearchItemsByDate(_0xe8d7dc,_0x382520){var _0x3da3f2=a0_0x3474c1;if(_0x382520=='default')return _0xe8d7dc;var _0x43a359=new Date(),_0xe7c450=new Date();_0xe7c450[_0x3da3f2(0xf0)](_0xe7c450[_0x3da3f2(0x87)]()-_0x382520);var _0x803fbc=_0xe8d7dc[_0x3da3f2(0x130)](function(_0x5254d1){var _0x164364=_0x3da3f2,_0x2b764c=new Date(_0x5254d1[_0x164364(0xed)]);return _0x2b764c>=_0xe7c450&&_0x2b764c<=_0x43a359;});return _0x803fbc;}function doesNextPageExistFunc(){return new Promise((_0x13487e,_0x5a1746)=>{var _0x46d4fa=a0_0x3c37,_0x49c8e6=document['getElementsByClassName']('pagination__next')[0x0];_0x49c8e6==undefined&&_0x13487e(![]);var _0x4a6632=_0x49c8e6[_0x46d4fa(0xf1)](_0x46d4fa(0x11e));_0x4a6632==_0x46d4fa(0x151)?_0x13487e(![]):_0x13487e(!![]);});}function createShowAllTitlesButton(){var _0x671a63=a0_0x3474c1,_0x539fc6=document[_0x671a63(0x146)](_0x671a63(0x14f));return _0x539fc6[_0x671a63(0x8e)]('class',_0x671a63(0xc2)),_0x539fc6[_0x671a63(0x100)]=_0x671a63(0x101),_0x539fc6[_0x671a63(0x13e)](_0x671a63(0x12b),function(_0x5c6ae9){var _0x1dcaaf=_0x671a63;chrome[_0x1dcaaf(0x9c)]['sendMessage']({'type':_0x1dcaaf(0xe8)});}),_0x539fc6;}async function viewSavedEbaySearchItems(){var _0x5246af=a0_0x3474c1,{savedEbaySearchItems:_0x616ce4}=await chrome[_0x5246af(0x118)][_0x5246af(0x127)][_0x5246af(0x10b)](_0x5246af(0x8c)),_0x5c8a6e=[];for(var _0x48039c=0x0;_0x48039c<_0x616ce4[_0x5246af(0xc6)];_0x48039c++){_0x5c8a6e[_0x5246af(0x114)](_0x616ce4[_0x48039c]['title']);}console[_0x5246af(0x117)](_0x5c8a6e),_0x5c8a6e=_0x5c8a6e[_0x5246af(0xbc)]('\x0a'),createModal(_0x5c8a6e),removeModal(),copyKeywords(_0x5c8a6e);}function createElement(_0x9e751f,_0x24f951,_0x4a96c3=''){var _0x27f41e=a0_0x3474c1;const _0x1d7406=document[_0x27f41e(0x146)](_0x9e751f);return _0x1d7406['id']=_0x24f951,_0x1d7406[_0x27f41e(0x100)]=_0x4a96c3,_0x1d7406;}function appendChildren(_0x56485b,_0x5017e5){var _0x4772e0=a0_0x3474c1;_0x5017e5[_0x4772e0(0xc5)](_0x4cc454=>_0x56485b['appendChild'](_0x4cc454));}function createModal(_0x78177c){var _0x5b4271=a0_0x3474c1;const _0x4c04b8=createElement('div','keyword_modal'),_0xa0f0df=createElement(_0x5b4271(0xe1),_0x5b4271(0x14b)),_0x163399=createElement('div',_0x5b4271(0xbe)),_0x54066c=createElement(_0x5b4271(0xe1),_0x5b4271(0x12e)),_0x4e985b=createElement('div',_0x5b4271(0xf6)),_0x39057e=createElement(_0x5b4271(0xb1),'keyword_modal_close',_0x5b4271(0xab));_0x163399['appendChild'](_0x39057e);const _0x11f3f7=_0x78177c['split']('\x0a')['length'],_0x337549=createElement('p','keyword_info',_0x5b4271(0x82)+_0x11f3f7+_0x5b4271(0xa1));_0x54066c[_0x5b4271(0x147)](_0x337549);const _0x37fc04=createElement('textarea',_0x5b4271(0x115));_0x37fc04[_0x5b4271(0x98)]=_0x78177c,_0x37fc04[_0x5b4271(0xf3)][_0x5b4271(0xf8)]=_0x5b4271(0x123),_0x54066c['appendChild'](_0x37fc04);const _0x4fe24c=createElement(_0x5b4271(0x14f),'copy_keywords_button',_0x5b4271(0xb2));_0x4fe24c[_0x5b4271(0xf3)][_0x5b4271(0x12d)]=_0x5b4271(0xfc),_0x4fe24c[_0x5b4271(0xf3)][_0x5b4271(0x14d)]=_0x5b4271(0xae),_0x4e985b['appendChild'](_0x4fe24c),appendChildren(_0xa0f0df,[_0x163399,_0x54066c,_0x4e985b]),_0x4c04b8[_0x5b4271(0x147)](_0xa0f0df),document[_0x5b4271(0x11d)]['appendChild'](_0x4c04b8),_0x4c04b8['style'][_0x5b4271(0x12d)]=_0x5b4271(0xfc);}function removeModal(){var _0x25649c=a0_0x3474c1;const _0x123006=document[_0x25649c(0x111)](_0x25649c(0xef));_0x123006['addEventListener'](_0x25649c(0x12b),function(){var _0x3a1efc=_0x25649c;document[_0x3a1efc(0x111)]('keyword_modal')[_0x3a1efc(0x152)]();});}function copyKeywords(_0x4e7ff8){var _0x3a8d6e=a0_0x3474c1;const _0x19d63e=document[_0x3a8d6e(0x111)](_0x3a8d6e(0x13d));_0x19d63e[_0x3a8d6e(0x13e)]('click',function(){var _0x301e82=_0x3a8d6e;navigator[_0x301e82(0x113)][_0x301e82(0xa3)](_0x4e7ff8),_0x19d63e[_0x301e82(0x100)]='Copied!';});}async function createAndInitializeFilterDropdown(){var _0x527bb5=a0_0x3474c1,_0x373099=document[_0x527bb5(0x146)](_0x527bb5(0xa5));_0x373099['id']='filter-dropdown',_0x373099[_0x527bb5(0x8e)]('class',_0x527bb5(0xd6));var _0x1820e1=[{'text':_0x527bb5(0xf9),'value':'default'},{'text':'Last\x201\x20Day','value':'1'},{'text':_0x527bb5(0xee),'value':'3'},{'text':_0x527bb5(0x105),'value':'7'},{'text':_0x527bb5(0xbb),'value':'14'},{'text':_0x527bb5(0x11b),'value':'21'},{'text':'Last\x2030\x20Days','value':'30'},{'text':_0x527bb5(0xc0),'value':'60'},{'text':_0x527bb5(0xb0),'value':'90'}];_0x1820e1['forEach'](_0x28b78b=>{var _0x378b47=_0x527bb5,_0xa161e1=document[_0x378b47(0x146)]('option');_0xa161e1[_0x378b47(0x98)]=_0x28b78b[_0x378b47(0x98)],_0xa161e1['innerText']=_0x28b78b[_0x378b47(0x11f)],_0x373099[_0x378b47(0x147)](_0xa161e1);});let {selectedFilter:_0x2d3633}=await chrome[_0x527bb5(0x118)]['local'][_0x527bb5(0x10b)]('selectedFilter');!_0x2d3633&&(_0x2d3633='90',await chrome[_0x527bb5(0x118)][_0x527bb5(0x127)]['set']({'selectedFilter':_0x2d3633}));_0x373099[_0x527bb5(0x98)]=_0x2d3633,_0x373099[_0x527bb5(0x13e)]('change',async function(){var _0x11577e=_0x527bb5;await chrome[_0x11577e(0x118)][_0x11577e(0x127)][_0x11577e(0xdb)]({'selectedFilter':this[_0x11577e(0x98)]}),console[_0x11577e(0x117)]('Filter\x20saved:\x20'+this['value']);});var _0x1b7ec4=document[_0x527bb5(0x146)](_0x527bb5(0xc7));_0x1b7ec4[_0x527bb5(0x9e)]=_0x527bb5(0xa0);var _0xa9df3e=document[_0x527bb5(0x146)](_0x527bb5(0xe1));return _0xa9df3e['setAttribute']('class',_0x527bb5(0xeb)),_0xa9df3e['id']=_0x527bb5(0x91),_0xa9df3e['appendChild'](_0x1b7ec4),_0xa9df3e[_0x527bb5(0x147)](_0x373099),_0xa9df3e;}function createTerapeakSearchButton(){var _0x3824c5=a0_0x3474c1,_0xb11045=document[_0x3824c5(0x146)]('button');return _0xb11045[_0x3824c5(0x8e)](_0x3824c5(0xc8),_0x3824c5(0x11c)),_0xb11045['innerHTML']='Find\x20Total\x20Sold\x20&\x20Competitors',_0xb11045[_0x3824c5(0xf3)]['display']='none',_0xb11045[_0x3824c5(0x13e)](_0x3824c5(0x12b),async function(_0x56e73e){var _0x3456ed=_0x3824c5;_0x56e73e[_0x3456ed(0x139)]();let _0x55c824=_0x56e73e[_0x3456ed(0xec)];while(_0x55c824&&!_0x55c824[_0x3456ed(0x96)]['contains'](_0x3456ed(0x109))){_0x55c824=_0x55c824['parentElement'];}if(!_0x55c824)return;console['log'](_0x3456ed(0x12c),_0x55c824),await searchTitleOnTeraPeakAndAddToItem(_0x55c824),removeOverlay();}),_0xb11045;}async function testfunc(){var _0x1982a6=a0_0x3474c1,_0x3c441f=document['querySelector'](_0x1982a6(0x116));}async function checkPurchaseHistory(_0x1e7daa,_0x3a06a){var _0x5ab568=a0_0x3474c1,_0x3a2690=_0x1e7daa[_0x5ab568(0x120)]('.s-item__itemID')[_0x5ab568(0x99)];_0x3a2690=_0x3a2690[_0x5ab568(0x119)]('Item:\x20')[0x1],_0x3a2690=_0x3a2690['replace'](/\s/g,''),console[_0x5ab568(0x117)]('itemNumber',_0x3a2690);var _0x444509=await chrome[_0x5ab568(0x9c)]['sendMessage']({'type':_0x5ab568(0x143),'itemNumber':_0x3a2690,'lastXDays':_0x3a06a});console[_0x5ab568(0x117)](_0x5ab568(0x153),_0x444509);var _0x2fd118=_0x444509[_0x5ab568(0x10a)];return _0x2fd118;}async function checkPurchaseHistoryAndAddToItem(_0x5cd7e8,_0xb94f2e,_0x1fe917=!![],_0x1ef688=![]){var _0x1fbc77=a0_0x3474c1;console[_0x1fbc77(0x117)]('item',_0x5cd7e8);var _0x4288e0;try{var _0x41ebc1=_0x5cd7e8[_0x1fbc77(0x120)](_0x1fbc77(0x145));!_0x41ebc1&&(_0x41ebc1=_0x5cd7e8[_0x1fbc77(0x120)](_0x1fbc77(0x157)),_0x41ebc1=_0x41ebc1['querySelectorAll'](_0x1fbc77(0x94)),_0x41ebc1=_0x41ebc1[_0x41ebc1[_0x1fbc77(0xc6)]-0x1]),_0x4288e0=_0x41ebc1['textContent'];}catch(_0x295817){console['log']('error',_0x295817);return;}console['log']('itemNumber',_0x4288e0),_0x4288e0=_0x4288e0[_0x1fbc77(0x119)](':')[0x1],console[_0x1fbc77(0x117)](_0x1fbc77(0x81),_0x4288e0),_0x4288e0=_0x4288e0[_0x1fbc77(0x7f)](/\s/g,'');var _0x325bf6=getTitleTag(_0x5cd7e8);console['log']('soldDateElement',_0x325bf6);var _0x20cbdd;_0x325bf6!==null?(_0x20cbdd=_0x325bf6[_0x1fbc77(0x99)],console[_0x1fbc77(0x117)]('soldDate',_0x20cbdd),_0x20cbdd=_0x20cbdd[_0x1fbc77(0x119)]('\x20')[_0x1fbc77(0xdd)](0x1)[_0x1fbc77(0xbc)]('\x20'),_0x20cbdd=_0x20cbdd[_0x1fbc77(0x126)]()):_0x20cbdd=null;console['log'](_0x1fbc77(0xc9),_0x20cbdd),_0x20cbdd=parseDateSimple(_0x20cbdd),console[_0x1fbc77(0x117)](_0x1fbc77(0x12f),_0x20cbdd);var _0x240a10=new Date(_0x20cbdd);console[_0x1fbc77(0x117)](_0x1fbc77(0xed),_0x240a10);var _0x1e34c7=new Date(),_0x40231c=_0x1e34c7-_0x240a10,_0x4ff262=_0x40231c/(0x3e8*0xe10*0x18);console[_0x1fbc77(0x117)](_0x1fbc77(0xda),_0x4ff262);var _0x3ac802=0x0;if(_0x4ff262>_0xb94f2e&&!_0x1ef688)console['log'](_0x1fbc77(0xa6));else try{var _0x307b91=await chrome['runtime']['sendMessage']({'type':'checkPurchaseHistory','itemNumber':_0x4288e0,'lastXDays':_0xb94f2e,'closeTabAfterSearch':_0x1fe917});console[_0x1fbc77(0x117)](_0x1fbc77(0x153),_0x307b91),_0x3ac802=_0x307b91[_0x1fbc77(0x10a)];}catch(_0x4da906){console[_0x1fbc77(0x117)](_0x1fbc77(0x10e),_0x4da906),_0x3ac802=-0x3e7;}var _0x8db2b3={0x1:_0x3ac802[_0x1fbc77(0x125)],0x3:_0x3ac802[_0x1fbc77(0x10c)],0x7:_0x3ac802['totalSoldIn7'],0xe:_0x3ac802[_0x1fbc77(0x8a)],0x1e:_0x3ac802['totalSoldIn30'],0x3c:_0x3ac802[_0x1fbc77(0xde)],0x5a:_0x3ac802[_0x1fbc77(0x122)]};const _0x2f4c08=[0x1,0x3,0x7,0xe,0x1e,0x3c,0x5a];let _0x414edf=!![];for(let _0x58a994 of _0x2f4c08){if(_0x8db2b3[_0x58a994]!==undefined&&_0x8db2b3[_0x58a994]!==_0x1fbc77(0xa2)){_0x414edf=![];break;}}var _0x3ac802=document[_0x1fbc77(0x146)]('p');if(_0x414edf)_0x3ac802[_0x1fbc77(0x100)]='<strong>Total\x20Sold:</strong>\x20Not\x20Available',_0x5cd7e8['appendChild'](_0x3ac802);else{var _0x2a2273=document[_0x1fbc77(0x146)](_0x1fbc77(0xb9));_0x2a2273[_0x1fbc77(0xd2)]=_0x1fbc77(0x9b);var _0x58cadb=document[_0x1fbc77(0x146)]('tr');_0x58cadb[_0x1fbc77(0x100)]=_0x1fbc77(0x95),_0x2a2273[_0x1fbc77(0x147)](_0x58cadb),_0x2f4c08[_0x1fbc77(0xc5)](_0x1cf92f=>{var _0x4147f3=_0x1fbc77,_0x5367c2=document[_0x4147f3(0x146)]('tr');_0x5367c2[_0x4147f3(0x8e)](_0x4147f3(0xc8),'total-sold-element'),_0x5367c2['setAttribute'](_0x4147f3(0xd3),_0x1cf92f),_0x5367c2['setAttribute'](_0x4147f3(0x93),_0x8db2b3[_0x1cf92f]!==_0x4147f3(0xa2)?_0x8db2b3[_0x1cf92f]:'Not\x20Available'),_0x5367c2[_0x4147f3(0x100)]=_0x4147f3(0x7a)+_0x1cf92f+_0x4147f3(0x13b)+(_0x8db2b3[_0x1cf92f]!=='undefined'?_0x8db2b3[_0x1cf92f]:_0x4147f3(0x121))+_0x4147f3(0xd4),_0x2a2273[_0x4147f3(0x147)](_0x5367c2);}),_0x5cd7e8['appendChild'](_0x2a2273);}setTimeout(()=>{var _0x31c01a=_0x1fbc77;try{_0x2a2273[_0x31c01a(0x96)]['add'](_0x31c01a(0x142));}catch(_0x1d5bc6){console[_0x31c01a(0x117)](_0x31c01a(0x10e),_0x1d5bc6);}try{var _0x2c047d=_0x2a2273['querySelectorAll']('tr');_0x2c047d[_0x31c01a(0xc5)](function(_0x4ed874){var _0x2a1329=_0x31c01a;_0x4ed874[_0x2a1329(0x96)]['add'](_0x2a1329(0x142));});}catch(_0x2c2736){console[_0x31c01a(0x117)](_0x31c01a(0x10e),_0x2c2736);}try{_0x3ac802[_0x31c01a(0x96)][_0x31c01a(0x14a)]('show-element');}catch(_0x5c31e8){console['log'](_0x31c01a(0x10e),_0x5c31e8);}},0x0);}function createTotalSoldElement(_0x45ab92,_0x4eebfd){var _0x156c97=a0_0x3474c1,_0x31177d=document[_0x156c97(0x146)]('div');return _0x31177d[_0x156c97(0x8e)]('class','total-sold-element'),_0x31177d[_0x156c97(0x8e)](_0x156c97(0x93),_0x45ab92),_0x31177d[_0x156c97(0x8e)](_0x156c97(0xd3),_0x4eebfd),_0x45ab92==-0x3e7?_0x31177d[_0x156c97(0x100)]=_0x156c97(0xf5):_0x31177d[_0x156c97(0x100)]=_0x156c97(0x134)+_0x45ab92+_0x156c97(0x128)+_0x4eebfd+_0x156c97(0x138),_0x31177d;}async function searchTitleOnTeraPeakAndAddToItem(_0x23f7cf){var _0x1d68b0=a0_0x3474c1,_0x4b5602=_0x23f7cf[_0x1d68b0(0x120)](_0x1d68b0(0xe6))[_0x1d68b0(0x99)];if(_0x4b5602==''||_0x4b5602==undefined||_0x4b5602==null||_0x4b5602==_0x1d68b0(0x149))return;console['log'](_0x1d68b0(0xbf),_0x4b5602);var _0x42800a=await searchTitleOnTeraPeak(_0x4b5602),_0x965a93=_0x42800a[_0x1d68b0(0x10a)],_0x1f0bbd=_0x42800a[_0x1d68b0(0xb6)],_0x58a7a9=document[_0x1d68b0(0x146)](_0x1d68b0(0xe1));_0x58a7a9[_0x1d68b0(0x8e)](_0x1d68b0(0xc8),_0x1d68b0(0xea)),_0x58a7a9[_0x1d68b0(0x8e)](_0x1d68b0(0x93),_0x965a93),_0x58a7a9[_0x1d68b0(0x100)]=_0x1d68b0(0xaa)+_0x965a93,_0x23f7cf[_0x1d68b0(0x147)](_0x58a7a9);var _0x4f4954=document[_0x1d68b0(0x146)](_0x1d68b0(0xe1));_0x4f4954[_0x1d68b0(0x8e)](_0x1d68b0(0xc8),'total-competitors-element'),_0x4f4954[_0x1d68b0(0x8e)]('data-total-competitors',_0x1f0bbd),_0x4f4954[_0x1d68b0(0x100)]='Total\x20Competitors:\x20'+_0x1f0bbd,_0x23f7cf[_0x1d68b0(0x147)](_0x4f4954),setTimeout(()=>{var _0x54fe46=_0x1d68b0;_0x58a7a9[_0x54fe46(0x96)][_0x54fe46(0x14a)](_0x54fe46(0x142)),_0x4f4954[_0x54fe46(0x96)][_0x54fe46(0x14a)](_0x54fe46(0x142));},0x0);}async function searchTitleOnTeraPeak(_0x384341){var _0x1da06e=a0_0x3474c1;if(rateLimitedReached===!![])return addOverlay(),timeOutMs=0x32,{'totalSold':_0x1da06e(0x9d),'totalCompetitors':_0x1da06e(0x9d)};var _0x4582ed=await chrome[_0x1da06e(0x9c)][_0x1da06e(0x7e)]({'type':_0x1da06e(0x83),'title':_0x384341});console['log'](_0x1da06e(0x153),_0x4582ed);try{var _0x2969ea=_0x4582ed['response'][_0x1da06e(0xfb)];console['log']('soldData',_0x2969ea),removeOverlay();}catch(_0x51d838){return console[_0x1da06e(0x117)]('error',_0x51d838),rateLimitedReached=!![],addOverlay(),timeOutMs=0x32,{'totalSold':_0x1da06e(0x9d),'totalCompetitors':_0x1da06e(0x9d)};}return _0x2969ea;}function addOverlay(){var _0x112cd7=a0_0x3474c1;if(document[_0x112cd7(0x120)](_0x112cd7(0x7c)))return;const _0x359fb2=document[_0x112cd7(0x146)](_0x112cd7(0xe1));_0x359fb2[_0x112cd7(0x96)]['add'](_0x112cd7(0x7b));const _0x33fe5b=document['createElement'](_0x112cd7(0xe1));_0x33fe5b['classList'][_0x112cd7(0x14a)](_0x112cd7(0x11f)),_0x33fe5b[_0x112cd7(0x99)]=_0x112cd7(0x110),_0x359fb2[_0x112cd7(0x147)](_0x33fe5b);var _0xbca774=document[_0x112cd7(0x146)]('button');_0xbca774['setAttribute'](_0x112cd7(0xc8),_0x112cd7(0x124)),_0xbca774[_0x112cd7(0x100)]=_0x112cd7(0xd8),_0xbca774[_0x112cd7(0x13e)](_0x112cd7(0x12b),function(_0x5bf5f9){var _0x4fb8b9=_0x112cd7;_0x5bf5f9[_0x4fb8b9(0x139)]();var _0x75fe61=document[_0x4fb8b9(0x111)]('btn-extract-all');_0x75fe61['classList'][_0x4fb8b9(0x152)]('btn-stop-extract-all'),_0x75fe61['classList'][_0x4fb8b9(0x14a)](_0x4fb8b9(0xf4)),_0x75fe61[_0x4fb8b9(0x100)]=_0x4fb8b9(0x13c),run_status_extract_titles=![],chrome[_0x4fb8b9(0x118)][_0x4fb8b9(0x127)][_0x4fb8b9(0xdb)]({'run_status_extract_titles':run_status_extract_titles}),_0xbca774['innerHTML']=_0x4fb8b9(0xac);}),_0x359fb2[_0x112cd7(0x147)](_0xbca774),document[_0x112cd7(0x11d)][_0x112cd7(0x147)](_0x359fb2),setTimeout(()=>{var _0x3b5ac7=_0x112cd7;_0x359fb2['style'][_0x3b5ac7(0x12d)]=_0x3b5ac7(0xb4);},0x0);}function removeOverlay(){var _0x4a6d67=a0_0x3474c1;const _0x6ef49e=document[_0x4a6d67(0x120)]('.overlay');_0x6ef49e&&(_0x6ef49e[_0x4a6d67(0xf3)]['animation']=_0x4a6d67(0xb7),setTimeout(()=>{_0x6ef49e['remove']();},0x1f4));}async function createTotalSoldFilterInput(){var _0x1b8759=a0_0x3474c1,_0x39badd=document[_0x1b8759(0x146)]('label');_0x39badd['setAttribute'](_0x1b8759(0xc8),'filter-label');var _0x38e35d=document[_0x1b8759(0x146)](_0x1b8759(0xb1));_0x38e35d[_0x1b8759(0x100)]='Total\x20Sold\x20Filter:\x20',_0x39badd[_0x1b8759(0x147)](_0x38e35d);var _0x3556e4=document['createElement'](_0x1b8759(0xf2));_0x3556e4['type']=_0x1b8759(0xb3),_0x3556e4['id']=_0x1b8759(0x136),_0x3556e4[_0x1b8759(0xe5)]='0',_0x3556e4['setAttribute'](_0x1b8759(0xc8),'filter-input');let {totalSoldFilter:_0x22b70a}=await chrome[_0x1b8759(0x118)][_0x1b8759(0x127)][_0x1b8759(0x10b)](_0x1b8759(0x129));return!_0x22b70a&&(_0x22b70a=0x0,await chrome['storage'][_0x1b8759(0x127)][_0x1b8759(0xdb)]({'totalSoldFilter':_0x22b70a})),_0x3556e4[_0x1b8759(0x98)]=_0x22b70a,_0x3556e4['addEventListener']('input',async function(){var _0x2d1f3f=_0x1b8759;this[_0x2d1f3f(0x98)]>=0x0&&(await chrome[_0x2d1f3f(0x118)][_0x2d1f3f(0x127)][_0x2d1f3f(0xdb)]({'totalSoldFilter':this['value']}),console[_0x2d1f3f(0x117)](_0x2d1f3f(0x13a)+this[_0x2d1f3f(0x98)]));}),_0x39badd[_0x1b8759(0x147)](_0x3556e4),_0x39badd;}function a0_0x3c37(_0x152af3,_0x470629){var _0x58fd67=a0_0x58fd();return a0_0x3c37=function(_0x3c37ba,_0xda6c08){_0x3c37ba=_0x3c37ba-0x7a;var _0x5bd7c1=_0x58fd67[_0x3c37ba];return _0x5bd7c1;},a0_0x3c37(_0x152af3,_0x470629);}async function createTotalCompetitorsFilterInput(){var _0x345e67=a0_0x3474c1,_0x153d5c=document['createElement'](_0x345e67(0xc7));_0x153d5c[_0x345e67(0x8e)](_0x345e67(0xc8),_0x345e67(0xb8));var _0x2392e4=document['createElement'](_0x345e67(0xb1));_0x2392e4[_0x345e67(0x100)]=_0x345e67(0x154),_0x153d5c[_0x345e67(0x147)](_0x2392e4);var _0x5a4eb8=document[_0x345e67(0x146)](_0x345e67(0xf2));_0x5a4eb8[_0x345e67(0x14e)]=_0x345e67(0xb3),_0x5a4eb8['id']=_0x345e67(0xcc),_0x5a4eb8['min']='0',_0x5a4eb8[_0x345e67(0x8e)]('class',_0x345e67(0xd0));let {totalCompetitorsFilter:_0x1dd4f4}=await chrome['storage'][_0x345e67(0x127)]['get']('totalCompetitorsFilter');return!_0x1dd4f4&&(_0x1dd4f4=0x0,await chrome[_0x345e67(0x118)][_0x345e67(0x127)][_0x345e67(0xdb)]({'totalCompetitorsFilter':_0x1dd4f4})),_0x5a4eb8[_0x345e67(0x98)]=_0x1dd4f4,_0x5a4eb8[_0x345e67(0x13e)](_0x345e67(0xf2),async function(){var _0x5a69dc=_0x345e67;this[_0x5a69dc(0x98)]>=0x0&&(await chrome[_0x5a69dc(0x118)][_0x5a69dc(0x127)][_0x5a69dc(0xdb)]({'totalCompetitorsFilter':this[_0x5a69dc(0x98)]}),console[_0x5a69dc(0x117)](_0x5a69dc(0x140)+this['value']));}),_0x153d5c[_0x345e67(0x147)](_0x5a4eb8),_0x153d5c;}async function fetchSoldDataConcurrently(){var _0x4d1b04=a0_0x3474c1,{concurrencyLimit:_0x36c873}=await chrome[_0x4d1b04(0x118)]['local'][_0x4d1b04(0x10b)](_0x4d1b04(0x103)),_0x1b12c4=_0x36c873;console[_0x4d1b04(0x117)](_0x4d1b04(0x148));var _0x46309a=document[_0x4d1b04(0xd7)](_0x4d1b04(0x116)),_0x490776=Array[_0x4d1b04(0xdc)](_0x46309a);const _0x33d935=[];for(let _0x68e56b=0x0;_0x68e56b<Math['min'](_0x1b12c4,_0x490776[_0x4d1b04(0xc6)]);_0x68e56b++){_0x33d935['push'](worker(_0x490776));}await Promise[_0x4d1b04(0x131)](_0x33d935);return;}async function worker(_0x17719b){var _0x4dc1fc=a0_0x3474c1;while(_0x17719b[_0x4dc1fc(0xc6)]>0x0){const _0x59f123=_0x17719b[_0x4dc1fc(0xc3)]();_0x59f123['classList'][_0x4dc1fc(0x14a)](_0x4dc1fc(0x7d)),await searchTitleOnTeraPeakAndAddToItem(_0x59f123),_0x59f123[_0x4dc1fc(0x96)]['remove'](_0x4dc1fc(0x7d));}}function a0_0x58fd(){var _0x45ad41=['scrape-all-pages-checkbox','auto','main-buttons-div','Last\x2090\x20Days','span','Copy\x20All\x20Keywords','number','flex','Fetching\x20data\x20concurrently\x20via\x20purchase\x20history.','totalCompetitors','fadeOut\x200.5s\x20ease\x20forwards','filter-label','table','createEcommerceSearchButtonsPanel','Last\x2014\x20Days','join','selectedFetchMethod','keyword_modal_header','title:','Last\x2060\x20Days','icons/sold.png','btn-show-all-titles','pop','nextSibling','forEach','length','label','class','soldDate\x20before\x20parse','213jHJOOQ','<b>\x20Search\x20on:\x20\x20</b>','total-competitors-input','none','13890565FGFMvC','nearest','filter-input','concurrency-limit-dropdown','className','data-last-x-days','</b></td>','10mugQnn','filter-dropdown-class','querySelectorAll','Stop\x20Extracting\x20Titles','btn-check-all-purchase-history','daysDifference','set','from','slice','totalSoldIn60','Fetch\x20Sold\x20Data','option','div','5292040vCiFjr','.total-sold-element','Complete!','min','.s-item__title','doesNextPageExist\x20==\x20false','openSortEbayItems','selectedFilter','total-sold-element','filter-dropdown-div','target','dateSold','Last\x203\x20Days','keyword_modal_close','setDate','getAttribute','input','style','btn-extract-all','Total\x20Sold:\x20<b\x20style=\x22color:red;\x22>\x20Error\x20Occurred\x20</b>','keyword_modal_footer','Fetching\x20data\x20sequentially\x20via\x20purchase\x20history.','width','Select\x20Filter','btn-clear-all-titles','soldData','block','.s-item__wrapper:not(.highlight)','insertBefore','2180XDrgUZ','innerHTML','Filter\x20Titles','Clear\x20Titles','concurrencyLimit','pagination__next','Last\x207\x20Days','concurrency-limit-dropdown-container','24868upSXjS','Fetching\x20data\x20sequentially.','s-item','totalSold','get','totalSoldIn3','firstChild','error','scrapeAllPages','Rate\x20Limit\x20Reached,\x20I\x20know\x20it\x20sucks\x20😭,\x20Try\x20again\x20in\x20a\x20bit!','getElementById','reverse','clipboard','push','keyword_textarea','.s-item__wrapper','log','storage','split','concurrent-purchase-history','Last\x2021\x20Days','btn-terapeak-search','body','aria-disabled','text','querySelector','Not\x20Available','totalSoldIn90','100%','btn-stop-extract-all','totalSoldIn1','trim','local','\x20</b>\x20(Last\x20<b>\x20','totalSoldFilter','fetchSoldDataSequentiallyViaPurchaseHistory\x20item','click','item','display','keyword_modal_body','soldDate\x20after\x20parse','filter','all','Get\x20Total\x20Sold\x20History:','🔥\x20Extracting\x20Titles\x20🔥','Total\x20Sold:\x20<b\x20style=\x22color:red;\x22>\x20','for','total-sold-input','concat','\x20</b>\x20Days)','preventDefault','Total\x20Sold\x20Filter\x20saved:\x20','</td><td><b\x20style=\x22color:red;\x22>','Extract\x20All\x20Titles','copy_keywords_button','addEventListener','checkbox','Total\x20Competitors\x20Filter\x20saved:\x20','Scanning\x20Speed:\x20','show-element','checkPurchaseHistory','Off','.s-item__itemID','createElement','appendChild','fetchSoldDataConcurrently','Shop\x20on\x20eBay','add','keyword_modal_content','extractAllTitles()','margin','type','button','2660110hBEQBb','true','remove','response','Total\x20Competitors\x20Filter:\x20','Titles\x20Cleared\x20✔️','6LNLdkx','.su-card-container__attributes__secondary','11yUisBH','<td\x20>','overlay','.overlay','highlight','sendMessage','replace','CONCURRENCY_LIMIT','itemNumber','Loaded\x20','searchTitleOnTeraPeak','checked','center','Scrape\x20All\x20Pages','getDate','scrollIntoView','title','totalSoldIn14','7NcyiLA','savedEbaySearchItems','doesNextPageExist\x20==\x20true','setAttribute','run_status_extract_titles','change','filter-dropdown-container','concurrent','data-total-sold','.s-card__attribute-row','<th>Last\x20X\x20Days</th><th>Total\x20Sold</th>','classList','306XOTRpj','value','textContent','Fetch\x20Method\x20saved:\x20','total-sold-table','runtime','Rate\x20Limited','innerText','parentNode','Only\x20Scan\x20Items\x20That\x20Sold\x20within:\x20','\x20keywords','undefined','writeText','getElementsByClassName','select','daysDifference\x20>\x20lastXDays','9994128gOSTPx','6282efnhTL','Concurrency\x20Limit\x20saved:\x20','Total\x20Sold:\x20','&times;','Please\x20Wait...'];a0_0x58fd=function(){return _0x45ad41;};return a0_0x58fd();}async function fetchSoldDataConcurrentlyViaPurchaseHistory(){var _0x268193=a0_0x3474c1,{concurrencyLimit:_0x5c1277}=await chrome[_0x268193(0x118)][_0x268193(0x127)][_0x268193(0x10b)](_0x268193(0x103)),_0x5c19b7=_0x5c1277,_0x440594=selectAllItems(),_0x1b0e4f=Array[_0x268193(0xdc)](_0x440594),{selectedFilter:_0x586f05}=await chrome[_0x268193(0x118)][_0x268193(0x127)]['get'](_0x268193(0xe9));!_0x586f05&&(_0x586f05='90',await chrome['storage'][_0x268193(0x127)][_0x268193(0xdb)]({'selectedFilter':_0x586f05}));var _0x801f37=_0x586f05;const _0x3d1323=[];console[_0x268193(0x117)](_0x268193(0x80),_0x5c19b7);for(let _0x4cb24a=0x0;_0x4cb24a<Math[_0x268193(0xe5)](_0x5c19b7,_0x1b0e4f['length']);_0x4cb24a++){_0x3d1323['push'](workerPurchaseHistory(_0x1b0e4f,_0x801f37));}await Promise[_0x268193(0x131)](_0x3d1323);return;}async function workerPurchaseHistory(_0x30b9c6,_0x4381d3){var _0x2519dc=a0_0x3474c1;while(_0x30b9c6[_0x2519dc(0xc6)]>0x0){var _0xf5c899=_0x30b9c6[_0x2519dc(0xc3)]();_0xf5c899[_0x2519dc(0x96)][_0x2519dc(0x14a)]('highlight');try{await checkPurchaseHistoryAndAddToItem(_0xf5c899,_0x4381d3);}catch(_0x59cd1f){console[_0x2519dc(0x117)](_0x2519dc(0x10e),_0x59cd1f);}_0xf5c899['classList'][_0x2519dc(0x152)](_0x2519dc(0x7d));}}async function fetchSoldDataSequentially(){var _0x5049e7=a0_0x3474c1;const _0x2ce53b=document[_0x5049e7(0xd7)](_0x5049e7(0x116));for(const _0xae1abf of _0x2ce53b){_0xae1abf[_0x5049e7(0x88)]({'block':_0x5049e7(0x85),'inline':'nearest'}),_0xae1abf[_0x5049e7(0x96)]['add'](_0x5049e7(0x7d)),await searchTitleOnTeraPeakAndAddToItem(_0xae1abf),await new Promise(_0x29947d=>setTimeout(_0x29947d,timeOutMs)),_0xae1abf[_0x5049e7(0x96)][_0x5049e7(0x152)](_0x5049e7(0x7d));}await new Promise(_0x2f66fe=>setTimeout(_0x2f66fe,0x7d0)),removeOverlay();}async function fetchSoldDataSequentiallyViaPurchaseHistory(){var _0x258c62=a0_0x3474c1,_0xfb7a99=document[_0x258c62(0xd7)](_0x258c62(0xfd));console[_0x258c62(0x117)]('fetchSoldDataSequentiallyViaPurchaseHistory\x20items',_0xfb7a99);for(var _0x5e6fbc of _0xfb7a99){var _0xf9ffc6;try{var _0xf9ffc6=_0x5e6fbc[_0x258c62(0x120)](_0x258c62(0x145))[_0x258c62(0x99)];}catch(_0x3bb8bc){console[_0x258c62(0x117)]('itemNumber\x20not\x20found');continue;}console[_0x258c62(0x117)](_0x258c62(0x12a),_0x5e6fbc),_0x5e6fbc[_0x258c62(0x88)]({'block':_0x258c62(0x85),'inline':_0x258c62(0xcf)}),_0x5e6fbc[_0x258c62(0x96)][_0x258c62(0x14a)]('highlight');var {selectedFilter:_0x4954b2}=await chrome[_0x258c62(0x118)][_0x258c62(0x127)][_0x258c62(0x10b)](_0x258c62(0xe9));!_0x4954b2&&(_0x4954b2='90',await chrome[_0x258c62(0x118)][_0x258c62(0x127)][_0x258c62(0xdb)]({'selectedFilter':_0x4954b2}));var _0x1c5063=_0x4954b2;await checkPurchaseHistoryAndAddToItem(_0x5e6fbc,_0x1c5063),timeOutMs=0x96,await new Promise(_0x981629=>setTimeout(_0x981629,timeOutMs)),_0x5e6fbc['classList'][_0x258c62(0x152)](_0x258c62(0x7d));}await new Promise(_0x533872=>setTimeout(_0x533872,0x7d0)),removeOverlay();}function createFetchSoldDataButton(){var _0x320f22=a0_0x3474c1,_0x2e643d=document[_0x320f22(0x146)](_0x320f22(0x14f));return _0x2e643d[_0x320f22(0x8e)](_0x320f22(0xc8),'btn-fetch-sold-data'),_0x2e643d['innerHTML']=_0x320f22(0xdf),_0x2e643d[_0x320f22(0x13e)](_0x320f22(0x12b),async function(_0x1f1928){var _0x2dcb41=_0x320f22;_0x1f1928[_0x2dcb41(0x139)]();var _0x2824e2=await fetchSoldDataSequentially();}),_0x2e643d;}async function createFetchSoldDataDropDown(){var _0x20d476=a0_0x3474c1,_0x3716a4=document[_0x20d476(0x146)](_0x20d476(0xa5));_0x3716a4[_0x20d476(0x8e)](_0x20d476(0xc8),_0x20d476(0xd6));var _0xaa3603=[{'text':_0x20d476(0x144),'value':'none'},{'text':'On','value':_0x20d476(0x11a)}];_0xaa3603[_0x20d476(0xc5)](_0x14a80e=>{var _0x1ef62f=_0x20d476,_0x2c6fa8=document[_0x1ef62f(0x146)](_0x1ef62f(0xe0));_0x2c6fa8[_0x1ef62f(0x98)]=_0x14a80e[_0x1ef62f(0x98)],_0x2c6fa8[_0x1ef62f(0x9e)]=_0x14a80e['text'],_0x3716a4['appendChild'](_0x2c6fa8);});let {selectedFetchMethod:_0x40776b}=await chrome[_0x20d476(0x118)][_0x20d476(0x127)][_0x20d476(0x10b)](_0x20d476(0xbd));!_0x40776b&&(_0x40776b='none',await chrome[_0x20d476(0x118)]['local'][_0x20d476(0xdb)]({'selectedFetchMethod':_0x40776b}));_0x3716a4[_0x20d476(0x98)]=_0x40776b,_0x3716a4[_0x20d476(0x13e)](_0x20d476(0x90),async function(){var _0xcc565d=_0x20d476;await chrome[_0xcc565d(0x118)]['local']['set']({'selectedFetchMethod':this[_0xcc565d(0x98)]}),console[_0xcc565d(0x117)](_0xcc565d(0x9a)+this[_0xcc565d(0x98)]),this[_0xcc565d(0x98)]==_0xcc565d(0x11a)?(document[_0xcc565d(0x111)](_0xcc565d(0x106))['style'][_0xcc565d(0x12d)]=_0xcc565d(0xfc),document['getElementById']('filter-dropdown-container')[_0xcc565d(0xf3)][_0xcc565d(0x12d)]=_0xcc565d(0xfc)):(document['getElementById'](_0xcc565d(0x106))[_0xcc565d(0xf3)][_0xcc565d(0x12d)]=_0xcc565d(0xcd),document[_0xcc565d(0x111)](_0xcc565d(0x91))[_0xcc565d(0xf3)][_0xcc565d(0x12d)]=_0xcc565d(0xcd));});var _0x369b49=document[_0x20d476(0x146)](_0x20d476(0xc7));_0x369b49[_0x20d476(0x9e)]=_0x20d476(0x132),_0x369b49[_0x20d476(0x8e)](_0x20d476(0x135),'terapeak-fetch-mode');var _0x1b9332=document[_0x20d476(0x146)]('div');return _0x1b9332[_0x20d476(0x147)](_0x369b49),_0x1b9332['appendChild'](_0x3716a4),_0x1b9332;}async function createScrapeAllPagesCheckbox(){var _0x3a7b43=a0_0x3474c1,_0x53ca70=document[_0x3a7b43(0x146)](_0x3a7b43(0xf2));_0x53ca70['type']=_0x3a7b43(0x13f),_0x53ca70['id']='scrape-all-pages-checkbox';var {scrapeAllPages:_0x3513d7}=await chrome[_0x3a7b43(0x118)][_0x3a7b43(0x127)][_0x3a7b43(0x10b)](_0x3a7b43(0x10f));!_0x3513d7&&(_0x3513d7=![],await chrome[_0x3a7b43(0x118)][_0x3a7b43(0x127)]['set']({'scrapeAllPages':_0x3513d7}));_0x53ca70[_0x3a7b43(0x84)]=_0x3513d7,_0x53ca70[_0x3a7b43(0x13e)](_0x3a7b43(0x90),async function(){var _0x148677=_0x3a7b43;await chrome[_0x148677(0x118)]['local'][_0x148677(0xdb)]({'scrapeAllPages':this[_0x148677(0x84)]}),console[_0x148677(0x117)]('Scrape\x20All\x20Pages\x20saved:\x20'+this[_0x148677(0x84)]);});var _0x2a0613=document[_0x3a7b43(0x146)](_0x3a7b43(0xc7));_0x2a0613[_0x3a7b43(0x9e)]=_0x3a7b43(0x86),_0x2a0613[_0x3a7b43(0x8e)](_0x3a7b43(0x135),_0x3a7b43(0xad));var _0x201b83=document[_0x3a7b43(0x146)](_0x3a7b43(0xe1));return _0x201b83[_0x3a7b43(0x147)](_0x53ca70),_0x201b83[_0x3a7b43(0x147)](_0x2a0613),_0x201b83;}async function createConcurrencyLimitDropdown(){var _0x2bfacd=a0_0x3474c1,_0x207f82=document[_0x2bfacd(0x146)](_0x2bfacd(0xa5));_0x207f82['id']='concurrency-limit-dropdown',_0x207f82[_0x2bfacd(0x8e)](_0x2bfacd(0xc8),_0x2bfacd(0xd6));var _0x39ff90=[{'text':'1','value':0x1},{'text':'3','value':0x3},{'text':'5','value':0x5},{'text':'10','value':0xa},{'text':'15','value':0xf},{'text':'20','value':0x14},{'text':'25','value':0x19},{'text':'30','value':0x1e},{'text':'40','value':0x28},{'text':'50','value':0x32}];_0x39ff90[_0x2bfacd(0xc5)](_0x25851a=>{var _0xc457c3=_0x2bfacd,_0x5c0b28=document[_0xc457c3(0x146)](_0xc457c3(0xe0));_0x5c0b28[_0xc457c3(0x98)]=_0x25851a[_0xc457c3(0x98)],_0x5c0b28[_0xc457c3(0x9e)]=_0x25851a['text'],_0x207f82[_0xc457c3(0x147)](_0x5c0b28);});var {concurrencyLimit:_0x223ff3}=await chrome[_0x2bfacd(0x118)]['local'][_0x2bfacd(0x10b)](_0x2bfacd(0x103));console['log'](_0x2bfacd(0x103),_0x223ff3);!_0x223ff3&&(_0x223ff3=0x5,chrome[_0x2bfacd(0x118)][_0x2bfacd(0x127)]['set']({'concurrencyLimit':_0x223ff3}));_0x207f82[_0x2bfacd(0x98)]=_0x223ff3,_0x207f82['addEventListener'](_0x2bfacd(0x90),async function(){var _0x3b1747=_0x2bfacd;await chrome['storage'][_0x3b1747(0x127)]['set']({'concurrencyLimit':this[_0x3b1747(0x98)]}),console['log'](_0x3b1747(0xa9)+this[_0x3b1747(0x98)]);});var _0x26c98c=document['createElement'](_0x2bfacd(0xc7));_0x26c98c[_0x2bfacd(0x9e)]=_0x2bfacd(0x141),_0x26c98c[_0x2bfacd(0x8e)](_0x2bfacd(0x135),_0x2bfacd(0xd1));var _0x3b61fc=document[_0x2bfacd(0x146)](_0x2bfacd(0xe1));return _0x3b61fc['id']=_0x2bfacd(0x106),_0x3b61fc[_0x2bfacd(0x147)](_0x26c98c),_0x3b61fc[_0x2bfacd(0x147)](_0x207f82),_0x3b61fc;}function sortByTotalSold(){var _0x201460=a0_0x3474c1,_0x4daec4=document[_0x201460(0xd7)]('li'),_0x81e5de=Array[_0x201460(0xdc)](_0x4daec4);_0x81e5de=_0x81e5de[_0x201460(0x130)](_0x55000b=>_0x55000b[_0x201460(0x120)](getSoldCaptionSelector())),_0x81e5de=_0x81e5de[_0x201460(0x130)](_0x260e3c=>_0x260e3c[_0x201460(0x120)](_0x201460(0xe3))),_0x81e5de['sort']((_0x499b01,_0x51bf14)=>{var _0x4f348a=_0x201460;console[_0x4f348a(0x117)]('a',_0x499b01),console['log']('b',_0x51bf14);var _0x4680d4=_0x499b01[_0x4f348a(0x120)](_0x4f348a(0xe3))[_0x4f348a(0xf1)](_0x4f348a(0x93)),_0x18c18d=_0x51bf14[_0x4f348a(0x120)]('.total-sold-element')[_0x4f348a(0xf1)](_0x4f348a(0x93));if(_0x4680d4==_0x4f348a(0x9d)||_0x18c18d=='Rate\x20Limited')return 0x0;return _0x18c18d-_0x4680d4;}),_0x81e5de[_0x201460(0x112)]();var _0x35d71b=_0x81e5de[0x0][_0x201460(0x9f)];_0x81e5de[_0x201460(0xc5)](_0x4bfd73=>_0x35d71b[_0x201460(0xfe)](_0x4bfd73,_0x35d71b[_0x201460(0x10d)][_0x201460(0xc4)]));}function createCheckAllPurchaseHistoryButton(){var _0x28ef63=a0_0x3474c1,_0x841222=document[_0x28ef63(0x146)](_0x28ef63(0x14f));return _0x841222[_0x28ef63(0x8e)](_0x28ef63(0xc8),_0x28ef63(0xd9)),_0x841222['classList'][_0x28ef63(0x14a)]('filter-date-context'),_0x841222['id']=_0x28ef63(0xd9),_0x841222['innerHTML']='Check\x20All\x20Purchase\x20History',_0x841222[_0x28ef63(0x13e)](_0x28ef63(0x12b),async function(_0x339cc9){try{await fetchSoldDataBasedOnSelection();}catch(_0x443a3c){console['log']('error',_0x443a3c);}}),_0x841222;}function createEcommerceSearchButtonsPanel(_0x29e8db=null,_0x43ff0a=null,_0xf8c3fb=null,_0x2f12e4=!![]){var _0xb2ad6e=a0_0x3474c1;console['log'](_0xb2ad6e(0xba),_0x29e8db);var _0x4c517b=createButtonToSaveSeller(_0x43ff0a),_0x4855ee=createSearchTerapeakButton(_0x29e8db),_0x55f660=createCheckPurchaseHistoryButton(),_0x517b44=createEbaySearchButton(_0x29e8db),_0x444861=createAmazonSearchButton(_0x29e8db),_0x4c43d7=createEbaySearchButton(_0x29e8db,{'soldItems':!![],'endedRecently':!![]},_0xb2ad6e(0xc1)),_0x186a91=createGoogleImageSearchButton(_0xf8c3fb),_0x2cd9bd=createOpenSellerItemsButton(_0x43ff0a),_0x1067fd=document['createElement'](_0xb2ad6e(0xe1));_0x1067fd[_0xb2ad6e(0x8e)]('id','search-div');var _0x1c2969=document[_0xb2ad6e(0x146)](_0xb2ad6e(0xc7));_0x1c2969[_0xb2ad6e(0x100)]=_0xb2ad6e(0xcb),_0x1067fd[_0xb2ad6e(0x147)](_0x1c2969),_0x1067fd[_0xb2ad6e(0x147)](_0x444861),_0x1067fd[_0xb2ad6e(0x147)](_0x517b44),_0x1067fd['appendChild'](_0x4855ee),_0x1067fd[_0xb2ad6e(0x147)](_0x186a91),_0x1067fd[_0xb2ad6e(0x147)](_0x4c43d7);var _0x46ae0a=createButtonToCopyData(),_0x21440d=createButtonListToEbay(),_0x5a76a0=document['createElement'](_0xb2ad6e(0xe1));_0x5a76a0['setAttribute']('id','item-buttons-div');var _0x525703=document[_0xb2ad6e(0x146)](_0xb2ad6e(0xe1));return _0x525703[_0xb2ad6e(0x8e)]('id',_0xb2ad6e(0xaf)),_0x525703[_0xb2ad6e(0x147)](_0x2cd9bd),_0x525703[_0xb2ad6e(0x147)](_0x55f660),_0x525703[_0xb2ad6e(0x147)](_0x46ae0a),_0x525703['appendChild'](_0x4c517b),_0x5a76a0[_0xb2ad6e(0x147)](_0x525703),_0x2f12e4&&_0x5a76a0[_0xb2ad6e(0x147)](_0x21440d),_0x5a76a0['appendChild'](_0x1067fd),_0x5a76a0;}