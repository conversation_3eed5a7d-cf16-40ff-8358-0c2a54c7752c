var a0_0x360ad7=a0_0x2779;(function(_0x46ec80,_0x23fb5b){var _0x4f6bfd=a0_0x2779,_0x35ade4=_0x46ec80();while(!![]){try{var _0x4411b6=parseInt(_0x4f6bfd(0xa3))/0x1*(parseInt(_0x4f6bfd(0xae))/0x2)+parseInt(_0x4f6bfd(0x98))/0x3+parseInt(_0x4f6bfd(0xa0))/0x4*(parseInt(_0x4f6bfd(0x91))/0x5)+parseInt(_0x4f6bfd(0x88))/0x6+parseInt(_0x4f6bfd(0xaf))/0x7*(parseInt(_0x4f6bfd(0x86))/0x8)+-parseInt(_0x4f6bfd(0x84))/0x9+-parseInt(_0x4f6bfd(0x8a))/0xa;if(_0x4411b6===_0x23fb5b)break;else _0x35ade4['push'](_0x35ade4['shift']());}catch(_0x4974db){_0x35ade4['push'](_0x35ade4['shift']());}}}(a0_0x5c4c,0x22b4d),console['log'](a0_0x360ad7(0xab)),chrome[a0_0x360ad7(0xa7)][a0_0x360ad7(0x92)][a0_0x360ad7(0xb1)](function(_0x13c818,_0x53e1f7,_0xfa4723){var _0x3d93cb=a0_0x360ad7;console[_0x3d93cb(0x96)](_0x3d93cb(0xa6),_0x13c818);if(_0x13c818[_0x3d93cb(0x9f)]===_0x3d93cb(0x9a)){console[_0x3d93cb(0x96)]('clicked_browser_action\x20message\x20received');var _0x5369d0=getItemsData();console['log'](_0x3d93cb(0xa2),_0x5369d0),chrome[_0x3d93cb(0x9b)][_0x3d93cb(0x94)][_0x3d93cb(0x83)]({'ebaySearchItems':_0x5369d0},function(){var _0x493515=_0x3d93cb;console['log'](_0x493515(0x9c)+_0x5369d0);var _0x1d25e8=chrome[_0x493515(0xa7)][_0x493515(0x93)](_0x493515(0x89));chrome[_0x493515(0xa7)][_0x493515(0xaa)]({'type':_0x493515(0xb0),'url':_0x1d25e8},function(_0x643c24){var _0x165273=_0x493515;console[_0x165273(0x96)](_0x165273(0x99));});});}if(_0x13c818[_0x3d93cb(0x8c)]===_0x3d93cb(0x97)){var _0x5369d0=getItemsData();return _0xfa4723({'ebaySearchItems':_0x5369d0}),!![];}return!![];}));async function main(){var _0xa3746a=a0_0x360ad7,_0x5bae7f=createCheckAllPurchaseHistoryButton(),_0x550c12=createSortSoldButton(),_0x2132c6=createSortBySoldDateButton(),_0x4fe0b9=createSortByLowestPriceButton(),_0x582058=document['querySelector']('.s-answer-region-center-top');_0x582058[_0xa3746a(0x8f)](_0x5bae7f);var _0x224091=document[_0xa3746a(0x8d)](_0xa3746a(0xa1));_0x224091[_0xa3746a(0x95)]('id',_0xa3746a(0x8e));var _0x3a7ada=document['createElement'](_0xa3746a(0xac));_0x3a7ada['setAttribute']('id',_0xa3746a(0x87)),_0x3a7ada[_0xa3746a(0x9e)]='<b>Sort\x20by:\x20\x20\x20</b>',_0x224091[_0xa3746a(0x8f)](_0x3a7ada),_0x224091[_0xa3746a(0x8f)](_0x550c12),_0x224091[_0xa3746a(0x8f)](_0x2132c6),_0x224091[_0xa3746a(0x8f)](_0x4fe0b9),_0x582058[_0xa3746a(0x8f)](_0x224091);var _0x53e787=getSellerInfoElements();console[_0xa3746a(0x96)]('sellerInfoElements',_0x53e787);for(i=0x0;i<_0x53e787[_0xa3746a(0xa5)];i++){var _0x250db4=_0x53e787[i],_0x59b17d;try{_0x59b17d=getEbaySellerNameFromNode(_0x250db4);}catch(_0x3094f3){console[_0xa3746a(0x96)](_0xa3746a(0x9d),_0x3094f3);continue;}var _0x2ab2f6=createEcommerceSearchButtonsPanel2();_0x250db4[_0xa3746a(0x8f)](_0x2ab2f6);var _0x567ecb=_0x250db4;_0x567ecb=getParentItemContainer(_0x567ecb);var _0x13c5f0=extractPriceFromElement(_0x567ecb),_0x30da0f=await showBreakEvenPrice(_0x13c5f0);_0x250db4[_0xa3746a(0x8f)](_0x30da0f),await updateSellerButton(_0x250db4['querySelector'](_0xa3746a(0xad)),_0x59b17d);}initTerapeakOptionsContextMenu(),initAmazonSortTypeContextMenu(),initDateFilterContextMenu();try{numberEbayListingsHigher();}catch(_0x25d800){}try{var _0x4f0b72=document[_0xa3746a(0x90)](_0xa3746a(0x8b));_0x4f0b72[_0xa3746a(0xa8)](function(_0x3d7d09){var _0x51f872=_0xa3746a;_0x3d7d09[_0x51f872(0xa4)]('click',async function(_0x786440){var _0x246e60=getParentItemContainer(_0x3d7d09);console['log']('itemContainer',_0x246e60),await updateAllSellerButtons(_0x246e60);});});}catch(_0x4c5869){}}main();async function updateAllSellerButtons(_0x46e53a){var _0x54bf24=a0_0x360ad7;await new Promise(_0x290d84=>setTimeout(_0x290d84,0x12c));var _0x271ca6=getEbaySellerNameFromNode(_0x46e53a);console[_0x54bf24(0x96)](_0x54bf24(0xa9),_0x271ca6);var _0xca4490=getSellerInfoElements();for(i=0x0;i<_0xca4490['length'];i++){if(_0xca4490[i]===_0x46e53a){console['log']('found\x20clicked\x20item\x20container,\x20skipping...');continue;}var _0x3bcd81=_0xca4490[i];try{var _0x375843=getEbaySellerNameFromNode(_0x3bcd81);await updateSellerButton(_0x3bcd81[_0x54bf24(0x85)](_0x54bf24(0xad)),_0x375843);}catch(_0x583a05){console[_0x54bf24(0x96)](_0x54bf24(0x9d),_0x583a05);continue;}}}function a0_0x2779(_0x230e06,_0x66d36c){var _0x5c4c47=a0_0x5c4c();return a0_0x2779=function(_0x2779e0,_0x14fdde){_0x2779e0=_0x2779e0-0x83;var _0x487492=_0x5c4c47[_0x2779e0];return _0x487492;},a0_0x2779(_0x230e06,_0x66d36c);}function a0_0x5c4c(){var _0x2ad7f0=['openNewTab','addListener','set','1481490mfgafJ','querySelector','78088ALbbgf','sort-label','396960WztjJY','research_seo/index.html','347670cqjFoh','.saveSellerLink','type','createElement','sort-div','appendChild','querySelectorAll','684705PqYBuu','onMessage','getURL','local','setAttribute','log','retrieve_competitors','58749RcStQu','Message\x20sent\x20to\x20background.js','research_seo','storage','Value\x20is\x20set\x20to\x20','Error\x20getting\x20seller\x20name\x20from\x20node','innerHTML','message','4yAMqFi','div','ebaySearchItems','1HTkTYP','addEventListener','length','Message\x20received\x20from\x20popup.js:','runtime','forEach','clickedUserName','sendMessage','public_search\x20content.js\x20loaded','label','#saveSellerLink','140090qYhKZh','35pfFPFu'];a0_0x5c4c=function(){return _0x2ad7f0;};return a0_0x5c4c();}