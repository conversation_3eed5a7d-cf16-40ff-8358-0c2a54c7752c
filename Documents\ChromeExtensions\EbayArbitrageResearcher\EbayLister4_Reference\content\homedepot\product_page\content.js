function a0_0x4b0f(_0x2426df,_0x334861){var _0x58a2f3=a0_0x58a2();return a0_0x4b0f=function(_0x4b0f56,_0x346bb1){_0x4b0f56=_0x4b0f56-0x1e4;var _0x5c2c87=_0x58a2f3[_0x4b0f56];return _0x5c2c87;},a0_0x4b0f(_0x2426df,_0x334861);}var a0_0x524b65=a0_0x4b0f;(function(_0x5d6b03,_0x21cc3f){var _0x3c2df1=a0_0x4b0f,_0x39f671=_0x5d6b03();while(!![]){try{var _0x2cc4d3=parseInt(_0x3c2df1(0x1f2))/0x1*(-parseInt(_0x3c2df1(0x1eb))/0x2)+-parseInt(_0x3c2df1(0x1e6))/0x3+parseInt(_0x3c2df1(0x204))/0x4*(parseInt(_0x3c2df1(0x1ec))/0x5)+-parseInt(_0x3c2df1(0x208))/0x6*(-parseInt(_0x3c2df1(0x201))/0x7)+-parseInt(_0x3c2df1(0x207))/0x8*(parseInt(_0x3c2df1(0x206))/0x9)+-parseInt(_0x3c2df1(0x1f1))/0xa+parseInt(_0x3c2df1(0x1fe))/0xb;if(_0x2cc4d3===_0x21cc3f)break;else _0x39f671['push'](_0x39f671['shift']());}catch(_0x3bc648){_0x39f671['push'](_0x39f671['shift']());}}}(a0_0x58a2,0x56903),console[a0_0x524b65(0x1e7)]('home_depot\x20product_page\x20content.js\x20loaded'));var product_data;async function startHomeDepotMain(){var _0x14222b=a0_0x524b65;await onPageLoadAndIdle(),console[_0x14222b(0x1e7)](_0x14222b(0x1fa)),initUi();var _0x59786e=findApolloStateScript();if(!_0x59786e)return null;var _0x5550a5=extractJsonFromScript(_0x59786e),_0x5763a9=Object[_0x14222b(0x209)](_0x5550a5),_0x3043e0=_0x5763a9['find'](_0x5289ef=>_0x5289ef[_0x14222b(0x20b)](_0x14222b(0x20d))),_0x13c820=_0x5550a5[_0x3043e0];product_data=parseProductDetails(_0x13c820),console['log'](_0x14222b(0x1e9),product_data);var _0x47b292=product_data[_0x14222b(0x1ed)][0x0],_0x2f2ab3=await urlToImage(_0x47b292);createImageAndAppend(_0x2f2ab3);var _0x30ef7f=createTableBoilerPlate();document[_0x14222b(0x1f5)](_0x14222b(0x1f3))['appendChild'](_0x30ef7f),createTableHeader(_0x30ef7f,{'headerName':_0x14222b(0x1fc)}),createTableHeader(_0x30ef7f,{'headerName':'Type'}),createTableHeader(_0x30ef7f,{'headerName':_0x14222b(0x203)}),createTableHeader(_0x30ef7f,{'headerName':_0x14222b(0x1e4)}),createTableHeader(_0x30ef7f,{'headerName':_0x14222b(0x1f8)}),clickAnimation('.total-characters-header',function(){var _0x3b2971=_0x14222b;sortTable(_0x3b2971(0x200),_0x3b2971(0x1e4));});var _0x4231cc=createRow(_0x30ef7f);createCell(_0x30ef7f,{'rowNumber':_0x4231cc,'cellValue':_0x4231cc,'headerName':_0x14222b(0x1fc)}),createCell(_0x30ef7f,{'rowNumber':_0x4231cc,'cellValue':product_data[_0x14222b(0x1f9)],'headerName':'Title'}),createCell(_0x30ef7f,{'rowNumber':_0x4231cc,'cellValue':_0x14222b(0x1e5),'headerName':_0x14222b(0x1fb)}),createCell(_0x30ef7f,{'rowNumber':_0x4231cc,'cellValue':product_data[_0x14222b(0x1f9)][_0x14222b(0x1fd)],'headerName':_0x14222b(0x1e4)});var _0x39fdba=createButtonToUpdateTextArea({'buttonInnerText':_0x14222b(0x20f),'textAreaSelector':_0x14222b(0x20e),'valueToSet':product_data[_0x14222b(0x1f9)],'callback':updateTheCharacterCountOnTextArea});createCellWithButton(_0x30ef7f,{'button':_0x39fdba,'rowNumber':_0x4231cc,'headerName':_0x14222b(0x1f8)}),await mainTitleBuilder();}function a0_0x58a2(){var _0x8f9e3d=['Total\x20Characters','Filtered','1906464DOegxT','log','JSON\x20extracted\x20and\x20parsed\x20successfully','|Product\x20Details|','Faulty\x20JSON\x20string:','116274nkJHfE','215lEvAxe','main_hd_images','Failed\x20to\x20parse\x20JSON\x20data:','substring','getElementsByTagName','6979110bZmLNz','7mpJdoz','listingToEbayUiTable','error','getElementById','Total\x20scripts\x20found:\x20','parse','Action','filteredTitle','Product\x20details\x20loaded','Type','Rank','length','23873410vkTXrK','innerText','listing-data-table','77yJrYsX','Script\x20content\x20for\x20review:','Title','16252GIlCpn','includes','1755hDEZPq','26952YdEroZ','221922TFNCsh','keys','script','startsWith','match','base-catalog','#the-textarea','Change'];a0_0x58a2=function(){return _0x8f9e3d;};return a0_0x58a2();}startHomeDepotMain();function findApolloStateScript(){var _0x2e0200=a0_0x524b65;const _0x25afeb=document[_0x2e0200(0x1f0)](_0x2e0200(0x20a));console[_0x2e0200(0x1e7)](_0x2e0200(0x1f6)+_0x25afeb[_0x2e0200(0x1fd)]);for(let _0x35422c=0x0;_0x35422c<_0x25afeb[_0x2e0200(0x1fd)];_0x35422c++){if(_0x25afeb[_0x35422c][_0x2e0200(0x1ff)][_0x2e0200(0x205)]('window.__APOLLO_STATE__'))return console[_0x2e0200(0x1e7)]('Apollo\x20State\x20found\x20in\x20script\x20'+(_0x35422c+0x1)),_0x25afeb[_0x35422c];}return console['log']('No\x20script\x20containing\x20\x22window.__APOLLO_STATE__\x22\x20found'),null;}function extractJsonFromScript(_0x304600){var _0x2a6618=a0_0x524b65;if(!_0x304600)return console[_0x2a6618(0x1e7)]('No\x20script\x20element\x20provided'),null;const _0x5db1c0=_0x304600[_0x2a6618(0x1ff)][_0x2a6618(0x20c)](/window\.__APOLLO_STATE__\s*=\s*(\{.*?\})\s*;/s);if(_0x5db1c0&&_0x5db1c0[0x1]){console[_0x2a6618(0x1e7)]('Match\x20found,\x20attempting\x20to\x20parse\x20JSON');try{const _0x4dc868=JSON[_0x2a6618(0x1f7)](_0x5db1c0[0x1]);return console[_0x2a6618(0x1e7)](_0x2a6618(0x1e8)),_0x4dc868;}catch(_0x373b08){console[_0x2a6618(0x1f4)](_0x2a6618(0x1ee),_0x373b08),console[_0x2a6618(0x1f4)](_0x2a6618(0x1ea),_0x5db1c0[0x1]);}}else console['log']('Failed\x20to\x20extract\x20JSON\x20string\x20from\x20script\x20element'),console[_0x2a6618(0x1e7)](_0x2a6618(0x202),_0x304600[_0x2a6618(0x1ff)][_0x2a6618(0x1ef)](0x0,0x1f4));return null;}