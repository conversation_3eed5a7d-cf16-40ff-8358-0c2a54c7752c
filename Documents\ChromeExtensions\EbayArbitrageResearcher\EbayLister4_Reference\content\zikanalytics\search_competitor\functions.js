function a0_0x12cb(_0x7dc570,_0x5e6512){var _0x4daf2f=a0_0x4daf();return a0_0x12cb=function(_0x12cbc1,_0x156290){_0x12cbc1=_0x12cbc1-0x129;var _0x5ecb11=_0x4daf2f[_0x12cbc1];return _0x5ecb11;},a0_0x12cb(_0x7dc570,_0x5e6512);}var a0_0x42e573=a0_0x12cb;(function(_0x424d3a,_0x44e006){var _0x135100=a0_0x12cb,_0x4e9d9a=_0x424d3a();while(!![]){try{var _0xa38313=parseInt(_0x135100(0x129))/0x1+parseInt(_0x135100(0x156))/0x2*(parseInt(_0x135100(0x144))/0x3)+parseInt(_0x135100(0x131))/0x4+parseInt(_0x135100(0x138))/0x5+parseInt(_0x135100(0x13e))/0x6+parseInt(_0x135100(0x152))/0x7*(parseInt(_0x135100(0x12e))/0x8)+-parseInt(_0x135100(0x132))/0x9*(parseInt(_0x135100(0x148))/0xa);if(_0xa38313===_0x44e006)break;else _0x4e9d9a['push'](_0x4e9d9a['shift']());}catch(_0x414019){_0x4e9d9a['push'](_0x4e9d9a['shift']());}}}(a0_0x4daf,0xcc9a2),console[a0_0x42e573(0x146)](a0_0x42e573(0x13c)));function createSearchAmazonButton(){var _0xba3abe=a0_0x42e573;let _0x4f542e=document['createElement'](_0xba3abe(0x130));return _0x4f542e[_0xba3abe(0x12f)]=_0xba3abe(0x151),_0x4f542e[_0xba3abe(0x140)]='Search\x20Amazon',_0x4f542e[_0xba3abe(0x13f)](_0xba3abe(0x150),searchAmazon),_0x4f542e;}function searchAmazon(){var _0x143281=a0_0x42e573,_0x1748fb,_0x3bdaf0=this;while(_0x3bdaf0[_0x143281(0x141)]!=='TR'){_0x3bdaf0=_0x3bdaf0['parentNode'];}_0x1748fb=_0x3bdaf0;var _0x31211a=getProductTitle(_0x1748fb);chrome[_0x143281(0x145)][_0x143281(0x136)]({'type':_0x143281(0x142),'searchQuery':_0x31211a});}function getProductTitle(_0x3cfc52){var _0x33090d=a0_0x42e573,_0x1513fa=_0x3cfc52[_0x33090d(0x147)](_0x33090d(0x155))[0x0][_0x33090d(0x159)];return _0x1513fa;}function getProductPrice(_0x114677){var _0x16f8f3=a0_0x42e573,_0x55a5c9=_0x114677[_0x16f8f3(0x147)]('sorting_1')[0x4][_0x16f8f3(0x159)];return console[_0x16f8f3(0x146)](_0x55a5c9),_0x55a5c9=_0x55a5c9[_0x16f8f3(0x15b)]('$',''),_0x55a5c9=_0x55a5c9['replace'](',',''),_0x55a5c9=_0x55a5c9[_0x16f8f3(0x13b)](),_0x55a5c9=parseFloat(_0x55a5c9),_0x55a5c9;}function searchSellerFromClipboardButton(){var _0x31011a=a0_0x42e573;let _0x5171ac=document[_0x31011a(0x158)](_0x31011a(0x130));return _0x5171ac['className']='search-seller-from-clipboard-button',_0x5171ac[_0x31011a(0x14b)][_0x31011a(0x12c)]='fit-content',_0x5171ac[_0x31011a(0x14b)]['fontSize']=_0x31011a(0x14f),navigator[_0x31011a(0x139)]['readText']()[_0x31011a(0x149)](_0x31968e=>{var _0x12a32e=_0x31011a;_0x5171ac[_0x12a32e(0x140)]='Search\x20'+_0x31968e;}),_0x5171ac[_0x31011a(0x13f)](_0x31011a(0x150),searchSellerFromClipboard),_0x5171ac;}function searchSellerFromClipboard(){var _0x3471e9=a0_0x42e573;navigator[_0x3471e9(0x139)]['readText']()[_0x3471e9(0x149)](_0x5a4473=>{var _0x59aeee=_0x3471e9,_0x379deb=document['getElementById']('tbSearch');_0x379deb[_0x59aeee(0x143)]=_0x5a4473;var _0x437208=document['getElementById']('tbSearchBtn');_0x437208['click']();});}async function fetchProductTitles(){var _0x16985a=a0_0x42e573;const _0x578b52=[],_0x396b42=document[_0x16985a(0x147)](_0x16985a(0x155));for(let _0x266491=0x0;_0x266491<_0x396b42[_0x16985a(0x14a)];_0x266491++){_0x578b52['push'](_0x396b42[_0x266491]['innerText']);}return _0x578b52;}function addSaveTitlesButton(){var _0x18d5cb=a0_0x42e573;const _0x3ffe1c=document['createElement'](_0x18d5cb(0x130));_0x3ffe1c[_0x18d5cb(0x140)]=_0x18d5cb(0x15a),_0x3ffe1c['addEventListener'](_0x18d5cb(0x150),async _0x196991=>{var _0x356ca5=_0x18d5cb;_0x196991[_0x356ca5(0x154)]();const _0x5683a1=await fetchProductTitles();console[_0x356ca5(0x146)](_0x5683a1);var {amazonSearchQuerys:_0x1ea3e7}=await chrome[_0x356ca5(0x12b)]['local'][_0x356ca5(0x137)](_0x356ca5(0x133));console[_0x356ca5(0x146)](_0x356ca5(0x133),_0x1ea3e7),_0x1ea3e7=_0x1ea3e7[_0x356ca5(0x14e)](_0x5683a1),console['log'](_0x356ca5(0x133),_0x1ea3e7),_0x1ea3e7=[...new Set(_0x1ea3e7)],chrome[_0x356ca5(0x12b)]['local']['set']({'amazonSearchQuerys':_0x1ea3e7},function(){var _0x4ebd80=_0x356ca5;console[_0x4ebd80(0x146)](_0x4ebd80(0x13d));});});const _0x138a8b=document['getElementById'](_0x18d5cb(0x12d));_0x138a8b['parentElement'][_0x18d5cb(0x14d)](_0x3ffe1c);}function a0_0x4daf(){var _0x3cbd21=['5532555qClpNk','clipboard','observe','trim','functions.js\x20loaded','amazonSearchQuerys\x20saved','1848192eTkBJh','addEventListener','textContent','tagName','search-on-amazon','value','1555287nCOJGw','runtime','log','getElementsByClassName','38110fabaiw','then','length','style','type','appendChild','concat','0.7em','click','search-amazon-button','14dsoKYm','getElementById','preventDefault','productTitle','2ZsKrzn','observeSearchBtn','createElement','innerText','Save\x20Titles','replace','searchBtn\x20found','719251QffZbR','childList','storage','width','searchBtn','4260072EbRunH','className','button','4855292Teapvw','9666uPVTBj','amazonSearchQuerys','disconnect','body','sendMessage','get'];a0_0x4daf=function(){return _0x3cbd21;};return a0_0x4daf();}function observeSearchBtn(){var _0x377c3d=a0_0x42e573;console['log'](_0x377c3d(0x157));const _0x1e9609=new MutationObserver(_0xc3979d=>{_0xc3979d['forEach'](_0x273b2e=>{var _0xdef450=a0_0x12cb;if(_0x273b2e[_0xdef450(0x14c)]===_0xdef450(0x12a)){const _0x20d7e1=document[_0xdef450(0x153)](_0xdef450(0x12d));_0x20d7e1&&(console['log'](_0xdef450(0x15c)),addSaveTitlesButton(),_0x1e9609[_0xdef450(0x134)]());}});});_0x1e9609[_0x377c3d(0x13a)](document[_0x377c3d(0x135)],{'childList':!![],'subtree':!![]});}