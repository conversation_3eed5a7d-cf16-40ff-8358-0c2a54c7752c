/* Container for the banner */
.snipeMode-banner-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: #f9f9f9; /* Light gray for minimal distraction */
    color: #333;
    padding: 10px 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    font-family: Arial, sans-serif;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    box-sizing: border-box;
}

/* Image styling */
.snipeMode-competitor-image {
    width: 15%; /* Image takes up 15% of the banner's width */
    max-width: 80px; /* Optional max-width to prevent it from getting too large */
    height: auto;
    margin-right: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    object-fit: contain;
}

/* Content container */
.snipeMode-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Competitor title */
.snipeMode-competitor-title {
    flex: 1; /* Allow the title to take up available space */
    font-size: 2em; /* Ensure font size matches */
    font-weight: bold;
    color: #333;
    line-height: 1.2;
    padding-right: 10px; /* Space between title and refresh button */
    border-bottom: 1px dashed #ccc; /* Optional: Visual indication of editability */
    cursor: text;
}

.snipeMode-competitor-title:focus {
    outline: none; /* Remove default outline on focus */
}

/* Refresh button */
.snipeMode-refresh-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1em; /* Adjust font size as needed */
    color: #0076ce; /* Adjust to match your color scheme */
    padding: 5px; /* Increase clickable area */
}

.snipeMode-refresh-button:hover {
    color: #0057a3; /* Hover color */
}

.snipeMode-refresh-button:focus {
    outline: none;
}


/* Competitor price */
.snipeMode-competitor-price {
    font-size: 1.5em; /* Slightly smaller than title */
    color: #27ae60; /* Green color to indicate price */
    font-weight: bold;
}

/* Close button at the top right */
.snipeMode-close-button {
    position: absolute; /* Updated positioning */
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: red;
}

.snipeMode-close-button:hover {
    color: #555;
}

/* Remove unwanted focus styles */
.snipeMode-close-button:focus,
.snipeMode-competitor-price:focus,
.snipeMode-competitor-title:focus,
.snipeMode-refresh-button:focus {
    outline: none;
}


/* =========================
   Custom Title Element Styles
   ========================= */

/* Style the custom title container */
.custom-title-container {
    margin-top: 10px;
}

/* Style the custom title element */
.custom-title-element {
    position: relative;
    display: block;
    white-space: pre-wrap; /* Preserve line breaks */
    word-wrap: break-word;
    color: #d35400; /* Custom title color */
    outline: none; /* Remove outline on focus */
    cursor: text;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    min-height: 40px; /* Ensure clickable area */
    font-size: 16px; /* Adjust font size as needed */
    background-color: #fff;
}

/* Placeholder styling for contenteditable element */
.custom-title-element:empty:before {
    content: attr(data-placeholder);
    color: #aaa;
    pointer-events: none;
}

/* Highlight custom title on hover or focus */
.custom-title-element:hover,
.custom-title-element:focus {
    border-color: #888;
    background-color: #fafafa;
}





/* Style for the character counter */
.char-counter {
    font-size: 12px;
    color: #555;
    margin-top: 2px;
    text-align: right;
}

/* Style when character limit is exceeded */
.custom-title-element.exceeds-limit {
    color: darkred;
}

.char-counter.exceeds-limit {
    color: darkred;
}

/* Ensure the Amazon title is selectable and not a link */
.s-title-instructions-style h2 span {
    cursor: text;
    text-decoration: none;
    color: #007600; /* Amazon title color */
}

/* Word bank container */
.word-bank-container {
    margin-top: 10px;
}

.word-bank-label {
    font-size: 12px;
    color: #555;
    margin-bottom: 5px;
}

.word-button {
    display: inline-block;
    margin: 2px;
    padding: 5px 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    background-color: #f0f0f0;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
}

.word-button:hover {
    background-color: #e0e0e0;
    border-color: #888;
}

/* Style for the snipe button */
.snipe-button {
    background-color: #28a745; /* Custom background color (green) */
    border-color: #28a745;
    color: #fff;
}

.snipe-button:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Style for the snipe button when disabled */
.snipe-button button:disabled {
    background-color: #ccc;
    border-color: #ccc;
    color: #666;
    cursor: not-allowed;
}

/* Sniper overlay */
.sniper-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black */
    z-index: 9998;
    opacity: 1;
    transition: opacity 0.5s ease;
}

/* Fade-out effect for overlay */
.sniper-overlay.sniper-overlay-fade-out {
    opacity: 0;
}

/* Sniper animation container */
.sniper-animation-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.5);
    z-index: 9999;
    opacity: 1;
    animation: sniperAnimationEntrance 0.5s ease-out forwards;
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Fade-out effect for animation container */
.sniper-animation-container.sniper-animation-fade-out {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3) rotate(720deg);
}

/* Sniper animation image */
.sniper-animation-image {
    width: 150px; /* Increased size for better visibility */
    height: 150px;
    animation: sniperImagePulse 1s infinite;
}

/* Keyframes for the entrance animation */
@keyframes sniperAnimationEntrance {
    0% {
        transform: translate(-50%, -50%) scale(0.5) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* Pulse animation for the sniper image */
@keyframes sniperImagePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Sniper message styling */
.sniper-message {
    position: fixed;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    font-size: 2em;
    color: #fff;
    text-align: center;
    opacity: 0;
    animation: sniperMessageFadeInOut 2s ease forwards;
}

/* Fade-in and fade-out for sniper message */
@keyframes sniperMessageFadeInOut {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* Fade-out for sniper message */
.sniper-message.sniper-message-fade-out {
    opacity: 0;
    transition: opacity 0.5s ease;
}


/* Optional: Adjust spacing and styling for the price at the top */
.price-at-top {
    margin-bottom: 8px; /* Adds space below the price */
    font-size: 1.2em;   /* Increases font size for prominence */
    font-weight: bold;  /* Makes the price text bold */
    color: #FF9900;     /* Optional: Change color to match Amazon's style */
}

/* Ensure that the price container does not have conflicting styles */
.s-price-instructions-style {
    /* Reset any unwanted margins or paddings if necessary */
    margin: 0;
    padding: 0;
}

/* =========================
   Custom Editable Price Element Styles
   ========================= */

/* Container for the custom price */
.custom-price-element {
    position: absolute;
    top: 10px; /* Adjust as needed */
    right: 10px; /* Adjust as needed */
    background-color: rgba(40, 167, 69, 0.1); /* Light green background with some transparency */
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 1.2em; /* Adjust font size to match Amazon's price */
    font-weight: bold;
    color: #28a745; /* Green text color */
    border: 1px solid #28a745; /* Green border */
    cursor: text; /* Indicates editable text */
    min-width: 60px;
    text-align: right;
    /* Optional: Add a box-shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Ensure the element is above other elements */
    z-index: 1000;
}

/* Placeholder styling for the custom price element */
.custom-price-element:empty:before {
    content: attr(data-placeholder);
    color: #6c757d; /* Gray color for placeholder */
}

/* Focus state for better visibility when editing */
.custom-price-element:focus {
    outline: none; /* Remove default outline */
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.5); /* Green glow */
    background-color: rgba(40, 167, 69, 0.2); /* Slightly darker background on focus */
}

/* Invalid price styling */
.custom-price-element.invalid {
    border-color: #dc3545; /* Red border */
    background-color: rgba(220, 53, 69, 0.1); /* Light red background */
    color: #dc3545; /* Red text color */
}

/* Tooltip for invalid input */
.custom-price-element.invalid:after {
    content: 'Invalid price format';
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #dc3545; /* Red background */
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 0.8em;
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.custom-price-element.invalid:hover:after {
    opacity: 1;
}



/* =========================
   Confirmation Message Styles
   ========================= */

/* Confirmation message for price update */
.price-update-confirmation {
    position: absolute;
    top: -30px; /* Position above the custom price element */
    right: 0;
    background-color: #28a745; /* Green background */
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.price-update-confirmation.fade-out {
    opacity: 0;
}



/* =========================
   Cleaning Icon Styles
   ========================= */

.custom-title-features-container{
    display: flex;
    align-items: center;
    justify-content: flex-end; /* Align to the right */
    margin-top: 10px;
}

/* Cleaning icon container */
.cleaning-icon {
    width: 20px; /* Same size as character count */
    height: 20px; /* Same size as character count */
    margin-left: 8px; /* Space between character count and icon */
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cleaning-icon:hover {
    transform: scale(1.2); /* Slight enlarge on hover */
}

.cleaning-icon.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* =========================
   Letter-Floating Animation Styles
   ========================= */

/* Ensure each letter has display inline-block for transform animations */
.custom-title-element .letter {
    display: inline-block;
    position: relative; /* Make position relative for animation */
    will-change: top, left;
}

/* Letters with the continuous floating animation */
.custom-title-element.floating-loop .letter {
    animation: floatUpDown 2s infinite;
    animation-delay: calc(var(--animation-order) * 0.1s);

}

.custom-title-container,
.custom-title-element {
    overflow: visible;
}

.custom-title-element .letter {
    position: relative;
}

/* Keyframes for continuous floating effect */
@keyframes floatUpDown {
    0% {
        top: 0;
    }
    50% {
        top: 20px;
    }
    100% {
        top: 0;
    }

    
}

/* =========================
   "WOW" Animation Styles
   ========================= */

/* "WOW" effect */

