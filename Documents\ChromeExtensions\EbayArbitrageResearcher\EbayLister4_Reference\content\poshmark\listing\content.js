function a0_0x52ac(){var _0x46b6b4=['runtime','41ykbbOR','688964JtEBHA','956128wbsAUR','type','content/poshmark/listing/content.js','error','log','insert_draft_details','26134oRUDAr','3991810ojNVQd','111378VEWRyb','then','35QyfBuw','840169ZbPuCh','3587571sghVpe','[data-et-name=\x22listingEditorPricingSection\x22]','productData','9eEzLnD','21ePZLcT','error:','catch'];a0_0x52ac=function(){return _0x46b6b4;};return a0_0x52ac();}function a0_0x10c7(_0x319e48,_0x22650d){var _0x52acff=a0_0x52ac();return a0_0x10c7=function(_0x10c7a2,_0x4c4992){_0x10c7a2=_0x10c7a2-0x167;var _0x163186=_0x52acff[_0x10c7a2];return _0x163186;},a0_0x10c7(_0x319e48,_0x22650d);}var a0_0x1900ba=a0_0x10c7;(function(_0x4e66b8,_0x1f8f04){var _0x4c20ac=a0_0x10c7,_0x4d54dc=_0x4e66b8();while(!![]){try{var _0x3cc870=-parseInt(_0x4c20ac(0x169))/0x1*(parseInt(_0x4c20ac(0x171))/0x2)+parseInt(_0x4c20ac(0x17a))/0x3*(parseInt(_0x4c20ac(0x16a))/0x4)+parseInt(_0x4c20ac(0x175))/0x5*(-parseInt(_0x4c20ac(0x173))/0x6)+parseInt(_0x4c20ac(0x17b))/0x7*(parseInt(_0x4c20ac(0x16b))/0x8)+-parseInt(_0x4c20ac(0x177))/0x9+parseInt(_0x4c20ac(0x172))/0xa+parseInt(_0x4c20ac(0x176))/0xb;if(_0x3cc870===_0x1f8f04)break;else _0x4d54dc['push'](_0x4d54dc['shift']());}catch(_0x391201){_0x4d54dc['push'](_0x4d54dc['shift']());}}}(a0_0x52ac,0x45f3c),console[a0_0x1900ba(0x16f)](a0_0x1900ba(0x16d)),onPageLoadAndStableNotifyBackground());var productData;chrome[a0_0x1900ba(0x168)]['onMessage']['addListener'](function(_0x602a1a,_0x3e5de4,_0x4133ab){var _0x53f76d=a0_0x1900ba;_0x602a1a[_0x53f76d(0x16c)]===_0x53f76d(0x170)&&(console[_0x53f76d(0x16f)]('insert_draft_details'),productData=_0x602a1a[_0x53f76d(0x179)],console[_0x53f76d(0x16f)]('productData:',productData),waitForElement(_0x53f76d(0x178),0x7530)[_0x53f76d(0x174)](async()=>{await insertDraftDetails(productData);})[_0x53f76d(0x167)](_0x842af0=>{var _0x272813=_0x53f76d;console['error'](_0x272813(0x17c),_0x842af0),chrome[_0x272813(0x168)]['sendMessage']({'type':_0x272813(0x16e),'error':_0x842af0});}));});