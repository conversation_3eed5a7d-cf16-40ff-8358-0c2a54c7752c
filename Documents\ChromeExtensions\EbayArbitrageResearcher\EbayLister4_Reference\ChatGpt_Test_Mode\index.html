<!DOCTYPE html>
<html>

<head>
    <title>ChatGPT Test</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>

<body>
    <div class="chat-container">
        <input type="text" id="messageInput" placeholder="Type your message..." value="Count to 100">
        <button id="sendMessage">Send</button>
        <div id="chatHistory"></div>
    </div>

    <div id="logContainer">
        <h3>Log Messages</h3>
        <button id="clearLog">Clear Log</button>
        <button id="getLastMessage">Get Last Message</button>
        <div id="logMessages"></div>

    </div>


    <script src="/libraries/chat_gpt_web.js"></script>
    <script src="script.js"></script>

</body>

</html>