/* Reset some default styling */
body, html, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block;
}

body {
    line-height: 1;
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    color: #333;
}

#filters {
    max-width: 800px;
    margin: auto;
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-top: 50px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #555;
}

input[type=number] {
    width: calc(100% - 22px);
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
}

button {
    padding: 10px 20px;
    background-color: #2883a7;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background-color: #1f6a8d;
}

#items-list {
    max-width: 800px;
    margin: auto;
    margin-top: 50px;
}

.item {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    opacity: 0;
    animation: fadeIn 0.5s forwards;
    animation-delay: 0.2s;
}

.item-image {
    max-width: 100px;
    border-radius: 5px;
    margin-bottom: 10px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}


/* ... (previous CSS styles) ... */

.filter-group {
    margin-bottom: 20px;
}

#results {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 20px; /* Creates a space between the button and the span */
}

/* If you want the inputs and labels to be side-by-side */
@media (min-width: 600px) {
    .filter-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filter-group label {
        margin-bottom: 0;
    }
}

.page-title {
    text-align: center;
    color: #333;
    font-size: 2em;
    margin-bottom: 40px;
    font-family: Arial, sans-serif;
}


/* ... (your existing styles) ... */

.item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
    transition: box-shadow 0.3s ease-in-out;
}

.item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.item img {
    max-width: 150px;
    border-right: 1px solid #eee;
}

.item-info {
    padding: 20px;
}

.displayed-titles {
    font-size: 1.2em;
    margin-bottom: 10px;
}

.item p {
    margin-bottom: 5px;
}


#clear_items {
    background-color: red;
    color: white;
    transition: background-color 0.3s ease-in-out;
    position: relative;
}

#clear_items:hover {
    background-color: darkred;
}


.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

button:disabled {
    background-color: grey;
    cursor: not-allowed;
}

#export_results {
    background-color: green; /* Change the background color to green */
    color: white;
    transition: background-color 0.3s ease-in-out;
}

#export_results:hover {
    background-color: darkgreen; /* Change the background color to darkgreen */
}

