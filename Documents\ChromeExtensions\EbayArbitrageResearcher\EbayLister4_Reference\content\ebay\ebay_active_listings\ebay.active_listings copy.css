/* Base styles for all buttons */
.end-item-button, .opti-relist-button, .standard-relist-button, .clone-list-button, .smart-relist-button, .update-listing-button {
    border: none;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

/* End Item Button */
.end-item-button {
    background-color: #e44d4d;
}

.end-item-button:disabled {
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.end-item-button:hover {
    background-color: #c0392b;
    transform: scale(1.1);
}

/* Opti Relist Button */
.opti-relist-button, .smart-relist-button {
    background-color: #2ecc71;
}

.opti-relist-button:disabled, .smart-relist-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.opti-relist-button:hover, .smart-relist-button:hover {
    background-color: #27ae60;
    transform: scale(1.1);
}

/* Standard Relist Button */
.standard-relist-button, .update-listing-button {
    background-color: lightgrey;
    color: #000;
}

.standard-relist-button:disabled, .update-listing-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}

.standard-relist-button:hover, .update-listing-button:hover{
    background-color: lightgrey;
    transform: scale(1.1);
}



.clone-list-button {
    background-color: #3498db;
}

.clone-list-button:hover {
    background-color: #2980b9;
    transform: scale(1.1);
}

.clone-list-button:disabled{
    background-color: grey; /* Styles for the disabled state of the button */
    color: darkgrey;
}



/* Add a span element to the button for the loader */


/* Add loader styles */
.loader {
    border: 2px solid grey; /* Changed border color to be visible against the button */
    border-top: 2px solid white; /* Made border-top white for a spinning effect */
    border-radius: 50%;
    width: 14px;
    height: 14px;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}



/* Style for the info-box */
.info-box {
    max-width: 300px;
    background-color: #f0f0f0;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-size: 0.9em;
}

.info-box p {
    margin: 5px 0;
}

/* Adding a bit of style to the URL for aesthetics */
.info-box a {
    color: #3498db;
    text-decoration: none;
}

.info-box a:hover {
    text-decoration: underline;
}


/* Style for the Low Performing Nodes */
.low-performing {
    /* make css over ride the default css */


    border: 2px solid #ff6961; /* Subtle red border */
    background-color: rgba(255, 105, 97, 0.1); /* Very light red background */
    padding: 2px; /* Minimal padding to avoid layout distortion */
    margin: 2px 0; /* Small margin to maintain layout structure */
    box-shadow: none; /* Remove box shadow to prevent layout shift */
    transition: border-color 0.3s ease; /* Smooth transition for border color */
}

.low-performing:hover {
    border-color: #ff4747; /* Slightly darker border on hover for interactivity */
}






.processed-node {
    background-color: #dff0d8; /* Light green background for successful processing */
    border-left: 4px solid #3c763d; /* Dark green left border for emphasis */
    color: #3c763d; /* Dark green text color for contrast */
}

.processed-node::after {
    content: '✔ Processed'; /* Adds a checkmark and text */
    color: #3c763d; /* Dark green text color */
    font-weight: bold; /* Bold text for emphasis */
    margin-left: 10px; /* Space before the text */
    font-size: 0.9em; /* Slightly smaller font size than the node text */
}



.bulk-smart-relist-button-container {
    margin: 15px 0; /* Space above and below the container */
}


.bulk-smart-relist-button {
    background-color: #4CAF50; /* Green background for a positive action */
    color: white; /* White text for contrast */
    padding: 10px 20px; /* Comfortable padding */
    border: none; /* No border for a modern look */
    border-radius: 5px; /* Rounded corners */
    cursor: pointer; /* Cursor indicates it's clickable */
    font-size: 1em; /* Readable font size */
    transition: background-color 0.3s; /* Smooth transition for hover effect */
}

.bulk-smart-relist-button:hover {
    background-color: #45a049; /* Slightly darker green on hover */
}

.bulk-smart-relist-button:disabled {
    background-color: #ccc; /* Grayed out when disabled */
    cursor: default; /* Default cursor when disabled */
}

.custom-loader {
    border: 2px solid #f3f3f3; /* Light part of the spinner */
    border-top: 2px solid #3498db; /* Blue part of the spinner */
    border-radius: 50%;
    width: 12px;
    height: 12px;
    animation: custom-spin 2s linear infinite;
}

@keyframes custom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.concurrent-tasks-select {
    margin-left: 10px; /* Space before the select */
}

.concurrent-tasks-select select {
    padding: 5px; /* Comfortable padding */
    border: 1px solid #ccc; /* Light gray border */
    border-radius: 5px; /* Rounded corners */
    font-size: 1em; /* Readable font size */
    transition: border-color 0.3s; /* Smooth transition for border color */
}

.concurrent-tasks-select select:focus {
    outline: none; /* Remove default focus style */
    border-color: #3498db; /* Blue border on focus */
}

.concurrent-tasks-select select:disabled {
    background-color: #f3f3f3; /* Grayed out when disabled */
    cursor: default; /* Default cursor when disabled */
}

.concurrent-tasks-select select option {
    padding: 5px; /* Comfortable padding */
    border: none; /* No border */
    border-radius: 5px; /* Rounded corners */
    font-size: 1em; /* Readable font size */
    transition: background-color 0.3s; /* Smooth transition for hover effect */
}

.concurrent-tasks-select select option:hover {
    background-color: #f3f3f3; /* Light gray background on hover */
}